#!/bin/bash
# 适用于3.27.0

# 初始化记录项目pwd
projectDir=`pwd`
# 获取 flutter sdk
rootFlutter=`which flutter`
# 提取 flutter skd路径
rootDir=${rootFlutter%/*}
# 获取
flutterOldAArVersion="0"
flutterAArVersion="0"


#!/bin/bash

# 自动定位并修改 .android/build.gradle 文件
update_build_gradle() {
    # 自动定位当前脚本所在目录
    local script_dir
    script_dir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

    # 定位 .android/build.gradle 文件
    local build_gradle_file="$script_dir/.android/build.gradle"

    # 检查文件是否存在
    if [[ ! -f "$build_gradle_file" ]]; then
        echo "Error: .android/build.gradle file not found in $script_dir!"
        return 1
    fi

    # 定义插入的代码段，macOS 的 sed 要求每行以 \\ 结尾
    local insert_code="    subprojects {\\
        afterEvaluate { project ->\\
            if (project.hasProperty('android')) {\\
                project.android {\\
                    if (namespace == null) {\\
                        namespace project.group\\
                    }\\
                }\\
            }\\
        }\\
    }"

    # 检查是否已包含 subprojects 块
    if grep -q "subprojects {" "$build_gradle_file"; then
        echo "The build.gradle file already contains the subprojects block. No changes made."
        return 0
    fi

    # 在 allprojects 的 repositories 块之前插入代码，并确保有空行
    if grep -q "allprojects {" "$build_gradle_file"; then
        sed -i '' "/allprojects {/a\\
$insert_code\\

" "$build_gradle_file"
        echo "The subprojects block has been successfully added with a preceding newline in $build_gradle_file."
    else
        echo "Error: The build.gradle file does not contain an 'allprojects' block!"
        return 1
    fi
}



# 更新aar 版本号
function updateAArVsersion(){
      # 版本号 + 1
    cd ${projectDir}
    v=`grep sonatypeVersion version.txt|cut -d'=' -f2`
    echo 旧版本号$v
    flutterOldAArVersion=$v

    v1=`echo | awk '{split("'$v'",array,"."); print array[1]}'`
    v2=`echo | awk '{split("'$v'",array,"."); print array[2]}'`
    v3=`echo | awk '{split("'$v'",array,"."); print array[3]}'`
    y=`expr $v3 + 1`

    vv=$v1"."$v2"."$y
    echo 新版本号$vv
    flutterAArVersion=$vv
    # 更新配置文件
    sed -i '' 's/sonatypeVersion='$v'/sonatypeVersion='$vv'/g' version.txt
    if [ $? -eq 0 ]; then
        echo ''
    else
        echo '更新版本号失败...'
        exit
    fi
}

function fixMinSdk(){
  echo '11'
}

# step1 git pull ,pub get
#git pull
flutter pub get

# step2 修改 minsdk
echo 'step2 修改 minsdk, 暂时忽略'

# step3
updateAArVsersion

update_build_gradle

# step4 打包aar
echo '执行 flutter build aar --no-profile --no-tree-shake-icons --build-number '$flutterAArVersion
flutter build aar --no-profile --no-tree-shake-icons --build-number $flutterAArVersion

echo '打包成功 : ddbes-flutter.aar......版本：'$flutterOldAArVersion '--->' $flutterAArVersion
exit
