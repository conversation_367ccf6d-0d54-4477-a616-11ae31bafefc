

import 'package:flutter_mixed/app/im/ext/im_time_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../app/im/route/route_helper.dart';
import '../app/im/ui/chat/chat_binding.dart';
import '../app/im/ui/chat/chat_controller.dart';
import '../app/im/ui/chat/chat_page.dart';
import '../app/im/ui/session_list/session_list_controller.dart';
import '../app/routes/app_pages.dart';

testContinuedSenMsg() async {
  // 查询会话，取第一个
  try {
    SessionListController sessionListController = Get.find<SessionListController>();
    if(sessionListController.list.isEmpty) return;
    var chatSessions = sessionListController.list.where((s) => s.isGroupChat() || s.isSingleChat()).toList();
    if(chatSessions.isEmpty) return;
    var session = chatSessions.first;
    RouteHelper.routeTotag(ChatPage(tag: session.sessionId),
        Routes.IM_CHAGE_PAGE,arguments: session,binding: ChatBinding(tag: session.sessionId));

    await Future.delayed(Duration(seconds: 2));
    ChatController chatController = Get.find<ChatController>(tag: session.sessionId);

    var list = [];
    for(int i =0 ;i< 3000 ;i++){
      list.add(i);
    }
    Future.forEach(list, (e) async {
      var time = DateTime.now().millisecondsSinceEpoch.convertDate();
      chatController.sendText('text : ${time}');
      await Future.delayed(Duration(seconds: 1));
    });
  }catch(e){
    print(e);
  }



}