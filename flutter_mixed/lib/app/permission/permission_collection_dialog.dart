import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

import '../common/widgets/theme.dart';

showSimpleDialog(BuildContext context,
    {String? title,
      String? cancelText,
      String? subTitle,
      String? confirmText,
      Function? cancelCallBack,
      Function? confirmCallBack,
      bool? touchDismiss}) {
  showDialog(
      context: context,
      barrierDismissible: touchDismiss ?? true,
      builder: (context) {
        return CupertinoAlertDialog(
          // shape: RoundedRectangleBorder(
          //     borderRadius: BorderRadius.all(Radius.circular(10))
          // ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              10.gap,
              Text(
                title ?? '温馨提示',
                style: const TextStyle(fontSize: 15, color: ColorConfig.mainTextColor),
              ),
              10.gap,
              if(!StringUtil.isEmpty(subTitle))...[
                Text(subTitle!,
                  style: const TextStyle(fontSize: 14 , color: ColorConfig.msgTextColor),
                )
              ]

            ],
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                cancelText ?? '取消',
                style: const TextStyle(fontSize: 15,color: ColorConfig.desTextColor),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (cancelCallBack != null) {
                  cancelCallBack();
                }
              },
            ),
            TextButton(
              child: Text(
                confirmText ?? '确认',
                style: const TextStyle(fontSize: 15,color: ColorConfig.mainTextColor),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (confirmCallBack != null) {
                  confirmCallBack();
                }
              },
            ),
          ],

        );
      });
}


showPermissionDialog(
  BuildContext context, {
  String? title,
  String? cancelText,
  String? confirmText,
  Function? cancelCallBack,
  Function? confirmCallBack,
  required VoidCallback? checkboxValue(bool),
}) {
  showDialog(
      context: context,
      builder: (context) {
        bool _checkbox = false;
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "温馨提示",
                style: const TextStyle(fontSize: 19, color: majorBlue),
              ),
              10.gap,
              Text(
                (title ?? ''),
                style: const TextStyle(fontSize: 16),
              ),
              10.gap,
              Row(
                children: [
                  Text(
                    '不再询问',
                  ),
                  StatefulBuilder(builder: (context, _setState) {
                    return Checkbox(
                        value: _checkbox,
                        onChanged: (value) {
                          print('${value}');
                          _setState(() {
                            _checkbox = !_checkbox;
                            checkboxValue.call(_checkbox);
                          });
                        });
                  }),
                ],
              )
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                cancelText ?? '取消',
                style: TextStyle(fontSize: 19),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (cancelCallBack != null) {
                  cancelCallBack();
                }
              },
            ),
            TextButton(
              child: Text(
                confirmText ?? "确认",
                style: const TextStyle(fontSize: 19),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (confirmCallBack != null) {
                  confirmCallBack();
                }
              },
            ),
          ],
        );
      });
}


final _textTitleStyle = TextStyle(fontSize: 20 ,color: majorBlue);
final _textStyle = TextStyle(fontSize: 16 ,color: Colors.black);
final _blueTextStyle = TextStyle(fontSize: 16 ,color: majorBlue);