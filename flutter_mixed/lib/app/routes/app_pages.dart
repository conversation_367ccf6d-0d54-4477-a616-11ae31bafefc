import 'package:get/get.dart';

import '../../scheme/cheme_binding.dart';
import '../../scheme/scheme_page.dart';
import '../common/controllers/commonVideoPlay/bindings/common_video_play_binding.dart';
import '../common/controllers/commonVideoPlay/views/common_video_play_view.dart';
import '../common/controllers/common_ScrollPic/bindings/common_scroll_pic_binding.dart';
import '../common/controllers/common_ScrollPic/views/common_scroll_pic_view.dart';
import '../common/controllers/splashPage/bindings/splash_page_binding.dart';
import '../common/controllers/splashPage/views/splash_page_view.dart';
import '../common/controllers/webDetail/bindings/web_detail_binding.dart';
import '../common/controllers/webDetail/views/web_detail_view.dart';
import '../im/ui/chat/chat_binding.dart';
import '../im/ui/chat/chat_page.dart';
import '../im/ui/chat_search/chat_search_list_ponit_persion/chat_search_list_ponit_persion_binding.dart';
import '../im/ui/chat_search/chat_search_list_ponit_persion/chat_search_list_ponit_persion_view.dart';
import '../im/ui/chat_search/search_chat_binding.dart';
import '../im/ui/chat_search/search_chat_page.dart';
import '../im/ui/file_picker/file_picker_binding.dart';
import '../im/ui/file_picker/file_picker_view.dart';
import '../im/ui/group_chat_info/group_chat_info_binding.dart';
import '../im/ui/group_chat_info/group_chat_info_page.dart';
import '../im/ui/group_chat_info/group_chat_manage_biding.dart';
import '../im/ui/group_chat_info/group_chat_manage_info.dart';
import '../im/ui/group_name_modify/group_name_modify_binding.dart';
import '../im/ui/group_name_modify/group_name_modify_page.dart';
import '../im/ui/im_notification_detail/im_notice_detail.dart';
import '../im/ui/im_notification_detail/im_notice_detail_binding.dart';
import '../im/ui/map_search/map_search_binding.dart';
import '../im/ui/map_search/map_search_page.dart';
import '../im/ui/map_target/map_target_binding.dart';
import '../im/ui/map_target/map_target_page.dart';
import '../im/ui/msg_record/msg_record_binding.dart';
import '../im/ui/msg_record/msg_record_page.dart';
import '../im/ui/noticeSetting/bindings/notice_setting_binding.dart';
import '../im/ui/noticeSetting/views/notice_setting_view.dart';
import '../im/ui/simple_group_memebers_page/simple_group_member_binding.dart';
import '../im/ui/simple_group_memebers_page/simple_group_members_page.dart';
import '../im/ui/single_chat_info/single_chat_info.dart';
import '../im/ui/single_chat_info/single_chat_info_binding.dart';
import '../im/ui/system_notification/system_notification_list.dart';
import '../im/ui/system_notification/system_notification_list_binding.dart';
import '../im/ui/tip_off/tip_off_main_binding.dart';
import '../im/ui/tip_off/tip_off_main_page.dart';
import '../im/ui/tip_off_feedback/tip_off_feedback_binding.dart';
import '../im/ui/tip_off_feedback/tip_off_feedback_page.dart';
import '../im/web_link/web_tip_link_binding.dart';
import '../im/web_link/web_tip_link_page.dart';
import '../modules/about_page/binding.dart';
import '../modules/about_page/view.dart';
import '../modules/common/group_change/groupChangeInfo/bindings/group_change_info_binding.dart';
import '../modules/common/group_change/groupChangeInfo/views/group_change_info_view.dart';
import '../modules/common/scan_group_code/DBUserSearch/bindings/db_user_search_binding.dart';
import '../modules/common/scan_group_code/DBUserSearch/views/db_user_search_view.dart';
import '../modules/common/scan_group_code/groupAddMembers/bindings/group_add_members_binding.dart';
import '../modules/common/scan_group_code/groupAddMembers/views/group_add_members_view.dart';
import '../modules/common/scan_group_code/joinGroup/scanJoinGroup/bindings/scan_join_group_binding.dart';
import '../modules/common/scan_group_code/joinGroup/scanJoinGroup/views/scan_join_group_view.dart';
import '../modules/contact/aboutFriend/contactAddFriend/bindings/contact_add_friend_binding.dart';
import '../modules/contact/aboutFriend/contactAddFriend/views/contact_add_friend_view.dart';
import '../modules/contact/aboutFriend/friendApplyDetail/bindings/friend_apply_detail_binding.dart';
import '../modules/contact/aboutFriend/friendApplyDetail/views/friend_apply_detail_view.dart';
import '../modules/contact/aboutFriend/friendPending/bindings/friend_pending_binding.dart';
import '../modules/contact/aboutFriend/friendPending/views/friend_pending_view.dart';
import '../modules/contact/aboutFriend/myFriend/bindings/my_friend_binding.dart';
import '../modules/contact/aboutFriend/myFriend/views/my_friend_view.dart';
import '../modules/contact/aboutFriend/orgApplyDetail/bindings/org_apply_detail_binding.dart';
import '../modules/contact/aboutFriend/orgApplyDetail/views/org_apply_detail_view.dart';
import '../modules/contact/aboutFriend/pendingList/bindings/pending_list_binding.dart';
import '../modules/contact/aboutFriend/pendingList/views/pending_list_view.dart';
import '../modules/contact/aboutGroup/myGroup/bindings/my_group_binding.dart';
import '../modules/contact/aboutGroup/myGroup/views/my_group_view.dart';
import '../modules/contact/aboutGroup/searchAll/bindings/search_all_binding.dart';
import '../modules/contact/aboutGroup/searchAll/views/search_all_view.dart';
import '../modules/contact/aboutGroup/searchGroup/bindings/search_group_binding.dart';
import '../modules/contact/aboutGroup/searchGroup/views/search_group_view.dart';
import '../modules/contact/aboutOrg/addDept/bindings/add_dept_binding.dart';
import '../modules/contact/aboutOrg/addDept/views/add_dept_view.dart';
import '../modules/contact/aboutOrg/chooseAllOrgMembers/bindings/choose_all_org_members_binding.dart';
import '../modules/contact/aboutOrg/chooseAllOrgMembers/views/choose_all_org_members_view.dart';
import '../modules/contact/aboutOrg/chooseLeader/bindings/choose_leader_binding.dart';
import '../modules/contact/aboutOrg/chooseLeader/views/choose_leader_view.dart';
import '../modules/contact/aboutOrg/chooseOrgMembers/bindings/choose_org_members_binding.dart';
import '../modules/contact/aboutOrg/chooseOrgMembers/views/choose_org_members_view.dart';
import '../modules/contact/aboutOrg/createAndJoin/bindings/create_and_join_binding.dart';
import '../modules/contact/aboutOrg/createAndJoin/views/create_and_join_view.dart';
import '../modules/contact/aboutOrg/deptDetail/bindings/dept_detail_binding.dart';
import '../modules/contact/aboutOrg/deptDetail/views/dept_detail_view.dart';
import '../modules/contact/aboutOrg/dissolveOrg/bindings/dissolve_org_binding.dart';
import '../modules/contact/aboutOrg/dissolveOrg/views/dissolve_org_view.dart';
import '../modules/contact/aboutOrg/editDept/bindings/edit_dept_binding.dart';
import '../modules/contact/aboutOrg/editDept/views/edit_dept_view.dart';
import '../modules/contact/aboutOrg/externalAdd/bindings/external_add_binding.dart';
import '../modules/contact/aboutOrg/externalAdd/views/external_add_view.dart';
import '../modules/contact/aboutOrg/externalContacts/bindings/external_contacts_binding.dart';
import '../modules/contact/aboutOrg/externalContacts/views/external_contacts_view.dart';
import '../modules/contact/aboutOrg/externalDetail/bindings/external_detail_binding.dart';
import '../modules/contact/aboutOrg/externalDetail/views/external_detail_view.dart';
import '../modules/contact/aboutOrg/inviteHome/bindings/invite_home_binding.dart';
import '../modules/contact/aboutOrg/inviteHome/views/invite_home_view.dart';
import '../modules/contact/aboutOrg/orgCreate/bindings/org_create_binding.dart';
import '../modules/contact/aboutOrg/orgCreate/views/org_create_view.dart';
import '../modules/contact/aboutOrg/orgDetail/bindings/org_detail_binding.dart';
import '../modules/contact/aboutOrg/orgDetail/views/org_detail_view.dart';
import '../modules/contact/aboutOrg/orgDetailSetting/bindings/org_detail_setting_binding.dart';
import '../modules/contact/aboutOrg/orgDetailSetting/views/org_detail_setting_view.dart';
import '../modules/contact/aboutOrg/orgList/bindings/org_list_binding.dart';
import '../modules/contact/aboutOrg/orgList/views/org_list_view.dart';
import '../modules/contact/aboutOrg/orgMoreSetting/bindings/org_more_setting_binding.dart';
import '../modules/contact/aboutOrg/orgMoreSetting/views/org_more_setting_view.dart';
import '../modules/contact/aboutOrg/orgPermission/bindings/add_permission_binding.dart';
import '../modules/contact/aboutOrg/orgPermission/bindings/org_permission_binding.dart';
import '../modules/contact/aboutOrg/orgPermission/views/add_permission_view.dart';
import '../modules/contact/aboutOrg/orgPermission/views/org_permission_view.dart';
import '../modules/contact/aboutOrg/orgPermissionInfo/bindings/org_permission_info_binding.dart';
import '../modules/contact/aboutOrg/orgPermissionInfo/views/org_permission_info_view.dart';
import '../modules/contact/aboutOrg/orgTransferCreater/bindings/org_transfer_creater_binding.dart';
import '../modules/contact/aboutOrg/orgTransferCreater/views/org_transfer_creater_view.dart';
import '../modules/contact/aboutOrg/orgVerify/bindings/org_verify_binding.dart';
import '../modules/contact/aboutOrg/orgVerify/views/org_verify_view.dart';
import '../modules/contact/aboutOrg/preferenceSetting/bindings/preference_setting_binding.dart';
import '../modules/contact/aboutOrg/preferenceSetting/views/preference_setting_view.dart';
import '../modules/contact/aboutOrg/searchOrg/bindings/search_org_binding.dart';
import '../modules/contact/aboutOrg/searchOrg/views/search_org_view.dart';
import '../modules/contact/aboutOrg/settingOrgIndustry/bindings/setting_org_industry_binding.dart';
import '../modules/contact/aboutOrg/settingOrgIndustry/views/setting_org_industry_view.dart';
import '../modules/contact/aboutOrg/settingOrgName/bindings/setting_org_name_binding.dart';
import '../modules/contact/aboutOrg/settingOrgName/views/setting_org_name_view.dart';
import '../modules/contact/aboutOrg/settingOrgScale/bindings/setting_org_scale_binding.dart';
import '../modules/contact/aboutOrg/settingOrgScale/views/setting_org_scale_view.dart';
import '../modules/contact/aboutOrg/userInfo/bindings/user_info_binding.dart';
import '../modules/contact/aboutOrg/userInfo/views/user_info_view.dart';
import '../modules/contact/aboutOrg/userInfoSetting/bindings/user_info_setting_binding.dart';
import '../modules/contact/aboutOrg/userInfoSetting/views/user_info_setting_view.dart';
import '../modules/contact/contact_home/bindings/contact_binding.dart';
import '../modules/contact/contact_home/views/contact_view.dart';
import '../modules/contact/qrCode/qrCode/bindings/qr_code_binding.dart';
import '../modules/contact/qrCode/qrCode/views/qr_code_view.dart';
import '../modules/contact/qrCode/qrcodeLogin/bindings/qrcode_login_binding.dart';
import '../modules/contact/qrCode/qrcodeLogin/views/qrcode_login_view.dart';
import '../modules/group_member/group_member_binding.dart';
import '../modules/group_member/group_member_binding.dart';
import '../modules/group_member/group_member_operate_binding.dart';
import '../modules/group_member/group_member_operate_page.dart';
import '../modules/group_member/group_member_page.dart';
import '../modules/group_member/group_member_page.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/invite_by_session/invite_by_session_binding.dart';
import '../modules/invite_by_session/invite_by_session_page.dart';
import '../modules/login/loginView/bindings/login_binding.dart';
import '../modules/login/loginView/views/login_view.dart';
import '../modules/login/pwdLogin/bindings/pwd_login_binding.dart';
import '../modules/login/pwdLogin/views/pwd_login_view.dart';
import '../modules/login/resetPwd/bindings/reset_pwd_binding.dart';
import '../modules/login/resetPwd/views/reset_pwd_view.dart';
import '../modules/login/supplementaryInfo/bindings/supplementary_info_binding.dart';
import '../modules/login/supplementaryInfo/views/supplementary_info_view.dart';
import '../modules/login/verifyCode/bindings/verify_code_binding.dart';
import '../modules/login/verifyCode/views/verify_code_view.dart';
import '../modules/login/verifyTel/bindings/verify_tel_binding.dart';
import '../modules/login/verifyTel/views/verify_tel_view.dart';
import '../modules/mine/mineHome/bindings/mine_home_binding.dart';
import '../modules/mine/mineHome/views/mine_home_view.dart';
import '../modules/mine/personInfo/bindings/person_info_binding.dart';
import '../modules/mine/personInfo/views/person_info_view.dart';
import '../modules/networkListen/bindings/network_listen_binding.dart';
import '../modules/networkListen/views/network_listen_view.dart';
import '../modules/orgInviteSetting/bindings/org_invite_setting_binding.dart';
import '../modules/orgInviteSetting/views/org_invite_setting_view.dart';
import '../modules/privacy_policy/privacy_policy/binding.dart';
import '../modules/privacy_policy/privacy_policy/view.dart';
import '../modules/real_name/auth_status/auth_status_binding.dart';
import '../modules/real_name/auth_status/auth_status_page.dart';
import '../modules/real_name/input_idcard/real_name_binding.dart';
import '../modules/real_name/input_idcard/real_name_page.dart';
import '../modules/real_name/upload_idcard/upload_idcard_binding.dart';
import '../modules/real_name/upload_idcard/upload_idcard_page.dart';
import '../modules/real_name/upload_photo/upload_photo_binding.dart';
import '../modules/real_name/upload_photo/upload_photo_page.dart';
import '../modules/workStand/editTopFunction/topFunctionList/bindings/top_function_list_binding.dart';
import '../modules/workStand/editTopFunction/topFunctionList/views/top_function_list_view.dart';
import '../modules/webTest/bindings/web_test_binding.dart';
import '../modules/webTest/views/web_test_view.dart';
import '../modules/webview/web_view_binding.dart';
import '../modules/webview/web_view_page.dart';
import '../modules/workStand/approveAdditional/bindings/approve_additional_binding.dart';
import '../modules/workStand/approveAdditional/views/approve_additional_view.dart';
import '../modules/workStand/approveChooseOption/bindings/approve_choose_option_binding.dart';
import '../modules/workStand/approveChooseOption/views/approve_choose_option_view.dart';
import '../modules/workStand/approveComment/bindings/approve_comment_binding.dart';
import '../modules/workStand/approveComment/views/approve_comment_view.dart';
import '../modules/workStand/approveDetail/bindings/approve_detail_binding.dart';
import '../modules/workStand/approveDetail/views/approve_detail_view.dart';
import '../modules/workStand/approveExport/bindings/approve_export_binding.dart';
import '../modules/workStand/approveExport/views/approve_export_view.dart';
import '../modules/workStand/approveHome/bindings/approve_home_binding.dart';
import '../modules/workStand/approveHome/views/approve_home_view.dart';
import '../modules/workStand/approveList/bindings/approve_list_binding.dart';
import '../modules/workStand/approveList/views/approve_list_view.dart';
import '../modules/workStand/approveListPage/bindings/approve_list_page_binding.dart';
import '../modules/workStand/approveListPage/views/approve_list_page_view.dart';
import '../modules/workStand/chooseCity/bindings/choose_city_binding.dart';
import '../modules/workStand/chooseCity/views/choose_city_view.dart';
import '../modules/workStand/editTopFunction/addTopFunction/bindings/add_top_function_binding.dart';
import '../modules/workStand/editTopFunction/addTopFunction/views/add_top_function_view.dart';
import '../modules/workStand/meetingDetail/bindings/meeting_detail_binding.dart';
import '../modules/workStand/meetingDetail/views/meeting_detail_view.dart';
import '../modules/workStand/meetingJoin/bindings/meeting_join_binding.dart';
import '../modules/workStand/meetingJoin/views/meeting_join_view.dart';
import '../modules/workStand/meetingList/bindings/meeting_list_binding.dart';
import '../modules/workStand/meetingList/views/meeting_list_view.dart';
import '../modules/workStand/meetingReserve/bindings/meeting_reserve_binding.dart';
import '../modules/workStand/meetingReserve/views/meeting_reserve_view.dart';
import '../modules/workStand/regimes/regimesChildPage/bindings/regimes_child_page_binding.dart';
import '../modules/workStand/regimes/regimesChildPage/views/regimes_child_page_view.dart';
import '../modules/workStand/regimes/regimesDetailPage/bindings/regimes_detail_page_binding.dart';
import '../modules/workStand/regimes/regimesDetailPage/views/regimes_detail_page_view.dart';
import '../modules/workStand/regimes/regimesManage/bindings/regimes_manage_binding.dart';
import '../modules/workStand/regimes/regimesManage/views/regimes_manage_view.dart';
import '../modules/workStand/sendApprove/bindings/send_approve_binding.dart';
import '../modules/workStand/sendApprove/views/send_approve_view.dart';
import '../modules/workStand/workFlow/bindings/work_flow_binding.dart';
import '../modules/workStand/workFlow/views/work_flow_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const HOME = Routes.HOME;
  static const LOGIN = Routes.LOGIN;
  static const SPLASH = Routes.SPLASH_PAGE;

  static final routes = [
    GetPage(
        name: _Paths.HOME,
        page: () => const HomeView(),
        binding: HomeBinding(),
        transition: Transition.noTransition),
    GetPage(
        name: _Paths.LOGIN,
        page: () => const LoginView(),
        binding: LoginBinding(),
        transition: Transition.noTransition),
    GetPage(
      name: _Paths.ORGHOME,
      page: () => ContactView(),
      binding: ContactBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_ORG_SCALE,
      page: () => const SettingOrgScaleView(),
      binding: SettingOrgScaleBinding(),
    ),
    GetPage(
      name: _Paths.SEARCH_ORG,
      page: () => SearchOrgView(),
      binding: SearchOrgBinding(),
    ),
    GetPage(
      name: _Paths.ORG_DETAIL,
      page: () => const OrgDetailView(),
      binding: OrgDetailBinding(),
    ),
    GetPage(
      name: _Paths.ORG_VERIFY,
      page: () => const OrgVerifyView(),
      binding: OrgVerifyBinding(),
    ),
    GetPage(
      name: _Paths.MY_FRIEND,
      page: () => const MyFriendView(),
      binding: MyFriendBinding(),
    ),
    GetPage(
      name: _Paths.MY_GROUP,
      page: () => const MyGroupView(),
      binding: MyGroupBinding(),
    ),
    GetPage(
      name: _Paths.SEARCH_ALL,
      page: () => const SearchAllView(),
      binding: SearchAllBinding(),
    ),
    GetPage(
      name: _Paths.SEARCH_GROUP,
      page: () => const SearchGroupView(),
      binding: SearchGroupBinding(),
    ),
    GetPage(
      name: _Paths.PENDING_LIST,
      page: () => const PendingListView(),
      binding: PendingListBinding(),
    ),
    GetPage(
      name: _Paths.FRIEND_APPLY_DETAIL,
      page: () => FriendApplyDetailView(),
      binding: FriendApplyDetailBinding(),
    ),
    GetPage(
      name: _Paths.ORG_APPLY_DETAIL,
      page: () => OrgApplyDetailView(),
      binding: OrgApplyDetailBinding(),
    ),
    GetPage(
        name: _Paths.QR_CODE,
        page: () => const QrCodeView(),
        binding: QrCodeBinding(),
        transition: Transition.noTransition),
    GetPage(
      name: _Paths.CONTACT_ADD_FRIEND,
      page: () => const ContactAddFriendView(),
      binding: ContactAddFriendBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_ORG_NAME,
      page: () => const SettingOrgNameView(),
      binding: SettingOrgNameBinding(),
    ),
    GetPage(
      name: _Paths.PREFERENCE_SETTING,
      page: () => const PreferenceSettingView(),
      binding: PreferenceSettingBinding(),
    ),
    GetPage(
      name: _Paths.VERIFY_CODE,
      page: () => const VerifyCodeView(),
      binding: VerifyCodeBinding(),
    ),
    GetPage(
      name: _Paths.PWD_LOGIN,
      page: () => const PwdLoginView(),
      binding: PwdLoginBinding(),
    ),
    GetPage(
      name: _Paths.VERIFY_TEL,
      page: () => const VerifyTelView(),
      binding: VerifyTelBinding(),
    ),
    GetPage(
      name: _Paths.SUPPLEMENTARY_INFO,
      page: () => const SupplementaryInfoView(),
      binding: SupplementaryInfoBinding(),
    ),
    GetPage(
      name: _Paths.RESET_PWD,
      page: () => const ResetPwdView(),
      binding: ResetPwdBinding(),
    ),
    GetPage(
      name: _Paths.NETWORK_LISTEN,
      page: () => const NetworkListenView(),
      binding: NetworkListenBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH_PAGE,
      page: () => const SplashPageView(),
      binding: SplashPageBinding(),
    ),
    GetPage(
      name: _Paths.WEB_DETAIL,
      page: () => const WebDetailView(),
      binding: WebDetailBinding(),
    ),
    GetPage(
      name: _Paths.QRCODE_LOGIN,
      page: () => const QrcodeLoginView(),
      binding: QrcodeLoginBinding(),
    ),
    GetPage(
        name: _Paths.COMMON_SCROLL_PIC,
        page: () => const CommonScrollPicView(),
        binding: CommonScrollPicBinding(),
        transition: Transition.noTransition),
    GetPage(
      name: _Paths.COMMON_VIDEO_PLAY,
      page: () => const CommonVideoPlayView(),
      binding: CommonVideoPlayBinding(),
    ),
    GetPage(
      name: _Paths.ORG_LIST,
      page: () => const OrgListView(),
      binding: OrgListBinding(),
    ),
    GetPage(
      name: _Paths.CREATE_AND_JOIN,
      page: () => const CreateAndJoinView(),
      binding: CreateAndJoinBinding(),
    ),
    GetPage(
      name: _Paths.ORG_CREATE,
      page: () => const OrgCreateView(),
      binding: OrgCreateBinding(),
    ),
    GetPage(
      name: _Paths.SETTING_ORG_INDUSTRY,
      page: () => const SettingOrgIndustryView(),
      binding: SettingOrgIndustryBinding(),
    ),
    GetPage(
      name: _Paths.DEPT_DETAIL,
      page: () => DeptDetailView(),
      binding: DeptDetailBinding(),
    ),
    GetPage(
      name: _Paths.CHOOSE_ORG_MEMBERS,
      page: () => const ChooseOrgMembersView(),
      binding: ChooseOrgMembersBinding(),
    ),
    GetPage(
      name: _Paths.ADD_DEPT,
      page: () => const AddDeptView(),
      binding: AddDeptBinding(),
    ),
    GetPage(
      name: _Paths.USER_INFO,
      page: () => UserInfoView(),
      binding: UserInfoBinding(),
    ),
    GetPage(
      name: _Paths.INVITE_HOME,
      page: () => const InviteHomeView(),
      binding: InviteHomeBinding(),
    ),
    GetPage(
      name: _Paths.EXTERNAL_CONTACTS,
      page: () => const ExternalContactsView(),
      binding: ExternalContactsBinding(),
    ),
    GetPage(
      name: _Paths.EXTERNAL_ADD,
      page: () => const ExternalAddView(),
      binding: ExternalAddBinding(),
    ),
    GetPage(
      name: _Paths.EXTERNAL_DETAIL,
      page: () => const ExternalDetailView(),
      binding: ExternalDetailBinding(),
    ),
    GetPage(
      name: _Paths.FRIEND_PENDING,
      page: () => const FriendPendingView(),
      binding: FriendPendingBinding(),
    ),
    GetPage(
      name: _Paths.EDIT_DEPT,
      page: () => const EditDeptView(),
      binding: EditDeptBinding(),
    ),
    GetPage(
      name: _Paths.CHOOSE_LEADER,
      page: () => const ChooseLeaderView(),
      binding: ChooseLeaderBinding(),
    ),
    GetPage(
      name: _Paths.DISSOLVE_ORG,
      page: () => const DissolveOrgView(),
      binding: DissolveOrgBinding(),
    ),
    GetPage(
      name: _Paths.ORG_MORE_SETTING,
      page: () => const OrgMoreSettingView(),
      binding: OrgMoreSettingBinding(),
    ),
    GetPage(
      name: _Paths.ORG_DETAIL_SETTING,
      page: () => const OrgDetailSettingView(),
      binding: OrgDetailSettingBinding(),
    ),
    GetPage(
      name: _Paths.ORG_INVITE_SETTING,
      page: () => const OrgInviteSettingView(),
      binding: OrgInviteSettingBinding(),
    ),
    GetPage(
      name: _Paths.ORG_TRANSFER_CREATER,
      page: () => const OrgTransferCreaterView(),
      binding: OrgTransferCreaterBinding(),
    ),
    GetPage(
      name: _Paths.CHOOSE_ALL_ORG_MEMBERS,
      page: () => const ChooseAllOrgMembersView(),
      binding: ChooseAllOrgMembersBinding(),
    ),
    GetPage(
      name: _Paths.USER_INFO_SETTING,
      page: () => const UserInfoSettingView(),
      binding: UserInfoSettingBinding(),
    ),
    GetPage(
      name: _Paths.WORK_FLOW,
      page: () => WorkFlowView(),
      binding: WorkFlowBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_HOME,
      page: () => ApproveHomeView(),
      binding: ApproveHomeBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_LIST,
      page: () => ApproveListView(),
      binding: ApproveListBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_DETAIL,
      page: () => ApproveDetailView(),
      binding: ApproveDetailBinding(),
    ),
    GetPage(
      name: _Paths.SEND_APPROVE,
      page: () => SendApproveView(),
      binding: SendApproveBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_CHOOSE_OPTION,
      page: () => const ApproveChooseOptionView(),
      binding: ApproveChooseOptionBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_LIST_PAGE,
      page: () => ApproveListPageView({}),
      binding: ApproveListPageBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_COMMENT,
      page: () => ApproveCommentView(),
      binding: ApproveCommentBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_ADDITIONAL,
      page: () => ApproveAdditionalView(),
      binding: ApproveAdditionalBinding(),
    ),
    GetPage(
      name: _Paths.CHOOSE_CITY,
      page: () => const ChooseCityView(),
      binding: ChooseCityBinding(),
    ),
    GetPage(
      name: _Paths.MINE_HOME,
      page: () => MineHomeView(),
      binding: MineHomeBinding(),
    ),
    GetPage(
      name: _Paths.PRIVACY_POLICY,
      page: () => PrivacyPolicyPage(),
      binding: PrivacyPolicyBinding(),
    ),
    GetPage(
      name: _Paths.WEB_TEST,
      page: () => const WebTestView(),
      binding: WebTestBinding(),
    ),
    GetPage(
      name: _Paths.APPROVE_EXPORT,
      page: () => ApproveExportView(),
      binding: ApproveExportBinding(),
    ),
    GetPage(
      name: _Paths.ABOUT,
      page: () => AboutPage(),
      binding: AboutPageBinding(),
    ),
    GetPage(
      name: _Paths.MEETING_DETAIL,
      page: () => MeetingDetailView(),
      binding: MeetingDetailBinding(),
    ),
    GetPage(
      name: _Paths.MEETING_LIST,
      page: () => MeetingListView(),
      binding: MeetingListBinding(),
    ),
    GetPage(
      name: _Paths.MEETING_JOIN,
      page: () => MeetingJoinView(),
      binding: MeetingJoinBinding(),
    ),
    GetPage(
      name: _Paths.MEETING_RESERVE,
      page: () => MeetingReserveView(),
      binding: MeetingReserveBinding(),
    ),
    // GetPage(
    //   name: _Paths.IM_CHAGE_PAGE,
    //   page: () => ChatPage(),
    //   binding: ChatBinding(),
    // ),
    GetPage(
      name: _Paths.IM_SYSTEM_NOTIFICATION_LIST,
      page: () => SystemNotificationListPage(),
      binding: SystemNotificationListBinding(),
    ),
    GetPage(
      name: _Paths.IM_SINGLE_CHAT_INFO,
      page: () => SingleChatInfo(),
      binding: SingleChatInfoBinding(),
    ),
    GetPage(
      name: _Paths.IM_GROUP_CHAT_INFO,
      page: () => GroupChatInfoPage(),
      binding: GroupChatInfoBinding(),
    ),
    GetPage(
      name: _Paths.GROUP_MEMBER_PAGE,
      page: () => GroupMembersPage(),
      binding: GroupMemberBinding(),
    ),
    GetPage(
      name: _Paths.PERSON_INFO,
      page: () => const PersonInfoView(),
      binding: PersonInfoBinding(),
    ),
    GetPage(
      name: _Paths.IM_MODIFY_GROUP_NAME,
      page: () => GroupNameModifyPage(),
      binding: GroupNameModifyBinding(),
    ),
    GetPage(
      name: _Paths.TIP_OFF_MAIN,
      page: () => TipOffMainPage(),
      binding: TipOffMainBinding(),
    ),
    GetPage(
      name: _Paths.TIP_OFF_FEEDBACK,
      page: () => TipOffFeedbackPage(),
      binding: TipOffFeedbackBinding(),
    ),
    GetPage(
      name: _Paths.GROUP_CHANGE_INFO,
      page: () => GroupChangeInfoView(),
      binding: GroupChangeInfoBinding(),
    ),
    GetPage(
      name: _Paths.GROUP_MANAGE,
      page: () => const GroupChatManageInfo(),
      binding: GroupChatManageBinding(),
    ),
    GetPage(
      name: _Paths.GROUP_MANAGE_OPERATE,
      page: () => const GroupManageOperateInfo(),
      binding: GroupMemberOperateBinding(),
    ),
    GetPage(
      name: _Paths.GROUP_ADD_MEMBERS,
      page: () => GroupAddMembersView(),
      binding: GroupAddMembersBinding(),
    ),
    GetPage(
      name: _Paths.SCAN_JOIN_GROUP,
      page: () => ScanJoinGroupView(),
      binding: ScanJoinGroupBinding(),
    ),
    GetPage(
      name: _Paths.CHAT_RECORD,
      page: () => MsgRecordPage(),
      binding: MsgRecordBinding(),
    ),
    // GetPage(
    //   name: _Paths.LOCATION_TARGET,
    //   page: () => MapTargetPage(),
    //   binding: MapTargetBinding(),
    // ),
    // GetPage(
    //   name: _Paths.LOCATION_SEARCH,
    //   page: () => MapSearchPage(),
    //   binding: MapSearchBinding(),
    // ),
    GetPage(
      name: _Paths.D_B_USER_SEARCH,
      page: () => DBUserSearchView(),
      binding: DBUserSearchBinding(),
    ),
    GetPage(
      name: _Paths.SEARCH_CHAT,
      page: () => SearchChatPage(),
      binding: SearchChatBinding(),
    ),
    GetPage(
      name: _Paths.SEARCH_CHAT_POINT_PERSION,
      page: () => ChatSearchListPointPersionView(),
      binding: ChatSearchListPointPersionBinding(),
    ),
    GetPage(
      name: _Paths.NOTICE_SETTING,
      page: () => NoticeSettingView(),
      binding: NoticeSettingBinding(),
    ),
    GetPage(
      name: _Paths.SIMPL_GROUP_MEMBERS,
      page: () => SimpleMembersPage(),
      binding: SimpleGroupMemberBinding(),
    ),
    GetPage(
      name: _Paths.INVITE_BY_SESSION,
      page: () => InviteBySessionPage(),
      binding: InviteBySessionBinding(),
    ),
    GetPage(
      name: _Paths.WEB_TIP_LINK,
      page: () => WebTipLinkPage(),
      binding: WebTipLinkBinding(),
    ),
    GetPage(
      name: _Paths.SYSTEM_NOTICE,
      page: () => ImNotificationDetail(),
      binding: ImNotificationDetailBinding(),
    ),
    GetPage(
      name: _Paths.ORG_PERMISSION,
      page: () => OrgPermissionView(),
      binding: OrgPermissionBinding(),
    ),
    GetPage(
      name: _Paths.ADD_PERMISSION,
      page: () => AddPermissionView(),
      binding: AddPermissionBinding(),
    ),
    GetPage(
      name: _Paths.REGIMES_MANAGE,
      page: () => RegimesManageView(),
      binding: RegimesManageBinding(),
    ),
    GetPage(
      name: _Paths.REGIMES_CHILD_PAGE,
      page: () => RegimesChildPageView(),
      binding: RegimesChildPageBinding(),
    ),
    GetPage(
      name: _Paths.REGIMES_DETAIL_PAGE,
      page: () => RegimesDetailPageView(),
      binding: RegimesDetailPageBinding(),
    ),
    GetPage(
      name: _Paths.ORG_PERMISSION_INFO,
      page: () => OrgPermissionInfoView(),
      binding: OrgPermissionInfoBinding(),
    ),
    GetPage(
      name: _Paths.FILE_PICKER,
      page: () => FilePickerPage(),
      binding: FilePickerBinding(),
    ),
    GetPage(
      name: _Paths.WEB_VIEW,
      page: () => WebViewPage(),
      binding: WebViewBinding(),
    ),
    GetPage(
      name: _Paths.REAL_NAME,
      page: () => RealNamePage(),
      binding: RealNameBinding(),
    ),
    GetPage(
      name: _Paths.UPLOAD_IDCARD,
      page: () => UploadIdcardPage(),
      binding: UploadIdcardBinding(),
    ),
    GetPage(
      name: _Paths.UPLOAD_PHOTO,
      page: () => UploadPhotoPage(),
      binding: UploadPhotoBinding(),
    ),
    GetPage(
      name: _Paths.REALNAME_STATUS,
      page: () => AuthStatusPage(),
      binding: AuthStatusBinding(),
    ),
    GetPage(
      name: _Paths.SCHEME,
      binding: SchemeBinding(),
      page: () => SchemePage(),
    ),
    GetPage(
      name: _Paths.ADD_TOP_FUNCTION,
      page: () => const AddTopFunctionView(),
      binding: AddTopFunctionBinding(),
    ),
    GetPage(
      name: _Paths.TOP_FUNCTION_LIST,
      page: () => const TopFunctionListView(),
      binding: TopFunctionListBinding(),
    ),
  ];
}
