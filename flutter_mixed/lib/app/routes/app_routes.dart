part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const LOGIN = _Paths.LOGIN;
  static const DIALOGUE = _Paths.DIALOGUE;
  static const ORGHOME = _Paths.ORGHOME;
  static const SETTING_ORG_SCALE = _Paths.SETTING_ORG_SCALE;
  static const SEARCH_ORG = _Paths.SEARCH_ORG;
  static const ORG_DETAIL = _Paths.ORG_DETAIL;
  static const ORG_VERIFY = _Paths.ORG_VERIFY;
  static const MY_FRIEND = _Paths.MY_FRIEND;
  static const MY_GROUP = _Paths.MY_GROUP;
  static const SEARCH_ALL = _Paths.SEARCH_ALL;
  static const SEARCH_GROUP = _Paths.SEARCH_GROUP;
  static const PENDING_LIST = _Paths.PENDING_LIST;
  static const FRIEND_APPLY_DETAIL = _Paths.FRIEND_APPLY_DETAIL;
  static const ORG_APPLY_DETAIL = _Paths.ORG_APPLY_DETAIL;
  static const QR_CODE = _Paths.QR_CODE;
  static const IMUSER = _Paths.IMUSER;
  static const IMGROUP = _Paths.IMGROUP;
  static const IM_PRIVATE_CHAT_SETUP = _Paths.IM_PRIVATE_CHAT_SETUP;
  static const CONTACT_ADD_FRIEND = _Paths.CONTACT_ADD_FRIEND;
  static const TEL_CONTACT = _Paths.TEL_CONTACT;
  static const IM_USER_INFO = _Paths.IM_USER_INFO;
  static const IM_USER_CODE = _Paths.IM_USER_CODE;
  static const IM_TEAM_LIST = _Paths.IM_TEAM_LIST;
  static const IM_ADD_USER = _Paths.IM_ADD_USER;
  static const SETTING_ORG_NAME = _Paths.SETTING_ORG_NAME;
  static const PREFERENCE_SETTING = _Paths.PREFERENCE_SETTING;
  static const VERIFY_CODE = _Paths.VERIFY_CODE;
  static const PWD_LOGIN = _Paths.PWD_LOGIN;
  static const VERIFY_TEL = _Paths.VERIFY_TEL;
  static const SUPPLEMENTARY_INFO = _Paths.SUPPLEMENTARY_INFO;
  static const RESET_PWD = _Paths.RESET_PWD;
  static const NETWORK_LISTEN = _Paths.NETWORK_LISTEN;
  static const SPLASH_PAGE = _Paths.SPLASH_PAGE;
  static const WEB_DETAIL = _Paths.WEB_DETAIL;
  static const QRCODE_LOGIN = _Paths.QRCODE_LOGIN;
  static const COMMON_SCROLL_PIC = _Paths.COMMON_SCROLL_PIC;
  static const COMMON_VIDEO_PLAY = _Paths.COMMON_VIDEO_PLAY;
  static const APPROVE_NOTICE = _Paths.APPROVE_NOTICE;
  static const SYSTEM_NOTICE = _Paths.SYSTEM_NOTICE;
  static const COMPANY_NOTICE = _Paths.COMPANY_NOTICE;
  static const ORG_LIST = _Paths.ORG_LIST;
  static const CREATE_AND_JOIN = _Paths.CREATE_AND_JOIN;
  static const ORG_CREATE = _Paths.ORG_CREATE;
  static const SETTING_ORG_INDUSTRY = _Paths.SETTING_ORG_INDUSTRY;
  static const DEPT_DETAIL = _Paths.DEPT_DETAIL;
  static const CHOOSE_ORG_MEMBERS = _Paths.CHOOSE_ORG_MEMBERS;
  static const ADD_DEPT = _Paths.ADD_DEPT;
  static const USER_INFO = _Paths.USER_INFO;
  static const INVITE_HOME = _Paths.INVITE_HOME;
  static const EXTERNAL_CONTACTS = _Paths.EXTERNAL_CONTACTS;
  static const EXTERNAL_ADD = _Paths.EXTERNAL_ADD;
  static const EXTERNAL_DETAIL = _Paths.EXTERNAL_DETAIL;
  static const FRIEND_PENDING = _Paths.FRIEND_PENDING;
  static const EDIT_DEPT = _Paths.EDIT_DEPT;
  static const CHOOSE_LEADER = _Paths.CHOOSE_LEADER;
  static const DISSOLVE_ORG = _Paths.DISSOLVE_ORG;
  static const ORG_MORE_SETTING = _Paths.ORG_MORE_SETTING;
  static const ORG_DETAIL_SETTING = _Paths.ORG_DETAIL_SETTING;
  static const ORG_INVITE_SETTING = _Paths.ORG_INVITE_SETTING;
  static const ORG_TRANSFER_CREATER = _Paths.ORG_TRANSFER_CREATER;
  static const CHOOSE_ALL_ORG_MEMBERS = _Paths.CHOOSE_ALL_ORG_MEMBERS;
  static const USER_INFO_SETTING = _Paths.USER_INFO_SETTING;
  static const WORK_FLOW = _Paths.WORK_FLOW;
  static const APPROVE_HOME = _Paths.APPROVE_HOME;
  static const APPROVE_LIST = _Paths.APPROVE_LIST;
  static const APPROVE_DETAIL = _Paths.APPROVE_DETAIL;
  static const SEND_APPROVE = _Paths.SEND_APPROVE;
  static const APPROVE_CHOOSE_OPTION = _Paths.APPROVE_CHOOSE_OPTION;
  static const APPROVE_LIST_PAGE = _Paths.APPROVE_LIST_PAGE;
  static const APPROVE_COMMENT = _Paths.APPROVE_COMMENT;
  static const APPROVE_ADDITIONAL = _Paths.APPROVE_ADDITIONAL;
  static const CHOOSE_CITY = _Paths.CHOOSE_CITY;
  static const MINE_HOME = _Paths.MINE_HOME;
  static const PRIVACY_POLICY = _Paths.PRIVACY_POLICY;
  static const WEB_TEST = _Paths.WEB_TEST;
  static const APPROVE_EXPORT = _Paths.APPROVE_EXPORT;
  static const ABOUT = _Paths.ABOUT;
  static const MEETING_DETAIL = _Paths.MEETING_DETAIL;
  static const MEETING_LIST = _Paths.MEETING_LIST;
  static const MEETING_JOIN = _Paths.MEETING_JOIN;
  static const MEETING_RESERVE = _Paths.MEETING_RESERVE;
  static const IM_CHAGE_PAGE = _Paths.IM_CHAGE_PAGE;
  static const IM_SYSTEM_NOTIFICATION_LIST = _Paths.IM_SYSTEM_NOTIFICATION_LIST;
  static const IM_SYSTEM_NOTIFICATION_DETAIL =
      _Paths.IM_SYSTEM_NOTIFICATION_DETAIL;
  static const IM_SINGLE_CHAT_INFO = _Paths.IM_SINGLE_CHAT_INFO;
  static const IM_GROUP_CHAT_INFO = _Paths.IM_GROUP_CHAT_INFO;
  static const GROUP_MEMBER_PAGE = _Paths.GROUP_MEMBER_PAGE;
  static const PERSON_INFO = _Paths.PERSON_INFO;
  static const IM_MODIFY_GROUP_NAME = _Paths.IM_MODIFY_GROUP_NAME;
  static const TIP_OFF_MAIN = _Paths.TIP_OFF_MAIN;
  static const TIP_OFF_FEEDBACK = _Paths.TIP_OFF_FEEDBACK;
  static const TIP_OFF_APPLY = _Paths.TIP_OFF_APPLY;
  static const GROUP_CHANGE_INFO = _Paths.GROUP_CHANGE_INFO;
  static const GROUP_MANAGE = _Paths.GROUP_MANAGE;
  static const GROUP_MANAGE_OPERATE = _Paths.GROUP_MANAGE_OPERATE;
  static const GROUP_ADD_MEMBERS = _Paths.GROUP_ADD_MEMBERS;
  static const SCAN_JOIN_GROUP = _Paths.SCAN_JOIN_GROUP;
  static const CHAT_RECORD = _Paths.CHAT_RECORD;
  static const LOCATION_TARGET = _Paths.LOCATION_TARGET;
  static const LOCATION_SEARCH = _Paths.LOCATION_SEARCH;
  static const D_B_USER_SEARCH = _Paths.D_B_USER_SEARCH;
  static const SEARCH_CHAT = _Paths.SEARCH_CHAT;
  static const SEARCH_CHAT_POINT_PERSION = _Paths.SEARCH_CHAT_POINT_PERSION;
  static const NOTICE_SETTING = _Paths.NOTICE_SETTING;
  static const SIMPL_GROUP_MEMBERS = _Paths.SIMPL_GROUP_MEMBERS;
  static const INVITE_BY_SESSION = _Paths.INVITE_BY_SESSION;
  static const WEB_TIP_LINK = _Paths.WEB_TIP_LINK;
  static const ORG_PERMISSION = _Paths.ORG_PERMISSION;
  static const ADD_PERMISSION = _Paths.ADD_PERMISSION;
  static const REGIMES_MANAGE = _Paths.REGIMES_MANAGE;
  static const REGIMES_CHILD_PAGE = _Paths.REGIMES_CHILD_PAGE;
  static const REGIMES_DETAIL_PAGE = _Paths.REGIMES_DETAIL_PAGE;
  static const ORG_PERMISSION_INFO = _Paths.ORG_PERMISSION_INFO;
  static const FILE_PICKER = _Paths.FILE_PICKER;
  static const WEB_VIEW = _Paths.WEB_VIEW;
  static const REAL_NAME = _Paths.REAL_NAME;
  static const UPLOAD_IDCARD = _Paths.UPLOAD_IDCARD;
  static const UPLOAD_PHOTO = _Paths.UPLOAD_PHOTO;
  static const REALNAME_STATUS = _Paths.REALNAME_STATUS;
  static const SCHEME = _Paths.SCHEME;
  static const ADD_TOP_FUNCTION = _Paths.ADD_TOP_FUNCTION;
  static const TOP_FUNCTION_LIST = _Paths.TOP_FUNCTION_LIST;
  static const SEARCH_TOP_FUNCTION = _Paths.SEARCH_TOP_FUNCTION;
  static const SEND_FEED_BACK = _Paths.SEND_FEED_BACK;
  static const FEED_BACK_LIST = _Paths.FEED_BACK_LIST;
  static const FEED_BACK_DETAIL = _Paths.FEED_BACK_DETAIL;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home'; //首页
  static const LOGIN = '/login'; //登录
  static const DIALOGUE = '/dialogue';
  static const ORGHOME = '/orgHome'; //通讯录首页
  static const SETTING_ORG_SCALE = '/setting-org-scale'; //设置企业规模
  static const SEARCH_ORG = '/search-org'; //搜索公司
  static const ORG_DETAIL = '/org-detail'; //公司详情
  static const ORG_VERIFY = '/org-verify'; //公司验证信息
  static const MY_FRIEND = '/my-friend';
  static const MY_GROUP = '/my-group';
  static const SEARCH_ALL = '/search-all';
  static const SEARCH_GROUP = '/search-group';
  static const PENDING_LIST = '/pending-list';
  static const FRIEND_APPLY_DETAIL = '/friend-apply-detail';
  static const ORG_APPLY_DETAIL = '/org-apply-detail';
  static const QR_CODE = '/qr-code';
  static const IMUSER = '/im-user';
  static const IMGROUP = '/im-group';
  static const IM_PRIVATE_CHAT_SETUP = '/im-private-chat-setup';
  static const CONTACT_ADD_FRIEND = '/contact-add-friend';
  static const TEL_CONTACT = '/tel-contact';
  static const IM_USER_INFO = '/im-user-info';
  static const IM_USER_CODE = '/im-user-code';
  static const IM_TEAM_LIST = '/im-team-list';
  static const IM_ADD_USER = '/im-add-user';
  static const SETTING_ORG_NAME = '/setting-org-name';
  static const PREFERENCE_SETTING = '/preference-setting';
  static const VERIFY_CODE = '/verify-code';
  static const PWD_LOGIN = '/pwd-login';
  static const VERIFY_TEL = '/verify-tel';
  static const SUPPLEMENTARY_INFO = '/supplementary-info';
  static const RESET_PWD = '/reset-pwd';
  static const NETWORK_LISTEN = '/network-listen';
  static const SPLASH_PAGE = '/splash-page';
  static const WEB_DETAIL = '/web-detail';
  static const QRCODE_LOGIN = '/qrcode-login';
  static const COMMON_SCROLL_PIC = '/common-scroll-pic';
  static const COMMON_VIDEO_PLAY = '/common-video-play';
  static const APPROVE_NOTICE = '/approve-notice';
  static const SYSTEM_NOTICE = '/system-notice';
  static const COMPANY_NOTICE = '/company-notice';
  static const ORG_LIST = '/org-list'; //其他企业
  static const CREATE_AND_JOIN = '/create-and-join'; //创建和加入
  static const ORG_CREATE = '/org-create';
  static const SETTING_ORG_INDUSTRY = '/setting-org-industry';
  static const DEPT_DETAIL = '/dept-detail';
  static const CHOOSE_ORG_MEMBERS = '/choose-org-members';
  static const ADD_DEPT = '/add-dept';
  static const USER_INFO = '/user-info';
  static const INVITE_HOME = '/invite-home';
  static const EXTERNAL_CONTACTS = '/external-contacts';
  static const EXTERNAL_ADD = '/external-add';
  static const EXTERNAL_DETAIL = '/external-detail';
  static const FRIEND_PENDING = '/friend-pending';
  static const EDIT_DEPT = '/edit-dept';
  static const CHOOSE_LEADER = '/choose-leader';
  static const DISSOLVE_ORG = '/dissolve-org';
  static const ORG_MORE_SETTING = '/org-more-setting';
  static const ORG_DETAIL_SETTING = '/org-detail-setting';
  static const ORG_INVITE_SETTING = '/org-invite-setting';
  static const ORG_TRANSFER_CREATER = '/org-transfer-creater';
  static const CHOOSE_ALL_ORG_MEMBERS = '/choose-all-org-members';
  static const USER_INFO_SETTING = '/user-info-setting';
  static const WORK_FLOW = '/work-flow';
  static const APPROVE_HOME = '/approve-home';
  static const APPROVE_LIST = '/approve-list';
  static const APPROVE_DETAIL = '/approve-detail';
  static const SEND_APPROVE = '/send-approve';
  static const APPROVE_CHOOSE_OPTION = '/approve-choose-option';
  static const APPROVE_LIST_PAGE = '/approve-list-page';
  static const APPROVE_COMMENT = '/approve-comment';
  static const APPROVE_ADDITIONAL = '/approve-additional';
  static const CHOOSE_CITY = '/choose-city';
  static const MINE_HOME = '/mine-home';
  static const PRIVACY_POLICY = '/privacy_policy_page';
  static const WEB_TEST = '/web-test';
  static const APPROVE_EXPORT = '/approve-export';
  static const ABOUT = '/about';
  static const MEETING_DETAIL = '/meeting-detail';
  static const MEETING_LIST = '/meeting-list';
  static const MEETING_JOIN = '/meeting-join';
  static const MEETING_RESERVE = '/meeting-reserve';
  static const IM_CHAGE_PAGE = '/im_chat'; // 单聊，群聊消息页面
  static const IM_SINGLE_CHAT_INFO = '/single_chat_info'; // 单聊，设置
  static const IM_GROUP_CHAT_INFO = '/group_chat_info'; // 单聊，设置
  static const IM_SYSTEM_NOTIFICATION_LIST =
      '/im_system_notification_list'; // im 系统通知列表
  static const IM_SYSTEM_NOTIFICATION_DETAIL =
      '/im_system_notification_detail'; // im 系统通知列表
  static const GROUP_MEMBER_PAGE = '/group_member_page';
  static const PERSON_INFO = '/person-info';
  static const IM_MODIFY_GROUP_NAME = '/im_modify_group_name';
  static const TIP_OFF_MAIN = '/tip_off_main';
  static const TIP_OFF_FEEDBACK = '/tip_off_feedback';
  static const TIP_OFF_APPLY = '/tip_off_apply';

  static const GROUP_CHANGE_INFO = '/group-change-info';
  static const GROUP_MANAGE = '/group-manage';
  static const GROUP_MANAGE_OPERATE = '/group-manage-operate';
  static const GROUP_ADD_MEMBERS = '/group-add-members';
  static const SCAN_JOIN_GROUP = '/scan-join-group';
  static const CHAT_RECORD = '/chat_record';

  static const LOCATION_TARGET = '/location_target';
  static const LOCATION_SEARCH = '/location_search';

  static const D_B_USER_SEARCH = '/d-b-user-search';
  static const SEARCH_CHAT = '/search_chat';
  static const SEARCH_CHAT_POINT_PERSION = '/search_chat_point_persion';
  static const NOTICE_SETTING = '/notice-setting';
  static const SIMPL_GROUP_MEMBERS = '/SimepleMembersPage';
  static const INVITE_BY_SESSION = '/invite_by_session';
  static const WEB_TIP_LINK = '/web_tip_link';
  static const ORG_PERMISSION = '/org_permission';
  static const ADD_PERMISSION = '/add_permission';
  static const REGIMES_MANAGE = '/regimes-manage';
  static const REGIMES_CHILD_PAGE = '/regimes-child-page';
  static const REGIMES_DETAIL_PAGE = '/regimes-detail-page';
  static const ORG_PERMISSION_INFO = '/org-permission-info';
  static const FILE_PICKER = '/file_picker';
  static const WEB_VIEW = '/web_view_page';
  static const REAL_NAME = '/real_name';
  static const UPLOAD_IDCARD = '/upload_idcard';
  static const UPLOAD_PHOTO = '/upload_photo';
  static const REALNAME_STATUS = '/realname_status';
  static const SCHEME = '/scheme';
  static const ADD_TOP_FUNCTION = '/add-top-function';
  static const TOP_FUNCTION_LIST = '/top-function-list';
  static const SEARCH_TOP_FUNCTION = '/search-top-function';
  static const SEND_FEED_BACK = '/send-feed-back';
  static const FEED_BACK_LIST = '/feed-back-list';
  static const FEED_BACK_DETAIL = '/feed-back-detail';
}
