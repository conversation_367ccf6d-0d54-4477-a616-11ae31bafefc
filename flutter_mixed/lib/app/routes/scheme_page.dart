import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/logger/logger.dart';

class SchemePage extends StatefulWidget {
  final String routeData;

  const SchemePage({
    super.key,
    required this.routeData,
  });

  @override
  State<SchemePage> createState() => _SchemePageState();
}

class _SchemePageState extends State<SchemePage> {
  @override
  void initState() {
    super.initState();
    _handleRoute();
  }

  void _handleRoute() {
    try {
      // 解析路由数据
      final Map<String, dynamic> routeMap = json.decode(widget.routeData);
      final String route = routeMap['route'] as String;
      final Map<String, dynamic> params = routeMap['params'] as Map<String, dynamic>;

      // 根据路由跳转到对应页面
      switch (route) {
        case '/approve-detail':
          _navigateToApproveDetail(params);
          break;
        // 添加其他路由处理
        default:
          logger('Unknown route: $route');
          break;
      }
    } catch (e) {
      logger('Error parsing route data: $e');
    }
  }

  void _navigateToApproveDetail(Map<String, dynamic> params) {
    // 这里替换成你的审批详情页面路由
    Navigator.pushNamed(
      context,
      '/approve-detail',
      arguments: params,
    );
  }

  @override
  Widget build(BuildContext context) {
    // 返回一个加载中的页面，因为路由处理完成后会立即跳转
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
} 