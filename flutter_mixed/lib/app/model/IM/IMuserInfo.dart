class IMUserInfo {
  IMUserInfo({
    this.publicInfo,
    this.friendInfo,
    this.blackInfo,
    required this.isFriendship,
    required this.isBlacklist,
    required this.userID,
    required this.nickname,
    required this.faceURL,
    required this.gender,
    required this.phoneNumber,
    required this.birth,
    required this.birthTime,
    required this.email,
    required this.ex,
    required this.createTime,
    this.remark,
    required this.globalRecvMsgOpt,
    this.allowAddFriend,
    this.allowBeep,
    this.allowVibration,
    this.forbidden,
  });
  late final dynamic publicInfo;
  late final dynamic friendInfo;
  late final dynamic blackInfo;
  late final bool isFriendship;
  late final bool isBlacklist;
  late final String userID;
  late final String nickname;
  late final String faceURL;
  late final int gender;
  late final String phoneNumber;
  late final int birth;
  late final String birthTime;
  late final String email;
  late final String ex;
  late final int createTime;
  late final dynamic remark;
  late final int globalRecvMsgOpt;
  late final dynamic allowAddFriend;
  late final dynamic allowBeep;
  late final dynamic allowVibration;
  late final dynamic forbidden;
  
  IMUserInfo.fromJson(Map<String, dynamic> json){
    publicInfo = json['publicInfo'];
    friendInfo = json['friendInfo'];
    blackInfo = json['blackInfo'];
    isFriendship = json['isFriendship'];
    isBlacklist = json['isBlacklist'];
    userID = json['userID'];
    nickname = json['nickname'];
    faceURL = json['faceURL'];
    gender = json['gender'];
    phoneNumber = json['phoneNumber'];
    birth = json['birth'];
    birthTime = json['birthTime'];
    email = json['email'];
    ex = json['ex'];
    createTime = json['createTime'];
    remark = json['remark'];
    globalRecvMsgOpt = json['globalRecvMsgOpt'];
    allowAddFriend = json['allowAddFriend'];
    allowBeep = json['allowBeep'];
    allowVibration = json['allowVibration'];
    forbidden = json['forbidden'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['publicInfo'] = publicInfo;
    _data['friendInfo'] = friendInfo;
    _data['blackInfo'] = blackInfo;
    _data['isFriendship'] = isFriendship;
    _data['isBlacklist'] = isBlacklist;
    _data['userID'] = userID;
    _data['nickname'] = nickname;
    _data['faceURL'] = faceURL;
    _data['gender'] = gender;
    _data['phoneNumber'] = phoneNumber;
    _data['birth'] = birth;
    _data['birthTime'] = birthTime;
    _data['email'] = email;
    _data['ex'] = ex;
    _data['createTime'] = createTime;
    _data['remark'] = remark;
    _data['globalRecvMsgOpt'] = globalRecvMsgOpt;
    _data['allowAddFriend'] = allowAddFriend;
    _data['allowBeep'] = allowBeep;
    _data['allowVibration'] = allowVibration;
    _data['forbidden'] = forbidden;
    return _data;
  }
}