
import 'package:flutter/material.dart';

/// AI 入口 - ‘订餐’ 气泡
class AiOrderMealBubble extends StatelessWidget {

  VoidCallback? orderMealInvoke;

  AiOrderMealBubble({super.key , this.orderMealInvoke});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: (){
        orderMealInvoke?.call();
      },
      child: Container(
        child: Text('订餐'),
      ),
    );
  }

}