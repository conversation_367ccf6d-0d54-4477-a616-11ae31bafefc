import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';

import 'package:get/get.dart';
import '../../../../platform/im_platform_plugin.dart';
import '../controllers/news_home_controller.dart';

class NewsHomeView extends GetView<NewsHomeController> {
  const NewsHomeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    NewsHomeController controller = Get.put(NewsHomeController());
    return Scaffold(
        body: Container(
          child: ImPlatformPlugin().backImPlatformView()
        ));
  }

  
}
