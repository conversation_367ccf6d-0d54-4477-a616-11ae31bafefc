import 'dart:async';
import 'dart:io';

import 'package:flutter/animation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';
import 'package:kumi_popup_window/kumi_popup_window.dart';

import '../../../../permission/permission_util.dart';

class NewsHomeController extends GetxController {

  RxBool isRefesh = false.obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  popComponent(offsetY) {
    showPopupWindow(
      Get.context!,
      gravity: KumiPopupGravity.rightTop,
      bgColor: Colors.transparent,
      clickOutDismiss: true,
      clickBackDismiss: true,
      customAnimation: false,
      customPop: false,
      customPage: false,
      underStatusBar: false,
      underAppBar: false,
      offsetX: 10,
      offsetY: offsetY,
      duration: const Duration(milliseconds: 200),
      childFun: (pop) {
        return Container(
          width: 97,
          height: 150,
          key: GlobalKey(),
          decoration: BoxDecoration(
            color: ColorConfig.whiteColor,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 7,
                offset: Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: Column(
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(Get.context!);
                  RouteHelper.route(Routes.SEARCH_ORG, arguments: {'type': 2});

                },
                child: Container(
                  height: 50,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: ColorConfig.lineColor,
                        width: 1, // 设置下边框宽度为 2
                        style: BorderStyle.solid, // 设置下边框样式为实线
                      ),
                    ),
                  ),
                  child: const Text(
                    "添加好友",
                    style: TextStyle(
                        fontSize: 15, color: ColorConfig.mainTextColor),
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(Get.context!);
                  Get.toNamed(Routes.GROUP_ADD_MEMBERS, arguments: {});
                },
                child: Container(
                  height: 50,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: ColorConfig.lineColor,
                        width: 1,
                        style: BorderStyle.solid, // 设置下边框样式为实线
                      ),
                    ),
                  ),
                  child: const Text(
                    "创建群组",
                    style: TextStyle(
                        fontSize: 15, color: ColorConfig.mainTextColor),
                  ),
                ),
              ),
              InkWell(
                onTap: () async {
                  Navigator.pop(Get.context!);
                  var r = await PermissionUtil.checkCameraPermission(
                      Get.context!,
                      tip: scanPermissionTip);
                  if (!r) return;
                  Get.toNamed(Routes.QR_CODE,arguments: {'type':1});
                },
                child: Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: const Text(
                    "扫一扫",
                    style: TextStyle(
                        fontSize: 15, color: ColorConfig.mainTextColor),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
