import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/retrofit/datasource/real_name_datasource.dart';
import 'package:flutter_mixed/app/retrofit/entity/real_name/verify_idcard_req.dart';
import 'package:flutter_mixed/app/utils/loading/loading_utils.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../../logger/logger.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/widgets/widgets.dart';
import '../../../im/route/route_helper.dart';
import '../../../routes/app_pages.dart';

class RealNameController extends GetxController{
  var nameController = TextEditingController();
  var idcardController = TextEditingController();

  RxBool isIdcardRight = false.obs;
  RxBool isNameRight = false.obs;

  var canBack = false;

  @override
  void onInit() {
    super.onInit();
    canBack = Get.arguments["canBack"]??false;
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> verifyIdcard(BuildContext context) async {
    if(isIdcardRight.value && isNameRight.value){
      try{
        LoadingUtils.showLoading(context,loadingText: "验证中...");
        var realNameDataSource = RealNameDataSource(retrofitDio,baseUrl:Host.HOST);
        var verifyIdcardReq = VerifyIdcardRequest(
            idCardNum: idcardController.text,
            name: nameController.text
        );
        var resp = await realNameDataSource.verifyIdcard(verifyIdcardReq);
        LoadingUtils.hideLoading();
        if(resp.success()){
          logger("========校验成功========");
          RouteHelper.route(Routes.UPLOAD_IDCARD,
              arguments: {"keyPrefix": (resp.data?.keyPrefix??"")+(resp.data?.token?.fileId??""),
                "idCardNum":idcardController.text ,"canBack": canBack});
        }else{
          logger("========校验失败========");
          toast("校验身份证失败");
        }
      }catch(e){
        logger("========校验失败catch========");
        e.printError();
        toast("校验失败");
        LoadingUtils.hideLoading();
      }

    }
  }
}