import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/real_name/input_idcard/real_name_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../common/config/config.dart';
import '../../../common/widgets/no_double_click_fun.dart';
import '../../../routes/app_pages.dart';

class RealNamePage extends GetView<RealNameController>{
  @override
  Widget build(BuildContext context) {
    return PopScope(canPop: controller.canBack,child: ToolBar(
        title: "身份认证",
        backFunction: (){
          if(controller.canBack){
            Get.back();
          }
        },
        body:Stack(
          children: [
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(28),
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    child: Text("请输入本人身份证信息",
                        style: TextStyle(fontSize: 20, color: Color(0xff3d3d3d),
                            fontWeight: FontWeight.w500)
                    ),
                  ),
                  SizedBox(height: 4,),
                  Text("根据《网络安全法》和《个人信息保护法》规定，用户要求完成实名认证",
                      style: TextStyle(fontSize: 12, color: Color(0x99000000) )),
                  SizedBox(height: 16,),
                  Container(
                    height: 0.5,
                    color: Color(0xffF2F2F6),
                  ),
                  SizedBox(height: 16,),
                  Container(
                    decoration: BoxDecoration(
                        color: Color(0xffF6F7FB),
                        borderRadius: BorderRadius.all(Radius.circular(4))
                    ),
                    height: 46,
                    child: Row(
                      children: [
                        Container(
                          height: 46,
                          padding: EdgeInsets.only(left: 16,top: 12,right: 16,bottom: 12),
                          child: Text("真实姓名",style: TextStyle(fontSize: 16,color: Color(0xe5222222)),),
                        ),
                        Expanded(
                            child: TextField(
                              controller: controller.nameController,
                              decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "请输入",
                                  hintStyle: TextStyle(fontSize: 16,color: Color(0x4c222222) )
                              ),
                              onChanged: (value){
                                controller.isNameRight.value = value.isNotEmpty;
                              },
                            )
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 12,),
                  Container(
                    decoration: BoxDecoration(
                        color: Color(0xffF6F7FB),
                        borderRadius: BorderRadius.all(Radius.circular(4))
                    ),
                    height: 46,
                    child: Row(
                      children: [
                        Container(
                          height: 46,
                          padding: EdgeInsets.only(left: 16,top: 12,right: 16,bottom: 12),
                          child: Text("证件号码",style: TextStyle(fontSize: 16,color: Color(0xe5222222)),),
                        ),
                        Expanded(
                            child: TextField(
                              controller: controller.idcardController,
                              keyboardType: TextInputType.text,
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(18),
                                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]'))
                              ],
                              decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "请输入",
                                  hintStyle: TextStyle(fontSize: 16,color: Color(0x4c222222) )
                              ),
                              onChanged:(value){
                                controller.isIdcardRight.value = value.length == 18;
                              } ,
                            )
                        )
                      ],
                    ),

                  ),
                  SizedBox(height: 6,),
                  Obx(
                          ()=>Visibility(
                        visible: (!controller.isIdcardRight.value) && (controller.idcardController.text.isNotEmpty),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.only(left: 8),
                          child: Text("身份证信息核验失败,请填写有效的身份证号",
                              style: TextStyle(fontSize: 14, color: Color(0xffEC392C) )),
                        ) ,
                      )
                  )

                ],
              ) ,
            ),
            Positioned(
                left: 16,
                right: 16,
                bottom: DeviceUtils().bottom.value + 16,
                child: Obx(()=>Container(
                  width: double.infinity,
                  padding: const EdgeInsets.only(left: 0, right: 0),
                  height: 48,
                  child: CupertinoButton(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                      color: (controller.isIdcardRight.value
                          && controller.isNameRight.value )
                          ?ColorConfig.themeCorlor:ColorConfig.btnGrayColor,
                      borderRadius:
                      const BorderRadius.all(Radius.circular(8)),
                      pressedOpacity: 0.5,
                      child: const Text(
                        '下一步',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      onPressed: NoDoubleClickFun(tapDuration: 2,onPressed: (){
                        controller.verifyIdcard(context);
                      } ).noDoublePressed),
                ))
            )
          ],
        )
    ),);
  }
}