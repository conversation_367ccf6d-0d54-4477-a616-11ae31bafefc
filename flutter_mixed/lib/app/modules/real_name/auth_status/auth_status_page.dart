import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/real_name/auth_status/auth_status_controller.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../logger/logger.dart';
import '../../../common/config/config.dart';
import '../../../routes/app_pages.dart';

class AuthStatusPage extends GetView<AuthStatusController>{
    @override
  Widget build(BuildContext context) {
    return PopScope(canPop: controller.canBack,child: ToolBar(
        appBarBackgroundColor:Color(0xffC4D6FF) ,
        backFunction: (){
          logger("=======status  backFunction========");
          if(controller.canBack){
            Get.until((route) => route.settings.name == Routes.HOME);
          }
        },
        actions: [
          Obx(()=> Visibility(
              visible: controller.authStatus == 3,
              child: IconButton(onPressed: (){
                controller.getAuthStatus();
              }, icon: Image.asset(AssetsRes.ICON_REFRESH_CIRCLE,height: 18,),)
          ))
        ],
        body: Obx(
                () => Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xffC4D6FF),Colors.white,Colors.white,
                            Colors.white,Colors.white,Colors.white]
                      )
                  ),
                  child:  Column(
                    children: [
                      Container(
                        height: 80,
                        child:authStatus() ,
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 16),
                        alignment: Alignment.center,
                        child: Text(controller.getAuthText(),
                          style: TextStyle(fontSize: 20,color: Color(0xff1F2937)
                              ,fontWeight: FontWeight.w500),),
                      ),
                      Visibility(
                          visible: controller.authStatus.value != 4,
                          child: Container(
                            margin: EdgeInsets.only(top: 24),
                            alignment: Alignment.center,
                            child: Text(controller.authStatus.value == 3?
                            "您的认证信息已提交，正在审核中":"提交信息与身份证信息不符,请重新认证",
                              style: TextStyle(fontSize: 14,
                                  color: controller.authStatus.value == 3?
                                  Color(0xff4B5563) : Color(0xffEC392C)),),
                          )
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 36,left: 16,right: 16),
                        padding: EdgeInsets.all(20),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow:[BoxShadow(
                                color: Color(0x19000000),
                                blurRadius: 6
                            )]
                        ),
                        child: Column(
                          children: [
                            Text("已提交的认证信息",style: TextStyle(fontSize: 14,
                                fontWeight: FontWeight.w500,color: Color(0xff3d3d3d)),),
                            14.gap,
                            Row(
                              children: [
                                Text("真实姓名",style: TextStyle(fontSize: 14,color: Color(0xff222222)),),
                                Expanded(
                                    child: Text(
                                      textAlign: TextAlign.end,
                                      controller.name.value,
                                      style: TextStyle(fontSize: 14,color: Color(0xff222222)),
                                    )
                                )
                              ],
                            ),
                            Row(
                              children: [
                                Text("身份证号",style: TextStyle(fontSize: 14,color: Color(0xff222222)),),
                                Expanded(
                                    child: Text(
                                      textAlign: TextAlign.end,
                                      controller.idcard.value,
                                      style: TextStyle(fontSize: 14,color: Color(0xff222222)),
                                    )
                                )
                              ],
                            ),
                            Row(
                              children: [
                                Text("提交时间",style: TextStyle(fontSize: 14,color: Color(0xff222222)),),
                                Expanded(
                                    child: Text(
                                      textAlign: TextAlign.end,
                                      controller.createTime.value,
                                      style: TextStyle(fontSize: 14,color: Color(0xff222222)),
                                    )
                                )
                              ],
                            )

                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                    left: 12,
                    right: 12,
                    bottom: DeviceUtils().bottom.value + 16,
                    child: Visibility(
                        visible: controller.authStatus.value != 4 && controller.authStatus.value != 3,
                        child:Container(
                          width: double.infinity,
                          height: 48,
                          padding: const EdgeInsets.only(left: 0, right: 0),
                          child: CupertinoButton(
                              color: ColorConfig.themeCorlor,
                              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                              borderRadius:
                              const BorderRadius.all(Radius.circular(8)),
                              pressedOpacity: 0.5,
                              child: Text(
                                "重新认证  ",
                                style: const TextStyle(color: Colors.white, fontSize: 16),
                              ),
                              onPressed: (){
                                Get.offAndToNamed(Routes.REAL_NAME,arguments: {"canBack":controller.canBack});
                              }
                          ),
                        )
                    )
                )

              ],
            )
        )
    ), );
  }

  Widget authStatus() {
      switch(controller.authStatus.value){
        case 3:
          return Image.asset(AssetsRes.ICON_REALNAME_AUTHING);
        case 4:
          return Image.asset(AssetsRes.ICON_REALNAME_AUTH_SUCCESS);
        default:
          return Image.asset(AssetsRes.ICON_REALNAME_AUTH_FAIL);
      }
  }

}