import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/retrofit/datasource/real_name_datasource.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../logger/logger.dart';
import '../../../common/api/Define.dart';
import '../../../common/api/LoginApi.dart';
import '../../../utils/storage.dart';

class AuthStatusController extends GetxController{
  var userInfo;
  var canBack = false;
  RxInt authStatus = 1.obs;
  RxString name = "".obs;
  RxString idcard = "".obs;
  RxString createTime = "0000".obs;
  @override
  Future<void> onInit() async {
    super.onInit();
    // userInfo = await UserDefault.getData(Define.TOKENKEY);
    canBack = Get.arguments["canBack"]??false;
    authStatus.value = Get.arguments["status"]??1;
    // name = Get.arguments['name'];
    // idcard = Get.arguments['idcard'];
    // update();
    getAuthStatus();
  }

  String getAuthText() {
    switch(authStatus.value){
      case 3:
        return "认证中";
      case 4:
        return "认证成功";
      default:
        return "认证失败";
    }
  }


  void getAuthStatus() async{
    var realNameDataSource = RealNameDataSource(retrofitDio,baseUrl: Host.HOST);
    var resp = await realNameDataSource.getVerifyStatus();
    if(resp.success()){
      name.value = resp.data?.name??"";
      idcard.value = resp.data?.idCardNum??"";
      var date = DateTime.fromMillisecondsSinceEpoch(resp.data?.createTime??0);
      var formatter = DateFormat("yyyy-MM-dd HH:mm:ss");
      createTime.value = formatter.format(date);
      authStatus.value = resp.data?.status??1;
      if(authStatus.value == 4){
        canBack = true;
      }
      logger("=========${name.value}=====${idcard.value}=========${authStatus.value}======$createTime");
    }else{
      toast("获取认证状态失败");
    }
  }
}