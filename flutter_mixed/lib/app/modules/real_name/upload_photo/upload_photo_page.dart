import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/modules/real_name/upload_photo/upload_photo_controller.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../common/widgets/no_double_click_fun.dart';

class UploadPhotoPage extends GetView<UploadPhotoController>{
  @override
  Widget build(BuildContext context) {
    return PopScope(canPop:false, child: ToolBar(
        isLightStatus: true,
        appBarBackgroundColor: Colors.black,
        backgroundColor: Colors.black,
        backFunction: (){},
        actions: [
          TextButton(onPressed: NoDoubleClickFun(tapDuration: 2,onPressed: (){
            if( controller.photoPath.value.isNotEmpty){
              controller.uploadPhoto(context);
            }
          } ).noDoublePressed,
          child:  const Text('确定',style: TextStyle(fontSize: 14 ,color: Colors.white),))
        ],
        body:Obx(() =>Stack(
          children: [
            Positioned(
                top: 32,
                left: 45,
                right: 45,
                child:Visibility(
                    visible: controller.photoPath.value.isEmpty,
                    child: Container(
                      child: Obx(()=> controller.cameraInitSuccess.value?
                      ClipPath(
                        clipper: CenterCliper((DeviceUtils().width.value-90)/2,
                            (DeviceUtils().width.value-90)/2,
                            (DeviceUtils().width.value-90)/2),
                        child:CameraPreview(controller.cameraController),
                      ) :
                      Container(color: Colors.grey,)),
                    )
                )
            ),
            Positioned(
                top: 32,
                left: 0,
                right: 0,
                child: Visibility(
                    visible: controller.photoPath.value.isNotEmpty,
                    child: Container(
                      child:  Image(fit: BoxFit.fitWidth,
                          image: ExtendedFileImageProvider(File(controller.photoPath.value))),
                    )
                )
            ),
            Positioned(
                top: 32,
                left: 45,
                right: 45,
                child: Visibility(
                    visible: controller.photoPath.value.isEmpty,
                    child: Container(
                      child: Image.asset(AssetsRes.BG_TAKE_PHOTO),
                    )
                )
            ),
            Positioned(
                top: Platform.isIOS? 386 : 356,
                left: 45,
                right: 45,
                child: Visibility(
                    visible: controller.photoPath.value.isEmpty,
                    child: Container(
                      alignment: Alignment.center,
                      child: Text("请正对镜头",style: TextStyle(fontSize: 14,color: Color(0xe5ffffff),fontWeight: FontWeight.w700),),
                    )
                )
            ),
            Positioned(
                bottom: 66,
                left: DeviceUtils().width/2.6,
                right: DeviceUtils().width/2.6,
                child: controller.photoPath.value.isEmpty ?
                IconButton(onPressed: (){
                  controller.takePic();
                }, icon: Image.asset(AssetsRes.ICON_TAKE_PHOTO)) :
                Column(
                  children: [
                    IconButton(onPressed: (){
                      controller.photoPath.value = "";
                    }, icon: Image.asset(AssetsRes.ICON_RETAKE)),
                    Text("重新拍照",style: TextStyle(fontSize: 14,color: Colors.white),)
                  ],
                )
            ),
          ],
        )
        )
    ));
  }
}

class CenterCliper extends CustomClipper<Path> {
  double? offsetX;
  double? offsetY;
  double? radius;
  CenterCliper(this.offsetX, this.offsetY, this.radius);

  @override
  Path getClip(Size size) {
    var path = Path();
    path.addOval(
        Rect.fromCircle(center: Offset(offsetX!, offsetY!), radius: radius!));
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return false;
  }
}