import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../../logger/logger.dart';
import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/cos/cosPanResp.dart';
import '../../../common/cos/cos_manager.dart';
import '../../../common/cos/real_name_cos.dart';
import '../../../im/route/route_helper.dart';
import '../../../retrofit/datasource/real_name_datasource.dart';
import '../../../retrofit/entity/real_name/cos_token_req.dart';
import '../../../retrofit/entity/real_name/upload_pic_req.dart';
import '../../../routes/app_pages.dart';
import '../../../utils/loading/loading_utils.dart';
import 'package:image/image.dart' as img;

class UploadPhotoController extends GetxController{
  RxString photoPath = "".obs;
  late List<CameraDescription> _cameras;
  late CameraController cameraController;
  RxBool cameraInitSuccess = false.obs;
  var keyPrefix = "";
  RealNameCos? token;
  var canBack = false;
  var idCardNum = "";
  @override
  Future<void> onInit() async {
    super.onInit();
    _cameras = await availableCameras();
    cameraController = CameraController(_cameras[1], ResolutionPreset.medium);
    cameraController.initialize().then((_){
      cameraInitSuccess.value = true;
    }).catchError((e){
      logger("=======相机初始化异常=======$e");
      cameraInitSuccess.value = false;
    });
    idCardNum = Get.arguments['idCardNum'];
    keyPrefix = Get.arguments['keyPrefix']??"";
    canBack = Get.arguments["canBack"]??false;
  }

  showPreView(BuildContext context) async{
    // CameraPicker.pickFromCamera(
    //   context,
    //   pickerConfig: CameraPickerConfig(
    //     foregroundBuilder: (
    //         BuildContext context,
    //         CameraController? controller,
    //         ) {
    //       return Center(
    //         child: Text(
    //           controller == null
    //               ? 'Waiting for initialize...'
    //               : '${controller.description.lensDirection}',
    //           style: const TextStyle(color: Colors.white),
    //         ),
    //       );
    //     },
    //   ),
    // );

  }

  Future<void> takePic() async {
    if(!cameraController.value.isInitialized){
      toast("相机未初始化");
      return;
    }
    if(cameraController.value.isTakingPicture){
      toast("正在拍摄。。。");
      return;
    }
    try{
      XFile file = await cameraController.takePicture();
      logger("====拍摄成功=====${file.path}");
      //翻转照片
      final image = img.decodeImage(await file.readAsBytes());
      if (image != null) {
        // 水平翻转图片
        final flippedImage = img.flipHorizontal(image); // 根据需要旋转图片，这里-90是为了适配某些前置摄像头的特殊需求
        final flippedBytes = img.encodeJpg(flippedImage);
        final newFile = await File('${(await getTemporaryDirectory()).path}/${DateTime.now().millisecondsSinceEpoch}.jpg')
            .writeAsBytes(flippedBytes);
        photoPath.value = newFile.path;
      }

    }catch(e){
      logger("拍摄发生异常====$e");
      toast("拍摄发生异常");
    }
  }

  void uploadPhoto(BuildContext context) async{
    try{
      if(photoPath.value.isNotEmpty ){
        if(keyPrefix.isNotEmpty){
          LoadingUtils.showLoading(context);
          var realNameDataSource = RealNameDataSource(retrofitDio,baseUrl:Host.HOST);
          //获取新token
          // var cosTokenReq = CosTokenRequest(idCardNum: idCardNum);
          // var resp = await realNameDataSource.getCosToken(cosTokenReq);
          // if(resp.success() && resp.data != null){
          //   token = resp.data!.token;
          //   token?.bucket = resp.data!.bucket;
          // }else{
          //   LoadingUtils.hideLoading();
          //   toast("上传失败");
          //   logger("===上传失败  没有token =");
          //   return;
          // }
          //
          // await CosManager().initPansWithToken(token!);
          //暂时先用网盘的
          await CosManager().initPans();

          String? bucket = await CosManager().bucket();
          var photoResult = await CosUploadHelper.upload(
              photoPath.value, fileId:keyPrefix, userBucket: bucket);

          if(photoResult != null ){
            logger("===上传成功====${photoResult}======");
            //上传结果发送后台

            var uploadPicReq = UploadPicReq(pic1: "",pic2: "",pic: photoResult);
            var resp = await realNameDataSource.uploadPic(uploadPicReq);
            LoadingUtils.hideLoading();
            if(resp.success() && resp.data?.status == 3){
              RouteHelper.offRoutePath(Routes.REALNAME_STATUS,
                  arguments: {"canBack":canBack,'status':3});
            }else{
              logger("===上传失败===status : ${resp.data?.status}========");
              toast("上传失败");
            }

          }else{
            LoadingUtils.hideLoading();
            toast("上传失败");
          }

        }else{
          toast("未获取到腾讯云配置");
        }

      }else{
        toast("请先添加照片");
      }
    }catch(e){
      LoadingUtils.hideLoading();
      toast("上传失败");
    }

  }
}