import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/cos/real_name_cos.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/retrofit/datasource/real_name_datasource.dart';
import 'package:flutter_mixed/app/retrofit/entity/real_name/cos_token_req.dart';
import 'package:flutter_mixed/app/retrofit/entity/real_name/upload_pic_req.dart';
import 'package:flutter_mixed/app/utils/loading/loading_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../logger/logger.dart';
import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/cos/cosPanResp.dart';
import '../../../im/route/route_helper.dart';
import '../../../im/utils/image_util.dart';
import '../../../routes/app_pages.dart';
import 'package:image/image.dart' as img;

class UploadIdcardController extends GetxController{
  var keyPrefix = "";
  var idCardNum = "";
  RealNameCos? token;
  RxString forwardPicPath = "".obs;
  RxString backPicPath = "".obs;

  var idContainerHeight = 188.0;
  var idContainerWidth = 329.0;
  var canBack = false;
  @override
  void onInit() {
    super.onInit();
    idContainerWidth = ScreenUtil().screenWidth - 28 * 2;
    idContainerHeight = idContainerWidth / 1.618;

    keyPrefix = Get.arguments['keyPrefix']??"";
    idCardNum = Get.arguments['idCardNum'];
    canBack = Get.arguments["canBack"]??false;
  }

  void uploadIdcard(BuildContext context) async{
    try{
      if(forwardPicPath.value.isNotEmpty && backPicPath.value.isNotEmpty){
        if( keyPrefix.isNotEmpty){
          LoadingUtils.showLoading(context);
          var realNameDataSource = RealNameDataSource(retrofitDio,baseUrl:Host.HOST);
          //获取新token
          // var cosTokenReq = CosTokenRequest(idCardNum: idCardNum);
          // var resp = await realNameDataSource.getCosToken(cosTokenReq);
          // if(resp.success() && resp.data != null){
          //   token = resp.data!.token;
          //   token?.bucket = resp.data!.bucket;
          // }else{
          //   LoadingUtils.hideLoading();
          //   toast("上传失败");
          //   logger("===上传失败  没有token =");
          //   return;
          // }
          //
          // await CosManager().initPansWithToken(token!);
          //暂时先用网盘的
          await CosManager().initPans();

          String? bucket = await CosManager().bucket();
          var forwardResult = await CosUploadHelper.upload(
              forwardPicPath.value, fileId:keyPrefix+"-1", userBucket: bucket);
          var backResult = await CosUploadHelper.upload(
              backPicPath.value, fileId:keyPrefix+"-2", userBucket: bucket);

          if(forwardResult != null && backResult != null){
            logger("===上传成功====forward:${forwardResult}====back:${backResult}====");
            //上传结果发送后台

            var uploadPicReq = UploadPicReq(pic1: forwardResult,pic2: backResult,pic: "");
            var resp = await realNameDataSource.uploadPic(uploadPicReq);
            LoadingUtils.hideLoading();
            if(resp.success() && resp.data?.status == 2){
              Get.offNamedUntil(Routes.UPLOAD_PHOTO,
                  arguments: {"idCardNum":idCardNum,"keyPrefix": (resp.data?.keyPrefix??"")+(resp.data?.token?.fileId??""),"canBack": canBack},
                  (route){
                    return route.settings.name == Routes.HOME;
                  });

              // RouteHelper.route(Routes.UPLOAD_PHOTO,
              //     arguments: {"token":resp.data?.token,"keyPrefix": (resp.data?.keyPrefix??"")+(resp.data?.token?.fileId??""),"canBack": canBack});
            }else{
              logger("===上传失败===status : ${resp.data?.status}========");
              toast("上传失败");
            }

          }else{
            LoadingUtils.hideLoading();
            toast("上传失败");
          }


        }else{
          toast("未获取到腾讯云配置");
        }
       
      }else{
        toast("请先添加照片");
      }
    }catch(e){
      LoadingUtils.hideLoading();
      toast("上传失败");
    }

  }

  Future<String> changeImageRotate(BuildContext context,String path) async {
    if (path.isEmpty) {
      return path;
    }

    try {
      LoadingUtils.showLoading(context, loadingText: "正在处理图片...");

      // 使用 try-catch 包装文件操作，防止文件不存在或损坏导致崩溃
      final File imageFile = File(path);
      if (!await imageFile.exists()) {
        logger("图片文件不存在: $path");
        LoadingUtils.hideLoading();
        return path;
      }

      // 使用 compute 在后台线程解码图片，避免阻塞主线程
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final image = await compute(img.decodeImage, imageBytes);

      if (image != null && image.height > image.width) {
        // 图片需要旋转
        logger("图片需要旋转: 高度=${image.height}, 宽度=${image.width}");
        LoadingUtils.hideLoading();

        // 使用 await 确保旋转操作完成
        final String rotatedPath = await ImageUtil.rotateImage(path, rotate: 90);
        logger("图片旋转完成: $rotatedPath");
        return rotatedPath;
      } else {
        logger("图片无需旋转");
        LoadingUtils.hideLoading();
        return path;
      }
    } catch (e) {
      logger("处理图片时发生错误: $e");
      LoadingUtils.hideLoading();
      // 发生错误时返回原始路径，确保流程不中断
      return path;
    }

  }
}