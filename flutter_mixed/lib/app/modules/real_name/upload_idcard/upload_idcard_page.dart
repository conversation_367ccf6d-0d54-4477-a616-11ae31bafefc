import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/real_name/upload_idcard/upload_idcard_controller.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../logger/logger.dart';
import '../../../common/config/config.dart';
import '../../../common/widgets/no_double_click_fun.dart';
import '../../../im/route/route_helper.dart';
import '../../../im/ui/select_picture/common_select_manager.dart';
import '../../../routes/app_pages.dart';

class UploadIdcardPage extends GetView<UploadIdcardController>{
  @override
  Widget build(BuildContext context) {

    return ToolBar(
      title: '身份认证',
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              margin: const EdgeInsets.only(left: 28 , top: 30),
              child: const Text("请上传身份证照片",style: TextStyle(fontSize: 20,color: Color(0xff3d3d3d),fontWeight: FontWeight.w500),),
            ),

            50.gap,

            // 人像面
            Container(
              height: controller.idContainerHeight,
              width: controller.idContainerWidth,
              margin: EdgeInsets.symmetric(horizontal: 28),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    child: InkWell(onTap: () async {
                      SelectPicManager manager = SelectPicManager();
                      var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                      controller.forwardPicPath.value = await controller.changeImageRotate(context,r.paths.first);
                    }, child: Image.asset(AssetsRes.BG_IDCARD_FORWARD)),
                  ),
                  Container(
                    alignment: Alignment.center,
                    child: Obx(() => Visibility(
                        visible: controller.forwardPicPath.value.isNotEmpty,
                        child:Container(
                          height: controller.idContainerHeight-15,
                          width: controller.idContainerWidth-15*1.618,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image(fit: BoxFit.cover,
                                image: ExtendedFileImageProvider(File(controller.forwardPicPath.value))),
                          ),
                        )
                    ),
                  )),
                  Container(
                    width: 100,
                    alignment: Alignment.center,
                    child: Obx(() => Visibility(
                        visible: controller.forwardPicPath.value.isNotEmpty,
                        child: InkWell(
                          onTap: () async {
                            SelectPicManager manager = SelectPicManager();
                            var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                            logger("=======路径====${r.paths}");
                            String originPath = r.paths.first;
                            controller.forwardPicPath.value = await controller.changeImageRotate(context,originPath);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 30,
                            decoration: BoxDecoration(
                                color: Color(0xb2000000),
                                borderRadius: BorderRadius.circular(3)
                            ),
                            child: Text("重新上传",style: TextStyle(fontSize:14,color: Color(0xe5ffffff) ),),
                          ),
                        )
                    ),
                  )),
                ],
              ),
            ),

            Container(
              margin: EdgeInsets.only(top: 15),
              alignment: Alignment.center,
              child: const Text("上传身份证原件人像面",style: TextStyle(fontSize: 14,color: Color(0xe5222222)),),
            ),

            30.gap,
            // 国徽面
            Container(
              height: controller.idContainerHeight,
              width: controller.idContainerWidth,
              margin: EdgeInsets.symmetric(horizontal: 28),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    child: InkWell(onTap: () async {
                      SelectPicManager manager = SelectPicManager();
                      var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                      logger("=======路径====${r.paths}");
                      controller.backPicPath.value = await controller.changeImageRotate(context,r.paths.first);
                    }, child: Image.asset(AssetsRes.BG_IDCARD_BACK)),
                  ),

                  Container(
                    child: Obx(() => Visibility(
                        visible: controller.backPicPath.value.isNotEmpty,
                        child:Container(
                          height: controller.idContainerHeight-15,
                          width: controller.idContainerWidth-15*1.618,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image(fit: BoxFit.cover,
                                image: ExtendedFileImageProvider(File(controller.backPicPath.value))),
                          ),
                        )
                    ),
                  )),
                  Container(
                    width: 100,
                    child: Obx(() => Visibility(
                        visible: controller.backPicPath.value.isNotEmpty,
                        child: InkWell(
                          onTap: () async {
                            SelectPicManager manager = SelectPicManager();
                            var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                            logger("=======路径====${r.paths}");
                            controller.backPicPath.value = await controller.changeImageRotate(context,r.paths.first);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 30,
                            decoration: BoxDecoration(
                                color: Color(0xb2000000),
                                borderRadius: BorderRadius.circular(3)
                            ),
                            child: Text("重新上传",style: TextStyle(fontSize:14,color: Color(0xe5ffffff) ),),
                          ),
                        )
                    )),
                  )

                ],
              ),
            ),

            Container(
              margin: EdgeInsets.only(top: 15),
              alignment: Alignment.center,
              child: const Text("上传身份证原件国徽面",style: TextStyle(fontSize: 14,color: Color(0xe5222222)),),
            ),

            Expanded(child: Container()),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(AssetsRes.ICON_SAFE , width: 17,),
                2.gap,
                const Text("担当办公将对信息加密,实时保障你的信息安全",style: TextStyle(fontSize: 10,color: Color(0x4c222222)))
              ],
            ),

            8.gap,
            Container(
              margin: EdgeInsets.only(left: 16 ,right: 16 ,bottom:  DeviceUtils().bottom.value + 16,),
              child: Obx(()=>Container(
                width: double.infinity,
                padding: const EdgeInsets.only(left: 0, right: 0),
                height: 48,
                child: CupertinoButton(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    color: (controller.forwardPicPath.value.isNotEmpty
                        && controller.backPicPath.value.isNotEmpty )
                        ?ColorConfig.themeCorlor:ColorConfig.btnGrayColor,
                    borderRadius:
                    const BorderRadius.all(Radius.circular(8)),
                    pressedOpacity: 0.5,
                    child: const Text(
                      '提交',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                    onPressed:
                    NoDoubleClickFun(tapDuration: 2,onPressed: (){
                      controller.uploadIdcard(context);
                      } ).noDoublePressed
                    ),
              )),
            )

          ],
        ),
      ),
    );


    return ToolBar(
      title: "身份认证",
      body:Container(
        color: Colors.white,
        child:  Stack(
          children: [
            Positioned(
                left: 28,
                top: 36,
                child:Text("请上传身份证照片",style: TextStyle(fontSize: 20,color: Color(0xff3d3d3d),fontWeight: FontWeight.w500),)
            ),
            Positioned(
                left: 34,
                right: 34,
                top: 96,
                child:Container(
                  height: 198,
                  child: InkWell(onTap: () async {
                    SelectPicManager manager = SelectPicManager();
                    var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                    controller.forwardPicPath.value = r.paths.first;
                  }, child: Image.asset(AssetsRes.BG_IDCARD_FORWARD)
                  ),
                )
            ),
            Positioned(
                left: 45,
                right: 45,
                top: 107,
                child: Obx(() => Visibility(
                    visible: controller.forwardPicPath.value.isNotEmpty,
                    child:Container(
                      color: Colors.black,
                      height: 178,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image(fit: BoxFit.contain,
                            image: ExtendedFileImageProvider(File(controller.forwardPicPath.value))),
                      ),
                    )
                  )
                )
            ),
            Positioned(
                left: 147,
                right: 147,
                top: 181,
                child:Obx(() => Visibility(
                    visible: controller.forwardPicPath.value.isNotEmpty,
                    child: InkWell(
                      onTap: () async {
                        SelectPicManager manager = SelectPicManager();
                        var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                        logger("=======路径====${r.paths}");
                        controller.forwardPicPath.value = r.paths.first;
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 30,
                        decoration: BoxDecoration(
                            color: Color(0xb2000000),
                            borderRadius: BorderRadius.circular(3)
                        ),
                        child: Text("重新上传",style: TextStyle(fontSize:14,color: Color(0xe5ffffff) ),),
                      ),
                    )
                  )
                )
            ),
            Positioned(
                left: 100,
                right: 100,
                top: 302,
                child: Container(
                  alignment: Alignment.center,
                  child: Text("上传身份证原件人像面",style: TextStyle(fontSize: 14,color: Color(0xe5222222)),),
                )
            ),
            Positioned(
                left: 34,
                right: 34,
                top: 340,
                child:Container(
                  height: 198,
                  child: InkWell(onTap: () async {
                    SelectPicManager manager = SelectPicManager();
                    var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                    logger("=======路径====${r.paths}");
                    controller.backPicPath.value = r.paths.first;
                  }, child: Image.asset(AssetsRes.BG_IDCARD_BACK)
                  ),
                )
            ),

            Positioned(
                left: 45,
                right: 45,
                top: 351,
                child: Obx(() => Visibility(
                    visible: controller.backPicPath.value.isNotEmpty,
                    child:Container(
                      height: 178,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image(fit: BoxFit.cover,
                            image: ExtendedFileImageProvider(File(controller.backPicPath.value))),
                      ),
                    )
                )
                )
            ),
            Positioned(
                left: 147,
                right: 147,
                top: 435,
                child:Obx(() => Visibility(
                    visible: controller.backPicPath.value.isNotEmpty,
                    child: InkWell(
                      onTap: () async {
                        SelectPicManager manager = SelectPicManager();
                        var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.IMAGE_ONLY,maxGallerySize: 1);
                        logger("=======路径====${r.paths}");
                        controller.backPicPath.value = r.paths.first;
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 30,
                        decoration: BoxDecoration(
                            color: Color(0xb2000000),
                            borderRadius: BorderRadius.circular(3)
                        ),
                        child: Text("重新上传",style: TextStyle(fontSize:14,color: Color(0xe5ffffff) ),),
                      ),
                    )
                ))
            ),
            Positioned(
                left: 100,
                right: 100,
                top: 546,
                child: Container(
                  alignment: Alignment.center,
                  child: Text("上传身份证原件国徽面",style: TextStyle(fontSize: 14,color: Color(0xe5222222)),),
                )
            ),
            Positioned(
                left: 20,
                right: 20,
                bottom: 70,
                child: Container(
                  height: 16,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(AssetsRes.ICON_SAFE),
                      Text("担当办公将对信息加密,实时保障你的信息安全",style: TextStyle(fontSize: 10,color: Color(0x4c222222)))
                    ],

                  ),
                )

            ),
            Positioned(
                left: 16,
                right: 16,
                bottom: DeviceUtils().bottom.value + 16,
                child: Obx(()=>Container(
                  width: double.infinity,
                  padding: const EdgeInsets.only(left: 0, right: 0),
                  height: 48,
                  child: CupertinoButton(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                      color: (controller.forwardPicPath.value.isNotEmpty
                          && controller.backPicPath.value.isNotEmpty )
                          ?ColorConfig.themeCorlor:ColorConfig.btnGrayColor,
                      borderRadius:
                      const BorderRadius.all(Radius.circular(8)),
                      pressedOpacity: 0.5,
                      child: const Text(
                        '提交',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      onPressed: () {
                        controller.uploadIdcard(context);
                      }),
                ))
            )
          ],
        ),
      )
    );
  }
}