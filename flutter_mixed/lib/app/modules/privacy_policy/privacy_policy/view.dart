import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../common/config/app_config.dart';
import '../../../common/config/string_const.dart';
import '../../../common/widgets/widgets.dart';
import 'logic.dart';

class PrivacyPolicyPage extends GetView<PrivacyPolicyLogic>  {
  const PrivacyPolicyPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.whiteColor,
      body: Column(
        children: [
          44.gap,
          Container(
            alignment: Alignment.center,
            width: double.infinity,
            height: 44,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: const Text('隐私政策',style: TextStyle(fontSize: 16,color: ColorConfig.mainTextColor),),
          ),
          Expanded(child:controller.isFirst.value?Container():WebViewWidget(
           controller: controller.webViewController,

          )),
          10.gap,
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              20.gap,
              MaterialButton(
                elevation: 0,
                color: const Color(0xE0DBDBDB),
                padding: const EdgeInsets.only(top: 10, bottom: 10),
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(2)),
                ),
                onPressed: () {
                  toast('同意本条款才能使用$appName');
                  SystemNavigator.pop();
                },
                child: const Text(
                  '不同意',
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
              ),
              15.gap,
              Expanded(child: MaterialButton(
                elevation: 0,
                color: Colors.blue,
                padding: const EdgeInsets.only(top: 10, bottom: 10),
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(2)),
                ),
                onPressed: () {
                  appInit();
                  controller.navigatePage();
                },
                child: const Text(
                  '我已阅读并同意本条款',
                  style: TextStyle(fontSize: 14, color: Colors.white),
                ),
              )),
              20.gap
            ],
          ),
          DeviceUtils().bottom.value.gap
        ],
      ),
    ));
  }
}
