import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/net_util.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../common/api/Define.dart';
import '../../../common/api/LoginApi.dart';
import '../../../push/device_manage.dart';
import '../../../utils/storage.dart';
import '../../networkListen/controllers/network_listen_controller.dart';

class PrivacyPolicyLogic extends GetxController {
  late WebViewController webViewController;

  ///进度条
  var progress = 0.0;

  RxBool isFirst = false.obs;
  @override
  void onInit() {
    super.onInit();
    Get.put(NetworkListenController());
    if (Get.arguments != null) {
      if (Get.arguments['isFirst'] != null) {
        isFirst.value = Get.arguments['isFirst'];
      }
    }
    if (!isFirst.value) {
      createWebViewController();
    } else {

      if (NetUtil.netStr != 'NONE' &&
          NetUtil.netStr != 'Unknown') {

        createWebViewController();
      }
    }
  }

  createWebViewController() {

    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            // if (request.url.startsWith('https://www.youtube.com/')) {
            //   return NavigationDecision.prevent;
            // }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(
          Uri.parse(LoginApi.privacyUrl()));
    isFirst.value = false;
    isFirst.refresh();
  }

  bool _allowPermission(String model) {
    if(model == AndroidDeviceMode.OPPO.name || model == AndroidDeviceMode.VIVO.name ||
      model == AndroidDeviceMode.HUAWEI.name){
      return false;
    }
    return true;
  }

  void navigatePage() async {
    if(Platform.isAndroid){
      try{
        await Channel().invoke(requestAndroidLaunchPermission);

        var androidModel = await Channel().invoke(getAndroidDeviceMode);
        if(androidModel != null){
          if(_allowPermission(androidModel)){
            var r = await PermissionUtil.checkStoragePermission(Get.context!);
          }
        }
      }catch(e){}
    }else {
      var r = await PermissionUtil.checkStoragePermission(Get.context!);
    }

    await UserDefault.saveSettingConfig(Define.HAS_READ_PRIVACY_POLICY, '1');
    dynamic userInfo = await UserDefault.getData(Define.TOKENKEY);
    if (userInfo == null) {
      Get.offAndToNamed(Routes.LOGIN);
    } else {
      Get.offAndToNamed(Routes.HOME);
    }
  }
}


isToShowPrivacyPage() async {
  var isShowPrivacy = await UserDefault.getSettingConfig(Define.HAS_READ_PRIVACY_POLICY);
  if(isShowPrivacy == null || isShowPrivacy == ''){
    return true;
  }else {
    return false;
  }
}