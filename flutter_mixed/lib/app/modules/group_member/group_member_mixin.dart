

import 'dart:async';
import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/page_status.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/request/datasource/relation_datasource.dart';
import 'package:flutter_mixed/app/modules/group_member/group_member_entity.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/app/retrofit/entity/group/reset_group_manager.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class GroupMemberMixin extends GetxController with PageStatus {

  List<dynamic> charLetterList = [];

  List<GroupMemberUIBody> groupMemberList = [];

  List<GroupMemberUIBody> uiList = [];

  List<String> letters = ['A','B','C'];

  String groupId = '';

  List<GroupMemberUIBody> members = [];

  int memberCount = 0;

  String selfUid = "";

  bool isCompanyGroup = true;

  GroupMemberUIBody selfMember = GroupMemberUIBody();

  String groupName = '';

  String currentKey = '';

  FocusNode searchJobFocusNode = FocusNode();

  TextEditingController searchJobEditingController = TextEditingController();

  @override
  void onInit() async {
    super.onInit();
    selfUid = await UserHelper.getUid();
  }

  Future fetchGroupInfo() async {
    var workDatasource = WorkbenchDataSource(retrofitDio , baseUrl: Host.HOST);
    var resp = await workDatasource.getGroupInfo(groupId);

    if(resp.success()){
      if(resp.data.isEmpty) {
        loadEmpty();
        update();
        return;
      }
      groupName = resp.data[0].name ?? '';
      var users = resp.data[0].users ?? [];

      isCompanyGroup = resp.data[0].type == 1;

      users.removeWhere((element) => element == null);
      memberCount = users.length;
      members = users.map((e) => convert2GroupMember(e!)).toList();
      // members.forEach((e) {
      //   e.letter = _convertPinFirstLetter(e.name ?? '');
      // });

      getSelfPermission(members);

      groupMemberList.clear();

      // 全部音序
      List<Map<String,List<GroupMemberUIBody>>> letterList = _createLetterSerList();

      letterList[0]["⭐️"] = members.where((e) => !e.isNormalMember()).toList();

      letterList.forEach((l) {
         var tempL = <GroupMemberUIBody>[];
         members.forEach((m) {
           // if(m.isNormalMember()) {
           //   if(l.containsKey(m.letter)){
           //     tempL.add(m);
           //     l[m.letter ?? ''] = tempL;
           //   }
           // }

           if(l.containsKey(m.letter)){
             tempL.add(m);
             l[m.letter ?? ''] = tempL;
           }
         });
      });

      _removeNullMembers(letterList);

      charLetterList = letterList;

      letters.clear();

      letterList.forEach((map) {
        var key = map.keys.first;
        var value = map[key];
        var l = value as List<GroupMemberUIBody>;
        groupMemberList.add(GroupMemberUIBody()..letter = key);
        letters.add(key);
        groupMemberList.addAll(l);
      });
      logger('groupMemberList');
      uiList.clear();
      uiList.addAll(groupMemberList);

      logger(uiList);
      loadSuccess();
    }else {
      toast(resp.msg);
      loadError();
    }
    update();
  }

  removeMember(List<String> ids) async {
    var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
    var body = RemoveGroupMembersReq()
      ..groupId = groupId ?? ''
      ..userIds = ids;
    var resp = await workDatasource.removeGroupMembers(body);
    if(resp.success()){
      fetchGroupInfo();
    }else {
      toast(resp.msg);
    }
  }

  transferGroupOwner(GroupMemberUIBody model, {bool isQuit = false}) async {
    var workDatasource = WorkbenchDataSource(retrofitDio , baseUrl: Host.HOST);
    var body = TransformCreateReq(groupId, groupName, model.id ?? '');
    var resp = await workDatasource.transformCreate(body);
    if(resp.success()){
      toast('群主已转让');
      // 修改群主db
      var group = await DbHelper.queryGroupByGroupId(groupId);
      if(group != null){
        group.createUserId = model.id;
        DbHelper.insertGroup(group);
      }
      if(isQuit){
        // 退出群组
        await quitGroup();
        Get.until((route) => route.settings.name == Routes.HOME);
      }
    }else {
      toast(resp.msg);
    }
  }

  quitGroup() async {
    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var resp = await datasource.quitGroup(groupId);
    if(resp.success()){
      // 退群后数据操作: 更新session ； 关掉当前页面；  关掉群聊页面
      var session = await DbHelper.getSessionByOwnerId2SessionId(selfUid, groupId);
      if(session != null){
        session.sessionHidden = 1;
        await DbHelper.insertSession(session);
      }
    }else {
      toast(resp.msg);
    }
  }

  getSelfPermission(List<GroupMemberUIBody> members) {
    var selfs = members.where((element) => element.id == selfUid).toList();
    if(selfs.isEmpty) return;
    selfMember = selfs.first;
  }

  _createLetterSerList() {
    return BaseInfo.letters.map((e) {
      var map = HashMap<String,List<GroupMemberUIBody>>();
      map.putIfAbsent(e, () => []);
      return map;
    }).toList();
  }

  _removeNullMembers(List<Map<String,List<GroupMemberUIBody>>> letterList) {
    letterList.removeWhere((element) {
      var key = (element as Map).keys.first;
      if(element[key] is List){
        if( (element[key] as List).isEmpty ){
          return true;
        }
      }
      return false;
    });
  }

  @override
  void onClose() {
    super.onClose();
  }
}