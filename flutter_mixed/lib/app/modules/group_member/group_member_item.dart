import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_swipe_action_cell/core/cell.dart';

import '../../common/config/config.dart';
import 'group_member_entity.dart';


class GroupMemberItem extends StatelessWidget {

  final GroupMemberUIBody mGroupMember;
  final GroupMemberUIBody self;

  final bool isCompanyGroup;

  final ValueChanged<GroupMemberUIBody>? deleteMember;
  final ValueChanged<GroupMemberUIBody>? setManager;
  final ValueChanged<GroupMemberUIBody>? removeManager;
  final ValueChanged<GroupMemberUIBody>? transfer;

  const GroupMemberItem(this.mGroupMember , this.self , this.isCompanyGroup ,{super.key, required this.deleteMember, required this.setManager
    , required this.removeManager, required this.transfer});


  @override
  Widget build(BuildContext context) {
    if(isCompanyGroup) return _buildUserInfoItem();
    if(!self.beyondTargetPermission(mGroupMember)) return _buildUserInfoItem();
    if(self.id == mGroupMember.id) return _buildUserInfoItem();
    if(self.isNormalMember()) return _buildUserInfoItem();
    if(self.isGroupOwner() && self.id == mGroupMember.id) return _buildUserInfoItem();

    return SwipeActionCell(key: ValueKey(mGroupMember.id),
        backgroundColor: Colors.white,
        trailingActions: [
          if(self.isGroupOwner())...[

            SwipeAction(
              style: const TextStyle(fontSize: 14, color: Colors.white),
              onTap: (handler) async{
                handler(false);
                deleteMember?.call(mGroupMember);
              },
              color: const Color(0xFFFE4A49),
              // icon: Icons.delete,
              title: '移除',
            ),
            // _buildDivider(),

            SwipeAction(
              style: const TextStyle(fontSize: 14, color: Colors.white),
              onTap: (handler) async {
                handler(false);
                mGroupMember.isManager() ? removeManager?.call(mGroupMember) : setManager?.call(mGroupMember);
              },
              color: const Color(0xFFFF9A3C),
              title: mGroupMember.isManager() ? '移除管理员': '设为管理员',
            ),

            SwipeAction(
              style: const TextStyle(fontSize: 14, color: Colors.black),
              onTap: (handler) async {
                handler(false);
                transfer?.call(mGroupMember);
              },
              color: const Color(0xFFDEDEDE),
              title: '转让群主',
            ),

            // _buildDivider(),
          ],

          if(self.isManager())...[
            SwipeAction(
              style: const TextStyle(fontSize: 14, color: Colors.white),
              onTap: (handler) async {
                handler(false);
                deleteMember?.call(mGroupMember);
              },
              color: const Color(0xFFFE4A49),
              // icon: Icons.delete,
              title: '移除',
            )
          ],
        ],
        child: _buildUserInfoItem());
  }

  // 成员信息
  _buildUserInfoItem() {
    return Container(
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.only(left: 16 ,top: 8,bottom: 8),
            padding: const EdgeInsets.all(1),
            width: 40,
            height: 40,
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: SettingWidget.backImageProvider(mGroupMember.headimg,cache: true), fit: BoxFit.contain),
                borderRadius:
                BorderRadius.circular(8),
                border: Border.all(
                    color: ColorConfig.lineColor, width: 0.5)),
          ),
          10.gap,
          Text(mGroupMember.name ?? '' , style: TextStyle(fontSize: 14 ,color: Color(0xff1D2129)),),
          if(mGroupMember.isGroupOwner())...[ _buildGroupOwnerItem() ],
          if(mGroupMember.isManager())...[ _buildManagerItem() ]
        ],
      ),
    );
  }

  _buildGroupOwnerItem() {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      padding: const EdgeInsets.only(left: 6 ,right: 6 ,top: 1 ,bottom: 2),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(38),
          border: Border.all(
              width: 0.5, color: const Color(0xff2D6DFF))),
      child: const Text('群主' , style: TextStyle(fontSize: 10 ,color: Color(0xff2D6DFF)),),
    );
  }

  _buildManagerItem() {
    return Container(
      alignment: Alignment.center,
      margin: const EdgeInsets.only(left: 10),
      padding: const EdgeInsets.only(left: 6 ,right: 6 ,top: 1 ,bottom: 2),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(38),
          border: Border.all(
              width: 0.5, color: const Color(0xffFFB92D))),
      child: const Text('管理员' , style: TextStyle(fontSize: 10 ,color: Color(0xffFFB92D))),
    );
  }

}