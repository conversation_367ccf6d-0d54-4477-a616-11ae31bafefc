import 'dart:async';

import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/modules/group_member/group_member_entity.dart';
import 'package:flutter_mixed/app/modules/group_member/group_member_mixin.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import 'package:get/get.dart' as group_get;

class GroupMemberOperateCtl extends GroupMemberMixin {
  String pageTitle = '';
  EnumGroupMemberOperateType pageType = EnumGroupMemberOperateType.removeMember;

  StreamSubscription? _groupStreamSubscription;

  List<GroupMemberUIBody> cacheMemberList = [];
  List<GroupMemberUIBody> uiMemberList = [];

  String loadPageTitle() {
    switch (pageType) {
      case EnumGroupMemberOperateType.removeMember:
        return '移除成员';
      case EnumGroupMemberOperateType.transferAndQuit:
      case EnumGroupMemberOperateType.transferLeader:
        return '转让群组创建者';
    }
  }

  List<GroupMemberUIBody> get selectedMembers => uiMemberList.where((item) => item.selected as bool).toList();  

  @override
  void onInit() async {
    super.onInit();

    group_get.Get.getOrEventParams(Routes.GROUP_MANAGE_OPERATE, arg: (arg){
      groupId = arg['groupId'];
      pageType = arg['pageType'];
      fetchGroupInfo();
      _listenerGroupDb();
    });
  }

  Future _listenerGroupDb() async {
    _groupStreamSubscription =
        DbHelper.listenGroupList(groupId).listen((group) async {
          await fetchGroupInfo();
    });
  }

  handleItemClick(GroupMemberUIBody user, bool selected) {
    user.selected = selected;
    update();
  }

  GroupMemberUIBody convert2GroupCheckedMember(GroupMemberUIBody user) {
    return GroupMemberUIBody()
      ..id = user.id
      ..name = user.name
      ..headimg = user.headimg
      ..selected = false
    ;
  }

  @override
  Future fetchGroupInfo() async {
    await super.fetchGroupInfo();
    cacheMemberList = members.where((item) => item.id != selfUid).map((e) => convert2GroupCheckedMember(e)).toList();
    uiMemberList.clear();
    uiMemberList.addAll(cacheMemberList);
  }

  // 搜索成员
  searchMembers(String key) {
    currentKey = key;
    showMembers();
  }

  showMembers() {
    if(StringUtil.isEmpty(currentKey)){
      searchJobEditingController.clear();
      searchJobFocusNode.unfocus();
      uiMemberList.clear();
      uiMemberList.addAll(cacheMemberList);
    }else {
      var dulist = cacheMemberList.map((e) => e.id).toSet();
      var copyList = cacheMemberList.map((e) => e).toList();
      copyList.retainWhere((element) => dulist.remove(element.id));
      var result = copyList.where((element) => (element.name?? '').contains(currentKey)).toList();
      uiMemberList.clear();
      uiMemberList.addAll(result);
    }
    update();
  }

  clearInput() {
    currentKey = '';
    searchJobEditingController.clear();
    showMembers();
  }

  handleRemoveMember() {
    List<String> ids = uiMemberList.where((item) => item.selected as bool).map((item) => item.id as String).toList();
    removeMember(ids);
  }

  @override
  void onClose() {
    super.onClose();
    _groupStreamSubscription?.cancel();
  }
}

enum EnumGroupMemberOperateType {
  removeMember,
  transferLeader,
  transferAndQuit
}