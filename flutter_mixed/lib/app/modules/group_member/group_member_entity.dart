

import '../../retrofit/entity/group/group_info.dart';

class GroupMemberUIBody {
  String? id;
  String? headimg;
  String? version;
  String? name;
  String? letter;

  bool? isBottom = false;

  bool? selected = false;

  int value = 0; // 1 群主，  2 ,管理员， 0 普通成员

  bool canTransform() {
    return true;
  }

  bool canDelMember() {
    return value == 1 || value == 2;
  }

  bool canSetManager() {
    return true;
  }

  bool isNormalMember() => value != 1 && value !=2;

  bool isGroupOwner() => value == 1;

  bool isManager() => value == 2;

  @override
  String toString() {
    return "{$id , $headimg , $name ,$letter, $selected}";
  }
}

GroupMemberUIBody convert2GroupMember(User user) {
  return GroupMemberUIBody()..id = user.id
    ..name = user.name
    ..headimg = user.headimg
    ..value = user.identity ?? 0
    ..letter = user.initial ?? '#'
  ;
}

extension GroupMemberExt on GroupMemberUIBody {

  bool beyondTargetPermission(GroupMemberUIBody target) {
    if(value == target.value) return false;
    if(isGroupOwner() && !target.isGroupOwner()) return true;
    if(isManager() && target.isGroupOwner()) return false;
    if(isManager() && target.isManager()) return false;
    if(isManager() && target.isNormalMember()) return true;
    if(isNormalMember() && !target.isNormalMember()) return false;
    return false;
  }

}