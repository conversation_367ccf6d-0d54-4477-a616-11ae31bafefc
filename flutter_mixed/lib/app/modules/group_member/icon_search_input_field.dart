import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

import '../../../res/assets_res.dart';
import '../../common/widgets/theme.dart';

/// 搜索框
class SearchInputField extends StatelessWidget {
  final String? hintText;
  final IconData? icon;
  final ValueChanged<String>? onChanged;

  final TextInputType? textInputType;

  final FocusNode focusNode;

  final TextEditingController? textFieldContainer;

  final Function? clearInput;

  final VoidCallback? onFieldSubmitted;

  final bool? isNeedTopMargin;

  const SearchInputField({
    Key? key,
    this.hintText,
    this.icon,
    this.onChanged,
    this.textInputType,
    required this.focusNode,
    this.clearInput,
    this.textFieldContainer,
    this.onFieldSubmitted,
    this.isNeedTopMargin = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InputTextFieldContainer(
        focusNode: focusNode,
        child: Stack(
          children: [
            // 可隐藏的icon
            Container(
              width: double.infinity,
              alignment: Alignment.centerLeft,
              child: _isHiddenIconAndHintText(textFieldContainer?.text.isNotEmpty == true),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(child: TextField(
                  onSubmitted: (String value){

                  },
                  autofocus: false,
                  style: const TextStyle(fontSize: 15, color: majorTextColor),
                  onChanged: onChanged,
                  keyboardType: textInputType,
                  controller: textFieldContainer,
                  focusNode: focusNode,
                  decoration: InputDecoration(
                        contentPadding: const EdgeInsets.only(
                                        top: 0, bottom: 0),
                          border: const OutlineInputBorder(
                                        borderSide: BorderSide.none),
             
                                    hintStyle: const TextStyle(
                                      color: ColorConfig.desTextColor,
                                      fontSize: 15,
                               )),
                     )

                )
              ],
            ),
          ],
        ));
  }

  _isHiddenIconAndHintText(bool hasFocus) {
    if(!hasFocus) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset('assets/images/3.0x/org_search.png', width: 17,),
          Container(
            padding: EdgeInsets.only(left: 10),
            child: Text(hintText ?? '' , style: TextStyle(fontSize: 15, color: hintTextColor),),
          )
        ],
      );
    }
    return Container();
  }

  _showDelIcon() {
    if (textFieldContainer == null)
      return Container(
        width: 1,
      );
    return textFieldContainer!.text.trim().isEmpty
        ? Container(
            width: 1,
          )
        : GestureDetector(
            onTap: () {
              if (clearInput != null) {
                // textFieldContainer?.clear();
                focusNode.unfocus();
                clearInput!();
              }
            },
            child: Container(
              child: const Icon(Icons.cancel , size: 17,),
            ),
          );
  }
}


class InputTextFieldContainer extends StatelessWidget {
  final Widget? child;

  final FocusNode? focusNode;

  final double height;
  InputTextFieldContainer({
    Key? key,
    this.child,
    this.focusNode,
    this.height=42,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      height: height,
      padding: const EdgeInsets.only(left: 14, right: 6 ),
      decoration: BoxDecoration(
        color: const Color(0xffFAFAFA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(width: 0, color: _borderColor()),
      ),
      child: child,
    );
  }

  _borderColor() {
    if(focusNode == null) return const Color(0xFFECECEC);;
    if(focusNode!.hasFocus) return Color(0xff29A0F2);
    return const Color(0xFFECECEC);
  }
}