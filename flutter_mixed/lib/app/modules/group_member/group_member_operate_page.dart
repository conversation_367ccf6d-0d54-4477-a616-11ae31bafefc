import 'package:extended_image/extended_image.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';

import 'package:flutter_mixed/app/common/widgets/page_status.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/group_member/group_member_entity.dart';
import 'package:flutter_mixed/app/modules/group_member/group_member_operate_ctl.dart';
import 'package:flutter_mixed/app/permission/permission_collection_dialog.dart';
import 'package:get/get.dart';
import 'icon_search_input_field.dart';

class GroupManageOperateInfo extends StatelessWidget with PageLoadWidget {
  const GroupManageOperateInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GroupMemberOperateCtl>(builder: (controller) {
      bool isEmpty = controller.selectedMembers.isEmpty;
      return ToolBar(
        title: controller.loadPageTitle(),
        actions: controller.pageType == EnumGroupMemberOperateType.removeMember ? [
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Theme(
                data: ThemeData(
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                ), 
                child: InkWell(
                  onTap: () {
                    if(isEmpty) {
                      toast("请选择要移除的成员");
                      return;
                    }
                    showSimpleDialog(
                      context,
                      title: "移除成员",
                      subTitle: "确定要将选中的用户移除群组吗？",
                      confirmCallBack: ()=> controller.handleRemoveMember()
                    );
                  },
                  child: Text('完成', style: 
                    TextStyle(
                      color: isEmpty ? ColorConfig.subTitleTextColor : ColorConfig.themeCorlor, 
                      fontSize: 15,
                      fontWeight: FontWeight.w500
                    )
                  ),
                )
              ),
            ),
          )
        ] : [],
        body: loadPage(
          controller.loadStatus,
          Column(
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: ColorConfig.whiteColor,
                ),
                padding: const EdgeInsets.only(bottom: 8),
                child:  _buildSearchBar(context, controller),
              ),
              Expanded(child: ListView.builder(
                itemCount: controller.uiMemberList.length,
                itemBuilder: (context, index) {
                  var userModel = controller.uiMemberList[index];
                  return InkWell(
                    onTap: () {
                      if (controller.pageType == EnumGroupMemberOperateType.removeMember) {
                        final bool selected = userModel.selected ?? false;
                        controller.handleItemClick(userModel, !selected);
                      } else if (controller.pageType == EnumGroupMemberOperateType.transferLeader) {
                        Navigator.of(context).pop();
                        showSimpleDialog(
                          context,
                          title: "转让群主", subTitle: "确认将群主转让给该成员吗",
                          confirmCallBack: () => controller.transferGroupOwner(userModel)
                        );
                      } else if (controller.pageType == EnumGroupMemberOperateType.transferAndQuit) {
                        Navigator.of(context).pop();
                        showSimpleDialog(
                          context,
                          title: "转让群主", subTitle: "确认将群主转让给该成员并退出群组吗",
                          confirmCallBack: () => controller.transferGroupOwner(userModel, isQuit: true)
                        );
                      }
                    },
                    child: _buildItem(context, controller, userModel, index),
                  );
                }
              )),
            ],
          ), 
          (){
            controller.fetchGroupInfo();
          }
        ),
      );
    });
  }

  _buildSearchBar(BuildContext context, GroupMemberOperateCtl controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SearchInputField(
        hintText: '搜索成员',
        focusNode: controller.searchJobFocusNode,
        onChanged: (e) {
          controller.searchMembers(e);

        },
        textFieldContainer: controller.searchJobEditingController,
        clearInput: () {
          controller.clearInput();
        },
      ),
    );
  }

  _buildItem(BuildContext context, GroupMemberOperateCtl controller, GroupMemberUIBody model, int idx) {
    return Container(
      decoration: const BoxDecoration(color: ColorConfig.whiteColor),
      height: 64,
      child: Container(
        padding: EdgeInsets.only(left: controller.pageType == EnumGroupMemberOperateType.removeMember? 0 : 16),
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(color: idx != 0 ? const Color(0xffE5E6EB) : Colors.transparent, width: idx != 0 ? 0.5 : 0)
          ),
        ),
        child: Row(
          children: [
            controller.pageType == EnumGroupMemberOperateType.removeMember ?
            Checkbox(
              value: model.selected,
              shape: const CircleBorder(),
              onChanged: (value) => {
                controller.handleItemClick(model, value ?? false)
              },
              activeColor: ColorConfig.themeCorlor,
            ) : 
            Container(),
            Container(
              margin: const EdgeInsets.only(top: 8,bottom: 8, right: 16),
              padding: const EdgeInsets.all(1),
              width: 40,
              height: 40,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: SettingWidget.backImageProvider(model.headimg,cache: true), fit: BoxFit.contain
                ),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorConfig.lineColor, width: 0.5)),
            ),
            Text(model.name ?? '' , style: const TextStyle(fontSize: 14 ,color: Color(0xff1D2129))),
          ],
        ),
      )
    );
  }
}