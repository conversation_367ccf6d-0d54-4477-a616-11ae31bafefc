

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/page_status.dart';
import 'package:flutter_mixed/app/permission/permission_collection_dialog.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

import '../../common/config/config.dart';
import '../../common/widgets/widgets.dart';
import '../../im/route/route_helper.dart';
import '../contact/aboutOrg/chooseAllOrgMembers/views/contact_indexs.dart';
import 'group_member_entity.dart';
import 'group_member_item.dart';
import 'group_members_controller.dart';
import 'icon_search_input_field.dart';

/// 群成员
class GroupMembersPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => GroupMemberState();
}

class GroupMemberState extends State<GroupMembersPage> with PageLoadWidget {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GroupMemberController>(builder: (controller) {
      return Scaffold(
        backgroundColor: ColorConfig.whiteColor,
        appBar: TitleBar().backAppbar(
            context,
            '群成员',
            false,
            [], onPressed: (){
          Get.back();}
        ),
        body: loadPage(controller.loadStatus
            , Stack(
              children: [
                Column(
                  children: [
                    _buildSearchBar(context , controller),
                    Expanded(child: ListView.builder(
                        controller: controller.scrollController,
                        itemCount: controller.uiList.length,
                        itemBuilder: (context, index) {
                          String letter = '';
                          GroupMemberUIBody memberModel = controller.uiList[index];
                          letter = memberModel.letter ?? '';

                          return StringUtil.isEmpty(memberModel.id)
                              ? Container(
                            alignment: Alignment.centerLeft,
                            padding: const EdgeInsets.only(top: 10,left: 15),
                            height: 30,
                            child: Text(
                              letter,
                              style: const TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.themeCorlor),
                            ),
                          )
                              : InkWell(
                            onTap: () {
                              RouteHelper.routePath(Routes.USER_INFO , arguments: {'userId': memberModel.id, 'type': 2});//todo...type缺少企业群组的判断 
                            },
                            child: _buildItem(context ,memberModel , controller),
                          );
                        })),
                  ],
                ),


                ContactIndexs(
                  onUpdateCallback: (String letter, int index) {
                    controller.indexViewOnTap(index);
                  },
                  letterList: controller.letters,
                ),

              ],
            )
            , (){
              controller.fetchGroupInfo();
        }),
      );

    });
  }

  _buildSearchBar(BuildContext context, GroupMemberController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SearchInputField(
        hintText: '搜索成员',
        focusNode: controller.searchJobFocusNode,
        onChanged: (e) {
          controller.searchMembers(e);

        },
        textFieldContainer: controller.searchJobEditingController,
        clearInput: () {
          controller.clearInput();
        },
      ),
    );
  }

  _buildItem(BuildContext context ,GroupMemberUIBody model , GroupMemberController controller) {
    var self = controller.selfMember;
    return GroupMemberItem(model , self , controller.isCompanyGroup , deleteMember: (model){
      showSimpleDialog(context , title: "移除成员", subTitle: "确认移除该成员吗"
          , confirmCallBack: ()=> controller.handleRemoveMember(model));
    } , setManager: (model){
      showSimpleDialog(context , title: "设置管理员", subTitle: "确认将该成员设置为管理员吗"
          , confirmCallBack: () => controller.appointManager(model));
    } ,transfer: (model){
      showSimpleDialog(context , title: "转让群主", subTitle: "确认将群主转让给该成员吗"
          , confirmCallBack: () => controller.transferGroupOwner(model));
    }, removeManager: (model) {
      showSimpleDialog(context , title: "移除管理员", subTitle: "确认移除该管理员吗"
          , confirmCallBack: () => controller.revocationManager(model));
    },);
  }


}