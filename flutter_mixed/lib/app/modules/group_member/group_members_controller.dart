import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/page_status.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import 'package:flutter_mixed/app/modules/group_member/group_member_mixin.dart';
import '../../../main.dart';
import '../../common/widgets/widgets.dart';
import '../../retrofit/datasource/workbench_datasource.dart';
import '../../retrofit/entity/group/reset_group_manager.dart';
import '../../routes/app_pages.dart';
import 'package:get/get.dart' as GroupGet;

import 'group_member_entity.dart';

class GroupMemberController extends GroupMemberMixin with PageStatus{
  ScrollController scrollController = ScrollController();

  StreamSubscription? _groupStreamSubscription;


  @override
  void onInit() async {
    super.onInit();
    selfUid = await UserHelper.getUid();

    GroupGet.Get.getOrEventParams(Routes.GROUP_MEMBER_PAGE, arg: (arg){
      groupId = arg['groupId'];
      fetchGroupInfo();
      _listenerGroupDb();
    });
  }

  Future _listenerGroupDb() async {
    _groupStreamSubscription =
        DbHelper.listenGroupList(groupId).listen((group) async {
          await fetchGroupInfo();
    });
  }

  // 搜索成员
  searchMembers(String key) {
    currentKey = key;
    showMembers();
  }

  showMembers() {
    if(StringUtil.isEmpty(currentKey)){
      searchJobEditingController.clear();
      searchJobFocusNode.unfocus();
      uiList.clear();
      uiList.addAll(groupMemberList);
    }else {
      var dulist = groupMemberList.map((e) => e.id).toSet();
      var copyList = groupMemberList.map((e) => e).toList();
      copyList.retainWhere((element) => dulist.remove(element.id));
      var result = copyList.where((element) => (element.name?? '').contains(currentKey)).toList();
      uiList.clear();
      uiList.addAll(result);
    }
    update();
  }

  clearInput() {
    currentKey = '';
    searchJobEditingController.clear();
    showMembers();
  }

  indexViewOnTap(index) {
    double offset = 50.0;
    for (var i = 0; i < index; i++) {
      Map userMap = charLetterList[i];
      String letter = userMap.keys.first;
      List memList = userMap[letter];
      offset += memList.length * 53 + 30;
    }
    scrollController.jumpTo(offset);
  }

  handleRemoveMember(GroupMemberUIBody model) {
    removeMember([model.id ?? ""]);
  }

  // 设置管理员
  appointManager(GroupMemberUIBody model) async {
    var workDatasource = WorkbenchDataSource(retrofitDio , baseUrl: Host.HOST);
    var body = ResetGroupManagerReq()
      ..groupId = groupId ?? ''
      ..operation = 2
      ..targetUserId = model.id ?? '';
    var resp = await workDatasource.resetManager(body);
    if(resp.success()){
      fetchGroupInfo();
    }else {
      toast(resp.msg);
    }
  }

  // 移除管理员
  revocationManager(GroupMemberUIBody model) async {
    var workDatasource = WorkbenchDataSource(retrofitDio , baseUrl: Host.HOST);
    var body = ResetGroupManagerReq()
      ..groupId = groupId ?? ''
      ..operation = 0
      ..targetUserId = model.id ?? '';
    var resp = await workDatasource.resetManager(body);
    if(resp.success()){
      fetchGroupInfo();
    }else {
      toast(resp.msg);
    }
  }

  @override
  void onClose() {
    super.onClose();
    _groupStreamSubscription?.cancel();
  }
}

