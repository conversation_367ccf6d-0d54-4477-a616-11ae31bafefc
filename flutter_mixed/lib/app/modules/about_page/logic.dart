import 'dart:io';

import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../logger/logger_helper.dart';
import '../../common/api/Define.dart';
import '../../utils/storage.dart';

class AboutPageLogic extends GetxController {

  var versionName = '';

  @override
  void onReady() async {
    super.onReady();
    var v = '';
    if(Platform.isIOS){
      var header = await UserDefault.getData(Define.HTTPHEADER);
      if(header != null && header is Map){
        v = header['appVersion'];
      }
    }else{
      v = await Channel().invoke(Channel_version_name);
    }
    if(v !=null){
      versionName = v;
      update();
    }
  }

  void checkUpgrade() async{
    Channel().invoke(about_check_update);
  }

  var _loggerAction = 0;
  void switchLogger() {
    if(EnvConfig.mEnv == Env.Product) return;
     if(_loggerAction == 4){
       enableLog = true;
       toast('已开启logger');
       _loggerAction = 0;
     }else{
       enableLog = false;
       // toast('已关闭logger');
       print('logger已关闭');
       _loggerAction ++;
     }
  }

}
