import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

import '../../common/api/LoginApi.dart';
import '../../common/config/config.dart';
import '../../common/config/string_const.dart';
import '../../common/env/env_convert_component.dart';
import '../../common/widgets/widgets.dart';
import 'logic.dart';

/// 关于页面
class AboutPage extends StatelessWidget {

  final logic = Get.find<AboutPageLogic>();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AboutPageLogic>(
        builder: (_){
      return Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: false,
          appBar: TitleBar().backAppbar(context, '关于担当', false, [], onPressed: () {
            Get.back();
          }),
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              24.gap,
              GestureDetector(
                onTap: ()=> logic.switchLogger(),
                child: Image.asset(
                  'assets/images/3.0x/about_icon_clear.png',
                  width: 72,
                  height: 72,
                ),
              ),

              Text('$appName v${logic.versionName}', style: const TextStyle(fontSize: 14 ,color: ColorConfig.mainTextColor),),
              20.gap,

              Container(
                color: Colors.white,
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 19,horizontal: 30),
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.topLeft,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('v${logic.versionName}版本更新内容：',style: const TextStyle(fontSize: 16,color: ColorConfig.mainTextColor),),
                       5.gap,
                       const Text(Host.APPUPDATECONTENT, style: TextStyle(fontSize: 14, color:ColorConfig.desTextColor)),
                    ],
                  )
                ),),

              if(Platform.isAndroid)...[
                15.gap,
                MaterialButton(
                    padding: const EdgeInsets.only(top: 10 , bottom: 10 , left: 28 , right: 28),
                    color: const Color(0xff29A0F2),
                    shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8))
                    ),
                    child: const Text('检查更新',
                      style: TextStyle(fontSize: 13, color: Colors.white),
                    ),
                    onPressed: () {
                      logic.checkUpgrade();
                    }),
                10.gap
              ],

              _buildEnv(context),

              const Spacer(),

              InkWell(
                child: const Text('《隐私政策》', style: TextStyle(fontSize: 13 ,color: Color(0xff2479ed)),),
                onTap: (){
                  Get.toNamed(Routes.WEB_DETAIL,
                      arguments: {
                        'url':
                        LoginApi.privacyUrl(),
                        'title': '隐私政策'
                      });
                },
              ),

              10.gap,
              const Text('担当由加优科技有限公司提供技术服务' , style: TextStyle(fontSize: 13 ,color: Color(0xff999999)),),
              5.gap,
              const Text('联系我们：<EMAIL>', style: TextStyle(fontSize: 13 ,color: Color(0xff999999)),),
              30.gap,
            ],
          ));
    });
  }

  _buildEnv(BuildContext context) {
     if(Platform.isAndroid){
       return ConvertEnvComponent(updateParent: (){
       },);
     }
     return Container();
  }
}
