import 'dart:convert';
import 'dart:async';
import 'package:webview_flutter/webview_flutter.dart';

class JSBridge {
  final WebViewController controller;
  final _pending = <String, Completer<dynamic>>{};
  final _handlers = <String, FutureOr<dynamic> Function(Map<String, dynamic>)>{};

  String jsObj = 'DDBESOFFICE';

  JSBridge(this.controller) {
    controller.addJavaScriptChannel(
      'DartBridge',
      onMessageReceived: _handleMessage,
    );
  }

  // Dart 调用 JS 方法并等待返回值
  Future<dynamic> call(String method, [Map<String, dynamic>? params]) async {
    final id = DateTime.now().microsecondsSinceEpoch.toString();
    final completer = Completer<dynamic>();
    _pending[id] = completer;

    final jsCode = '''
      if (window.DDBESOFFICE && window.DDBESOFFICE._handleCall) {
        window.DDBESOFFICE._handleCall({
          id: "$id",
          method: "$method",
          params: ${jsonEncode(params ?? {})}
        });
      }
    ''';

    await controller.runJavaScript(jsCode);
    return completer.future;
  }

  // Dart 注册给 JS 调用的方法
  void registerHandler(String method, FutureOr<dynamic> Function(Map<String, dynamic>) handler) {
    _handlers[method] = handler;
  }

  void _handleMessage(JavaScriptMessage message) async {
    final data = jsonDecode(message.message);
    final id = data['id'];
    final method = data['method'];
    final params = data['params'];
    final result = data['result'];

    // 来自 Dart 自己 call() 的响应
    if (result != null && _pending.containsKey(id)) {
      _pending.remove(id)?.complete(result);
      return;
    }

    // 来自 JS 主动调用 Dart 方法
    if (_handlers.containsKey(method)) {
      final res = await _handlers[method]!(params ?? {});
      final returnJS = '''
        if (window.DDBESOFFICE && window.DDBESOFFICE._receiveResult) {
          window.DDBESOFFICE._receiveResult("$id", ${jsonEncode(res)});
        }
      ''';
      await controller.runJavaScript(returnJS);
    }
  }
}
