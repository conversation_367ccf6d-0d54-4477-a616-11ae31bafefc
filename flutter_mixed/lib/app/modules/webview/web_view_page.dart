import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/modules/webview/native_webview.dart';
import 'package:flutter_mixed/app/modules/webview/web_view_ctl.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// 关于js 交互 ： https://doc.joinu.ltd/web/#/628652708/206443374
class WebViewPage extends StatelessWidget {

  WebViewPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    WebViewCtl webController = Get.find();
    return GetBuilder<WebViewCtl>(
        init: webController,
        global: false,
        builder: (controller){
      return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: ColorConfig.backgroundColor,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [


            if(webController.webArgs != null)...[
              Expanded(child: NativeWebView(creationParams: webController.webArgs))
            ]

            // Expanded(child: WebViewWidget(
            //     controller: controller.webViewController!)),


          ],
        ),
      );
    });
  }
}
