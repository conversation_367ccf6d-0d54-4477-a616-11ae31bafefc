import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:flutter/foundation.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../../common/event/event.dart';
import 'js_body.dart';
import 'js_native_enum.dart';


class WebViewCtl extends GetxController {

  WebViewController? webViewController ;
  String url = '';
  var webProgress = 0;
  String barTitle = '';
  bool showToolbar = true;

  final mainWindow = 'DDBESOFFICE';
  String objName = 'ddbes_web';

  var currentWindowObj = '';

  var webArgs = null;

  StreamSubscription? eventTianditu;

  ///  {
  //       'url': url,
  //       'title': title,
  //       'isWebNavigation': isWebNavigation,
  //       'orgId': companyId
  //     }

  @override
  void onInit() {
    super.onInit();
    eventTianditu = eventBus.on<EventTianditu>().listen((result){
      // 接收天地图参数  todo
      var map =  result.param;
      if(map == null) return;
      Get.back(result: map);
    });

    currentWindowObj = mainWindow;

    var arg = Get.arguments;
    logger('arg = ${arg}');

    if(arg == null) {
      Get.back();
      return;
    }
    // arg
    webArgs = arg;
    webArgs['hybridcompositionEnabled'] = true;
    update();
  }


  registerJsBind() async {
    webViewController?.runJavaScript('''
       window.${currentWindowObj} = window.${currentWindowObj} || {};
       window.${currentWindowObj}.${JsCallNative.client_isAlready.name} = function(param) {
          return window.${currentWindowObj}.callHandler('${JsCallNative.client_isAlready.name}', param);
        };
    ''');

    webViewController?.runJavaScript('''
       window.${currentWindowObj} = window.${currentWindowObj} || {};
       window.${currentWindowObj}.${JsCallNative.client_isAlready.name} = function(param) {
          return window.${currentWindowObj}.callHandler('${JsCallNative.client_isAlready.name}', param);
        };
    ''');
  }

  // 给js 注册 native的本地方法，供调用
  registerJavaScript() async {
    logger('registerJavaScript....');

    webViewController?.runJavaScript('''
      // window.$currentWindowObj = window.$currentWindowObj || {};
      window.$currentWindowObj.${JsCallNative.client_isAlready.name} = function(param) {
        // 通过标准通道传递参数
        return ${_clientIsAlreadyResult('')}
        // clientisalreadychannel.postMessage(param);
      };
    ''');

    var r = await webViewController?.runJavaScriptReturningResult('window.${currentWindowObj} = {${JsCallNative.client_isAlready.name}: function(param) ');
    logger('r = $r');
  }

  // js 调用 client_isAlready 后，app端
  _clientIsAlreadyResult(String clientAlreadJson) {
    logger('调用了 _clientIsAlreadyResult $clientAlreadJson');
    if(StringUtil.isEmpty(clientAlreadJson)) return;
    var clientAlready = ClientAlreadyEntity.fromJson(json.decode(clientAlreadJson));

    if(clientAlready.useTitleBar == true){
       _updateToolbar(clientAlready.title);
       showToolbar = true;
    }else {
      showToolbar = false;
    }

    // 更新 objName
    if(!StringUtil.isEmpty(clientAlready.objName)){
      objName = clientAlready.objName!;
    }

    update();

    _updateWebClientInfo();
  }

  /// 更新状态栏（是否显示，修改标题等）
  _updateToolbar(String? title) {
    showToolbar = !StringUtil.isEmpty(title);
    barTitle = title ?? '';
    update();
  }

  /// 传递 token 等信息给 web
  _updateWebClientInfo() {
    var param = HashMap<String,dynamic>();
    var token = UserHelper.getAccessToken();
    var statusBarHeight = ScreenUtil().statusBarHeight;

    param.putIfAbsent('token', () => token);
    param.putIfAbsent('statusHeight', () => statusBarHeight);
    // todo  param put (user , company)
    _invokeJs(NativeCallJs.updateClientInfo.name , param: json.encode(param));
  }


  _invokeJs(String method , {String? param}) async {
    var jsCode = '';
    if (param == null) {
      jsCode = 'javascript:window.${objName}.${method}()';
    } else {
      jsCode = "javascript:window.${objName}.${method}($param)";
    }
    return await webViewController?.runJavaScript(
        jsCode);
  }

  goBack() async {
    if(webViewController == null) return;
    if(await webViewController!.canGoBack()){
       webViewController!.goBack();
    }else {
       Get.back();
    }
  }

  updateProgressBar(int progress) async {
    this.webProgress = progress;
    update();
  }

  @override
  void onClose() {
    super.onClose();
    eventTianditu?.cancel();
    Channel().invoke(DEALLOC_WEB_VIEW);//目前销毁最后一个，销毁指定改为tag
  }
}


