import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NativeWebView extends StatelessWidget {

  final webViewType = 'dd_webview_type';

  Map<dynamic, dynamic> creationParams = <dynamic, dynamic>{};

  NativeWebView({Key? key , required this.creationParams}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            child: _createWebView()
        ));
  }

  Widget _createWebView() {
    if (Platform.isAndroid) {
      return AndroidView(
        viewType: webViewType,
        creationParams: creationParams,
        creationParamsCodec: const StandardMessageCodec(),
        onPlatformViewCreated: (int id) {

        },
        gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
          Factory<OneSequenceGestureRecognizer>(
                () => EagerGestureRecognizer(),
          ),
        },
      );
    } else if(Platform.isIOS) {
      return UiKitView(
        viewType: webViewType,
        creationParams: creationParams,
        creationParamsCodec: const StandardMessageCodec(),
        gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
          Factory<OneSequenceGestureRecognizer>(
                () => EagerGestureRecognizer(),
          ),
        },
      );
    }
    return Container();
  }


}
