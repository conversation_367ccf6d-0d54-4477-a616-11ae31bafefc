

class ClientAlreadyEntity {

  String? title = '';
  String? subTitle = '';
  String? objName = '';
  bool? useTitleBar = false;

  ClientAlreadyEntity(
  {this.title, this.subTitle, this.objName, this.useTitleBar});

  factory ClientAlreadyEntity.fromJson(Map<String, dynamic> json) {
    return ClientAlreadyEntity(
      title: json['title'],
      subTitle: json['subTitle'],
      objName: json['objName'],
      useTitleBar: json['useTitleBar'],
    );
  }

  Map<String, dynamic> toJson() => {
        'title': title,
        'subTitle': subTitle,
        'objName': objName,
        'useTitleBar': useTitleBar,
      };
}