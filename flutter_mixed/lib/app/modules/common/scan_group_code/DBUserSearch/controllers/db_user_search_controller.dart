import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/db/db_helper.dart';
import 'package:flutter_mixed/app/db/user/db_user_model.dart';
import 'package:flutter_mixed/app/db/user/db_user_model_ext.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../contact/model/friend/user_model.dart';

class DBUserSearchController extends GetxController {

  RxList selectGroupList = [].obs;
  RxList searchList = [].obs;
  TextEditingController searchController = TextEditingController();
  FocusNode node = FocusNode();
  String myUserId = '';
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments['selectGroupList'] != null) {
        selectGroupList = Get.arguments['selectGroupList'];
      }
    }
    UserHelper.getUid().then((uid) {
      myUserId = uid;
    });
    
  }

  searchText(text) {
    searchList.clear;
    _getLocalData(text);
  }

  //获取本地数据
  _getLocalData(text) async {
    List friendList = await UserDefault.getData(Define.FRIENDLIST);
    searchList.clear();
    if (friendList.isNotEmpty) {
      for (var j = 0; j < friendList.length; j++) {
        UserModel model = UserModel.fromJson(friendList[j]);
        if (model.name.contains(text) || model.remark.contains(text)) {
          MemberModel memberModel = MemberModel(model.userId);
          memberModel.headimg = model.avatar;
          memberModel.remark = model.remark;
          memberModel.name = model.name;
          memberModel.imId = model.imId;
          searchList.add(memberModel);
        }
      }
    }
    List<DBUserModel> users = await DBHelper.getAllLocalUser(myUserId);
    for (var i = 0; i < users.length; i++) {
      DBUserModel userModel = users[i];
      bool isHave = false;
      for (var j = 0; j < searchList.length; j++) {
        MemberModel searchModel = searchList[j];
        if (searchModel.userId == userModel.userId) {
          isHave = true;
          break;
        }
      }
      if (!isHave) {
        if (userModel.userName != null) {
          if (userModel.userName!.contains(text)) {
            isHave = true;
            MemberModel memberModel = userModel.getMemberModel();
            searchList.add(memberModel);
          }
        }
      }
      if (!isHave) {
        if (userModel.remark != null) {
          if (userModel.remark!.contains(text)) {
            MemberModel memberModel = userModel.getMemberModel();
            searchList.add(memberModel);
          }
        }
      }
    }
    for (var i = 0; i < searchList.length; i++) {
      MemberModel searchModel = searchList[i];
      for (var j = 0; j < selectGroupList.length; j++) {
        MemberModel selectModel = selectGroupList[j];
        if (searchModel.userId == selectModel.userId) {
          if (selectModel.chooseState == 2) {
            searchModel.chooseState = 2;
          }else{
            searchModel.chooseState = 1;
          }
          break;
        }
      }
    }
    update();
  }

  @override
  void onReady() {
    super.onReady();
    node.requestFocus();
  }

  @override
  void onClose() {
    node.unfocus();
    searchController.dispose();
    super.onClose();
  }
}
