import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';

import '../controllers/db_user_search_controller.dart';

class DBUserSearchView extends GetView<DBUserSearchController> {
  DBUserSearchView({Key? key}) : super(key: key);
  DBUserSearchController dbUserSearchController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: dbUserSearchController,
        builder: (logic) {
          return Scaffold(
            backgroundColor: ColorConfig.backgroundColor,
            appBar: TitleBar().backAppbar(
                context, '搜索联系人', false, _backAction(), onPressed: () {
              Get.back();
            }),
            body: Column(
              children: [
                _backSearchWidget(),
                Expanded(child: _backListView(context))
              ],
            ),
          );
        });
  }

  _backAction() {
    List<Widget> lists = [];
    lists.add(Container(
      padding: const EdgeInsets.only(right: 16),
      child: CupertinoButton(
          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          pressedOpacity: 0.5,
          child: const Text(
            '确定',
            style: TextStyle(color: ColorConfig.mainTextColor, fontSize: 14),
          ),
          onPressed: () {
            Get.back(result: {
              'selectGroupList': dbUserSearchController.selectGroupList
            });
          }),
    ));
    return lists;
  }

  _backSearchWidget() {
    return Container(
        width: double.infinity,
        color: Colors.white,
        height: 50,
        padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
        child: SettingWidget().backSearchWidget(
            dbUserSearchController.searchController,
            dbUserSearchController.node,
            '搜索联系人',
            onSub: (value) {
              dbUserSearchController.searchText(value);
            },
            onTap: () async {}));
  }

  _backListView(context) {
    return MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: ListView.builder(
            itemCount: dbUserSearchController.searchList.length,
            itemBuilder: ((context, index) {
              MemberModel model = dbUserSearchController.searchList[index];
              String selectStr = AssetsRes.APPROVE_SELECTED;
              if (model.chooseState == 0) {
                selectStr = AssetsRes.APPROVE_UNSELECTED;
              }
              logger('=====${model.name}==${model.chooseState}');
              return InkWell(
                onTap: () {
                  if (model.chooseState == 2) {
                    toast('不能取消当前成员');
                    return;
                  }
                  if (model.chooseState == 1) {
                    model.chooseState = 0;
                    for (var i = 0; i < dbUserSearchController.selectGroupList.length; i++) {
                      MemberModel selectModel = dbUserSearchController.selectGroupList[i];
                      if (selectModel.userId == model.userId) {
                        dbUserSearchController.selectGroupList.remove(selectModel);
                      }
                    }
                  }
                  else if (model.chooseState == 0) {
                    model.chooseState = 1;
                    dbUserSearchController.selectGroupList.add(model);
                  }
                  dbUserSearchController.update();
                },
                child: SettingWidget().backSettingWidget(
                    selectStr, model.headimg, model.name, '', true, 60),
              );
            })));
  }
}
