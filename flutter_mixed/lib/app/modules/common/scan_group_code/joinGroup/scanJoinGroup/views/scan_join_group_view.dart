import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/joinGroup/scanJoinGroup/views/scan_join_group_bg.dart';

import 'package:get/get.dart';

import '../../../../../../common/config/config.dart';
import '../controllers/scan_join_group_controller.dart';

class ScanJoinGroupView extends GetView<ScanJoinGroupController> {
  ScanJoinGroupView({Key? key}) : super(key: key);
  ScanJoinGroupController scanJoinGroupController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: scanJoinGroupController,
        builder: (logic) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            backgroundColor: ColorConfig.backgroundColor,
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(44),
                child: AppBar(
                  title: const Text(
                    '',
                    style: TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 16),
                  ),
                  centerTitle: true,
                  leading: IconButton(
                    onPressed: () {
                      Get.back();
                    },
                    icon: SizedBox(
                      width: 24,
                      height: 24,
                      child: Image.asset('assets/images/3.0x/pic_return.png'),
                    ),
                  ),
                  backgroundColor: const Color(0x00FFFFFF),
                  elevation: 0,
                )),
            body: Container(
              child: Stack(
                children: [
                  ScanJoinGroupBg(),
                  Column(
                    children: [
                      155.gap,
                      Expanded(
                          child: Container(
                        child: Column(
                          children: [
                            Container(
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              padding:
                                  const EdgeInsets.fromLTRB(16, 24, 16, 24),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16),
                                  color: ColorConfig.whiteColor),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 56,
                                    height: 56,
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            width: 1,
                                            color: ColorConfig.lineColor),
                                        borderRadius: BorderRadius.circular(8)),
                                    child: ImageLoader(
                                      width: 56,
                                      height: 56,
                                      url:
                                          scanJoinGroupController.model?.logo ??
                                              '',
                                      radius: 8,
                                    ),
                                  ),
                                  14.gap,
                                  Container(
                                    alignment: Alignment.center,
                                    child: Text(
                                      scanJoinGroupController.groupName.value,
                                      style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: ColorConfig.mainTextColor),
                                    ),
                                  ),
                                  4.gap,
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        alignment: Alignment.center,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                        decoration: BoxDecoration(
                                            border: Border.all(
                                                width: 0.5,
                                                color: ColorConfig.themeCorlor),
                                            borderRadius:
                                                BorderRadius.circular(4)),
                                        child: const Text(
                                          '群主',
                                          style: TextStyle(
                                              fontSize: 11,
                                              color: ColorConfig.themeCorlor),
                                        ),
                                      ),
                                      4.gap,
                                      Flexible(
                                          child: Container(
                                        child: Text(
                                          scanJoinGroupController
                                                  .model?.createUserName ??
                                              '',
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                              fontSize: 14,
                                              color: ColorConfig.mainTextColor),
                                        ),
                                      ))
                                    ],
                                  ),
                                  24.gap,
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const ImageLoader(
                                        width: 16,
                                        height: 16,
                                        url:
                                            'assets/images/3.0x/group_population.png',
                                        radius: 0,
                                      ),
                                      4.gap,
                                      Flexible(
                                          child: Container(
                                        child: Text(
                                          '${scanJoinGroupController.model?.members ?? 0}人',
                                          style: const TextStyle(
                                              fontSize: 14,
                                              color: ColorConfig.mainTextColor),
                                        ),
                                      ))
                                    ],
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      )),
                      InkWell(
                        onTap: () {
                          scanJoinGroupController.joinGroup();
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          height: 44,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                              color: ColorConfig.themeCorlor),
                          child: const Text(
                            '立即加入',
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.whiteColor),
                          ),
                        ),
                      ),
                      (DeviceUtils().bottom.value + 10).gap
                    ],
                  )
                ],
              ),
            ),
          );
        });
  }
}
