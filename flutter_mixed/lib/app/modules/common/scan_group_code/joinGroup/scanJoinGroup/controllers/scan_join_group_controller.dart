import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:get/get.dart';

import '../../../../../../../main.dart';
import '../../../../../../common/api/LoginApi.dart';
import '../../../../../../common/channel/channel.dart';
import '../../../../../../common/widgets/widgets.dart';
import '../../../../../../retrofit/datasource/workbench_datasource.dart';
import '../../../../../../retrofit/entity/group/group_info.dart';

class ScanJoinGroupController extends GetxController {

  RxString groupName = ''.obs;
  GroupQrCodeDataModel? model;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments['model'] != null) {
        model = Get.arguments['model'];
        groupName.value = model!.name;
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  joinGroup() async {
    if (model == null) return;
    try {
      Get.loading();
      var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await workDatasource.scanGroupQrCodeJoin(model!.code);
      Get.dismiss();
      if (resp.success()) {
        Get.back();
        // Channel().invoke(Channel_jumpGroupChat, {
        //   'group': {
        //     'groupId': model!.groupId,
        //     'logo': model!.logo,
        //     'name': model!.name
        //   }
        // });

        var groupParam = {
          'group': {
            'groupId': model!.groupId,
            'logo': model!.logo,
            'name': model!.name
          }
        };
        groupParam.createGroupSessionAndEnter();

      } else {
        toast(resp.msg);
      }
    } catch (e) {}
  }
}
