import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

class ScanJoinGroupBg extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorConfig.backgroundColor,
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(painter: DiagonalSplitPainter()),
    );
  }
}

class DiagonalSplitPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double diagonalOffset = 80;

    // var trapezoid = Path()
    //   ..moveTo(0, 0)
    //   ..lineTo(size.width, 0)
    //   ..lineTo(size.width, size.height)
    //   ..lineTo(0, size.height)
    //   ..close();

    var triangle = Path()
      ..moveTo(size.width, diagonalOffset)
      ..lineTo(size.width, size.height)
      ..lineTo(size.width / 5, size.height)
      ..close();

    // final redPaint = Paint()
    //   ..color = ColorConfig.backgroundColor
    //   ..style = PaintingStyle.fill;

    final bluePaint = Paint()
      ..color = ColorConfig.mybackgroundColor
      ..style = PaintingStyle.fill;

    //canvas.drawPath(trapezoid, redPaint);
    canvas.drawPath(triangle, bluePaint);
  }

  @override
  bool shouldRepaint(covariant DiagonalSplitPainter oldDelegate) {
    return false;
  }
}