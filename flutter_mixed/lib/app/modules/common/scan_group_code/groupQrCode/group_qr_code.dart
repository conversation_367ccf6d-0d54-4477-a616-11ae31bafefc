import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/groupQrCode/code_expired_time_dialog.dart';
import 'package:flutter_mixed/app/modules/helper/photo_manager.dart';
import 'package:flutter_mixed/app/retrofit/entity/group/group_info.dart';
import 'package:get/get.dart';
import 'dart:ui' as ui;

import 'package:qr_flutter/qr_flutter.dart';

import '../../../../../main.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../retrofit/datasource/workbench_datasource.dart';

class GroupQrCodePage extends StatefulWidget {
  final argument;

  GroupQrCodePage({this.argument});

  @override
  GroupQrCodePageState createState() => GroupQrCodePageState();
}

class GroupQrCodePageState extends State<GroupQrCodePage>
    with AutomaticKeepAliveClientMixin {
  final GlobalKey codeKey = GlobalKey();
  StreamSubscription? subscription;
  String groupId = '';
  RxString groupLogo = ''.obs;
  RxString groupName = ''.obs;
  RxString codeStr = ''.obs;
  double imageW = 56;
  String sevenStr = ''; //7天后的日期
  String mouthStr = ''; //30天后的日期
  RxString currentStr = ''.obs; //当前选择的日期，此界面有效。退出后重新默认7天
  List chooseList = [];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _dealData(widget.argument);
    _fetchGroupInfo();
    _getGroupQrCode(1);
    subscription = eventBus.on<Map>().listen((event) {
      if (event['groupQrCodePage'] != null) {
        _dealData(event['groupQrCodePage']);
      }
    });
  }

  _dealData(argument) {
    if (argument != null) {
      groupId = argument['groupId'];
    }
    sevenStr = '7天内 (${BaseInfo().backAfterMoreDays(7)}) 前有效';
    mouthStr = '30天内 (${BaseInfo().backAfterMoreDays(30)}) 前有效';
    chooseList = [sevenStr, mouthStr, '永久有效'];
    currentStr.value = sevenStr;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
          color: ColorConfig.whiteColor,
          padding: EdgeInsets.only(bottom: DeviceUtils().bottom.value),
          child: Column(
            children: [
              Expanded(
                  child: SingleChildScrollView(
                child: Column(
                  children: [
                    RepaintBoundary(
                      key: codeKey,
                      child: Container(
                        color: Colors.white,
                        padding: const EdgeInsets.fromLTRB(16, 24, 16, 15),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.only(top: 70),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Color(0xFF85AAFF),
                                    ColorConfig.themeCorlor
                                  ])),
                          child: Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: ColorConfig.whiteColor,
                                  boxShadow: const [
                                    BoxShadow(
                                      color: ColorConfig.backgroundColor,
                                      offset: Offset(5.0, 5.0),
                                      blurRadius: 10.0,
                                      spreadRadius: 0.0,
                                    ),
                                  ],
                                ),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 30,
                                      child: Stack(
                                        clipBehavior: Clip.none,
                                        children: [
                                          Positioned(
                                              top: -(imageW * 0.5),
                                              left: (DeviceUtils().width.value -
                                                      32 -
                                                      imageW) *
                                                  0.5,
                                              width: imageW,
                                              height: imageW,
                                              child: Container(
                                                width: imageW,
                                                height: imageW,
                                                decoration: BoxDecoration(
                                                    border: Border.all(
                                                        width: 1,
                                                        color: ColorConfig
                                                            .lineColor),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12)),
                                                child: ImageLoader(
                                                  width: imageW,
                                                  height: imageW,
                                                  url: groupLogo.value,
                                                  radius: 12,
                                                ),
                                              )),
                                        ],
                                      ),
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        10.gap,
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 40),
                                          alignment: Alignment.center,
                                          child: Text(
                                            '群聊：${groupName.value}',
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color:
                                                    ColorConfig.mainTextColor),
                                          ),
                                        ),
                                        10.gap,
                                        Container(
                                          width: 150,
                                          height: 150,
                                          color: ColorConfig.whiteColor,
                                          child: codeStr.value == ''
                                              ? Container()
                                              : QrImageView(
                                                  data: codeStr.value,
                                                  size: 150,
                                                ),
                                        ),
                                        16.gap,
                                        Container(
                                          child: const Text(
                                            '扫描群二维码，立刻加入该群',
                                            style: TextStyle(
                                                fontSize: 14,
                                                color:
                                                    ColorConfig.mainTextColor),
                                          ),
                                        ),
                                        10.gap,
                                        Container(
                                          child: Text(
                                            '该二维码 ${currentStr.value}',
                                            style: const TextStyle(
                                                fontSize: 14,
                                                color:
                                                    ColorConfig.desTextColor),
                                          ),
                                        ),
                                        40.gap
                                      ],
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        _tapChangeExpiredView();
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        width: double.infinity,
                        height: 44,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: ColorConfig.backgroundColor),
                        child: Row(
                          children: [
                            const Text(
                              '更改有效期',
                              style: TextStyle(
                                  fontSize: 14, color: ColorConfig.themeCorlor),
                            ),
                            Expanded(
                                child: Container(
                              alignment: Alignment.centerRight,
                              child: Text(
                                currentStr.value,
                                style: const TextStyle(
                                    fontSize: 12,
                                    color: ColorConfig.themeCorlor),
                              ),
                            )),
                            8.gap,
                            SizedBox(
                              width: 9,
                              height: 17,
                              child: Image.asset(
                                  'assets/images/3.0x/mine_right.png'),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
              InkWell(
                onTap: () {
                  if (codeStr.value.isEmpty) {
                    toast('无效二维码不能保存');
                    return;
                  }
                  _savePicture();
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.center,
                  height: 44,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: ColorConfig.themeCorlor,
                  ),
                  child: const Text(
                    '保存图片',
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: ColorConfig.whiteColor),
                  ),
                ),
              )
            ],
          ),
        ));
  }

  //点击了更改有效期
  _tapChangeExpiredView() {
    ChangeCodeExpiredTimeDialog().show(chooseList, (index) {
      currentStr.value = chooseList[index];
      int type = 1;
      if (index == 0) {
        type = 1;
      }
      if (index == 1) {
        type = 2;
      }
      if (index == 2) {
        type = 0;
      }
      _getGroupQrCode(type);
    });
  }

  // 保存到手机
  _savePicture() async {
    RenderRepaintBoundary boundary =
        codeKey.currentContext?.findRenderObject() as RenderRepaintBoundary;
    ui.Image image =
        await boundary.toImage(pixelRatio: ui.window.devicePixelRatio);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    // 保存到相册
    final result = await PhotoHelper.saveToGallery(pngBytes);
    String alertDoc = result ? "保存成功" : "保存失败";
    toast(alertDoc);
  }

  //获取群组详情
  _fetchGroupInfo() async {
    var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
    var resp = await workDatasource.getGroupInfo(groupId);

    if (resp.success()) {
      if (resp.data.isEmpty) {
        return;
      }
      groupName.value = resp.data[0].name ?? '';
      groupLogo.value = resp.data[0].logo ?? '';
      groupName.refresh();
    } else {
      toast(resp.msg);
    }
  }

  //生成二维码
  _getGroupQrCode(int type) async {
    var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
    var body = GroupQrCodeReq(groupId, type);
    var resp = await workDatasource.getGroupQrCode(body);

    if (resp.success()) {
      if (resp.data.isNotEmpty) {
        codeStr.value = resp.data;
      }
    } else {
      toast(resp.msg);
    }
  }
}
