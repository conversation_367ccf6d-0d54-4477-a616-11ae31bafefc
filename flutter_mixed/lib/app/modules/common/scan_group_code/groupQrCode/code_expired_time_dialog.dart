import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/groupQrCode/code_expired_widget.dart';
import 'package:get/get.dart';

class ChangeCodeExpiredTimeDialog {
  show(List contentList,Function onPressed) {
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Scaffold(
          backgroundColor: Colors.transparent,
          body: Stack(
            children: [
              Container(
                alignment: Alignment.bottomCenter,
                color: ColorConfig.maskColor,
                child: Container(
                  decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12)),
                      color: ColorConfig.whiteColor),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      10.gap,
                      Container(
                        height: 44,
                        padding: const EdgeInsets.symmetric(horizontal: 6),
                        child: Row(
                          children: [
                            44.gap,
                            Expanded(
                                child: Container(
                              alignment: Alignment.center,
                              child: const Text(
                                '更改有效期',
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: ColorConfig.mainTextColor),
                              ),
                            )),
                            InkWell(
                              onTap: () {
                                Get.back();
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: 44,
                                height: 44,
                                child: const ImageLoader(
                                  width: 24,
                                  height: 24,
                                  url:
                                      'assets/images/3.0x/contact_dept_close.png',
                                  radius: 0,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                      Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 3),
                        child: const Text(
                          '该有效期仅对此次分享生效',
                          style: TextStyle(
                              fontSize: 12, color: ColorConfig.desTextColor),
                        ),
                      ),
                      8.gap,
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                              color: ColorConfig.backgroundColor),
                          child: CodeExpriedWidget( {'contentList':contentList},(index){
                            onPressed(index);
                            Get.back();
                          }),
                        ),
                      ),
                      10.gap,
                      const Divider(height: 1,color: ColorConfig.lineColor,),
                      DeviceUtils().bottom.value.gap
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
  }

  
}
