import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../common/config/config.dart';

class CodeExpriedWidget extends StatefulWidget {
  final argument;
  Function onPressed;
  CodeExpriedWidget(this.argument,this.onPressed);
  @override
  CodeExpriedWidgetState createState() => CodeExpriedWidgetState();
}

class CodeExpriedWidgetState extends State<CodeExpriedWidget> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _backExpiredCell(),
    );
  }

  _backExpiredCell() {
    List contentList = widget.argument['contentList'];
    List<Widget> lists = [];
    for (var i = 0; i < contentList.length; i++) {
      lists.add(Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: () {
              widget.onPressed(i);
            },
            child: Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              height: 44,
              child: Text(
                contentList[i],
                style:
                    const TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
              ),
            ),
          ),
          if (i != contentList.length - 1) ...[
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              color: ColorConfig.lineColor,
              height: 0.5,
            )
          ]
        ],
      ));
    }
    return lists;
  }
}
