import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/addMembes/user_selected_widget.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/groupAddMembers/controllers/group_add_members_controller.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/model/select-group-model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/config/config.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/storage.dart';
import '../../../contact/model/org/dept_members_model.dart';
import '../../../contact/model/org/org_model.dart';

class GroupAddMembers extends StatefulWidget {
  final argument;

  GroupAddMembers({this.argument}) {
    logger('---------memebers --------$argument');
  }

  @override
  GroupAddMembersState createState() => GroupAddMembersState();
}

class GroupAddMembersState extends State<GroupAddMembers>
    with AutomaticKeepAliveClientMixin {
  StreamSubscription? subscription;
  RxList dataList = [].obs;

  RxList selectGroupList = [].obs;

  String myUserId = '';
  String groupId = ''; //群组id
  int type = 0; //0创建群组 1群组添加成员 2im消息转发

  StreamSubscription? subscription1;
  bool isFromNative = false;

  GroupAddMembersController? groupAddMembersController;

  TextEditingController searchController = TextEditingController();
  FocusNode node = FocusNode();
  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _dealData(widget.argument);

    subscription = eventBus.on<SelectGroupmodel>().listen((event) {
      if (event.sign != null) {
        if (event.sign == Routes.GROUP_ADD_MEMBERS &&
            event.selectGroupList != null) {
          selectGroupList = event.selectGroupList!;
          if (groupAddMembersController != null) {
            groupAddMembersController!.dealselectGroupList(selectGroupList,
                isChooseEnd: event.isChooseEnd ?? false);
          }
          dataList.refresh();
        }
      }
    });
    subscription1 = eventBus.on<Map>().listen((event) {
      if (event['groupAddMembers'] != null) {
        _dealData(event['groupAddMembers']);
      }
    });
    getAllCompany();
  }

  _getMyUserId() async {
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    myUserId = tokenInfo['userId'];
    MemberModel userModel = MemberModel(myUserId);
    userModel.name = tokenInfo['name'];
    userModel.chooseState = 2;
    selectGroupList.add(userModel);
  }

  _dealData(argument) {
    selectGroupList.clear();
    if (argument != null) {
      Map eventMap = argument;
      if (eventMap['type'] != null) {
        type = eventMap['type'];
      }
      if (eventMap['controller'] != null) {
        groupAddMembersController = eventMap['controller'];
      }
      if (eventMap['isFromNative'] != null) {
        isFromNative = eventMap['isFromNative'];
      }
      if (type != 2 && Platform.isIOS) {
        _getMyUserId();
      }

      if (eventMap['selectList'] != null) {
        List selectList = eventMap['selectList'];
        for (var i = 0; i < selectList.length; i++) {
          String userId = selectList[i];
          MemberModel userModel = MemberModel(userId);
          userModel.chooseState = 2;
          if (eventMap['userList'] != null) {
            List userList = eventMap['userList'];
            if (userList.length > i) {
              Map userMap = userList[i];
              userModel.name = userMap['name'];
            }
          }
          selectGroupList.add(userModel);
        }
      }
      if (eventMap['groupId'] != null) {
        groupId = eventMap['groupId'];
      }
    }
    dataList.refresh();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (groupAddMembersController != null) {
        groupAddMembersController!.dealselectGroupList(selectGroupList);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (type != 2) ...[
              _backSearchWidget(),
              UserSelected(
                userList: _backSelectedList(),
                selectGroupList: selectGroupList,
              ),
              10.gap,
              Container(
                width: double.infinity,
                height: 36,
                color: ColorConfig.whiteColor,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '联系人',
                  style:
                      TextStyle(fontSize: 13, color: ColorConfig.mainTextColor),
                ),
              ),
            ],
            Expanded(
                child: ListView.builder(
                    itemCount:
                        type == 2 ? dataList.length + 2 : dataList.length + 1,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return InkWell(
                          onTap: () async {
                            var result =
                                await Get.toNamed(Routes.MY_FRIEND, arguments: {
                              'chooseType': type == 2 ? 2 : 1,
                              'isFromNative': isFromNative,
                              'selectGroupList': RxList(selectGroupList
                                  .map((e) => MemberModel.fromJson(e.toJson()))
                                  .toList())
                            });
                            if (result != null &&
                                result['selectGroupList'] != null &&
                                result['isChooseEnd'] != null) {
                              selectGroupList = result['selectGroupList'];
                              if (groupAddMembersController != null) {
                                groupAddMembersController!.dealselectGroupList(
                                    selectGroupList,
                                    isChooseEnd: result['isChooseEnd']);
                              }
                              dataList.refresh();
                            }
                          },
                          child: backCell(
                              ColorConfig.friendColor,
                              'assets/images/3.0x/contact_home_my_friend.png',
                              '我的好友'),
                        );
                      } else {
                        if (type == 2 && index == 1) {
                          return InkWell(
                            onTap: () async {
                              var result = await Get.toNamed(Routes.MY_GROUP,
                                  arguments: {
                                    'type': 1,
                                    'isFromNative': isFromNative,
                                    'selectGroupList': RxList(selectGroupList
                                        .map((e) =>
                                            MemberModel.fromJson(e.toJson()))
                                        .toList())
                                  });
                              if (result != null &&
                                  result['selectGroupList'] != null) {
                                selectGroupList = result['selectGroupList'];
                                if (groupAddMembersController != null) {
                                  groupAddMembersController!
                                      .dealselectGroupList(selectGroupList);
                                }
                              }
                            },
                            child: backCell(
                                ColorConfig.themeCorlor,
                                'assets/images/3.0x/contact_home_group.png',
                                '我的群组'),
                          );
                        } else {
                          int otherCount = 1;
                          if (type == 2) {
                            otherCount = 2;
                          }
                          OrgModel model = dataList[index - otherCount];
                          return InkWell(
                            onTap: () async {
                              var result = await Get.toNamed(Routes.DEPT_DETAIL,
                                  arguments: {
                                    'topModel': model,
                                    'levelList': ['联系人', model.name],
                                    'isFromNative': isFromNative,
                                    'model': model,
                                    'deptId': model.deptId,
                                    'isManager': false,
                                    'companyId': model.companyId,
                                    'type': type == 2 ? 5 : 4,
                                    'selectGroupList': RxList(selectGroupList
                                        .map((e) =>
                                            MemberModel.fromJson(e.toJson()))
                                        .toList()),
                                  },
                                  preventDuplicates: false);
                              if (result != null &&
                                  result['selectGroupList'] != null) {
                                selectGroupList = result['selectGroupList'];
                                if (groupAddMembersController != null) {
                                  groupAddMembersController!
                                      .dealselectGroupList(selectGroupList);
                                }
                                dataList.refresh();
                              }
                            },
                            child: SettingWidget().backSettingWidget(
                                '', model.logo, model.name, '', true, 56),
                          );
                        }
                      }
                    }))
          ],
        ));
  }

  _backSelectedList() {
    RxList chooseList = [].obs;
    for (var i = 0; i < selectGroupList.length; i++) {
      MemberModel model = selectGroupList[i];
      if (model.chooseState != 2 && type != 2) {
        chooseList.add(model);
      }
    }
    return chooseList;
  }

  _backSearchWidget() {
    return Container(
        width: double.infinity,
        color: Colors.white,
        height: 50,
        padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
        child: SettingWidget().backSearchWidget(searchController, node, '搜索联系人',
            onSub: (value) {}, onTap: () async {
          node.unfocus();
          var result = await Get.toNamed(Routes.D_B_USER_SEARCH,
              arguments: {'selectGroupList': selectGroupList},
              preventDuplicates: false);
          if (result != null && result['selectGroupList'] != null) {
            selectGroupList = result['selectGroupList'];
            if (groupAddMembersController != null) {
              groupAddMembersController!.dealselectGroupList(selectGroupList);
            }
          }
        }));
  }

  @override
  void dispose() {
    super.dispose();
    subscription?.cancel();
    subscription1?.cancel();
  }

  Widget backCell(Color lineColor, String headImageName, String name) {
    double viewH = 56;
    double imageW = 40;
    return Container(
      width: double.infinity,
      height: viewH,
      color: ColorConfig.whiteColor,
      padding: const EdgeInsets.only(left: 15, right: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.center,
            width: imageW,
            height: imageW,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                ),
            child: Container(
              width: imageW,
              height: imageW,
              child: Image.asset(headImageName),
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
              child: Container(
            child: Text(
              name,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          )),
          const SizedBox(
            width: 8,
          ),
          SizedBox(
            width: 9,
            height: 17,
            child: Image.asset('assets/images/3.0x/mine_right.png'),
          )
        ],
      ),
    );
  }

  getAllCompany() async {
    Map dataDic = await UserDefault.getData(Define.ORGLIST);

    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }
    if (companies.isNotEmpty) {
      dataList.value =
          companies.map((item) => OrgModel.fromJson(item)).toList();
    }
    dataList.refresh();
  }
}
