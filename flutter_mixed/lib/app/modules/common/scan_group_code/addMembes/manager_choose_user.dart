import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/model/select-group-model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class ManagerChooseUser extends StatelessWidget {
  RxList? selectGroupList = [].obs;
  RxList chooseList = [].obs;
  ManagerChooseUser(this.chooseList, {this.selectGroupList});
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Obx(() => Container(
            child: Container(
          padding: EdgeInsets.only(top: 44 + DeviceUtils().top.value + 50),
          color: ColorConfig.maskColor,
          child: Container(
            decoration: const BoxDecoration(
                color: ColorConfig.whiteColor,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10))),
            child: Column(
              children: [
                _backTitleWidget(),
                Expanded(child: _backListView(context))
              ],
            ),
          ),
        )));
  }

  _backTitleWidget() {
    return SizedBox(
      width: double.infinity,
      height: 44,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          6.gap,
          InkWell(
            onTap: () {
              Get.back();
            },
            child: Container(
              padding: const EdgeInsets.all(10),
              child: SizedBox(
                width: 24,
                height: 24,
                child: Image.asset('assets/images/3.0x/contact_dept_close.png'),
              ),
            ),
          ),
          Expanded(
              child: Container(
            alignment: Alignment.center,
            child: const Text(
              '已选',
              style: TextStyle(fontSize: 16, color: ColorConfig.mainTextColor),
            ),
          )),
          InkWell(
            onTap: () {
              if (chooseList.isEmpty) {
                Get.back();
              } else {
                eventBus.fire(SelectGroupmodel(
                    sign: Routes.GROUP_ADD_MEMBERS,
                    selectGroupList: selectGroupList,
                    isChooseEnd: true));
                Get.until((route) {
                  if (route.settings.name == Routes.GROUP_ADD_MEMBERS) {
                    return true;
                  }
                  return false;
                });
              }
            },
            child: Container(
              child: Text(
                '确定(${chooseList.length})',
                style: const TextStyle(
                    fontSize: 16, color: ColorConfig.themeCorlor),
              ),
            ),
          ),
          16.gap
        ],
      ),
    );
  }

  _backListView(context) {
    return MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: ListView.builder(
            itemCount: chooseList.length,
            itemBuilder: ((context, index) {
              MemberModel model = chooseList[index];
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                height: 64,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                        child: InkWell(
                      onTap: () {
                        _enterUserInfo(model.userId);
                      },
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10)),
                            child: ImageLoader(
                              url: model.headimg,
                              width: 40,
                              height: 40,
                              radius: 10,
                            ),
                          ),
                          12.gap,
                          Expanded(
                              child: Container(
                            child: Text(
                              model.name,
                              style: const TextStyle(
                                  fontSize: 16,
                                  color: ColorConfig.mainTextColor),
                            ),
                          ))
                        ],
                      ),
                    )),
                    10.gap,
                    InkWell(
                      onTap: () {
                        chooseList.removeAt(index);
                        if (selectGroupList != null) {
                          for (var i = 0; i < selectGroupList!.length; i++) {
                            MemberModel selectModel = selectGroupList![i];
                            if (selectModel.userId == model.userId) {
                              selectGroupList!.remove(selectModel);
                              break;
                            }
                          }
                        }
                        eventBus.fire(SelectGroupmodel(
                            sign: Routes.GROUP_ADD_MEMBERS,
                            selectGroupList: selectGroupList,
                            isChooseEnd: false));
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        alignment: Alignment.centerRight,
                        child: Container(
                          width: 38,
                          height: 22,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 0.5, color: ColorConfig.lineColor),
                              borderRadius: BorderRadius.circular(4)),
                          child: const Text(
                            '移除',
                            style: TextStyle(
                                fontSize: 12, color: ColorConfig.mainTextColor),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              );
            })));
  }

  _enterUserInfo(userId) {
    RouteHelper.route(Routes.USER_INFO,
        arguments: {'type': 2, 'userId': userId});
  }
}
