import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/addMembes/manager_choose_user.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:get/get.dart';

class UserSelected extends StatelessWidget {
  RxList? userList = [].obs;
  RxList? selectGroupList = [].obs;
  Function? dataBack;
  UserSelected({this.userList, this.selectGroupList, this.dataBack});
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _showDialog();
      },
      child: Obx(() => _buildWidget()),
    );
  }

  _showDialog() {
    if (userList == null) return;
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        ManagerChooseUser(
          userList!,
          selectGroupList: selectGroupList,
        ));
  }

  _buildWidget() {
    if (userList == null) return Container();
    if (userList!.isEmpty) return Container();
    return Container(
      color: ColorConfig.whiteColor,
      width: double.infinity,
      height: 88,
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 20,
            child: const Text(
              '已选择',
              style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
          Expanded(
              child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: userList?.length,
                      itemBuilder: ((context, index) {
                        MemberModel model = userList![index];
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8)),
                              child: ImageLoader(
                                url: model.headimg,
                                width: 32,
                                height: 32,
                                radius: 8,
                              ),
                            ),
                            12.gap
                          ],
                        );
                      }))),
              SizedBox(
                width: 9,
                height: 17,
                child: Image.asset('assets/images/3.0x/mine_right.png'),
              )
            ],
          )),
        ],
      ),
    );
  }
}
