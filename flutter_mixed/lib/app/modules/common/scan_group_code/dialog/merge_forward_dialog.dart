import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

class MergeForwardDialog {
  RxList forwardList = [].obs;
  TextEditingController msgController = TextEditingController();
  show(RxList chooseList, String text, Function onSend, Function onJumpMerge) {
    forwardList = chooseList;
    MemberModel model = forwardList.first;
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(() => Scaffold(
              backgroundColor: Colors.transparent,
              body: Stack(
                children: [
                  Container(
                    alignment: Alignment.center,
                    color: ColorConfig.maskColor,
                    child: Container(
                      margin: const EdgeInsets.only(left: 28, right: 28),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: ColorConfig.whiteColor,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  height: 20,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    forwardList.length == 1 ? '发送给' : '分别发送给',
                                    style: const TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                ),
                                8.gap,
                                if (forwardList.length == 1) ...[
                                  Container(
                                    height: 40,
                                    child: Row(
                                      children: [
                                        backItem(model),
                                        16.gap,
                                        Expanded(
                                            child: Container(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            model.name,
                                            style: const TextStyle(
                                                fontSize: 14,
                                                color:
                                                    ColorConfig.mainTextColor),
                                          ),
                                        )),
                                      ],
                                    ),
                                  )
                                ],
                                if (forwardList.length > 1) ...[
                                  GridView.count(
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    crossAxisCount: 5,
                                    padding: const EdgeInsets.only(
                                        left: 8, right: 16),
                                    crossAxisSpacing: 16,
                                    mainAxisSpacing: 8,
                                    childAspectRatio: 1,
                                    shrinkWrap: true,
                                    children: backSessionImageCell(),
                                  )
                                ],
                                8.gap,
                                Container(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  height: 44,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      color: ColorConfig.backgroundColor),
                                  child: InkWell(
                                    onTap: () async{
                                      onJumpMerge();
                                    },
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Expanded(
                                            child: Container(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            '[合并转发] $text',
                                            style: const TextStyle(
                                                fontSize: 14,
                                                color:
                                                    ColorConfig.mainTextColor),
                                          ),
                                        )),
                                        SizedBox(
                                          width: 9,
                                          height: 17,
                                          child: Image.asset(
                                              'assets/images/3.0x/mine_right.png'),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                                8.gap,
                                Container(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  height: 44,
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.backgroundColor),
                                      borderRadius: BorderRadius.circular(6)),
                                  child: TextField(
                                    onTap: () {},
                                    onChanged: (value) {},
                                    textInputAction: TextInputAction.done,
                                    controller: msgController,
                                    style: const TextStyle(
                                      color: ColorConfig.mainTextColor,
                                      fontSize: 14,
                                    ),
                                    decoration: const InputDecoration(
                                        contentPadding:
                                            EdgeInsets.only(top: 0, bottom: 0),
                                        border: OutlineInputBorder(
                                            borderSide: BorderSide.none),
                                        hintText: '留言...',
                                        hintStyle: TextStyle(
                                          color: ColorConfig.desTextColor,
                                          fontSize: 14,
                                        )),
                                  ),
                                )
                              ],
                            ),
                          ),
                          const Divider(
                            height: 1,
                            color: ColorConfig.lineColor,
                          ),
                          Container(
                            height: 48,
                            child: Row(
                              children: [
                                Expanded(
                                    flex: 1,
                                    child: InkWell(
                                      onTap: () {
                                        hiden();
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: const Text(
                                          '取消',
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: ColorConfig.desTextColor),
                                        ),
                                      ),
                                    )),
                                Container(
                                  color: ColorConfig.lineColor,
                                  width: 0.5,
                                  height: double.infinity,
                                ),
                                Expanded(
                                    flex: 1,
                                    child: InkWell(
                                      onTap: () {
                                        hiden();
                                        onSend(msgController.text);
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: const Text(
                                          '发送',
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: ColorConfig.themeCorlor),
                                        ),
                                      ),
                                    ))
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  )
                ],
              ),
            )));
  }

  backSessionImageCell() {
    List<Widget> lists = [];
    for (var i = 0; i < forwardList.length; i++) {
      MemberModel model = forwardList[i];
      lists.add(InkWell(
        child: backItem(model),
      ));
    }
    return lists;
  }

  backItem(MemberModel model) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
      child: ImageLoader(
        width: 40,
        height: 40,
        url: model.headimg,
        radius: 8,
      ),
    );
  }

  hiden() {
    // msgController.dispose();
    Get.back();
  }
}
