import 'dart:async';

import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/base_get_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/request/entity/chat_resp_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/addMembes/group_add_members.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/dialog/merge_forward_dialog.dart';
import 'package:flutter_mixed/app/utils/cache_helper.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../common/widgets/ios_native_route_focus.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../routes/app_pages.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../../contact/model/org/dept_members_model.dart';
import '../../../../home/<USER>/home_controller.dart';
import '../../groupQrCode/group_qr_code.dart';
import 'package:get/get.dart' as GroupGet;

class GroupAddMembersController extends BaseGetXController
    with MixNativeController {

  String groupId = '';
  RxBool isNative = false.obs; //是否是原生跳转
  StreamSubscription? subscription;

  TabController? tabController;
  PageController pageController = PageController();
  RxInt currentIndex = 0.obs;

  List<Widget> tabWidgets = [];
  RxList selectGroupList = [].obs;
  int type = 0; //0创建群组 1群组添加成员 2im消息转发
  int forwardType = 0; //0默认单条转发 1逐条转发 2合并转发
  String fromSessionId = ''; //多选转发的来源
  String myUserId = '';

  bool acceptParam = false;

  RxString titleStr = ''.obs;
  String forwardText = '';
  List<MemberModel> chooseList = [];
  @override
  void onInit() async {
    super.onInit();
    // tabWidgets = [
    //   GroupAddMembers(
    //     argument: {'controller': this},
    //   ),
    //   GroupQrCodePage()
    // ];
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    myUserId = tokenInfo['userId'];

    // 兼容Flutter本地传值和native传值情况
    subscription = GroupGet.Get.getOrEventParams(Routes.GROUP_ADD_MEMBERS,
        arg: (arg) {
          try {
            var arguments = arg;
            selectGroupList.clear();
            Map eventMap = arguments;
            if (eventMap != null) {
              if (eventMap['type'] != null) {
                type = eventMap['type'];
              }
              if (eventMap['groupId'] != null) {
                groupId = eventMap['groupId'];
              }
            }
            _dealTitleStr();
            eventMap['controller'] = this;
            tabWidgets = [
              GroupAddMembers(
                argument: eventMap,
              ),
              GroupQrCodePage(argument: {'groupId': groupId})
            ];
            // eventBus.fire({'groupAddMembers': eventMap});
            // eventBus.fire({'groupQrCodePage': {'groupId':groupId}});
            update();

            if (arguments['index'] != null) {
              currentIndex.value = arguments['index'];
            }
            if (arguments['forwardType'] != null) {
              forwardType = arguments['forwardType'];
            }
            if (arguments['fromSessionId'] != null) {
              fromSessionId = arguments['fromSessionId'];
            }
            if (arguments['forwardText'] != null) {
              forwardText = arguments['forwardText'];
            }

            tabController?.animateTo(currentIndex.value);
          } catch (e) {}
        },
        isNative: isNative,
        voidBack: () {
          acceptParam = true;
          update();
        });

    /*subscription = eventBus.on<Map>().listen((event) async {
      if (event == null) return;
      var route = event['route'];
      if (route == Routes.GROUP_ADD_MEMBERS) {
        isNative.value = true;
        var arguments = event['arguments'];
        groupId = arguments['groupId'];

        arguments['isFromNative'] = true;

        nativeOpen();
        selectGroupList.clear();
        Map eventMap = event['arguments'];
        if (event['arguments'] != null) {
          if (eventMap['type'] != null) {
            type = eventMap['type'];
          }
          if (eventMap['groupId'] != null) {
            groupId = eventMap['groupId'];
          }
        }

        eventMap['controller'] = this;
        tabWidgets = [
          GroupAddMembers(
            argument: eventMap,
          ),
          GroupQrCodePage(argument: {'groupId': groupId})
        ];
        // eventBus.fire({'groupAddMembers': eventMap});
        // eventBus.fire({'groupQrCodePage': {'groupId':groupId}});
        update();
        if (arguments['index'] != null) {
          currentIndex.value = arguments['index'];
        }
        if (tabController != null) {
          tabController!.animateTo(currentIndex.value);
        }
      }
    });*/
  }

  @override
  void onReady() {
    super.onReady();
    if (type == 0 || type == 1) {
      bool isHome = Get.isRegistered<HomeController>();
      if (isHome) {
        HomeController homeController = Get.find();
        homeController.getAllCompanies(isSaveUsers: true);
      }
    }
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

  createGroup() async {
    List userIds = [];
    List names = [];

    bool isHaveMe = false;
    for (var i = 0; i < selectGroupList.length; i++) {
      MemberModel model = selectGroupList[i];
      if (model.userId != myUserId) {
        userIds.add(model.userId);
      } else {
        isHaveMe = true;
      }

      names.add(model.name);
    }

    if (!isHaveMe) {
      Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
      names.insert(0, tokenInfo['name']);
    }

    String nameStr = names.join(',');
    if (nameStr.length > 30) {
      nameStr = nameStr.getRange(0, 30);
    }

    if (userIds.isEmpty) {
      toast('未选择成员');
      return;
    }

    Map dict = {'groupName': nameStr, 'userIds': userIds};

    DioUtil().post(LoginApi.IM_GROUP_CREATE, dict, true, () {}).then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        toast('创建成功');
        if (isNative.value) {
          onClose();
          dispose();
          Channel().invoke(Channel_Native_Back, {});
        } else {
          Get.back();
        }
        bool isHave = Get.isRegistered<HomeController>();
        if (isHave) {
          Get.until((route) {
            if (route.settings.name == Routes.HOME) {
              return true;
            }
            return false;
          });
        }
        Channel().invoke(Channel_Native_Pop_Root, {});

        var groupParam = {
          'group': {
            'groupId': data['data']['groupId'],
            'logo': data['data']['logo'],
            'name': nameStr
          }
        };
        groupParam.createGroupSessionAndEnter();

        // Channel().invoke(Channel_jumpGroupChat, {
        //   'group': {
        //     'groupId': data['data']['groupId'],
        //     'logo': data['data']['logo'],
        //     'name': nameStr
        //   }
        // });
        if (isHave) {
          HomeController homeController = Get.find();
          homeController.getAllGroup();
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  inviteMemberJoinGroup() {
    logger('----grouplist--$selectGroupList---$hashCode');
    List userIds = [];
    for (var i = 0; i < selectGroupList.length; i++) {
      MemberModel memberModel = selectGroupList[i];
      if (memberModel.chooseState != 2) {
        userIds.add(memberModel.userId);
      }
    }
    if (userIds.isEmpty) {
      toast('未选择成员');
      return;
    }
    Map dict = {'groupId': groupId, 'userIds': userIds};
    DioUtil().post(LoginApi.IM_GROUP_MEMBER, dict, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('添加成功');
        Channel().invoke(refresh_group_info, {});
        if (isNative.value) {
          onClose();
          dispose();
          Channel().invoke(Channel_Native_Back, {});
        } else {
          Get.back();
        }
        bool isHave = Get.isRegistered<HomeController>();
        if (isHave) {
          HomeController homeController = Get.find();
          homeController.getAllGroup();
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  tapSureButtonWithForward() {
    if (selectGroupList.isEmpty) {
      toast('未选择成员');
      return;
    }
    if (forwardType == 2) {
      showMergeForwardDialog();
    } else {
      sureForward('');
    }
  }

  //普通转发 逐条转发
  sureForward(String text) async {
    List forwardList = [];
    for (var i = 0; i < selectGroupList.length; i++) {
      MemberModel forwardModel = selectGroupList[i];
      forwardList.add(forwardModel.toJson());
    }
    var transParam = {
      'list': forwardList,
      'forwardType': forwardType,
      'fromSessionId': fromSessionId,
      'text': text //合并转发 留言
    };
    logger('transParam = ${transParam}');
    // Channel().invoke(Channel_Native_IM_Forward, );
    Get.back(result: transParam);
    // goBack();
  }

  //显示合并转发dialog
  showMergeForwardDialog() {
    MergeForwardDialog().show(selectGroupList, forwardText, (text) {
      sureForward(text);
    }, () {
      var record = objectStore['multiMessages'];
      if(record is MsgUIRecord){
        RouteHelper.routePath(Routes.CHAT_RECORD ,arguments:  {'msgList': record});
      }
    });
  }

  _dealTitleStr() {
    switch (type) {
      case 0:
        titleStr.value = '创建群组';
        break;
      case 1:
        titleStr.value = '添加成员';
        break;
      case 2:
        titleStr.value = '转发消息';
        break;
      default:
    }
  }

  dealselectGroupList(groupList, {bool isChooseEnd = false}) {
    //是否是好友、组织人员选择结束
    selectGroupList = groupList;
    if (isChooseEnd) {
      if (type == 0) {
        createGroup();
      }
      if (type == 1) {
        inviteMemberJoinGroup();
      }
    }
    chooseList.clear();
    for (var i = 0; i < selectGroupList.length; i++) {
      MemberModel model = selectGroupList[i];
      if (model.chooseState != 2 && type != 2) {
        chooseList.add(model);
      }
    }
    update();
  }

  String backTilte() {
    if (type == 2) {
      return '确定';
    } else {
      return '确定(${chooseList.length})';
    }
  }

  Color backTitleColor() {
    if (type == 2) {
      return ColorConfig.mainTextColor;
    } else {
      if (chooseList.isEmpty) {
        return ColorConfig.desTextColor;
      } else {
        return ColorConfig.themeCorlor;
      }
    }
  }

  didClickSureBtn() {}
}
