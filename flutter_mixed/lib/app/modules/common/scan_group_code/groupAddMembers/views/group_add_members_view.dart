import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/ios_native_route_focus.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/addMembes/group_add_members.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';

import '../../../../../common/channel/channel.dart';
import '../../../../../common/config/config.dart';
import '../controllers/group_add_members_controller.dart';

class GroupAddMembersView extends GetView<GroupAddMembersController> {
  GroupAddMembersView({Key? key}) : super(key: key);
  GroupAddMembersController groupAddMembersController = Get.find();
  @override
  Widget build(BuildContext context) {
    return IosNativeRouteFixGesture.builder(
      controller: groupAddMembersController,
      childBuilder: () {
        return GetBuilder(
          global: false,
          init: groupAddMembersController,
          builder: (logic) {
            return Scaffold(
              backgroundColor: ColorConfig.backgroundColor,
              appBar: TitleBar().backAppbar(
                  context,
                  groupAddMembersController.titleStr.value,
                  false,
                  groupAddMembersController.currentIndex.value == 0
                      ? [
                          Container(
                            padding: EdgeInsets.only(right: 16),
                            child: CupertinoButton(
                                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                pressedOpacity: 0.5,
                                child: Text(
                                  groupAddMembersController.backTilte(),
                                  style: TextStyle(
                                      color: groupAddMembersController.backTitleColor(),
                                      fontSize: 14),
                                ),
                                onPressed: () {
                                  if (groupAddMembersController.type == 0) {
                                    groupAddMembersController.createGroup();
                                  }
                                  if (groupAddMembersController.type == 1) {
                                    groupAddMembersController
                                        .inviteMemberJoinGroup();
                                  }
                                  if (groupAddMembersController.type == 2) {
                                    groupAddMembersController
                                        .tapSureButtonWithForward();
                                  }
                                }),
                          )
                        ]
                      : [], onPressed: () {
                if (groupAddMembersController.isNative.value) {
                  groupAddMembersController.onClose();
                  Channel().invoke(Channel_Native_Back, {});
                } else {
                  Get.back();
                }
              }),
              body: _buildAddGroupMembersBody(),
            );
          },
        );
      },
    );
  }

  _buildAddGroupMembersBody() {
    if (Platform.isAndroid && !groupAddMembersController.acceptParam) {
      return Center(
        child: CupertinoActivityIndicator(),
      );
    }
    return groupAddMembersController.type != 1
        ? (groupAddMembersController.tabWidgets.isEmpty
            ? Container()
            : groupAddMembersController.tabWidgets.first)
        : Container(
            color: ColorConfig.whiteColor,
            child: DefaultTabController(
                initialIndex: groupAddMembersController.currentIndex.value,
                length: 2,
                child: Builder(builder: (context) {
                  groupAddMembersController.tabController =
                      DefaultTabController.of(context);

                  groupAddMembersController.tabController!.addListener(() {
                    groupAddMembersController.currentIndex.value =
                        groupAddMembersController.tabController!.index;
                    groupAddMembersController.update();
                  });
                  return Column(
                    children: [
                      Container(
                          color: ColorConfig.whiteColor,
                          height: 48,
                          padding: const EdgeInsets.only(left: 15, right: 15),
                          child: TabBar(
                            onTap: (value) {},
                            indicatorPadding: EdgeInsets.symmetric(
                                horizontal:
                                    ((DeviceUtils().width.value - 30) * 0.5 -
                                            50) *
                                        0.5),
                            indicatorColor: ColorConfig.themeCorlor,
                            indicatorWeight: 1,
                            labelPadding: const EdgeInsets.all(0),
                            tabAlignment: TabAlignment.start,
                            dividerHeight: 0,
                            tabs: [
                              Container(
                                width: (DeviceUtils().width.value - 30) * 0.5,
                                alignment: Alignment.center,
                                child: Text(
                                  '添加群成员',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: groupAddMembersController
                                                  .currentIndex.value ==
                                              0
                                          ? ColorConfig.themeCorlor
                                          : ColorConfig.mainTextColor),
                                ),
                              ),
                              Container(
                                width: (DeviceUtils().width.value - 30) * 0.5,
                                alignment: Alignment.center,
                                child: Text(
                                  '群二维码',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: groupAddMembersController
                                                  .currentIndex.value ==
                                              1
                                          ? ColorConfig.themeCorlor
                                          : ColorConfig.mainTextColor),
                                ),
                              )
                            ],
                            isScrollable: true,
                          )),
                      Expanded(
                          child: SizedBox(
                              width: double.infinity,
                              child: TabBarView(
                                  children:
                                      groupAddMembersController.tabWidgets))),
                      //
                    ],
                  );
                })),
          );
  }
}
