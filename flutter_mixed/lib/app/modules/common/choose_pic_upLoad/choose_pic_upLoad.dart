import 'dart:async';
import 'dart:io';

import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_photo_editor/flutter_photo_editor.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../common/base_info/info.dart';
import '../../../common/channel/channel.dart';
import '../../../permission/permission_util.dart';
import '../../../utils/cos_helper.dart';
import '../../workStand/models/filemodel.dart';

class ChooseFileUpLoad {
  //单个上传
  Completer? completer;
  String upLoadPath = '';


  // 解耦， 单纯拍照
  static Future<File?> pickCameraOnly() async {
    var r = await PermissionUtil.checkCameraPermission(Get.context!,
        tip: cameraPhotoPermissionTip);
    if (!r) return null;
    File? file;
    if (Platform.isIOS) {
      //iOS使用插件会出现相机功能一直启用，右上角绿点一直展示的问题
      var result = await Channel()
          .invokeMap(Channel_call_iOS_camera, {'allowsEditing': 1});

      String filePath = result!['filePath'];
      file = File(filePath);
    } else {
      final AssetEntity? entity =
      await CameraPicker.pickFromCamera(Get.context!);

      file = await entity!.file;
    }
    return file;
  }

  // 解耦， 单纯选择图片
  static Future<File?> pickGalleryOnly() async {
    var r = await PermissionUtil.checkGalleryPermission(Get.context!,
        tip: takePhotoPermissionTip);
    if (!r) return null;

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: const AssetPickerConfig(
            maxAssets: 1, requestType: RequestType.image));

    if (assets == null) return null;
    if (assets.isEmpty) return null;

    var entity = assets.first.file;
    File? file = await entity;
    return file;
  }


  Future uploadImage(String localPath,{bool isNeedEdit = true}) async {
    var file = File(localPath);
    File tempFile = await CosHelper.move2Img(file);
    if (isNeedEdit) {
      var cropResult = await FlutterPhotoEditor().editImage(tempFile.path);
      if(!cropResult) return;
    }

    file = tempFile;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    print('$fileModel');

    await startDealFile(fileModel);
    return await startUploadFileList(fileModel);
  }

  tackPhotoWithCamera(String path) async {
    upLoadPath = path;
    var r = await PermissionUtil.checkCameraPermission(Get.context!,
        tip: cameraPhotoPermissionTip);
    if (!r) return;
    File? file;
    if (Platform.isIOS) {
      //iOS使用插件会出现相机功能一直启用，右上角绿点一直展示的问题
      var result = await Channel()
          .invokeMap(Channel_call_iOS_camera, {'allowsEditing': 1});

      String filePath = result!['filePath'];
      file = File(filePath);
    } else {
      final AssetEntity? entity =
          await CameraPicker.pickFromCamera(Get.context!);

      file = await entity!.file;
    }
    if (file == null) return;
    File tempFile = await CosHelper.move2Img(file);
    var editResult = await FlutterPhotoEditor().editImage(tempFile.path);
    if(!editResult) return;
    file = tempFile;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file!.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    await startDealFile(fileModel);
    return await startUploadFileList(fileModel);
  }

  //从相册中选取

  tackPhotoFromPic(String path) async {
    upLoadPath = path;
    var r = await PermissionUtil.checkGalleryPermission(Get.context!,
        tip: takePhotoPermissionTip);
    if (!r) return;

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: const AssetPickerConfig(
            maxAssets: 1, requestType: RequestType.image));

    if (assets == null) return;
    if (assets.isEmpty) return;

    var entity = assets.first.file;
    File? file = await entity;
    if (file == null) return;
    if (file.path == null) return;

    File tempFile = await CosHelper.move2Img(file);
    var editResult = await FlutterPhotoEditor().editImage(tempFile.path);
    if(!editResult) {
      logger('编辑图片 返回为空');
      return;
    };

    file = tempFile;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    print('上传头像： $fileModel');

    await startDealFile(fileModel);
    return await startUploadFileList(fileModel);
  }

  Future startDealFile(FileModel fileModel) async {
    completer = Completer<String>();
    await saveFile(fileModel).then((value) {
      fileModel.savePath = value;
      fileModel.hash = BaseInfo().generateMD5(value);
      if (completer!.isCompleted == false) {
        completer!.complete('');
      }
    });
    return completer!.future;
  }

  //保存图片
  Future<String> saveFile(FileModel fileModel) async {
    File file = fileModel.file!;
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;
    String filePath = '$docment/approve/images/$fileName';
    Directory directory = Directory('$docment/approve/images');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  Future startUploadFileList(FileModel fileModel) async {
    Get.loading(dismissible: true);
    await CosManager().initPans();
    String? bucket = await CosManager().userBucket();
    var result =
          await CosUploadHelper.nativeUpload(fileModel.savePath,'$upLoadPath/${fileModel.fileName}' , bucket);
    Get.dismiss();
    return result;
  }
}
