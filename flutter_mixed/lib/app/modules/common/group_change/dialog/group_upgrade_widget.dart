


int calculateLength(String input) {
  int length = 0;
  for (int i = 0; i < input.length; i++) {
    int codeUnit = input.codeUnitAt(i);
    if (isFullWidth(codeUnit)) {
      length += 2;
    } else {
      length++;
    }
  }
  return length;
}
bool isFullWidth(int codeUnit) {
  // 更精确的全角字符判断范围
  return (codeUnit >= 0xFF01 && codeUnit <= 0xFF5E) ||
      (codeUnit >= 0x2010 && codeUnit <= 0x2027) ||
      (codeUnit >= 0x3000 && codeUnit <= 0x303F) ||
      (codeUnit >= 0x4E00 && codeUnit <= 0x9FFF);
}

