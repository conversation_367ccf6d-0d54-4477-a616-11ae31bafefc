import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/retrofit/entity/group/group_info.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/config/config.dart';
import '../../../../common/widgets/image_loader.dart';
import '../../../../retrofit/datasource/workbench_datasource.dart';
import '../../../../utils/storage.dart';
import '../../choose_pic_upLoad/choose_pic_upLoad.dart';

class GroupChangeDialog {
  RxString groupLogo = ''.obs;
  RxString groupName = ''.obs;
  String groupId = '';
  bool isSave = false;//是否修改了头像
  TextEditingController msgController = TextEditingController();
  show(String id, Function onPressed, {String logo = '', String name = ''}) {
    groupLogo.value = logo;
    groupName.value = name;
    groupId = id;
    msgController.text = name;
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(() => Scaffold(
              backgroundColor: Colors.transparent,
              body: Stack(
                children: [
                  Container(
                    padding: EdgeInsets.only(top: 44 + DeviceUtils().top.value),
                    color: ColorConfig.maskColor,
                    child: Container(
                      decoration: const BoxDecoration(
                          color: ColorConfig.whiteColor,
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10))),
                      child: Column(
                        children: [
                          10.gap,
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            height: 42,
                            child: Row(
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.back();
                                  },
                                  child: Container(
                                    width: 54,
                                    alignment: Alignment.centerLeft,
                                    child: const Text(
                                      '取消',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.mainTextColor),
                                    ),
                                  ),
                                ),
                                Expanded(
                                    child: Container(
                                        alignment: Alignment.center,
                                        child: Text(
                                          _backTitle(),
                                          style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                              color: ColorConfig.mainTextColor),
                                        ))),
                                InkWell(
                                  onTap: () async {
                                    if (logo.isNotEmpty) {
                                      String result = await _tapSaveAvatar();
                                      if (result.isNotEmpty) {
                                        onPressed(result);
                                        Get.back();
                                      }
                                    }
                                    if (name.isNotEmpty) {
                                      String result = await _tapSaveGroupName();
                                      if (result.isNotEmpty) {
                                        onPressed(result);
                                        Get.back();
                                      }
                                    }
                                  },
                                  child: Container(
                                    width: 54,
                                    alignment: Alignment.centerRight,
                                    child: const Text(
                                      '保存',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.themeCorlor),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                          16.gap,
                          if (groupLogo.value.isNotEmpty) ...[
                            //修改头像
                            Container(
                              alignment: Alignment.center,
                              child: InkWell(
                                onTap: () {
                                  _tapAvatar(Get.context);
                                },
                                child: Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.lineColor)),
                                  child: Stack(
                                    clipBehavior: Clip.none,
                                    children: [
                                      ImageLoader(
                                        width: 80,
                                        height: 80,
                                        url: groupLogo.value,
                                        radius: 16,
                                      ),
                                      Positioned(
                                          bottom: 0,
                                          right: -8,
                                          width: 24,
                                          height: 24,
                                          child: Container(
                                            alignment: Alignment.center,
                                            width: 24,
                                            height: 24,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                                border: Border.all(
                                                    width: 0.5,
                                                    color:
                                                        ColorConfig.lineColor),
                                                color: ColorConfig.whiteColor),
                                            child: Container(
                                              alignment: Alignment.center,
                                              width: 21,
                                              height: 21,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  color:
                                                      ColorConfig.themeCorlor),
                                              child: const ImageLoader(
                                                width: 10.5,
                                                height: 9.45,
                                                url:
                                                    'assets/images/3.0x/group_change_avatar.png',
                                              ),
                                            ),
                                          ))
                                    ],
                                  ),
                                ),
                              ),
                            )
                          ],
                          if (groupName.value.isNotEmpty) ...[
                            //修改群名称
                            Container(
                              height: 100,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                        width: 1,
                                        color: ColorConfig.lineColor)),
                                child: TextField(
                                  onSubmitted: (value) {},
                                  onChanged: (value) {},
                                  controller: msgController,
                                  maxLines: null,
                                  minLines: 1,
                                  maxLength: 30,
                                  textInputAction: TextInputAction.done,
                                  style: const TextStyle(
                                    color: ColorConfig.mainTextColor,
                                    fontSize: 14,
                                  ),
                                  decoration: const InputDecoration(
                                      contentPadding:
                                          EdgeInsets.only(top: 0, bottom: 0),
                                      border: OutlineInputBorder(
                                          borderSide: BorderSide.none),
                                      hintText: '请输入群名称,最多30字',
                                      hintStyle: TextStyle(
                                        color: ColorConfig.desTextColor,
                                        fontSize: 14,
                                      )),
                                ),
                              ),
                            )
                          ]
                        ],
                      ),
                    ),
                  )
                ],
              ),
            )));
  }

  //返回标题
  _backTitle() {
    String titleStr = '';
    if (groupLogo.value.isNotEmpty) {
      titleStr = '修改头像';
    }
    if (groupName.value.isNotEmpty) {
      titleStr = '修改群名称';
    }
    return titleStr;
  }

  //点击了头像
  _tapAvatar(context) {
    SettingWidget().showCupertinoActionSheetForPage(
        context, ['拍照', '从手机相册选择', '随机系统头像'], (value) async {
      if (value == 0) {
        var result = await ChooseFileUpLoad().tackPhotoWithCamera('/IM/GROUP');
        _dealUpLoadData(result);

      }
      if (value == 1) {
        var result = await ChooseFileUpLoad().tackPhotoFromPic('/IM/GROUP');
        _dealUpLoadData(result);
      }
      if (value == 2) {
        var random = Random();
        int randomNumber = random.nextInt(8) + 1;
        Map preMap = await UserDefault.getData(Define.COSPREKEY);
        String preStr = preMap['pre'];
        groupLogo.value = '$preStr/IM/GROUP/group_icon_new$randomNumber.png';
        isSave = true;
      }
    });
  }

  _dealUpLoadData(result) async {
    if (result != null) {
      //上传成功
      groupLogo.value = result;
      isSave = true;
      logger('-----上传成功--${groupLogo.value}');
    } else {
      logger('-----上传失败--$result');
      toast('图片上传失败');
    }
  }

  //点击了保存头像
  _tapSaveAvatar() async {
    if (!isSave) {
      return groupLogo.value;
    }
    try {
      var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var body = ChangeGroupNameReq(groupId, groupAvatar: groupLogo.value);
      var resp = await workDatasource.changeGroupName(body);
      if (resp.success()) {
        return groupLogo.value;
      } else {
        toast(resp.msg);
        return '';
      }
    } catch (e) {
      toast(LoginApi.ERROR_MSG);
      return '';
    }
  }

  //点击了保存群名称
  _tapSaveGroupName() async {
    if (msgController.text.isEmpty) {
      toast('请输入群名称');
      return;
    }
    if (msgController.text == groupName.value) {
      return groupName.value;
    }
    try {
      var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var body = ChangeGroupNameReq(groupId, name: msgController.text);
      var resp = await workDatasource.changeGroupName(body);
      if (resp.success()) {
        return msgController.text;
      } else {
        toast(resp.msg);
        return '';
      }
    } catch (e) {
      toast(LoginApi.ERROR_MSG);
      return '';
    }
  }
}
