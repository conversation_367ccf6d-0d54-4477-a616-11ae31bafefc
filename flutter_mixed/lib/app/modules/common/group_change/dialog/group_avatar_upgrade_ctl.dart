import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

class GroupAvatarUpgradeController extends GetxController {
  String avatarBgColor = '';

  String? inputGroupText;

  double inputTxtSize = 22.0;

  final AUTOFILLINGSPACE = " ";

  String groupName = '';

  String logoText = '';

  // 主要用于模式切换，后触发的操作优先级高
  bool isCustomAvatar = false;

  final ballShapeColors = [
    '0xffE11818',
    '0xffFF8686',
    '0xffFFD035',
    '0xff46D5B3',
    '0xff37E3F0',
    '0xff2C67EC',
    '0xff5D5FEF',
    '0xffC95DEF'
  ];

  final ballItems = <BallItem>[];

  ChinaTextEditController inputController = ChinaTextEditController();

  @override
  void onInit() {
    super.onInit();
    inputController.groupAvatarController = this;
    ballItems.addAll(ballShapeColors.map((e) => BallItem(e, false)));
    inputController.addListener(() {
      if (logoText != inputController.completeText) {
        logoText = inputController.completeText;
        upgradeCustomText(inputController.completeText);
      }
    });
  }

  bool showCustomAvatar() {
    return isCustomAvatar;
  }

  //匹配返回的色值
  dealColour(String color) {
    avatarBgColor = ColorUtil.backColorString(color);
    for (var i = 0; i < ballItems.length; i++) {
      BallItem item = ballItems[i];
      if (item.color == avatarBgColor) {
        item.selected = true;
      } else {
        item.selected = false;
      }
    }
  }

  upgradeCustomText(String inputTxt) {
    isCustomAvatar = true;
    if (avatarBgColor.isEmpty) {
      ballItems.firstOrNull?.selected = true;
      fillCustomAvatarBgColor();
    }
    testPrint();

    switch (inputTxt.length) {
      case 1:
        inputTxtSize = 30;
        break;
      case 2:
        inputTxtSize = 26;

        break;
      case 3:
        inputTxtSize = 22;

        break;
      default:
        inputTxtSize = 20;
        break;
    }
    var secondLength = calculateLength(inputTxt);
    
    if (secondLength > 8) {
      inputController.text = createMaxPre4String(inputTxt, maxLength: 8);
      inputController.completeText = createMaxPre4String(inputTxt, maxLength: 8);
    }else{
      inputController.text = inputTxt;
    }
    inputController.value = TextEditingValue(
        text: inputController.completeText,
        selection: TextSelection.fromPosition(TextPosition(
            affinity: TextAffinity.downstream, offset: inputController.completeText.length)));
    update();
  }

  getLogoText() {
    if (inputController.completeText.isNotEmpty) {
      return inputController.completeText;
    }
    return createMaxPre4String(groupName, maxLength: 8);
  }

  // 自动补全需要补充空格的情况 ,例如（yy汉 -> yy 汉)
  autoFillingUpChar(String txt) {
    String showText = '';
    for (int i = 0; i < txt.characters.length; i++) {
      var t = '${txt.characters.characterAt(i)}';
      if (i == 1) {
        //汉汉
        if (txt.characters.length == 2) {
          if (StringUtil.isFullWidth('${txt.characters.characterAt(0)}') &&
              StringUtil.isFullWidth('${txt.characters.characterAt(1)}')) {
            t = '$AUTOFILLINGSPACE$t';
          }
        }
        if (txt.characters.length == 3) {
          //汉UI
          if (StringUtil.isFullWidth('${txt.characters.characterAt(0)}') &&
              !StringUtil.isFullWidth('${txt.characters.characterAt(1)}') &&
              !StringUtil.isFullWidth('${txt.characters.characterAt(2)}')) {
            t = '$AUTOFILLINGSPACE$t';
          }
        }
      }
      if (i == 2) {
        if (txt.characters.length == 3) {
          //UI汉
          if (!StringUtil.isFullWidth('${txt.characters.characterAt(0)}') &&
              !StringUtil.isFullWidth('${txt.characters.characterAt(1)}') &&
              StringUtil.isFullWidth('${txt.characters.characterAt(2)}')) {
            t = '$AUTOFILLINGSPACE$t';
          }
        }
      }
      showText = '$showText$t';
    }
    return showText;
  }

  createFirstLine() {
    var txt = inputController.completeText;
    if (txt.isEmpty) {
      txt = groupName;
    }
    var totalCharLength = calculateLength(txt);
    String resultStr = txt;
    if (totalCharLength > 4) {
      resultStr = createMaxPre4String(txt);
    }
    return autoFillingUpChar(resultStr);
  }

  createSecondLine() {
    var txt = inputController.completeText;
    if (txt.isEmpty) {
      txt = groupName;
    }
    var totalCharLength = calculateLength(txt);
    if (totalCharLength <= 4) return '';
    var charLength = 0;
    var firstIndex = 0;
    var firstCharLength = 0;
    for (int i = 0; i < txt.characters.length; i++) {
      var t = '${txt.characters.characterAt(i)}';
      if (StringUtil.isFullWidth(t)) {
        charLength += 2;
      } else {
        charLength++;
      }

      if (charLength > 4) {
        firstIndex = i;
        break;
      } else {
        firstCharLength = charLength;
      }
    }

    var second =
        '${txt.characters.getRange(firstIndex, txt.characters.length)}';
    String resultStr = second;
    var secondLength = calculateLength(second);
    if (secondLength > 8 - firstCharLength) {
      resultStr = createMaxPre4String(second, maxLength: 8 - firstCharLength);
    }
    return autoFillingUpChar(resultStr);
  }

  createMaxPre4String(String content, {int maxLength = 4}) {
    var txt = content;
    var charLength = 0;
    var firstIndex = 0;
    for (int i = 0; i < txt.characters.length; i++) {
      var t = '${txt.characters.characterAt(i)}';
      if (StringUtil.isFullWidth(t)) {
        charLength += 2;
      } else {
        charLength++;
      }
      if (charLength > maxLength) {
        firstIndex = i;
        break;
      }
    }
    return '${txt.characters.getRange(0, firstIndex)}';
  }

  testPrint() {
    var txt = inputController.completeText;
    for (int i = 0; i < txt.characters.length; i++) {
      var t = '${txt.characters.characterAt(i)}';
      if (StringUtil.isFullWidth(t)) {
        logger("$t 是全角字符");
      } else {
        logger("$t 不是全角字符");
      }
    }
  }

  int calculateLength(String input) {
    var txt = input;
    var charLength = 0;
    for (int i = 0; i < txt.characters.length; i++) {
      var t = '${txt.characters.characterAt(i)}';
      if (StringUtil.isFullWidth(t)) {
        charLength += 2;
      } else {
        charLength++;
      }
    }
    return charLength;
  }

  fillCustomAvatarBgColor() {
    var item = ballItems.where((element) => element.selected).firstOrNull;
    if (item == null) return;
    avatarBgColor = item.color;
  }

  checkItem(BallItem item) {
    isCustomAvatar = true;
    item.selected = true;
    for (var e in ballItems) {
      if (e.color != item.color) {
        e.selected = false;
      }
    }
    fillCustomAvatarBgColor();

    update();
  }

  @override
  void onClose() {
    super.onClose();
    inputController
      ..clear()
      ..dispose();
  }
}

class BallItem {
  String color;
  bool selected = false;

  BallItem(this.color, this.selected);
}
