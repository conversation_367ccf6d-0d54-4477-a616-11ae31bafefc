import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/common/group_change/dialog/group_avatar_upgrade_ctl.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/config/config.dart';
import '../../../../common/widgets/image_loader.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../im/utils/file_util.dart';
import '../../../../retrofit/datasource/workbench_datasource.dart';
import '../../../../retrofit/entity/group/group_info.dart';
import '../../../../../logger/logger.dart';
import '../../../../utils/storage.dart';
import '../../choose_pic_upLoad/choose_pic_upLoad.dart';
import 'group_upgrade_widget.dart';

/// 更新群头像组件 （群名称和群头像修改 分离）
class GroupAvatarUpgradeDialog extends StatelessWidget {
  String groupId;
  String groupLogo;
  String groupName;
  String colour = '';//自定义头像颜色
  String logoText = '';//自定义头像文字
  GroupAvatarUpgradeController? groupAvatarUpgradeController;
  Function saveResult;
  final GlobalKey shotKey = GlobalKey();

  bool isSave = false; //是否修改了头像

  TextEditingController msgController = TextEditingController();

  GroupAvatarUpgradeDialog(this.groupId, this.groupLogo,this.groupName,this.colour,this.logoText,this.groupAvatarUpgradeController,this.saveResult);

  var c = Get.lazyPut(() => GroupAvatarUpgradeController());
  
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      global: false,
      init: groupAvatarUpgradeController,
      builder: (_) {
      return Container(
        child: Container(
          padding: EdgeInsets.only(top: 44 + DeviceUtils().top.value),
          color: ColorConfig.maskColor,
          child: Container(
            decoration: const BoxDecoration(
                color: ColorConfig.whiteColor,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10))),
            child: Column(
              children: [
                10.gap,
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  height: 42,
                  child: Row(
                    children: [
                      InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          width: 54,
                          alignment: Alignment.centerLeft,
                          child: const Text(
                            '取消',
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.mainTextColor),
                          ),
                        ),
                      ),
                      Expanded(
                          child: Container(
                              alignment: Alignment.center,
                              child: Text(
                                _backTitle(),
                                style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: ColorConfig.mainTextColor),
                              ))),
                      InkWell(
                        onTap: () async {
                          if (groupLogo.isNotEmpty) {
                            String result = await _saveClick();
                            if (result.isNotEmpty) {
                              saveResult({'logo':result,'logoText':logoText,'colour':colour});
                              Get.back();
                            }
                          }
                        },
                        child: Container(
                          width: 54,
                          alignment: Alignment.centerRight,
                          child: const Text(
                            '保存',
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.themeCorlor),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                16.gap,
                if (groupLogo.isNotEmpty) ...[
                  //修改头像
                  Container(
                    alignment: Alignment.center,
                    child: InkWell(
                      onTap: () {
                        _tapAvatar(Get.context);
                      },
                      child: _buildCustomAvatar(),
                    ),
                  )
                ],

                16.gap,
                Container(
                  margin: EdgeInsets.only(left: 16),
                  alignment: Alignment.topLeft,
                  child: const Text('自定义头像文字',
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: ColorConfig.mainTextColor)),
                ),

                8.gap,
                Container(
                  height: 35,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.center,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border:
                            Border.all(width: 1, color: ColorConfig.lineColor)),
                    child: TextField(
                      onSubmitted: (value) {},
                      controller: groupAvatarUpgradeController!.inputController,
                      maxLines: null,
                      minLines: 1,
                      textInputAction: TextInputAction.done,
                      style: const TextStyle(
                        color: ColorConfig.mainTextColor,
                        fontSize: 14,
                      ),
                      decoration: const InputDecoration(
                          border: InputBorder.none,
                          isDense: true,
                          counterText: '',
                          hintStyle: TextStyle(
                            color: ColorConfig.desTextColor,
                            fontSize: 14,
                          )),
                    ),
                  ),
                ),

                36.gap,
                Container(
                  margin: const EdgeInsets.only(left: 16),
                  alignment: Alignment.topLeft,
                  child: const Text('自定义颜色',
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: ColorConfig.mainTextColor)),
                ),
                16.gap,

                /// 调色板布局
                GridView.builder(
                    padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 8,
                            childAspectRatio: 1.0,
                            crossAxisSpacing: 8),
                    itemCount: groupAvatarUpgradeController!.ballItems.length,
                    itemBuilder: (ctx, index) {
                      var item = groupAvatarUpgradeController!.ballItems[index];

                      return InkWell(
                        onTap: () => groupAvatarUpgradeController!.checkItem(item),
                        child: Container(
                          width: 22,
                          height: 22,
                          child: Stack(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: Color(int.parse(item.color)),
                                  borderRadius: BorderRadius.circular(32),
                                ),
                              ),
                              if (item.selected) ...[
                                Container(
                                  alignment: Alignment.center,
                                  child: Image.asset(
                                      AssetsRes.GROUP_AVATAR_COLOR_CHECK,
                                      width: 14,
                                      height: 10),
                                )
                              ]
                            ],
                          ),
                        ),
                      );
                    }),

                16.gap,
                Container(
                  margin: EdgeInsets.only(left: 16),
                  alignment: Alignment.topLeft,
                  child: const Text('如果仅选择颜色,将使用该颜色默认群名称作为头像',
                      style: TextStyle(
                          fontSize: 12, color: ColorConfig.desTextColor)),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  // 自定义头像： 上层自选图片/随机头像 ； 下层： 自定义底色/文字
  Widget _buildCustomAvatar() {
    return Container(
      width: 80,
      height: 80,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            child: RepaintBoundary(
              key: shotKey,
              child: Stack(
                children: [
                  if (!groupAvatarUpgradeController!.showCustomAvatar()) ...[
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                              width: 1, color: ColorConfig.lineColor)),
                      child: ImageLoader(
                        width: 80,
                        height: 80,
                        url: groupLogo,
                        radius: 16,
                      ),
                    ),
                  ],
                  if (groupAvatarUpgradeController!.showCustomAvatar()) ...[
                    Container(
                      alignment: Alignment.center,
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: _buildCustomColor(),
                      ),
                      child: Container(
                        padding: EdgeInsets.all(5),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              groupAvatarUpgradeController!.createFirstLine(),
                              maxLines: 1,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: groupAvatarUpgradeController!.inputTxtSize,
                                  fontWeight: FontWeight.bold),
                            ),
                            if (!StringUtil.isEmpty(
                                groupAvatarUpgradeController!.createSecondLine())) ...[
                              Text(
                                groupAvatarUpgradeController!.createSecondLine(),
                                maxLines: 1,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: groupAvatarUpgradeController!.inputTxtSize,
                                    fontWeight: FontWeight.bold),
                              ),
                            ]
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          // camera 小图标
          Positioned(
              bottom: 0,
              right: -8,
              width: 24,
              height: 24,
              child: Container(
                alignment: Alignment.center,
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    border:
                        Border.all(width: 0.5, color: ColorConfig.lineColor),
                    color: ColorConfig.whiteColor),
                child: Container(
                  alignment: Alignment.center,
                  width: 21,
                  height: 21,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: ColorConfig.themeCorlor),
                  child: const ImageLoader(
                    width: 10.5,
                    height: 9.45,
                    url: 'assets/images/3.0x/group_change_avatar.png',
                  ),
                ),
              ))
        ],
      ),
    );
  }

  Color _buildCustomColor() {
    if (groupAvatarUpgradeController!.avatarBgColor.isEmpty) return ColorConfig.lineColor;
    return Color(int.parse(groupAvatarUpgradeController!.avatarBgColor));
  }

  //返回标题
  _backTitle() {
    String titleStr = '';
    if (groupLogo.isNotEmpty) {
      titleStr = '修改头像';
    }

    return titleStr;
  }

  //点击了头像
  _tapAvatar(context) {
    SettingWidget().showCupertinoActionSheetForPage(
        context, ['拍照', '从手机相册选择', '随机系统头像'], (value) async {
      if (value == 0) {
        var result = await ChooseFileUpLoad().tackPhotoWithCamera('/IM/GROUP');
        _dealUpLoadData(result);
      }
      if (value == 1) {
        var result = await ChooseFileUpLoad().tackPhotoFromPic('/IM/GROUP');
        _dealUpLoadData(result);
      }
      if (value == 2) {
        var random = Random();
        int randomNumber = random.nextInt(8) + 1;
        Map preMap = await UserDefault.getData(Define.COSPREKEY);
        String preStr = preMap['pre'];
        groupLogo = '$preStr/IM/GROUP/group_icon_new$randomNumber.png';
        groupAvatarUpgradeController!.isCustomAvatar = false;
        isSave = true;
        groupAvatarUpgradeController!.update();
      }
    });
  }

  _dealUpLoadData(result,{bool isCustom = false}) async {
    if (result != null) {
      //上传成功
      groupLogo = result;
      isSave = true;
      if (!isCustom) {
        groupAvatarUpgradeController!.isCustomAvatar = false;
      }
      
      groupAvatarUpgradeController!.update();
      logger('-----上传成功--${groupLogo}');
    } else {
      logger('-----上传失败--$result');
      // toast('图片上传失败');
    }
  }

  // 处理自定义头像或者api获取头像
  _saveClick() async {
    if (groupAvatarUpgradeController!.isCustomAvatar) {
      var filePath = await FileUtil.captureImageByKey(shotKey);
      var data = await ChooseFileUpLoad().uploadImage(filePath,isNeedEdit: false);
      await _dealUpLoadData(data,isCustom: true);
      return await _tapSaveAvatar();
    } else {
      return await _tapSaveAvatar();
    }
  }

  //点击了保存头像
  _tapSaveAvatar() async {
    if (!isSave) {
      return groupLogo;
    }
    try {
      var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      colour = '';
      logoText = '';
      if (groupAvatarUpgradeController!.isCustomAvatar) {
        colour = ColorUtil.backJingColor(groupAvatarUpgradeController!.avatarBgColor);
        logoText = groupAvatarUpgradeController!.getLogoText();
      }
      var body = ChangeGroupNameReq(groupId, groupAvatar: groupLogo,colour:colour,logoText: logoText);
      logger("boddddddddd => ${body.toJson()}");
      var resp = await workDatasource.changeGroupName(body);
      if (resp.success()) {
        return groupLogo;
      } else {
        toast(resp.msg);
        return '';
      }
    } catch (e) {
      toast(LoginApi.ERROR_MSG);
      return '';
    }
  }
}
