import 'package:floor/floor.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/ios_native_route_focus.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/common/group_change/dialog/group_avatar_upgrade_dialog.dart';
import 'package:flutter_mixed/app/modules/common/group_change/dialog/group_change_dialog.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';

import '../../../../../common/channel/channel.dart';
import '../../dialog/group_avatar_upgrade_ctl.dart';
import '../controllers/group_change_info_controller.dart';

class GroupChangeInfoView extends GetView<GroupChangeInfoController> {
  GroupChangeInfoView({Key? key}) : super(key: key);
  GroupChangeInfoController groupChangeInfoController = Get.find();
  @override
  Widget build(BuildContext context) {
    return IosNativeRouteFixGesture.builder(
        controller: groupChangeInfoController,
        childBuilder: () {
          return GetBuilder(
              global: false,
              init: groupChangeInfoController,
              builder: (logic) {
                return Scaffold(
                  backgroundColor: ColorConfig.backgroundColor,
                  appBar: TitleBar().backAppbar(context, '群信息', false, [],
                      onPressed: () {
                    if (groupChangeInfoController.isNative.value) {
                      groupChangeInfoController.onClose();
                      Channel().invoke(Channel_Native_Back, {});
                    } else {
                      Get.back();
                    }
                  }),
                  body: Container(
                    color: ColorConfig.backgroundColor,
                    padding:
                        const EdgeInsets.only(left: 16, right: 16, top: 16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                          color: ColorConfig.whiteColor,
                          borderRadius: BorderRadius.circular(6)),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          InkWell(
                            onTap: () {
                              if (groupChangeInfoController.groupLogo.isEmpty)
                                return;
                              GroupAvatarUpgradeController
                                  groupAvatarUpgradeController = Get.find();
                              groupAvatarUpgradeController.groupName =
                                  groupChangeInfoController.groupName.value;

                              if (groupChangeInfoController.colour.isNotEmpty) {
                                groupAvatarUpgradeController.dealColour(
                                    groupChangeInfoController.colour);
                                groupAvatarUpgradeController.inputController
                                    .text = groupChangeInfoController.logoText;
                              }
                              Get.dialog(
                                GroupAvatarUpgradeDialog(
                                    groupChangeInfoController.groupId,
                                    groupChangeInfoController.groupLogo.value,
                                    groupChangeInfoController.groupName.value,
                                    groupChangeInfoController.colour,
                                    groupChangeInfoController.logoText,
                                    groupAvatarUpgradeController, (argument) {
                                  groupChangeInfoController.groupLogo.value =
                                      argument['logo'];
                                  groupChangeInfoController.colour =
                                      argument['colour'];
                                  groupChangeInfoController.logoText =
                                      argument['logoText'];
                                  groupChangeInfoController.update();
                                }),
                                barrierDismissible: false,
                                useSafeArea: false,
                              );

                              // GroupChangeDialog().show(
                              //     groupChangeInfoController.groupId, (result) {
                              //   groupChangeInfoController.groupLogo.value = result;
                              //   groupChangeInfoController.update();
                              // }, logo: groupChangeInfoController
                              //         .groupLogo.value);
                            },
                            child: Container(
                              height: 56,
                              child: Row(
                                children: [
                                  const Text(
                                    '群头像',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                  Expanded(
                                      child: Container(
                                    alignment: Alignment.centerRight,
                                    child: Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                              width: 1,
                                              color: ColorConfig.lineColor)),
                                      child: ImageLoader(
                                        width: 40,
                                        height: 40,
                                        url: groupChangeInfoController
                                            .groupLogo.value,
                                        radius: 8,
                                      ),
                                    ),
                                  )),
                                  8.gap,
                                  const ImageLoader(
                                    width: 9,
                                    height: 17,
                                    url: 'assets/images/3.0x/mine_right.png',
                                    radius: 0,
                                  )
                                ],
                              ),
                            ),
                          ),
                          const Divider(
                            height: 1,
                            color: ColorConfig.lineColor,
                          ),
                          InkWell(
                            onTap: () {
                              if (groupChangeInfoController.groupName.isEmpty)
                                return;
                              GroupChangeDialog().show(
                                  groupChangeInfoController.groupId, (result) {
                                groupChangeInfoController.groupName.value =
                                    result;
                                groupChangeInfoController.update();
                              },
                                  name: groupChangeInfoController
                                      .groupName.value);
                            },
                            child: Container(
                              height: 56,
                              child: Row(
                                children: [
                                  const Text(
                                    '群名称',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                  10.gap,
                                  Expanded(
                                      child: Container(
                                    alignment: Alignment.centerRight,
                                    child: Text(
                                      groupChangeInfoController.groupName.value,
                                      style: const TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.desTextColor),
                                    ),
                                  )),
                                  8.gap,
                                  const ImageLoader(
                                    width: 9,
                                    height: 17,
                                    url: 'assets/images/3.0x/mine_right.png',
                                    radius: 0,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              });
        });
  }
}
