import 'dart:async';

import 'package:flutter_mixed/app/common/widgets/ios_native_route_focus.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../routes/app_pages.dart';

import 'package:get/get.dart' as GroupGet;

class GroupChangeInfoController extends GetxController
    with MixNativeController {
  String groupId = '';
  RxString groupName = ''.obs;
  RxString groupLogo = ''.obs;
  String colour = '';
  String logoText = '';
  RxBool isNative = false.obs; //是否是原生跳转
  StreamSubscription? subscription;
  @override
  void onInit() {
    super.onInit();

    subscription =
        GroupGet.Get.getOrEventParams(Routes.GROUP_CHANGE_INFO, arg: (arg) {
      groupId = arg['groupId'];
      groupLogo.value = arg['groupLogo'];
      groupName.value = arg['groupName'];
      if (arg['colour'] != null) {
        colour = arg['colour'];
      }
      if (arg['logoText'] != null) {
        logoText = arg['logoText'];
      }
      logger('$arg');
      update();
    }, isNative: isNative);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }
}
