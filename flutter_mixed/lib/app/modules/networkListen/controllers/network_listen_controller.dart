import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/modules/privacy_policy/privacy_policy/logic.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/net_util.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:get/get.dart';

class NetworkListenController extends GetxController {
  String netStr = 'Unknown';
  List<ConnectivityResult> connectionStatus = [];
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  bool _firstListenNetChanged = false;

  @override
  void onInit() {
    super.onInit();
    logger('NetworkListenController--init');
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }



  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    connectionStatus = result;
    if (connectionStatus.contains(ConnectivityResult.wifi)) {
      netStr = 'WIFI';
    }
    if (connectionStatus.contains(ConnectivityResult.mobile)) {
      netStr = 'MOBILE';
    }
    if (connectionStatus.contains(ConnectivityResult.none)) {
      netStr = 'NONE';
    }
    bool isHavePrivacy = Get.isRegistered<PrivacyPolicyLogic>();
    if (isHavePrivacy && netStr != 'NONE' && netStr != 'Unknown') {
      PrivacyPolicyLogic logic = Get.find();
      logic.createWebViewController();
    }

    NetUtil.netStr = netStr;

    if(!_firstListenNetChanged){
      _firstListenNetChanged = true;
      return;
    }
    logger('网络变化的监听： $netStr');

    //_handleSendingMessages();
    
    if (netStr != 'NONE' && netStr != 'Unknown') {
      try {
        ImClientManager.instance.reConnect();
        HomeController homeController = Get.find();
        homeController.getUserInfo();
        homeController.getSixMergeData();

      } catch (e) {
        
      }
    }
  }

  Future<void> _handleSendingMessages() async {
    try {
      var ownId = await UserHelper.getUid();
      var sendingMessages = await DbHelper.getMySendingMsgList(ownId);
      
      if (sendingMessages.isNotEmpty) {
        logger('网络变化：终止 ${sendingMessages.length} 条发送中的消息');
        for (var message in sendingMessages) {
          message.isSuccess = 0;
          await DbHelper.insertMsg(message);
        }
      }
    } catch (e) {
      logger('处理发送中消息出错: $e');
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    _connectivitySubscription?.cancel();
  }
}


Future<bool> netConnected() async {
  final connectivityResult = await Connectivity().checkConnectivity();
  if (connectivityResult.contains(ConnectivityResult.none)) {
    return false;
  }
  return true;
}