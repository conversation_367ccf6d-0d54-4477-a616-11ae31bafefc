import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/network_listen_controller.dart';

class NetworkListenView extends GetView<NetworkListenController> {
  const NetworkListenView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('NetworkListenView'),
        centerTitle: true,
      ),
      body: const Center(
        child: Text(
          'NetworkListenView is working',
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}
