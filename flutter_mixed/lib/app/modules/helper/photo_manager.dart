

import 'dart:io';
import 'dart:typed_data';

import 'package:photo_manager/photo_manager.dart';

class PhotoHelper {

  static Future<bool> saveToGallery(Uint8List data) async {
    AssetEntity? assetEntity = await PhotoManager.editor.saveImage(data, filename: '${DateTime.now().millisecondsSinceEpoch}.jpg');
    return assetEntity != null;
  }

  static Future<bool> saveImageUrlToGallery(String? filePath) async {

    if(filePath == null) return false;
    var uinit8List = await File(filePath).readAsBytes();

    AssetEntity? assetEntity = await PhotoManager.editor.saveImage(uinit8List ,  filename: '${DateTime.now().millisecondsSinceEpoch}.jpg');
    return assetEntity != null;

  }

  static Future<bool> saveVideoToGallery(String? filePath) async {
    if(filePath == null) return false;
    try{
      var fileName = '${DateTime.now().millisecondsSinceEpoch}.mp4';
      AssetEntity? assetEntity = await PhotoManager.editor.saveVideo(
          File(filePath), title: fileName);
      return assetEntity != null;
    }catch(e){
      return false;
    }
  }

}