

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:fluwx/fluwx.dart';

import '../../common/api/Define.dart';
import '../../common/api/LoginApi.dart';
import '../../common/channel/channel.dart';
import '../../../logger/logger.dart';
import '../../utils/storage.dart';

/// 微信帮助类： 微信登录
class WechatHelper {

  static WechatHelper? _instance;

  WechatHelper._internal();

  static WechatHelper get instance => _instance ??= WechatHelper._internal();

  Fluwx fluwx = Fluwx();

  // 注册 4.0 新api
  Future register() async {
    fluwx.registerApi(appId: WeChat.WXAPPID, universalLink: "https:///pan.ddbes.com/");
    // registerWxApi(appId: WeChat.WXAPPID, universalLink: "https:///pan.ddbes.com/");
  }

  var isLoginPage = false;

  var listener;

  void initAndroidWxListener() {

    listener = (res) {
      if(Platform.isIOS) return;
      if(isLoginPage) return;
      logger('initAndroidWxListener ====$res');
      if (res is WeChatAuthResponse) {
        int errCode = res.errCode!;

        if (errCode == 0) {
          String code = res.code!;
          Channel().invoke(Channel_sendWeChatCode, {
            'wechat_code': code
          });

        } else if (errCode == -4) {
          //用户拒绝授权
        } else if (errCode == -2) {
          //用户取消授权
        }
      }
    };

    // fluwx.su
    fluwx.addSubscriber(listener);
  }

  listenLoginResult({ValueChanged<Map>? callback}) {

    listener = (res) {
      if (res is WeChatAuthResponse) {
        int errCode = res.errCode!;
        logger('微信登录返回值：ErrCode :$errCode  code:${res.code}  ');

        if (errCode == 0) {
          String code = res.code!;
          wxLoginAuth(code , callback: callback);
        } else if (errCode == -4) {
          //用户拒绝授权
          toast('用户拒绝授权');
        } else if (errCode == -2) {
          //用户取消授权
          toast('用户取消授权');
        }
      }
    };

    fluwx.addSubscriber(listener);

    /*wxStream = weChatResponseEventHandler.distinct((a, b) => a == b).listen((res) {
      logger('res123====$res');
      if (res is WeChatAuthResponse) {
        int errCode = res.errCode!;
        logger('微信登录返回值：ErrCode :$errCode  code:${res.code}  ');

        if (errCode == 0) {
          String code = res.code!;
          wxLoginAuth(code , callback: callback);
        } else if (errCode == -4) {
          //用户拒绝授权
          toast('用户拒绝授权');
        } else if (errCode == -2) {
          //用户取消授权
          toast('用户取消授权');
        }
      }
    });*/
  }


  wxLoginAuth(String code , {ValueChanged<Map>? callback}) async {
    WeChat().wechatLoginAuth(code).then((response) {
      Map map = json.decode(response.data);
      WeChat().getWechatUserInfo(map["access_token"], map["openid"])
          .then((response) {
        Map userInfo = json.decode(response.data);
        UserDefault.setData(Define.WXUSERINFOKEY, userInfo);

        callback?.call(userInfo);
      });
    });
  }

  Future login() async {
    // fluwx.sendWeChatAuth(scope: "snsapi_userinfo", state: "wechat_sdk_demo_test")

    fluwx.authBy(which: NormalAuth(scope: 'snsapi_userinfo', state: "wechat_sdk_demo_test"))
        .then((data) {
      if (!data) {
        toast('没有安装微信，请安装微信后使用该功能');
      } else {}
    }).catchError((e) {
      logger('weChatLogin error $e');
    });
  }

  Future<bool> wechatHasInstall() async {
    return await fluwx.isWeChatInstalled;
  }

  Future<Uint8List> loadAssetAsUint8List(String assetPath) async {
    // 加载资源文件
    ByteData byteData = await rootBundle.load(assetPath);
    // 转换为 Uint8List
    return byteData.buffer.asUint8List();
  }

  //分享到微信
  shareLinkToWechat(String linkUrl, String title, String description,
      String thumbnail) async {
    var result = await WechatHelper.instance.wechatHasInstall();
    if (!result) {
      toast('无法打开微信 请检查是否安装了微信');

      return;
    }

    var imageData = await loadAssetAsUint8List('assets/images/3.0x/about_icon.png');

    //分享的小图片
    /// 分享到好友
    var wechatModel = WeChatShareWebPageModel(
      //链接
      linkUrl,
      //标题
      title: title,
      description: description,
      //小图
      thumbData: imageData,
      //微信消息
      // scene: WeChatScene.SESSION,
      scene: WeChatScene.session,
    );

    fluwx.share(wechatModel);
  }

  //分享到微信
  shareTextToWechat(String text) async {
    var result = await fluwx.isWeChatInstalled;
    if (!result) {
      toast('无法打开微信 请检查是否安装了微信');

      return;
    }
    logger('reusult=====$result');
    var wechatModel = WeChatShareTextModel('123', description: text);

    fluwx.share(wechatModel);
    // shareToWeChat(wechatModel);
  }

  cancel() {
    if(listener == null) return;
    fluwx.removeSubscriber(listener);
  }

}