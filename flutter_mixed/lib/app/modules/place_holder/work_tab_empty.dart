
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../common/config/config.dart';
import '../contact/model/org/org_model.dart';


// work tab 空页面
class WorkTabEmpty extends StatelessWidget {

  RxString? orgModel;

  String icon;

  String title;

  String tips;

  VoidCallback? createTeamCallBack;
  VoidCallback? joinTeamCallBack;

  WorkTabEmpty(this.orgModel , this.icon, this.title ,this.tips , this.createTeamCallBack ,this.joinTeamCallBack);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text((orgModel ?? '').toString()),
          Image.asset(AssetsRes.IC_EMPTY_COMPANY , width: 783 ,height: 575),
          const Text('你还没有加入工作企业' , style: TextStyle(fontSize: 17 , color: Colors.black),),
          15.gap,
          const Text('马上尝试创建或加入一个企业吧\n加入企业后，你即可开始使用考勤、公告等功能' , style: TextStyle(fontSize: 17 , color: Colors.grey),),
          CupertinoButton(
              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
              color: ColorConfig.themeCorlor,
              borderRadius: const BorderRadius.all(Radius.circular(4)),
              pressedOpacity: 0.5,
              child: const Text(
                '创建企业',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
              onPressed: () async {
                createTeamCallBack?.call();
              }),


          CupertinoButton(
              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
              color: ColorConfig.themeCorlor,
              borderRadius: const BorderRadius.all(Radius.circular(4)),
              pressedOpacity: 0.5,
              child: const Text(
                '加入企业',
                style: TextStyle(color: Colors.blue, fontSize: 14),
              ),
              onPressed: () async {
                joinTeamCallBack?.call();
              }),
        ],
      ),
    );
  }



}
