
import 'package:flutter/material.dart';
import 'package:flutter_mixed/res/assets_res.dart';


// 通用 空页面
class CommonEmpty extends StatelessWidget {

  final String title;

  final String? tips;

  // VoidCallback? createTeamCallBack;
  // VoidCallback? joinTeamCallBack;

  const CommonEmpty(this.title ,{this.tips = '' });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 290,
      height: 216,
      color: Colors.transparent,
      child: Stack(
        // mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            alignment: Alignment.center,
            child: Image.asset(AssetsRes.IC_EMPTY_COMPANY , width: 290 , height: 216,),
          ),
          Container(
            margin: const EdgeInsets.only(bottom: 50),
            alignment: Alignment.bottomCenter,
            child: Text(title , style: TextStyle(fontSize: 17 , color: Colors.black),),
          ),
          Text(tips ?? '' , style: TextStyle(fontSize: 17 , color: Colors.grey),),
        ],
      ),
    );
  }



}
