import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_mixed/logger/logger.dart';

class SchemePage extends StatefulWidget {
  const SchemePage({super.key});

  @override
  State<SchemePage> createState() => _SchemePageState();
}

class _SchemePageState extends State<SchemePage> {
  @override
  void initState() {
    super.initState();
    _handleRoute();
  }

  void _handleRoute() {
    try {
      // 获取路由数据
      final String routeData = Get.arguments as String;
      
      // 解析路由数据
      final Map<String, dynamic> routeMap = json.decode(routeData);
      final String route = routeMap['route'] as String;
      final Map<String, dynamic> params = routeMap['params'] as Map<String, dynamic>;

      // 根据路由跳转到对应页面
      switch (route) {
        case '/approve-detail':
          Get.toNamed('/approve-detail', arguments: params);
          break;
        // 添加其他路由处理
        default:
          logger('Unknown route: $route');
          break;
      }
    } catch (e) {
      logger('Error parsing route data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 返回一个加载中的页面，因为路由处理完成后会立即跳转
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
} 