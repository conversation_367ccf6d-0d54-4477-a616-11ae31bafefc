import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';

import '../controllers/feed_back_detail_controller.dart';

class FeedBackDetailView extends GetView<FeedBackDetailController> {
  const FeedBackDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          body: Stack(
            children: [
              Positioned(
                left: 0,
                right: 0,
                top: 0,
                height: DeviceUtils().top.value + 44,
                child: Container(
                  decoration: const BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xFFCBDBFE), Color(0xffDDE6FA)])),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                top: DeviceUtils().top.value + 44,
                height: DeviceUtils().height.value * 0.25 -
                    DeviceUtils().top.value -
                    44,
                child: Container(
                  decoration: const BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                        Color(0xffDDE6FA),
                        ColorConfig.backgroundColor
                      ])),
                ),
              ),
              Column(
                children: [
                  (DeviceUtils().top.value + 44).gap,
                  if (controller.status.value == 0) ...[_buildUnDoneWidget()],
                  if (controller.status.value == 1) ...[_buildDoneWidget()]
                ],
              ),
              _buildTitleBar()
            ],
          ),
        ));
  }

  _buildTitleBar() {
    return Container(
      padding: EdgeInsets.only(top: DeviceUtils().top.value),
      width: double.infinity,
      height: DeviceUtils().top.value + 44,
      decoration: const BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFCBDBFE), Color(0xffDDE6FA)])),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 24,
            child: CupertinoButton(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                onPressed: () {
                  Get.back();
                },
                child: Image.asset('assets/images/3.0x/pic_return.png')),
          ),
          Expanded(
              child: Container(
            alignment: Alignment.center,
            child: const Text(
              '需求详情',
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ColorConfig.mainTextColor),
            ),
          )),
          // 测试切换按钮
          Container(
            width: 56,
            height: 24,
            child: CupertinoButton(
              padding: const EdgeInsets.all(0),
              onPressed: () {
                controller.status.value = controller.status.value == 0 ? 1 : 0;
              },
              child: const Text(
                '切换',
                style: TextStyle(
                  fontSize: 12,
                  color: ColorConfig.mainTextColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildUnDoneWidget() {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            16.gap,
            // 状态标题区域
            _buildStatusHeader(
              icon: AssetsRes.FEED_BACK_DOING,
              iconColor: ColorConfig.mainTextColor,
              title: '受理中',
            ),
            12.gap,
            // 时间线内容
            _buildTimelineContent(showCompleted: false),
          ],
        ),
      ),
    );
  }

  _buildDoneWidget() {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            16.gap,
            // 状态标题区域
            _buildStatusHeader(
              icon: AssetsRes.FEED_BACK_DONE,
              iconColor: ColorConfig.mainTextColor,
              title: '受理完成',
            ),
            12.gap,
            // 时间线内容
            _buildTimelineContent(showCompleted: true),
          ],
        ),
      ),
    );
  }

  // 状态标题组件
  Widget _buildStatusHeader({
    required String icon,
    required Color iconColor,
    required String title,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        children: [
          Image.asset(icon,width: 20,),
          8.gap,
          Text(
            title,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w500,
              color: iconColor,
            ),
          ),
        ],
      ),
    );
  }

  // 时间线内容组件
  Widget _buildTimelineContent({required bool showCompleted}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ColorConfig.whiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 如果是受理完成状态，显示受理完成节点
          if (showCompleted) ...[
            _buildTimelineItem(
              icon: AssetsRes.ABOUT_ICON,
              stateStr: '受理完成',
              name: '担当办公',
              time: '2025-06-01 13:00',
              content: '内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内',
              title: '',
              isSend: false
            ),
          ],
          // 发起反馈节点
          _buildTimelineItem(
            icon: AssetsRes.ABOUT_ICON,
            stateStr: '发起反馈',
            name: '张三',
            time: '2025-06-01 13:00',
            content: '123',
            title: '发起者的标题',
            isSend: true
          ),
        ],
      ),
    );
  }

  // 时间线单个节点组件
  Widget _buildTimelineItem({
    required String icon,
    required String stateStr,
    required String name,
    required String time,
    required String content,
    required String title,
    required bool isSend,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 左侧时间线区域
            SizedBox(
              width: 12,
              height: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //图标
                  _buildCircleIcon(isSend),
                  4.gap,
                  Expanded(
                      child: Container(
                    width: 1,
                    color: ColorConfig.backgroundColor,
                  ))
                ],
              ),
            ),
            12.gap,
            // 右侧内容区域
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 24,
                    child: Text(
                      stateStr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: ColorConfig.mainTextColor,
                      ),
                    ),
                  ),
                  // 标题行
                  4.gap,
                  // 副标题
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                        color: ColorConfig.backgroundColor,
                        borderRadius: BorderRadius.circular(8)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 42,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      color: ColorConfig.whiteColor),
                                  child: ImageLoader(
                                    url: icon,
                                    width: 32,
                                    height: 32,
                                    radius: 6,
                                  )),
                              8.gap,
                              Expanded(
                                  child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    height: 24,
                                    child: Text(
                                      name,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: ColorConfig.mainTextColor,
                                      ),
                                    ),
                                  ),
                                  Container(
                                    height: 18,
                                    child: Text(
                                      time,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.msgTextColor,
                                      ),
                                    ),
                                  )
                                ],
                              ))
                            ],
                          ),
                        ),
                        4.gap,
                        if (!StringUtil.isEmpty(title)) ...[
                          Container(
                            padding: const EdgeInsets.only(top: 3, bottom: 7),
                            child: Text(
                              title,
                              maxLines: null,
                              style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: ColorConfig.mainTextColor),
                            ),
                          )
                        ],
                        // 内容
                        Text(
                          content,
                          style: const TextStyle(
                            fontSize: 14,
                            color: ColorConfig.mainTextColor,
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  //左侧圆形图标
  Widget _buildCircleIcon(bool isSend) {
    if (isSend) {
      return Container(
        width: 12,
        height: 24,
        alignment: Alignment.center,
        child: Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
              color: ColorConfig.msgTextColor,
              borderRadius: BorderRadius.circular(3)),
        ),
      );
    }
    return Container(
      width: 12,
      height: 24,
      alignment: Alignment.center,
      child: Container(
        width: 12,
        height: 6,
        decoration: BoxDecoration(
            color: ColorConfig.themeCorlor,
            borderRadius: BorderRadius.circular(3)),
      ),
    );
  }
}
