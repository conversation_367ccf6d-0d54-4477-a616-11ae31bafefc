import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

import 'package:get/get.dart';

import '../controllers/feed_back_detail_controller.dart';

class FeedBackDetailView extends GetView<FeedBackDetailController> {
  const FeedBackDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          left: 0,
          right: 0,
          top: 0,
          height: DeviceUtils().top.value + 44,
          child: Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFFCBDBFE), Color(0xffDDE6FA)])),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          top: DeviceUtils().top.value + 44,
          height:
              DeviceUtils().height.value * 0.25 - DeviceUtils().top.value - 44,
          child: Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xffDDE6FA), ColorConfig.backgroundColor])),
          ),
        ),
        Column(
          children: [
            (DeviceUtils().top.value + 44).gap,
            
          ],
        ),
        _buildTitleBar()
      ],
    );
  }

  _buildTitleBar() {
    return Container(
      padding: EdgeInsets.only(top: DeviceUtils().top.value),
      width: double.infinity,
      height: DeviceUtils().top.value + 44,
      decoration: const BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFCBDBFE), Color(0xffDDE6FA)])),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 24,
            child: CupertinoButton(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                onPressed: () {
                  Get.back();
                },
                child: Image.asset('assets/images/3.0x/pic_return.png')),
          ),
          Expanded(
              child: Container(
            alignment: Alignment.center,
            child: const Text(
              '需求详情',
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ColorConfig.mainTextColor),
            ),
          )),
          56.gap
        ],
      ),
    );
  }
}
