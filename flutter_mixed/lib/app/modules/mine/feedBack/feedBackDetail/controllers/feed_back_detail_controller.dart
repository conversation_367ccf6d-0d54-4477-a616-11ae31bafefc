import 'package:get/get.dart';

// 反馈状态枚举
enum FeedbackStatus {
  processing, // 受理中
  completed,  // 受理完成
}

// 反馈详情数据模型
class FeedbackDetailModel {
  final String id;
  final String title;
  final String content;
  final FeedbackStatus status;
  final DateTime createTime;
  final DateTime? completeTime;
  final String userName;
  final String userAvatar;
  final String? adminName;
  final String? adminAvatar;
  final String? adminReply;

  FeedbackDetailModel({
    required this.id,
    required this.title,
    required this.content,
    required this.status,
    required this.createTime,
    this.completeTime,
    required this.userName,
    required this.userAvatar,
    this.adminName,
    this.adminAvatar,
    this.adminReply,
  });
}

class FeedBackDetailController extends GetxController {
  //TODO: Implement FeedBackDetailController

  // 反馈详情数据
  final Rx<FeedbackDetailModel?> feedbackDetail = Rx<FeedbackDetailModel?>(null);
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void increment() => count.value++;
}
