import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

class SendFeedBackController extends GetxController {
  //TODO: Implement SendFeedBackController

  TextEditingController? titleEditingController;
  TextEditingController? contentEditingController;
  @override
  void onInit() {
    super.onInit();
    titleEditingController = TextEditingController();
    contentEditingController = TextEditingController();
  }

  //进入意见反馈历史记录
  jumpFeedBackHistory(){
    RouteHelper.route(Routes.FEED_BACK_LIST);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    titleEditingController?.dispose();
    contentEditingController?.dispose();
  }
}
