import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class SendFeedBackController extends GetxController {
  //TODO: Implement SendFeedBackController

  TextEditingController? titleEditingController;
  TextEditingController? contentEditingController;

  // 图片相关
  final RxList<File> imageList = <File>[].obs;
  final int maxImageCount = 56;
  final ImagePicker _picker = ImagePicker();
  @override
  void onInit() {
    super.onInit();
    titleEditingController = TextEditingController();
    contentEditingController = TextEditingController();
  }

  //进入意见反馈历史记录
  jumpFeedBackHistory(){
    RouteHelper.route(Routes.FEED_BACK_LIST);
  }

  // 选择图片
  Future<void> pickImage() async {
var r = await PermissionUtil.checkGalleryPermission(Get.context!,
        tip: takePhotoPermissionTip);
    if (!r) return;

    if (imageList.length >= maxImageCount) {
      toast('最多上传$maxImageCount个附件');
      return;
    }

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: AssetPickerConfig(
            maxAssets: maxImageCount - imageList.length >= maxImageCount? maxImageCount : maxImageCount - imageList.length,
            requestType: RequestType.image));
    if (assets == null) return;
    for (var i = 0; i < assets.length; i++) {
      AssetEntity entity = assets[i];
      File? file = await entity.file;
      
    }

  }

  // 删除图片
  void removeImage(int index) {
    if (index >= 0 && index < imageList.length) {
      imageList.removeAt(index);
    }
  }

  // 提交反馈
  void submitFeedback() {
    String title = titleEditingController?.text.trim() ?? '';
    String content = contentEditingController?.text.trim() ?? '';

    if (title.isEmpty) {
      Get.snackbar('提示', '请输入标题');
      return;
    }

    if (content.isEmpty) {
      Get.snackbar('提示', '请输入反馈内容');
      return;
    }

    // TODO: 实现实际的提交逻辑，包括图片上传
    Get.snackbar('提示', '反馈提交成功');
    Get.back();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    titleEditingController?.dispose();
    contentEditingController?.dispose();
  }
}
