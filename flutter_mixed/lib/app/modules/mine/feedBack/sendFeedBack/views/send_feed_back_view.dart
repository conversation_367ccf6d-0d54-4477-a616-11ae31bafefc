import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

import 'package:get/get.dart';

import '../controllers/send_feed_back_controller.dart';

class SendFeedBackView extends GetView<SendFeedBackController> {
  const SendFeedBackView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context, '意见反馈', false, [_buildAction()], onPressed: () {
            Get.back();
          }, backgroundColor: Colors.transparent),
          body: Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFFCBDBFE), ColorConfig.backgroundColor])),
            child: Column(
              children: [
                (DeviceUtils().top.value).gap,
                44.gap,
                16.gap,
                _buildHelloTitle('Hello'),
                _buildMsgTitle('有什么好的建议可以分享下哦～'),
                12.gap,
                _buildContentWidget(),
              ],
            ),
          ),
        ));
  }

  //历史记录
  _buildAction() {
    return Container(
      padding: const EdgeInsets.only(right: 16),
      child: CupertinoButton(
          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          pressedOpacity: 0.5,
          child: const Text(
            '历史记录',
            style: TextStyle(color: ColorConfig.themeCorlor, fontSize: 14),
          ),
          onPressed: () {
            controller.jumpFeedBackHistory();
          }),
    );
  }

  //Hello
  _buildHelloTitle(String title) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Text(
        title,
        style: const TextStyle(fontSize: 32, color: ColorConfig.mainTextColor),
      ),
    );
  }

  _buildMsgTitle(String title) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        title,
        style: const TextStyle(fontSize: 16, color: ColorConfig.msgTextColor),
      ),
    );
  }

  _buildContentWidget() {
    return Flexible(
        child: Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: ColorConfig.whiteColor),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTopWidget(),
          _buildTitleInput(),
          const Divider(
            color: ColorConfig.backgroundColor,
            height: 1,
            indent: 12,
          ),
          12.gap,
          _buildContentInput(),
          _buildTextLimit()
        ],
      ),
    ));
  }

  _buildTopWidget() {
    return Container(
      alignment: Alignment.centerLeft,
      width: double.infinity,
      height: 34,
      color: ColorConfig.backgroundColor,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12), topRight: Radius.circular(12))),
      child: Container(
        width: 88,
        height: double.infinity,
        padding: const EdgeInsets.only(left: 12),
        alignment: Alignment.center,
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12)),
            gradient: LinearGradient(colors: [
              Color(0xFFA2F9FF),
              Color(0x4BD0FEDF),
              Color(0x00D0FEDF)
            ])),
        child: const Text(
          '详细描述',
          style: TextStyle(fontSize: 16, color: ColorConfig.mainTextColor),
        ),
      ),
    );
  }

  //输入标题
  _buildTitleInput() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      height: 42,
      child: Row(
        children: [
          Expanded(
              child: TextField(
                onChanged: (value) {
                  
                },
            controller: controller.titleEditingController,
            decoration: const InputDecoration(
                contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                hintText: '清晰的标题会更快被核实',
                hintStyle: TextStyle(
                  color: ColorConfig.desTextColor,
                  fontSize: 14,
                )),
          )),
          Text('${controller.titleEditingController?.text.length}/30', style: TextStyle(fontSize: 13, color: ColorConfig.msgTextColor))
        ],
      ),
    );
  }

  //输入内容
  _buildContentInput() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      height: 150,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: ColorConfig.whiteColor),
      child: TextField(
        controller: controller.contentEditingController,
        maxLines: null,
        minLines: 1,
        decoration: const InputDecoration(
            contentPadding: EdgeInsets.only(top: 0, bottom: 0),
            border: OutlineInputBorder(borderSide: BorderSide.none),
            hintText: '请描述您使用场景中遇到的困难或优化建议',
            hintStyle: TextStyle(
              color: ColorConfig.desTextColor,
              fontSize: 14,
            )),
      ),
    );
  }

  _buildTextLimit(){
    return Container(
      width: double.infinity,
      height: 20,
      child: Text('${controller.contentEditingController?.text.length}/500',style: const TextStyle(fontSize: 13, color: ColorConfig.msgTextColor),),
    );
  }
}
