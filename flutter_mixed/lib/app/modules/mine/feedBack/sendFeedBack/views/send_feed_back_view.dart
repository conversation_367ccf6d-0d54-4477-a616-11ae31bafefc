import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'dart:io';

import 'package:get/get.dart';

import '../controllers/send_feed_back_controller.dart';

class SendFeedBackView extends GetView<SendFeedBackController> {
  const SendFeedBackView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          body: Stack(
            children: [
              Positioned(
                left: 0,
                right: 0,
                top: 0,
                height: DeviceUtils().top.value + 44,
                child: Container(
                  decoration: const BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xFFCBDBFE), Color(0xffDDE6FA)])),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                top: DeviceUtils().top.value + 44,
                height: DeviceUtils().height.value * 0.25 -
                    DeviceUtils().top.value -
                    44,
                child: Container(
                  decoration: const BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                        Color(0xffDDE6FA),
                        ColorConfig.backgroundColor
                      ])),
                ),
              ),
              Column(
                children: [
                  Expanded(
                      child: Container(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          (DeviceUtils().top.value + 44).gap,
                          16.gap,
                          _buildHelloTitle('Hello'),
                          _buildMsgTitle('有什么好的建议可以分享下哦～'),
                          12.gap,
                          _buildContentWidget(),
                        ],
                      ),
                    ),
                  )),
                  16.gap,
                  Column(
                    children: [
                      _buildSubmitButton(),
                      (DeviceUtils().bottom.value + 16).gap,
                    ],
                  )
                ],
              ),
              _buildTitleBar()
            ],
          ),
        ));
  }

  _buildTitleBar() {
    return Container(
      padding: EdgeInsets.only(top: DeviceUtils().top.value),
      width: double.infinity,
      height: DeviceUtils().top.value + 44,
      decoration: const BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFCBDBFE), Color(0xffDDE6FA)])),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 24,
            child: CupertinoButton(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                onPressed: () {
                  Get.back();
                },
                child: Image.asset('assets/images/3.0x/pic_return.png')),
          ),
          Expanded(
              child: Container(
            alignment: Alignment.center,
            child: const Text(
              '意见反馈',
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ColorConfig.mainTextColor),
            ),
          )),
          _buildAction()
        ],
      ),
    );
  }

  //历史记录
  _buildAction() {
    return Container(
      width: 56,
      height: 24,
      child: CupertinoButton(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
          onPressed: () {
            controller.jumpFeedBackHistory();
          },
          child: Image.asset(AssetsRes.FEED_BACK_RECORD)),
    );
  }

  //Hello
  _buildHelloTitle(String title) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Text(
        title,
        style: const TextStyle(fontSize: 32, color: ColorConfig.mainTextColor),
      ),
    );
  }

  _buildMsgTitle(String title) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        title,
        style: const TextStyle(fontSize: 16, color: ColorConfig.msgTextColor),
      ),
    );
  }

  _buildContentWidget() {
    return Flexible(
        child: Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: ColorConfig.whiteColor),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTopWidget(),
          _buildTitleInput(),
          const Divider(
            color: ColorConfig.backgroundColor,
            height: 1,
            indent: 12,
          ),
          12.gap,
          _buildContentInput(),
          _buildTextLimit(),
          _buildImageSection(),
        ],
      ),
    ));
  }

  _buildTopWidget() {
    return Container(
      alignment: Alignment.centerLeft,
      width: double.infinity,
      height: 34,
      decoration: const BoxDecoration(
          color: ColorConfig.backgroundColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12), topRight: Radius.circular(12))),
      child: Container(
        width: 88,
        height: double.infinity,
        padding: const EdgeInsets.only(left: 12),
        alignment: Alignment.center,
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12)),
            gradient: LinearGradient(colors: [
              Color(0xFFA2F9FF),
              Color(0x4BD0FEDF),
              Color(0x00D0FEDF)
            ])),
        child: const Text(
          '详细描述',
          style: TextStyle(fontSize: 16, color: ColorConfig.mainTextColor),
        ),
      ),
    );
  }

  //输入标题
  _buildTitleInput() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      height: 42,
      child: Row(
        children: [
          Expanded(
              child: TextField(
            onChanged: (value) {
              controller.imageList.refresh();
            },
            inputFormatters: [LengthLimitingTextInputFormatter(30)],
            controller: controller.titleEditingController,
            decoration: const InputDecoration(
                contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                border: OutlineInputBorder(borderSide: BorderSide.none),
                hintText: '清晰的标题会更快被核实',
                hintStyle: TextStyle(
                  color: ColorConfig.desTextColor,
                  fontSize: 14,
                )),
          )),
          Text('${controller.titleEditingController?.text.length}/30',
              style: const TextStyle(
                  fontSize: 13, color: ColorConfig.msgTextColor))
        ],
      ),
    );
  }

  //输入内容
  _buildContentInput() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      height: 150,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: ColorConfig.whiteColor),
      child: TextField(
        onChanged: (value) {
          controller.imageList.refresh();
        },
        inputFormatters: [LengthLimitingTextInputFormatter(500)],
        controller: controller.contentEditingController,
        maxLines: null,
        minLines: 1,
        decoration: const InputDecoration(
            contentPadding: EdgeInsets.only(top: 0, bottom: 0),
            border: OutlineInputBorder(borderSide: BorderSide.none),
            hintText: '请描述您使用场景中遇到的困难或优化建议',
            hintStyle: TextStyle(
              color: ColorConfig.desTextColor,
              fontSize: 14,
            )),
      ),
    );
  }

  _buildTextLimit() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      width: double.infinity,
      height: 20,
      alignment: Alignment.centerRight,
      child: Text(
        '${controller.contentEditingController?.text.length}/500',
        style: const TextStyle(fontSize: 13, color: ColorConfig.msgTextColor),
      ),
    );
  }

  // 图片上传区域
  _buildImageSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: _buildImageGrid(),
    );
  }

  // 图片网格显示
  _buildImageGrid() {
    return MediaQuery.removePadding(
        removeTop: true,
        removeBottom: true,
        context: Get.context!,
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: controller.imageList.length < controller.maxImageCount
              ? controller.imageList.length + 1
              : controller.maxImageCount,
          itemBuilder: (context, index) {
            if (index == controller.imageList.length &&
                controller.imageList.length < controller.maxImageCount) {
              return _buildAddImageItem();
            }
            return _buildImageItem(controller.imageList[index], index);
          },
        ));
  }

  // 单个图片项
  _buildImageItem(File imageFile, int index) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: ColorConfig.backgroundColor,
      ),
      child: Stack(
        children: [
          // 图片
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              imageFile,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
          // 删除按钮
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () => controller.removeImage(index),
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: const DecorationImage(
                        image: AssetImage(AssetsRes.FEED_BACK_CLOSE))),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildAddImageItem() {
    return GestureDetector(
      onTap: () {
        controller.pickImage();
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: ColorConfig.backgroundColor,
        ),
        child: Column(
          children: [
            20.gap,
            Image.asset(
              AssetsRes.FEED_BACK_NO_IMAGE,
              width: 24,
            ),
            Container(
              alignment: Alignment.center,
              height: 20,
              child: Text(
                '${controller.imageList.length}/6',
                style: const TextStyle(
                    fontSize: 13, color: ColorConfig.msgTextColor),
              ),
            )
          ],
        ),
      ),
    );
  }

  // 提交按钮
  _buildSubmitButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: () {
          // TODO: 实现提交逻辑
          controller.submitFeedback();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConfig.themeCorlor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: const Text(
          '提交',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
