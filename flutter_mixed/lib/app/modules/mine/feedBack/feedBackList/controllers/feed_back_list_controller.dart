import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

class FeedBackListController extends GetxController {
  //TODO: Implement FeedBackListController

  RxList dataList = ['ceshi','ceshi1'].obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  jumpFeedBackDetail(){
    RouteHelper.route(Routes.FEED_BACK_DETAIL);
  }

}
