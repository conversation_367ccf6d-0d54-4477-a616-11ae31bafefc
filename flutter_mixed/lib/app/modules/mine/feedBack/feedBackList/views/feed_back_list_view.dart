import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

import 'package:get/get.dart';

import '../controllers/feed_back_list_controller.dart';

class FeedBackListView extends GetView<FeedBackListController> {
  const FeedBackListView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar:
              TitleBar().backAppbar(context, '历史记录', false, [], onPressed: () {
            Get.back();
          }),
          body: Column(
            children: [
              16.gap,
              Expanded(
                  child: ListView.builder(
                itemCount: controller.dataList.length,
                itemBuilder: (context, index) {
                  return InkWell(
                    child: Container(
                      margin: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorConfig.whiteColor),
                      padding: const EdgeInsets.all(12),
                      child: _buildItem(),
                    ),
                  );
                },
              ))
            ],
          ),
        ));
  }

  _buildItem() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Container(
              child: const Text(
                '标题',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: ColorConfig.mainTextColor),
              ),
            )),
            8.gap,
            Container(
              padding: EdgeInsets.symmetric(horizontal: 3,vertical: 2),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: ColorConfig.imMyBackColor),
              child: const Text(
                '已收到',
                style: TextStyle(fontSize: 12, color: ColorConfig.themeCorlor),
              ),
            )
          ],
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: const Text(
            '这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容这是内容',
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
          ),
        ),
        Container(
          alignment: Alignment.centerLeft,
          child: const Text(
            '2023-08-10 10:00:00',
            style: TextStyle(fontSize: 12, color: ColorConfig.desTextColor),
          ),
        ),
      ],
    );
  }
}
