import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/click_card/click_card_jump.dart';
import 'package:flutter_mixed/app/im/widget/some_widget.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/cache_helper.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../common/channel/channel.dart';
import '../../../../common/widgets/image_loader.dart';
import '../../../../test_widget/test_mine_enter.dart';
import '../../../../utils/login_util.dart';
import '../../../contact/model/friend/user_model.dart';
import '../controllers/mine_home_controller.dart';

class MineHomeView extends GetView<MineHomeController> {
  MineHomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    MineHomeController controller = Get.put(MineHomeController());
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(context, '我的', true, [],
              onPressed: () {}, backgroundColor: ColorConfig.backgroundColor),
          body: ListView(
            children: [
              12.gap,
              _buildUserInfo(),
              8.gap,
              _buildSingleContent(controller.qrMap),
              if (controller.isHaveParkAuth) ...[
                _buildSingleContent(controller.benefitMap),
              ],
              _buildSingleContent(controller.realNameMap,
                  rightWidget: _buildUnRealName()),
              _buildMoreContent([controller.settingMap, controller.aboutMap]),
              if (kDebugMode) ...[
                const TestMineEnter(),
              ]
            ],
          ),
        ));
  }

  //个人资料
  _buildUserInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      width: double.infinity,
      height: 72,
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: ColorConfig.whiteColor),
      child: Row(
        children: [
          InkWell(
            onTap: () {
              controller.tapAvatar();
            },
            child: ImageLoader(
              url: controller.userInfo['avatar'],
              width: 48,
              height: 48,
              radius: 8,
            ),
          ),
          12.gap,
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 24,
                alignment: Alignment.centerLeft,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                        child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                            child: Text(
                          controller.userInfo['name'] ?? '',
                          maxLines: 1,
                          style: const TextStyle(
                              overflow: TextOverflow.ellipsis,
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              color: ColorConfig.mainTextColor),
                        )),
                        4.gap,
                        Visibility(
                            visible: controller.authStatus.value,
                            child: Image.asset(
                              AssetsRes.ICON_REALNAME_VERIFY,
                              height: 18,
                            ))
                      ],
                    )),
                    2.gap,
                    InkWell(
                      onTap: () {
                        controller.jumpMineInfo();
                      },
                      child: Container(
                        width: 62,
                        height: double.infinity,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            border: Border.all(
                                width: 0.5, color: ColorConfig.lineColor),
                            borderRadius: BorderRadius.circular(4)),
                        child: const Text(
                          '编辑资料',
                          style: TextStyle(
                              fontSize: 11, color: ColorConfig.desTextColor),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              2.gap,
              Row(
                children: [
                  Container(
                    height: 22,
                    child: Text(
                      controller.mainOrgName.value,
                      maxLines: 1,
                      style: const TextStyle(
                          fontSize: 14,
                          overflow: TextOverflow.ellipsis,
                          color: ColorConfig.desTextColor),
                    ),
                  ),
                ],
              )
            ],
          )),
        ],
      ),
    );
  }

  _buildSingleContent(Map contentMap, {Widget rightWidget = const SizedBox()}) {
    return InkWell(
      onTap: () async {
        Function? ontap = contentMap['ontap'];
        if (ontap != null) {
          ontap();
        }
      },
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 12),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: ColorConfig.whiteColor),
            child: _buildRowContent(contentMap, rightWidget: rightWidget),
          ),
          8.gap
        ],
      ),
    );
  }

  _buildRowContent(Map contentMap, {Widget rightWidget = const SizedBox()}) {
    return Container(
      height: 48,
      width: double.infinity,
      child: Row(
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: Image.asset(contentMap['headImageName']),
          ),
          8.gap,
          Expanded(
              child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: Container(
                child: Text(
                  contentMap['title'],
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              )),
              rightWidget
            ],
          )),
          2.gap,
          SizedBox(
            width: 20,
            height: 20,
            child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
          ),
        ],
      ),
    );
  }

  //未认证
  _buildUnRealName() {
    return Offstage(
      offstage: controller.authStatus.value,
      child: Container(
      padding: const EdgeInsets.fromLTRB(4, 3, 4, 3),
      decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: ColorConfig.lineColor),
          borderRadius: BorderRadius.circular(4)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 14,
            height: 14,
            child: Image.asset(AssetsRes.ICON_REALNAME_NON),
          ),
          3.gap,
          const Text(
            '未实名',
            style: TextStyle(fontSize: 11, color: ColorConfig.desTextColor),
          ),
        ],
      ),
    ),
    );
  }

  _buildMoreContent(List contentList) {
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: ColorConfig.whiteColor),
        child: Column(
          children: _buildWidgetList(contentList),
        ));
  }

  _buildWidgetList(List contentList) {
    List<Widget> lists = [];
    for (var i = 0; i < contentList.length; i++) {
      Map contentMap = contentList[i];
      lists.add(InkWell(
        onTap: () {
          Function? ontap = contentMap['ontap'];
          if (ontap != null) {
            ontap();
          }
        },
        child: Column(
          children: [
            _buildRowContent(contentList[i]),
            if (i != contentList.length - 1) ...[
              Container(
                margin: const EdgeInsets.only(left: 32),
                color: ColorConfig.backgroundColor,
                height: 0.5,
              )
            ]
          ],
        ),
      ));
    }
    return lists;
  }
}
