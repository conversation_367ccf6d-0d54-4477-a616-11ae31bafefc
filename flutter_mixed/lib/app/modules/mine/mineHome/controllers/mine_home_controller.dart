import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/click_card/click_card_jump.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../retrofit/datasource/real_name_datasource.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/storage.dart';
import '../../../gallery/gallery.dart';

class MineHomeController extends GetxController {
  RxString mainOrgName = ''.obs;
  RxMap userInfo = {}.obs;
  RxBool authStatus = false.obs;

  static Map staMap = {};

  bool isHaveParkAuth = false;//是否有员工福利入口

  //员工福利
  Map benefitMap = {
    'headImageName': AssetsRes.MINE_BENEFIT,
    'title': '员工福利',
    'ontap': () {
      _jumpMineBenefit();
    }
  };

  //二维码
  Map qrMap = {
    'headImageName': 'assets/images/3.0x/mineHome_qrCode.png',
    'title': '二维码',
    'ontap': () {
      _jumpMineQrCode();
    }
  };
  //实名认证
  Map realNameMap = {
    'headImageName': 'assets/images/3.0x/icon_real_name.png',
    'title': '实名认证',
    'ontap': () {
      _jumpRealName();
    }
  };
  //设置
  Map settingMap = {
    'headImageName': 'assets/images/3.0x/mineHome_setting.png',
    'title': '设置',
    'ontap': () {
      _jumpMineSetting();
    }
  };

  //意见反馈
  Map feedBackMap = {
    'headImageName': AssetsRes.MINE_HOME_FEED_BACK,
    'title': '意见反馈',
    'ontap': () {
      _jumpFeedBack();
    }
  };
  //关于担当
  Map aboutMap = {
    'headImageName': 'assets/images/3.0x/mineHome_about.png',
    'title': '关于担当',
    'ontap': () {
      _jumpAbout();
    }
  };
  
  @override
  void onInit() async {
    super.onInit();

  }

  @override
  void onReady() {
    super.onReady();
    _getIsRangeParkUser();
    dealUserInfo();
    dealMainOrg('');
    HomeController homeController = Get.find();
    homeController.getUserInfo();
    authStatus = homeController.authStatus;
  }

  @override
  void onClose() {
    super.onClose();
  }

  //点击了头像
  tapAvatar(){
    if (StringUtil.isEmpty(userInfo['avatar'])) return;
    var galleryList = <GalleryItem>[];
    galleryList.add(GalleryItem('',userInfo['avatar']));
    Navigator.push(Get.context!,
        PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
          return FadeTransition(
              opacity: animation, child: GalleryPage(galleryList, 0));
        }));
  }

  //获取员工福利入口权限
  _getIsRangeParkUser() async{
    try {
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource.getIsRangeParkUser();
      logger('======getrangepark===$resp');
      if (resp.success()) {
        if (resp.data == true) {
          isHaveParkAuth = true;
         mainOrgName.refresh();
        }
      }
    } catch (e) {
      logger('======error==$e');
    }

  }

  //跳转个人资料
  jumpMineInfo() {
    Channel().invoke(Channel_minePageJumpNext, {'index': 0});
  }

  //跳转二维码
  static _jumpMineQrCode() {
    if (staMap['userId'] == null) return;
    UserModel userModel = UserModel(staMap['userId']);
    userModel.avatar = staMap['avatar'];
    userModel.name = staMap['name'];
    Get.toNamed('/invite-home', arguments: {'userModel': userModel, 'type': 1});
  }

  //跳转员工福利
  static _jumpMineBenefit() {
    var url =
        '${Host.WEBHOST}/ddbes-park-welfare-web/index.html#/h5/daily';
    ClickCardJump.webJump(url, '');
  }

  //跳转设置页面
  static _jumpMineSetting() {
    Channel().invoke(Channel_minePageJumpNext, {'index': 2});
  }

  //跳转意见反馈页面
  static _jumpFeedBack(){
    RouteHelper.route(Routes.SEND_FEED_BACK);
  }

  //跳转关于担当
  static _jumpAbout() {
    Get.toNamed(Routes.ABOUT);
  }
  dealUserInfo() async {
    dynamic userData = await UserDefault.getData(Define.TOKENKEY);
    if (userData != null) {
      userInfo.value = userData;
      staMap = userData;
    }
  }

  dealMainOrg(String mainId) async {
    dynamic orgDic = await UserDefault.getData(Define.ORGLIST);
    if (orgDic == null) return;
    if (mainId == '') {
      mainId = orgDic['mainCompanyId'];
    }
    if (orgDic['mainCompanyId'] != null) {
      List companyList = orgDic['companies'];
      for (var i = 0; i < companyList.length; i++) {
        Map<String, dynamic> companyDic = companyList[i];
        if (companyDic['companyId'] == mainId) {
          mainOrgName.value = companyDic['name'];
          break;
        }
      }
    }

    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    userInfo['orgName'] = mainOrgName.value;
    staMap['orgName'] = mainOrgName.value;
    await UserDefault.setData(Define.TOKENKEY, userInfo);
  }


  static Future<int> getAuthStatus() async{
    try{
      var realNameDataSource = RealNameDataSource(retrofitDio,baseUrl: Host.HOST);
      var resp = await realNameDataSource.getVerifyStatus();
      if(resp.success()){
        return  resp.data?.status??1;
      }else{
        return 1;
      }
    }catch(e){
      return 1;
    }

  }

  static _jumpRealName()  {
     getAuthStatus().then((status){
       if(status == 3 || status == 4 || status == -1){
         Get.toNamed(Routes.REALNAME_STATUS,arguments: {'canBack':true, 'status':status});
       }else{
         Get.toNamed(Routes.REAL_NAME,arguments: {'canBack':true});
       }
    });

  }
}
