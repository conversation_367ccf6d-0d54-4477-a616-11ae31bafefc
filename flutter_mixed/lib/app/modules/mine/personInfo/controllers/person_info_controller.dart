import 'dart:async';
import 'dart:io';

import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_photo_editor/flutter_photo_editor.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../../common/api/Define.dart';
import '../../../../common/base_info/info.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../permission/permission_util.dart';
import '../../../../utils/cos_helper.dart';
import '../../../../utils/storage.dart';
import '../../../workStand/models/filemodel.dart';

class PersonInfoController extends GetxController {

  String uploadBucket = '';
  Completer? completer;
  String preStr = '';
  RxString headImageStr = ''.obs;
  Map? userInfo;
  @override
  void onInit() async {
    super.onInit();
    dynamic preMap = await UserDefault.getData(Define.COSPREKEY);
    if (preMap != null) {
      preStr = preMap['pre'];
    }
  }

  @override
  void onReady() {
    super.onReady();
    getPersonInfo();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getPersonInfo() async{
    userInfo = await UserDefault.getData(Define.TOKENKEY);
    if (userInfo != null) {
      headImageStr.value = userInfo!['avatar'];
      headImageStr.refresh();
    }
  }

  tackPhotoWithCamera() async {
    var r = await PermissionUtil.checkCameraPermission(Get.context!);
    if (!r) return;
    File? file;
    if (Platform.isIOS) {
      //iOS使用插件会出现相机功能一直启用，右上角绿点一直展示的问题
      var result = await Channel()
          .invokeMap(Channel_call_iOS_camera, {'allowsEditing': 1});

      String filePath = result!['filePath'];
      file = File(filePath);
    } else {
      final AssetEntity? entity =
          await CameraPicker.pickFromCamera(Get.context!);

      file = await entity!.file;
    }
    if (file == null) return;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    await startDealFile(fileModel);
    // await startUploadFile(fileModel);

    await startUploadFileList(fileModel);
  }

  //从相册中选取

  tackPhotoFromPic() async {
    var r = await PermissionUtil.checkGalleryPermission(Get.context!);
    if (!r) return;

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: const AssetPickerConfig(
            maxAssets: 1, requestType: RequestType.image));

    if (assets == null) return;
    if (assets.isEmpty) return;

    var entity = assets.first.file;
    File? file = await entity;
    if (file == null) return;
    if (file.path == null) return;

    File tempFile = await CosHelper.move2Img(file);
    var cropResult = await FlutterPhotoEditor().editImage(tempFile.path);

    file = tempFile;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    print('$fileModel');

    await startDealFile(fileModel);
    // await startUploadFile(fileModel);

    await startUploadFileList(fileModel);
  }

  Future startDealFile(FileModel fileModel) async {
    completer = Completer<String>();
    await saveFile(fileModel).then((value) {
      fileModel.savePath = value;
      fileModel.hash = BaseInfo().generateMD5(value);
      if (completer!.isCompleted == false) {
        completer!.complete('');
      }
    });
    return completer!.future;
  }

  //保存图片
  Future<String> saveFile(FileModel fileModel) async {
    File file = fileModel.file!;
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;
    String filePath = '$docment/head/images/$fileName';
    Directory directory = Directory('$docment/head/images');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  //开始上传
  startUploadFileList(FileModel fileModel) async {
    Get.loading();
    await CosManager().initPans();
    String? bucket = await CosManager().userBucket();
    var result =
          await CosUploadHelper.nativeUpload(fileModel.savePath,'/HEAD/${fileModel.fileName}' , bucket);
    if (result != null) {
      //上传成功
      headImageStr.value = result;
    } else {
      //toast('图片上传失败');
    }

    Get.dismiss();
  }
}
