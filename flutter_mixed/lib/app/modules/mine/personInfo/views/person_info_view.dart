import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';

import 'package:get/get.dart';

import '../../../../common/config/config.dart';
import '../controllers/person_info_controller.dart';

class PersonInfoView extends GetView<PersonInfoController> {
  const PersonInfoView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
          appBar:
              TitleBar().backAppbar(context, '个人资料', false, [], onPressed: () {
            Get.back();
          }),
          body: controller.headImageStr.isEmpty
              ? Container()
              : Column(
                  children: [
                    Container(
                      color: Colors.white,
                      width: double.infinity,
                      height: 86,
                      
                      child: Column(
                        children: [
                          const Divider(
                            height: 1,
                            color: ColorConfig.lineColor,
                          ),
                          InkWell(
                              onTap: () {
                                SettingWidget().showCupertinoActionSheetForPage(
                                    context, ['拍照', '从相册中选取'], (value) {
                                  if (value == 0) {
                                    controller.tackPhotoWithCamera();
                                  }
                                  if (value == 1) {
                                    controller.tackPhotoFromPic();
                                  }
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.only(left: 15, right: 15),
                                width: double.infinity,
                                height: 84,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      alignment: Alignment.centerLeft,
                                      child: const Text(
                                        '头像',
                                        textAlign: TextAlign.left,
                                        style: TextStyle(
                                            color: ColorConfig.mainTextColor,
                                            fontSize: 14),
                                      ),
                                    ),
                                    Container(
                                      width: 76,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(1),
                                            width: 56,
                                            height: 56,
                                            alignment: Alignment.centerRight,
                                            decoration: BoxDecoration(
                                                image: DecorationImage(
                                                    image:
                                                        SettingWidget.backImageProvider(controller.headImageStr.value)),
                                                borderRadius:
                                                    BorderRadius.circular(28),
                                                border: Border.all(
                                                    color:
                                                        ColorConfig.lineColor,
                                                    width: 1)),
                                          ),
                                          Row(
                                            children: [
                                              const SizedBox(
                                                width: 10,
                                              ),
                                              SizedBox(
                                                width: 9,
                                                height: 17,
                                                child: Image.asset(
                                                    'assets/images/3.0x/mine_right.png'),
                                              )
                                            ],
                                          )
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              )),
                          const Divider(
                            height: 1,
                            color: ColorConfig.lineColor,
                          )
                        ],
                      ),
                    ),
                    InkWell(
                        onTap: () async {
                          var result = await Get.toNamed(Routes.SETTING_ORG_NAME,
                              arguments: {
                                'type': 1,
                                'name': controller.userInfo!['name']
                              });
                          if (result != null) {}
                        },
                        child: SettingWidget().backEditOrgWidget(
                            '姓名', controller.userInfo!['name'], true)),
                    InkWell(
                        onTap: () async {
                          var result = await Get.toNamed(Routes.SETTING_ORG_NAME,
                              arguments: {
                                'type': 2,
                                'name': controller.userInfo!['email']
                              });
                          if (result != null) {}
                        },
                        child: SettingWidget().backEditOrgWidget(
                            '邮箱', controller.userInfo!['email'], true)),
                    InkWell(
                        onTap: () async {},
                        child: SettingWidget().backEditOrgWidget(
                            '主要企业', controller.userInfo!['orgName'], true)),
                  ],
                ),
        ));
  }
}
