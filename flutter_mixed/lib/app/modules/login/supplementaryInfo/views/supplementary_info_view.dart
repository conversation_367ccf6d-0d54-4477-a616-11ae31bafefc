import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../common/config/config.dart';
import '../../../../../logger/logger.dart';
import '../controllers/supplementary_info_controller.dart';

class SupplementaryInfoView extends GetView<SupplementaryInfoController> {
  const SupplementaryInfoView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
        body: Obx(() => Stack(
              children: [
                Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    top: controller.top.value,
                    child: SingleChildScrollView(
                      child: Container(
                        alignment: Alignment.centerLeft,
                        width: double.infinity,
                        height: DeviceUtils().height.value,
                        padding: EdgeInsets.only(left: 16, right: 16),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                fit: BoxFit.fitWidth,
                                image: AssetImage(
                                    'assets/images/3.0x/login_backGround.png'))),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SettingWidget().backLoginBackBtn(onPressed: () {
                                  Get.back();
                                }),
                                SizedBox(
                                  height: 35,
                                ),
                                Container(
                                  width: double.infinity,
                                  
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '完善个人信息',
                                    style: TextStyle(
                                        color: ColorConfig.mainTextColor,
                                        fontSize: 27),
                                  ),
                                ),
                                SizedBox(
                                  height: 25,
                                ),
                                Container(
                                  width: 100,
                                  height: 20,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '用户名',
                                    style: TextStyle(
                                        color: ColorConfig.mainTextColor,
                                        fontSize: 14),
                                  ),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  width: double.infinity,
                                  height: 50,
                                  padding: EdgeInsets.only(left: 20),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.lineColor)),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: TextField(
                                        onTap: () {
                                          controller.isChange = true;
                                        },
                                        onChanged: (value) {
                                          if (controller.nameController.text
                                                  .isNotEmpty &&
                                              controller.pwdController.text
                                                  .isNotEmpty &&
                                              controller.againController.text
                                                  .isNotEmpty) {
                                            controller.isBlue.value = true;
                                          } else {
                                            controller.isBlue.value = false;
                                          }
                                        },
                                        textInputAction: TextInputAction.done,
                                        controller: controller.nameController,
                                        //focusNode: controller.node,
                                        style: const TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 17,
                                        ),
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    top: 0, bottom: 0),
                                            border: const OutlineInputBorder(
                                                borderSide: BorderSide.none),
                                            hintText: '请输入用户名',
                                            hintStyle: const TextStyle(
                                              color: ColorConfig.desTextColor,
                                              fontSize: 17,
                                            )),
                                      )),
                                      // InkWell(
                                      //   child: Container(
                                      //     width: 40,
                                      //     height: 40,
                                      //     alignment: Alignment.center,
                                      //     child: Container(
                                      //       width: 17,
                                      //       height: 13,
                                      //       child: Image.asset(controller
                                      //               .isOpen.value
                                      //           ? 'assets/images/3.0x/login_pwd_open.png'
                                      //           : ''),
                                      //     ),
                                      //   ),
                                      // )
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 20,
                                ),
                                Container(
                                  width: 100,
                                  height: 20,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '密码',
                                    style: TextStyle(
                                        color: ColorConfig.mainTextColor,
                                        fontSize: 14),
                                  ),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  width: double.infinity,
                                  height: 50,
                                  padding: EdgeInsets.only(left: 20),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.lineColor)),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: TextField(
                                        onTap: () {
                                          controller.isChange = true;
                                        },
                                        onChanged: (value) {
                                          if (controller.nameController.text
                                                  .isNotEmpty &&
                                              controller.pwdController.text
                                                  .isNotEmpty &&
                                              controller.againController.text
                                                  .isNotEmpty) {
                                            controller.isBlue.value = true;
                                          } else {
                                            controller.isBlue.value = false;
                                          }
                                          
                                          controller.jugeLevelWithStr();
                                        },
                                        onEditingComplete: () {
                                          logger('onEditingComplete');
                                          
                                        },
                                        onSubmitted: (value) {
                                          logger('onSubmitted');
                                        },
                                        obscureText: !controller.isPwdOpen.value,
                                        keyboardType:
                                            TextInputType.visiblePassword,
                                        textInputAction: TextInputAction.none,
                                        controller: controller.pwdController,

                                        //focusNode: controller.node,
                                        style: const TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 17,
                                        ),
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    top: 0, bottom: 0),
                                            border: const OutlineInputBorder(
                                                borderSide: BorderSide.none),
                                            hintText: '请输入密码',
                                            hintStyle: const TextStyle(
                                              color: ColorConfig.desTextColor,
                                              fontSize: 17,
                                            )),
                                      )),
                                      InkWell(
                                        onTap: () {
                                          controller.isPwdOpen.value = !controller.isPwdOpen.value;
                                        },
                                        child: Container(
                                          width: 40,
                                          height: 40,
                                          alignment: Alignment.center,
                                          child: Container(
                                            width: 17,
                                            height: 13,
                                            child: Image.asset(controller
                                                    .isPwdOpen.value
                                                ? 'assets/images/3.0x/login_pwd_open.png'
                                                : 'assets/images/3.0x/login_pwd_close.png'),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 50,
                                  child: Row(
                                    children: [
                                      Container(
                                          
                                          child: controller.level.value < 4
                                              ? Text('密码强度:弱')
                                              : controller.level.value >= 4 &&
                                                      controller.level.value <=
                                                          6
                                                  ? Text('密码强度:中')
                                                  : Text('密码强度:强')),
                                      SizedBox(
                                        width: 10,
                                      ),
                                      Container(
                                        width: 25,
                                        height: 3.5,
                                        color: controller.level.value < 4
                                              ? Colors.red
                                              : controller.level.value >= 4 &&
                                                      controller.level.value <=
                                                          6
                                                  ? Colors.yellow
                                                  : Colors.green,
                                      ),
                                      SizedBox(
                                        width: 5,
                                      ),
                                      Container(
                                        width: 25,
                                        height: 3.5,
                                        color: controller.level.value == 1?Colors.transparent:
                                        controller.level.value == 2?Colors.red:
                                        controller.level.value == 3?Colors.red:
                                        controller.level.value == 4?Colors.transparent:
                                        controller.level.value == 5?Colors.yellow:
                                        controller.level.value == 6?Colors.yellow:
                                        Colors.green,
                                      ),
                                      SizedBox(
                                        width: 5,
                                      ),
                                      Container(
                                        width: 25,
                                        height: 3.5,
                                        color: controller.level.value == 1?Colors.transparent:
                                        controller.level.value == 2?Colors.transparent:
                                        controller.level.value == 3?Colors.red:
                                        controller.level.value == 4?Colors.transparent:
                                        controller.level.value == 5?Colors.transparent:
                                        controller.level.value == 6?Colors.yellow:
                                        Colors.green,
                                      )
                                    ],
                                  ),
                                ),
                                Container(
                                  width: double.infinity,
                                  height: 50,
                                  padding: EdgeInsets.only(left: 20),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.lineColor)),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: TextField(
                                        onTap: () {
                                          controller.isChange = true;
                                        },
                                        onChanged: (value) {
                                          if (controller.nameController.text
                                                  .isNotEmpty &&
                                              controller.pwdController.text
                                                  .isNotEmpty &&
                                              controller.againController.text
                                                  .isNotEmpty) {
                                            controller.isBlue.value = true;
                                          } else {
                                            controller.isBlue.value = false;
                                          }
                                        },
                                        keyboardType:
                                            TextInputType.visiblePassword,
                                        textInputAction: TextInputAction.done,
                                        controller: controller.againController,
                                        obscureText: !controller.isAgainOpen.value,
                                        //focusNode: controller.node,
                                        style: const TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 17,
                                        ),
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    top: 0, bottom: 0),
                                            border: const OutlineInputBorder(
                                                borderSide: BorderSide.none),
                                            hintText: '再次输入密码',
                                            hintStyle: const TextStyle(
                                              color: ColorConfig.desTextColor,
                                              fontSize: 17,
                                            )),
                                      )),
                                      InkWell(
                                        onTap: () {
                                          controller.isAgainOpen.value = !controller.isAgainOpen.value;
                                        },
                                        child: Container(
                                          width: 40,
                                          height: 40,
                                          alignment: Alignment.center,
                                          child: Container(
                                            width: 17,
                                            height: 13,
                                            child: Image.asset(controller
                                                    .isAgainOpen.value
                                                ? 'assets/images/3.0x/login_pwd_open.png'
                                                : 'assets/images/3.0x/login_pwd_close.png'),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.only(left: 0, right: 0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 20,
                                        height: 20,
                                        alignment: Alignment.centerLeft,
                                        child: Container(
                                          width: 13,
                                          height: 13,
                                          child: Image.asset(
                                              'assets/images/3.0x/login_supplementary_msg.png'),
                                        ),
                                      ),
                                      Expanded(
                                          child: Container(
                                              alignment: Alignment.topCenter,
                                              height: 40,
                                              child: Text(
                                                '密码需包含数字和英文字母两种类型，且长度在6-16个字符之间',
                                                style: TextStyle(
                                                    color: ColorConfig
                                                        .desTextColor,
                                                    fontSize: 14),
                                              )))
                                    ],
                                  ),
                                )
                              ],
                            ),
                            Container(
                              width: double.infinity,
                              alignment: Alignment.topCenter,
                              height: 50 + DeviceUtils().bottom.value+16,
                              child: Container(
                                width: double.infinity,
                                height: 50,
                                child: CupertinoButton(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    color: controller.isBlue.value
                                        ? ColorConfig.themeCorlor
                                        : ColorConfig.btnGrayColor,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(7.5)),
                                    pressedOpacity: 0.5,
                                    child: const Text(
                                      '开始使用',
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 14),
                                    ),
                                    onPressed: () {
                                      controller.jugeFunction();
                            
                                    }),
                              ),
                            )
                          ],
                        ),
                      ),
                    )),
              ],
            )));
  }
}
