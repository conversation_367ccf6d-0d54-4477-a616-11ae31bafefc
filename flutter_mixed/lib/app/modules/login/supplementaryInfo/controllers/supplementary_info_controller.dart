import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/utils/net_util.dart';
import 'package:get/get.dart';
import '../../../../common/base_info/info.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/config/config.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';
import '../../../networkListen/controllers/network_listen_controller.dart';
import '../../../../utils/string-util.dart';

class SupplementaryInfoController extends GetxController
    with WidgetsBindingObserver {

  TextEditingController nameController = TextEditingController();
  TextEditingController pwdController = TextEditingController();
  TextEditingController againController = TextEditingController();

  RxBool isPwdOpen = false.obs;
  RxBool isAgainOpen = false.obs;
  RxDouble top = 0.00.obs;
  bool isChange = false;
  RxInt level = 1.obs;

  RxBool isBlue = false.obs;
  String phone = '';
  Map signData = {};
  int type = 0;
  @override
  void onInit() {
    super.onInit();
    phone = Get.arguments['phone'];
    type = Get.arguments['type'];
    signData = Get.arguments['data'];
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeMetrics() {
    // TODO: implement didChangeMetrics
    super.didChangeMetrics();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      double keyHeight = MediaQuery.of(Get.context!).viewInsets.bottom;
      if (isChange) {
        top.value = -(keyHeight / 300 * 112);
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  jugeLevelWithStr(){
    level.value = StringUtil.backLevelWithPwd(pwdController.text);
  }


  jugeFunction() {

    if (nameController.text.isEmpty) {
      toast('请输入姓名');
      return;
    }
    if (pwdController.text.isEmpty || againController.text.isEmpty) {
      toast('请输入密码');
      return;
    }
     bool isOk = StringUtil.numberIsOk(pwdController.text, StringUtil.LETTER_DIGIT_REGEX);
     bool isAgainOK = StringUtil.numberIsOk(againController.text, StringUtil.LETTER_DIGIT_REGEX);
    if(!isOk || !isAgainOK){
      toast('请输入6-16位字母和数字组合密码');
      return;
    }

    if((!(StringUtil.isPwd(pwdController.text))) || (!StringUtil.isPwd(againController.text))){
      toast('必须是字母和数字组合密码');
      return;
    }

    if (pwdController.text != againController.text) {
      toast('两次密码输入不一致');
      return;
    }

    if (type == 1) {
      checkCode();
    }
    if(type == 101){
      registerWX();
    }
  }

  checkCode() async {
    print('singdata ----$signData');
    String udidStr = await DeviceUtils.getUDIDStr();
    Map dict = {
      'client':Platform.isIOS?1:2,
      'name': nameController.text,
      'password': pwdController.text,
      'device':udidStr,
      'registToken':signData['registToken']
    };
    DioUtil().post(LoginApi.REGISTERURL, dict, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        pwdLogin();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  pwdLogin() async {
    String udidStr = await DeviceUtils.getUDIDStr();
    String device = await DeviceUtils.getDeviceName();
    Map dict = {
      'client': Platform.isIOS ? 1 : 2,
      'deviceMark': udidStr,
      'deviceModel': device,
      'network': NetUtil.netStr,
      'version': LoginApi.API_VERSION,
      'mobile': phone,
      'password': pwdController.text
    };

    DioUtil().post(LoginApi.MOBILEGETTOKEN, dict, true, () {
    }).then((data) async{
      if (data == null) return;
      if (data!['access_token'] != null) {
    
        await UserDefault.setData(Define.TOKENKEY, data);
        await BaseInfo().getUserInfo();
      } else {
        toast('${data['msg']}');
      }
    });
  }

    //注册微信
  registerWX() async {
    Map userInfo = await UserDefault.getData(Define.WXUSERINFOKEY);
    Map dict = {
      'password': pwdController.text,
      'nickName': userInfo['nickname'],
      //'phone': phone,
      'version': LoginApi.API_VERSION,
      'unionId': userInfo['unionid'],
      'openid': userInfo['openid'],
      'registToken':signData['registToken']
    };
    DioUtil().post(LoginApi.REGISTERURL, dict, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        wechatLoginGetToken(userInfo);
      } else {
        toast('${data['msg']}');
      }
    });
  }

//微信登录获取token
  wechatLoginGetToken(userInfo) async {
    String udidStr = await DeviceUtils.getUDIDStr();
    String device = await DeviceUtils.getDeviceName();
    Map dict = {
      'client': Platform.isIOS ? 1 : 2,
      'deviceMark': udidStr,
      'deviceModel': device,
      'network': NetUtil.netStr,
      'version': LoginApi.API_VERSION,
      'wxAuthId': userInfo['unionid'],
   
    };

    DioUtil()
        .post(LoginApi.WECHATGETTOKEN, dict, true, () {},isShowErrorToast: false)
        .then((data) async{
          if (data == null) return;
      if (data!['access_token'] != null) {
    
        await UserDefault.setData(Define.TOKENKEY, data);
        await BaseInfo().getUserInfo();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
