import 'dart:async';
import 'dart:ffi';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/modules/login/loginView/controllers/login_controller.dart';
import 'package:flutter_mixed/app/modules/networkListen/controllers/network_listen_controller.dart';
import 'package:flutter_mixed/app/utils/net_util.dart';
import 'package:get/get.dart';

import '../../../../common/base_info/info.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/dialog/slider-dialog.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';

class VerifyCodeController extends GetxController {

  TextEditingController editingController = TextEditingController();
  RxString codeText = ''.obs;
  double verifyContainerH = 50.00;
  Timer? codeTimer;
  RxInt timeInt = 60.obs;

  String phone = '';
  String phoneHide = '';
  int type = 0;
  @override
  void onInit() {
    super.onInit();

    phone = Get.arguments['phone'];
    type = Get.arguments['type'];
    phoneHide = phone.replaceRange(3, 7, '****');
    timerStart();
    LoginController loginController = Get.find();

    if (loginController != null) {
      loginController.node.unfocus();
      loginController.height.value = 112;
    }

    codeText.value = editingController.text;
    double screenW = DeviceUtils().width.value;
    if (screenW > 300) {
      verifyContainerH = 50.00;
    } else {
      verifyContainerH = 40.00;
    }
  }

  @override
  void onReady() {
    super.onReady();
    Get.dismiss();
  }

  @override
  void onClose() {
    editingController.dispose();
    if (codeTimer != null) {
      codeTimer!.cancel();
    }
    super.onClose();
  }

  timerStart() {
    timeInt.value = 60;
    codeTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (timer.tick == 60) {
        timeInt.value = 0;
        timer.cancel();
      } else {
        timeInt.value--;
      }
    });
  }

  //发送验证码
  checkPhone() async {
    if (type == 7) {
      DioUtil()
          .get('${LoginApi.CHECK_PHONE_CODE}$phone/type/7', null, false, () {})
          .then((data) {
        if (data == null) return;
        if (data!['code'] == 1) {
          getImageCode();
        } else {
          toast('${data['msg']}');
        }
      });
    } else {
      int localType = type;
      if (type == 101) {
        localType = 1;
      }
      DioUtil()
          .get('${LoginApi.GETSMSCODE}$phone/$localType', null, false, () {})
          .then((data) {
        if (data == null) return;
        if (data!['code'] == 1) {
          toast('发送成功');
          timerStart();
        } else {
          toast('${data['msg']}');
        }
      });
    }
  }

  getImageCode() async {
    DioUtil()
        .get(LoginApi.GET_IMAGE_CODE + phone, null, false, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        //showSliderVerify.value = true;
        String backStr = data['data']['backUrl'];
        String topStr = data['data']['topUrl'];
        SliderDialog(phone, type, backStr, topStr, false).show();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  jumpToPage() {
    if (type == 7) {
      mobileCodeLogin();
    }
    if (type == 1 || type == 101) {
      checkCode();
    }
    if (type == 13) {
      bindingWX();
    }
  }

  mobileCodeLogin() async {
    String udidStr = await DeviceUtils.getUDIDStr();
    String device = await DeviceUtils.getDeviceName();
    Map dict = {
      'client': Platform.isIOS ? 1 : 2,
      'deviceMark': udidStr,
      'deviceModel': device,
      'network': NetUtil.netStr,
      'version': LoginApi.API_VERSION,
      'mobile': phone,
      'code': codeText.value
    };
    DioUtil()
        .post(LoginApi.CODEVERIFYTOKEN, dict, true, () {})
        .then((data) async {
      if (data == null) return;
      if (data != null && data['access_token'] != null) {
        await UserDefault.setData(Define.TOKENKEY, data);
        await BaseInfo().getUserInfo();
      } else {
        if (data != null && data['msg'] != null) {
          toast('${data!['msg']}');
        }
      }
    });
  }

  checkCode() async {
    Map dict = {
      'mobile': phone,
      'code': codeText.value,
    };
    DioUtil().post(LoginApi.VERIFYCODEURL, dict, false, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Get.offAndToNamed('/supplementary-info',
            arguments: {'phone': phone, 'type': type, 'data': data['data']});
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //绑定微信
  bindingWX() async {
    Map userInfo = await UserDefault.getData(Define.WXUSERINFOKEY);
    Map dict = {
      'code': codeText.value,
      'nickName': userInfo['nickname'],
      'phone': phone,
      'version': LoginApi.API_VERSION,
      'wxAuthId': userInfo['unionid'],
      'wxOpenId': userInfo['openid'],
      'type': 1
    };
    DioUtil().post(LoginApi.VERIFYWXCODEURL, dict, false, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        wechatLoginGetToken(userInfo);
      } else {
        toast('${data['msg']}');
      }
    });
  }

//微信登录获取token
  wechatLoginGetToken(userInfo) async {
    String udidStr = await DeviceUtils.getUDIDStr();
    String device = await DeviceUtils.getDeviceName();
    Map dict = {
      'client': Platform.isIOS ? 1 : 2,
      'deviceMark': udidStr,
      'deviceModel': device,
      'network': NetUtil.netStr,
      'version': LoginApi.API_VERSION,
      'wxAuthId': userInfo['unionid']
    };

    DioUtil()
        .post(LoginApi.WECHATGETTOKEN, dict, true, () {},
            isShowErrorToast: false)
        .then((data) async {
      if (data == null) return;
      if (data!['access_token'] != null) {
        await UserDefault.setData(Define.TOKENKEY, data);
        await BaseInfo().getUserInfo();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
