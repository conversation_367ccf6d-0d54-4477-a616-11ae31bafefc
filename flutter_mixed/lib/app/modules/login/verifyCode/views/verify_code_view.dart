import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/login/loginView/controllers/login_controller.dart';
import '../../../../common/config/config.dart';
import 'package:get/get.dart';
import '../controllers/verify_code_controller.dart';

// 手机验证码界面
class VerifyCodeView extends GetView<VerifyCodeController> {
  const VerifyCodeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => Stack(
            children: [
              Container(
                alignment: Alignment.centerLeft,
                width: double.infinity,
                height: DeviceUtils().height.value,
                padding: EdgeInsets.only(left: 16, right: 16),
                decoration: BoxDecoration(
                    image: DecorationImage(
                        fit: BoxFit.fitWidth,
                        image: AssetImage(
                            'assets/images/3.0x/login_backGround.png'))),
                child: Column(
                  children: [
                    SettingWidget().backLoginBackBtn(onPressed: () {
                      Get.back();
                    }),
                    SizedBox(
                      height: 38,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      width: double.infinity,
                      child: Text(
                        '输入手机号验证码',
                        style: TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 27),
                      ),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      width: double.infinity,
                      child: Text(
                        '请输入发送至+86${controller.phoneHide}的4位验证码，有效期5分钟',
                        style: TextStyle(
                            color: ColorConfig.desTextColor, fontSize: 14),
                      ),
                    ),
                    SizedBox(
                      height: 35,
                    ),
                    Container(
                      height: controller.verifyContainerH,
                      width: DeviceUtils().width.value - 40,
                      child: Stack(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Container(
                                alignment: AlignmentDirectional.center,
                                width: controller.verifyContainerH,
                                height: controller.verifyContainerH,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7.5),
                                    border: Border.all(
                                        color:
                                            controller.codeText.value.isNotEmpty
                                                ? ColorConfig.themeCorlor
                                                : ColorConfig.lineColor)),
                                child: Text(
                                  controller.codeText.value.isNotEmpty
                                      ? controller.codeText.value
                                          .substring(0, 1)
                                      : '',
                                  style: TextStyle(
                                      color: ColorConfig.mainTextColor,
                                      fontSize: 24.0,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              Container(
                                alignment: AlignmentDirectional.center,
                                width: controller.verifyContainerH,
                                height: controller.verifyContainerH,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7.5),
                                    border: Border.all(
                                        color:
                                            controller.codeText.value.length > 1
                                                ? ColorConfig.themeCorlor
                                                : ColorConfig.lineColor)),
                                child: Text(
                                  controller.codeText.value.length > 1
                                      ? controller.codeText.value
                                          .substring(1, 2)
                                      : '',
                                  style: TextStyle(
                                      color: ColorConfig.mainTextColor,
                                      fontSize: 24.0,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              Container(
                                height: 2,
                                width: 11,
                                color: ColorConfig.mainTextColor,
                              ),
                              Container(
                                alignment: AlignmentDirectional.center,
                                width: controller.verifyContainerH,
                                height: controller.verifyContainerH,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7.5),
                                    border: Border.all(
                                        color:
                                            controller.codeText.value.length > 2
                                                ? ColorConfig.themeCorlor
                                                : ColorConfig.lineColor)),
                                child: Text(
                                  controller.codeText.value.length > 2
                                      ? controller.codeText.value
                                          .substring(2, 3)
                                      : '',
                                  style: TextStyle(
                                      color: ColorConfig.mainTextColor,
                                      fontSize: 24.0,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              Container(
                                alignment: AlignmentDirectional.center,
                                width: controller.verifyContainerH,
                                height: controller.verifyContainerH,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7.5),
                                    border: Border.all(
                                        color:
                                            controller.codeText.value.length > 3
                                                ? ColorConfig.themeCorlor
                                                : ColorConfig.lineColor)),
                                child: Text(
                                  controller.codeText.value.length > 3
                                      ? controller.codeText.value
                                          .substring(3, 4)
                                      : '',
                                  style: TextStyle(
                                      color: ColorConfig.mainTextColor,
                                      fontSize: 24.0,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.only(left: 0),
                            child: TextField(
                              controller: controller.editingController,
                              textAlign: TextAlign.start,
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(4),
                              ],
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                controller.codeText.value = value;
                                if (value.length == 4) {
                                  controller.jumpToPage();
                                }
                              },
                              showCursor: false,
                              style: TextStyle(
                                color: Colors.transparent,
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                                letterSpacing: 36,
                              ),
                              decoration: InputDecoration(
                                  hintText: "",
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.only(left: 30)),
                              // )
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Container(
                      width: double.infinity,
                      alignment: Alignment.centerLeft,
                      child: CupertinoButton(
                          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                          color: ColorConfig.whiteColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(3)),
                          pressedOpacity: 0.5,
                          child: Text(
                            controller.timeInt.value == 0
                                ? '获取验证码'
                                : '${controller.timeInt.value}s可重新获取验证码',
                            style: TextStyle(
                                color: controller.timeInt.value == 0
                                    ? ColorConfig.themeCorlor
                                    : ColorConfig.desTextColor,
                                fontSize: 14),
                          ),
                          onPressed: () {
                            if (controller.timeInt.value == 0) {
                              controller.checkPhone();
                            }
                          }),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    Offstage(
                      offstage: controller.type == 1 || controller.type == 101,
                      child: Container(
                        width: double.infinity,
                        height: 25,
                        alignment: Alignment.centerLeft,
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            color: ColorConfig.whiteColor,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(3)),
                            pressedOpacity: 0.5,
                            child: const Text(
                              '密码登录',
                              style: TextStyle(
                                  color: ColorConfig.themeCorlor, fontSize: 14),
                            ),
                            onPressed: () {
                              Get.toNamed('/pwd-login',
                                  arguments: {'phone': controller.phone});
                            }),
                      ),
                    )
                  ],
                ),
              )
            ],
          )),
    );
  }
}
