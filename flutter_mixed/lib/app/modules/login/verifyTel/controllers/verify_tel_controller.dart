import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/dialog/slider-dialog.dart';
import 'package:get/get.dart';

import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../../../utils/string-util.dart';

class VerifyTelController extends GetxController {

  TextEditingController telController = TextEditingController();
  RxBool isAgree = false.obs;
  int type = 1;
  String backStr = '';
  String topStr = '';

  RxString phone = ''.obs;
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments['phone'] != null) {
      telController.text = Get.arguments['phone'];
      phone.value = Get.arguments['phone'];
    }
    type = Get.arguments['type'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    telController.dispose();
    super.onClose();
  }

  jugeFunction() {
        bool isOk = StringUtil.numberIsOk(telController.text, StringUtil.TEL_REGEX);
    if(!isOk){
      toast('请输入正确手机号码');
      return;
    }
    if (!isAgree.value) {
      toast('请阅读并同意《服务协议》及《隐私政策》');
      return;
    }
    if (telController.text.isEmpty) {
      toast('请输入手机号');
      return;
    }
    if (type == 1) {
      checkPhone();
    }
    if (type == 13) {
      verifyWXTel();
    }
  }

  checkPhone() async {
    DioUtil().get(
        '${LoginApi.CHECK_PHONE_CODE}${telController.text}/type/$type',
        null,
        false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        getImageCode();
      } else if (data['code'] == 1101) {
        toast('手机号未注册');
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getImageCode() async {
    DioUtil().get(LoginApi.GET_IMAGE_CODE + telController.text, null, false,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        backStr = data['data']['backUrl'];
        topStr = data['data']['topUrl'];
        SliderDialog(telController.text, type, backStr, topStr, true).show();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //校验手机号是否绑定微信
  verifyWXTel() async {
    DioUtil().get(LoginApi.VERIFYWXDDURL + telController.text, null, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        type = 13;
        getImageCode();
      } else if (data['code'] == 1104) {
        //手机号未注册
        type = 101;//本地自定义微信未注册
        getImageCode();
      } else if (data['code'] == 1301) {
        toast('你输入的手机号已经关联了微信，请关联其他手机号');
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
