import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/dialog/slider-dialog.dart';

import 'package:get/get.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../common/config/config.dart';
import '../controllers/verify_tel_controller.dart';

class VerifyTelView extends GetView<VerifyTelController> {
  const VerifyTelView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
        body: Stack(
      children: [
        Container(
          alignment: Alignment.centerLeft,
          width: double.infinity,
          height: DeviceUtils().height.value,
          padding: EdgeInsets.only(left: 16, right: 16),
          decoration: BoxDecoration(
              image: DecorationImage(
                  fit: BoxFit.fitWidth,
                  image:
                      AssetImage('assets/images/3.0x/login_backGround.png'))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SettingWidget().backLoginBackBtn(onPressed: () {
                Get.back();
              }),
              SizedBox(
                height: 35,
              ),
              Container(
                width: double.infinity,
                //height: 35,
                alignment: Alignment.centerLeft,
                child: Text(controller.type==1?
                  '新用户注册':'首次使用此微信登录,请先关联手机号',
                  style:
                      TextStyle(color: ColorConfig.mainTextColor, fontSize: 27),
                      maxLines: null,
                ),
              ),
              SizedBox(
                height: 45,
              ),
              Container(
                padding: EdgeInsets.only(left: 5, right: 15),
                width: DeviceUtils().width.value - 32,
                height: 50,
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(width: 1, color: ColorConfig.lineColor)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 65,
                      height: 50,
                      alignment: Alignment.center,
                      child: Text(
                        '+86',
                        style: TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 18),
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 20,
                      color: ColorConfig.lineColor,
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                        child: TextField(
                          controller: controller.telController,
                      textInputAction: TextInputAction.search, inputFormatters: <TextInputFormatter>[
                            LengthLimitingTextInputFormatter(11)//限制长度
                          ],
                      keyboardType: TextInputType.phone,
                      style: const TextStyle(
                        color: ColorConfig.mainTextColor,
                        fontSize: 17,
                      ),
                      onChanged: (value) {
                        controller.phone.value = value;
                      },
                      decoration: InputDecoration(
                          contentPadding:
                              const EdgeInsets.only(top: 0, bottom: 0),
                          border: const OutlineInputBorder(
                              borderSide: BorderSide.none),
                          hintText: '请输入你的手机号',
                          hintStyle: const TextStyle(
                            color: ColorConfig.desTextColor,
                            fontSize: 17,
                          )),
                    ))
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Container(
                width: double.infinity,
                height: 25,
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {
                        controller.isAgree.value = !controller.isAgree.value;
                      },
                      child: Container(
                        width: 25,
                        height: 25,
                        alignment: Alignment.centerLeft,
                        child: Container(
                          width: 15,
                          height: 15,
                          child: Image.asset(
                              controller.isAgree.value?'assets/images/3.0x/login_selected.png':'assets/images/3.0x/login_unselect.png'),
                        ),
                      ),
                    ),
                    Container(
                      width: 300,
                      height: 25,
                      alignment: Alignment.centerLeft,
                      child: RichText(
                          text: TextSpan(
                              text: '我已阅读并同意',
                              style: TextStyle(
                                  color: ColorConfig.desTextColor,
                                  fontSize: 14),
                              children: [
                            TextSpan(
                              recognizer: TapGestureRecognizer()..onTap = () {
                                Get.toNamed('/web-detail',arguments: {'url':'${LoginApi.PROTOCAOLURL}','title':'服务协议'});
                              },
                              text: '服务协议',
                              style: TextStyle(
                                  color: ColorConfig.themeCorlor, fontSize: 14),
                            ),
                            TextSpan(
                              text: '和',
                              style: TextStyle(
                                  color: ColorConfig.desTextColor,
                                  fontSize: 14),
                            ),
                            TextSpan(
                              recognizer: TapGestureRecognizer()..onTap = () {
                                Get.toNamed('/web-detail',arguments: {'url':LoginApi.privacyUrl(),'title':'隐私政策'});
                              },
                              text: '隐私政策',
                              style: TextStyle(
                                  color: ColorConfig.themeCorlor, fontSize: 14),
                            ),
                          ])),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
        Positioned(
            left: 16,
            right: 16,
            bottom: DeviceUtils().bottom.value + 16,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.only(left: 0, right: 0),
              height: 50,
              child: CupertinoButton(
                  padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                  color: controller.phone.value.isNotEmpty?ColorConfig.themeCorlor:ColorConfig.btnGrayColor,
                  borderRadius: const BorderRadius.all(Radius.circular(7.5)),
                  pressedOpacity: 0.5,
                  child: const Text(
                    '下一步',
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  ),
                  onPressed: () {
                    controller.jugeFunction();
                    
                  }),
            ))
      ],
    )));
  }
}
