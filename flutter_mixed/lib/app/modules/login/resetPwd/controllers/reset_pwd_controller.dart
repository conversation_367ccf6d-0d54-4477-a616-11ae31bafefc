import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../common/api/LoginApi.dart';
import '../../../../common/dialog/slider-dialog.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../../../utils/string-util.dart';

class ResetPwdController extends GetxController {

  TextEditingController codeController = TextEditingController();
  TextEditingController pwdController = TextEditingController();
  TextEditingController againController = TextEditingController();
  RxBool isOpen = false.obs;
  RxBool isAgainOpen = false.obs;
  RxDouble top = 0.00.obs;
  RxBool isBlue = false.obs;

  Timer? codeTimer;
  RxInt timeInt = 0.obs;

  String phone = '';
  String phoneHide = '';
  @override
  void onInit() {
    super.onInit();

    phone = Get.arguments['phone'];
    codeButtonClick();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    codeController.dispose();
    pwdController.dispose();
    againController.dispose();
    if (codeTimer != null) {
      codeTimer!.cancel();
    }
    super.onClose();
  }

  getImageCode() async {
    DioUtil().get(LoginApi.GET_IMAGE_CODE + phone, null, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        //showSliderVerify.value = true;
        String backStr = data['data']['backUrl'];
        String topStr = data['data']['topUrl'];
        SliderDialog(phone, 4, backStr, topStr, false).show();
 
        
      } else {
        toast('${data['msg']}');
      }
    });
  }

  codeButtonClick() async {
    DioUtil().get('${LoginApi.CHECK_PHONE_CODE}$phone/type/4', null, false, () {
    }).then((data) {
     if (data == null) return;
      if (data!['code'] == 1) {
        getImageCode();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  timerStart() {
    timeInt.value = 60;
    codeTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (timer.tick == 60) {
        timeInt.value = 0;
        timer.cancel();
      } else {
        timeInt.value--;
      }
    });
  }

  forgetPwd() async {
    if (codeController.text.isEmpty) {
      toast('请输入验证码');
      return;
    }
    if (pwdController.text.isEmpty || againController.text.isEmpty) {
      toast('请输入密码');
      return;
    }
    bool isOk = StringUtil.numberIsOk(
        pwdController.text, StringUtil.LETTER_DIGIT_REGEX);
    bool isAgainOK = StringUtil.numberIsOk(
        againController.text, StringUtil.LETTER_DIGIT_REGEX);
    if (!isOk || !isAgainOK) {
      toast('请输入6-16位字母和数字组合密码');
      return;
    }
    if (pwdController.text != againController.text) {
      toast('两次密码输入不一致');
      return;
    }

    Map dict = {
      'mobile': phone,
      'code': codeController.text,
      'password': pwdController.text,
      'version': LoginApi.API_VERSION,
    };
    DioUtil().post(LoginApi.FORGETPWDURL, dict, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('密码设置成功');
        Get.back();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
