import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../common/config/config.dart';
import '../controllers/reset_pwd_controller.dart';

class ResetPwdView extends GetView<ResetPwdController> {
  const ResetPwdView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
        body: Obx(() => Stack(
              children: [
                Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    top: controller.top.value,
                    child: SingleChildScrollView(
                      child: Container(
                        alignment: Alignment.centerLeft,
                        width: double.infinity,
                        height: DeviceUtils().height.value,
                        padding: EdgeInsets.only(left: 16, right: 16),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                fit: BoxFit.fitWidth,
                                image: AssetImage(
                                    'assets/images/3.0x/login_backGround.png'))),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SettingWidget().backLoginBackBtn(onPressed: () {
                                  Get.back();
                                }),
                                SizedBox(
                                  height: 35,
                                ),
                                Container(
                                  width: double.infinity,
                         
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '设置密码',
                                    style: TextStyle(
                                        color: ColorConfig.mainTextColor,
                                        fontSize: 27),
                                  ),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  width: double.infinity,
                                  height: 40,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '密码需包含数字和英文字母两种类型，且长度在6-16个字符之间',
                                    style: TextStyle(
                                        color: ColorConfig.mainTextColor,
                                        fontSize: 14),
                                  ),
                                ),
                                SizedBox(
                                  height: 30,
                                ),
                                Container(
                                  width: double.infinity,
                                  height: 50,
                                  padding: EdgeInsets.only(left: 20),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.lineColor)),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: TextField(
                                        onTap: () {},
                                        onChanged: (value) {
                                          if (controller.codeController.text
                                                  .isNotEmpty &&
                                              controller.pwdController.text
                                                  .isNotEmpty &&
                                              controller.againController.text
                                                  .isNotEmpty) {
                                            controller.isBlue.value = true;
                                          } else {
                                            controller.isBlue.value = false;
                                          }
                                        },
                                        textInputAction: TextInputAction.done,
                                        controller: controller.codeController,
                                        //focusNode: controller.node,
                                        style: const TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 17,
                                        ),
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    top: 0, bottom: 0),
                                            border: const OutlineInputBorder(
                                                borderSide: BorderSide.none),
                                            hintText: '请输入验证码',
                                            hintStyle: const TextStyle(
                                              color: ColorConfig.desTextColor,
                                              fontSize: 17,
                                            )),
                                      )),
                                      InkWell(
                                        child: Container(
                                            width: 115,
                                            height: 40,
                                            alignment: Alignment.centerRight,
                                            padding: EdgeInsets.only(right: 15),
                                            child: CupertinoButton(
                                                padding:
                                                    const EdgeInsets.fromLTRB(
                                                        0, 0, 0, 0),
                                                color: ColorConfig.whiteColor,
                                                borderRadius:
                                                    const BorderRadius.all(
                                                        Radius.circular(3)),
                                                pressedOpacity: 0.5,
                                                child: Text(
                                                  controller.timeInt.value == 0
                                                      ? '获取验证码'
                                                      : '${controller.timeInt.value}s',
                                                  style: TextStyle(
                                                      color: controller.timeInt
                                                                  .value ==
                                                              0
                                                          ? ColorConfig
                                                              .themeCorlor
                                                          : ColorConfig
                                                              .desTextColor,
                                                      fontSize: 14),
                                                ),
                                                onPressed: () {
                                                  if (controller
                                                          .timeInt.value ==
                                                      0) {
                                                    controller.codeButtonClick();
                                                  }
                                                }),
                                          ),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 30,
                                ),
                                Container(
                                  width: double.infinity,
                                  height: 50,
                                  padding: EdgeInsets.only(left: 20),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.lineColor)),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: TextField(
                                        onTap: () {},
                                        onChanged: (value) {
                                          if (controller.codeController.text
                                                  .isNotEmpty &&
                                              controller.pwdController.text
                                                  .isNotEmpty &&
                                              controller.againController.text
                                                  .isNotEmpty) {
                                            controller.isBlue.value = true;
                                          } else {
                                            controller.isBlue.value = false;
                                          }
                                        },
                                        textInputAction: TextInputAction.done,
                                        controller: controller.pwdController,
                                        obscureText: !controller.isOpen.value,
                                        //focusNode: controller.node,
                                        style: const TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 17,
                                        ),
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    top: 0, bottom: 0),
                                            border: const OutlineInputBorder(
                                                borderSide: BorderSide.none),
                                            hintText: '请输入密码',
                                            hintStyle: const TextStyle(
                                              color: ColorConfig.desTextColor,
                                              fontSize: 17,
                                            )),
                                      )),
                                      InkWell(
                                        onTap: () {
                                          controller.isOpen.value =
                                              !controller.isOpen.value;
                                        },
                                        child: Container(
                                          width: 40,
                                          height: 40,
                                          alignment: Alignment.center,
                                          child: Container(
                                            width: 17,
                                            height: 13,
                                            child: Image.asset(controller
                                                    .isOpen.value
                                                ? 'assets/images/3.0x/login_pwd_open.png'
                                                : 'assets/images/3.0x/login_pwd_close.png'),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 30,
                                ),
                                Container(
                                  width: double.infinity,
                                  height: 50,
                                  padding: EdgeInsets.only(left: 20),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.lineColor)),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                          child: TextField(
                                        onTap: () {},
                                        onChanged: (value) {
                                          if (controller.codeController.text
                                                  .isNotEmpty &&
                                              controller.pwdController.text
                                                  .isNotEmpty &&
                                              controller.againController.text
                                                  .isNotEmpty) {
                                            controller.isBlue.value = true;
                                          } else {
                                            controller.isBlue.value = false;
                                          }
                                        },
                                        textInputAction: TextInputAction.done,
                                        controller: controller.againController,
                                        //focusNode: controller.node,
                                        style: const TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 17,
                                        ),
                                        obscureText: !controller.isAgainOpen.value,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    top: 0, bottom: 0),
                                            border: const OutlineInputBorder(
                                                borderSide: BorderSide.none),
                                            hintText: '请再次输入密码',
                                            hintStyle: const TextStyle(
                                              color: ColorConfig.desTextColor,
                                              fontSize: 17,
                                            )),
                                      )),
                                      InkWell(
                                        onTap: () {
                                          controller.isAgainOpen.value =
                                              !controller.isAgainOpen.value;
                                        },
                                        child: Container(
                                          width: 40,
                                          height: 40,
                                          alignment: Alignment.center,
                                          child: Container(
                                            width: 17,
                                            height: 13,
                                            child: Image.asset(controller
                                                    .isAgainOpen.value
                                                ? 'assets/images/3.0x/login_pwd_open.png'
                                                : 'assets/images/3.0x/login_pwd_close.png'),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              width: double.infinity,
                              alignment: Alignment.topCenter,
                              height: 50 + DeviceUtils().bottom.value + 16,
                              child: Container(
                                width: double.infinity,
                                height: 50,
                                child: CupertinoButton(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    color: controller.isBlue.value
                                        ? ColorConfig.themeCorlor
                                        : ColorConfig.btnGrayColor,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(7.5)),
                                    pressedOpacity: 0.5,
                                    child: const Text(
                                      '下一步',
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 14),
                                    ),
                                    onPressed: () {
                                      controller.forgetPwd();
                                    }),
                              ),
                            )
                          ],
                        ),
                      ),
                    )),
              ],
            )));
  }
}
