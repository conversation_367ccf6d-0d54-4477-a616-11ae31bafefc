import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/env/env_convert_component.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:fluwx/fluwx.dart';
import '../../../../common/config/config.dart';

import 'package:get/get.dart';

import '../../../../common/config/string_const.dart';
import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
            backgroundColor: ColorConfig.backgroundColor,
            body: Stack(
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.vertical,
              controller: controller.scrollController,
              child: Container(
                alignment: Alignment.centerLeft,
                width: double.infinity,
                height: DeviceUtils().height.value,
                padding: EdgeInsets.only(left: 16, right: 16),
                decoration: BoxDecoration(
                    image: DecorationImage(
                        fit: BoxFit.fitWidth,
                        image: AssetImage(
                            'assets/images/3.0x/login_backGround.png'))),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 112,
                        ),
                        // Obx(() => SizedBox(height: controller.height.value)),

                        Row(
                          children: [
                            InkWell(
                              onLongPress: () {
                                controller.useTokenTest();
                              },
                              child: Container(
                                width: 65,
                                height: 65,
                                alignment: Alignment.centerLeft,
                                child: Image.asset(
                                    'assets/images/3.0x/about_icon_clear.png'),
                              ),
                            ),
                            Expanded(child: ConvertEnvComponent(updateParent: (){
                              controller.updateEnvName();
                            },)),
                            // ()
                          ],
                        ),
                        25.gap,
                        Text('${controller.envName}'),
                        Container(
                          width: double.infinity,
                          alignment: Alignment.centerLeft,
                          child: const Text('欢迎使用$appName', style: TextStyle(
                                color: ColorConfig.mainTextColor, fontSize: 27),
                          ),
                        ),
                        85.gap,
                        Container(
                          padding: EdgeInsets.only(left: 5, right: 15),
                          width: DeviceUtils().width.value - 32,
                          height: 50,
                          alignment: Alignment.centerLeft,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  width: 1, color: ColorConfig.lineColor)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                width: 65,
                                height: 50,
                                alignment: Alignment.center,
                                child: Text(
                                  '+86',
                                  style: TextStyle(
                                      color: ColorConfig.mainTextColor,
                                      fontSize: 18),
                                ),
                              ),
                              Container(
                                width: 1,
                                height: 20,
                                color: ColorConfig.lineColor,
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  child: TextField(
                                onTap: () {
                                  controller.isBack = false;
                                },
                                onChanged: (value) {
                                  controller.phone.value =
                                      controller.telController.text;
                                },
                                keyboardType: TextInputType.phone,
                                textInputAction: TextInputAction.search,
                                controller: controller.telController,
                                focusNode: controller.node,
                                style: const TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 17,
                                ),
                                inputFormatters: <TextInputFormatter>[
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(11) //限制长度
                                ],
                                decoration: InputDecoration(
                                    contentPadding: const EdgeInsets.only(
                                        top: 0, bottom: 0),
                                    border: const OutlineInputBorder(
                                        borderSide: BorderSide.none),
                                    hintText: '请输入你的手机号',
                                    hintStyle: const TextStyle(
                                      color: ColorConfig.desTextColor,
                                      fontSize: 17,
                                    )),
                              ))
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 16,
                        ),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.only(left: 0, right: 0),
                          height: 50,
                          child: CupertinoButton(
                              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                              color: controller.phone.value.isNotEmpty
                                  ? ColorConfig.themeCorlor
                                  : ColorConfig.btnGrayColor,
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(7.5)),
                              pressedOpacity: 0.5,
                              child: const Text(
                                '下一步',
                                style: TextStyle(
                                    color: Colors.white, fontSize: 14),
                              ),
                              onPressed: () async {
                                controller.node.unfocus();
                                controller.checkPhone(context);
                              }),
                        ),
                        SizedBox(
                          height: 18,
                        ),
                        Container(
                          width: double.infinity,
                          height: 25,
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  controller.isAgree.value =
                                      !controller.isAgree.value;
                                },
                                child: Container(
                                  width: 25,
                                  height: 25,
                                  alignment: Alignment.centerLeft,
                                  child: Image.asset(
                                    controller.isAgree.value
                                        ? 'assets/images/3.0x/login_selected.png'
                                        : 'assets/images/3.0x/login_unselect.png',
                                    fit: BoxFit.contain,
                                    width: 17,
                                    height: 17,
                                  ),
                                ),
                              ),
                              Container(
                                width: 300,
                                height: 25,
                                alignment: Alignment.centerLeft,
                                child: RichText(
                                    text: TextSpan(
                                        text: '我已阅读并同意',
                                        style: TextStyle(
                                            color: ColorConfig.desTextColor,
                                            fontSize: 14),
                                        children: [
                                      TextSpan(
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            Get.toNamed('/web-detail',
                                                arguments: {
                                                  'url': '${LoginApi.PROTOCAOLURL}',
                                                  'title': '服务协议'
                                                });
                                          },
                                        text: '服务协议',
                                        style: TextStyle(
                                            color: ColorConfig.themeCorlor,
                                            fontSize: 14),
                                      ),
                                      TextSpan(
                                        text: '和',
                                        style: TextStyle(
                                            color: ColorConfig.desTextColor,
                                            fontSize: 14),
                                      ),
                                      TextSpan(
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            Get.toNamed('/web-detail',
                                                arguments: {
                                                  'url':
                                                      LoginApi.privacyUrl(),
                                                  'title': '隐私政策'
                                                });
                                          },
                                        text: '隐私政策',
                                        style: TextStyle(
                                            color: ColorConfig.themeCorlor,
                                            fontSize: 14),
                                      ),
                                    ])),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    Container(
                      height: 108 + DeviceUtils().bottom.value,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            width: double.infinity,
                            height: 22,
                            alignment: Alignment.center,
                            child: RichText(
                                text: TextSpan(
                                    text: '还没有账号？',
                                    style: TextStyle(
                                        color: ColorConfig.desTextColor,
                                        fontSize: 14),
                                    children: [
                                  TextSpan(
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        Get.toNamed(Routes.VERIFY_TEL,
                                            arguments: {
                                              'phone':
                                                  controller.telController.text,
                                              'type': 1
                                            });
                                      },
                                    text: '立即注册',
                                    style: TextStyle(
                                        color: ColorConfig.themeCorlor,
                                        fontSize: 14),
                                  ),
                                ])),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Offstage(
                            offstage: !controller.isInstall.value,
                            child: InkWell(
                              onTap: () {
                                controller.weChatLogin();
                              },
                              child: Container(
                                width: double.infinity,
                                height: 50,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7.5),
                                    border: Border.all(
                                        width: 1,
                                        color: ColorConfig.lineColor)),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 21,
                                      height: 17,
                                      child: Image.asset(
                                          'assets/images/3.0x/login_weChat.png'),
                                    ),
                                    SizedBox(
                                      width: 3,
                                    ),
                                    Text(
                                      '微信快速登录',
                                      style: TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 16),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        )));
  }
}
