import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/dialog/msg-dialog.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/utils/net_util.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fluwx/fluwx.dart';
import 'package:get/get.dart';

import '../../../../common/api/Define.dart';
import '../../../../common/config/app_config.dart';
import '../../../../common/config/string_const.dart';
import '../../../../common/dialog/slider-dialog.dart';
import '../../../../utils/http.dart';
import '../../../../../logger/logger.dart';
import '../../../../utils/storage.dart';
import '../../../../utils/string-util.dart';
import '../../../helper/wechat.dart';
import '../../../networkListen/controllers/network_listen_controller.dart';

class LoginController extends GetxController with WidgetsBindingObserver {
  TextEditingController telController = TextEditingController();
  FocusNode node = FocusNode();
  ScrollController scrollController = ScrollController();
  RxBool isAgree = false.obs; //是否同意
  RxDouble height = 112.00.obs; //上移高度
  bool isBack = false;
  RxBool showSliderVerify = false.obs;
  RxDouble sliderValue = 0.00.obs;
  RxDouble offsetX = 0.00.obs;
  double startValue = 0.00;
  double moveValue = 0.00;
  double scale = 0.00;
  RxString phone = ''.obs;

  String backStr = '';
  String topStr = '';
  RxBool isInstall = false.obs;
  @override
  void onInit() async {
    super.onInit();
    Get.put(NetworkListenController());

    WechatHelper.instance.isLoginPage = true;

    isLoginPage = true;
    updateEnvName();
    isInstall.value = await WechatHelper.instance.wechatHasInstall();
    phone.value = telController.text;
    WidgetsBinding.instance.addObserver(this);
    // wxStream =
    //     weChatResponseEventHandler.distinct((a, b) => a == b).listen((res) {
    //   logger('res123====$res');
    //   if (res is WeChatAuthResponse) {
    //     int errCode = res.errCode!;
    //     logger('微信登录返回值：ErrCode :$errCode  code:${res.code}  ');
    //
    //     if (errCode == 0) {
    //       String code = res.code!;
    //       wxLoginAuth(code);
    //     } else if (errCode == -4) {
    //       //用户拒绝授权
    //     } else if (errCode == -2) {
    //       //用户取消授权
    //     }
    //   }
    // });

    WechatHelper.instance.listenLoginResult(callback: (userInfo) {
      wechatLoginGetToken(userInfo);
    });
  }

  fetchEnvName() => flutterTarget();

  RxString envName = ''.obs;

  updateEnvName() {
    envName.value = fetchEnvName();
  }

  @override
  void didChangeMetrics() {
    // TODO: implement didChangeMetrics
    super.didChangeMetrics();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      double keyHeight = MediaQuery.of(Get.context!).viewInsets.bottom;
      if (!isBack) {
        height.value = 112 - keyHeight / 300 * 112;
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();

    telController.dispose();
    scrollController.dispose();
    WidgetsBinding.instance.removeObserver(this);

    WechatHelper.instance.cancel();
    WechatHelper.instance.isLoginPage = false;
  }

  checkPhone(BuildContext context) async {
    if (telController.text.isEmpty) {
      toast('请输入手机号');
      return;
    }
    bool isOk = StringUtil.numberIsOk(telController.text, StringUtil.TEL_REGEX);
    if (!isOk) {
      toast('请输入正确手机号码');
      return;
    }
    if (!isAgree.value) {
      // toast('请阅读并同意《服务协议》及《隐私政策》');

      _alertAgreePrivacy(context);

      return;
    }
    _didLogin();
  }

  _didLogin(){
    if (EnvConfig.mEnv == Env.TokenProduct) {
      Get.toNamed('/pwd-login', arguments: {'phone': telController.text});
    } else {
      _requestVerifyMobile();
    }
  }

  _alertAgreePrivacy(BuildContext context) {
    MsgDiaLog('', '你确认已经阅读并同意服务协议和隐私协议吗', '取消', '确认', () {
      Navigator.of(context).pop();
    }, () {
      Navigator.of(context).pop();
      _didLogin();
    }).show();
  }

  _requestVerifyMobile() {
    DioUtil()
        .get('${LoginApi.CHECK_PHONE_CODE}${telController.text}/type/7', null,
            false, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        getImageCode();
      } else if (data['code'] == 1104) {
        MsgDiaLog('', '此手机号尚未注册$appName，请注册后登录', '好的', '立即注册', () {
          Navigator.of(Get.context!).pop();
        }, () {
          Navigator.of(Get.context!).pop();
          Get.toNamed('/verify-tel',
              arguments: {'phone': telController.text, 'type': 1});
        }).show();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getImageCode() async {
    DioUtil()
        .get(LoginApi.GET_IMAGE_CODE + telController.text, null, false, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        //showSliderVerify.value = true;
        backStr = data['data']['backUrl'];
        topStr = data['data']['topUrl'];
        SliderDialog(telController.text, 7, backStr, topStr, true).show();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  checkSlider() async {
    Map dict = {
      'newId': backStr,
      'oriId': topStr,
      'phone': telController.text,
      'scale': scale,
      'type': 7
    };

    DioUtil()
        .post(LoginApi.CHECK_IMAGE_SEND_CODE, dict, false, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        showSliderVerify.value = false;
        Get.toNamed('/verify-code',
            arguments: {'phone': telController.text, 'type': 0});
      } else {
        if (data['code'] == 1110) {
          //滑块验证成功 其他失败形式
          Get.toNamed('/verify-code',
              arguments: {'phone': telController.text, 'type': 0});
        }
        toast('${data['msg']}');
      }
    });
  }

  //微信登录
  weChatLogin() {
    if (!isAgree.value) {
      toast('请阅读并同意《服务协议》及《隐私政策》');
      return;
    }

    WechatHelper.instance.login();
  }

  void wxLoginAuth(String code) async {
    WeChat().wechatLoginAuth(code).then((response) {
      Map map = json.decode(response.data);
      WeChat()
          .getWechatUserInfo(map["access_token"], map["openid"])
          .then((response) {
        Map userInfo = json.decode(response.data);
        UserDefault.setData(Define.WXUSERINFOKEY, userInfo);
        wechatLoginGetToken(userInfo);
      });
    });
  }

  //微信登录获取token
  wechatLoginGetToken(userInfo) async {
    String udidStr = await DeviceUtils.getUDIDStr();
    String device = await DeviceUtils.getDeviceName();
    Map dict = {
      'client': Platform.isIOS ? 1 : 2,
      'deviceMark': udidStr,
      'deviceModel': device,
      'network': NetUtil.netStr,
      'version': LoginApi.API_VERSION,
      'wxAuthId': userInfo['unionid']
    };

    DioUtil().post(LoginApi.WECHATGETTOKEN, dict, true, (jsonData) {
      if (jsonData is String) {
        if (jsonData.contains('301')) {
          Get.toNamed('/verify-tel', arguments: {'phone': '', 'type': 13});
        }
      } else {
        if (jsonData['code'] == 0) {
          Get.toNamed('/verify-tel', arguments: {'phone': '', 'type': 13});
        }
      }
    }, isShowErrorToast: false).then((data) async {
      if (data == null) return;
      if (data!['access_token'] != null) {
        await UserDefault.setData(Define.TOKENKEY, data);
        await BaseInfo().getUserInfo();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //token调试
  useTokenTest() async {
    if (EnvConfig.mEnv == Env.TokenProduct) {
      await UserDefault.setData(Define.TOKENKEY, {'access_token': Host.token});
      await BaseInfo().getUserInfo();
    }
  }
}
