import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';

import 'package:get/get.dart';
import '../../../../common/config/config.dart';
import '../controllers/pwd_login_controller.dart';

class PwdLoginView extends GetView<PwdLoginController> {
  const PwdLoginView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      body: Obx(() => Stack(
            children: [
              Container(
                alignment: Alignment.centerLeft,
                width: double.infinity,
                height: DeviceUtils().height.value,
                padding: EdgeInsets.only(left: 16, right: 16),
                decoration: BoxDecoration(
                    image: DecorationImage(
                        fit: BoxFit.fitWidth,
                        image: AssetImage(
                            'assets/images/3.0x/login_backGround.png'))),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SettingWidget().backLoginBackBtn(onPressed: () {
                      Get.back();
                    }),
                    SizedBox(
                      height: 35,
                    ),
                    Container(
                      width: double.infinity,
                 
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '输入密码',
                        style: TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 27),
                      ),
                    ),
                    SizedBox(
                      height: 55,
                    ),
                    Container(
                      width: double.infinity,
                      height: 50,
                      padding: EdgeInsets.only(left: 20),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              width: 1, color: ColorConfig.lineColor)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                              child: TextField(
                            onTap: () {},
                            onChanged: (value) {
                              controller.pwdStr.value = controller.pwdController.text;
                            },
                            textInputAction: TextInputAction.done,
                            keyboardType: TextInputType.visiblePassword,
                            controller: controller.pwdController,
                            obscureText: !controller.isOpen.value,
                            //focusNode: controller.node,
                            style: const TextStyle(
                              color: ColorConfig.mainTextColor,
                              fontSize: 17,
                            ),
                            decoration: InputDecoration(
                                contentPadding:
                                    const EdgeInsets.only(top: 0, bottom: 0),
                                border: const OutlineInputBorder(
                                    borderSide: BorderSide.none),
                                hintText: '请输入密码',
                                hintStyle: const TextStyle(
                                  color: ColorConfig.desTextColor,
                                  fontSize: 17,
                                )),
                          )),
                          InkWell(
                            onTap: () {
                              controller.isOpen.value = !controller.isOpen.value;
                            },
                            child: Container(
                              width: 40,
                              height: 40,
                              alignment: Alignment.center,
                              child: Container(
                                width: 17,
                                height: 13,
                                child: Image.asset(controller.isOpen.value
                                    ? 'assets/images/3.0x/login_pwd_open.png'
                                    : 'assets/images/3.0x/login_pwd_close.png'),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Container(
                      width: double.infinity,
                      height: 25,
                      alignment: Alignment.centerLeft,
                      child: CupertinoButton(
                          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                          color: ColorConfig.whiteColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(3)),
                          pressedOpacity: 0.5,
                          child: const Text(
                            '验证码登录',
                            style: TextStyle(
                                color: ColorConfig.themeCorlor, fontSize: 14),
                          ),
                          onPressed: () {
                            Get.back();
                          }),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    Container(
                      width: double.infinity,
                      height: 25,
                      alignment: Alignment.centerLeft,
                      child: RichText(
                          text: TextSpan(
                              text: '忘记密码？',
                              style: TextStyle(
                                  color: ColorConfig.desTextColor,
                                  fontSize: 14),
                              children: [
                            TextSpan(
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  Get.toNamed('/reset-pwd',
                                      arguments: {'phone': controller.phone});
                                },
                              text: '点此重置',
                              style: TextStyle(
                                  color: ColorConfig.themeCorlor, fontSize: 14),
                            ),
                          ])),
                    ),
                  ],
                ),
              ),
              Obx(() => Positioned(
                  left: 16,
                  right: 16,
                  bottom: DeviceUtils().bottom.value + 16,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(left: 0, right: 0),
                    height: 50,
                    child: CupertinoButton(
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                        color: controller.pwdStr.value.isNotEmpty?ColorConfig.themeCorlor:ColorConfig.btnGrayColor,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(7.5)),
                        pressedOpacity: 0.5,
                        child: const Text(
                          '下一步',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        onPressed: () {
                          controller.pwdLogin();
                        }),
                  )))
            ],
          )),
    );
  }
}
