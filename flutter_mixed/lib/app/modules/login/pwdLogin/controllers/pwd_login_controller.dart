import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/utils/net_util.dart';
import 'package:get/get.dart';

import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/base_info/info.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/config/config.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';
import '../../../networkListen/controllers/network_listen_controller.dart';

class PwdLoginController extends GetxController {

  final count = 0.obs;
  RxBool isOpen = false.obs;

  String phone = '';
  TextEditingController pwdController = TextEditingController();
  RxString pwdStr = ''.obs;
  @override
  void onInit() {
    super.onInit();
    phone = Get.arguments['phone'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    pwdController.dispose();
    super.onClose();
  }

  pwdLogin() async {
    if(pwdController.text.isEmpty){
      toast('密码不能为空');
      return;
    }
    String udidStr = await DeviceUtils.getUDIDStr();
    String device = await DeviceUtils.getDeviceName();
    Map dict = {
      'client': Platform.isIOS?1:2,
      'mobile': phone,
      'password': pwdController.text,
      'version': LoginApi.API_VERSION,
      'deviceMark': udidStr,
      'deviceModel': device,
      'network': NetUtil.netStr
    };

    DioUtil().post(LoginApi.MOBILEGETTOKEN, dict, true, () {
      // if(error != null){
      //   toast(error.toString());
      // }else{
      //   toast(LoginApi.ERROR_MSG);
      // }
    } , ).then((data) async{
      if (data == null) return;
      if (data!['access_token'] != null) {
       
        await UserDefault.setData(Define.TOKENKEY, data);
        await BaseInfo().getUserInfo();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
