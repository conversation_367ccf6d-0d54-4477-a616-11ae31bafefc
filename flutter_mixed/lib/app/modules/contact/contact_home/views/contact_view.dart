import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/app_scaffold.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import '../../../../common/channel/channel.dart';
import 'package:get/get.dart';

import '../../../../im/route/route_helper.dart';
import '../../../../routes/app_pages.dart';
import '../../aboutOrg/orgWidget/org_widget.dart';
import '../controllers/contact_controller.dart';
import '../../../../common/config/config.dart';
import 'package:badges/badges.dart' as badge;

class ContactView extends GetView<ContactController> {
  ContactView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ContactController controller = Get.put(ContactController());
    return Obx(() => AppScaffold(
        title: "通讯录",
        appBarColor: ColorConfig.backgroundColor,
        isHiddenBackBtn: true,
        actions: [
          Container(
            width: 40,
            height: 24,
            child: CupertinoButton(
                padding: const EdgeInsets.fromLTRB(16, 0, 0, 0),
                onPressed: () {
                  RouteHelper.route(Routes.SEARCH_CHAT);
                },
                child:
                    Image.asset("assets/images/3.0x/im_newsHome_search.png")),
          ),
          Container(
            width: 56,
            height: 24,
            child: CupertinoButton(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                onPressed: () {
                  Get.toNamed('/create-and-join');
                },
                child: Image.asset(AssetsRes.CONTACT_ADD)),
          )
        ],
        body: ListView(
          children: [
            12.gap,
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: ColorConfig.whiteColor,
              ),
              child: Column(
                children: [
                  InkWell(
                      onTap: () {
                        Get.toNamed('/pending-list');
                      },
                      child: backCell(
                          ColorConfig.newFriendColor,
                          'assets/images/3.0x/contact_home_new_friend.png',
                          '新的申请',
                          controller.totalCount.value)),
                  const Divider(
                    color: ColorConfig.backgroundColor,
                    height: 1,
                    indent: 60,
                  ),
                  InkWell(
                      onTap: () {
                        Get.toNamed('/my-friend', arguments: {'chooseType': 0});
                      },
                      child: backCell(
                          ColorConfig.friendColor,
                          'assets/images/3.0x/contact_home_my_friend.png',
                          '担当好友',
                          0)),
                  const Divider(
                    color: ColorConfig.backgroundColor,
                    height: 1,
                    indent: 60,
                  ),
                  InkWell(
                      onTap: () {
                        Get.toNamed('/my-group', arguments: {'type': 0});
                      },
                      child: backCell(
                          ColorConfig.themeCorlor,
                          'assets/images/3.0x/contact_home_group.png',
                          '我的群组',
                          0)),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Offstage(
              offstage: !controller.isHaveData.value,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 12),
                width: double.infinity,
                decoration: BoxDecoration(
                    color: ColorConfig.whiteColor,
                    borderRadius: BorderRadius.circular(8)),
                child: OrgWidget().backOrgCard(
                    controller.model?.logo ?? '',
                    controller.model.name,
                    controller.typeInt.value, onNamePressed: () {
                  if (controller.typeInt.value == 2) {
                    Get.toNamed('/org-more-setting',
                        arguments: {'model': controller.model});
                  } else {
                    Get.toNamed('/org-detail-setting',
                        arguments: {'model': controller.model});
                  }
                }, onButtonPressed: () {
                  if (controller.typeInt.value == 1) {
                    Get.toNamed('/invite-home',
                        arguments: {'model': controller.model, 'type': 0});
                  }
                  if (controller.typeInt.value == 2) {
                    Get.toNamed('/org-more-setting',
                        arguments: {'model': controller.model});
                  }
                }, onOrgPressed: () {
                  Get.toNamed('/dept-detail',
                      arguments: {
                        'topModel':
                            OrgModel.fromJson(controller.model.toJson()),
                        'levelList': [controller.model.name],
                        'model': OrgModel.fromJson(controller.model.toJson()),
                        'deptId': controller.model.deptId,
                        'isManager': false,
                        'companyId': controller.model.companyId
                      },
                      preventDuplicates: false);
                }, onOutPressed: () {
                  Get.toNamed('/external-contacts', arguments: {
                    'model': OrgModel.fromJson(controller.model.toJson()),
                    'chooseType': 0
                  });
                }),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                  color: ColorConfig.whiteColor,
                  borderRadius: BorderRadius.circular(8)),
              child: InkWell(
                  onTap: () {
                    Get.toNamed('/org-list');
                  },
                  child: backCell(
                      ColorConfig.themeCorlor,
                      'assets/images/3.0x/contact_home_architecture.png',
                      '我的其他企业',
                      0)),
            ),
          ],
        )));
  }

  Widget backCell(
      Color lineColor, String headImageName, String name, int unreadCount) {
    double viewH = 56;
    double imageW = 32;
    return Container(
      width: double.infinity,
      height: viewH,
      padding: const EdgeInsets.only(left: 12, right: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.center,
            width: imageW,
            height: imageW,
            child: ImageLoader(
              width: imageW,
              height: imageW,
              url: headImageName,
              radius: 0,
            ),
          ),
          12.gap,
          Expanded(
              child: Container(
            child: Text(
              name,
              style: const TextStyle(
                  fontSize: 16, color: ColorConfig.mainTextColor),
            ),
          )),
          Offstage(
            offstage: unreadCount == 0,
            child: _badge(
                Key('$headImageName$name-$unreadCount'),
                unreadCount,
                Container(
                  width: 24,
                  height: 24,
                )),
          ),
          2.gap,
          SizedBox(
            width: 20,
            height: 20,
            child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
          )
        ],
      ),
    );
  }

  _badge(Key key, int count, Widget child) {
    var value = '';
    var shape = badge.BadgeShape.circle;
    if (count <= 0) {
      value = '';
    } else if (count > 99) {
      value = '99+';
      shape = badge.BadgeShape.square;
    } else {
      value = '$count';
      shape = badge.BadgeShape.circle;
    }
    double fontSize = 10;
    return Padding(
        padding: const EdgeInsets.all(0),
        child: badge.Badge(
          // shape: BadgeShape.circle,
          ignorePointer: false,
          badgeAnimation: const badge.BadgeAnimation.scale(
            animationDuration: Duration(seconds: 1),
            colorChangeAnimationDuration: Duration(seconds: 1),
            loopAnimation: false,
            curve: Curves.fastOutSlowIn,
            colorChangeAnimationCurve: Curves.easeInCubic,
          ),
          badgeStyle: badge.BadgeStyle(
            shape: shape,
            badgeColor: ColorConfig.deleteCorlor,
            padding: const EdgeInsets.all(1),
            borderRadius: BorderRadius.circular(12),
            // borderSide: BorderSide(color: ColorConfig.deleteCorlor, width: 1),
            elevation: 0,
          ),
          badgeContent: Container(
            padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
            key: key,
            constraints: const BoxConstraints(minWidth: 22),
            child: Center(
              child: Text(
                value,
                style: TextStyle(fontSize: fontSize, color: Colors.white),
              ),
            ),
          ),
          showBadge: count > 0,
          position: badge.BadgePosition.topEnd(top: 3.5, end: -2),
          child: child,
        ));
  }
}
