import 'dart:async';

import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../model/org/org_model.dart';

class ContactController extends GetxController {
  StreamSubscription? subscription;
  RxInt typeInt = 0.obs;
  late OrgModel model = OrgModel("");
  RxBool isHaveData = false.obs;

  RxInt totalCount = 0.obs;
  @override
  void onInit() {
    super.onInit();
    subscription = eventBus.on<Map>().listen((event) {
      if (event['waitCount'] == 1) {
        getPendingListCount();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    dealMainOrg('');
    getPendingListCount();
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

  getPendingListCount() {
    DioUtil().get(ORGApi.GETWAITLIST, null, true, () {
    },isShowLoading: false).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        totalCount.value = 0;
        Map dataMap = data['data'];
        int friendCount = dataMap['friendApplyNumber'];
        int externalCount = dataMap['contactsInviteNumber'];
        dynamic orgCountMap = dataMap['orgApplyNumber'];

        logger("orgCountMap --> $orgCountMap");

        if (orgCountMap != null && orgCountMap is Map) {
          for (var key in orgCountMap.keys) {
            int count = orgCountMap[key];
            totalCount.value = totalCount.value + count;
          }

          totalCount.value += friendCount;
          totalCount.value += externalCount;
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  dealMainOrg(String mainId) async {
    dynamic orgDic = await UserDefault.getData(Define.ORGLIST);
    if(orgDic == null) return;

    if (mainId == '') {
      mainId = orgDic['mainCompanyId'];
    }
    if (orgDic['mainCompanyId'] != null) {
      List companyList = orgDic['companies'];
      for (var i = 0; i < companyList.length; i++) {
        Map<String, dynamic> companyDic = companyList[i];
        if (companyDic['companyId'] == mainId) {
          var model1 = OrgModel.fromJson(companyDic);

          if (model1 != null) {
            model = model1;
            if (model.rejectInvitation == 0) {
              typeInt.value = 1;
            } else {
              typeInt.value = 0;
            }
            if (model.deptId == '0' ||
                (model.power ?? '').contains('-1') ||
                (model.power ?? '').contains('1')) {
              typeInt.value = 2;
            }
            isHaveData.value = true;
          }
          break;
        }
      }
    }
    isHaveData.refresh();
  }
}
