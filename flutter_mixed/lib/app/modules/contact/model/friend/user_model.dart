class UserModel {
  late String userId;
  late String name;
  late String avatar;
  String? initial;
  late String remark;
  int friendState = 0;
  String imId = '';
  UserModel(
    this.userId,
  );

  UserModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    name = json['name'];
    avatar = json['avatar'];
    // initial = json['initial'] == null ? '' : json['initial'] ;
    initial = json['initial'] ;
    remark = json['remark'] == null ? '' : json['remark'];
    friendState = json['friendState'] ?? 0;
    imId = json['imId']??'';
  }

  Map<String, dynamic> toJson() => {
        'userId': userId,
        'name': name,
        'avatar': avatar,
        'initial': initial,
        'remark': remark,
        'friendState':friendState,
        'imId':imId
      };
}
