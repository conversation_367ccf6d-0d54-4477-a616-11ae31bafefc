import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';

class AllOrgModel {
  List<OrgModel?>? externalCompanies;
  List<OrgModel?>? companies;
  String? mainCompanyId;
  int? unprocessedMessage;

  AllOrgModel(
      {this.externalCompanies,
      this.companies,
      this.mainCompanyId,
      this.unprocessedMessage});

  AllOrgModel.fromJson(Map<String, dynamic> json) {
    externalCompanies = <OrgModel>[];
    companies = <OrgModel>[];
    if (json['externalCompanies'] != null) {
      json['externalCompanies'].forEach((v) {
        externalCompanies!.add(OrgModel.fromJson(v));
      });
    }
    if (json['companies'] != null) {
      json['companies'].forEach((v) {
        companies!.add(OrgModel.fromJson(v));
      });
    }
    mainCompanyId = json['mainCompanyId'] ?? '';
    unprocessedMessage = json['unprocessedMessage'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['externalCompanies'] = externalCompanies != null
        ? externalCompanies!.map((v) => v?.toJson()).toList()
        : [];
    data['companies'] =
        companies != null ? companies!.map((v) => v?.toJson()).toList() : [];
    data['mainCompanyId'] = mainCompanyId;
    data['unprocessedMessage'] = unprocessedMessage;
    return data;
  }
}
