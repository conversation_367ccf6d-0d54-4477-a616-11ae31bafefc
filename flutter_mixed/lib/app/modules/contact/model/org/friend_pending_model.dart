class FriendPendingModel {
  late String userId;
  String name = '';
  String message = '';
  String avatar = '';
  String recordId = '';
  int status = 0;
  int createTime = 0;
  int updateTime = 0;
  FriendPendingModel(
    this.userId,
  );

  FriendPendingModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    name = json['name'] ?? '';
    message = json['message'] ?? '';
    avatar = json['avatar'] ?? '';
    recordId = json['id'] ?? '';
    status = json['status'] ?? 1;
    createTime = json['createTime'] ?? 0;
    updateTime = json['updateTime'] ?? 0;
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'userId': userId,
        'message': message,
        'avatar': avatar,
        'id': recordId,
        'status': status,
        'createTime': createTime,
        'updateTime': updateTime
      };
}
