class ExternalModel {
  late String userId;
  String desc = '';
  late String name;
  late String mobile;
  late int level;
  late int type;
  late int status;
  late int active;
  late String avatar;
  String email = '';
  late String updateUser;
  late String externalContactsName;
  late int updateTime;

  ExternalModel(
    this.userId,
  );

  ExternalModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    desc = json['desc'];
    name = json['name'];
    level = json['level'];
    type = json['type'];
    status = json['status'];
    active = json['active'];
    if (json['avatar'] == null) {
      avatar = json['headimg'];
    } else {
      avatar = json['avatar'];
    }

    mobile = json['mobile'] == null ? '' : json['mobile'];
    email = json['email'] == null ? '' : json['email'];
    updateUser = json['updateUser'] == null ? '' : json['updateUser'];
    externalContactsName = json['externalContactsName'] == null
        ? ''
        : json['externalContactsName'];
    updateTime = json['updateTime'] == null ? 0 : json['updateTime'];
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'userId': userId,
        'mobile': mobile,
        'desc': desc,
        'level': level,
        'status': status,
        'active': active,
        'avatar': avatar,
        'type': type,
        'email': email,
        'updateUser': updateUser,
        'externalContactsName': externalContactsName,
        'updateTime': updateTime,
      };
}
