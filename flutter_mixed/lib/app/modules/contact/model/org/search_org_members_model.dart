class SearchMemberModel {
  String userId = '';
  String userName = '';
  String avatar = '';
  String orgName = '';
  String deptName = '';
  String positionName = '';
  String powerName = '';
  String orgId = '';
  String deptId = '';
  String mobile = '';
  String email = '';
  String imId = '';
  String name = '';
  String headimg = '';
  int? type = 0;
  SearchMemberModel(
    this.userId,
  );

  SearchMemberModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    imId = json['imId'] == null ? '' : json['imId'];
    userName = json['userName'] == null ? '' : json['userName'];
    avatar = json['avatar'] == null ? '' : json['avatar'];
    orgName = json['orgName'] == null ? '' : json['orgName'];
    deptName = json['deptName'] == null ? '' : json['deptName'];
    positionName = json['positionName'] == null ? '': json['positionName'];
    powerName = json['powerName'] == null ?'': json['powerName'];
    orgId = json['orgId'] == null ? '' : json['orgId'];
    deptId = json['deptId'] == null ?'' : json['deptId'];
    mobile = json['mobile'] == null ?'' : json['mobile'];
    email = json['email'] == null ?'' : json['email'];
    name = json['name'] == null ?'' : json['name'];
    headimg = json['headimg'] == null ?'' : json['headimg'];
    type = json['type'] == null ? 0 : json['type'];
  }

  Map<String, dynamic> toJson() => {
        'userId': userId,
        'userName': userName,
        'avatar': avatar,
        'orgName': orgName,
        'deptName': deptName,
        'positionName': positionName,
        'powerName': powerName,
        'orgId': orgId,
        'deptId': deptId,
        'mobile': mobile,
        'email': email,
        'imId':imId,
        'name':name,
        'headimg':headimg,
        'type':type
      };
}
