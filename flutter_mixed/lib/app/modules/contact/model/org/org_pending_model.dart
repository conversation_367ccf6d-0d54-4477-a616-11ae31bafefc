class OrgPendingModel {
  late String userId;
  late String name;
  late String content;
  late String headimg;
  late String recordId;
  late int status;
  late int createTime;
  late String handlerName;
  OrgPendingModel(
    this.userId,
  );

  OrgPendingModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'] ?? '';
    name = json['name'] ?? '';
    content = json['content'] ?? '';
    headimg = json['headimg'] ?? '';
    recordId = json['recordId'] ?? '';
    status = json['status'] ?? 0;
    createTime = json['createTime'] ?? 0;
    handlerName = json['handlerName'] ?? '';
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'userId': userId,
        'content': content,
        'headimg': headimg,
        'recordId': recordId,
        'status': status,
        'createTime': createTime,
        'handlerName': handlerName
      };
}
