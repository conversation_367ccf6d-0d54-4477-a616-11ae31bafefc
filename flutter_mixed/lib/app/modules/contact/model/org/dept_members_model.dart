class MemberModel {
  String userId = '';
  String name = '';
  String positionId = '';
  int level = 0;
  String headimg = '';
  String avatar = '';
  String initial = '';
  String imId = '';
  String remark = '';
  String userName = '';
  int type = 0; //0普通 1创建者 -1超级管理员 2负责人

  //自定义字段
  int chooseState = 0; //0未选中1选中2选中不能取消
  int sessionType = 1; //转发-1单聊2群聊
  MemberModel(
    this.userId,
  );

  MemberModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'] ?? "";
    if (json['name'] == null) {
      name = json['userName'];
    } else {
      name = json['name'];
    }
    userName = name;
    if (json['headimg'] == null) {
      headimg = json['avatar'];
    } else {
      headimg = json['headimg'];
    }
    avatar = headimg;
    positionId = json['positionId'] ?? '';
    level = json['level'] ?? 1;
    initial = json['initial'] ?? '';
    imId = json['imId'] ?? '';
    remark = json['remark'] ?? '';
    type = json['type'] ?? 0;
    chooseState = json['chooseState'] ?? 0;
    sessionType = json['sessionType'] ?? 1;
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'userName': userName,
        'userId': userId,
        'positionId': positionId,
        'headimg': headimg,
        'avatar': avatar,
        'level': level,
        'initial': initial,
        'imId': imId,
        'remark': remark,
        'type': type,
        'chooseState': chooseState,
        'sessionType': sessionType
      };

  @override
  String toString() {
    return 'MemberModel{userId: $userId, name: $name, level: $level, imId: $imId, type: $type, sessionType: $sessionType}';
  }
}
