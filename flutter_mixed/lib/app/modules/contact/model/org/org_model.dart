import 'package:floor/floor.dart';
class OrgModel {
  String companyId = '';
  String deptId = '';
  String name = '';
  String logo = '';
  int haveApprove = 0;
  int rejectInvitation = 0;
  int rejectJoin = 0;
  int isJoin = 1;

  String power = '';

  String? organizationId;
  String? type;
  String? scale;
  String? industry;
  String? linkManPhone;
  String? linkManMail;
  String? officialWebsite;
  String? profile;
  String content = '';
  String noticeId = '0';

  String corgId = '';
  //自定义字段
  bool isExternal = false; //是否是外部联系人
  bool isSureDeptId = true; //是否是确定的部门 用于组织群组查看人员信息传值
  int chooseState = 0;//0未选中 1选中
  OrgModel(
    this.companyId,
  );

  OrgModel.fromJson(Map<String, dynamic> json) {
    companyId = json['companyId'] ?? '';
    corgId = json['corgId'] ?? '';
    organizationId = json['organizationId'];
    name = json['name'] ?? '';
    haveApprove = json['haveApprove'] ?? 0;
    logo = json['logo'] ?? '';
    rejectJoin = json['rejectJoin'] ?? 0;
    isJoin = json['isJoin'] ?? 0;
    deptId = json['deptId'] ?? '';
    rejectInvitation = json['rejectInvitation'] ?? 0;
    power = json['power'] ?? '';
    type = json['type'] ?? '';
    scale = json['scale'] ?? '';
    industry = json['industry'] ?? '';
    linkManPhone = json['linkManPhone'] ?? '';
    linkManMail = json['linkManMail'] ?? '';
    officialWebsite = json['officialWebsite'] ?? '';
    profile = json['profile'] ?? '';
    content = json['content'] ?? '';
    noticeId = json['noticeId'] ?? '';
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'companyId': companyId,
        'corgId': corgId,
        'logo': logo,
        'haveApprove': haveApprove,
        'rejectJoin': rejectJoin,
        'isJoin': isJoin,
        'deptId': deptId,
        'rejectInvitation': rejectInvitation,
        'power': power,
        'organizationId': organizationId,
        'type': type,
        'scale': scale,
        'industry': industry,
        'linkManPhone': linkManPhone,
        'linkManMail': linkManMail,
        'officialWebsite': officialWebsite,
        'profile': profile,
        'content': content,
        'noticeId': noticeId,
      };
}
