class ExternalPendingModel {
  late String companyId;
  late String name;
  late String logo;
  late String externalId;
  late int status;
  late int handleTime;
  ExternalPendingModel(
    this.companyId,
  );

  ExternalPendingModel.fromJson(Map<String, dynamic> json) {
    companyId = json['companyId'];
    name = json['name'];
    logo = json['logo'];
    externalId = json['externalId'];
    status = json['status'];
    handleTime = json['handleTime'];
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'companyId': companyId,
        'logo': logo,
        'externalId': externalId,
        'status': status,
        'handleTime': handleTime
      };
}