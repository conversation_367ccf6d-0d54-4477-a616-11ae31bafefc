import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';

class AllOrgUserModel {
  List<MemberModel?>? staffVOList;
  String? orgId;
  AllOrgUserModel.fromJson(Map<String, dynamic> json) {
    staffVOList = <MemberModel>[];
    if (json['staffVOList'] != null) {
      json['staffVOList'].forEach((v) {
        staffVOList!.add(MemberModel.fromJson(v));
      });
    }
    orgId = json['orgId'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['staffVOList'] = staffVOList != null
        ? staffVOList!.map((v) => v?.toJson()).toList()
        : [];
    data['orgId'] = orgId;
    return data;
  }
}
