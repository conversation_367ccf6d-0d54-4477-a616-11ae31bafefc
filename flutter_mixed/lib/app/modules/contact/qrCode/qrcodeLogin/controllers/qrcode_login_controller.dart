import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';

class QrcodeLoginController extends GetxController {

  String key = '';
  @override
  void onInit() {
    super.onInit();
    key = Get.arguments['key'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  dealQrCodeLogin(int op) async{
        DioUtil()
        .get('${LoginApi.PLAT_OPEN_DEAL}/$key/$op', null, true, () {},isShowErrorToast: false)
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        if(op == 1){
          toast('登录成功!');
        }
      } else {
        toast('${data['msg']}');
      }
      Get.back();
    });
  }

}
