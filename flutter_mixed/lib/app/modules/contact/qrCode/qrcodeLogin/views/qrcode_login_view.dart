import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/config/string_const.dart';
import '../controllers/qrcode_login_controller.dart';

class QrcodeLoginView extends GetView<QrcodeLoginController> {
  const QrcodeLoginView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      body: Obx(() => Stack(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            width: double.infinity,
            height: DeviceUtils().height.value,
            padding: EdgeInsets.only(left: 16, right: 16),
            decoration: BoxDecoration(
                image: DecorationImage(
                    fit: BoxFit.fitWidth,
                    image:
                        AssetImage('assets/images/3.0x/login_backGround.png'))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 150,
                ),
                Container(
                  width: double.infinity,
                  height: 140,
                  alignment: Alignment.center,
                  child: Container(
                    width: 140,
                    height: 140,
                    child: Image.asset('assets/images/3.0x/qrCode_login.png'),
                  ),
                ),
                Container(
                  width: double.infinity,
                  height: 30,
                  alignment: Alignment.center,
                  child: Text(
                    '$appName登录确认',
                    style: TextStyle(
                        fontSize: 20, color: ColorConfig.mainTextColor),
                  ),
                ),
                SizedBox(
                  height: 160,
                ),
                Container(
                  width: double.infinity,
                  height: 44,
                  padding: EdgeInsets.only(left: 0, right: 0),
                  child: CupertinoButton(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                      color: ColorConfig.themeCorlor,
                      borderRadius: const BorderRadius.all(Radius.circular(7)),
                      pressedOpacity: 0.5,
                      child: const Text(
                        '确认',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      onPressed: () {
                        controller.dealQrCodeLogin(1);
                      }),
                ),
                SizedBox(
                  height: 15,
                ),
                Container(
                  width: double.infinity,
                  height: 44,
                  padding: EdgeInsets.only(left: 0, right: 0),
                  child: CupertinoButton(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                      color: Colors.white,
                      borderRadius: const BorderRadius.all(Radius.circular(7)),
                      pressedOpacity: 0.5,
                      child: const Text(
                        '取消',
                        style: TextStyle(color: ColorConfig.themeCorlor, fontSize: 16),
                      ),
                      onPressed: () {
                        controller.dealQrCodeLogin(-1);
                      }),
                ),
              ],
            ),
          )
        ],
      )),
    );
  }
}
