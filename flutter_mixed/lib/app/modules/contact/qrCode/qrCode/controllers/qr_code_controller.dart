import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/widgets/ios_native_route_focus.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/click_card/click_card_jump.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/modules/mine/model/park_address_model.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/app/retrofit/entity/group/group_info.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_scankit/flutter_scankit.dart';
import 'package:flutter_scankit/src/scan_kit_widget.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../retrofit/datasource/workbench_datasource.dart';
import '../../../../../routes/app_pages.dart';
import '../../../../../utils/http.dart';
import '../../../../../../logger/logger.dart';
import '../../../model/org/org_model.dart';

class QrCodeController extends GetxController with MixNativeController {
  ScanKitController? scanController;
  bool isResult = false;
  StreamSubscription? subscription;
  StreamSubscription? nativeSubscription;
  RxBool isPause = false.obs;
  int type = 0; //0默认 1flutter跳转 2取餐web扫码
  @override
  void onInit() {
    super.onInit();
    scanController = ScanKitController();
    if (Get.arguments != null) {
      if (Get.arguments['type'] != null) {
        type = Get.arguments['type'];
      }
    }
    if (type > 0) {
      _addSubScription();
    }
    logger('====qrcode===init');
    nativeSubscription = eventBus.on<Map>().listen((event) async {
      logger('userinfo... event-------$event');
      if (event == null) return;
      var route = event['route'];
      if (route == Routes.QR_CODE) {
        isNative.value = true;
        var arguments = event['arguments'];
        type = 2;
        _addSubScription();
      }
    });
    _listenerCamera();
  }

  _addSubScription() {
    logger('执行了');
    subscription = scanController?.onResult.listen((result) async {
      logger('=====获取1');
      subscription!.pause();
      logger('=====获取1');
      scanController?.pauseContinuouslyScan();
      logger('=====获取1');
      dealResult(result.originalValue);
    });
  }

  _listenerCamera() async {
    PermissionUtil.listenerCamera((bool isShow){
      if (isShow) {
         toast('相机权限已关闭，无法使用扫码功能，请从手机设置中打开相机权限');
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    scanController?.dispose();
    subscription?.cancel();
    nativeSubscription?.cancel();
    super.onClose();
  }

  tapGallery() async {
    var result = await PermissionUtil.checkGalleryPermission(Get.context!);
    if (result) {
      scanController?.pickPhoto();
    }
  }

  dealResult(String codeStr) {
    //扫码登录
    logger('二维码数据： $codeStr');

    if (codeStr.contains('m.k8x.cn/p')) {
      //食堂扫码领餐  'http://m.k8x.cn/p/1ck' http://m.k8x.cn/p/17s
      List strings = codeStr.split('/');
      if (strings.isNotEmpty) {
        _getMealsStatus(strings.last);
      }
    }else {
      if (type == 2) {
        toast('无法识别此二维码');
        backPage();
        return;
      }
      if (codeStr.startsWith(Define.SCANLOGIN)) {
        List list = codeStr.split('.');
        if (list.length > 1) {
          String jsonStr = list[1];
          Map<String, dynamic> jsonData = json.decode(jsonStr);
          qrCodeLoginIsAlready(jsonData['key']);
        }
      } else if (codeStr.startsWith(Define.SCANUSER)) {
        //个人二维码
        logger('userinfodijici=====');
        List list = codeStr.split('.');
        Get.offAndToNamed('/user-info',
            arguments: {'userId': list.last, 'type': 1});
      } else if (codeStr.startsWith(Define.SCANORG)) {
        List resultList = codeStr.split('.');
        //企业二维码
        OrgModel model = OrgModel(resultList.last);
        Get.offAndToNamed('/org-detail', arguments: {'model': model});
      } else if (codeStr.startsWith(Define.SIGN)) {
        //扫码签名
        Get.back();
        List resultList = codeStr.split('.');
        String dataStr = resultList.last;
        Map<String, dynamic> jsonData = json.decode(dataStr);
        Channel().invoke(Channel_Native_Qr_Sign, jsonData);
      } else if (codeStr.startsWith(Define.GROUP)) {
        //扫描群二维码进群
        _scanGroupQrCode(codeStr);
      } else if(codeStr.contains('api.rangepark.cn/sps/index.html?type=2&mch=')){
        backPage(isBackBtn: false);
        List strings = codeStr.split('type=2&mch=');
        if (strings.isNotEmpty) {
          _jumpRangePage(strings.last);
        }
      } else if (codeStr.startsWith('http')) {
        //网页
        if (codeStr.contains(Host.MEETINGLINK)) {
          Get.back();
          List meetingList = codeStr.split('meetingId=');
          if (meetingList.length > 1) {
            String endStr = meetingList.last;
            if (endStr.contains('&')) {
              List endList = endStr.split('&');
              endStr = endList.first;
            }
            Channel().invoke(Channel_callNative_jumpRoute, {
              'route': Routes.MEETING_DETAIL,
              'arguments': {'meetingId': endStr, 'type': 2}
            });
          }
        } else {
          Get.back();

          openWebView({
            'url': codeStr,
            'title': '',
            'isWebNavigation': 0,
          });
        }
      } else {
        toast('无法识别此二维码');
        backPage();
      }
    }
  }

  //获取食堂领餐状态
  _getMealsStatus(String address) async {
    try {
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource
          .getMealsStatus(ParkAddressModel(address: address));
      logger('========resp===${resp.toString()}');
      if (resp.success()) {
        backPage(isBackBtn: false);
        dynamic data = resp.data;
        if (data is String) {
          if (data == '暂未到领餐时间') {
            _jumpErrorStatus(1);
          }
        } else if (data is List) {
          if (data.isEmpty) {
            _jumpErrorStatus(2);
          }
        } else {
          String jsonData = jsonEncode(resp.data);
          _jumpSuccessStatus(jsonData);
        }
      } else {
        if (!StringUtil.isEmpty(resp.msg)) {
          toast(resp.msg);
          backPage();
        }
      }
    } catch (e) {
      toast('获取领餐状态错误');
      backPage();
      logger('=====error===$e');
    }
  }

  _jumpErrorStatus(int status) {
    var url =
        '${Host.WEBHOST}/ddbes-park-welfare-web/index.html#/h5/daily/NotSuccess?num=$status';
    ClickCardJump.webJump(url, '');
  }

  _jumpSuccessStatus(String jsonData) {
    String uriStr = Uri.encodeComponent(jsonData);
    var url =
        '${Host.WEBHOST}/ddbes-park-welfare-web/index.html#/h5/daily/MentSuccess?data=$uriStr';

    ClickCardJump.webJump(url, '');
  }

  _jumpRangePage(String mch){
    var url =
        '${Host.WEBHOST}/ddbes-park-welfare-web/index.html#/h5/daily/paymentpage?mch=$mch';
    ClickCardJump.webJump(url, '');
  }
  //确认已经扫码
  qrCodeLoginIsAlready(String key) async {
    if (isResult) {
      return;
    } else {
      isResult = true;
    }
    DioUtil()
        .get('${LoginApi.PLAT_OPEN_SCAN}/$key', null, true, () {},
            isShowErrorToast: false)
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Get.offAndToNamed('qrcode-login', arguments: {'key': key});
      } else {
        toast('${data['msg']}');
        backPage();
      }
    });
  }

  _scanGroupQrCode(codeStr) async {
    Get.loading();
    var workDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
    var resp = await workDatasource.getGroupQrCodeData(codeStr);
    Get.dismiss();
    if (resp.success()) {
      Get.back();
      if (resp.data == null) return;
      GroupQrCodeDataModel model = resp.data!;
      if (model.exist == 0) {
        // Channel().invoke(Channel_jumpGroupChat, {
        //   'group': {
        //     'groupId': model.groupId,
        //     'logo': model.logo,
        //     'name': model.name
        //   }
        // });
        var map = {
          'group': {
            'groupId': model.groupId,
            'logo': model.logo,
            'name': model.name
          }
        };
        map.createGroupSessionAndEnter();
      } else {
        Get.toNamed(Routes.SCAN_JOIN_GROUP,
            arguments: {'model': resp.data}, preventDuplicates: false);
      }
    } else {
      Get.back();
      toast(resp.msg);
    }
  }

  backPage({bool isBackBtn = true}) {
    if (isNative.value) {
      onClose();
      if (isBackBtn || Platform.isAndroid) {
        var param =  {"route": Routes.QR_CODE};
        Channel().invoke(Channel_Native_Back, param);
      }
    } else {
      Get.back();
    }
  }
}
