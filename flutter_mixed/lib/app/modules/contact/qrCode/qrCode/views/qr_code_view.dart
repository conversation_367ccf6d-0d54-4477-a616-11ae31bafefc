import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/widgets/ios_native_route_focus.dart';
import 'package:flutter_scankit/src/scan_kit_widget.dart';
import 'package:get/get.dart';
import '../controllers/qr_code_controller.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';

class QrCodeView extends GetView<QrCodeController> {
  const QrCodeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return IosNativeRouteFixGesture.builder(
        controller: controller,
        childBuilder: () {
          return GetBuilder<QrCodeController>(builder: (controller){
            return Scaffold(
              backgroundColor: ColorConfig.backgroundColor,
              appBar: TitleBar().backAppbar(context, '扫一扫', false, [
                Container(
                  width: 60,
                  child: CupertinoButton(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                      pressedOpacity: 0.5,
                      child: const Text(
                        '相册',
                        style: TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 14),
                      ),
                      onPressed: () {
                        //controller.scanController.pickPhoto();
                        controller.tapGallery();
                      }),
                )
              ], onPressed: () {
                controller.backPage();
              }),
              body: Stack(
                children: [
                  if(controller.scanController != null)...[
                    ScanKitWidget(
                    controller: controller.scanController!,
                  )],
                  Obx(() => Align(
                        alignment: Alignment.center,
                        child: Container(
                          width: (DeviceUtils().width.value <
                                      DeviceUtils().height.value
                                  ? DeviceUtils().width.value
                                  : DeviceUtils().height.value) *
                              0.66,
                          height: (DeviceUtils().width.value <
                                      DeviceUtils().height.value
                                  ? DeviceUtils().width.value
                                  : DeviceUtils().height.value) *
                              0.66,
                          decoration: BoxDecoration(
                            border: Border(
                                left: BorderSide(
                                    color: Colors.orangeAccent, width: 2),
                                right: BorderSide(
                                    color: Colors.orangeAccent, width: 2),
                                top: BorderSide(
                                    color: Colors.orangeAccent, width: 2),
                                bottom: BorderSide(
                                    color: Colors.orangeAccent, width: 2)),
                          ),
                        ),
                      ))
                ],
              ));
          });
        });
  }
}
