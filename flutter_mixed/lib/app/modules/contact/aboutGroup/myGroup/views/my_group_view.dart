import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';

import '../../../model/org/dept_members_model.dart';
import '../controllers/my_group_controller.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';

class MyGroupView extends GetView<MyGroupController> {
  const MyGroupView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context,
              '我的群组',
              false,
              controller.type == 0
                  ? [
                      Container(
                        width: 100,
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            pressedOpacity: 0.5,
                            child: const Text(
                              '创建群组',
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            ),
                            onPressed: () {
                              Get.toNamed(Routes.GROUP_ADD_MEMBERS , arguments: {});
                            }),
                      )
                    ]
                  : controller.type == 1
                      ? [
                          Container(
                            width: 60,
                            child: CupertinoButton(
                                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                pressedOpacity: 0.5,
                                child: const Text(
                                  '完成',
                                  style: TextStyle(
                                      color: ColorConfig.themeCorlor,
                                      fontSize: 14),
                                ),
                                onPressed: () {
                                  Get.back(result: {
                                    'selectGroupList':
                                        controller.selectGroupList
                                  });
                                }),
                          )
                        ]
                      : [], onPressed: () {
            Get.back();
          } , isNative: !controller.isFromNative()),
          body: ListView.builder(
            itemBuilder: (context, index) {
              Map? dataDic;
              MemberModel? tempModel;
              if (index > 0 && controller.dataList.isNotEmpty) {
                dataDic = controller.dataList[index - 1];

                for (var j = 0; j < controller.selectGroupList.length; j++) {
                  MemberModel selectModel = controller.selectGroupList[j];
                  if (selectModel.userId == dataDic!['groupId']) {
                    tempModel = selectModel;
                    break;
                  }
                }
              }

              return index == 0
                  ? Container(
                      width: double.infinity,
                      color: Colors.white,
                      height: 50,
                      padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                      child: SettingWidget().backSearchWidget(
                          controller.searchController, controller.node, '搜索群组',
                          onSub: (value) {}, onTap: () async {
                        controller.node.unfocus();
                        var result =
                            await Get.toNamed('/search-group', arguments: {
                          'type': controller.type == 0 ? 0 : 1,
                          'selectGroupList': RxList(controller.selectGroupList
                              .map((e) => MemberModel.fromJson(e.toJson()))
                              .toList())
                        });
                        if (result != null && result['list'] != null) {
                          controller.selectGroupList = result['list'];
                          controller.dataList.refresh();
                        }
                      }))
                  : controller.dataList.isEmpty
                      ? Container(
                          width: double.infinity,
                          height: (DeviceUtils().height.value - 130) * 0.5,
                          alignment: Alignment.center,
                          child: Text(
                            '暂无群组',
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.mainTextColor),
                          ),
                        )
                      : InkWell(
                          onTap: () {
                            if (controller.type == 0) {
                              controller.jumpGroupChat(dataDic!);
                            }
                            if (controller.type == 1) {
                              if (tempModel == null) {
                                if (controller.selectGroupList.length == 10) {
                                  toast('最多选择10条会话');
                                  return;
                                }
                                MemberModel groupModel =
                                    MemberModel(dataDic!['groupId']);
                                groupModel.imId = dataDic!['groupId'];
                                groupModel.headimg = dataDic['logo'];
                                groupModel.name = dataDic['name'];
                                groupModel.sessionType = 2;
                                controller.selectGroupList.add(groupModel);
                              } else {
                                controller.selectGroupList.remove(tempModel);
                              }
                              controller.dataList.refresh();
                            }
                          },
                          child: SettingWidget().backSettingWidget(
                              controller.type == 1
                                  ? tempModel != null
                                      ? AssetsRes.APPROVE_SELECTED
                                      : AssetsRes.APPROVE_UNSELECTED
                                  : '',
                              dataDic!['logo'],
                              dataDic['name'],
                              '',
                              false,
                              56),
                        );
            },
            itemCount: controller.dataList.isEmpty
                ? 2
                : controller.dataList.length + 1,
          ),
        ));
  }
}
