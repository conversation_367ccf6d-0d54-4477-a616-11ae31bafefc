import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/widgets/base_get_controller.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../utils/storage.dart';

class MyGroupController extends BaseGetXController {

  TextEditingController searchController = TextEditingController();
  RxList dataList = [].obs;

  int type = 0; //0展示群组 1转发
  RxList selectGroupList = [].obs;
  FocusNode node = FocusNode();
 
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments['type'] != null) {
        type = Get.arguments['type'];
      }
      if (Get.arguments['selectGroupList'] != null) {
        selectGroupList = Get.arguments['selectGroupList'];
      }

    }
  }

  @override
  void onReady() {
    super.onReady();
    getAllGroup();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  getAllGroup() async {
    dynamic groups = await UserDefault.getData(Define.GROUPLIST);
    if (groups != null) {
      dataList.value = groups;
    }
  }

  //选择群组聊天 原生交互
  jumpGroupChat(Map groupDic) {
    print('groupDic: $groupDic');
    // Channel().invoke(Channel_jumpGroupChat, {'group': groupDic});
    var param = {'group': groupDic};
    param.createGroupSessionAndEnter();
  }
}
