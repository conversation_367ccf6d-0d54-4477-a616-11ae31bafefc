import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SearchAllController extends GetxController {

  final count = 0.obs;
  TextEditingController searchController = TextEditingController();
  FocusNode node = FocusNode();
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    searchController.dispose();
  }

  void increment() => count.value++;
}
