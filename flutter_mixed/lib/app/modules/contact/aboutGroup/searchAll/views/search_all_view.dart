import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/search_all_controller.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';

class SearchAllView extends GetView<SearchAllController> {
  const SearchAllView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      appBar: TitleBar().backAppbar(context, '搜索', false, [], onPressed: () {
        Get.back();
      }),
      body: Container(
        color: ColorConfig.backgroundColor,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              color: Colors.white,
              height: 50,
              padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
              child: SettingWidget().backSearchWidget(controller.searchController, controller.node,'搜索', onSub:(value){
                  
                },onTap: (){
                  
                }),
            ),
            Expanded(
                child: DefaultTabController(
                    length: 2,
                    child: Column(
                      children: [
                        Container(
                            height: 34,
                            child: TabBar(
                                dividerHeight: 0,
                                indicatorColor: ColorConfig.themeCorlor,
                                indicatorWeight: 1,
                                tabAlignment: TabAlignment.start,
                                tabs: [
                                  Text(
                                    '全部',
                                    style: TextStyle(
                                        color: ColorConfig.mainTextColor,
                                        fontSize: 14),
                                  ),
                                  Text('我的好友',
                                      style: TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 14)),
                                  Text('企业群组',
                                      style: TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 14)),
                                  Text('私有群组',
                                      style: TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 14)),
                                ])),
                        Expanded(
                            child: TabBarView(children: [
                          getWidgetForGroup(0),
                          getWidgetForGroup(1),
                          getWidgetForGroup(2),
                          getWidgetForGroup(3)
                        ]))
                      ],
                    )))
          ],
        ),
      ),
    );
  }
}

getWidgetForGroup(int type) {
  return ListView.builder(
    itemBuilder: (context, index) {
      return InkWell(
        onTap: () {},
        child: Container(
          color: Colors.white,
          width: double.infinity,
          height: 70,
          padding: EdgeInsets.only(left: 15, right: 15),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(1),
                    width: 48,
                    height: 48,
                    alignment: Alignment.centerLeft,
                    decoration: BoxDecoration(
                        image: DecorationImage(image: AssetImage('')),
                        borderRadius: BorderRadius.circular(12),
                        border:
                            Border.all(color: ColorConfig.lineColor, width: 1)),
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  Obx(() => Container(
                        width: DeviceUtils().width.value - 140,
                        alignment: Alignment.centerLeft,
                        height: 69,
                        child: Text('这是个类型为${type}的群组',
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                color: ColorConfig.mainTextColor,
                                fontSize: 17)),
                      ))
                ],
              ),
              const Divider(height: 1, color: ColorConfig.lineColor)
            ],
          ),
        ),
      );
    },
    itemCount: 40,
  );
}
