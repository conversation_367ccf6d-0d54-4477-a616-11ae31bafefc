import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../utils/storage.dart';

class SearchGroupController extends GetxController {
  TextEditingController searchController = TextEditingController();
  RxList dataList = [].obs;
  RxList showList = [].obs;
  int type = 0; //0展示群组 1选择群组
  RxList selectGroupList = [].obs;

  FocusNode node = FocusNode();
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments['type'] != null) {
        type = Get.arguments['type'];
      }
      if (Get.arguments['selectGroupList'] != null) {
        selectGroupList = Get.arguments['selectGroupList'];
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
    getAllGroup();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  getAllGroup() async {
    dataList.value = await UserDefault.getData(Define.GROUPLIST);
  }

  searchGroup() {
    showList.clear();
    for (var i = 0; i < dataList.length; i++) {
      Map dict = dataList[i];
      String name = dict['name'];
      if (name.contains(searchController.text)) {
        showList.add(dict);
      }
    }
    showList.refresh();
  }

  //选择群组聊天 原生交互
  jumpGroupChat(Map groupDic) {
    print('groupDic: $groupDic');
    // Channel().invoke(Channel_jumpGroupChat, {'group': groupDic});
    var param = {'group': groupDic};
    param.createGroupSessionAndEnter();
  }
}
