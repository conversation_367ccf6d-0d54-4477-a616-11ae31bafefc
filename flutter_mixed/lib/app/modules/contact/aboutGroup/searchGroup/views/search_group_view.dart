import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';

import '../../../model/org/dept_members_model.dart';
import '../controllers/search_group_controller.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';

class SearchGroupView extends GetView<SearchGroupController> {
  const SearchGroupView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context,
              '搜索群组',
              false,
              controller.type == 1
                  ? [
                      Container(
                        width: 60,
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            pressedOpacity: 0.5,
                            child: const Text(
                              '确定',
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            ),
                            onPressed: () {
                              Get.back(
                                  result: {'list': controller.selectGroupList});
                            }),
                      )
                    ]
                  : [], onPressed: () {
            Get.back();
          }),
          body: ListView.builder(
            itemBuilder: (context, index) {
              Map? dataDic;
              MemberModel? tempModel;
              if (index > 0) {
                dataDic = controller.showList[index - 1];
                for (var j = 0; j < controller.selectGroupList.length; j++) {
                  MemberModel selectModel = controller.selectGroupList[j];
                  if (selectModel.userId == dataDic!['groupId']) {
                    tempModel = selectModel;
                    break;
                  }
                }
              }

              return index == 0
                  ? Container(
                      width: double.infinity,
                      color: Colors.white,
                      height: 50,
                      padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                      child: SettingWidget().backSearchWidget(
                          controller.searchController, controller.node, '',
                          onSub: (value) {
                        controller.searchGroup();
                      }, onTap: () {}))
                  : InkWell(
                      onTap: () {
                        if (controller.type == 0) {
                          controller.jumpGroupChat(dataDic!);
                        }

                        if (controller.type == 1) {
                          if (tempModel == null) {
                            if (controller.selectGroupList.length == 10) {
                              toast('最多选择10条会话');
                              return;
                            }
                            MemberModel groupModel =
                                MemberModel(dataDic!['groupId']);
                            groupModel.imId = dataDic!['groupId'];
                            groupModel.headimg = dataDic['logo'];
                            groupModel.name = dataDic['name'];
                            groupModel.sessionType = 2;
                            controller.selectGroupList.add(groupModel);
                          } else {
                            controller.selectGroupList.remove(tempModel);
                          }
                          controller.showList.refresh();
                        }
                      },
                      child: SettingWidget().backSettingWidget(
                          controller.type == 1
                              ? tempModel != null
                                  ? AssetsRes.APPROVE_SELECTED
                                  : AssetsRes.APPROVE_UNSELECTED
                              : '',
                          dataDic!['logo'],
                          dataDic['name'],
                          '',
                          false,
                          56),
                    );
            },
            itemCount: controller.showList.length + 1,
          ),
        ));
  }
}
