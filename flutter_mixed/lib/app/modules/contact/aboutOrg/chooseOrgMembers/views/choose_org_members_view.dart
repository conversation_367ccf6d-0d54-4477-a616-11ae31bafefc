import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';

import 'package:get/get.dart';

import '../../../model/org/dept_members_model.dart';
import '../controllers/choose_org_members_controller.dart';

class ChooseOrgMembersView extends GetView<ChooseOrgMembersController> {
  const ChooseOrgMembersView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        backgroundColor: ColorConfig.backgroundColor,
        extendBodyBehindAppBar: true,
        appBar: TitleBar().backAppbar(
            context, controller.type == 0 ? '请离成员' : '选择成员', false, [],
            onPressed: () {
          Get.back();
        }),
        body: Column(
          children: [
            Expanded(
                child: ListView.builder(
                    itemCount: controller.memberList.length + 1,
                    itemBuilder: (context, index) {
                      late MemberModel model;
                      if (index > 0) {
                        model = controller.memberList[index - 1];
                      }
                      return InkWell(
                        onTap: () {
                          if (controller.selectList.contains(model.userId)) {
                            controller.selectList.remove(model.userId);
                          } else {
                            controller.selectList.add(model.userId);
                          }
                        },
                        child: index == 0
                            ? Container(
                                width: double.infinity,
                                height: 40,
                                color: ColorConfig.whiteColor,
                                padding: EdgeInsets.only(left: 16),
                                child: Text(
                                  '该部门共有${controller.memberList.length}名成员',
                                  style: TextStyle(
                                      color: ColorConfig.desTextColor,
                                      fontSize: 12),
                                ),
                              )
                            : SettingWidget().backSettingWidget(
                                controller.selectList.contains(model.userId)
                                    ? 'assets/images/3.0x/login_selected.png'
                                    : 'assets/images/3.0x/login_unselect.png',
                                model.headimg,
                                model.name,
                                '',
                                true,
                                48),
                      );
                    })),
            Container(
              width: double.infinity,
              padding: EdgeInsets.only(
                  left: 15,
                  right: 15,
                  top: 16,
                  bottom: 16 + DeviceUtils().bottom.value),
              height: DeviceUtils().bottom.value + 76,
              child: CupertinoButton(
                  padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                  color: ColorConfig.themeCorlor,
                  borderRadius: const BorderRadius.all(Radius.circular(4)),
                  pressedOpacity: 0.5,
                  child: Text(
                    '下一步 (${controller.selectList.length})',
                    style:
                        TextStyle(color: ColorConfig.whiteColor, fontSize: 14),
                  ),
                  onPressed: () async {
                    if (controller.type == 1) {
                      if (controller.selectList.isNotEmpty) {
   
                        Get.toNamed('/dept-detail',
                            arguments: {
                              'topModel':controller.topModel,
                              'levelList': [controller.model!.name],
                              'deptId': '0',
                              'model': OrgModel.fromJson(controller.topModel!.toJson()),
                              'isManager': true,
                              'type': 1,
                              'selectList': controller.selectList,
                              'moveOrgModel': controller.model,
                              'companyId': controller.companyId
                            },
                            preventDuplicates: false);
                      } else {
                        toast('请选择要移动的成员');
                      }
                    } else {
                      if (controller.selectList.isNotEmpty) {
                        showCupertinoActionSheetWithLevel(context);
                      } else {
                        toast('请选择要请离的成员');
                      }
                    }
                  }),
            )
          ],
        )));
  }

  showCupertinoActionSheetWithLevel(context) async {
     await showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            actions: <Widget>[
              CupertinoActionSheetAction(
                child: Text(
                  '请离出公司',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
                onPressed: () {
                  Navigator.of(context).pop('cancel');
                  controller.leaveCompany(1);
                },
                isDefaultAction: true,
              ),
              CupertinoActionSheetAction(
                child: Text(
                  '请离出部门',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
                onPressed: () {
                  Navigator.of(context).pop('cancel');
                  controller.leaveCompany(2);
                },
                isDefaultAction: true,
              )
            ],
            cancelButton: CupertinoActionSheetAction(
              child: Text(
                '取消',
                style:
                    TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
              ),
              onPressed: () {
                Navigator.of(context).pop('cancel');
              },
            ),
          );
        });
  }
}
