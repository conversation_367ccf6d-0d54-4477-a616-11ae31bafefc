import 'dart:async';

import 'package:event_bus/event_bus.dart';
import 'package:flutter_mixed/app/common/event/event.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';

class ChooseOrgMembersController extends GetxController {

  /*选择部门成员进行移动和请离操作*/

  String companyId = '';
  OrgModel? model;
  List memberList = [];
  int type = 0; //1移动0请离
  RxList selectList = [].obs;

  StreamSubscription? subscription;
    OrgModel? topModel;//总公司数据
  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    memberList = Get.arguments['memberList'];
    type = Get.arguments['type'];
    model!.deptId = Get.arguments['deptId'];
    companyId = Get.arguments['companyId'];
    topModel = Get.arguments['topModel'];
    subscription = eventBus.on<Map>().listen((event) {
      if (event['refreshChooseMember'] != null) {
        selectList.clear();
        selectList.refresh();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  leaveCompany(int exitType) {
    if (model!.deptId == '1') {
      model!.deptId = '0';
    }
    Map param = {
      'deptId': model!.deptId,
      'exitType': exitType,
      'orgId': model!.companyId,
      'userIds': selectList
    };
    DioUtil().delete(ORGApi.LEAVELCOMPANY, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('已成功请离');
        eventBus.fire(DeptRefresh(model!));
        Get.back();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
