import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/addMembes/user_selected_widget.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/model/select-group-model.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/editDept/controllers/edit_dept_controller.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../addDept/controllers/add_dept_controller.dart';
import '../controllers/dept_detail_controller.dart';

class DeptDetailView extends GetView<DeptDetailController> {
  DeptDetailView({Key? key}) : super(key: key);
  DeptDetailController deptController = Get.find();
  FocusNode node = FocusNode();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: deptController,
        builder: (logic) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            backgroundColor: ColorConfig.backgroundColor,
            appBar: TitleBar().backAppbar(
                context,
                deptController.deptName.value,
                false,
                _dealAction(context), onPressed: () {
              if (deptController.type == 4) {
                Get.back(result: {
                  'selectGroupList': deptController.selectGroupList,
                  'isChooseEnd': false
                });
              } else {
                Get.back();
              }
            }, isNative: !deptController.isFromNative()),
            body: Column(
              children: [
                (DeviceUtils().top.value + 44).gap,
                Container(
                  width: double.infinity,
                  color: ColorConfig.whiteColor,
                  height: 50,
                  padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                  child: SettingWidget().backSearchWidget(
                      deptController.searchController, node, '搜索企业人员',
                      onSub: (value) {}, onTap: () async {
                    node.unfocus();
                    OrgModel bigModel = OrgModel(deptController.companyId);
                    if (deptController.model.deptId == '0') {
                      bigModel.power = '-1';
                    } else {
                      bigModel.power = deptController.model.power;
                    }

                    if (deptController.type == 4 || deptController.type == 5) {
                      var result = await Get.toNamed(Routes.SEARCH_ORG, arguments: {
                        'model': bigModel,
                        'type': deptController.type == 4 ? 1 : 6,
                        'chooseType': 2,
                        'selectList': RxList(deptController.selectGroupList
                            .map((e) => MemberModel.fromJson(e.toJson()))
                            .toList())
                      },preventDuplicates: false);
                      if (result != null && result['list'] != null) {
                        deptController.selectGroupList = result['list'];
                        deptController.dealselectGroupList();
                        deptController.update();
                      }
                    } else {
                      RouteHelper.route(Routes.SEARCH_ORG, arguments: {
                        'model': bigModel,
                        'type': 1,
                      });
                    }
                  }),
                ),
                UserSelected(
                  userList: deptController.chooseList,
                  selectGroupList: deptController.selectGroupList,
                ),
                SizedBox(
                  height: 10,
                ),
                Offstage(
                  offstage: deptController.model.deptId == '1' &&
                          !(deptController.model.power ?? '').contains('-1') &&
                          !(deptController.model.power ?? '').contains('1') &&
                          deptController.model.rejectInvitation == 1 ||
                      deptController.type == 4 ||
                      deptController.type == 5,
                  child: InkWell(
                    onTap: () {
                      Get.toNamed('/invite-home', arguments: {
                        'model': deptController.topModel,
                        'type': 0
                      });
                    },
                    child: SettingWidget()
                        .backSettingWidget('', '', '添加企业成员', '', true, 48),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                  width: double.infinity,
                  color: ColorConfig.whiteColor,
                  height: 38,
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.only(left: 15, right: 15),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: RichText(
                        text: TextSpan(
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                if (deptController.levelList.length > 1) {
                                  if (deptController.type == 4) {
                                    //联系人
                                    eventBus.fire(SelectGroupmodel(
                                        sign: Routes.GROUP_ADD_MEMBERS,
                                        selectGroupList:
                                            deptController.selectGroupList,
                                        isChooseEnd: false));
                                    Get.until((route) {
                                      if (route.settings.name ==
                                          Routes.GROUP_ADD_MEMBERS) {
                                        return true;
                                      }
                                      return false;
                                    });
                                  } else {
                                    Get.until((route) {
                                      Map argument =
                                          route.settings.arguments as Map;

                                      List levelList = argument['levelList'];
                                      if (levelList.length == 1) {
                                        return true;
                                      }
                                      return false;
                                    });
                                  }
                                }
                              },
                            text: deptController.levelList[0],
                            style: TextStyle(
                                fontSize: 14,
                                color: deptController.levelList.length == 1
                                    ? ColorConfig.desTextColor
                                    : ColorConfig.themeCorlor),
                            children: backTextSpan())),
                  ),
                ),
                SizedBox(
                  height: 1,
                ),
                Expanded(
                  child: MediaQuery.removePadding(
                      removeTop: true,
                      context: context,
                      child: ListView(
                        children: [
                          Container(
                            width: double.infinity,
                            color: ColorConfig.whiteColor,
                            height: 49.0 * deptController.deptList.length,
                            child: ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: deptController.deptList.length,
                                itemBuilder: (context, index) {
                                  Map<String, dynamic> deptDic =
                                      deptController.deptList[index];
                                  return InkWell(
                                    onTap: () async {
                                      List levelList = [];
                                      for (var i = 0;
                                          i < deptController.levelList.length;
                                          i++) {
                                        levelList.add(
                                            '${deptController.levelList[i]}');
                                      }
                                      levelList.add('${deptDic['name']}');

                                      OrgModel? currentModel;
                                      if (deptDic['companyId'] !=
                                          deptController.companyId) {
                                        currentModel =
                                            OrgModel.fromJson(deptDic);
                                      }

                                      var result = await Get.toNamed(
                                          '/dept-detail',
                                          arguments: {
                                            'topModel': deptController.topModel,
                                            'levelList': levelList,
                                            'deptId': deptDic['deptId'],
                                            'model': currentModel ??
                                                deptController.model,
                                            'isManager':
                                                deptController.isManager.value,
                                            'type': deptController.type,
                                            'selectList':
                                                deptController.selectList,
                                            'moveOrgModel':
                                                deptController.moveOrgModel,
                                            'companyId':
                                                deptController.companyId,
                                            'topId': deptController.deptId,
                                            'topName':
                                                deptController.deptName.value,
                                            'topCompanyId':
                                                deptController.model.companyId,
                                            'selectGroupList':
                                                deptController.selectGroupList,
                                          },
                                          preventDuplicates: false);
                                      if (result != null) {
                                        if (result['selectGroupList'] != null) {
                                          deptController.selectGroupList =
                                              result['selectGroupList'];
                                        }
                                        deptController.dealselectGroupList();
                                        deptController.update();
                                      }
                                    },
                                    child: SettingWidget().backSettingWidget(
                                        '',
                                        'assets/images/3.0x/contact_dept_logo.png',
                                        deptDic['name'],
                                        '',
                                        true,
                                        48),
                                  );
                                }),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            width: double.infinity,
                            height: 38,
                            padding: EdgeInsets.only(left: 15, right: 15),
                            child: Text(
                              '当前部门共${deptController.memberList.length}人',
                              style: TextStyle(
                                  fontSize: 12,
                                  color: ColorConfig.desTextColor),
                            ),
                          ),
                          Container(
                            width: double.infinity,
                            color: ColorConfig.whiteColor,
                            height: 49.0 * deptController.memberList.length,
                            child: ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: deptController.memberList.length,
                                itemBuilder: (context, index) {
                                  MemberModel model =
                                      deptController.memberList[index];
                                  MemberModel? tempModel;
                                  for (var i = 0;
                                      i < deptController.selectGroupList.length;
                                      i++) {
                                    MemberModel selectModel =
                                        deptController.selectGroupList[i];
                                    if (selectModel.userId == model.userId) {
                                      tempModel = selectModel;
                                      break;
                                    }
                                  }

                                  String signStr = '';
                                  Color color = ColorConfig.whiteColor;
                                  if (model.type == 1) {
                                    signStr = '创建者';
                                    color = ColorConfig.themeCorlor;
                                  }
                                  if (model.type == -1) {
                                    signStr = '超级管理员';
                                    color = ColorConfig.themeCorlor;
                                  }
                                  if (model.type == 2) {
                                    signStr = '负责人';
                                    color = ColorConfig.themeCorlor;
                                  }
                                  return InkWell(
                                    onTap: () {
                                      if (deptController.type == 4 ||
                                          deptController.type == 5) {
                                        if (tempModel != null) {
                                          if (tempModel.chooseState == 2) {
                                            toast('不能取消当前成员');
                                            return;
                                          }
                                          deptController.selectGroupList
                                              .remove(tempModel);
                                        } else {
                                          if (deptController.type == 5 &&
                                              deptController
                                                      .selectGroupList.length ==
                                                  10) {
                                            toast('最多选择10条会话');
                                            return;
                                          }
                                          deptController.selectGroupList
                                              .add(model);
                                        }
                                        deptController.dealselectGroupList();
                                        deptController.update();
                                      } else {
                                        OrgModel orgModel = OrgModel(
                                            deptController.model.companyId);
                                        orgModel.deptId = deptController.deptId;
                                        orgModel.power =
                                            deptController.model.power;
                                        RouteHelper.route(Routes.USER_INFO, arguments: {
                                          'userId': model.userId,
                                          'type': 3,
                                          'model': orgModel
                                        });
                                      }
                                    },
                                    child: SettingWidget().backSettingWidget(
                                        deptController.type != 4 &&
                                                deptController.type != 5
                                            ? ''
                                            : tempModel != null
                                                ? AssetsRes.APPROVE_SELECTED
                                                : AssetsRes.APPROVE_UNSELECTED,
                                        model.headimg,
                                        model.name,
                                        '',
                                        true,
                                        48,
                                        signStr: signStr,
                                        color: color),
                                  );
                                }),
                          )
                        ],
                      )),
                ),
                Offstage(
                  offstage: !deptController.isManager.value,
                  child: deptController.type == 0
                      ? Container(
                          alignment: Alignment.topCenter,
                          width: double.infinity,
                          height: DeviceUtils().bottom.value + 44,
                          color: ColorConfig.whiteColor,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Container(
                                width: DeviceUtils().width.value * 0.5,
                                height: 44,
                                child: CupertinoButton(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(7.5)),
                                    pressedOpacity: 0.5,
                                    child: const Text(
                                      '添加子部门',
                                      style: TextStyle(
                                          color: ColorConfig.themeCorlor,
                                          fontSize: 14),
                                    ),
                                    onPressed: () async {
                                      Get.toNamed('/add-dept', arguments: {
                                        'topModel': deptController.topModel,
                                        'model': deptController.model,
                                        'deptName':
                                            deptController.deptName.value,
                                        'deptId': deptController.deptId,
                                        'companyId': deptController.companyId
                                      });
                                    }),
                              ),
                              Container(
                                width: DeviceUtils().width.value * 0.5,
                                height: 44,
                                child: CupertinoButton(
                                    padding:
                                        const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(7.5)),
                                    pressedOpacity: 0.5,
                                    child: const Text(
                                      '批量操作',
                                      style: TextStyle(
                                          color: ColorConfig.themeCorlor,
                                          fontSize: 14),
                                    ),
                                    onPressed: () async {
                                      showCupertinoActionSheetWithMove(context);
                                    }),
                              ),
                            ],
                          ),
                        )
                      : Container(
                          width: double.infinity,
                          padding: EdgeInsets.only(
                              left: 15,
                              right: 15,
                              top: 16,
                              bottom: 16 + DeviceUtils().bottom.value),
                          height: DeviceUtils().bottom.value + 76,
                          child: CupertinoButton(
                              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                              color: ColorConfig.themeCorlor,
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(4)),
                              pressedOpacity: 0.5,
                              child: Text(
                                deptController.type == 1
                                    ? '移动到这里'
                                    : deptController.type == 2
                                        ? '选择此部门'
                                        : deptController.type == 3
                                            ? '选择此部门'
                                            : '',
                                style: TextStyle(
                                    color: Colors.white, fontSize: 14),
                              ),
                              onPressed: () async {
                                if (deptController.type == 1) {
                                  //移动部门
                                  deptController.moveMembersToDept();
                                } else if (deptController.type == 2) {
                                  //选择上级部门

                                  AddDeptController addDeptController =
                                      Get.find();
                                  addDeptController.deptId =
                                      deptController.deptId;
                                  addDeptController.topDept.value =
                                      deptController.deptName.value;
                                  Get.until((route) {
                                    Map argument =
                                        route.settings.arguments as Map;
                                    if (!argument['isManager']) {
                                      return true;
                                    }
                                    return false;
                                  });
                                } else if (deptController.type == 3) {
                                  //选择上级部门

                                  EditDeptController editDeptController =
                                      Get.find();
                                  editDeptController.topId =
                                      deptController.deptId;
                                  editDeptController.topName.value =
                                      deptController.deptName.value;
                                  editDeptController.topCompanyId =
                                      deptController.model.companyId;
                                  Get.until((route) {
                                    Map argument =
                                        route.settings.arguments as Map;
                                    if (!argument['isManager']) {
                                      return true;
                                    }
                                    return false;
                                  });
                                }
                              }),
                        ),
                )
              ],
            ),
          );
        });
  }

  _dealAction(context) {
    if (deptController.type < 4) {
      return _backOtherAction(context);
    } else if (deptController.type == 4 || deptController.type == 5) {
      return _backSureAction();
    }
  }

  _backOtherAction(context) {
    List<Widget> lists = [];
    lists.add(Offstage(
      offstage: deptController.model.deptId == '1' &&
          !(deptController.model.power ?? '').contains('-1') &&
          !(deptController.model.power ?? '').contains('1'),
      child: Container(
        width: 100,
        child: CupertinoButton(
            padding: const EdgeInsets.fromLTRB(60, 12, 20, 12),
            pressedOpacity: 0.5,
            child: Image.asset(
              deptController.isManager.value
                  ? 'assets/images/3.0x/contact_dept_close.png'
                  : 'assets/images/3.0x/contact_manager_org.png',
            ),
            onPressed: () {
              if (deptController.isManager.value) {
                Get.until((route) {
                  Map argument = route.settings.arguments as Map;
                  if (!argument['isManager']) {
                    return true;
                  }
                  return false;
                });
              } else {
                showCupertinoActionSheet(context, '');
              }
            }),
      ),
    ));
    return lists;
  }

  _backSureAction() {
    List<Widget> lists = [];
    lists.add(Container(
      padding: EdgeInsets.only(right: 16),
      child: CupertinoButton(
          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          pressedOpacity: 0.5,
          child: Text(
            deptController.backButtonTitle(),
            style: TextStyle(
                color: deptController.backBtnTitleColor(), fontSize: 14),
          ),
          onPressed: () {
            eventBus.fire(SelectGroupmodel(
                sign: Routes.GROUP_ADD_MEMBERS,
                selectGroupList: deptController.selectGroupList,
                isChooseEnd: true));
            Get.until((route) {
              if (route.settings.name == Routes.GROUP_ADD_MEMBERS) {
                return true;
              }
              return false;
            });
          }),
    ));

    return lists;
  }

  List<TextSpan> backTextSpan() {
    List<TextSpan> list = [];
    for (var i = 0; i < deptController.levelList.length - 1; i++) {
      TextSpan textSpan = TextSpan(
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              if (i < deptController.levelList.length - 2) {
                Get.until((route) {
                  Map argument = route.settings.arguments as Map;
                  if (argument['levelList'].length == i + 2) {
                    return true;
                  }
                  return false;
                });
              }
            },
          text: ' > ${deptController.levelList[i + 1]}',
          style: TextStyle(
              fontSize: 14,
              color: i == deptController.levelList.length - 2
                  ? ColorConfig.desTextColor
                  : ColorConfig.themeCorlor));
      list.add(textSpan);
    }
    return list;
  }

  showCupertinoActionSheet(context, msg) async {
    await showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            actions: <Widget>[
              CupertinoActionSheetAction(
                child: Text(
                  '管理组织架构',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
                onPressed: () {
                  Navigator.of(context).pop('cancel');
                  Get.toNamed('/dept-detail',
                      arguments: {
                        'topModel': deptController.topModel,
                        'levelList': [deptController.topModel!.name],
                        'deptId': deptController.topModel!.deptId,
                        'model': OrgModel.fromJson(
                            deptController.topModel!.toJson()),
                        'isManager': true,
                        'companyId': deptController.companyId,
                      },
                      preventDuplicates: false);
                },
                isDefaultAction: true,
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              child: Text(
                '取消',
                style:
                    TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
              ),
              onPressed: () {
                Navigator.of(context).pop('cancel');
              },
            ),
          );
        });
  }

  showCupertinoActionSheetWithMove(context) async {
    await showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            actions: <Widget>[
              CupertinoActionSheetAction(
                child: Text(
                  '编辑部门',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
                onPressed: () async {
                  Navigator.of(context).pop('cancel');
                  var reslut = await Get.toNamed('/edit-dept', arguments: {
                    'topModel': deptController.topModel,
                    'model': deptController.model,
                    'deptId': deptController.deptId,
                    'topId': deptController.topId,
                    'topName': deptController.topName,
                    'companyId': deptController.companyId,
                    'topCompanyId': deptController.topCompanyId,
                  });
                  if (reslut['deleteDept'] == deptController.deptId) {
                    Get.back();
                  }
                },
                isDefaultAction: true,
              ),
              CupertinoActionSheetAction(
                child: Text(
                  '批量移动',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
                onPressed: () {
                  Navigator.of(context).pop('cancel');
                  if (deptController.memberList.isNotEmpty) {
                    Get.toNamed('/choose-org-members', arguments: {
                      'topModel': deptController.topModel,
                      'model': deptController.model,
                      'deptId': deptController.deptId,
                      'memberList': deptController.memberList,
                      'type': 1,
                      'companyId': deptController.companyId,
                    });
                  } else {
                    toast('该部门没有可选择的成员');
                  }
                },
                isDefaultAction: true,
              ),
              CupertinoActionSheetAction(
                child: Text(
                  '批量请离',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
                onPressed: () {
                  Navigator.of(context).pop('cancel');
                  if (deptController.memberList.isNotEmpty) {
                    Get.toNamed('/choose-org-members', arguments: {
                      'topModel': deptController.topModel,
                      'model': deptController.model,
                      'deptId': deptController.deptId,
                      'memberList': deptController.memberList,
                      'type': 0,
                      'companyId': deptController.companyId,
                    });
                  } else {
                    toast('该部门没有可请离的成员');
                  }
                },
                isDefaultAction: true,
              )
            ],
            cancelButton: CupertinoActionSheetAction(
              child: Text(
                '取消',
                style:
                    TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
              ),
              onPressed: () {
                Navigator.of(context).pop('cancel');
              },
            ),
          );
        });
  }
}
