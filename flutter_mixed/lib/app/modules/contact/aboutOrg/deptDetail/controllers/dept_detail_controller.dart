import 'dart:async';

import 'package:event_bus/event_bus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/dialog/msg-dialog.dart';
import 'package:flutter_mixed/app/common/event/event.dart';
import 'package:flutter_mixed/app/common/widgets/base_get_controller.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/model/select-group-model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../model/org/dept_members_model.dart';

class DeptDetailController extends BaseGetXController {
  StreamSubscription? subscription;
  StreamSubscription? subscription1;
  StreamSubscription? selectSubscription;
  TextEditingController searchController = TextEditingController();
  String companyId = ''; //总公司id
  RxString titleStr = ''.obs;
  List levelList = [];
  RxList deptList = [].obs;
  RxList memberList = [].obs;
  OrgModel? topModel; //总公司数据
  late OrgModel model; //当前公司公司数据
  RxString deptName = ''.obs;
  String deptId = '';
  RxBool isManager = false.obs;

  int type = 0; //1移动2添加部门3编辑部门4群组创建、邀请 5转发
  List selectList = []; //要移动的人员
  OrgModel? moveOrgModel;

  String topId = ''; //上级部门id
  String topName = ''; //上级部门名称
  String topCompanyId = ''; //上级部门公司id

  RxList selectGroupList = [].obs; //选择群组人员数组
  RxBool isLoad = false.obs;
  String myUserId = '';
  RxList chooseList = [].obs;
  @override
  void onInit() async {
    super.onInit();
    companyId = Get.arguments['companyId'] ?? '';

    if (Get.arguments['levelList'] != null) {
      levelList = Get.arguments['levelList'];
    }
    if (Get.arguments['deptId'] != null) {
      deptId = Get.arguments['deptId'];
    }
    if (Get.arguments['isManager'] != null) {
      isManager.value = Get.arguments['isManager'];
    }

    if (Get.arguments['type'] != null) {
      type = Get.arguments['type'];
    }
    if (Get.arguments['selectList'] != null) {
      selectList = Get.arguments['selectList'];
    }
    if (Get.arguments['topId'] != null) {
      topId = Get.arguments['topId'];
    }
    if (Get.arguments['topName'] != null) {
      topName = Get.arguments['topName'];
    }
    if (Get.arguments['topModel'] != null) {
      topModel = Get.arguments['topModel'];
    }
    if (Get.arguments['topCompanyId'] != null) {
      topCompanyId = Get.arguments['topCompanyId'];
    }
    if (Get.arguments['model'] != null) {
      model = Get.arguments['model'];
    }
    if (Get.arguments['isFromWork'] != null) {
      int isFromWork = Get.arguments['isFromWork'];
      if (isFromWork == 1) {
        WorkFlowController workFlowController = Get.find();
        topModel = workFlowController.currentModel;
        levelList = [topModel!.name];
        deptId = topModel!.deptId;
        model = OrgModel.fromJson(workFlowController.currentModel!.toJson());
      }
    }

    if (Get.arguments['selectGroupList'] != null) {
      selectGroupList = Get.arguments['selectGroupList'];
      dealselectGroupList();
      Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
      myUserId = tokenInfo['userId'];
    }
    if (Get.arguments['moveOrgModel'] != null) {
      moveOrgModel = Get.arguments['moveOrgModel'];
    }

    subscription = eventBus.on<DeptRefresh>().listen((event) {
      if (event.model.companyId == model.companyId &&
          (event.model.deptId == deptId)) {
        getDeptDetail();
      } else {
        //非创建者deptId为1
        if ((event.model.deptId == '0' || event.model.deptId == '1') &&
            (deptId == '0' || deptId == '1')) {
          getDeptDetail();
        }
      }
    });

    subscription1 = eventBus.on<Map>().listen((event) {
      if (event['deleteDept'] != null) {
        bool isRefresh = false;
        for (var i = 0; i < deptList.length; i++) {
          Map deptDic = deptList[i];
          if (deptDic['deptId'] == event['deptId']) {
            isRefresh = true;
          }
        }
        if (isRefresh) {
          getDeptDetail();
        }
      }
    });

    selectSubscription = eventBus.on<SelectGroupmodel>().listen((event) {
      if (event.sign != null) {
        if (event.sign == Routes.GROUP_ADD_MEMBERS &&
            event.selectGroupList != null) {
          selectGroupList = event.selectGroupList!;
          dealselectGroupList(isNeed: false);
          update();
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    getDeptDetail();
  }

  @override
  void onClose() {
    searchController.dispose();
    subscription?.cancel();
    subscription1?.cancel();
    selectSubscription?.cancel();
    super.onClose();
  }

  //获取部门详情
  getDeptDetail() async {
    DioUtil()
        .get('${ORGApi.PERSONOUTORG}/${model.companyId}/dept/$deptId', null,
            true, () {},isShowLoading: false)
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataDic = data['data'];
        deptName.value = dataDic['deptName'];
        if (dataDic['branchList'] != null) {
          deptList.value = dataDic['branchList'];
        }
        if (dataDic['memberList'] != null) {
          memberList.value = dataDic['memberList']
              .map((item) => MemberModel.fromJson(item))
              .toList();
        }
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //批量移动人员到部门
  moveMembersToDept() async {
    List haveList = [];
    for (var i = 0; i < memberList.length; i++) {
      MemberModel memberModel = memberList[i];
      for (var j = 0; j < selectList.length; j++) {
        if (memberModel.userId == selectList[j]) {
          haveList.add(memberModel);
        }
      }
    }

    List resultList = List.from(selectList);
    if (haveList.isNotEmpty) {
      String nameStr = '';
      for (var i = 0; i < haveList.length; i++) {
        MemberModel memberModel = haveList[i];
        if (i == 0) {
          nameStr = memberModel.name;
        } else {
          nameStr = '$nameStr、${memberModel.name}';
        }
        resultList.remove(memberModel.userId);
      }

      MsgDiaLog(
          '',
          '成员$nameStr已经在该部门下，不可重复移动${resultList.isEmpty ? '' : '，是否继续移动其他成员'}',
          '取消',
          '确定', () {
        Navigator.of(Get.context!).pop();
      }, () {
        if (resultList.isNotEmpty) {
          sureMove(resultList);
        }

        Navigator.of(Get.context!).pop();
      }).show();
    } else {
      sureMove(resultList);
    }
  }

  sureMove(List resultList) {
    if (deptId == '1') {
      deptId = '0';
    }
    String moveDeptId = moveOrgModel!.deptId;
    if (moveDeptId == '1') {
      moveDeptId = '0';
    }
    print('movemodel----${moveOrgModel!.companyId}');
    Map param = {
      'orgId': moveOrgModel!.companyId,
      'deptId': moveDeptId,
      'targetOrgId': model.companyId,
      'targetDeptId': deptId,
      'userIds': resultList
    };
    DioUtil().post(ORGApi.USERSMOVE, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('转移成功');

        OrgModel targetModel = OrgModel(model.companyId);
        targetModel.deptId = deptId;
        eventBus.fire(DeptRefresh(targetModel));
        eventBus.fire(DeptRefresh(moveOrgModel!));
        eventBus.fire({'refreshChooseMember': 1});
        Get.until((route) {
          Map argument = route.settings.arguments as Map;
          if (route.settings.name == Routes.DEPT_DETAIL &&
              argument['type'] != 1) {
            return true;
          }
          return false;
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }

  dealselectGroupList({bool isNeed = true}) {
    //isNeed 是否需要发送同步
    if (isNeed) {
      eventBus.fire(SelectGroupmodel(
        sign: Routes.GROUP_ADD_MEMBERS,
        selectGroupList: selectGroupList,
        isChooseEnd: false,
      ));
    }

    chooseList.clear();
    for (var i = 0; i < selectGroupList.length; i++) {
      MemberModel model = selectGroupList[i];
      if (model.chooseState != 2 && type == 4) {
        chooseList.add(model);
      }
    }
  }

  backButtonTitle() {
    if (type == 5) {
      return '确定';
    } else if (type == 4) {
      return '确定(${chooseList.length})';
    }
    return '';
  }

  backBtnTitleColor() {
    if (type == 5) {
      return ColorConfig.mainTextColor;
    } else if (type == 4) {
      if (chooseList.isEmpty) {
        return ColorConfig.desTextColor;
      } else {
        return ColorConfig.themeCorlor;
      }
    }
    return Colors.transparent;
  }
}
