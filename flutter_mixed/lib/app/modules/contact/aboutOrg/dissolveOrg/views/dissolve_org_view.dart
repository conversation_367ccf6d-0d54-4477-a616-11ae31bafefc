import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/dissolve_org_controller.dart';

class DissolveOrgView extends GetView<DissolveOrgController> {
  const DissolveOrgView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.whiteColor,
          extendBodyBehindAppBar: true,
          appBar:
              TitleBar().backAppbar(context, '解散企业', false, [], onPressed: () {
            Get.back();
          }),
          body: ListView(
            children: [
              Container(
                padding: EdgeInsets.only(left: 15),
                alignment: Alignment.centerLeft,
                width: double.infinity,
                height: 35,
                child: Text(
                  '输入手机号验证码',
                  style:
                      TextStyle(color: ColorConfig.mainTextColor, fontSize: 20),
                ),
              ),
              SizedBox(
                height: 15,
              ),
              Container(
                padding: EdgeInsets.only(left: 15,right: 15),
                alignment: Alignment.centerLeft,
                width: double.infinity,
                height: 38,
                child: Text(
                  '请输入发送至企业创建者手机号 ${controller.phoneHide} 的4位验证码，有效期5分钟',
                  style:
                      TextStyle(color: ColorConfig.desTextColor, fontSize: 14),
                ),
              ),
              SizedBox(
                height: 35,
              ),
              Container(
                height: controller.verifyContainerH,
                width: DeviceUtils().width.value - 40,
                child: Stack(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          alignment: AlignmentDirectional.center,
                          width: controller.verifyContainerH,
                          height: controller.verifyContainerH,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7.5),
                              border: Border.all(
                                  color: controller.codeText.value.isNotEmpty
                                      ? ColorConfig.themeCorlor
                                      : ColorConfig.lineColor)),
                          child: Text(
                            controller.codeText.value.isNotEmpty
                                ? controller.codeText.value.getRange(0, 1)
                                : '',
                            style: TextStyle(
                                color: ColorConfig.mainTextColor,
                                fontSize: 24.0,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                        Container(
                          alignment: AlignmentDirectional.center,
                          width: controller.verifyContainerH,
                          height: controller.verifyContainerH,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7.5),
                              border: Border.all(
                                  color: controller.codeText.value.length > 1
                                      ? ColorConfig.themeCorlor
                                      : ColorConfig.lineColor)),
                          child: Text(
                            controller.codeText.value.length > 1
                                ? controller.codeText.value.getRange(1, 2)
                                : '',
                            style: TextStyle(
                                color: ColorConfig.mainTextColor,
                                fontSize: 24.0,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                        Container(
                          height: 2,
                          width: 11,
                          color: ColorConfig.mainTextColor,
                        ),
                        Container(
                          alignment: AlignmentDirectional.center,
                          width: controller.verifyContainerH,
                          height: controller.verifyContainerH,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7.5),
                              border: Border.all(
                                  color: controller.codeText.value.length > 2
                                      ? ColorConfig.themeCorlor
                                      : ColorConfig.lineColor)),
                          child: Text(
                            controller.codeText.value.length > 2
                                ? controller.codeText.value.getRange(2, 3)
                                : '',
                            style: TextStyle(
                                color: ColorConfig.mainTextColor,
                                fontSize: 24.0,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                        Container(
                          alignment: AlignmentDirectional.center,
                          width: controller.verifyContainerH,
                          height: controller.verifyContainerH,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7.5),
                              border: Border.all(
                                  color: controller.codeText.value.length > 3
                                      ? ColorConfig.themeCorlor
                                      : ColorConfig.lineColor)),
                          child: Text(
                            controller.codeText.value.length > 3
                                ? controller.codeText.value.getRange(3, 4)
                                : '',
                            style: TextStyle(
                                color: ColorConfig.mainTextColor,
                                fontSize: 24.0,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 0),
                      child: TextField(
                        controller: controller.editingController,
                        textAlign: TextAlign.start,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(4),
                        ],
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          controller.codeText.value = value;
                          if (value.length == 4) {
                            controller.dissolveOrg();
                          }
                        },
                        showCursor: false,
                        style: TextStyle(
                          color: Colors.transparent,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                          letterSpacing: 36,
                        ),
                        decoration: InputDecoration(
                            hintText: "",
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.only(left: 30)),
                        // )
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Container(
                padding: EdgeInsets.only(left: 15),
                width: double.infinity,
                height: 25,
                alignment: Alignment.centerLeft,
                child: CupertinoButton(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    color: ColorConfig.whiteColor,
                    borderRadius: const BorderRadius.all(Radius.circular(3)),
                    pressedOpacity: 0.5,
                    child: Text(
                      controller.timeInt.value == 0
                          ? '获取验证码'
                          : '${controller.timeInt.value}s可重新获取验证码',
                      style: TextStyle(
                          color: controller.timeInt.value == 0
                              ? ColorConfig.themeCorlor
                              : ColorConfig.desTextColor,
                          fontSize: 14),
                    ),
                    onPressed: () {
                      controller.sendCode();
                    }),
              ),
              
            ],
          ),
        ));
  }
}
