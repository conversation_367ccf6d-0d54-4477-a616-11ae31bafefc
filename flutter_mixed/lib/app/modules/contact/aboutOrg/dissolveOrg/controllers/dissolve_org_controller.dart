import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../model/org/org_model.dart';

class DissolveOrgController extends GetxController {

  TextEditingController editingController = TextEditingController();
  RxString codeText = ''.obs;
  double verifyContainerH = 50.00;
  Timer? codeTimer;
  RxInt timeInt = 0.obs;

  String phone = '';
  RxString phoneHide = ''.obs;

  late OrgModel model;
  @override
  void onInit() async {
    super.onInit();
    model = Get.arguments['model'];
    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    phone = userInfo['mobile'];
    phoneHide.value = phone.replaceRange(3, 7, '****');
    sendCode();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    editingController.dispose();
    if (codeTimer != null) {
      codeTimer!.cancel();
    }
    super.onClose();
  }

  timerStart() {
    timeInt.value = 60;
    codeTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (timer.tick == 60) {
        timeInt.value = 0;
        timer.cancel();
      } else {
        timeInt.value--;
      }
    });
  }

  dissolveOrg() {
    DioUtil().delete(
        '${ORGApi.DELETEORG}/${model.companyId}/phone/$phone/code/${codeText.value}',
        null,
        true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('解散企业成功');
        HomeController homeController = Get.find();
        homeController.getAllCompanies();
        Get.until((route) {
          if (route.settings.name == Routes.ORG_LIST ||
              route.settings.name == Routes.HOME) {
            return true;
          }
          return false;
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }

  sendCode() {
    if(timeInt.value != 0) return;
    DioUtil().get('${LoginApi.GETSMSCODE}$phone/8', null, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('发送成功');
        timerStart();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
