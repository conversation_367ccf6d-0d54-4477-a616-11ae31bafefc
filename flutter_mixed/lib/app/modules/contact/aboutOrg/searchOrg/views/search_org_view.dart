import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/orgWidget/org_widget.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/search_org_members_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../../common/widgets/widgets.dart';
import '../../../model/org/external_model.dart';
import '../controllers/search_org_controller.dart';

class SearchOrgView extends GetView<SearchOrgController> {
  SearchOrgView({Key? key}) : super(key: key);
  SearchOrgController searchOrgController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      global: false,
      init: searchOrgController,
      builder: (logic){
      return Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: ColorConfig.backgroundColor,
          appBar: TitleBar().backAppbar(
              context,
              backTitle(),
              false,
              searchOrgController.chooseType == 0
                  ? []
                  : [
                      Container(
                        width: 60,
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            pressedOpacity: 0.5,
                            child: const Text(
                              '确定',
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            ),
                            onPressed: () {
                              Get.back(result: {'list': searchOrgController.selectList});
                            }),
                      )
                    ], onPressed: () {
            // Focus.of(context).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
            Get.back();
          }),
          body: ListView.builder(
            itemBuilder: (context, index) {
              late OrgModel model;
              late ExternalModel externalModel;
              late UserModel userModel;
              late SearchMemberModel memberModel;

              bool isSelect = false;
              if (index > 0 && searchOrgController.dataList.isNotEmpty) {
                if (searchOrgController.type == 4 || searchOrgController.type == 5) {
                  userModel = searchOrgController.dataList[index - 1];
                  for (var i = 0; i < searchOrgController.selectList.length; i++) {
                    MemberModel selectModel = searchOrgController.selectList[i];
                    if (selectModel.userId == userModel.userId) {
                      isSelect = true;
                    }
                  }
                } else if (searchOrgController.type == 3) {
                  externalModel = searchOrgController.dataList[index - 1];

                  for (var i = 0; i < searchOrgController.selectList.length; i++) {
                    ExternalModel selectModel = searchOrgController.selectList[i];
                    if (selectModel.userId == externalModel.userId) {
                      isSelect = true;
                    }
                  }
                } else if (searchOrgController.type == 2) {
                  userModel = searchOrgController.dataList[index - 1];
                } else if (searchOrgController.type == 1 || searchOrgController.type == 6) {
                  memberModel = searchOrgController.dataList[index - 1];

                  for (var i = 0; i < searchOrgController.selectList.length; i++) {
                    MemberModel selectModel = searchOrgController.selectList[i];
                    if (selectModel.userId == memberModel.userId) {
                      isSelect = true;
                    }
                  }
                } else if (searchOrgController.type == 0) {
                  model = searchOrgController.dataList[index - 1];
                }
              }
                    String selectedStr = AssetsRes.APPROVE_SELECTED;
                    String unSelectedStr = AssetsRes.APPROVE_UNSELECTED;
                    if (searchOrgController.chooseType == 1) {
                      selectedStr = 'assets/images/3.0x/login_selected.png';
                      unSelectedStr = 'assets/images/3.0x/login_unselect.png';
                    }
              return InkWell(
                onTap: () async {
                  if (searchOrgController.type == 2) {
                    RouteHelper.route(Routes.USER_INFO,
                        arguments: {'userId': userModel.userId, 'type': 1});
                  } else if (searchOrgController.type == 0) {
                    Get.toNamed('/org-detail', arguments: {'model': model});
                  }
                },
                child: index == 0
                    ? Container(
                        width: double.infinity,
                        color: ColorConfig.whiteColor,
                        height: 50,
                        padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                        child: SettingWidget().backSearchWidget(
                            searchOrgController.searchController,
                            searchOrgController.node,
                            backTitle(), onSub: (value) {
                          searchOrgController.isSearch = true;
                          if (searchOrgController.type == 4 || searchOrgController.type == 5) {
                            searchOrgController.serachFriends();
                          } else if (searchOrgController.type == 3) {
                            searchOrgController.getExternalList();
                          } else if (searchOrgController.type == 2) {
                            searchOrgController.searchTel();
                          } else if (searchOrgController.type == 1 ||
                              searchOrgController.type == 6) {
                            searchOrgController.searchOrgMembers(
                                searchOrgController.searchController.text);
                          } else if (searchOrgController.type == 0) {
                            searchOrgController
                                .searchOrg(searchOrgController.searchController.text);
                          }
                        }, onTap: () {}),
                      )
                    : searchOrgController.dataList.isEmpty && searchOrgController.isSearch
                        ? Container(
                            width: double.infinity,
                            height: (DeviceUtils().height.value - 130) * 0.5,
                            alignment: Alignment.center,
                            child: Text(
                              backMsg(),
                              style: TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.mainTextColor),
                            ),
                          )
                        : searchOrgController.type == 4 || searchOrgController.type == 5
                            ? InkWell(
                                onTap: () {
                                  if (searchOrgController.chooseType == 0) {
                                    RouteHelper.route(Routes.USER_INFO, arguments: {
                                      'userId': userModel.userId,
                                      'type': 1
                                    });
                                  } else {
                                    if (isSelect) {
                                      int selectIndex = -1;

                                      MemberModel? tempSelectModel;
                                      for (var i = 0;
                                          i < searchOrgController.selectList.length;
                                          i++) {
                                        MemberModel selectMemModel =
                                            searchOrgController.selectList[i];
                                        if (selectMemModel.userId ==
                                            userModel.userId) {
                                          tempSelectModel = selectMemModel;
                                          selectIndex = i;
                                        }
                                      }
                                      if (selectIndex >= 0) {
                                        if (tempSelectModel!.chooseState == 2) {
                                          toast('不能取消当前成员');
                                          return;
                                        }
                                        isSelect = false;
                                        searchOrgController.selectList
                                            .removeAt(selectIndex);
                                      }
                                    } else {
                                      isSelect = true;

                                      if (searchOrgController.chooseType == 1) {
                                        searchOrgController.selectList.clear();
                                      }
                                      if (searchOrgController.type == 5 &&
                                          searchOrgController.selectList.length == 10) {
                                        toast('最多选择10条会话');
                                        return;
                                      }
                                      MemberModel addUserModel =
                                          MemberModel(userModel.userId);
                                      addUserModel.headimg = userModel.avatar;
                                      addUserModel.name = userModel.name;
                                      addUserModel.remark = userModel.remark;
                                      addUserModel.imId = userModel.imId;
                                      searchOrgController.selectList.add(addUserModel);
                                    }
                                    searchOrgController.update();
                                  }
                                },
                                child: SettingWidget().backSettingWidget(
                                    searchOrgController.chooseType == 0
                                        ? ''
                                        : isSelect
                                            ? selectedStr
                                            : unSelectedStr,
                                    userModel.avatar,
                                    userModel.name,
                                    '',
                                    true,
                                    60),
                              )
                            : searchOrgController.type == 3
                                ? InkWell(
                                    onTap: () {
                                      if (searchOrgController.chooseType > 0) {
                                        if (isSelect) {
                                          isSelect = false;
                                          int selectIndex = -1;
                                          for (var i = 0;
                                              i < searchOrgController.selectList.length;
                                              i++) {
                                            ExternalModel selectMemModel =
                                                searchOrgController.selectList[i];
                                            if (selectMemModel.userId ==
                                                externalModel.userId) {
                                              selectIndex = i;
                                            }
                                          }
                                          if (selectIndex >= 0) {
                                            searchOrgController.selectList
                                                .removeAt(selectIndex);
                                          }
                                        } else {
                                          isSelect = true;
                                          // ExternalModel selectMemModel =
                                          //     ExternalModel(
                                          //         memberModel.userId);
                                          // selectMemModel.avatar =
                                          //     externalModel.avatar;
                                          // selectMemModel.name =
                                          //     externalModel.name;
                                          if (searchOrgController.chooseType == 1) {
                                            searchOrgController.selectList.clear();
                                          }
                                          searchOrgController.selectList
                                              .add(externalModel);
                                        }
                                        searchOrgController.update();
                                      } else {
                                        Get.toNamed('/external-detail',
                                            arguments: {
                                              'model': searchOrgController.model,
                                              'userId': externalModel.userId
                                            });
                                      }
                                    },
                                    child: getExternalListWidget(externalModel),
                                  )
                                : searchOrgController.type == 2
                                    ? getUserInfoWidget(userModel)
                                    : searchOrgController.type == 1 ||
                                            searchOrgController.type == 6
                                        ? InkWell(
                                            onTap: () {
                                              if (searchOrgController.chooseType > 0) {
                                                if (isSelect) {
                                                  int selectIndex = -1;
                                                  MemberModel? tempSelectModel;
                                                  for (var i = 0;
                                                      i <
                                                          searchOrgController.selectList
                                                              .length;
                                                      i++) {
                                                    MemberModel selectMemModel =
                                                        searchOrgController
                                                            .selectList[i];
                                                    if (selectMemModel.userId ==
                                                        memberModel.userId) {
                                                      tempSelectModel =
                                                          selectMemModel;
                                                      selectIndex = i;
                                                    }
                                                  }
                                                  if (selectIndex >= 0) {
                                                    if (tempSelectModel!
                                                            .chooseState ==
                                                        2) {
                                                      toast('不能取消当前成员');
                                                      return;
                                                    }
                                                    searchOrgController.selectList
                                                        .removeAt(selectIndex);
                                                  }
                                                  isSelect = false;
                                                } else {
                                                  if (searchOrgController.type == 6 &&
                                                      searchOrgController.selectList
                                                              .length ==
                                                          10) {
                                                    toast('最多选择10条会话');
                                                    return;
                                                  }
                                                  isSelect = true;
                                                  MemberModel selectMemModel =
                                                      MemberModel(
                                                          memberModel.userId);
                                                  selectMemModel.headimg =
                                                      memberModel.avatar;
                                                  selectMemModel.name =
                                                      memberModel.userName;
                                                  selectMemModel.imId =
                                                      memberModel.imId;
                                                  if (searchOrgController.chooseType ==
                                                      1) {
                                                    searchOrgController.selectList
                                                        .clear();
                                                  }
                                                  searchOrgController.selectList
                                                      .add(selectMemModel);
                                                }
                                                searchOrgController.update();
                                              } else {
                                                OrgModel model =
                                                    OrgModel(memberModel.orgId);
                                                model.deptId =
                                                    memberModel.deptId;
                                                model.power =
                                                    searchOrgController.model.power;
                                                RouteHelper.route(Routes.USER_INFO,
                                                    arguments: {
                                                      'userId':
                                                          memberModel.userId,
                                                      'type': 3,
                                                      'model': model
                                                    });
                                              }
                                            },
                                            child: OrgWidget().backDeptMemberWidget(
                                                searchOrgController.chooseType == 0
                                                    ? ''
                                                    : isSelect
                                                        ? selectedStr
                                                        : unSelectedStr,
                                                memberModel.avatar,
                                                memberModel.userName,
                                                '${memberModel.orgName} - ${memberModel.deptName}${memberModel.positionName.isNotEmpty ? ' - ${memberModel.positionName}' : ''}',
                                                memberModel.powerName),
                                          )
                                        : SettingWidget().backSettingWidget(
                                            '',
                                            model.logo,
                                            model.name,
                                            '',
                                            true,
                                            60),
              );
            },
            itemCount: searchOrgController.isSearch && searchOrgController.dataList.isEmpty
                ? 2
                : searchOrgController.dataList.length + 1,
          ),
        );
    });
  }

  backTitle() {
    switch (searchOrgController.type) {
      case 0:
        return '加入企业';
      case 1:
        return '搜索公司成员';

      case 2:
        return '搜索手机号';
      case 3:
        return '搜索外部联系人';
      case 4:
        return '搜索好友';
      case 5:
        return '搜索好友';
      case 6:
        return '搜索公司成员';
      default:
        return '';
    }
  }

  backMsg() {
    switch (searchOrgController.type) {
      case 0:
        return '未找到符合条件的企业';
      case 1:
        return '未找到符合条件的用户';

      case 2:
        return '未找到符合条件的用户';
      case 3:
        return '未找到符合条件的用户';
      case 4:
        return '未找到符合条件的好友';
      case 5:
        return '未找到符合条件的用户';
      case 6:
        return '未找到符合条件的好友';
      default:
        return '';
    }
  }

  //搜索手机号控件
  getUserInfoWidget(UserModel userModel) {
    return Container(
      width: double.infinity,
      height: 56,
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.fromLTRB(15, 8, 15, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: DeviceUtils().width.value - 30 - 42 - 10,
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image:
                              SettingWidget.backImageProvider(userModel.avatar),fit: BoxFit.cover),
                      borderRadius: BorderRadius.circular(20)),
                ),
                SizedBox(
                  width: 8,
                ),
                Expanded(
                    child: Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    userModel.name,
                    style: TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                )),
              ],
            ),
          ),
          Offstage(
            offstage: userModel.friendState != 0,
            child: InkWell(
              onTap: () {
                searchOrgController.voliteIsAllow(userModel);
              },
              child: Container(
                alignment: Alignment.center,
                width: 42,
                height: 22,
                decoration: BoxDecoration(
                    border:
                        Border.all(width: 0.5, color: ColorConfig.themeCorlor),
                    borderRadius: BorderRadius.circular(2)),
                child: Text(
                  '添加',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.themeCorlor),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  //外部联系人控件
  getExternalListWidget(ExternalModel model) {
    bool isHave = false;
    for (var i = 0; i < searchOrgController.selectList.length; i++) {
      ExternalModel externalModel = searchOrgController.selectList[i];
      if (externalModel.userId == model.userId) {
        isHave = true;
      }
    }
    return Container(
      width: double.infinity,
      height: 58,
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.only(left: 15, right: 15, top: 9, bottom: 9),
      child: Row(
        children: [
          Offstage(
            offstage: searchOrgController.chooseType == 0,
            child: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: Image.asset(isHave
                      ? 'assets/images/3.0x/login_selected.png'
                      : 'assets/images/3.0x/login_unselect.png'),
                ),
                SizedBox(
                  width: 8,
                )
              ],
            ),
          ),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: SettingWidget.backImageProvider(model.avatar)),
                borderRadius: BorderRadius.circular(20)),
          ),
          SizedBox(
            width: 10,
          ),
          Expanded(
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      constraints: BoxConstraints(
                          maxWidth: DeviceUtils().width.value - 154),
                      height: 22,
                      child: Text(
                        model.name,
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      ),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    Container(
                      alignment: Alignment.center,
                      width: model.level == 1 ? 64 : 40,
                      height: 22,
                      decoration: BoxDecoration(
                          border: Border.all(
                              width: 0.5,
                              color: searchOrgController.levelColorList[model.level]),
                          borderRadius: BorderRadius.circular(3)),
                      child: Text(
                        searchOrgController.levelList[model.level],
                        style: TextStyle(
                            fontSize: 12,
                            color: searchOrgController.levelColorList[model.level]),
                      ),
                    )
                  ],
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  height: 20,
                  child: Text(
                    searchOrgController.tabList[model.type],
                    style: TextStyle(
                        fontSize: 12, color: ColorConfig.desTextColor),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
