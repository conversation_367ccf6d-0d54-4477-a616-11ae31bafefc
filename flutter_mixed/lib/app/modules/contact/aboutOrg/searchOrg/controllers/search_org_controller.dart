import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/search_org_members_model.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/base_info/info.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../routes/app_pages.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../model/org/external_model.dart';

class SearchOrgController extends GetxController {

  RxList dataList = [].obs;
  TextEditingController searchController = TextEditingController();

  int type = 0; //0加入企业1搜索公司员工2搜索手机号3外部联系人4好友搜索5好友转发6公司成员转发
  int chooseType = 0; //0非选择类1可以选择单人2可以选择多人
  List tabList = ['全部', '客户', '渠道商', '供应商', '合作伙伴', '其他'];
  List levelList = ['全部', '非常重要', '重要', '普通'];
  List levelColorList = [
    ColorConfig.externalLevelOne,
    ColorConfig.externalLevelOne,
    ColorConfig.themeCorlor,
    ColorConfig.externalLevelNormal
  ];
  OrgModel model = OrgModel('');
  FocusNode node = FocusNode();

  List allFriends = [];
  bool isSearch = false;

  RxList selectList = [].obs;
  String myUserId = '';
  bool chooseSelf = true; //是否可以选择自己 type1时用到
  @override
  void onInit() async {
    super.onInit();

    type = Get.arguments['type'];
    if (type == 3 || type == 1 || type == 6) {
      model = Get.arguments['model'];
    }
    if (Get.arguments['chooseType'] != null) {
      chooseType = Get.arguments['chooseType'];
    }
    if (Get.arguments['selectList'] != null) {
      selectList = Get.arguments['selectList'];
    }
    if (Get.arguments['chooseSelf'] != null) {
      chooseSelf = Get.arguments['chooseSelf'];
    }

    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    myUserId = tokenInfo['userId'];
    node.requestFocus();
  }

  @override
  void onReady() {
    super.onReady();
    if (type == 4 || type == 5) {
      getFriendData();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  //搜索组织
  searchOrg(String keyword) async {
    DioUtil()
        .get('${ORGApi.SEARCHCOMPANY}?keyword=$keyword', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        List list = data['data'];
        dataList.value = list.map((item) => OrgModel.fromJson(item)).toList();
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //搜索公司成员
  searchOrgMembers(String keyword) async {
    if (keyword.isEmpty) return;
    DioUtil()
        .get(
            '${ORGApi.ORG_SEARCH_USER}/$keyword/page/1/size/9999?orgId=${model.companyId}',
            null,
            true,
            () {})
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        List list = data['data'];
        if (!chooseSelf) {
          list = list.where((map) {
            return map['userId'] != myUserId;
          }).toList();
        }
        dataList.value =
            list.map((item) => SearchMemberModel.fromJson(item)).toList();
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取外部联系人列表
  getExternalList({bool isShow = true}) async {
    DioUtil()
        .get(
            '${ORGApi.EXTERNAL_CONTACT_LIST}/${model.companyId}?keyword=${searchController.text}&level=0&type=0',
            null,
            true,
            () {},
            isShowLoading: isShow)
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        dataList.value =
            data['data'].map((item) => ExternalModel.fromJson(item)).toList();
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //搜索手机号
  searchTel() async {
    DioUtil()
        .get('${LoginApi.PHONE_SEARCH_USER}/${searchController.text}', null,
            true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        UserModel userModel = UserModel.fromJson(data['data']);
        if (userModel.userId == myUserId) {
          userModel.friendState = 1;
        }
        dataList.value = [userModel];
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  voliteIsAllow(UserModel model) async {
    //是否允许添加好友

    Map param = {'type': 1, 'userId': model.userId};
    DioUtil().post(LoginApi.ISALLOWFRIEND, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Get.toNamed(Routes.ORG_VERIFY,
            arguments: {'userId': model.userId, 'type': 1});
      } else {
        if (data['code'] == 1405) {
          toast('添加好友成功');
        } else {
          toast('${data['msg']}');
        }
      }
    });
  }

  //获取好友
  getFriendData() async {
    var result = await UserDefault.getData(Define.FRIENDLIST);
    if(result != null && result is List){
      List friendList = result;
      if (friendList.isNotEmpty) {
        for (var j = 0; j < friendList.length; j++) {
          UserModel model = UserModel.fromJson(friendList[j]);
          allFriends.add(model);
        }
      }
    }
  }

  //搜索好友
  serachFriends() {
    dataList.clear();
    for (var j = 0; j < allFriends.length; j++) {
      UserModel model = allFriends[j];
      if (model.name.contains(searchController.text) ||
          model.remark.contains(searchController.text)) {
        dataList.add(model);
      }
    }
    update();
  }
}
