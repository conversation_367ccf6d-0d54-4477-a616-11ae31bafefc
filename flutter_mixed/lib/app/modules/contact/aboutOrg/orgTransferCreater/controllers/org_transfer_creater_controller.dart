import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/dialog/slider-dialog.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../model/org/org_model.dart';

class OrgTransferCreaterController extends GetxController {

  late MemberModel memberModel;
  late OrgModel model;

  TextEditingController codeController = TextEditingController();

  Timer? codeTimer;
  RxInt timeInt = 0.obs;

  String phone = '';
  RxString phoneHide = ''.obs;
  bool isRequestImageCode = false;
  @override
  void onInit() async {
    super.onInit();
    model = Get.arguments['model'];
    memberModel = Get.arguments['memberModel'];
    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    phone = userInfo['mobile'];
    phoneHide.value = phone.replaceRange(3, 7, '****');
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    if (codeTimer != null) {
      codeTimer!.cancel();
    }
    super.onClose();
  }

  //发送验证码
  getCode() async {
    DioUtil().get('${LoginApi.GETSMSCODE}$phone/12', null, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('发送成功');
        timerStart();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getImageCode() async {
    DioUtil().get(LoginApi.GET_IMAGE_CODE + phone, null, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        //showSliderVerify.value = true;
        isRequestImageCode = true;
        String backStr = data['data']['backUrl'];
        String topStr = data['data']['topUrl'];
        SliderDialog(phone, 12, backStr, topStr, false).show();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  codeButtonClick() async {
    if (isRequestImageCode) {
      getCode();
      return;
    }
    DioUtil().get('${LoginApi.CHECK_PHONE_CODE}$phone/type/12', null, false,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        getImageCode();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  timerStart() {
    timeInt.value = 60;
    codeTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (timer.tick == 60) {
        timeInt.value = 0;
        timer.cancel();
      } else {
        timeInt.value--;
      }
    });
  }

  sureTransfer() {
    Map param = {
      'companyId': model.companyId,
      'userId': memberModel.userId,
      'phone': phone,
      'code': codeController.text
    };
    DioUtil().put(ORGApi.CHANGEORGCREATER, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        HomeController homeController = Get.find();
        homeController.getAllCompanies();
        Get.until((route){
          if(route.settings.name == '/home'){
            return true;
          }
          return false;
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
