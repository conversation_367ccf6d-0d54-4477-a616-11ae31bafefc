import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/org_transfer_creater_controller.dart';

class OrgTransferCreaterView extends GetView<OrgTransferCreaterController> {
  const OrgTransferCreaterView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.whiteColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(context, '选择公司成员', false, [],
              onPressed: () {
            Get.back();
          }),
          body: <PERSON>View(
            children: [
              SizedBox(
                height: 15,
              ),
              Container(
                alignment: Alignment.center,
                width: double.infinity,
                padding: EdgeInsets.only(left: 15, right: 15),
                child: Text(
                  '你确认要转移该企业的创建者身份给 ${controller.memberModel.name} 吗?',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              ),
              SizedBox(
                height: 30,
              ),
              Container(
                alignment: Alignment.center,
                width: double.infinity,
                height: 72,
                child: Container(
                  width: 72,
                  height: 72,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: SettingWidget.backImageProvider(controller.memberModel.headimg)),
                      borderRadius: BorderRadius.circular(36)),
                ),
              ),
              SizedBox(
                height: 30,
              ),
              Container(
                alignment: Alignment.center,
                width: double.infinity,
                padding: EdgeInsets.only(left: 15, right: 15),
                child: Text(
                  controller.model.name,
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Container(
                alignment: Alignment.centerLeft,
                width: double.infinity,
                padding: EdgeInsets.only(left: 15, right: 15),
                child: Text(
                  '请验证企业创建者手机号：${controller.phoneHide.value}',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Container(
                width: double.infinity,
                height: 50,
                margin: EdgeInsets.only(left: 15,right: 15),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(width: 1, color: ColorConfig.lineColor)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                        child: TextField(
                      onTap: () {},
                      onChanged: (value) {},
                      textInputAction: TextInputAction.done,
                      controller: controller.codeController,
                      //focusNode: controller.node,
                      style: const TextStyle(
                        color: ColorConfig.mainTextColor,
                        fontSize: 17,
                      ),
                      decoration: InputDecoration(
                          contentPadding:
                              const EdgeInsets.only(top: 0, bottom: 0),
                          border: const OutlineInputBorder(
                              borderSide: BorderSide.none),
                          hintText: '请输入验证码',
                          hintStyle: const TextStyle(
                            color: ColorConfig.desTextColor,
                            fontSize: 17,
                          )),
                    )),
                    InkWell(
                      child: Container(
                        width: 115,
                        height: 40,
                        alignment: Alignment.centerRight,
                        padding: EdgeInsets.only(right: 15),
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            color: ColorConfig.whiteColor,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(3)),
                            pressedOpacity: 0.5,
                            child: Text(
                              controller.timeInt.value == 0
                                  ? '获取验证码'
                                  : '${controller.timeInt.value}s',
                              style: TextStyle(
                                  color: controller.timeInt.value == 0
                                      ? ColorConfig.themeCorlor
                                      : ColorConfig.desTextColor,
                                  fontSize: 14),
                            ),
                            onPressed: () {
                              if (controller.timeInt.value == 0) {
                                controller.codeButtonClick();
                              }
                            }),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 50,
              ),
              Container(
                  width: double.infinity,
                  height: 48,
                  padding: EdgeInsets.only(left: 15, right: 15),
                  child: CupertinoButton(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                      color: ColorConfig.themeCorlor,
                      borderRadius: const BorderRadius.all(Radius.circular(7)),
                      pressedOpacity: 0.5,
                      child: const Text(
                        '确认转移',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      onPressed: () {
                        controller.sureTransfer();
                      }),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(left: 15,right: 15),
                  child: const Text('1.完成转移之后，你将失去在本企业内的企业创建者身份(你的部门负责人或部门副职负责人职位将得以保留)\n2.你所使用的其他担当办公系列产品(如担当企业云盘、担当云资产)中，本企业的创建者身份也将同时转移给该用户\n3.转移操作一旦完成不可撤销，请谨慎操作',style: TextStyle(fontSize: 12,color: ColorConfig.desTextColor),),
                )
            ],
          ),
        ));
  }
}
