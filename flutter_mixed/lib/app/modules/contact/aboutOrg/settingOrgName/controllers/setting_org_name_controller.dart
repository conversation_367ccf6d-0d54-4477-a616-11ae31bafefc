import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';

class SettingOrgNameController extends GetxController {

  RxInt type = 0.obs; //0:设置企业名称1:个人资料-姓名2:个人资料-邮箱

  List titleList = ['企业名称', '姓名','邮箱'];
  List<String> hitList = [
    '请输入企业名称',
    '请输入姓名',
    '请输入邮箱',
  ];
  List msgList = [
    '请不要轻易修改企业名称，以免对你的企业工作带来不便',
    '1-10个字符。建议你使用真实姓名，方便你在企业中的工作',
    '电子邮件地址的标准格式：用户名@服务器域名。'
  ];
  TextEditingController editingController = TextEditingController();
  String nameStr = '';
  @override
  void onInit() {
    super.onInit();
    type.value = Get.arguments['type'];
    if (type.value == 0) {
      nameStr = Get.arguments['name'];
      editingController.text = nameStr;
      type.refresh();
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

//设置企业名称
  settingOrgName() async {
    if (editingController.text.isEmpty) {
      toast(hitList[type.value]);
      return;
    }
    if (type.value == 1) {
      if (editingController.text.length > 30) {
        toast('请将企业名称控制在1-30个字符内');
        return;
      }
    }
    if (type.value == 2) {
      if (editingController.text.length > 10) {
        toast('请将姓名控制在1-10个字符内');
        return;
      }
    }

    if (!StringUtil.numberIsOk(
            editingController.text, StringUtil.NAME_DIGIT_REGEX) &&
        type.value == 1) {
      toast('企业名称不能含有特殊字符');
      return;
    }
    if (!StringUtil.numberIsOk(
            editingController.text, StringUtil.NAME_DIGIT_REGEX) &&
        type.value == 2) {
      toast('姓名不能含有特殊字符');
      return;
    }

    Get.back(result: {'name': editingController.text});
  }
}
