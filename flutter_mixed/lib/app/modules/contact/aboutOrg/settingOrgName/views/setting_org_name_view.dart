import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/setting_org_name_controller.dart';

class SettingOrgNameView extends GetView<SettingOrgNameController> {
  const SettingOrgNameView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        backgroundColor: ColorConfig.backgroundColor,
        extendBodyBehindAppBar: true,
        appBar: TitleBar().backAppbar(
            context, controller.titleList[controller.type.value], false, [
          Container(
            width: 60,
            child: CupertinoButton(
                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                pressedOpacity: 0.5,
                child: const Text(
                  '保存',
                  style:
                      TextStyle(color: ColorConfig.themeCorlor, fontSize: 14),
                ),
                onPressed: () {
                  controller.settingOrgName();
                }),
          )
        ], onPressed: () {
          Get.back();
        }),
        body: ListView(
          children: [
            SizedBox(
              height: 10,
            ),
            Offstage(
              offstage: controller.type.value!=2,
              child: Container(
                padding: EdgeInsets.fromLTRB(15, 8, 15, 8),
                child: Text('请设置可用的邮箱，以方便你方便接收各类工作邮件',style: TextStyle(fontSize: 13,color: ColorConfig.desTextColor),),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft ,
              height: 44,
              width: double.infinity,
              color: Colors.white,
              padding: EdgeInsets.only(left: 15, right: 15),
              child: TextField(
                  onSubmitted: (value) {},
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(controller.type.value==1?10:controller.type.value==2?100:30)
                  ],
                  onChanged: (value) {},
                  textInputAction: TextInputAction.done,
                  controller: controller.editingController,
                  style: const TextStyle(
                    color: ColorConfig.mainTextColor,
                    fontSize: 15,
                  ),
                  decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                      border: OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: controller.hitList[controller.type.value],
                      hintStyle: TextStyle(
                        color: ColorConfig.desTextColor,
                        fontSize: 15,
                      ))),
            ),
            SizedBox(
              height: 3,
            ),
            Container(
              alignment: Alignment.topLeft,
              height: 500,
              padding: EdgeInsets.only(left: 15, right: 15),
              child: Text(
                controller.msgList[controller.type.value],
                textAlign: TextAlign.left,
                style: TextStyle(color: ColorConfig.desTextColor, fontSize: 13),
              ),
            )
          ],
        )));
  }
}
