import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../model/org/org_model.dart';

class OrgVerifyController extends GetxController {
  TextEditingController msgController = TextEditingController(text: '');
  OrgModel? model;
  RxString name = ''.obs;
  RxInt type = 0.obs;
  String userId = '';
  @override
  void onInit() async {
    super.onInit();
    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    name.value = userInfo['name'];
    type.value = Get.arguments['type'] ?? 1;
    if (type.value == 0) {
      model = Get.arguments['model'];

      msgController.text = '你好，我是${name.value}，申请加入你的企业';
    } else {
      userId = Get.arguments['userId'];
      msgController.text = '你好，我是${name.value}，申请加你为好友';
    }
  }

  String getTitle() {
    if (type.value == 0) {
      return '你需要发送申请理由，等待企业管理员通过';
    } else {
      return '你需要发送申请理由，等待通过';
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  applyJoinOrg(content) async {
    Map param = {'orgId': model!.organizationId, 'content': content};
    DioUtil().put(ORGApi.JOINORG, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        if (data['data'] == 0) {
          toast('你今天申请加入该企业次数过多，请明天再试');
        } else {
          toast('已向该企业发送加入申请');
          Get.back();
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  addFriend() async {
    Map param = {'message': msgController.text, 'targetUserId': userId};
    DioUtil().post(LoginApi.IM_FRIEND_APPLY, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Get.back();
        if (data['data'] == '添加好友成功') {
          toast('添加好友成功');
        } else {
          toast('已成功发送好友申请');
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
