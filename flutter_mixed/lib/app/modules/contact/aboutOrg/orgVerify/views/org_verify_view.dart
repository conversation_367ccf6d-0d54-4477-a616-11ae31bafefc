import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

import 'package:get/get.dart';

import '../controllers/org_verify_controller.dart';
import '../../../../../common/widgets/widgets.dart';

class OrgVerifyView extends GetView<OrgVerifyController> {
  const OrgVerifyView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      extendBodyBehindAppBar: true,
      appBar: TitleBar().backAppbar(context, controller.type.value == 1? '好友请求' : '验证申请', false, [
        Container(
          width: 72,
          padding: EdgeInsets.fromLTRB(16, 7, 16, 7),
          child: CupertinoButton(
              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
              pressedOpacity: 0.5,
              borderRadius: BorderRadius.circular(6),
              color: ColorConfig.themeCorlor,
              child: const Text(
                '发送',
                style: TextStyle(color: ColorConfig.whiteColor, fontSize: 14),
              ),
              onPressed: () {
                if (controller.type.value == 0) {
                  controller.applyJoinOrg(controller.msgController.text);
                } else {
                  controller.addFriend();
                }
              }),
        )
      ], onPressed: () {
        Get.back();
      }),
      body: ListView(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            color: ColorConfig.backgroundColor,
            height: 20,
            padding: const EdgeInsets.only(left: 15, right: 15),
            child: Obx(() => Text(
              controller.getTitle(),
              textAlign: TextAlign.left,
              style: const TextStyle(
                color: ColorConfig.desTextColor,
                fontSize: 12,
              ),
            )),
          ),
          Container(
            height: 100,
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(15, 20, 15, 0),
            margin: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: ColorConfig.whiteColor
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 80,
                  padding: const EdgeInsets.all(0),
                  alignment: Alignment.topLeft,
                  child: TextField(
                    onSubmitted: (value) {},
                    onChanged: (value) {},
                    controller: controller.msgController,
                    maxLines: null,
                    minLines: 1,
                    maxLength: 30,
                    textInputAction: TextInputAction.done,
                    style: const TextStyle(
                      color: ColorConfig.mainTextColor,
                      fontSize: 14,
                    ),
                    decoration: const InputDecoration(
                      contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                      border: OutlineInputBorder(borderSide: BorderSide.none),
                    ),
                  ),
                ),
                // Container(
                //   height: 20,
                //   alignment: Alignment.centerRight,
                //   child: Text(
                //     '1/30',
                //     textAlign: TextAlign.right,
                //     style: TextStyle(
                //       color: ColorConfig.desTextColor,
                //       fontSize: 12,
                //     ),
                //   ),
                // )
              ],
            ),
          )
        ],
      ),
    );
  }
}
