import 'dart:async';
import 'dart:io';

import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../routes/app_pages.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';

class OrgDetailController extends GetxController {

  OrgModel? model;
  RxString nameString = ''.obs;
  String companyId = '';
  RxBool isNative = false.obs; //是否是原生跳转
  StreamSubscription? subscription;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments['model'] != null) {
        model = Get.arguments['model'];
      }
    }

    subscription = eventBus.on<Map>().listen((event) async {
      if (event == null) return;
      var route = event['route'];
      if (route == Routes.ORG_DETAIL) {
        var arguments = event['arguments'];
        companyId = arguments['companyId'];
        model = OrgModel(companyId);
        isNative.value = true;
        getOrgDetail();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();

    getOrgDetail();
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

  getOrgDetail() async {
    if (model == null) {
      return;
    }
    DioUtil().get('${ORGApi.GETCOMPANYINFO}/${model!.companyId}', null, true,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        model = OrgModel.fromJson(data['data']);
        nameString.value = model!.name;
        nameString.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
