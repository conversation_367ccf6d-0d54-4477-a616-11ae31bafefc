import 'dart:developer';
import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import '../../../../../../plugin/focus_detector/focus_detector.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../common/widgets/widgets.dart';

import 'package:get/get.dart';

import '../controllers/org_detail_controller.dart';
import '../../../../../common/config/config.dart';

class OrgDetailView extends GetView<OrgDetailController> {
  const OrgDetailView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return FocusDetector(
        onFocusGained: () {
          if (controller.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 1});
          }
        },
        onFocusLost: () {
          if (controller.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 0});
          }
        },
        child: Obx(() => Scaffold(
            backgroundColor: ColorConfig.backgroundColor,
            extendBodyBehindAppBar: true,
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(44),
                child: AppBar(
                  title: const Text(
                    '企业简介',
                    style: TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  centerTitle: true,
                  leading: IconButton(
                    onPressed: () {
                      if (controller.isNative.value) {
                        controller.onClose();
                        Channel().invoke(Channel_Native_Back, {});
                      } else {
                        Get.back();
                      }
                    },
                    icon: SizedBox(
                      width: 20,
                      height: 20,
                      child:
                          Image.asset('assets/images/3.0x/org_white_back.png'),
                    ),
                  ),
                  backgroundColor: const Color(0x00FFFFFF),
                  elevation: 0,
                )),
            resizeToAvoidBottomInset: false,
            body: Container(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      Container(
                        width: double.infinity,
                        height: 268, //DeviceUtils().width.value* 268/375,
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                              fit: BoxFit.fill,
                              image: AssetImage(
                                  'assets/images/3.0x/org_detail_back.png')),
                        ),
                        child: Stack(
                          children: [
                            Positioned(
                                bottom: 16,
                                height: 122,
                                left: 15,
                                right: 15,
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 72,
                                      height: 72,
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              width: 1, color: Colors.white),
                                          borderRadius:
                                              BorderRadius.circular(36),
                                          image: DecorationImage(
                                              image:
                                                  SettingWidget.backImageProvider(controller.model?.logo))),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      height: 32,
                                      child: Text(
                                        controller.nameString.value,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                            fontSize: 18, color: Colors.white),
                                      ),
                                    )
                                  ],
                                ))
                          ],
                        ),
                      ),
                      SettingWidget().backSettingWidget(
                          '',
                          '',
                          '企业所属行业: ${controller.model!.industry ?? ''}',
                          '',
                          false,
                          45),
                      SettingWidget().backSettingWidget(
                          '',
                          '',
                          '企业类型: ${controller.model!.type ?? ''}',
                          '',
                          false,
                          45),
                      SettingWidget().backSettingWidget(
                          '',
                          '',
                          '企业人数: ${controller.model!.scale ?? ''}',
                          '',
                          false,
                          45),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(bottom: DeviceUtils().bottom.value + 10),
                    width: double.infinity,
                    padding: const EdgeInsets.only(left: 25, right: 25),
                    height: 44,
                    child: CupertinoButton(
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                        color: controller.model!.isJoin == 1 ||
                                controller.model!.rejectJoin == 1
                            ? ColorConfig.btnGrayColor
                            : ColorConfig.themeCorlor,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)),
                        pressedOpacity: 0.5,
                        child: Text(
                          controller.model!.isJoin == 1
                              ? '你已加入该企业'
                              : controller.model!.rejectJoin == 1
                                  ? '该企业拒绝申请加入'
                                  : '申请加入企业',
                          style: const TextStyle(
                              color: Colors.white, fontSize: 14),
                        ),
                        onPressed: () {
                          if (controller.model!.isJoin == 0 &&
                              controller.model!.rejectJoin == 0) {
                            Get.toNamed(Routes.ORG_VERIFY, arguments: {
                              'model': controller.model,
                              'type': 0
                            });
                          }
                        }),
                  )
                ],
              ),
            ))));
  }
}
