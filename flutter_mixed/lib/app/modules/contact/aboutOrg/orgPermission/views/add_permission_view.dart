import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/orgPermission/controllers/add_permission_controller.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/orgPermission/views/permission_item.dart';
import 'package:get/get.dart';

import '../../../../../../res/assets_res.dart';

class AddPermissionView extends GetView<AddPermissionController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddPermissionController>(builder: (controller) {
      return ToolBar(
        title: controller.title,
        actions: [
          TextButton(
              onPressed: () {
                controller.didClickSaveBtn();
              },
              child: const Text(
                "保存",
                style: TextStyle(color: ColorConfig.themeCorlor, fontSize: 15),
              ))
        ],
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                children: [
                  10.gap,
                  _backNameWidget(controller),
                  10.gap,
                  _backChooseUserWidget(controller),
                  _backSettingAuthText(),
                  _backSettingAllSwitch(controller),
                  1.gap,
                  _backSwitchsWidget(controller),
                  30.gap,
                  if (!controller.isManager &&
                      controller.permissionItem != null) ...[
                    _backDeleteAuthWidget(controller, context),
                  ],
                  20.gap
                ],
              ),
            )
          ],
        ),
      );
    });
  }

  //管理员名称
  _backNameWidget(AddPermissionController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      height: 50,
      color: ColorConfig.whiteColor,
      child: Row(
        children: [
          Container(
            child: const Text(
              '管理员名称',
              style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
          Expanded(
            child: _backManagerNameWidget(controller),
          ),
        ],
      ),
    );
  }

  _backManagerNameWidget(AddPermissionController controller) {
    if (controller.isManager) {
      return _backManageWidget(controller);
    } else {
      return _backInputTextField(controller);
    }
  }

  _backManageWidget(AddPermissionController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Image.asset(
          AssetsRes.ICON_SUPER_PERMISSION_TAG,
          width: 15,
        ),
        3.gap,
        Container(
          child: const Text(
            '超级管理员',
            style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
          ),
        )
      ],
    );
  }

  _backInputTextField(AddPermissionController controller) {
    return TextField(
      textAlign: TextAlign.right,
      controller: controller.inputController,
      style: const TextStyle(color: ColorConfig.mainTextColor, fontSize: 14),
      inputFormatters: <TextInputFormatter>[
        LengthLimitingTextInputFormatter(10)
      ],
      decoration: const InputDecoration(
          border: InputBorder.none,
          hintText: "请输入管理员名称",
          hintStyle: TextStyle(color: ColorConfig.desTextColor, fontSize: 14)),
    );
  }

  //选择人员控件
  _backChooseUserWidget(AddPermissionController controller) {
    return GestureDetector(
      onTap: () {
        //跳转选择人员页面
        controller.didChooseOrgMember();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        height: 50,
        color: ColorConfig.whiteColor,
        child: Row(
          children: [
            Container(
              child: const Text(
                '选择人员',
                style:
                    TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
              ),
            ),
            5.gap,
            Expanded(
              child: Container(
                alignment: Alignment.centerRight,
                child: Text(
                  controller.requestResp?.userIds?.isNotEmpty == true
                      ? '已选择${controller.requestResp!.userIds!.length}人'
                      : '请选择人员',
                  style: TextStyle(
                      fontSize: 14,
                      color: controller.requestResp?.userIds?.isNotEmpty == true
                          ? ColorConfig.mainTextColor
                          : ColorConfig.desTextColor),
                ),
              ),
            ),
            5.gap,
            SizedBox(
              width: 9,
              height: 17,
              child: Image.asset('assets/images/3.0x/mine_right.png'),
            )
          ],
        ),
      ),
    );
  }

  //分配权限文字
  _backSettingAuthText() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      height: 25,
      child: const Text(
        '可分配权限',
        style: TextStyle(fontSize: 12, color: ColorConfig.msgTextColor),
      ),
    );
  }

  //全部权限开关
  _backSettingAllSwitch(AddPermissionController controller) {
    return Column(
      children: [
        if (controller.isManager) ...[
          _backAllSwitchWidget(controller, '普通管理员设置权限', true),
          1.gap,
          _backAllSwitchWidget(controller, '团队偏好设置权限', true),
          1.gap
        ],
        _backAllSwitchWidget(controller, '全部权限', controller.allSwitchIsOpen,
            isAllSwitch: true)
      ],
    );
  }

  _backAllSwitchWidget(
      AddPermissionController controller, String msgStr, bool isOpen,
      {bool isAllSwitch = false}) {
    return Container(
      padding: const EdgeInsets.only(left: 15, right: 12),
      height: 50,
      color: ColorConfig.whiteColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            child: Text(
              msgStr,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
          CupertinoSwitch(
              activeTrackColor: ColorConfig.themeCorlor,
              inactiveTrackColor: ColorConfig.lineColor,
              value: isOpen,
              onChanged: (value) {
                controller.didClickAllSwitch(value, isAllSwitch: isAllSwitch);
              })
        ],
      ),
    );
  }

  _backSwitchsWidget(AddPermissionController controller) {
    return Column(
      children: _backSwitchList(controller),
    );
  }

  List<Widget> _backSwitchList(AddPermissionController controller) {
    List<Widget> lists = [];
    for (var i = 0; i < PermissionData.dataList.length; i++) {
      Map dataMap = PermissionData.dataList[i];
      lists.add(_backSingleSwitch(controller, dataMap));
    }
    return lists;
  }

  //单个开关
  _backSingleSwitch(AddPermissionController controller, Map dataMap) {
    String imageName = dataMap['imageName'];
    String topStr = dataMap['topStr'];
    String bottomStr = dataMap['bottomStr'];

    String power = dataMap['power'];
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.fromLTRB(15, 6, 12, 6),
          color: ColorConfig.whiteColor,
          child: Row(
            children: [
              Image.asset(
                imageName,
                width: 32,
              ),
              5.gap,
              Expanded(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      topStr,
                      style: const TextStyle(
                          fontSize: 15, color: ColorConfig.mainTextColor),
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      bottomStr,
                      style: const TextStyle(
                          fontSize: 12, color: ColorConfig.mainTextColor),
                    ),
                  )
                ],
              )),
              CupertinoSwitch(
                  activeTrackColor: ColorConfig.themeCorlor,
                  inactiveTrackColor: ColorConfig.lineColor,
                  value: controller.isManager
                      ? true
                      : controller.powerList.contains(power),
                  onChanged: (value) {
                    controller.settingPower(power, value);
                  })
            ],
          ),
        ),
        const Divider(
          color: ColorConfig.backgroundColor,
          height: 1,
        )
      ],
    );
  }

  //删除管理员控件
  _backDeleteAuthWidget(AddPermissionController controller, context) {
    return InkWell(
      onTap: () {
        controller.didClickDeleteBtn(context);
      },
      child: Container(
        height: 40,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 50),
        child: const Text(
          '删除此类管理员',
          style: TextStyle(fontSize: 14, color: ColorConfig.deleteCorlor),
        ),
      ),
    );
  }
}
