
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/orgPermission/controllers/org_permission_controller.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/orgPermission/views/permission_item.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/org_permission_resp.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/im/widget/page_status.dart';
import 'package:get/get.dart';

import '../../../../../../res/assets_res.dart';

class OrgPermissionView extends GetView<OrgPermissionController> with PageLoadWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrgPermissionController>(builder: (controller){
      return ToolBar(
        title: "权限设置",
        actions: [
          IconButton(
              onPressed: () async{
               var result = await RouteHelper.routePath(Routes.ADD_PERMISSION,arguments: {'orgId':controller.orgId});
                if (result != null) {
                controller.getPermissionList();
              }
              },
              icon: SizedBox(
                width: 20,
                height: 20,
                child: Image.asset(AssetsRes.IC_IM_MORE)
              )
          )
        ],
        body: controller.permissionList.isEmpty?
          _buildEmptyWidget() : _buildContent(controller)
      );

    });
  }

  _buildEmptyWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset( AssetsRes.IC_EMPTY_FRIEND),
          const Text(
              "暂无添加的管理员",
              style: TextStyle(color: Color(0xff333333),fontSize: 16)
          ),
          const Text(
              "点击右上角的加号，可以添加管理员",
              style: TextStyle(color: Color(0xff333333),fontSize: 16)
          )
        ],
      ),
    );
  }

  _buildContent(OrgPermissionController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ListView.builder(
        itemCount: controller.permissionList.length,
        itemBuilder: (context,index){
          return PermissionItem(permissionItem: controller.permissionList[index],isCreator: controller.deptId == "0", onItemClick: ()async{
            var result = await RouteHelper.routePath(
              Routes.ADD_PERMISSION,
              arguments: {'orgId':controller.orgId,'permissionItem':controller.permissionList[index]}
            );
            if (result != null) {
              controller.getPermissionList();
            }
          });
        },
      ),
    );
  }
}