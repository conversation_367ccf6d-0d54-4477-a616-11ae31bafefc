import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/org_permission_resp.dart';

import '../../../../../../res/assets_res.dart';

class PermissionItem extends StatefulWidget {
  const PermissionItem(
      {super.key,
      required this.permissionItem,
      required this.isCreator,
      required this.onItemClick});

  final bool isCreator;
  final OrgPermissionResp permissionItem;
  final VoidCallback onItemClick;

  @override
  State<StatefulWidget> createState() {
    return _PermissionItemState();
  }
}

class _PermissionItemState extends State<PermissionItem> {
  var isManager = false;
  var isShowMoreLines = false;
  @override
  void initState() {
    if (widget.permissionItem.managerPower == null) return;
    isManager = widget.permissionItem.managerPower!.isNotEmpty &&
        widget.permissionItem.managerPower!
            .contains(PermissionType.SUPER_PERMISSION.status);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (isManager && !widget.isCreator) {
          //非创建者不能编辑超级管理员
          return;
        }
        widget.onItemClick();
      },
      child: Container(
        color: ColorConfig.backgroundColor,
        padding: const EdgeInsets.only(top: 9),
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(bottom: 15),
          child: Stack(
            alignment: Alignment.centerRight,
            children: [
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                        left: 13, top: 20, right: 10, bottom: 17),
                    child: Row(
                      children: [
                        Visibility(
                          visible: isManager,
                          child: Row(
                            children: [
                              Image.asset(
                                AssetsRes.ICON_SUPER_PERMISSION_TAG,
                                width: 15,
                              ),
                              6.gap
                            ],
                          ),
                        ),
                        Text(
                          widget.permissionItem.managerName ?? "",
                          style: const TextStyle(
                              color: ColorConfig.mainTextColor, fontSize: 17),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    child: Row(
                      children: _buildPermissionIcon(),
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            Container(
                              margin: const EdgeInsets.only(left: 13),
                              width: double.infinity,
                              child: widget
                                          .permissionItem.userIds?.isNotEmpty ==
                                      true
                                  ? Text(
                                      textAlign: TextAlign.left,
                                      maxLines: isShowMoreLines ? null : 1,
                                      widget.permissionItem.userNames ?? "未选择",
                                      style: const TextStyle(
                                          color: ColorConfig.mainTextColor,
                                          fontSize: 13),
                                    )
                                  : const Text(
                                      "未选择",
                                      style: TextStyle(
                                          color: ColorConfig.desTextColor,
                                          fontSize: 13),
                                    ),
                            ),
                            const SizedBox(
                              height: 8,
                            ),
                            Container(
                              margin: const EdgeInsets.only(left: 13),
                              width: double.infinity,
                              child: Visibility(
                                  visible: widget.permissionItem.managerPower
                                          ?.isNotEmpty ==
                                      false,
                                  child: const Text("暂未分配权限",
                                      style: TextStyle(
                                          color: ColorConfig.desTextColor,
                                          fontSize: 13))),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(right: 6),
                        child: Visibility(
                            visible:
                                widget.permissionItem.userIds?.isNotEmpty ==
                                    true,
                            child: IconButton(
                                onPressed: () {
                                  setState(() {
                                    isShowMoreLines = !isShowMoreLines;
                                  });
                                },
                                icon: isShowMoreLines
                                    ? Image.asset(
                                        AssetsRes.ICON_UP_ARROW,
                                        width: 20,
                                      )
                                    : Image.asset(
                                        AssetsRes.ICON_DOWN_ARROW,
                                        width: 20,
                                      ))),
                      )
                    ],
                  )
                ],
              ),
              Positioned(
                  right: 10,
                  child: Visibility(
                      visible: !(isManager && !widget.isCreator),
                      child: Image.asset(
                        AssetsRes.ICON_SET_BLUE,
                        width: 22,
                      )))
            ],
          ),
        ),
      ),
    );
  }

  _buildPermissionIcon() {
    List<Container> images = [];
    if (isManager) {
      images.add(Container(
        padding: const EdgeInsets.only(left: 13, right: 38),
        child: const Text(
          '全部管理权限及普通管理员设置权限等',
          style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
        ),
      ));
    } else {
      for (var char in (widget.permissionItem.managerPower ?? "").split("")) {
        List permissions = PermissionData.dataList.where((map) {
          return map['power'] == char;
        }).toList();
        if (permissions.isNotEmpty) {
          Map dataMap = permissions.first;
          images.add(Container(
            padding: const EdgeInsets.only(left: 13),
            child: Image.asset(
              dataMap['imageName'],
              width: 19,
            ),
          ));
        }
      }
    }

    return images;
  }
}

enum PermissionType {
  SUPER_PERMISSION("-1"), //超级管理权限
  ORG_PERMISSION("1"), //团队管理权限
  ATTENDANCE_PERMISSION("2"), //考勤管理权限
  TASK_PERMISSION("3"), //任务管理权限
  APPROVAL_PERMISSION("4"), //审批管理权限
  REPORT_PERMISSION("5"), //日报管理权限
  DIGITAL_REPORT("6"), //数字报告预览
  TOTAL_APPROVAL("7"), //全部审批权限
  //HEALTH_STATISTICS ("8"), //健康上报统计权限
  CLOUD_MANAGE("9"), //云文档管理权限
  //VISITOR_MANAGE ("a"),  //访客管理权限
  REGIMES_MANAGE("d"); //规章制度管理权限

  final String status;
  const PermissionType(this.status);
}

class PermissionData {
  static List dataList = [
    {
      'imageName': AssetsRes.PER_ORG,
      'topStr': '团队管理权限',
      'bottomStr': '可对团队资料、成员及架构进行调整，可编辑公告',
      'power': PermissionType.ORG_PERMISSION.status
    },
    {
      'imageName': AssetsRes.PER_ATTENDACE,
      'topStr': '考勤管理权限',
      'bottomStr': '可查看全团队考勤数据、调整考勤规则等',
      'power': PermissionType.ATTENDANCE_PERMISSION.status
    },
    {
      'imageName': AssetsRes.ICON_TASK_MANAGE_SMALL,
      'topStr': '项目创建权限',
      'bottomStr': '可在任务功能中创建新项目',
      'power': PermissionType.TASK_PERMISSION.status
    },
    {
      'imageName': AssetsRes.ICON_APPROVAL_PERMISSION,
      'topStr': '审批管理权限',
      'bottomStr': '可快速启/停审批模版，可编辑审批流程',
      'power': PermissionType.APPROVAL_PERMISSION.status
    },
    {
      'imageName': AssetsRes.ICON_REPORT_PERMISSION,
      'topStr': '汇报管理权限',
      'bottomStr': '可设定工作汇报规则，查看工作汇报统计',
      'power': PermissionType.REPORT_PERMISSION.status
    },
    {
      'imageName': AssetsRes.ICON_NUMBER_REPORT_PERMISSION,
      'topStr': '企业数字报告预览权限',
      'bottomStr': '可在管理团队模块查看本团队的企业数字报告',
      'power': PermissionType.DIGITAL_REPORT.status
    },
    {
      'imageName': AssetsRes.ICON_TOTAL_APPROVAL,
      'topStr': '查看全部审批权限',
      'bottomStr': '可查看企业全员发起的审批',
      'power': PermissionType.TOTAL_APPROVAL.status
    },
    {
      'imageName': AssetsRes.ICON_CLOUD_MANAGE,
      'topStr': '云文档管理权限',
      'bottomStr': '可对企业所有部门文件进行查看、删除等操作',
      'power': PermissionType.CLOUD_MANAGE.status
    },
    {
      'imageName': AssetsRes.ICON_REGIMES_PERMISSION,
      'topStr': '规章制度管理权限',
      'bottomStr': '可对企业的规章制度进行增删改查等功能',
      'power': PermissionType.REGIMES_MANAGE.status
    }
  ];
}
