
import 'package:flutter_mixed/app/im/widget/page_status.dart';
import 'package:flutter_mixed/app/retrofit/datasource/org_permission_datasource.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/org_permission_resp.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../../../common/widgets/widgets.dart';

class OrgPermissionController extends GetxController with PageStatus {
  List<OrgPermissionResp> permissionList = [];
  String orgId = '';
  String deptId = '';

  @override
  void onInit() {
    super.onInit();
    orgId = Get.arguments['companyId'];
    deptId = Get.arguments['deptId'];
    getPermissionList();
  }

  getPermissionList() async{
    OrgPermissionDatasource datasource = OrgPermissionDatasource(retrofitDio );
    var resp = await datasource.getOrgPermissions(orgId);
    if(resp.success()){
      permissionList.clear();
      permissionList.addAll(resp.data);
      update();
      _whetherShowEmptyPage();
    }else {
      toast(resp.msg);
    }
  }

  _whetherShowEmptyPage() {
    permissionList.isEmpty ? loadEmpty() : loadSuccess();
  }
}