import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/dialog/msg-dialog.dart';
import 'package:flutter_mixed/app/db/user/member_model_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/retrofit/datasource/org_permission_datasource.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/add_permission_resp.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/base_permission_ext.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/base_permission_resp.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/edit_permission_resp.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/org_permission_resp.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import '../../../../../common/widgets/widgets.dart';
import '../views/permission_item.dart';

class AddPermissionController extends GetxController {
  String orgId = ''; //公司id
  OrgPermissionResp? permissionItem; //权限数据
  String title = '';
  bool isManager = false; //是否是编辑超级管理员权限
  TextEditingController? inputController;

  bool allSwitchIsOpen = false; //全部开关状态
  List powerList = [];
  String allPower = ''; //所有权限字符串
  List dataList = []; //可设置权限数据
  BasePermissionResp? requestResp;
  @override
  void onInit() {
    super.onInit();
    inputController = TextEditingController();
    permissionItem = Get.arguments['permissionItem'];
    orgId = Get.arguments['orgId'];
    if (permissionItem == null) {
      title = "新增管理员";
    } else {
      title = "编辑管理员";
      isManager = permissionItem?.managerPower?.isNotEmpty == true &&
          permissionItem?.managerPower
                  ?.contains(PermissionType.SUPER_PERMISSION.status) ==
              true;
    }
    _initData();
  }

  _initData() {
    _getAllPower();
    if (permissionItem == null) {
      inputController?.text = '';
      requestResp = BasePermissionResp()
        ..managerName = ''
        ..managerPower = ''
        ..userIds = [];
    } else {
      inputController?.text = permissionItem?.managerName ?? '';
      requestResp = BasePermissionResp()
        ..managerName = permissionItem?.managerName ?? ''
        ..managerPower = permissionItem?.managerPower ?? ''
        ..userIds = permissionItem?.userIds ?? []
        ..managerId = permissionItem?.managerId;
      _dealPowerList();
      if (isManager) {
        allSwitchIsOpen = true;
      }
    }
  }

  //获取所以权限字符串
  _getAllPower() {
    String powers = '';
    for (var i = 0; i < PermissionData.dataList.length; i++) {
      Map dataMap = PermissionData.dataList[i];
      powers = powers + dataMap['power'];
    }
    allPower = powers;
  }

  _dealPowerList() {
    if (requestResp!.managerPower != null) {
      for (var i = 0; i < requestResp!.managerPower!.characters.length; i++) {
        var char = requestResp!.managerPower!.characters.characterAt(i);
        if (!powerList.contains('$char')) {
          powerList.add('$char');
        }
      }
    }
  }

  //选择人员
  didChooseOrgMember() async {
    List selectList = [];
    if (requestResp?.userIds?.isNotEmpty == true) {
      for (var i = 0; i < requestResp!.userIds!.length; i++) {
        selectList.add(MemberModel(requestResp!.userIds![i]));
      }
    }
    OrgModel model = OrgModel(orgId);
    var result =
        await RouteHelper.routePath(Routes.CHOOSE_ALL_ORG_MEMBERS, arguments: {
      'model': model,
      'type': 1,
      'chooseType': 2,
      'selectList': selectList,
      'chooseSelf': false
    });
    if (result != null) {
      List list = result['list'];
      List<String> userIds = [];
      for (MemberModel element in list) {
        userIds.add(element.userId);
      }
      requestResp?.userIds = userIds;
      update();
    }
  }

  //点击了全部开关
  didClickAllSwitch(value, {bool isAllSwitch = false}) {
    if (isManager) {
      toast('设置超级管理员权限，不能进行修改');
      return;
    }
    if (isAllSwitch) {
      allSwitchIsOpen = value;
      if (value) {
        requestResp?.managerPower = allPower;
        _dealPowerList();
      } else {
        requestResp?.managerPower = '';
        powerList.clear();
      }
      update();
    }
  }

  //权限设置开关
  settingPower(String power, bool value) {
    if (isManager) {
      toast('设置超级管理员权限，不能进行修改');
      return;
    }
    if (value) {
      if (!powerList.contains(power)) {
        powerList.add(power);
      }
    } else {
      allSwitchIsOpen = false;
      if (powerList.contains(power)) {
        powerList.remove(power);
      }
    }
    requestResp?.managerPower = powerList.join('');
    update();
  }

  //点击了保存数据校验
  didClickSaveBtn() {
    if (inputController!.text.isEmpty) {
      toast('请输入管理员名称');
      return;
    }
    if (inputController!.text.characters.length > 10) {
      toast('最多输入10个字符');
      return;
    }
    requestResp?.managerName = inputController?.text;
    if (permissionItem == null) {
      addOrgPermission();
    } else {
      editOrgPermission();
    }
  }

  //点击了删除按钮
  didClickDeleteBtn(context) {
    MsgDiaLog('提示', '是否确认删除此管理员?', '取消', '确认', () {
      Navigator.of(context).pop();
    }, () {
      Navigator.of(context).pop();
      deletePermission();
    }).show();
  }

  //新增管理员请求
  addOrgPermission() async {
    try {
      var permissionDatasource =
          OrgPermissionDatasource(retrofitDio, baseUrl: Host.HOST);
      var resp = await permissionDatasource.addOrgPermissions(
          orgId, requestResp?.getAddPermissionModel());
      if (resp.success()) {
        toast('权限添加成功');
        Get.back(result: 1);
      } else {
        if (resp.msg != null) {
          toast(resp.msg);
        }
      }
    } catch (e) {}
  }

  //编辑管理员请求
  editOrgPermission() async {
    try {
      var permissionDatasource =
          OrgPermissionDatasource(retrofitDio, baseUrl: Host.HOST);
      var resp = await permissionDatasource.editOrgPermissions(
          orgId, requestResp?.getEditPermissionModel());
      if (resp.success()) {
        toast('权限修改成功');
        Get.back(result: 1);
      } else {
        if (resp.msg != null) {
          toast(resp.msg);
        }
      }
    } catch (e) {}
  }

  deletePermission() async {
    try {
      var permissionDatasource =
          OrgPermissionDatasource(retrofitDio, baseUrl: Host.HOST);
      var resp = await permissionDatasource.deleteOrgPermissions(
          orgId, requestResp?.managerId);
      if (resp.success()) {
        toast('权限删除成功');
        Get.back(result: 1);
      } else {
        if (resp.msg != null) {
          toast(resp.msg);
        }
      }
    } catch (e) {}
  }
}
