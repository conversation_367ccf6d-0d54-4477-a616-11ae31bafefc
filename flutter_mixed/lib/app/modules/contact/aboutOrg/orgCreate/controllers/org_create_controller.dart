import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/app/routes/route_util.dart';
import 'package:flutter_mixed/app/utils/cos_helper.dart';
import 'package:flutter_photo_editor/flutter_photo_editor.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/base_info/info.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../../home/<USER>/home_controller.dart';
import '../../../../workStand/models/filemodel.dart';

class OrgCreateController extends GetxController {

  TextEditingController nameController = TextEditingController();
  RxString industyStr = ''.obs;
  RxString scaleStr = ''.obs;

  String uploadBucket = '';
  Completer? completer;

  String preStr = '';
  RxString logoStr = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    // Map cosInfo = await UserDefault.getData(Define.COSTOKENKEY);
    // uploadBucket = cosInfo['userBucket'];
    Map preMap = await UserDefault.getData(Define.COSPREKEY);
    preStr = preMap['pre'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    nameController.dispose();
    super.onClose();
  }

  //拍照
  tackPhotoWithCamera() async {
    var r = await PermissionUtil.checkCameraPermission(Get.context! , tip: cameraPhotoPermissionTip);
    if (!r) return;
    File? file;
    if (Platform.isIOS) {
      //iOS使用插件会出现相机功能一直启用，右上角绿点一直展示的问题
      var result = await Channel()
          .invokeMap(Channel_call_iOS_camera, {'allowsEditing': 1});

      String filePath = result!['filePath'];
      file = File(filePath);
    } else {
      final AssetEntity? entity =
          await CameraPicker.pickFromCamera(Get.context!);

      file = await entity!.file;
    }
    if (file == null) return;
    File tempFile = await CosHelper.move2Img(file);
    await FlutterPhotoEditor().editImage(tempFile.path);
    file = tempFile;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file!.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    await startDealFile(fileModel);
    await startUploadFileList(fileModel);
  }

  //从相册中选取

  tackPhotoFromPic() async {
    var r = await PermissionUtil.checkGalleryPermission(Get.context! , tip: takePhotoPermissionTip);
    if (!r) return;

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: const AssetPickerConfig(
            maxAssets: 1, requestType: RequestType.image));

    if (assets == null) return;
    if (assets.isEmpty) return;

    var entity = assets.first.file;
    File? file = await entity;
    if (file == null) return;
    if (file.path == null) return;

    File tempFile = await CosHelper.move2Img(file);
    var cropResult = await FlutterPhotoEditor().editImage(tempFile.path);

    file = tempFile;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    print('$fileModel');

    await startDealFile(fileModel);

    await startUploadFileList(fileModel);

  }

  Future startDealFile(FileModel fileModel) async {
    completer = Completer<String>();
    await saveFile(fileModel).then((value) {
      fileModel.savePath = value;
      fileModel.hash = BaseInfo().generateMD5(value);
      if (completer!.isCompleted == false) {
        completer!.complete('');
      }
    });
    return completer!.future;
  }

  //保存图片
  Future<String> saveFile(FileModel fileModel) async {
    File file = fileModel.file!;
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;
    String filePath = '$docment/approve/images/$fileName';
    Directory directory = Directory('$docment/approve/images');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  //开始上传
  startUploadFileList(FileModel fileModel) async {
    Get.loading();
    await CosManager().initPans();
    String? bucket = await CosManager().userBucket();
    var backUrl =
          await CosUploadHelper.nativeUpload(fileModel.savePath,'/LOGO/${fileModel.fileName}' , bucket);
    Get.dismiss();
    if (backUrl != null) {
      logoStr.value = backUrl;
    }else{
      //toast('图片上传失败');
    }
  }

  jugeData() {
    if (nameController.text.isEmpty) {
      toast('请输入企业名称');
      return;
    }
    if (industyStr.value.isEmpty) {
      toast('请选择所属行业');
      return;
    }
    if (scaleStr.value.isEmpty) {
      toast('请选择企业规模');
      return;
    }
    createOrg();
  }

  createOrg() async {
    Map dict = {
      'name': nameController.text,
      'industry': industyStr.value,
      'scale': scaleStr.value,
      'source': 1,
      'createArea': '',
      'type': ''
    };
    if (logoStr.value.isNotEmpty) {
      dict['logo'] = logoStr.value;
    }

    DioUtil().post(ORGApi.CREATECOMPANY, dict, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('企业创建成功');
        try {
          HomeController homeController = Get.find();
          homeController.getAllCompanies();
          homeController.getAllGroup();
        } catch (e) {}

        Get.back();
        RouteUtil.androidClosePage();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
