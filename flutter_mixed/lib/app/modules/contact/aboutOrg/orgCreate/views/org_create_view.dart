import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/org_create_controller.dart';
import 'package:extended_image/extended_image.dart';

class OrgCreateView extends GetView<OrgCreateController> {
  const OrgCreateView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: ColorConfig.whiteColor,
          appBar:
              TitleBar().backAppbar(context, '创建企业', false, [], onPressed: () {
            Get.back();
          }),
          body: Stack(
            children: [
              <PERSON>View(
                children: [
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    color: ColorConfig.whiteColor,
                    width: double.infinity,
                    height: 32,
                    padding: EdgeInsets.only(left: 15, right: 15),
                    child: Text(
                      '设置企业信息',
                      style: TextStyle(
                          fontSize: 20, color: ColorConfig.mainTextColor),
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    color: ColorConfig.whiteColor,
                    height: 5,
                  ),
                  Container(
                    color: ColorConfig.whiteColor,
                    width: double.infinity,
                    height: 22,
                    padding: EdgeInsets.only(left: 15, right: 15),
                    child: Text(
                      '注册新企业，并填写企业信息',
                      style: TextStyle(
                          fontSize: 14, color: ColorConfig.desTextColor),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    width: double.infinity,
                    height: 82,
                    alignment: Alignment.center,
                    child: InkWell(
                      onTap: () {
                        SettingWidget().showCupertinoActionSheetForPage(
                            context, ['拍照', '从相册中选取'], (value) {
                          if (value == 0) {
                            controller.tackPhotoWithCamera();
                          }
                          if (value == 1) {
                            controller.tackPhotoFromPic();
                          }
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                            image: DecorationImage(image: SettingWidget.backImageProvider(controller.logoStr.value),fit: BoxFit.contain),
                            color: ColorConfig.backgroundColor,
                            border: Border.all(
                                width: 1, color: ColorConfig.mybackgroundColor),
                            borderRadius: BorderRadius.circular(40)),
                        child: Container(
                          width: 24,
                          height: 24,
                          child: Image.asset(
                              'assets/images/3.0x/contact_org_camera.png'),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    color: ColorConfig.whiteColor,
                    width: DeviceUtils().width.value - 30,
                    height: 55,
                    padding: EdgeInsets.only(left: 15, right: 15),
                    child: Column(
                      children: [
                        Container(
                          height: 54,
                          child: Row(
                            children: [
                              Text(
                                '企业名称',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.mainTextColor),
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Expanded(
                                  child: TextField(
                                onChanged: (value) {},
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(30)
                                ],
                                textAlign: TextAlign.right,
                                textInputAction: TextInputAction.done,
                                controller: controller.nameController,
                                style: const TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14,
                                ),
                                decoration: InputDecoration(
                                    contentPadding: const EdgeInsets.only(
                                        top: 0, bottom: 0),
                                    border: const OutlineInputBorder(
                                        borderSide: BorderSide.none),
                                    hintText: '请输入企业名称',
                                    hintStyle: const TextStyle(
                                      color: ColorConfig.desTextColor,
                                      fontSize: 14,
                                    )),
                              ))
                            ],
                          ),
                        ),
                        Divider(
                          height: 1,
                          color: ColorConfig.backgroundColor,
                        )
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      var result = await Get.toNamed('/setting-org-industry');
                      if (result != null && result['industry'] != null) {
                        controller.industyStr.value = result['industry'];
                      }
                    },
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: DeviceUtils().width.value - 30,
                      height: 55,
                      padding: EdgeInsets.only(left: 15, right: 15),
                      child: Column(
                        children: [
                          Container(
                            width: DeviceUtils().width.value - 30,
                            height: 54,
                            child: Row(
                              children: [
                                Text(
                                  '所属行业',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Expanded(
                                    child: Container(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    controller.industyStr.value == ''
                                        ? '请选择所属行业'
                                        : controller.industyStr.value,
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.desTextColor),
                                  ),
                                )),
                                SizedBox(
                                  width: 10,
                                ),
                                SizedBox(
                                  width: 9,
                                  height: 17,
                                  child: Image.asset(
                                      'assets/images/3.0x/mine_right.png'),
                                )
                              ],
                            ),
                          ),
                          Divider(
                            height: 1,
                            color: ColorConfig.backgroundColor,
                          )
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      var result = await Get.toNamed('/setting-org-scale',
                          arguments: {'type': 0});
                      if (result != null && result['scale'] != null) {
                        controller.scaleStr.value = result['scale'];
                      }
                    },
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: DeviceUtils().width.value - 30,
                      height: 55,
                      padding: EdgeInsets.only(left: 15, right: 15),
                      child: Column(
                        children: [
                          Container(
                            width: DeviceUtils().width.value - 30,
                            height: 54,
                            child: Row(
                              children: [
                                Text(
                                  '人员规模',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Expanded(
                                    child: Container(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    controller.scaleStr.value == ''
                                        ? '请选择人员规模'
                                        : controller.scaleStr.value,
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.desTextColor),
                                  ),
                                )),
                                SizedBox(
                                  width: 10,
                                ),
                                SizedBox(
                                  width: 9,
                                  height: 17,
                                  child: Image.asset(
                                      'assets/images/3.0x/mine_right.png'),
                                )
                              ],
                            ),
                          ),
                          Divider(
                            height: 1,
                            color: ColorConfig.backgroundColor,
                          )
                        ],
                      ),
                    ),
                  )
                ],
              ),
              Positioned(
                  left: 15,
                  right: 15,
                  bottom: DeviceUtils().bottom.value + 16,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(left: 0, right: 0),
                    height: 44,
                    child: CupertinoButton(
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                        color: ColorConfig.themeCorlor,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)),
                        pressedOpacity: 0.5,
                        child: const Text(
                          '创建企业',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        onPressed: () {
                          controller.jugeData();
                        }),
                  ))
            ],
          ),
        ));
  }
}
