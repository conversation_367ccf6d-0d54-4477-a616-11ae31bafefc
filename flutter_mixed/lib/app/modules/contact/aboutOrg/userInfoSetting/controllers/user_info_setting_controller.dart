import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:get/get.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/event/event.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';

class UserInfoSettingController extends GetxController {

  RxInt type = 0.obs;
  late OrgModel model;
  String userId = '';
  bool isRouteBack = false;
  @override
  void onInit() {
    super.onInit();
    type.value = Get.arguments['type'];
    userId = Get.arguments['userId'];
    if (type.value == 3) {
      model = Get.arguments['model'];
    }
    if (Get.arguments['routeback'] != null) {
      isRouteBack = Get.arguments['routeback'];
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  didClickDeleteBtn() {
    if (type.value == 3) {
      leaveCompany();
    } else {
      deleteFriend();
    }
  }

  //删除好友
  deleteFriend() {
    Map param = {
      'userIds': [userId]
    };
    DioUtil().delete(LoginApi.IM_FRIENDS_LIST, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        if (isRouteBack) {
          Get.until((route) => route.settings.name == Routes.MY_FRIEND);
        } else {
          Get.until((route) => route.settings.name == Routes.HOME);
        }
        eventBus.fire({'userInfo':'1'});
        
        Channel().invoke(Channel_Native_RereshFriend, {});

        // 删除 session
        DbHelper.deleteSession(userId);
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //请离企业
  leaveCompany() {
    String deptId = model.deptId;
    if (deptId == '1') {
      deptId = '0';
    }
    Map param = {
      'deptId': deptId,
      'exitType': 1,
      'orgId': model.companyId,
      'userIds': [userId]
    };
    DioUtil().delete(ORGApi.LEAVELCOMPANY, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('已成功请离');
        eventBus.fire(DeptRefresh(model));
        eventBus.fire({'userInfo':'1'});
        Get.back(result: 1);
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
