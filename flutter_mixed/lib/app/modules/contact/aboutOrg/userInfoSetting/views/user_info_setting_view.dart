import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';

import 'package:get/get.dart';

import '../controllers/user_info_setting_controller.dart';

class UserInfoSettingView extends GetView<UserInfoSettingController> {
  const UserInfoSettingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      extendBodyBehindAppBar: true,
      appBar: TitleBar().backAppbar(context, '设置', false, [], onPressed: (){
        Get.back();
      }),
      body: ListView(
        children: [
          SizedBox(height: 10,),
          // Container(
          //   width: double.infinity,
          //   height: 48,
          //   padding: EdgeInsets.only(left: 16,right: 16),
          //   child: InkWell(
          //     onTap: () {
                
          //     },
          //     child: Container(
          //     height: 48,
          //     padding: EdgeInsets.only(left: 16,right: 16),
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(8),
          //       color: ColorConfig.whiteColor
          //     ),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //       children: [
          //         Container(
          //           alignment: Alignment.centerLeft,
          //           child: Text('投诉该用户',style: TextStyle(fontSize: 14,color: ColorConfig.mainTextColor),),
          //         ),
          //         SizedBox(
          //           width: 9,
          //           height: 17,
          //           child: Image.asset('assets/images/3.0x/mine_right.png'),
          //         )
          //       ],
          //     ),
          //   ),
          //   ),
          // ),
          SizedBox(height: 10,),
          Offstage(
            offstage: controller.type.value==3&&controller.model.power!='-1'&&controller.model.power!='1'&&controller.model.deptId!='0',
            child: Container(
            width: double.infinity,
            height: 48,
            padding: EdgeInsets.only(left: 16,right: 16),
            child: InkWell(
              onTap: () {
                controller.didClickDeleteBtn();
              },
              child: Container(
              alignment: Alignment.center,
              height: 48,
              padding: EdgeInsets.only(left: 16,right: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: ColorConfig.whiteColor
              ),
              child: Text(controller.type.value==3?'请离企业':'删除好友',style: TextStyle(fontSize: 14,color: ColorConfig.deleteCorlor),)
            ),
            ),
          ),
          )
        ],
      ),
    ));
  }

  
}
