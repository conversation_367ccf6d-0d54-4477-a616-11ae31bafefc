import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/utils/storage.dart';

import 'package:get/get.dart';

import '../../../../../common/config/string_const.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../helper/wechat.dart';
import '../controllers/invite_home_controller.dart';
import 'package:qr_flutter/qr_flutter.dart';

class InviteHomeView extends GetView<InviteHomeController> {
  const InviteHomeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context, controller.type == 0 ? '添加成员' : '个人二维码', false, [],
              onPressed: () {
            Get.back();
          }),
          body: SingleChildScrollView(
            child: Column(
              //crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 30 + DeviceUtils().top.value + 44,
                ),
                RepaintBoundary(
                  key: controller.codeKey,
                  child: Column(
                    children: [
                      Container(
                        width: 263,
                        height: 34,
                        color: ColorConfig.backgroundColor,
                      ),
                      Container(
                    width: 263,
                    height: 429,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: ColorConfig.whiteColor),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 44,
                          child: Stack(
                            clipBehavior: Clip.none,
                            children: [
                              Positioned(
                                  top: -34,
                                  width: 68,
                                  height: 68,
                                  left: (263 - 68) * 0.5,
                                  child: Container(
                                    width: 68,
                                    height: 68,
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 1,color: ColorConfig.btnGrayColor),
                                        borderRadius: BorderRadius.circular(34),
                                        image: DecorationImage(
                                            image: SettingWidget.backImageProvider(
                                                controller.type == 0
                                                    ? controller.model!.logo
                                                    : controller
                                                        .userModel!.avatar))),
                                  )),
                            ],
                          ),
                        ),
                        Text(
                          controller.type == 0
                              ? controller.model!.name
                              : controller.userModel!.name,
                          style: const TextStyle(
                              fontSize: 16, color: ColorConfig.mainTextColor),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        Container(
                            width: 229,
                            height: 229,
                            color: ColorConfig.whiteColor,
                            child: controller.codeString.value == ''
                                ? Container()
                                : QrImageView(
                                    data: controller.codeString.value,
                                    size: 229,
                                  ),
                          ),
                        const SizedBox(
                          height: 25,
                        ),
                        InkWell(
                          onTap: () {
                            controller.savePicture();
                          },
                          child: const Text(
                            '保存到手机',
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.msgTextColor),
                          ),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                        Text(
                          controller.type == 0
                              ? '手机担当扫描二维码，快速加入企业'
                              : '使用担当扫一扫，添加我为担当好友',
                          style: const TextStyle(
                              fontSize: 12, color: ColorConfig.msgTextColor),
                        ),
                      ],
                    ))
                    ],
                  ),
                ),
                const SizedBox(
                  height: 40,
                ),
                Container(
                  width: double.infinity,
                  height: 52,
                  padding: const EdgeInsets.only(left: 15, right: 15),
                  child: Offstage(
                    offstage: controller.type == 1,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            controller.chooseImInvite();
                          },
                          child: SettingWidget().actionForInvite(
                              'assets/images/3.0x/contact_invite_im.png',
                              '邀请担当好友',
                              80,
                              52,
                              ColorConfig.backgroundColor),
                        ),
                        InkWell(
                          onTap: () async {
                            Map tokenDic =
                                await UserDefault.getData(Define.TOKENKEY);
                            WechatHelper.instance.shareLinkToWechat(
                                controller.shortUrl,
                                '',
                                '${tokenDic['name']}在$appName中邀请你加入企业[${controller.model!.name}]',
                                'assets/images/3.0x/about_icon.png');
                          },
                          child: SettingWidget().actionForInvite(
                              'assets/images/3.0x/contact_invite_wechat.png',
                              '通过微信邀请',
                              80,
                              52,
                              ColorConfig.backgroundColor),
                        ),
                        InkWell(
                          onTap: () async {
                            Map tokenDic = await UserDefault.getData(Define.TOKENKEY);
                            String name = tokenDic['name'];
                            BaseInfo().launchSmsURL(' ',
                                '$name在$appName中邀请你加入企业[${controller.model!.name}]。${controller.shortUrl}');
                          },
                          child: SettingWidget().actionForInvite(
                              'assets/images/3.0x/contact_invite_sms.png',
                              '通过短信邀请',
                              80,
                              52,
                              ColorConfig.backgroundColor),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ));
  }
}
