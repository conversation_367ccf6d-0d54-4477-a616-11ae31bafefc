import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../../helper/photo_manager.dart';
import '../../../model/friend/user_model.dart';
import '../../../model/org/org_model.dart';

class InviteHomeController extends GetxController {

  final GlobalKey codeKey = GlobalKey();
  OrgModel? model;
  RxString codeString = ''.obs;
  UserModel? userModel;
  int type = 0; //0组织 1个人二维码

  String shortUrl = '';
  String userId = '';
  @override
  void onInit() async {
    super.onInit();
    type = Get.arguments['type'];
    if (type == 1) {
      userModel = Get.arguments['userModel'];
    } else {
      model = Get.arguments['model'];
    }
    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    userId = userInfo['userId'];
    getShortUrl();
  }

  @override
  void onReady() {
    super.onReady();
    if (model != null) {
      codeString.value = 'o.${model!.companyId}';
      
    }
    if (userModel != null) {
      codeString.value = 'r.${userModel!.userId}';
    }
    
  }

  @override
  void onClose() {
    super.onClose();
  }

  //获取短链接地址
  getShortUrl() async {
    DioUtil().get('${LoginApi.MAKESHORTURL}?userId=$userId&companyId=${model?.companyId ?? ''}&url=${Host.SHORTHOST}', null, true,
        () {
    },isShowLoading: false).then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        shortUrl = data['data'];
      } else {
        toast('${data['msg']}');
      }
    });
  }

  // 保存到手机
  savePicture() async {
    RenderRepaintBoundary boundary =
        codeKey.currentContext?.findRenderObject() as RenderRepaintBoundary;
    ui.Image image =
        await boundary.toImage(pixelRatio: ui.window.devicePixelRatio);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    // 保存到相册
    final result = await PhotoHelper.saveToGallery(pngBytes);
    String alertDoc = result ? "保存成功" : "保存失败";
    toast(alertDoc);
  }

  //选择聊天 原生交互
  /**
   * {name: ljt企业, companyId: 2603725684413039614, corgId: ,
   * logo: https://cdn.ddbes.com/LOGO/1701321231053.jpg,
   * haveApprove: 7, rejectJoin: 0, isJoin: 0, deptId: 0, rejectInvitation: 0,
   * power: 123456, organizationId: null, type: ,
   * scale: , industry: , linkManPhone: , linkManMail: ,
   * officialWebsite: , profile: , content: 去, noticeId: 2603778546870518782}
   */
  chooseImInvite() {
    if(model == null) return;
    print("Channel_jumpChooseIM : ${model!.toJson()}");
    // Channel().invoke(Channel_jumpChooseIM, {'model': model!.toJson()});

    // var path = '/storage/emulated/0/Pictures/WeiXin/wx_camera_1741563561978.jpg';
    // RouteHelper.routePath(Routes.INVITE_BY_SESSION , arguments: path!);

    RouteHelper.routePath(Routes.INVITE_BY_SESSION , arguments: model!);
  }
}
