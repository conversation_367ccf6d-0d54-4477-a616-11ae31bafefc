import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/search_org_members_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/ios_native_route_focus.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/string-util.dart';
import '../../../../gallery/gallery.dart';
import '../../../model/org/org_model.dart';

class UserInfoController extends GetxController with MixNativeController {
  String userId = '';
  RxBool isFriend = true.obs;
  RxString name = ''.obs;
  RxString headimg = ''.obs;
  RxString mainOrgName = ''.obs;

  MemberModel? memberModel;
  OrgModel? model;
  late SearchMemberModel infoModel;
  int type = 0; //1手机号搜索2群组3组织4外部联系人

  RxList showList = [].obs; //可见字段数组

  String myUserId = '';

  bool isRouteBack = false;

  RxBool isHaveAuth = false.obs;
  RxBool isStranger = true.obs; //是否是陌生人
  StreamSubscription? subscription;

  String? fromSessionId = '';//会话来源 

  String? imId = '';//对方imId

  bool isShowSettingBtn () => isHaveAuth.value && userId != myUserId && !isStranger.value && isFriend.value;

  @override
  void onInit() async {
    super.onInit();
    if (Get.arguments != null) {
      userId = Get.arguments['userId'];
      type = Get.arguments['type'];
      if (Get.arguments['routeback'] != null) {
        isRouteBack = Get.arguments['routeback'];
      }
      if (type == 3) {
        model = Get.arguments['model'];
      }
      if (Get.arguments['fromSessionId'] != null) {
        fromSessionId = Get.arguments['fromSessionId'];
      }
    }
    dealOrgAuth();
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    myUserId = tokenInfo['userId'];

    subscription = eventBus.on<Map>().listen((event) async {
      logger('userinfo... event-------$event');
      if (event == null) return;
      var route = event['route'];
      if (route == Routes.USER_INFO) {
        var arguments = event['arguments'];
        userId = arguments['userId'];
        type = arguments['type'];
        isNative.value = true;
        if (type == 3) {
          if (arguments['companyId'] != null) {
            String companyId = arguments['companyId'];
            Map dataDic = await UserDefault.getData(Define.ORGLIST);

            List companies = [];
            if (dataDic['companies'] != null) {
              companies = dataDic['companies'];
            }
            for (var element in companies) {
              if (element['companyId'] == companyId) {
                Map<String, dynamic> orgDic = element;
                model = OrgModel.fromJson(orgDic);
              }
            }
          }
          if (arguments['deptId'] != null) {
            if (model != null) {
              model!.deptId = arguments['deptId'];
            }
          } else {
            if (model != null) {
              model!.isSureDeptId = false;
            }
          }
        }
        dealOrgAuth();
        getCompanyUserInfo();
      }
      if (event['userInfo'] != null) {
        getCompanyUserInfo();
      }
    });
  }

  //点击了头像
  tapAvatar(){
    if (StringUtil.isEmpty(headimg.value)) return;
    var galleryList = <GalleryItem>[];
    galleryList.add(GalleryItem('',headimg.value));
    Navigator.push(Get.context!,
        PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
          return FadeTransition(
              opacity: animation, child: GalleryPage(galleryList, 0));
        }));
  }

  navigate2UserSetting() async {
    dynamic argument;
    if (isNative.value) {
      argument = {"userId": userId, "model": model, "type": type, "routeback": isRouteBack};
    } else {
      argument = Get.arguments;
      argument.addAll({'userId': userId});
    }
    var result =
        await Get.toNamed(Routes.USER_INFO_SETTING, arguments: argument);
    if (result == 1) {
      // 请离企业
      Get.back();
    }
  }

  @override
  void onReady() {
    super.onReady();

    getCompanyUserInfo();
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

  //判断组织权限
  dealOrgAuth() {
    if (type == 3 && model != null) {
      if (model!.deptId == '0' ||
          (model!.power ?? '').contains('-1') ||
          (model!.power ?? '').contains('1')) {
        isHaveAuth.value = true;
      }
    } else {
      isHaveAuth.value = true;
    }

    logger('>>>>${isHaveAuth.value}');
  }

  //公司获取人员信息
  getCompanyUserInfo() async {
    List friendList = await UserDefault.getData(Define.FRIENDLIST);
    bool isHave = false;
    if (friendList.isNotEmpty) {
      for (var j = 0; j < friendList.length; j++) {
        Map friendMap = friendList[j];
        if (friendMap['userId'] == userId) {
          isHave = true;
        }
      }
    }
    if (myUserId == userId) {
      isHave = true;
    }
    if (type != 3) {
      if (isFriend.value == false) {
        isHaveAuth.value = false;
      }
    }

    isFriend.value = isHave;
    if (userId.isEmpty) return;
    String getUrl = '${ORGApi.COMPANYUSERINFO}/$userId';
    if (type == 3) {
      if (model == null) return;
      if (model!.isSureDeptId) {
        getUrl =
            '$getUrl?orgId=${model!.companyId}&deptId=${model!.deptId}&type=';
      } else {
        getUrl = '$getUrl?orgId=${model!.companyId}&deptId=&type=';
      }
    } else {
      if (isFriend.value) {
        getUrl = '$getUrl?orgId=&deptId=&type=3';
      } else {
        getUrl = '$getUrl?orgId=&deptId=&type=';
      }
    }
    DioUtil().get(getUrl, null, true, () {},isShowLoading: false).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        showList.clear();
        Map<String, dynamic> dataDic = data['data'];
        infoModel = SearchMemberModel.fromJson(dataDic);
        dynamic showType = dataDic['type']; //1自己2同组织3好友4陌生人
        if (showType != null) {
          if (showType == 4) {
            isStranger.value = true;
          } else {
            isStranger.value = false;
          }
        }

        logger('dataDic = ${isFriend.value}');
        dynamic unmae = dataDic['name'];
        if (unmae != null) {
          name.value = dataDic['name'];
        }

        dynamic avatar = dataDic['headimg'];
        if (avatar != null) {
          headimg.value = dataDic['headimg'];
        }

        var deptName = dataDic['mainOrgName'];
        if (deptName != null) {
          mainOrgName.value = deptName;
        }

        dynamic sessionId = dataDic['imId'];
        if (sessionId != null) {
          imId = sessionId;
        }

        if (dataDic['deptName'] != null) {
          showList.add({'name': '部门', 'content': dataDic['deptName']});
        }
        if (dataDic['positionName'] != null) {
          showList.add({'name': '职位', 'content': dataDic['positionName']});
        }
        if (dataDic['mobile'] != null) {
          showList.add({'name': '电话', 'content': dataDic['mobile']});
        }
        if (dataDic['email'] != null) {
          showList.add({'name': '邮箱', 'content': dataDic['email']});
        }
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //好友获取信息
  getUserInfo() async {
    if (userId.isEmpty) {
      return;
    }
    DioUtil()
        .get('${LoginApi.IM_USER_INFO}/$userId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        print('好友获取信息......');
        Map<String, dynamic> dataDic = data['data'];
        memberModel = MemberModel.fromJson(dataDic);

        isFriend.value = dataDic['isFriend'];
        if (myUserId == userId) {
          isFriend.value = true;
        }
        if (isFriend.value == false) {
          isHaveAuth.value = false;
        }
        name.value = dataDic['name'];
        headimg.value = dataDic['headimg'];
      } else {
        toast('${data['msg']}');
      }
    });
  }

  voliteIsAllow() async {
    //是否允许添加好友

    Map param = {'type': type == 0 ? 1 : type, 'userId': userId};
    DioUtil().post(LoginApi.ISALLOWFRIEND, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Get.toNamed(Routes.ORG_VERIFY,
            arguments: {'userId': userId, 'type': 1});
      } else {
        if (data['code'] == 1405) {
          toast('添加好友成功');
        } else {
          toast('${data['msg']}');
        }
      }
    });
  }

  //发消息 ，跳转到单聊消息页面
  sendMessage() async {
    Map<String, dynamic> dataDic = {};
    dataDic = infoModel.toJson();

    dataDic.addAll({'userId': userId});

    logger('dataDic = $dataDic');
    var session = await dataDic.createSinglePageParam();

    if (dataDic['judgeFriend'] != null) {
      var judgeFriend = (dataDic['judgeFriend'] as int);
      if (judgeFriend == 1) {
        await dataDic.sendMsgAfterFriend();
      }
    }
    if (fromSessionId == session.sessionId && type == 1) {
      //点击当前单聊会话对方头像进入个人信息页面，点消息返回
      Get.back();
      
    }else{
      RouteHelper.routeTotag(
          ChatPage(tag: session.sessionId), Routes.IM_CHAGE_PAGE,
          arguments: session, binding: ChatBinding(tag: session.sessionId));
    }
  }
}

class SingleInfoChatEntity {
  Map<String, dynamic> param = Map();

  String? targetId;
  String? targetName;
  String? targetLogo;
  String? sessionId;
}

class GroupInfoChatEntity {
  Map<String, dynamic> param = Map();
}
