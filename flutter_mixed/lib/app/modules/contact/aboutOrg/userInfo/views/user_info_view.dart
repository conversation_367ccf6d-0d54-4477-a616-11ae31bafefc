import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/dd_bottom_sheet.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/ios_native_route_focus.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/storage.dart';
import '../../../model/friend/user_model.dart';
import '../controllers/user_info_controller.dart';

class UserInfoView extends GetView<UserInfoController> {
  UserInfoView({Key? key}) : super(key: key);
  UserInfoController userInfoVC = Get.find();
  @override
  Widget build(BuildContext context) {
    return IosNativeRouteFixGesture.builder(
        controller: userInfoVC,
        childBuilder: () {
          return GetBuilder<UserInfoController>(
              init: userInfoVC,
              global: false,
              builder: (logic) {
                return Scaffold(
                  backgroundColor: ColorConfig.backgroundColor,
                  extendBodyBehindAppBar: true,
                  appBar: PreferredSize(
                      preferredSize: const Size.fromHeight(44),
                      child: AppBar(
                        title: const Text(
                          '',
                          style: TextStyle(color: Colors.white, fontSize: 16),
                        ),
                        actions: userInfoVC.isShowSettingBtn()
                            ? [
                                Container(
                                  width: 100,
                                  child: CupertinoButton(
                                      padding: const EdgeInsets.fromLTRB(
                                          60, 10, 16, 10),
                                      pressedOpacity: 0.5,
                                      child: Image.asset(
                                        'assets/images/3.0x/threePoint.png',
                                      ),
                                      onPressed: () {
                                        userInfoVC.navigate2UserSetting();
                                      }),
                                )
                              ]
                            : [],
                        centerTitle: true,
                        leading: IconButton(
                          onPressed: () {
                            if (userInfoVC.isNative.value) {
                              userInfoVC.onClose();
                              Channel().invoke(Channel_Native_Back, {});
                            } else {
                              Get.back();
                            }
                          },
                          icon: SizedBox(
                            width: 24,
                            height: 24,
                            child: Image.asset(
                                'assets/images/3.0x/pic_return.png'),
                          ),
                        ),
                        backgroundColor: const Color(0x00FFFFFF),
                        elevation: 0,
                      )),
                  resizeToAvoidBottomInset: false,
                  body: Stack(
                    children: [
                      Column(
                        children: [
                          Expanded(
                              child: SingleChildScrollView(
                            child: Column(
                              children: [
                                _buildUserInfoBackImage(),
                                12.gap,
                                _buildNameAndCompany(),
                                12.gap,
                                Offstage(
                                  offstage: userInfoVC.showList.isEmpty,
                                  child: _buildShowContent(),
                                ),
                              ],
                            ),
                          )),
                          _buildBottomContent(),
                          DeviceUtils().bottom.value.gap
                        ],
                      ),
                      _buildAvatar()
                    ],
                  ),
                );
              });
        });
  }

  _buildAvatar() {
    double imageW = 88;
    double backImageH = DeviceUtils().width.value * 140 / 375;
    double padding = 12;
    return Positioned(
        left: 28,
        width: imageW,
        height: imageW,
        top: backImageH + padding - (imageW / 2),
        child: InkWell(
          onTap: () {
            userInfoVC.tapAvatar();
          },
          child: Container(
            width: 88,
            height: 88,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                image: DecorationImage(
                    image: ExtendedNetworkImageProvider(
                        userInfoVC.headimg.value))),
          ),
        ));
  }

  //背景图
  _buildUserInfoBackImage() {
    return Container(
      width: DeviceUtils().width.value,
      height: DeviceUtils().width.value * 140 / 375,
      child: FittedBox(
        fit: BoxFit.fitWidth,
        alignment: Alignment.topCenter,
        child: Image.asset(AssetsRes.USER_INFO_BACK),
      ),
    );
  }

  //名称与公司
  _buildNameAndCompany() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: ColorConfig.whiteColor),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: 44,
            alignment: Alignment.centerRight,
            child: Offstage(
              offstage: userInfoVC.userId != userInfoVC.myUserId,
              child: InkWell(
                onTap: () async {
                  Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
                  UserModel userModel = UserModel(tokenInfo['userId']);
                  userModel.avatar = tokenInfo['avatar'];
                  userModel.name = tokenInfo['name'];
                  Get.toNamed('/invite-home',
                      arguments: {'userModel': userModel, 'type': 1});
                },
                child: Container(
                  width: 44,
                  height: 44,
                  padding: const EdgeInsets.only(left: 18, bottom: 18),
                  child: Image.asset(AssetsRes.USER_INFO_QR),
                ),
              ),
            ),
          ),
          Container(
            width: double.infinity,
            height: 28,
            child: Text(
              userInfoVC.name.value,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: ColorConfig.mainTextColor),
            ),
          ),
          2.gap,
          Container(
            width: double.infinity,
            height: 22,
            child: Text(
              userInfoVC.mainOrgName.value,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.msgTextColor),
            ),
          )
        ],
      ),
    );
  }

  backShowContent() {
    List<Widget> lists = [];
    for (var i = 0; i < userInfoVC.showList.length; i++) {
      Map showMap = userInfoVC.showList[i];
      lists.add(SettingWidget().backSettingWidget(
          '', '', showMap['name'], showMap['content'], false, 44));
    }
    if (userInfoVC.userId == userInfoVC.myUserId) {
      lists.add(InkWell(
        onTap: () async {
          Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
          UserModel userModel = UserModel(tokenInfo['userId']);
          userModel.avatar = tokenInfo['avatar'];
          userModel.name = tokenInfo['name'];
          Get.toNamed('/invite-home',
              arguments: {'userModel': userModel, 'type': 1});
        },
        child: Container(
          color: ColorConfig.whiteColor,
          height: 44,
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: const Text(
                  '我的二维码',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                child: const Text(
                  '立即查看',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.themeCorlor),
                ),
              )
            ],
          ),
        ),
      ));
    }
    return lists;
  }

  _buildShowContent() {
    //以最宽的对齐
    List leftList = [];
    List rightList = [];
    for (var i = 0; i < userInfoVC.showList.length; i++) {
      Map showMap = userInfoVC.showList[i];
      leftList.add(showMap['name']);
      rightList.add(showMap['content']);
    }
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: ColorConfig.whiteColor),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _buildLeftContent(leftList),
          ),
          16.gap,
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _buildRightContent(rightList),
          ))
        ],
      ),
    );
  }

  _buildLeftContent(List leftList) {
    List<Widget> lists = [];
    for (var i = 0; i < leftList.length; i++) {
      lists.add(Container(
        padding: const EdgeInsets.only(left: 16),
        height: 46,
        alignment: Alignment.centerLeft,
        child: Text(
          leftList[i],
          style: const TextStyle(fontSize: 16, color: ColorConfig.msgTextColor),
        ),
      ));
    }
    return lists;
  }

  _buildRightContent(List rightList) {
    List<Widget> lists = [];
    for (var i = 0; i < rightList.length; i++) {
      lists.add(Container(
        height: 46,
        alignment: Alignment.centerLeft,
        child: Column(
          children: [
            Expanded(
                child: Container(
              width: double.infinity,
              alignment: Alignment.centerLeft,
              child: Text(
                rightList[i],
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    fontSize: 16, color: ColorConfig.mainTextColor),
              ),
            )),
            Container(
              color: i != rightList.length - 1
                  ? ColorConfig.backgroundColor
                  : Colors.transparent,
              height: 0.5,
            )
          ],
        ),
      ));
    }
    return lists;
  }

  _buildBottomContent() {
    if (userInfoVC.name.isEmpty) return Container();
    if (userInfoVC.isStranger.value) {
      return Container(
        alignment: Alignment.center,
        width: double.infinity,
        height: 80,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: _buildSingleAddBtn(),
      );
    } else {
      return Container(
        alignment: Alignment.center,
        width: double.infinity,
        height: 80,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            _buildSendMessage(),
            if (userInfoVC.userId != userInfoVC.myUserId) ...[
              12.gap,
              _buildCall(),
            ],
            if (!userInfoVC.isFriend.value) ...[12.gap, _buildAddFriendForRow()]
          ],
        ),
      );
    }
  }

  //发消息
  _buildSendMessage() {
    return Expanded(
        flex: 1,
        child: InkWell(
          onTap: () {
            userInfoVC.sendMessage();
          },
          child: Container(
            height: 48,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: ColorConfig.mybackgroundColor),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 24,
                  height: 24,
                  child: Image.asset(AssetsRes.USER_INFO_MSG),
                ),
                8.gap,
                const Text(
                  '发消息',
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: ColorConfig.themeColorSecondary),
                )
              ],
            ),
          ),
        ));
  }

  //语音通话
  _buildCall() {
    return Expanded(
        flex: 1,
        child: InkWell(
          onTap: () {
            _showBottomSheet();
          },
          child: Container(
          height: 48,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: ColorConfig.mybackgroundColor),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: Image.asset(AssetsRes.USER_INFO_CALL),
              ),
              8.gap,
              const Text(
                '语音通话',
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: ColorConfig.themeColorSecondary),
              )
            ],
          ),
        ),
        ));
  }

  _showBottomSheet() {
    List sheetList = [
      {'icon': AssetsRes.USER_INFO_AUDIO_CALL, 'name': '语音通话'},
      {'icon': AssetsRes.USER_INFO_VIDEO_CALL, 'name': '视频通话'}
    ];
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        DdBottomSheet(
          sheetList: sheetList,
          onSelectPressed: (index) {
            if (index == 0) {
              //语音通话
              _sendCall(1);
            } else if (index == 1) {
              //视频通话
              _sendCall(2);
            }
          },
          onCancelPressed: () {},
        ));
  }

  //发起语音、视频通话
  _sendCall(int type){
    if (StringUtil.isEmpty(userInfoVC.imId)) return;
    Map<String, dynamic> map = {
      'sessionType': ConstantImMsgType.SSChatConversationTypeChat, //会话类型
      'type': type, //int 1语音2视频3加入
      'userId': userInfoVC.myUserId, //自己的userId,
      'appChatId': userInfoVC.userId, //单聊为对方的userId,群组为groupId
      'targetName': userInfoVC.name.value,
      'targetLogo': userInfoVC.headimg.value,
      'sessionId': userInfoVC.imId,
    };
    Channel().invoke(Channel_Native_IM_Video, map);
  }

  //在row上的添加好友控件
  _buildAddFriendForRow() {
    return InkWell(
      onTap: () {
        userInfoVC.voliteIsAllow();
      },
      child: Container(
        width: 48,
        height: 48,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: ColorConfig.mybackgroundColor),
        child: SizedBox(
          width: 20,
          height: 20,
          child: Image.asset(AssetsRes.USER_INFO_ADD_BLUE),
        ),
      ),
    );
  }

  //单独添加好友控件
  _buildSingleAddBtn() {
    return InkWell(
      onTap: () {
        userInfoVC.voliteIsAllow();
      },
      child: Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: ColorConfig.themeCorlor),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
                width: 20,
                height: 20,
                child: Image.asset(AssetsRes.USER_INFO_ADD_WHITE)),
            8.gap,
            const Text(
              '添加好友',
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ColorConfig.whiteColor),
            )
          ],
        ),
      ),
    );
  }
}
