import 'dart:async';
import 'dart:io';

import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_photo_editor/flutter_photo_editor.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/base_info/info.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../permission/permission_util.dart';
import '../../../../../utils/cos_helper.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../../workStand/models/filemodel.dart';

class OrgDetailSettingController extends GetxController {
  late OrgModel model;
  RxString nameStr = ''.obs;

  RxBool isHaveAuth = false.obs;

  String uploadBucket = '';
  Completer? completer;

  String preStr = '';
  RxString logoStr = ''.obs;
  @override
  void onInit() async {
    super.onInit();
    model = Get.arguments['model'];

    // Map cosInfo = await UserDefault.getData(Define.COSTOKENKEY);
    // uploadBucket = cosInfo['userBucket'];

    dynamic preMap = await UserDefault.getData(Define.COSPREKEY);
    if (preMap != null) {
      preStr = preMap['pre'];
    }
    getOrgDetail();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getOrgDetail() async {
    DioUtil()
        .get('${ORGApi.GETCOMPANYINFO}/${model.companyId}', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        OrgModel tempModel = OrgModel.fromJson(data['data']);
        tempModel.companyId = model.companyId;
        model = tempModel;
        nameStr.value = model.name;
        if (model.deptId == '0' ||
            (model.power ?? '').contains('-1') ||
            (model.power ?? '').contains('1')) {
          isHaveAuth.value = true;
        }
        logoStr.value = model.logo;
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //拍照
  tackPhotoWithCamera() async {
    var r = await PermissionUtil.checkCameraPermission(Get.context!);
    if (!r) return;
    File? file;
    if (Platform.isIOS) {
      //iOS使用插件会出现相机功能一直启用，右上角绿点一直展示的问题
      var result = await Channel()
          .invokeMap(Channel_call_iOS_camera, {'allowsEditing': 1});

      String filePath = result!['filePath'];
      file = File(filePath);
    } else {
      final AssetEntity? entity =
          await CameraPicker.pickFromCamera(Get.context!);

      file = await entity!.file;
    }
    if (file == null) return;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    await startDealFile(fileModel);
    await startUploadFileList(fileModel);
  }

  //从相册中选取

  tackPhotoFromPic() async {
    var r = await PermissionUtil.checkGalleryPermission(Get.context!);
    if (!r) return;

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: const AssetPickerConfig(
            maxAssets: 1, requestType: RequestType.image));

    if (assets == null) return;
    if (assets.isEmpty) return;

    var entity = assets.first.file;
    File? file = await entity;
    if (file == null) return;
    if (file.path == null) return;

    File tempFile = await CosHelper.move2Img(file);
    var cropResult = await FlutterPhotoEditor().editImage(tempFile.path);

    file = tempFile;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    print('$fileModel');

    await startDealFile(fileModel);
    await startUploadFileList(fileModel);
  }

  Future startDealFile(FileModel fileModel) async {
    completer = Completer<String>();
    await saveFile(fileModel).then((value) {
      fileModel.savePath = value;
      fileModel.hash = BaseInfo().generateMD5(value);
      if (completer!.isCompleted == false) {
        completer!.complete('');
      }
    });
    return completer!.future;
  }

  //保存图片
  Future<String> saveFile(FileModel fileModel) async {
    File file = fileModel.file!;
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;
    String filePath = '$docment/approve/images/$fileName';
    Directory directory = Directory('$docment/approve/images');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  startUploadFileList(FileModel fileModel) async {
    Get.loading();

    await CosManager().initPans();
    String? bucket = await CosManager().userBucket();
    var result = await CosUploadHelper.nativeUpload(
        fileModel.savePath, '/LOGO/${fileModel.fileName}', bucket);
    if (result != null) {
      logoStr.value = result;
      settingOrgDetail({'logo': logoStr.value});
    }else{
      print('-----上传失败--${fileModel.filePath}');
      // toast('图片上传失败');
    }

    Get.dismiss();
  }

  settingOrgDetail(Map result) {
    Map param = {'companyId': model.companyId};
    param.addAll(result);
    DioUtil().put(ORGApi.CHANGEORGDETAIL, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('修改成功');

        if (result['logo'] != null) {
          model.logo = result['logo'];
          logoStr.value = result['logo'];
        }
        if (result['name'] != null) {
          model.name = result['name'];
          nameStr.value = result['name'];
        }
        if (result['industry'] != null) {
          model.industry = result['industry'];
        }
        if (result['type'] != null) {
          model.type = result['type'];
        }
        if (result['scale'] != null) {
          model.scale = result['scale'];
        }

        nameStr.refresh();
        eventBus.fire({'moreSetting': model});
        HomeController homeController = Get.find();
        homeController.getAllCompanies();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //退出企业
  exitOrg() {
    DioUtil()
        .delete('${ORGApi.PERSONOUTORG}/${model.companyId}', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('退出企业成功');
        HomeController homeController = Get.find();
        homeController.getAllCompanies();
        Get.until((route) {
          if (route.settings.name == '/org-list' ||
              route.settings.name == '/orgHome' ||
              route.settings.name == Routes.HOME) {
            return true;
          }
          return false;
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
