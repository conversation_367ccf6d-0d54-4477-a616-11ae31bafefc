import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/widget/some_widget.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/org_detail_setting_controller.dart';

class OrgDetailSettingView extends GetView<OrgDetailSettingController> {
  const OrgDetailSettingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => ToolBar(
      title: '企业设置',
      body: ListView(
        children: [
          InkWell(
            child: Container(
              color: Colors.white,
              width: double.infinity,
              height: 80,
              padding: EdgeInsets.only(left: 15, right: 15),
              child: Column(
                children: [
                  InkWell(
                      onTap: () {
                        if (controller.isHaveAuth.value) {
                          SettingWidget().showCupertinoActionSheetForPage(
                              context, ['拍照', '从相册中选取'], (value) {
                            if (value == 0) {
                              controller.tackPhotoWithCamera();
                            }
                            if (value == 1) {
                              controller.tackPhotoFromPic();
                            }
                          });
                        }
                      },
                      child: Container(
                        width: double.infinity,
                        height: 70,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                '企业LOGO',
                                textAlign: TextAlign.left,
                                style: TextStyle(
                                    color: ColorConfig.mainTextColor,
                                    fontSize: 14),
                              ),
                            ),
                            Container(
                              width: 76,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  ImageLoader(url:controller
                                      .logoStr.value, width: 50,height: 50,),

                                  Offstage(
                                    offstage: !controller.isHaveAuth.value,
                                    child: Row(
                                      children: [
                                        SizedBox(
                                          width: 10,
                                        ),
                                        SizedBox(
                                          width: 9,
                                          height: 17,
                                          child: Image.asset(
                                              'assets/images/3.0x/mine_right.png'),
                                        )
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      )),
                  line,
                ],
              ),
            ),
          ),
          InkWell(
              onTap: () async {
                if (controller.model.deptId == '0') {
                  var result = await Get.toNamed('/setting-org-name',
                      arguments: {
                        'type': 0,
                        'name': controller.model.name
                      });
                  if (result != null) {
                    controller.settingOrgDetail(result);
                  }
                } else {
                  toast('只有创建者才能修改企业名称');
                }
              },
              child: SettingWidget().backEditOrgWidget(
                  '企业名称',
                  controller.nameStr.value,
                  controller.model.deptId == '0' ? true : false)),
          InkWell(
              onTap: () async {
                if (controller.isHaveAuth.value) {
                  var result = await Get.toNamed('/setting-org-industry',
                      arguments: {'model': controller.model});
                  if (result != null) {
                    controller.settingOrgDetail(result);
                  }
                }
              },
              child: SettingWidget().backEditOrgWidget(
                  '企业所属行业',
                  controller.model.industry ?? '',
                  controller.isHaveAuth.value)),
          InkWell(
              onTap: () async {
                if (controller.isHaveAuth.value) {
                  var result = await Get.toNamed('/setting-org-scale',
                      arguments: {'type': 1, 'model': controller.model});
                  if (result != null) {
                    controller.settingOrgDetail(result);
                  }
                }
              },
              child: SettingWidget().backEditOrgWidget(
                  '企业类型',
                  controller.model.type ?? '',
                  controller.isHaveAuth.value)),
          InkWell(
              onTap: () async {
                if (controller.isHaveAuth.value) {
                  var result = await Get.toNamed('/setting-org-scale',
                      arguments: {'type': 0, 'model': controller.model});
                  if (result != null) {
                    controller.settingOrgDetail(result);
                  }
                }
              },
              child: SettingWidget().backEditOrgWidget(
                  '企业人数',
                  controller.model.scale ?? '',
                  controller.isHaveAuth.value)),
          SizedBox(
            height: 30,
          ),
          Container(
            width: double.infinity,
            height: 48,
            color: ColorConfig.whiteColor,
            child: CupertinoButton(
                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                pressedOpacity: 0.5,
                child: Text(
                  controller.isHaveAuth.value ? '解散企业' : '退出企业',
                  style: TextStyle(
                      color: ColorConfig.deleteCorlor, fontSize: 16),
                ),
                onPressed: () async {
                  SettingWidget().showCupertinoActionSheetForPage(context,
                      controller.isHaveAuth.value ? ['解散该企业'] : ['直接退出'],
                          (value) {
                        if (value == 0) {
                          if (controller.isHaveAuth.value) {
                            Get.toNamed('/dissolve-org',
                                arguments: {'model': controller.model});
                          } else {
                            controller.exitOrg();
                          }
                        }
                      },
                      messsage: controller.isHaveAuth.value
                          ? '解散之后将删除所有后台管理和通讯录中的用户，且数据无法找回，请谨慎操作。'
                          : '退出之后将删除所有企业内的聊天信息，且数据无法找回，请谨慎操作');
                }),
          )
        ],
      ),
    ));
  }
}
