import 'package:flutter_mixed/app/im/request/entity/notice_detail_data.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/orgPermission/views/permission_item.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

class OrgPermissionInfoController extends GetxController {

  //通过点击公司通知IM查询详情
  ClientNoticeMsg? noticeMsg;
  NoticeData? noticeData;
  NoticeDataDetail? detailData;
  List permissions = [];
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments['noticeMsg'] != null) {
      noticeMsg = Get.arguments['noticeMsg'];
      noticeData = noticeMsg?.getDataP();
      detailData = noticeData?.getDetail();
      if (detailData != null) {
        if (!StringUtil.isEmpty(detailData!.permissions)) {
          _dealPermissions(detailData!.permissions!);
        }
      }
      update();
    }
  }

  _dealPermissions(String permissionStr){
    permissions.clear();
    for (var i = 0; i < PermissionData.dataList.length; i++) {
      Map permissionMap = PermissionData.dataList[i];
      String power = permissionMap['power'];
      if (permissionStr.contains(power) || permissionStr.contains('-1')) {
        permissions.add(permissionMap);
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
