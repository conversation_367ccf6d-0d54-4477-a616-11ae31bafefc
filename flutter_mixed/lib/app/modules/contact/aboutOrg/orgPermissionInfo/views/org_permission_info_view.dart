import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';

import '../controllers/org_permission_info_controller.dart';

class OrgPermissionInfoView extends GetView<OrgPermissionInfoController> {
  OrgPermissionInfoView({super.key});
  OrgPermissionInfoController orgPermissionInfoController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        init: orgPermissionInfoController,
        global: false,
        builder: (_) {
          return ToolBar(
            title: '管理员分配',
            body: Column(
              children: [
                1.gap,
                _backMsgWidget(),
                Expanded(child: _backContentWidget())
              ],
            ),
          );
        });
  }

  _backMsgWidget() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 10, 16, 10),
      color: ColorConfig.whiteColor,
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              orgPermissionInfoController.noticeMsg?.context ?? '',
              style: const TextStyle(
                  fontSize: 18, color: ColorConfig.mainTextColor),
            ),
          ),
          10.gap,
          Container(
            alignment: Alignment.centerLeft,
            child: RichText(
                maxLines: null,
                text: TextSpan(
                    text: '管理员名称: ',
                    style: const TextStyle(
                        fontSize: 15, color: ColorConfig.mainTextColor),
                    children: [
                      TextSpan(
                          text: orgPermissionInfoController
                                  .noticeData?.noticeTitle ??
                              '',
                          style: const TextStyle(
                              fontSize: 15, color: ColorConfig.themeCorlor))
                    ])),
          ),
        ],
      ),
    );
  }

  _backContentWidget() {
    if (orgPermissionInfoController.permissions.isNotEmpty) {
      return Column(
        children: [_backMidWidget(), Expanded(child: _backPermissionInfo())],
      );
    } else {
      return _backNoPermisssionView();
    }
  }

  _backMidWidget() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      height: 35,
      child: const Text(
        '权限',
        style: TextStyle(fontSize: 15, color: ColorConfig.mainTextColor),
      ),
    );
  }

  _backPermissionInfo() {
    return ListView.builder(
        itemCount: orgPermissionInfoController.permissions.length,
        itemBuilder: (context, index) {
          return _backBodyWidget(
              orgPermissionInfoController.permissions[index]);
        });
  }

  _backBodyWidget(Map permissionMap) {
    String imageName = permissionMap['imageName'] ?? '';
    String topStr = permissionMap['topStr'] ?? '';
    String bottomStr = permissionMap['bottomStr'] ?? '';
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 58,
      color: ColorConfig.whiteColor,
      child: Column(
        children: [
          Expanded(
              child: Row(
            children: [
              Image.asset(
                imageName,
                width: 32,
              ),
              8.gap,
              Expanded(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      topStr,
                      style: const TextStyle(
                          fontSize: 15, color: ColorConfig.mainTextColor),
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      bottomStr,
                      style: const TextStyle(
                          fontSize: 12, color: ColorConfig.mainTextColor),
                    ),
                  )
                ],
              ))
            ],
          )),
          const Divider(
            indent: 0,
            color: ColorConfig.backgroundColor,
            height: 1,
          )
        ],
      ),
    );
  }

  _backNoPermisssionView(){
    return Container(
      alignment: Alignment.center,
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(AssetsRes.ORG_NO_PERMISSIONS,width: 86,),
          10.gap,
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: const Text('管理员未分配权限',style: TextStyle(fontSize: 15,color: ColorConfig.mainTextColor),),
          )
        ],
      ),
    );
  }
}
