import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/external_add_controller.dart';

class ExternalAddView extends GetView<ExternalAddController> {
  const ExternalAddView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: ColorConfig.backgroundColor,
          appBar: TitleBar().backAppbar(context, '添加外部联系人', false, [
            Container(
              width: 60,
              child: CupertinoButton(
                  padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                  pressedOpacity: 0.5,
                  child: const Text(
                    '保存',
                    style:
                        TextStyle(color: ColorConfig.themeCorlor, fontSize: 14),
                  ),
                  onPressed: () {
                    controller.voliteTel();
                  }),
            )
          ], onPressed: () {
            Get.back();
          }),
          body: ListView(
            children: [
              SizedBox(
                height: 16,
              ),
              Container(
                height: 20,
                padding: EdgeInsets.only(left: 15),
                alignment: Alignment.centerLeft,
                child: Text(
                  '外部协作人信息',
                  style:
                      TextStyle(fontSize: 13, color: ColorConfig.desTextColor),
                ),
              ),
              Container(
                child: Column(
                  children: [
                    Container(
                      color: ColorConfig.whiteColor,
                      height: 55,
                      padding: EdgeInsets.only(left: 15, right: 15),
                      child: Column(
                        children: [
                          Container(
                            height: 54,
                            child: Row(
                              children: [
                                Container(
                                  width: 54,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '姓名',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                ),
                                SizedBox(
                                  width: 16,
                                ),
                                Expanded(
                                    child: TextField(
                                  onChanged: (value) {},
                                  inputFormatters: [
                                    LengthLimitingTextInputFormatter(10)
                                  ],
                                  textInputAction: TextInputAction.done,
                                  controller: controller.nameController,
                                  style: const TextStyle(
                                    color: ColorConfig.mainTextColor,
                                    fontSize: 16,
                                  ),
                                  decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.only(
                                          top: 0, bottom: 0),
                                      border: const OutlineInputBorder(
                                          borderSide: BorderSide.none),
                                      hintText: '必填',
                                      hintStyle: const TextStyle(
                                        color: ColorConfig.desTextColor,
                                        fontSize: 16,
                                      )),
                                )),
                                InkWell(
                                  onTap: () async{
                                     await Get.toNamed('/contact-add-friend',arguments: {'type':1});
                                     
                                  },
                                  child: SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: Image.asset(
                                        'assets/images/3.0x/external_contact.png'),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Divider(
                            height: 1,
                            color: ColorConfig.backgroundColor,
                          )
                        ],
                      ),
                    ),
                    getWidgetForExternalInfo(
                        controller.telController, '手机号', '必填'),
                    getWidgetForExternalInfo(
                        controller.emailContrller, '邮箱', '请填写邮箱'),
                    getWidgetForExternalInfo(
                        controller.markController, '备注', '请填写备注'),
                    SizedBox(
                      height: 16,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.only(left: 15),
                      height: 20,
                      child: Text(
                        '外部协作人属性',
                        style: TextStyle(
                            fontSize: 13, color: ColorConfig.desTextColor),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        //showCupertinoActionSheetForExternal(context, 0);
                        SettingWidget().showCupertinoActionSheetForPage(
                            context, controller.typeList, (int value) {
                          controller.didChooseSheet(0, value);
                        });
                      },
                      child: backExternalSettingWidget(
                          '类型',
                          controller.typeList[controller.typeInt.value],
                          ColorConfig.desTextColor),
                    ),
                    InkWell(
                      onTap: () {
                        SettingWidget().showCupertinoActionSheetForPage(
                            context, controller.levelList, (int value) {
                          controller.didChooseSheet(1, value);
                        });
                      },
                      child: backExternalSettingWidget(
                          '重要级别',
                          controller.levelList[controller.levelInt.value],
                          controller.levelColorList[controller.levelInt.value]),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    Offstage(
                      offstage: controller.hideSendSwitch.value,
                      child: Container(
                        width: double.infinity,
                        height: 54,
                        padding: EdgeInsets.only(left: 15, right: 15),
                        color: Colors.white,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                child: Text(
                              '发送激活邀请给该用户',
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            )),
                            Container(
                                width: 80,
                                height: 54,
                                alignment: Alignment.centerRight,
                                child: Obx(
                                  () => CupertinoSwitch(
                                      activeColor: ColorConfig.themeCorlor,
                                      trackColor: ColorConfig.lineColor,
                                      value: controller.isSend.value,
                                      onChanged: (value) {
                                        controller.isSend.value = value;
                                      }),
                                ))
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    Offstage(
                      offstage: controller.type == 0,
                      child: InkWell(
                        onTap: () {
                          controller.deleteExternalContacts();
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: double.infinity,
                          height: 54,
                          color: ColorConfig.whiteColor,
                          child: const Text(
                            '删除外部协作人员',
                            style: TextStyle(
                                fontSize: 16, color: ColorConfig.deleteCorlor),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }

  getWidgetForExternalInfo(
      TextEditingController editingController, String leftStr, String hitText) {
    return Container(
      color: ColorConfig.whiteColor,
      height: 55,
      padding: EdgeInsets.only(left: 15, right: 15),
      child: Column(
        children: [
          Container(
            height: 54,
            child: Row(
              children: [
                Container(
                  width: 54,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    leftStr,
                    style: TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                ),
                SizedBox(
                  width: 16,
                ),
                Expanded(
                    child: TextField(
                  onChanged: (value) {},
                  textInputAction: TextInputAction.done,
                  controller: editingController,
                  style: const TextStyle(
                    color: ColorConfig.mainTextColor,
                    fontSize: 16,
                  ),
                  decoration: InputDecoration(
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: hitText,
                      hintStyle: const TextStyle(
                        color: ColorConfig.desTextColor,
                        fontSize: 16,
                      )),
                )),
              ],
            ),
          ),
          Divider(
            height: 1,
            color: ColorConfig.backgroundColor,
          )
        ],
      ),
    );
  }

  backExternalSettingWidget(String leftStr, String rightStr, Color rightColor) {
    return Column(
      children: [
        Container(
          color: ColorConfig.whiteColor,
          height: 55,
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: 80,
                    alignment: Alignment.centerLeft,
                    height: 54,
                    child: Text(leftStr,
                        style: const TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 14)),
                  )
                ],
              ),
              Container(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Offstage(
                      offstage: rightStr.isEmpty,
                      child: Container(
                        alignment: Alignment.centerRight,
                        height: 54,
                        child: Text(rightStr,
                            maxLines: 1,
                            style: TextStyle(color: rightColor, fontSize: 14)),
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    SizedBox(
                      width: 9,
                      height: 17,
                      child: Image.asset('assets/images/3.0x/mine_right.png'),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
        const Divider(height: 1, color: ColorConfig.backgroundColor, indent: 15)
      ],
    );
  }
}
