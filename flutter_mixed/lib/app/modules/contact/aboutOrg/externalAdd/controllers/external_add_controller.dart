import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/externalContacts/controllers/external_contacts_controller.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/config/string_const.dart';
import '../../../../../common/event/event.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/external_model.dart';
import '../../../model/org/org_model.dart';

class ExternalAddController extends GetxController {

  TextEditingController nameController = TextEditingController();
  TextEditingController telController = TextEditingController();
  TextEditingController emailContrller = TextEditingController();
  TextEditingController markController = TextEditingController();

  List levelList = ['非常重要', '重要', '普通'];
  List typeList = ['客户', '渠道商', '供应商', '合作伙伴', '其他'];
  List levelColorList = [
    ColorConfig.externalLevelOne,
    ColorConfig.themeCorlor,
    ColorConfig.externalLevelNormal
  ];

  RxInt typeInt = 0.obs;
  RxInt levelInt = 0.obs;
  RxBool isSend = true.obs;

  late OrgModel model;
  int type = 0; //0添加 1编辑
  late ExternalModel externalModel;
  RxBool hideSendSwitch = false.obs;

  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    type = Get.arguments['type'];
    if (type == 1) {
      externalModel = Get.arguments['externalModel'];
      nameController.text = externalModel.externalContactsName;
      telController.text = externalModel.mobile;
      emailContrller.text = externalModel.email;
      markController.text = externalModel.desc;

      typeInt.value = externalModel.type - 1;
      levelInt.value = externalModel.level - 1;
      if (externalModel.active == 0) {
        hideSendSwitch.value = true;
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    nameController.dispose();
    telController.dispose();
    emailContrller.dispose();
    markController.dispose();
  }

  voliteTel() {
    if (nameController.text.isEmpty) {
      toast('请输入姓名');
      return;
    }
    if (telController.text.isEmpty) {
      toast('请输入手机号');
      return;
    }
    
    DioUtil().get('${LoginApi.VERIFYTELURL}/${telController.text}', null, false,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('当前添加的外部协作人员，尚未注册$appName!');
      } else if (data['code'] == 1100) {
        if (type == 0) {
          addExternalContacts();
        } else {
          editExternalContacts();
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  addExternalContacts() {
    Map param = {
      'orgId': model.companyId,
      'name': nameController.text,
      'phone': telController.text,
      'email': emailContrller.text,
      'desc': markController.text,
      'type': typeInt.value + 1,
      'level': levelInt.value + 1,
      'isSend': isSend.value ? 1 : 0,
    };
    DioUtil().post(ORGApi.ADD_PUT_EXTERNAL_CONTACT, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('添加成功');
        Get.back(result: {'isAdd': 1});
        eventBus.fire(ExternalRefresh(ExternalModel('external')));
      } else {
        toast('${data['msg']}');
      }
    });
  }

  editExternalContacts() {
    Map param = {
      'orgId': model.companyId,
      'name': nameController.text,
      'phone': telController.text,
      'email': emailContrller.text,
      'desc': markController.text,
      'type': typeInt.value + 1,
      'level': levelInt.value + 1,
      'isSend': isSend.value ? 1 : 0,
      'userId': externalModel.userId
    };
    DioUtil().put(ORGApi.ADD_PUT_EXTERNAL_CONTACT, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('修改成功');
        Get.back(result: {'isAdd': 1});
        eventBus.fire(ExternalRefresh(externalModel));
      } else {
        toast('${data['msg']}');
      }
    });
  }

  deleteExternalContacts() {
    Map param = {'orgId': model.companyId, 'userId': externalModel.userId};
    DioUtil().patch(ORGApi.ADD_PUT_EXTERNAL_CONTACT, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('删除成功');
        eventBus.fire(ExternalRefresh(externalModel));
        Get.until((route) {
          if (route.settings.name == '/external-contacts') {
            return true;
          }
          return false;
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }

  didChooseSheet(int type, int value) {
    if (type == 0) {
      typeInt.value = value;
    } else {
      levelInt.value = value;
    }
  }
}
