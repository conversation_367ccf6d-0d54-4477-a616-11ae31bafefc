import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';

import 'package:get/get.dart';

import '../controllers/choose_leader_controller.dart';

class ChooseLeaderView extends GetView<ChooseLeaderController> {
  const ChooseLeaderView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      extendBodyBehindAppBar: true,
      appBar: TitleBar().backAppbar(context, '选择负责人', false, [], onPressed: (){
        Get.back();
      }),
      body: ListView.builder(
        itemCount: controller.dataList.length,
        itemBuilder: (context,index){
          MemberModel memberModel = MemberModel.from<PERSON><PERSON>(controller.dataList[index]);
          return InkWell(
            onTap: () {
              Get.back(result: {'memberModel':memberModel});
            },
            child: SettingWidget().backSettingWidget('', memberModel.headimg, memberModel.name, '', true, 56),
          );
      }),
    ));
  }
}
