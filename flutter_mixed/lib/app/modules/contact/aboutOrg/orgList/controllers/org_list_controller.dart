import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';

class OrgListController extends GetxController {

  final List titleList = ['我创建的企业', '我加入的企业', '我的合作企业'];

  RxList dataList = [].obs;

  RxInt companyCount = 0.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    getAllCompany();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getAllCompany() async {
    Map dataDic = await UserDefault.getData(Define.ORGLIST);

    List externalCompanies = [];
    if (dataDic['externalCompanies'] != null) {
      externalCompanies = dataDic['externalCompanies'];
    }
    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }
    List createList = [];
    List joinList = [];
    List externalList = [];
    if (companies.isNotEmpty) {
      for (var i = 0; i < companies.length; i++) {
        OrgModel model = OrgModel.fromJson(companies[i]);
        if (model.companyId != dataDic['mainCompanyId']) {
          if (model.deptId == '0') {
            createList.add(model);
          } else {
            joinList.add(model);
          }
        }
      }
    }

    if (externalCompanies.isNotEmpty) {
      for (var i = 0; i < externalCompanies.length; i++) {
        Map<String, dynamic> extMap = externalCompanies[i];
        OrgModel model = OrgModel.fromJson(extMap);
        model.isExternal = true;
        externalList.add(model);
      }
    }
    dataList.clear();
    if(createList.isNotEmpty){
      dataList.add({'list':createList,'name':titleList[0]});
    }
    if(joinList.isNotEmpty){
      dataList.add({'list':joinList,'name':titleList[1]});
    }
    if(externalList.isNotEmpty){
      dataList.add({'list':externalList,'name':titleList[2]});
    }

    companyCount.value = createList.length
        + joinList.length
        + externalList.length;
    dataList.refresh();
  }
}
