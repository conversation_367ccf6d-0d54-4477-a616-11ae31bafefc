import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';
import 'package:sticky_headers/sticky_headers/widget.dart';

import '../../../../../common/widgets/widgets.dart';
import '../../orgWidget/org_widget.dart';
import '../controllers/org_list_controller.dart';

class OrgListView extends GetView<OrgListController> {
  const OrgListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context, '其他企业(${controller.companyCount.value})', false, [],
              onPressed: () {
            Get.back();
          },backgroundColor: ColorConfig.backgroundColor),
          body: ListView.builder(
              itemCount: controller.dataList.length,
              itemBuilder: (context, index) {
                Map infoDic = controller.dataList[index];
                List orgList = infoDic['list'];
                String name = infoDic['name'];
                return StickyHeader(
                  header: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 15, vertical: 6),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      name,
                      style: const TextStyle(
                          color: ColorConfig.mainTextColor, fontSize: 13),
                    ),
                  ),
                  content: index == 2
                      ? Container(
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                              color: ColorConfig.whiteColor,
                              borderRadius: BorderRadius.circular(8)),
                          child: Column(
                            children: backExternalOrgWidget(orgList),
                          ),
                        )
                      : Column(
                          children: backOrgWidget(orgList),
                        ),
                );
              }),
        ));
  }

  backExternalOrgWidget(List orgList) {
    List<Widget> lists = [];
    for (var i = 0; i < orgList.length; i++) {
      OrgModel model = orgList[i];
      lists.add(InkWell(
        onTap: () {
          Get.toNamed(Routes.APPROVE_LIST,
              arguments: {'orgId': model.companyId, 'model': model, 'type': 2},
              preventDuplicates: false);
        },
        child: backCell(model.logo, model.name),
      ));
    }
    return lists;
  }

  Widget backCell(String headImageName, String name) {
    double viewH = 56;
    double imageW = 32;
    return Container(
      width: double.infinity,
      height: viewH,
      padding: const EdgeInsets.only(left: 12, right: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.center,
            width: imageW,
            height: imageW,
            child: ImageLoader(
              width: imageW,
              height: imageW,
              url: headImageName,
              radius: 8,
            ),
          ),
          const SizedBox(
            width: 16,
          ),
          Expanded(
              child: Container(
            child: Text(
              name,
              style: const TextStyle(
                  fontSize: 16, color: ColorConfig.mainTextColor),
            ),
          )),
          const SizedBox(
            width: 8,
          ),
          SizedBox(
            width: 20,
            height: 20,
            child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
          )
        ],
      ),
    );
  }

  backOrgWidget(List orgList) {
    List<Widget> lists = [];
    for (var i = 0; i < orgList.length; i++) {
      OrgModel model = orgList[i];
      int type = 0;
      if (model.rejectInvitation == 0) {
        type = 1;
      }
      if (model.deptId == '0' ||
          (model.power ?? '').contains('-1') ||
          (model.power ?? '').contains('1')) {
        type = 2;
      }
      lists.add(Column(
        children: [
          Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
                color: ColorConfig.whiteColor,
                borderRadius: BorderRadius.circular(8)),
            child: OrgWidget().backOrgCard(model.logo, model.name, type,
                onNamePressed: () {
              if (type == 2) {
                Get.toNamed('/org-more-setting', arguments: {'model': model});
              } else {
                Get.toNamed('/org-detail-setting', arguments: {'model': model});
              }
            }, onButtonPressed: () {
              //邀请按钮
              if (type == 1) {
                Get.toNamed('/invite-home',
                    arguments: {'model': model, 'type': 0});
              }

              //管理按钮
              if (type == 2) {
                Get.toNamed('/org-more-setting', arguments: {'model': model});
              }
            }, onOrgPressed: () {
              Get.toNamed('/dept-detail',
                  arguments: {
                    'topModel': OrgModel.fromJson(model.toJson()),
                    'levelList': [model.name],
                    'model': OrgModel.fromJson(model.toJson()),
                    'deptId': model.deptId,
                    'isManager': false,
                    'companyId': model.companyId
                  },
                  preventDuplicates: false);
            }, onOutPressed: () {
              Get.toNamed('/external-contacts',
                  arguments: {'model': model, 'chooseType': 0});
            }),
          ),
          const SizedBox(
            height: 10,
          )
        ],
      ));
    }
    return lists;
  }
}
