import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/preference_setting_controller.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';

class PreferenceSettingView extends GetView<PreferenceSettingController> {
  const PreferenceSettingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        backgroundColor: ColorConfig.backgroundColor,
        extendBodyBehindAppBar: true,
        appBar:
            TitleBar().backAppbar(context, '偏好设置', false, [], onPressed: () {
          Get.back();
        }),
        body: ListView(
          children: [
            SizedBox(
              height: 10,
            ),
            Container(
              width: double.infinity,
              height: 44,
              padding: EdgeInsets.only(left: 15, right: 15),
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      child: Text(
                    '企业是否允许申请加入',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  )),
                  Container(
                      width: 80,
                      height: 44,
                      alignment: Alignment.centerRight,
                      child: CupertinoSwitch(
                            activeColor: ColorConfig.themeCorlor,
                            trackColor: ColorConfig.lineColor,
                            value: controller.joinSwitch.value,
                            onChanged: (value) {
                              controller.joinSwitch.value = value;
                              controller.settingOrgDetail({'rejectJoin':value?0:1});
                            }))
                ],
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              width: double.infinity,
              height: 20,
              padding: EdgeInsets.only(left: 15, right: 15),
              child: Text(
                '关闭后，企业外的用户无法申请加入本企业',
                style: TextStyle(color: ColorConfig.desTextColor, fontSize: 12),
              ),
            ),
            Container(
              width: double.infinity,
              height: 44,
              padding: EdgeInsets.only(left: 15, right: 15),
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      child: Text(
                    '是否允许普通员工邀请加入企业',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  )),
                  Container(
                      width: 80,
                      height: 44,
                      alignment: Alignment.centerRight,
                      child:  CupertinoSwitch(
                            activeColor: ColorConfig.themeCorlor,
                            trackColor: ColorConfig.lineColor,
                            value: controller.inviteSwitch.value,
                            onChanged: (value) {
                              controller.inviteSwitch.value = value;
                              controller.settingOrgDetail({'rejectInvitation':value?0:1});
                            }))
                ],
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              width: double.infinity,
              height: 20,
              padding: EdgeInsets.only(left: 15, right: 15),
              child: Text(
                '关闭后，普通员工邀请功能将不可用',
                style: TextStyle(color: ColorConfig.desTextColor, fontSize: 12),
              ),
            )
          ],
        )));
  }
}
