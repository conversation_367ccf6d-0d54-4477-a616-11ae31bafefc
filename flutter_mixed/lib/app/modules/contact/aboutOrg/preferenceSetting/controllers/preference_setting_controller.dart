import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../home/<USER>/home_controller.dart';
import '../../../model/org/org_model.dart';

class PreferenceSettingController extends GetxController {

  late OrgModel model;
  RxBool joinSwitch = true.obs;
  RxBool inviteSwitch = true.obs;
  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    joinSwitch.value = model.rejectJoin == 0 ? true : false;
    inviteSwitch.value = model.rejectInvitation == 0 ? true : false;
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  settingOrgDetail(Map result) {
    Map param = {'companyId': model.companyId};
    param.addAll(result);
    DioUtil().put(ORGApi.CHANGEORGDETAIL, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('修改成功');

        if (result['rejectInvitation'] != null) {
          model.rejectInvitation = result['rejectInvitation'];
        }
        if (result['rejectJoin'] != null) {
          model.rejectInvitation = result['rejectJoin'];
        }

        joinSwitch.refresh();

        HomeController homeController = Get.find();
        homeController.getAllCompanies();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
