import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

import 'package:get/get.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/setting_org_scale_controller.dart';

class SettingOrgScaleView extends GetView<SettingOrgScaleController> {
  const SettingOrgScaleView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: ColorConfig.backgroundColor,
          appBar:
              TitleBar().backAppbar(context, controller.type==0?'企业人数':'企业类型', false, [], onPressed: () {
            Get.back();
          }),
          body: SizedBox(
              width: double.infinity,
              child: ListView.builder(
                itemBuilder: (context, index) {
                  return InkWell(
                    onTap: () {
                      if (controller.type == 0) {
                    
                        Get.back(result: {'scale': controller.dataList[index]});
                      }
                      if (controller.type == 1) {
                    
                        Get.back(result: {'type': controller.dataList[index]});
                      }
                    },
                    child: SettingWidget().backSettingWidget(
                        '', '', controller.dataList[index], '', false, 50),
                  );
                },
                itemCount: controller.dataList.length,
              )),
        ));
  }
}
