import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';

class SettingOrgScaleController extends GetxController {

  RxList dataList = [].obs;
  int type = 0;
  late OrgModel model;
  @override
  void onInit() {
    super.onInit();
    type = Get.arguments['type'];
    if (type == 0) {
      dataList.value = [
        '1-50人',
        '51-200人',
        '201-500人',
        '501-1000人',
        '1001人及以上'
      ];
    }
  }

  @override
  void onReady() {
    super.onReady();
    if (type == 1) {
      getOrgType();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  getOrgType() async {
    DioUtil().get(ORGApi.GETCOMPANYTYPE, null, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        List typeList = data['data'];
        for (var i = 0; i < typeList.length; i++) {
          Map typeDic = typeList[i];
          dataList.add(typeDic['name']);
        }
        dataList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
