import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../model/org/external_model.dart';
import '../controllers/external_contacts_controller.dart';

class ExternalContactsView extends GetView<ExternalContactsController> {
  const ExternalContactsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.whiteColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context,
              '外部联系人',
              false,
              controller.chooseType == 0
                  ? controller.model.deptId == '0' ||
                          (controller.model.power ?? '').contains('-1')
                      ? [
                          Container(
                            width: 36,
                            height: 20,
                            child: CupertinoButton(
                                padding: EdgeInsets.fromLTRB(16, 0, 0, 0),
                                onPressed: () {
                                  SettingWidget()
                                      .showCupertinoActionSheetForPage(
                                          context, controller.levelList,
                                          (value) {
                                    controller.currentLevel.value = value;
                                  });
                                },
                                child: Image.asset(
                                    "assets/images/3.0x/external_screen.png")),
                          ),
                          Container(
                            width: 52,
                            height: 20,
                            child: CupertinoButton(
                                padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
                                onPressed: () async {
                                  var result = await Get.toNamed(
                                      '/external-add',
                                      arguments: {
                                        'model': controller.model,
                                        'type': 0
                                      });
                                  controller.getExternalList(isShow: false);
                                },
                                child: Image.asset(
                                    "assets/images/3.0x/external_add.png")),
                          )
                        ]
                      : [
                          Container(
                            width: 52,
                            height: 20,
                            child: CupertinoButton(
                                padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
                                onPressed: () {
                                  SettingWidget()
                                      .showCupertinoActionSheetForPage(
                                          context, controller.levelList,
                                          (value) {
                                    controller.currentLevel.value = value;
                                  });
                                },
                                child: Image.asset(
                                    "assets/images/3.0x/external_screen.png")),
                          ),
                        ]
                  : [
                      Container(
                        width: 60,
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            pressedOpacity: 0.5,
                            child: const Text(
                              '确定',
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            ),
                            onPressed: () {
                              Get.back(result: {
                                'list': controller.selectList.value
                              });
                            }),
                      )
                    ], onPressed: () {
            Get.back();
          }),
          body: ListView(
            physics: NeverScrollableScrollPhysics(),
            children: [
              Container(
                width: double.infinity,
                color: ColorConfig.whiteColor,
                height: 50,
                padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                child: SettingWidget().backSearchWidget(
                    controller.searchController, controller.node, '搜索',
                    onSub: (value) {}, onTap: () async {
                  controller.node.unfocus();
                  var result = await Get.toNamed(Routes.SEARCH_ORG, arguments: {
                    'model': controller.model,
                    'type': 3,
                    'chooseType': controller.chooseType,
                    'selectList': controller.selectList
                  },preventDuplicates: false);
                  Future.delayed(Duration(milliseconds: 300)).then((value) {
                    controller.dataList.refresh();
                  });
                }),
              ),
              DefaultTabController(
                  length: controller.tabList.length,
                  child: Column(
                    children: [
                      Container(
                          color: ColorConfig.whiteColor,
                          height: 48,
                          child: TabBar(
                            indicatorColor: ColorConfig.themeCorlor,
                            indicatorWeight: 1,
                            dividerHeight: 0,
                            tabAlignment: TabAlignment.start,
                            tabs: backTabbar(),
                            isScrollable: true,
                          )),
                      controller.dataList.isEmpty
                          ? Container()
                          : Container(
                              width: double.infinity,
                              height: DeviceUtils().height.value -
                                  48 -
                                  50 -
                                  44 -
                                  DeviceUtils().top.value -
                                  DeviceUtils().bottom.value,
                              child: TabBarView(children: getWidgetList()))
                    ],
                  ))
            ],
          ),
        ));
  }

  List<Widget> backTabbar() {
    List<Widget> list = [];
    for (var i = 0; i < controller.tabList.length; i++) {
      Widget tabText = Text(controller.tabList[i],
          style: TextStyle(color: ColorConfig.mainTextColor, fontSize: 14));
      list.add(tabText);
    }
    return list;
  }

  List<Widget> getWidgetList() {
    List<Widget> list = [];
    for (var i = 0; i < controller.tabList.length; i++) {
      Widget listWidget = getWidgetForExternalContacts(i);
      list.add(listWidget);
    }
    return list;
  }

  getWidgetForExternalContacts(int type) {
    List contentList = controller.dataList[type];
    List typeList = [];
    if (controller.currentLevel.value == 0) {
      typeList = contentList;
    } else {
      for (var i = 0; i < contentList.length; i++) {
        ExternalModel externalModel = contentList[i];
        if (externalModel.level == controller.currentLevel.value) {
          typeList.add(externalModel);
        }
      }
    }

    return typeList.isEmpty
        ? Column(
            children: [
              SizedBox(
                height: 90,
              ),
              Container(
                child: Text(
                  controller.currentLevel.value == 0
                      ? '暂无添加的外部联系人'
                      : '未找到符合条件的外部联系人',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.desTextColor),
                ),
              ),
              SizedBox(
                height: 15,
              ),
              Offstage(
                offstage: (controller.model.deptId != '0' &&
                        !(controller.model.power ?? '').contains('-1')) ||
                    controller.chooseType > 0 ||
                    controller.currentLevel.value > 0,
                child: InkWell(
                    onTap: () async {
                      var result = await Get.toNamed('/external-add',
                          arguments: {'model': controller.model, 'type': 0});
                      controller.getExternalList(isShow: false);
                    },
                    child: Container(
                      width: 108,
                      height: 38,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                              width: 1, color: ColorConfig.themeCorlor)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            child:
                                Image.asset('assets/images/3.0x/blue_add.png'),
                          ),
                          SizedBox(
                            width: 3,
                          ),
                          Container(
                            child: Text(
                              '点击添加',
                              style: TextStyle(
                                  fontSize: 14, color: ColorConfig.themeCorlor),
                            ),
                          )
                        ],
                      ),
                    )),
              )
            ],
          )
        : ListView.builder(
            itemCount: typeList.length,
            itemBuilder: (context, index) {
              ExternalModel externalModel = typeList[index];
              return InkWell(
                onTap: () {
                  if (controller.chooseType == 0) {
                    Get.toNamed('/external-detail', arguments: {
                      'model': controller.model,
                      'userId': externalModel.userId
                    });
                  } else {
                    ExternalModel? tempModel;
                    for (var i = 0; i < controller.selectList.length; i++) {
                      ExternalModel model = controller.selectList[i];
                      if (externalModel.userId == model.userId) {
                        tempModel = model;
                      }
                    }
                    if (tempModel == null) {
                      if (controller.chooseType == 1) {
                        controller.selectList.clear();
                      }
                      controller.selectList.add(externalModel);
                    } else {
                      controller.selectList.remove(tempModel);
                    }
                    controller.dataList.refresh();
                  }
                },
                child: getExternalListWidget(externalModel),
              );
            });
  }

  getExternalListWidget(ExternalModel model) {
    bool isHave = false;
    for (var i = 0; i < controller.selectList.length; i++) {
      ExternalModel externalModel = controller.selectList[i];
      if (externalModel.userId == model.userId) {
        isHave = true;
      }
    }

    return Container(
      width: double.infinity,
      height: 58,
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.only(left: 15, right: 15, top: 9, bottom: 9),
      child: Row(
        children: [
          Offstage(
            offstage: controller.chooseType == 0,
            child: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: Image.asset(isHave
                      ? 'assets/images/3.0x/login_selected.png'
                      : 'assets/images/3.0x/login_unselect.png'),
                ),
                SizedBox(
                  width: 8,
                )
              ],
            ),
          ),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: SettingWidget.backImageProvider(model.avatar)),
                borderRadius: BorderRadius.circular(20)),
          ),
          SizedBox(
            width: 10,
          ),
          Expanded(
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      constraints: BoxConstraints(
                          maxWidth: DeviceUtils().width.value - 154),
                      height: 22,
                      child: Text(
                        model.name,
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      ),
                    ),
                    SizedBox(
                      width: 8,
                    ),
                    Container(
                      alignment: Alignment.center,
                      width: model.level == 1 ? 64 : 40,
                      height: 22,
                      decoration: BoxDecoration(
                          border: Border.all(
                              width: 0.5,
                              color: controller.levelColorList[model.level]),
                          borderRadius: BorderRadius.circular(3)),
                      child: Text(
                        controller.levelList[model.level],
                        style: TextStyle(
                            fontSize: 12,
                            color: controller.levelColorList[model.level]),
                      ),
                    )
                  ],
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    controller.tabList[model.type],
                    style: TextStyle(
                        fontSize: 12, color: ColorConfig.desTextColor),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
