import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/external_model.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/event/event.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';

class ExternalContactsController extends GetxController {

  TextEditingController searchController = TextEditingController();
  FocusNode node = FocusNode();
  late StreamSubscription subscription;
  List tabList = ['全部', '客户', '渠道商', '供应商', '合作伙伴', '其他'];
  List levelList = ['全部','非常重要', '重要', '普通'];
  List levelColorList = [
    ColorConfig.externalLevelOne,
    ColorConfig.externalLevelOne,
    ColorConfig.themeCorlor,
    ColorConfig.externalLevelNormal
  ];
  late OrgModel model;
  RxList dataList = [].obs;

  RxInt currentLevel = 0.obs;

  int chooseType = 0;//1单选2多选

  bool isHaveAuth = false;//是否有组织权限
  RxList selectList = [].obs;
  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    chooseType = Get.arguments['chooseType'];
    if(chooseType > 0){
      selectList.value = Get.arguments['selectList'];
    }
  }

  @override
  void onReady() {
    super.onReady();
      subscription = eventBus.on<ExternalRefresh>().listen((event) {
        getExternalList(isShow: false);
    });
    getExternalList();
  }

  @override
  void onClose() {
    searchController.dispose();
    subscription.cancel();
    super.onClose();
  }

  //获取外部联系人列表
  getExternalList({bool isShow = true}) async {
    DioUtil().get(
        '${ORGApi.EXTERNAL_CONTACT_LIST}/${model.companyId}', null, true, () {
    },isShowLoading: isShow).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
 
        List externalList = data['data'];
        List typeList = [];
        for (var i = 0; i < tabList.length; i++) {
          typeList.add([]);
        }

        for (var i = 0; i < externalList.length; i++) {
          ExternalModel externalModel =  ExternalModel.fromJson(externalList[i]);
          int externalType = externalModel.type;
          List list = typeList[externalType];
          list.add(externalModel);
          List allList = typeList[0];
          allList.add(externalModel);
        }
        
        dataList.value = typeList;
        dataList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
