import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/add_dept_controller.dart';

class AddDeptView extends GetView<AddDeptController> {
  const AddDeptView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(context, '添加子部门', false, [
            
            // Container(
            //   width: 100,
            //   child: CupertinoButton(
            //       padding: const EdgeInsets.fromLTRB(60, 12, 20, 12),
            //       pressedOpacity: 0.5,
            //       child: Image.asset(
            //         'assets/images/3.0x/contact_dept_close.png',
            //       ),
            //       onPressed: () {
            //         Get.until((route) {
            //           var argument = route.settings.arguments;
            //           if (route.settings.name == Routes.DEPT_DETAIL) {
            //             if (argument is Map) {
            //               if (argument['isManager'] is bool) {
            //                 if (!argument['isManager']) {
            //                   return true;
            //                 }
            //               }
            //             }
            //           }
            //           return false;
            //         });
            //       }),
            // )
          ], onPressed: () {
            Get.back();
          }),
          body: Column(
            children: [
              Expanded(
                  child: ListView(
                children: [
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    color: ColorConfig.whiteColor,
                    width: DeviceUtils().width.value - 30,
                    height: 55,
                    padding: EdgeInsets.only(left: 15, right: 15),
                    child: Column(
                      children: [
                        Container(
                          height: 54,
                          child: Row(
                            children: [
                              Text(
                                '部门名称',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.mainTextColor),
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Expanded(
                                  child: TextField(
                                onChanged: (value) {},
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(10)
                                ],
                                textInputAction: TextInputAction.done,
                                controller: controller.nameController,
                                style: const TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14,
                                ),
                                decoration: InputDecoration(
                                    contentPadding: const EdgeInsets.only(
                                        top: 0, bottom: 0),
                                    border: const OutlineInputBorder(
                                        borderSide: BorderSide.none),
                                    hintText: '请输入部门名称',
                                    hintStyle: const TextStyle(
                                      color: ColorConfig.desTextColor,
                                      fontSize: 14,
                                    )),
                              ))
                            ],
                          ),
                        ),
                        Divider(
                          height: 1,
                          color: ColorConfig.backgroundColor,
                        )
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      Get.toNamed('/dept-detail',
                            arguments: {
                              'topModel':controller.topModel,
                              'levelList': [controller.model.name],
                              'deptId': controller.model.deptId,
                              'model': controller.model,
                              'isManager': true,
                              'type': 2,
                              'companyId':controller.companyId
                            },
                            preventDuplicates: false);
                    },
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: DeviceUtils().width.value - 30,
                      height: 55,
                      padding: EdgeInsets.only(left: 15, right: 15),
                      child: Column(
                        children: [
                          Container(
                            width: DeviceUtils().width.value - 30,
                            height: 54,
                            child: Row(
                              children: [
                                Text(
                                  '上级部门',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Expanded(
                                    child: Text(
                                  controller.topDept.value,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                )),
                                SizedBox(
                                  width: 9,
                                  height: 17,
                                  child: Image.asset(
                                      'assets/images/3.0x/mine_right.png'),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  // Container(
                  //   width: double.infinity,
                  //   height: 54,
                  //   padding: EdgeInsets.only(left: 15, right: 15),
                  //   color: Colors.white,
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       Expanded(
                  //           child: Text(
                  //         '创建部门群',
                  //         textAlign: TextAlign.left,
                  //         style: TextStyle(
                  //             color: ColorConfig.mainTextColor, fontSize: 14),
                  //       )),
                  //       Container(
                  //           width: 80,
                  //           height: 54,
                  //           alignment: Alignment.centerRight,
                  //           child: Obx(
                  //             () => CupertinoSwitch(
                  //                 activeColor: ColorConfig.themeCorlor,
                  //                 trackColor: ColorConfig.lineColor,
                  //                 value: controller.isCreateGroup.value,
                  //                 onChanged: (value) {
                  //                   controller.isCreateGroup.value = value;
                  //                 }),
                  //           ))
                  //     ],
                  //   ),
                  // ),
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.only(left: 15, top: 5),
                    height: 30,
                    child: Text(
                      '添加类型',
                      style: TextStyle(
                          fontSize: 12, color: ColorConfig.desTextColor),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      controller.isDept.value = true;
                    },
                    child: SettingWidget().backSettingWidget(
                        controller.isDept.value
                            ? 'assets/images/3.0x/contact_dept_selected.png'
                            : 'assets/images/3.0x/login_unselect.png',
                        '',
                        '部门',
                        '',
                        false,
                        54),
                  ),
                  InkWell(
                    onTap: () {
                      controller.isDept.value = false;
                    },
                    child: SettingWidget().backSettingWidget(
                        controller.isDept.value
                            ? 'assets/images/3.0x/login_unselect.png'
                            : 'assets/images/3.0x/contact_dept_selected.png',
                        '',
                        '子公司',
                        '',
                        false,
                        54),
                  )
                ],
              )),
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    left: 15,
                    right: 15,
                    top: 16,
                    bottom: 16 + DeviceUtils().bottom.value),
                height: DeviceUtils().bottom.value + 76,
                child: CupertinoButton(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    color: ColorConfig.themeCorlor,
                    borderRadius: const BorderRadius.all(Radius.circular(4)),
                    pressedOpacity: 0.5,
                    child: Text(
                      '完成',
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    onPressed: () async {
                      if(controller.isDept.value){
                        controller.addChildDept();
                      }else{
                        controller.createOrg();
                      }
                    }),
              )
            ],
          ),
        ));
  }
}
