import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/event/event.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../home/<USER>/home_controller.dart';

class AddDeptController extends GetxController {

  TextEditingController nameController = TextEditingController();
  String companyId = '';
  RxString topDept = ''.obs;
  RxBool isCreateGroup = true.obs;
  RxBool isDept = true.obs; //是否是部门创建

  late OrgModel model;
  String deptId = '';

   OrgModel? topModel;//总公司数据
  @override
  void onInit() {
    super.onInit();
    topDept.value = Get.arguments['deptName'];
    model = Get.arguments['model'];
    deptId = Get.arguments['deptId'];
    companyId = Get.arguments['companyId'];
    topModel = Get.arguments['topModel'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    nameController.dispose();
    super.onClose();
  }

//创建子部门
  addChildDept() async {
    if (nameController.text.isEmpty) {
      toast('请输入子部门名称');
      return;
    }
    if (deptId == '1') {
      deptId = '0';
    }
    Map param = {
      'name': nameController.text,
      'orgId': model.companyId,
      'parentDeptId': deptId,
      'setUpStatus': isDept.value ? 1 : 0
    };
    DioUtil().post(ORGApi.DEPTADD, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('创建成功');
        OrgModel deptModel = OrgModel(model.companyId);
        deptModel.deptId = deptId;
        eventBus.fire(DeptRefresh(deptModel));
        Get.back();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //创建子公司
  createOrg() async {
    if (nameController.text.isEmpty) {
      toast('请输入子公司名称');
      return;
    }
    if (deptId == '1') {
      deptId = '0';
    }
    Map dict = {
      'name': nameController.text,
      'parentId': deptId,
      'orgId': model.companyId,
      'source': 1
    };

    DioUtil().post(ORGApi.CREATECOMPANY, dict, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('创建成功');
        OrgModel deptModel = OrgModel(model.companyId);
        deptModel.deptId = deptId;
        eventBus.fire(DeptRefresh(deptModel));
        Get.back();
        HomeController homeController = Get.find();
        homeController.getAllCompanies();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
