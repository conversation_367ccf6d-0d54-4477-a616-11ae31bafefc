import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/setting_org_industry_controller.dart';

class SettingOrgIndustryView extends GetView<SettingOrgIndustryController> {
  const SettingOrgIndustryView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: ColorConfig.backgroundColor,
      appBar: TitleBar().backAppbar(context, '所属行业', false, [
        Container(
          width: 60,
          child: CupertinoButton(
              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
              pressedOpacity: 0.5,
              child: const Text(
                '保存',
                style: TextStyle(color: ColorConfig.themeCorlor, fontSize: 14),
              ),
              onPressed: () {
            
                Get.back(result: {'industry':controller.industryStr.value});
              }),
        )
      ], onPressed: () {
        Get.back();
      }),
      body: Container(
        padding: EdgeInsets.only(left: 15, right: 15),
        child: Row(
          children: [
            Container(
              width: DeviceUtils().width.value / 2.0 - 30 - 15,
              child: ListView.builder(
                  itemCount: controller.dataList.length,
                  itemBuilder: (context, index) {
                    Map dict = controller.dataList[index];
                    return InkWell(
                      onTap: () {
                        controller.faIndex.value = index;
                        controller.sonIndex.value = 0;
                        controller.sonList.value = dict['son'];
                        Map sonDic = controller.sonList[0];
                        controller.industryStr.value = sonDic['name'];
                      },
                      child: Container(
                        alignment: Alignment.center,
                          height: 40,
                          child: Text(
                            dict['name'],
                            style: TextStyle(
                                fontSize: 14,
                                color: controller.faIndex.value == index
                                    ? ColorConfig.themeCorlor
                                    : ColorConfig.mainTextColor),
                          )),
                    );
                  }),
            ),
            Expanded(
                child: Container(
              // width: DeviceUtils().width.value / 2.0 - 30 - 15,
              child: ListView.builder(
                  itemCount: controller.sonList.length,
                  itemBuilder: (context, index) {
                    Map sonDic = controller.sonList[index];
                    print('re------$index-----$sonDic');
                    return InkWell(
                      onTap: () {
                        print('ontap------$index-----$sonDic');
                        controller.sonIndex.value = index;
                        controller.industryStr.value = sonDic['name'];
                      },
                      child: Obx(() => Container(
                        alignment: Alignment.center,
                          height: 40,
                          child: Text(
                            sonDic['name'],
                            style: TextStyle(
                                fontSize: 14,
                                color: controller.sonIndex.value == index
                                    ? ColorConfig.themeCorlor
                                    : ColorConfig.mainTextColor),
                          )),
                    ));
                  }),
            ))
          ],
        ),
      ),
    ));
  }
}
