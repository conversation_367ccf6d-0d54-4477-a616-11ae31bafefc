import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';

class SettingOrgIndustryController extends GetxController {

  RxList dataList = [].obs;
  RxList sonList = [].obs;
  RxString industryStr = ''.obs;
  RxInt faIndex = 0.obs;
  RxInt sonIndex = 0.obs;


  @override
  void onInit() {
    super.onInit();

  }

  @override
  void onReady() {
    super.onReady();
    getOrgIndusty();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getOrgIndusty() async {
    DioUtil().get(ORGApi.GETCOMPANYINDUSTRY, null, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        dataList.value = data['data'];
        Map currentDic = dataList[0];
        sonList.value = currentDic['son'];
        Map sonDic = sonList[0];
        industryStr.value = sonDic['name'];
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
