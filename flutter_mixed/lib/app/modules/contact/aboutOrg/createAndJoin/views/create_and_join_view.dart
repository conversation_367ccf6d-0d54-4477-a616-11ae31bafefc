import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/utils/storage.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../permission/permission_util.dart';
import '../../../../../routes/app_pages.dart';
import '../../orgWidget/org_widget.dart';
import '../controllers/create_and_join_controller.dart';

class CreateAndJoinView extends GetView<CreateAndJoinController> {
  const CreateAndJoinView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: ColorConfig.backgroundColor,
      appBar: TitleBar().backAppbar(context, '创建和添加', false, [], onPressed: () {
        Get.back();
      }),
      body: ListView(
        children: [
          Container(
            alignment: Alignment.center,
            height: 20,
            padding: EdgeInsets.only(left: 15, right: 15),
            child: Text(
              '企业',
              style: TextStyle(fontSize: 12, color: ColorConfig.desTextColor),
            ),
          ),
          InkWell(
            onTap: () {
              Get.toNamed('/org-create');
            },
            child: OrgWidget().backCreateAndJoinCell(
                'assets/images/3.0x/contact_create_org.png', '创建企业'),
          ),
          Divider(
            height: 1,
            color: ColorConfig.backgroundColor,
          ),
          InkWell(
            onTap: () {
              RouteHelper.route(Routes.SEARCH_ORG, arguments: {'type': 0});
            },
            child: OrgWidget().backCreateAndJoinCell(
                'assets/images/3.0x/contact_join_org.png', '加入企业'),
          ),
          Container(
            height: 20,
            padding: EdgeInsets.only(left: 15, right: 15),
            child: Text(
              '添加好友',
              style: TextStyle(fontSize: 12, color: ColorConfig.desTextColor),
            ),
          ),
          InkWell(
            onTap: () {
              RouteHelper.route(Routes.SEARCH_ORG, arguments: {'type': 2});
            },
            child: OrgWidget().backCreateAndJoinCell(
                'assets/images/3.0x/contact_add_search.png', '搜索添加好友'),
          ),
          InkWell(
            onTap: () async {
              var r = await PermissionUtil.checkCameraPermission(Get.context!,
                  tip: scanPermissionTip);
              if (!r) return;
              Get.toNamed(Routes.QR_CODE,arguments: {'type':1});
            },
            child: OrgWidget().backCreateAndJoinCell(
                'assets/images/3.0x/contact_add_qrcode.png', '扫一扫'),
          ),
          InkWell(
            onTap: () {
              Get.toNamed('/contact-add-friend', arguments: {'type': 0});
            },
            child: OrgWidget().backCreateAndJoinCell(
                'assets/images/3.0x/contact_add_tel.png', '通过手机通讯录添加'),
          ),
          SizedBox(
            height: 20,
          ),
          InkWell(
              onTap: () async {
                Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
                UserModel userModel = UserModel(tokenInfo['userId']);
                userModel.avatar = tokenInfo['avatar'];
                userModel.name = tokenInfo['name'];
                Get.toNamed('/invite-home',
                    arguments: {'userModel': userModel, 'type': 1});
              },
              child: Container(
                  alignment: Alignment.center,
                  color: ColorConfig.whiteColor,
                  width: double.infinity,
                  height: 40,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '我的二维码 ',
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      ),
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: Image.asset(
                            'assets/images/3.0x/contact_myQrCode.png'),
                      )
                    ],
                  )))
        ],
      ),
    );
  }
}
