import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../controllers/edit_dept_controller.dart';

class EditDeptView extends GetView<EditDeptController> {
  const EditDeptView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(context, '编辑子部门', false, [
            // Container(
            //   width: 100,
            //   child: CupertinoButton(
            //       padding: const EdgeInsets.fromLTRB(60, 12, 20, 12),
            //       pressedOpacity: 0.5,
            //       child: Image.asset(
            //         'assets/images/3.0x/contact_dept_close.png',
            //       ),
            //       onPressed: () {
            //         Get.until((route) {
            //           Map argument = route.settings.arguments as Map;
            //           if (!argument['isManager']) {
            //             return true;
            //           }
            //           return false;
            //         });
            //       }),
            // )
          ], onPressed: () {
            Get.back();
          }),
          body: Column(
            children: [
              Expanded(
                  child: ListView(
                children: [
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    color: ColorConfig.whiteColor,
                    width: DeviceUtils().width.value - 30,
                    height: 55,
                    padding: EdgeInsets.only(left: 15, right: 15),
                    child: Column(
                      children: [
                        Container(
                          height: 54,
                          child: Row(
                            children: [
                              Text(
                                '部门名称',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.mainTextColor),
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Expanded(
                                  child: TextField(
                                onChanged: (value) {},
                                onTap: () {
                                  if (controller.deptId == '0' ||
                                      controller.deptId == '1') {
                                    toast('当前部门不可以修改名称');
                                    controller.node.unfocus();
                                  }
                                },
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(10)
                                ],
                                focusNode: controller.node,
                                textInputAction: TextInputAction.done,
                                controller: controller.nameController,
                                style: const TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14,
                                ),
                                decoration: InputDecoration(
                                    contentPadding: const EdgeInsets.only(
                                        top: 0, bottom: 0),
                                    border: const OutlineInputBorder(
                                        borderSide: BorderSide.none),
                                    hintText: '请输入部门名称',
                                    hintStyle: const TextStyle(
                                      color: ColorConfig.desTextColor,
                                      fontSize: 14,
                                    )),
                              ))
                            ],
                          ),
                        ),
                        Divider(
                          height: 1,
                          color: ColorConfig.backgroundColor,
                        )
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      if (controller.topName.value.isEmpty) {
                        toast('当前部门不能移动到其他部门');
                        return;
                      }

                      Get.toNamed('/dept-detail',
                          arguments: {
                            'topModel':controller.topModel,
                            'levelList': [controller.model.name],
                            'deptId': '0',
                            'model': controller.model,
                            'isManager': true,
                            'type': 3,
                            'companyId': controller.companyId,
                          },
                          preventDuplicates: false);
                    },
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: DeviceUtils().width.value - 30,
                      height: 55,
                      padding: EdgeInsets.only(left: 15, right: 15),
                      child: Column(
                        children: [
                          Container(
                            width: DeviceUtils().width.value - 30,
                            height: 54,
                            child: Row(
                              children: [
                                Text(
                                  '上级部门',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Expanded(
                                    child: Text(
                                  controller.topName.value,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                )),
                                SizedBox(
                                  width: 9,
                                  height: 17,
                                  child: Image.asset(
                                      'assets/images/3.0x/mine_right.png'),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  InkWell(
                    onTap: () async {
                      controller.didClickChooseTopDept();
                    },
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: DeviceUtils().width.value - 30,
                      height: 55,
                      padding: EdgeInsets.only(left: 15, right: 15),
                      child: Column(
                        children: [
                          Container(
                            width: DeviceUtils().width.value - 30,
                            height: 54,
                            child: Row(
                              children: [
                                Text(
                                  '部门负责人',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Expanded(
                                    child: Text(
                                  controller.managerName.value,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                )),
                                SizedBox(
                                  width: 9,
                                  height: 17,
                                  child: Image.asset(
                                      'assets/images/3.0x/mine_right.png'),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Offstage(
                    offstage: controller.isHiddenBtn.value,
                    child: InkWell(
                      onTap: () {
                        if (controller.topId.isEmpty) {
                          controller.deleteCompany();
                        } else {
                          controller.deleteDept();
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        width: double.infinity,
                        height: 54,
                        color: ColorConfig.whiteColor,
                        child: Text(
                          '删除部门',
                          style: TextStyle(
                              fontSize: 16, color: ColorConfig.deleteCorlor),
                        ),
                      ),
                    ),
                  )
                ],
              )),
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    left: 15,
                    right: 15,
                    top: 16,
                    bottom: 16 + DeviceUtils().bottom.value),
                height: DeviceUtils().bottom.value + 76,
                child: CupertinoButton(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    color: ColorConfig.themeCorlor,
                    borderRadius: const BorderRadius.all(Radius.circular(4)),
                    pressedOpacity: 0.5,
                    child: Text(
                      '完成',
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    onPressed: () async {
                      controller.sureEditDept();
                    }),
              )
            ],
          ),
        ));
  }
}
