import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/event/event.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/dept_members_model.dart';
import '../../../model/org/org_model.dart';

class EditDeptController extends GetxController {

  TextEditingController nameController = TextEditingController();
  late OrgModel model; //分公司数据
  String deptId = '';
  RxString deptName = ''.obs;
  RxString topName = ''.obs;
  String topId = '';
  String companyId = '';
  RxString managerName = ''.obs; //负责人名称
  String positionId = '';
  String userId = '';
  String topCompanyId = '';
  RxList memberList = [].obs;
  RxBool isHiddenBtn = true.obs;
  FocusNode node = FocusNode();
  OrgModel? topModel; //总公司数据
  @override
  void onInit() {
    super.onInit();

    model = Get.arguments['model'];
    deptId = Get.arguments['deptId'];
    topId = Get.arguments['topId'];
    topName.value = Get.arguments['topName'];
    companyId = Get.arguments['companyId'];
    topCompanyId = Get.arguments['topCompanyId'];
    topModel = Get.arguments['topModel'];
  }

  @override
  void onReady() {
    super.onReady();
    getDeptDetail();
  }

  @override
  void onClose() {
    super.onClose();
  }

  //获取部门详情
  getDeptDetail() async {
    DioUtil()
        .get('${ORGApi.PERSONOUTORG}/${model.companyId}/dept/$deptId', null,
            true, () {})
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataDic = data['data'];
        deptName.value = dataDic['deptName'];
        nameController.text = deptName.value;
        memberList.value = dataDic['memberList'];
        for (var i = 0; i < memberList.length; i++) {
          Map userDic = memberList[i];
          if (userDic['positionId'] != '0') {
            managerName.value = userDic['name'];
            positionId = userDic['positionId'];
            userId = userDic['userId'];
          }
        }
        Map userInfo = await UserDefault.getData(Define.TOKENKEY);
        if (userInfo['userId'] == dataDic['userId']) {
          //是创建者
          isHiddenBtn.value = false;
          if (companyId == model.companyId &&
              int.parse(model.deptId) < 2 &&
              topId.isEmpty) {
            isHiddenBtn.value = true;
          }
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  didClickChooseTopDept() async {
    var result = await Get.toNamed('/choose-leader',
        arguments: {'memberList': memberList.value});

    if (result != null && result['memberModel'] != null) {
      MemberModel memberModel = result['memberModel'];
      managerName.value = memberModel.name;
      userId = memberModel.userId;
    }
  }

  sureEditDept() async {
    List userIds = [];
    if (userId.isNotEmpty) {
      userIds.add(userId);
    }
    Map param = {
      'deptId': deptId,
      'deptName': nameController.text,
      'orgId': model.companyId,
      'targetDeptId': topId,
      'targetOrgId': topCompanyId,
      'userIds': userIds
    };
    DioUtil().post(ORGApi.EDITDEPT, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('设置成功');
        OrgModel deptModel = OrgModel(model.companyId);
        deptModel.deptId = deptId;
        OrgModel targetModel = OrgModel(topCompanyId);
        targetModel.deptId = topId;
        eventBus.fire(DeptRefresh(deptModel));
        eventBus.fire(DeptRefresh(targetModel));
        Get.back();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  deleteDept() {
    DioUtil()
        .delete('${ORGApi.DELETEDEPT}/${model.companyId}/$deptId', null, true,
            () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('删除成功');
        eventBus.fire({'deleteDept': 1, 'deptId': deptId});
        Get.back(result: {'deleteDept': deptId});
      } else {
        toast('${data['msg']}');
      }
    });
  }

  deleteCompany() {
    Get.toNamed('/dissolve-org', arguments: {'model': model});
  }
}
