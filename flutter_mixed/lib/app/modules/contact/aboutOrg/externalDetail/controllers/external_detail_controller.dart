import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/channel/channel.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../routes/app_pages.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../model/org/external_model.dart';
import '../../../model/org/org_model.dart';

class ExternalDetailController extends GetxController {

  late OrgModel model;
  String userId = '';
  ExternalModel? externalModel;

  List tabList = ['全部', '客户', '渠道商', '供应商', '合作伙伴', '其他'];
  List levelList = ['', '非常重要', '重要', '普通'];
  List levelColorList = [
    ColorConfig.externalLevelOne,
    ColorConfig.externalLevelOne,
    ColorConfig.themeCorlor,
    ColorConfig.externalLevelNormal
  ];

  RxBool isFriend = false.obs;
  Map? chatMap;
  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    userId = Get.arguments['userId'];
  }

  @override
  void onReady() {
    super.onReady();
    judgeIsFriend();
    getExternalDetail();
  }

  @override
  void onClose() {
    super.onClose();
  }

  //判断是否为好友
  judgeIsFriend() async {
    List friendList = await UserDefault.getData(Define.FRIENDLIST);
    if (friendList.isNotEmpty) {
      for (var j = 0; j < friendList.length; j++) {
        Map friendMap = friendList[j];
        if (friendMap['userId'] == userId) {
          isFriend.value = true;
          chatMap = friendMap;
          isFriend.refresh();
        }
      }
    }
  }

  //获取外部联系人详情
  getExternalDetail({bool isShow = true}) async {
    DioUtil().get(
        '${ORGApi.EXTERNAL_CONTACT_DETAIL}/${model.companyId}/$userId',
        null,
        true, () {
    }, isShowLoading: isShow).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        externalModel = ExternalModel.fromJson(data['data']);
        isFriend.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //发送激活邀请
  sendActiveInvite() {
    Map param = {'orgId': model.companyId, 'phone': externalModel!.mobile};
    DioUtil().post(ORGApi.POST_ACTIVE_MESSAGE, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('已向 ${externalModel!.externalContactsName} 发送邀请');
      } else {
        toast('${data['msg']}');
      }
    });
  }

  voliteIsAllow() async {
    //是否允许添加好友

    Map param = {'type': 4, 'userId': userId};
    DioUtil().post(LoginApi.ISALLOWFRIEND, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
         Get.toNamed(Routes.ORG_VERIFY,
            arguments: {'userId': userId, 'type': 4});
      } else {
        if (data['code'] == 1405) {
          toast('添加好友成功');
          isFriend.value = true;
        } else {
          toast('${data['msg']}');
        }
      }
    });
  }

    //发消息
  sendMessage() async{
    if(chatMap == null) return;
    logger('=======chatMap=======$chatMap');
    chatMap!['headimg'] = chatMap!['avatar'];
    var session = await chatMap!.createSinglePageParam();
    RouteHelper.routeTotag(
        ChatPage(tag: session.sessionId), Routes.IM_CHAGE_PAGE,
        arguments: session, binding: ChatBinding(tag: session.sessionId));
  }
}
