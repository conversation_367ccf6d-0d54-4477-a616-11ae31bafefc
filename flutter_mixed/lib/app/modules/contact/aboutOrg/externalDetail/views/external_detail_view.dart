import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

import 'package:get/get.dart';

import '../../../../../common/widgets/widgets.dart';
import '../controllers/external_detail_controller.dart';

class ExternalDetailView extends GetView<ExternalDetailController> {
  const ExternalDetailView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      extendBodyBehindAppBar: true,
      appBar: TitleBar().backAppbar(context, '外部联系人信息', false, [
        Container(
          width: 100,
          child: CupertinoButton(
              padding: const EdgeInsets.fromLTRB(60, 12, 20, 12),
              pressedOpacity: 0.5,
              child: Image.asset('assets/images/3.0x/external_edit.png'),
              onPressed: () async{
                var result = await Get.toNamed('/external-add',
                        arguments: {'model': controller.model,'type':1,'externalModel':controller.externalModel});
                controller.getExternalDetail(isShow: false);
              }),
        )
      ], onPressed: () {
        Get.back();
      }),
      body:controller.externalModel==null?Container(
        child: Text(controller.isFriend.value?'':''),
      ): ListView(
        children: [
          Container(
            width: double.infinity,
            height: 108,
            color: ColorConfig.whiteColor,
            padding: EdgeInsets.only(left: 15, right: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: Column(
                  children: [
                    Container(
                      alignment: Alignment.centerLeft,
                      height: 32,
                      child: Text(
                        controller.externalModel!.externalContactsName,
                        style: TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 16),
                      ),
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      // color: ColorConfig.btnGrayColor,
                      height: 24,

                      child: Text(
                          '类型: ${controller.tabList[controller.externalModel!.type]}',
                          style: TextStyle(
                              color: ColorConfig.desTextColor, fontSize: 14)),
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      //  color: ColorConfig.btnGrayColor,
                      height: 24,

                      child: Row(
                        children: [
                          Text('重要级别:',
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14)),
                          SizedBox(
                            width: 5,
                          ),
                          Container(
                            alignment: Alignment.center,
                            width: 64,
                            height: 20,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(3),
                                border: Border.all(
                                    width: 1, color: ColorConfig.themeCorlor)),
                            child: Text(
                              '${controller.levelList[controller.externalModel!.level]}',
                              style: TextStyle(
                                  fontSize: 12,
                                  color: controller.levelColorList[
                                      controller.externalModel!.level]),
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                )),
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(36),
                      image: DecorationImage(
                          image: SettingWidget.backImageProvider(controller.externalModel?.avatar))),
                )
              ],
            ),
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            alignment: Alignment.centerLeft,
            height: 40,
            color: ColorConfig.whiteColor,
            padding: EdgeInsets.only(left: 15),
            child: Text(
              '基本信息',
              style: TextStyle(fontSize: 16, color: ColorConfig.mainTextColor),
            ),
          ),
          Divider(
            height: 1,
            color: ColorConfig.backgroundColor,
          ),
          backTopAndBottomWidget('姓名', controller.externalModel!.externalContactsName),
          Container(
            height: 4,
            color: ColorConfig.whiteColor,
          ),
          backTopAndBottomWidget('手机号', controller.externalModel!.mobile),
          Container(
            height: 4,
            color: ColorConfig.whiteColor,
          ),
          backTopAndBottomWidget('邮箱', controller.externalModel!.email),
          Container(
            height: 4,
            color: ColorConfig.whiteColor,
          ),
          backTopAndBottomWidget('备注', controller.externalModel!.desc),
          Container(
            width: double.infinity,
            height: 5,
            color: ColorConfig.whiteColor,
          ),
          SizedBox(
            height: 10,
          ),
          Offstage(
            offstage: controller.externalModel!.active == 0,
            child: Container(
              width: double.infinity,
              height: 54,
              padding: EdgeInsets.only(left: 15, right: 15),
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      child: Text(
                    '发送激活邀请给该用户',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 16),
                  )),
                  InkWell(
                    onTap: () {
                      controller.sendActiveInvite();
                    },
                    child: Container(
                        width: 105,
                        height: 54,
                        alignment: Alignment.centerRight,
                        child: Text(
                          '发送激活邀请',
                          style: TextStyle(
                              fontSize: 16, color: ColorConfig.themeCorlor),
                        )),
                  )
                ],
              ),
            ),
          ),
          SizedBox(
            height: 15,
          ),
          InkWell(
            onTap: () {
              BaseInfo().launchTelURL(controller.externalModel!.mobile);
            },
            child: SettingWidget()
                .backFunWidget('assets/images/3.0x/contact_add_tel.png', '呼叫'),
          ),
          const Divider(
              indent: 15, color: ColorConfig.backgroundColor, height: 1),
          InkWell(
            onTap: () {
              if(controller.isFriend.value){
                controller.sendMessage();
              }else{
                controller.voliteIsAllow();
              }
            },
            child: SettingWidget()
                .backFunWidget('assets/images/3.0x/external_add.png',controller.isFriend.value?'发消息': '添加好友'),
          ),
          Container(
            alignment: Alignment.center,
            height: 50,
            child: Text('${BaseInfo().formatTimestamp(controller.externalModel!.updateTime, 'yy年MM月dd日 HH:mm:ss')} 由 ${controller.externalModel!.externalContactsName} 保存',style: TextStyle(fontSize: 14,color: ColorConfig.desTextColor),),
          )

        ],
      ),
    ));
  }

  backTopAndBottomWidget(String top, String bottom) {
    return Container(
      width: double.infinity,
      height: 48,
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.only(left: 15, right: 15),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 24,
            child: Text(
              top,
              style: TextStyle(fontSize: 13, color: ColorConfig.desTextColor),
            ),
          ),
          Container(
            alignment: Alignment.centerLeft,
            height: 24,
            child: Text(
              bottom,
              style: TextStyle(fontSize: 16, color: ColorConfig.mainTextColor),
            ),
          )
        ],
      ),
    );
  }
}
