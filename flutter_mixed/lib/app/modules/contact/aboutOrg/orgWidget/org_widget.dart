import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../../common/config/config.dart';

class OrgWidget {
  //公司卡片
  Widget backOrgCard(String logo, String name, int type,
      {required VoidCallback onNamePressed,
      required VoidCallback onButtonPressed,
      required VoidCallback onOrgPressed,
      required VoidCallback onOutPressed}) {
    //type:0无权限1邀请权限2管理权限
    double buttonW = 40;
    double buttonH = 20;
    double viewH = 56;
    double imageW = 32;
    return Container(
      width: double.infinity,
      height: 144,
      padding: const EdgeInsets.only(left: 15, right: 15),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: viewH,
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: onNamePressed,
                    child: Container(
                      height: viewH,
                      child: Row(
                        children: [
                          Container(
                            width: imageW,
                            height: imageW,
                            decoration: BoxDecoration(
                                image: DecorationImage(
                                    image: SettingWidget.backImageProvider(logo),fit: BoxFit.cover),
                                borderRadius:
                                    BorderRadius.circular(8)),
                          ),
                          8.gap,
                          Expanded(
                              child: Text(
                            name,
                            style: const TextStyle(
                                fontSize: 14, color: ColorConfig.mainTextColor),
                          ))
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                InkWell(
                  onTap: onButtonPressed,
                  child: Container(
                    width: buttonW,
                    height: buttonH,

                    alignment: Alignment.center,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(2) , color: type > 0
                            ? ColorConfig.mybackgroundColor
                            : ColorConfig.whiteColor,),
                    child: Text(
                      type == 1
                          ? '邀请'
                          : type == 2
                              ? '管理'
                              : '',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          fontSize: 12, color: ColorConfig.themeCorlor),
                    ),
                  ),
                )
              ],
            ),
          ),
          InkWell(
            onTap: onOrgPressed,
            child: Container(
              height: 44,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      ImageLoader(
                        url: AssetsRes.CONTACT_ORG_FRAMEWORK,
                        width: imageW,
                        height: 28,
                        radius: 0,
                      ),
                      8.gap,
                      const Text(
                        '组织架构',
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      )
                    ],
                  ),
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
                  )
                ],
              ),
            ),
          ),
          InkWell(
            onTap: onOutPressed,
            child: Container(
              height: 44,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      ImageLoader(
                        url: AssetsRes.CONTACT_ORG_FRAMEWORK,
                        width: imageW,
                        height: 28,
                        radius: 0,
                      ),
                      8.gap,
                      const Text(
                        '外部联系人',
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      )
                    ],
                  ),
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  //创建和加入
  Widget backCreateAndJoinCell(String imageName, String nameStr) {
    return Column(
      children: [
        Container(
          color: Colors.white,
          height: 48,
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(1),
                        width: 20,
                        height: 20,
                        alignment: Alignment.centerLeft,
                        child: Image.asset(imageName),
                      ),
                      const SizedBox(
                        width: 5,
                      )
                    ],
                  ),
                  Obx(() => Container(
                        width: DeviceUtils().width.value - 100,
                        alignment: Alignment.centerLeft,
                        height: 48,
                        child: Text(nameStr,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                color: ColorConfig.mainTextColor,
                                fontSize: 14)),
                      ))
                ],
              ),
              SizedBox(
                width: 20,
                height: 20,
                child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
              )
            ],
          ),
        ),
      ],
    );
  }

  backDeptMemberWidget(String selectImageName, String headImageName,
      String nameStr, String deptStr, String powerName) {
    return Column(
      children: [
        Container(
          width: DeviceUtils().width.value,
          color: ColorConfig.whiteColor,
          height: 58,
          padding: const EdgeInsets.fromLTRB(15, 9, 15, 9),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Offstage(
                    offstage: selectImageName.isEmpty,
                    child: Row(
                      children: [
                        Container(
                          width: 16,
                          height: 16,
                          alignment: Alignment.centerLeft,
                          child: Image.asset(selectImageName),
                        ),
                        const SizedBox(
                          width: 5,
                        )
                      ],
                    ),
                  ),
                  Container(
                    width: 45,
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(1),
                          width: 40,
                          height: 40,
                          alignment: Alignment.centerLeft,
                          decoration: BoxDecoration(
                              image: DecorationImage(image: SettingWidget.backImageProvider(headImageName,cache: true)),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                  color: ColorConfig.lineColor, width: 0.5)),
                        ),
                        const SizedBox(
                          width: 5,
                        )
                      ],
                    ),
                  ),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            //constraints: BoxConstraints(maxWidth: 160),
                            alignment: Alignment.centerLeft,
                            height: 19,
                            child: Text(
                              nameStr,
                              style: const TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.mainTextColor),
                            ),
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Offstage(
                            offstage: powerName.isEmpty,
                            child: Container(
                              width: 58,
                              height: 19,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      width: 0.5,
                                      color: ColorConfig.themeCorlor),
                                  borderRadius: BorderRadius.circular(3)),
                              child: Text(powerName,
                                  style: const TextStyle(
                                      fontSize: 10,
                                      color: ColorConfig.themeCorlor)),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 2,
                      ),
                      Container(
                        alignment: Alignment.centerLeft,
                        height: 18,
                        child: Text(
                          deptStr,
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.desTextColor),
                        ),
                      )
                    ],
                  ))
                ],
              )),
              const SizedBox(
                width: 8,
              ),
              SizedBox(
                width: 20,
                height: 20,
                child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
              )
            ],
          ),
        ),
        // const Divider(height: 1, color: ColorConfig.lineColor, indent: 15)
      ],
    );
  }
}
