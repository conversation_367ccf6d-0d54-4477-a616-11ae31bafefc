import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';

import 'package:get/get.dart';

import '../../../../../common/channel/channel.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../im/route/route_helper.dart';
import '../../../../../routes/app_pages.dart';
import '../controllers/org_more_setting_controller.dart';

class OrgMoreSettingView extends GetView<OrgMoreSettingController> {
  const OrgMoreSettingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => ToolBar(
      title: '企业管理',
      body: ListView(
        children: [
          SizedBox(
            height: 1,
          ),
          InkWell(
            onTap: () {
              Get.toNamed('/org-detail-setting',
                  arguments: {'model': controller.model});
            },
            child: SettingWidget().backSettingWidget(
                '',
                controller.logoStr.value,
                controller.nameStr.value,
                '',
                true,
                56),
          ),
          SizedBox(
            height: 10,
          ),
          Offstage(
            offstage: controller.model.deptId != '0' &&
                !(controller.model.power ?? '').contains('-1'), //不是创建者，不是超管
            child: Column(
              children: [
                InkWell(
                  onTap: () {
                    // //跳转原生权限设置列表页面
                    // print('Channel_contactJumpAuthSetting : ${controller.model.companyId}');
                    // Channel().invoke(Channel_contactJumpAuthSetting, {'companyId':controller.model.companyId,'deptId':controller.model.deptId});
                    RouteHelper.routePath(Routes.ORG_PERMISSION , arguments: {
                      'companyId':controller.model.companyId,
                      'deptId' : controller.model.deptId
                    });
                  },
                  child: SettingWidget()
                      .backSettingWidget('', '', '权限设置', '', true, 44),
                ),
                SizedBox(
                  height: 10,
                ),
                InkWell(
                  onTap: () {
                    Get.toNamed('/preference-setting',arguments: {'model':controller.model});
                  },
                  child: SettingWidget()
                      .backSettingWidget('', '', '偏好设置', '', true, 44),
                ),
                SizedBox(
                  height: 10,
                ),
                Offstage(
                  offstage: (controller.model.power ?? '').contains('-1'),
                  child: InkWell(
                    onTap: () {
                      Get.toNamed('/choose-all-org-members',arguments: {'model':controller.model,'type':0,'chooseType':1});
                    },
                    child: SettingWidget()
                        .backSettingWidget('', '', '转移企业创建者', '', true, 44),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    ));
  }
}
