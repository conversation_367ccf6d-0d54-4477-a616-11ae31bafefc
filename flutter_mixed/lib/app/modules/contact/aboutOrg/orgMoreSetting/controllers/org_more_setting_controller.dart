import 'dart:async';

import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class OrgMoreSettingController extends GetxController {

  late OrgModel model;
  RxString nameStr = ''.obs;
  RxString logoStr = ''.obs;
  StreamSubscription? subscription;
  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    nameStr.value = model.name;
    logoStr.value = model.logo;
    subscription = eventBus.on<Map>().listen((event) {
      print('event===$event');
      if(event['moreSetting'] != null){
        OrgModel changeModel = event['moreSetting'];
        nameStr.value = changeModel.name;
        logoStr.value = changeModel.logo;
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

 
}
