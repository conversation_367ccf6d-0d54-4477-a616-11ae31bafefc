import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';

import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../model/org/dept_members_model.dart';
import '../controllers/choose_all_org_members_controller.dart';
import 'contact_indexs.dart';

class ChooseAllOrgMembersView extends GetView<ChooseAllOrgMembersController> {
  const ChooseAllOrgMembersView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          //extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context,
              '选择公司成员',
              false,
              controller.type.value == 1
                  ? [
                      Container(
                        width: 60,
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            pressedOpacity: 0.5,
                            child: const Text(
                              '确定',
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            ),
                            onPressed: () {
                              Get.back(result: {
                                'list': controller.selectList.value
                              });
                            }),
                      )
                    ]
                  : [], onPressed: () {
            Get.back();
          }),
          body: Stack(
            children: [
              ListView.builder(
                  controller: controller.scrollController,
                  itemCount: controller.totalList.length + 1,
                  itemBuilder: (context, index) {
                    String letter = '';
                    MemberModel? memberModel;
                    MemberModel? tempModel;
                    if (index > 0) {
                      memberModel = controller.totalList[index - 1];
                      letter = memberModel!.name;
                      for (var selectModel in controller.selectList) {
                        if (selectModel.userId == memberModel!.userId) {
                          tempModel = selectModel;
                        }
                      }
                    }
                    String selectedStr = AssetsRes.APPROVE_SELECTED;
                    String unSelectedStr = AssetsRes.APPROVE_UNSELECTED;
                    if (controller.chooseType == 1) {
                      selectedStr = 'assets/images/3.0x/login_selected.png';
                      unSelectedStr = 'assets/images/3.0x/login_unselect.png';
                    }
                    return index == 0
                        ? Container(
                            width: double.infinity,
                            color: ColorConfig.whiteColor,
                            height: 50,
                            padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                            child: SettingWidget().backSearchWidget(
                                controller.searchController,
                                controller.node,
                                '搜索公司成员',
                                onSub: (value) {}, onTap: () async {
                              controller.node.unfocus();
                              var result =
                                  await Get.toNamed(Routes.SEARCH_ORG, arguments: {
                                'type': 1,
                                'chooseType': controller.chooseType,
                                'model': controller.model,
                                'selectList': controller.selectList,
                                'chooseSelf':controller.chooseSelf
                              },preventDuplicates: false);

                              Future.delayed(Duration(milliseconds: 300))
                                  .then((value) {
                                controller.totalList.refresh();
                              });
                            }),
                          )
                        : memberModel!.userId.isEmpty
                            ? Container(
                                alignment: Alignment.centerLeft,
                                padding: const EdgeInsets.only(left: 15),
                                height: 30,
                                child: Text(
                                  letter,
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.themeCorlor),
                                ),
                              )
                            : InkWell(
                                onTap: () {
                                  if (controller.type.value == 0) {
                                    Get.toNamed('/org-transfer-creater',
                                        arguments: {
                                          'model': controller.model,
                                          'memberModel': memberModel
                                        });
                                  } else {
                                    if (controller.chooseType == 1) {
                                      controller.selectList.clear();
                                      controller.selectList.add(memberModel);
                                    } else {
                                      if (tempModel == null) {
                                        controller.selectList.add(memberModel);
                                      } else {
                                        controller.selectList.remove(tempModel);
                                      }
                                    }

                                    controller.totalList.refresh();
                                  }
                                },
                                child: Column(
                                  children: [
                                    SettingWidget().backSettingWidget(
                                        controller.chooseType == 0
                                            ? ''
                                            : tempModel == null
                                                ? unSelectedStr
                                                : selectedStr,
                                        memberModel.headimg,
                                        memberModel.name,
                                        '',
                                        controller.type.value == 0,
                                        56),
                                    const Divider(
                                      indent: 15,
                                      color: ColorConfig.backgroundColor,
                                      height: 1,
                                    )
                                  ],
                                ),
                              );
                  }),
              ContactIndexs(
                onUpdateCallback: (String letter, int index) {
                  controller.indexViewOnTap(index);
                },
                letterList: controller.letters.value,
              ),
            ],
          ),
        ));
  }
}
