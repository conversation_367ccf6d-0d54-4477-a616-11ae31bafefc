import 'package:flutter/material.dart';

///索引字母表
class ContactIndexs extends StatefulWidget {
  final Function(String letter, int index) onUpdateCallback; //声明一个回调

  final List<String> letterList;

  const ContactIndexs(
      {Key? key, required this.letterList, required this.onUpdateCallback})
      : super(key: key);

  @override
  State<ContactIndexs> createState() => _ContactIndexsState();
}

class _ContactIndexsState extends State<ContactIndexs> {
  int lastIndex = -1;
  double bubbleAligmentY = 1;

  onUpdate(double dy) {
    final length = widget.letterList.length;
    final height = context.size!.height; //组件高度
    const letterHeight = 14;
    final allLetterHeight = length * letterHeight;
    //获取第一个索引的起始y坐标
    final startY = (height - allLetterHeight) / 2;

    var index = (dy - startY) ~/ letterHeight; //~/取模,即向下取整
    index = index.clamp(0, length - 1); //可以通过 clamp 方法处理数字越界问题
    if (index != lastIndex) {
      widget.onUpdateCallback(widget.letterList[index], index);
    }
    lastIndex = index;

    final firstLetterCy = startY + letterHeight / 2;
    final halfHeight = height / 2;
    final radioY =
        (halfHeight - firstLetterCy - index * letterHeight) / (halfHeight - 30);

    setState(() {
      bubbleAligmentY = -radioY;
    });
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> words = [];
    for (String item in widget.letterList) {
      final wordWidget = SizedBox(
          height: 14, child: Text(item, style: const TextStyle(fontSize: 10)));
      words.add(wordWidget);
    }
    return Positioned(
      top: 0,
      right: 0,
      bottom: 0,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //左边气泡
          Container(
            alignment: Alignment(-0.15, bubbleAligmentY),
            child: lastIndex > -1
                ? Stack(
              alignment: const Alignment(-0.15, 0),
              children: [
                Image.asset(
                  'assets/images/index_letter_buble.png',
                  width: 60,
                  height: 60,
                ),
                Text(
                  widget.letterList[lastIndex],
                  style: const TextStyle(fontSize: 18),
                )
              ],
            )
                : null,
          ),
          //右边索引
          GestureDetector(
            onVerticalDragUpdate: (DragUpdateDetails details) {
              onUpdate(details.localPosition.dy);
            },
            onVerticalDragStart: (DragStartDetails details) {
              //拖拽点击时
              onUpdate(details.localPosition.dy);
            },
            onVerticalDragEnd: (DragEndDetails details) {
              //拖拽结束
              lastIndex = -1;
              setState(() {});
            },
            //处理点击的
            onTapDown: (TapDownDetails details) {
              onUpdate(details.localPosition.dy);
            },
            onTapCancel: () {
              lastIndex = -1;
              setState(() {});
            },
            onTapUp: (TapUpDetails details) {
              lastIndex = -1;
              setState(() {});
            },
            child: Container(
              width: 30,
              color: Colors.transparent,
              padding: EdgeInsets.symmetric(horizontal: 5),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: words,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
