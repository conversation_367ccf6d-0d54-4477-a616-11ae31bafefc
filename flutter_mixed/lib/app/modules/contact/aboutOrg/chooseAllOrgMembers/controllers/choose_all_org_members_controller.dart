import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/base_info/info.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/org_model.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class ChooseAllOrgMembersController extends GetxController {
  TextEditingController searchController = TextEditingController();
  FocusNode node = FocusNode();
  RxList dataList = [].obs; //分组数据
  RxList totalList = [].obs; //不分组数据
  late OrgModel model;
  RxInt type = 0.obs; //0选择成员-转让创建者 1选择公司成员
  int chooseType = 1; //1单选2多选
  bool chooseSelf = true; //是否可以选择自己
  RxList selectList = [].obs;
  List<String> indexList = [];

  // AutoScrollController scrollController = AutoScrollController();
  //  List<Widget> indexWidgetList = []; //索引控件数组

  RxList<String> letters = ['A', 'B', 'C'].obs;

  ScrollController scrollController = ScrollController();
  @override
  void onInit() {
    super.onInit();
    model = Get.arguments['model'];
    type.value = Get.arguments['type'];
    chooseType = Get.arguments['chooseType'];
    if (Get.arguments['selectList'] != null) {
      selectList.value = Get.arguments['selectList'];
    }
    if (Get.arguments['chooseSelf'] != null) {
      chooseSelf = Get.arguments['chooseSelf'];
    }
  }

  @override
  void onReady() {
    super.onReady();
    getOrgMemebers();
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

  getOrgMemebers() async {
    DioUtil()
        .get('${ORGApi.GETALLSTAFFSORT}/${model.companyId}?keyword=', null,
            true, () {})
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        List memberList = data['data'];
        List tempList = [];
        Map userInfo = await UserDefault.getData(Define.TOKENKEY);
        String userId = userInfo['userId'];
        for (var i = 0; i < memberList.length; i++) {
          Map<String, dynamic> memberDic = memberList[i];
          if (memberDic['userId'] != userId || chooseSelf) {
            tempList.add(memberDic);
          }
        }

        List letterList = BaseInfo().letterList;
        for (var i = 0; i < tempList.length; i++) {
          Map<String, dynamic> tempMap = tempList[i];
          MemberModel memberModel = MemberModel.fromJson(tempMap);
          for (var k = 0; k < letterList.length; k++) {
            Map dataMap = letterList[k];
            if (dataMap.containsKey(memberModel.initial)) {
              List memList = dataMap[memberModel.initial];
              memList.add(memberModel);
            }
          }
        }

        List userList = [];
        List<String> keyList = [];

        for (var i = 0; i < letterList.length; i++) {
          Map map = letterList[i];
          String letter = map.keys.first;
          List modelList = map[letter];
          modelList.sort((a, b) => a.initial.compareTo(b.initial));
          if (modelList.isNotEmpty) {
            keyList.add(letter);
            userList.add(map);
          }
        }

        totalList.clear();
        for (var i = 0; i < userList.length; i++) {
          Map userMap = userList[i];
          String letter = userMap.keys.first;
          List memList = userMap[letter];

          MemberModel letterModel = MemberModel('');
          letterModel.name = letter;
          totalList.add(letterModel);
          for (var j = 0; j < memList.length; j++) {
            MemberModel memModel = memList[j];
            totalList.add(memModel);
          }
        }

        indexList = keyList;
        // indexWidgetList = backIndexListWidget();
        letters.value = _createLetters();
        dataList.value = userList;
        totalList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  List<String> _createLetters() {
    var l = indexList.map((e) => e).toList();
    return l ?? [];
  }

  indexViewOnTap(index) {
    double offset = 50.0;
    for (var i = 0; i < index; i++) {
      Map userMap = dataList[i];
      String letter = userMap.keys.first;
      List memList = userMap[letter];
      offset += memList.length * 57 + 30;
    }
    scrollController.jumpTo(offset);
    // scrollController.scrollToIndex(totalCount,
    //     preferPosition: AutoScrollPosition.begin);
  }
}
