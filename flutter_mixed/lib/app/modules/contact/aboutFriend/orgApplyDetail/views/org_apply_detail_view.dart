import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/friend_pending_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_pending_model.dart';
import 'package:get/get.dart';
import '../../../../../common/base_info/info.dart';
import '../../../../place_holder/work_tab_empty.dart';
import '../../../model/org/external_pending_model.dart';
import '../controllers/org_apply_detail_controller.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../..//common/config/config.dart';

class OrgApplyDetailView extends GetView<OrgApplyDetailController> {

  OrgApplyDetailView({Key? key}) : super(key: key);
  List statusList = ['', '已同意', '已忽略', '已过期'];

  @override
  Widget build(BuildContext context) {

    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(
              context, controller.titleList[controller.type], false, [],
              onPressed: () {
            Get.back();
          }),
          body: Stack(
            children: [
              _noApplyPge(),
              // _applyList()
            ],
          ),
        ));
  }

  _applyList() {
    return ListView.builder(
        itemCount: controller.dataList.length,
        itemBuilder: (context, index) {
          String headImageName = '';
          String name = '';
          int status = 0;
          String timeStr = '';
          String content = '';
          String recordId = '';

          FriendPendingModel? model;
          ExternalPendingModel? externalPendingModel ;
          OrgPendingModel? orgPendingModel ;
          if (controller.type == 0) {
            model = controller.dataList[index];
            headImageName = model?.avatar ?? '';
            name = model?.name ?? '';
            status = model?.status ?? 0;
            timeStr = BaseInfo().formatTimestamp(model?.updateTime ?? 0, 'yyyy.MM.dd HH:mm');
            content = '申请理由：${model?.message}';
            recordId = model?.recordId ?? '';
          }
          if (controller.type == 1) {

            externalPendingModel = controller.dataList[index];
            headImageName = externalPendingModel!.logo;
            name = externalPendingModel.name;
            status = externalPendingModel.status;
            if(status == 0){
              status = 1;
            }
            if(status == 1){
              status = 0;
            }
            timeStr =externalPendingModel.handleTime==0?'': BaseInfo().formatTimestamp(externalPendingModel.handleTime, 'yyyy.MM.dd HH:mm');
            content = '邀请你成为本企业外部协作人';
            recordId = externalPendingModel.externalId;
          }
          if (controller.type == 2) {
            orgPendingModel = controller.dataList[index];
            headImageName = orgPendingModel!.headimg;
            name = orgPendingModel.name;
            status = orgPendingModel.status;
            timeStr = BaseInfo().formatTimestamp(orgPendingModel.createTime, 'yyyy.MM.dd HH:mm');
            content = orgPendingModel.content;
            recordId = orgPendingModel.recordId;
          }

          return InkWell(
            onTap: () {
              Get.toNamed('/friend-apply-detail',
                  arguments: {
                    'type': controller.type,
                    'companyId': controller.companyId,
                    'recordId':recordId
                  },
                  preventDuplicates: false);
            },
            child: Column(
              children: [
                Container(
                  height: 60,
                  width: double.infinity,
                  color: ColorConfig.whiteColor,
                  padding: EdgeInsets.only(left: 15,right: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(1),
                        width: 40,
                        height: 40,
                        alignment: Alignment.centerLeft,
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: SettingWidget.backImageProvider(headImageName)),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                                color: ColorConfig.lineColor, width: 1)),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                          child: Container(
                            height: double.infinity,
                            child: Row(
                              mainAxisAlignment:
                              MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                    child: Container(
                                      height: double.infinity,
                                      alignment: Alignment.centerLeft,
                                      child: Column(
                                        mainAxisAlignment:
                                        MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            alignment: Alignment.centerLeft,
                                            child: Text(
                                              name,
                                              textAlign: TextAlign.right,
                                              style: TextStyle(
                                                  color:
                                                  ColorConfig.mainTextColor,
                                                  fontSize: 14),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 2,
                                          ),
                                          Container(
                                            alignment: Alignment.centerLeft,
                                            child: Text(
                                              content,
                                              textAlign: TextAlign.left,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                  color: ColorConfig.desTextColor,
                                                  fontSize: 12),
                                            ),
                                          )
                                        ],
                                      ),
                                    )),
                                status == 0
                                    ? Container(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    '查看',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.themeCorlor),
                                  ),
                                )
                                    : Container(
                                  height: double.infinity,
                                  width: 120,
                                  alignment: Alignment.centerRight,
                                  child: Column(
                                    mainAxisAlignment:
                                    MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        alignment:
                                        Alignment.centerRight,
                                        child: Text(
                                          statusList[status],
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                              color: ColorConfig
                                                  .desTextColor,
                                              fontSize: 12),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 2,
                                      ),
                                      Container(
                                        width: 120,
                                        alignment:
                                        Alignment.centerRight,
                                        child: Text(
                                          timeStr,
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                              color: ColorConfig
                                                  .desTextColor,
                                              fontSize: 12),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ))
                    ],
                  ),
                ),
                const Divider(height: 1, color: ColorConfig.lineColor)
              ],
            ),
          );
        });
  }

  _noApplyPge() {
    if(controller.dataList.isEmpty){
      return Container(
        alignment: Alignment.center,
        child: Text('没有可用的申请'),
      );
    }

    return _applyList();

  }
}
