import 'dart:async';

import 'package:flutter_mixed/app/modules/contact/model/org/friend_pending_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_pending_model.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../model/org/external_pending_model.dart';

class OrgApplyDetailController extends GetxController {

  StreamSubscription? subscription;
  List titleList = ['新的好友', '外部联系人', '企业加入申请'];
  int type = 0; //0好友申请1外部联系人邀请2企业加入申请
  int page = 1;
  int pageSize = 20;
  String companyId = '';
  RxList dataList = [].obs;

  @override
  void onInit() {
    super.onInit();
    type = Get.arguments['type'];
    if (type == 2) {
      companyId = Get.arguments['companyId'];
    }
    subscription = eventBus.on<Map>().listen((event) {
      if (event['waitCount'] == 1) {
        if (type == 0) {
          getFriendPendingList();
        }
        if (type == 1) {
          getExternalPendingList();
        }
        if (type == 2) {
          getOrgPendingList();
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (type == 0) {
      getFriendPendingList();
    }
    if (type == 1) {
      getExternalPendingList();
    }
    if (type == 2) {
      getOrgPendingList();
    }
  }

  @override
  void onClose() {
    subscription!.cancel();
    super.onClose();
  }

  getFriendPendingList() {
    DioUtil().get(
        '${ORGApi.GETWAITLIST}/friend/page/$page/size/$pageSize', null, true,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        List friendList = data['data'];
        if (page == 1) {
          dataList.clear();
        }
        for (var i = 0; i < friendList.length; i++) {
          FriendPendingModel model = FriendPendingModel.fromJson(friendList[i]);
          dataList.add(model);
        }
        dataList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getExternalPendingList() {
    DioUtil().get(
        '${ORGApi.GETWAITLIST}/external/page/$page/size/$pageSize', null, true,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        List externalList = data['data'];
        if (page == 1) {
          dataList.clear();
        }
        for (var i = 0; i < externalList.length; i++) {
          ExternalPendingModel model =
              ExternalPendingModel.fromJson(externalList[i]);
          dataList.add(model);
        }
        dataList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getOrgPendingList() {
    DioUtil().get(
        '${ORGApi.GETWAITLIST}/org/$companyId/page/$page/size/$pageSize',
        null,
        true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        List orgList = data['data'];
        if (page == 1) {
          dataList.clear();
        }
        for (var i = 0; i < orgList.length; i++) {
          OrgPendingModel model = OrgPendingModel.fromJson(orgList[i]);
          dataList.add(model);
        }
        dataList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
