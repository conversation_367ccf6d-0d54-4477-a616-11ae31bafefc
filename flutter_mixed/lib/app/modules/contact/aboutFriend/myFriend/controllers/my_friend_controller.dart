import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/base_get_controller.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/model/select-group-model.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../mix/page_mix.dart';

class MyFriendController extends BaseGetXController {
  TextEditingController searchController = TextEditingController();
  RxInt chooseType = 0.obs; //0好友列表，1选择好友 2转发
  String? groupId;
  RxList dataList = [].obs;
  RxList searchList = [].obs;

  RxList selectGroupList = [].obs;
  FocusNode node = FocusNode();
  StreamSubscription? subscription;
  RxList chooseList = [].obs;
  @override
  void onInit() {
    super.onInit();
    chooseType.value = Get.arguments['chooseType'];
    if (Get.arguments['selectGroupList'] != null) {
      selectGroupList = Get.arguments['selectGroupList'];
      dealselectGroupList();
    }

    subscription = eventBus.on<Map>().listen((event) async {
      if (event == null) return;

      if (event['friendList'] != null) {
        dealFriendData();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    dealFriendData();
  }

  @override
  void onClose() {
    searchController.dispose();
    subscription?.cancel();
    super.onClose();
  }

  dealFriendData() async {
    List tempList = BaseInfo().letterList;
    List friendList = await UserDefault.getData(Define.FRIENDLIST);
    dataList.clear();
    if (friendList.isNotEmpty) {
      for (var i = 0; i < tempList.length; i++) {
        Map dataDic = tempList[i];

        String letter = dataDic.keys.last;
        List letterList = dataDic[letter];
        for (var j = 0; j < friendList.length; j++) {
          UserModel model = UserModel.fromJson(friendList[j]);
          if (letter == model.initial) {
            letterList.add(model);
          }
        }
        if (letterList.isNotEmpty) {
          dataList.add(dataDic);
        }
      }
    }
    dataList.refresh();
    searchList = dataList;
  }

  dealselectGroupList() {
    eventBus.fire(SelectGroupmodel(
        sign: Routes.GROUP_ADD_MEMBERS,
        selectGroupList: selectGroupList,
        isChooseEnd: false));
    chooseList.clear();
    for (var i = 0; i < selectGroupList.length; i++) {
      MemberModel model = selectGroupList[i];
      if (model.chooseState != 2 && chooseType.value == 1) {
        chooseList.add(model);
      }
    }
  }

  backButtonTitle() {
    if (chooseType.value == 2) {
      return '确定';
    } else if (chooseType.value == 1) {
      return '确定(${chooseList.length})';
    }
    return '';
  }

  backBtnTitleColor() {
    if (chooseType.value == 2) {
      return ColorConfig.mainTextColor;
    } else if (chooseType.value == 1) {
      if (chooseList.isEmpty) {
        return ColorConfig.desTextColor;
      } else {
        return ColorConfig.themeCorlor;
      }
    }
    return Colors.transparent;
  }
}
