import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/addMembes/user_selected_widget.dart';
import 'package:flutter_mixed/app/modules/common/scan_group_code/model/select-group-model.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';

import 'package:get/get.dart';
import 'package:sticky_headers/sticky_headers/widget.dart';

import '../../../../../../res/assets_res.dart';
import '../../../model/org/dept_members_model.dart';
import '../controllers/my_friend_controller.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../common/config/config.dart';

class MyFriendView extends GetView<MyFriendController> {
  const MyFriendView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        backgroundColor: ColorConfig.backgroundColor,
        extendBodyBehindAppBar: true,
        appBar: TitleBar().backAppbar(
            context,
            backTitleString(),
            false,
            controller.chooseType.value == 0
                ? []
                : [
                    Container(
                      padding: const EdgeInsets.only(right: 16),
                      child: CupertinoButton(
                          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                          pressedOpacity: 0.5,
                          child: Text(
                            controller.backButtonTitle(),
                            style: TextStyle(
                                color: controller.backBtnTitleColor(),
                                fontSize: 14),
                          ),
                          onPressed: () {
                            Get.back(result: {
                              'selectGroupList': controller.selectGroupList,
                              'isChooseEnd': true
                            });
                          }),
                    )
                  ], onPressed: () {
          if (controller.chooseType.value == 1) {
            Get.back(result: {
              'selectGroupList': controller.selectGroupList,
              'isChooseEnd': false
            });
          } else {
            Get.back();
          }
        }, isNative: !controller.isNative.value),
        body: Column(
          children: [
            (DeviceUtils().top.value + 44).gap,
            _backSearchWidget(),
            UserSelected(
              userList: controller.chooseList,
              dataBack: (userId) {},
              selectGroupList: controller.selectGroupList,
            ),
            if (controller.chooseType.value == 1) ...[_backTextSpan()],
            Expanded(
                child: MediaQuery.removePadding(
                    removeTop: true, context: context, child: _backListView()))
          ],
        )));
  }

  _backListView() {
    return ListView.builder(
      itemBuilder: (context, index) {
        Map dataDic;
        String letter = '';
        List friendList = [];
        if (controller.dataList.isNotEmpty) {
          dataDic = controller.dataList[index];
          letter = dataDic.keys.last;
          friendList = dataDic[letter];
        }

        return controller.dataList.isEmpty
            ? Container(
                width: double.infinity,
                height: (DeviceUtils().height.value - 130) * 0.5,
                alignment: Alignment.center,
                child: const Text(
                  '暂无好友',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              )
            : StickyHeader(
                header: Container(
                  width: double.infinity,
                  color: ColorConfig.backgroundColor,
                  padding: const EdgeInsets.only(left: 15),
                  height: 20,
                  child: Text(
                    letter,
                    style: const TextStyle(
                        fontSize: 14, color: ColorConfig.desTextColor),
                  ),
                ),
                content: Column(
                  children: backWidgetList(friendList),
                ));
      },
      itemCount: controller.dataList.isEmpty ? 1 : controller.dataList.length,
    );
  }

  _backSearchWidget() {
    return Container(
        width: double.infinity,
        color: Colors.white,
        height: 50,
        padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
        child: SettingWidget().backSearchWidget(controller.searchController,
            controller.node, '搜索好友', onSub: (value) {},
            onTap: () async {
          controller.node.unfocus();
          if (controller.chooseType.value == 0) {
            RouteHelper.route(Routes.SEARCH_ORG, arguments: {'type': 4, 'chooseType': 0});
          }
          if (controller.chooseType.value == 1 ||
              controller.chooseType.value == 2) {
            var result = await Get.toNamed(Routes.SEARCH_ORG, arguments: {
              'type': controller.chooseType.value == 1 ? 4 : 5,
              'chooseType': 2,
              'selectList': RxList(controller.selectGroupList
                  .map((e) => MemberModel.fromJson(e.toJson()))
                  .toList())
            },preventDuplicates: false);
            if (result != null && result['list'] != null) {
              controller.selectGroupList = result['list'];
              controller.dealselectGroupList();
              controller.dataList.refresh();
            }
          }
        }));
  }

  backWidgetList(List friendList) {
    List<Widget> widgets = [];
    for (var i = 0; i < friendList.length; i++) {
      UserModel model = friendList[i];

      MemberModel? tempModel;
      for (var j = 0; j < controller.selectGroupList.length; j++) {
        MemberModel selectModel = controller.selectGroupList[j];
        if (selectModel.userId == model.userId) {
          tempModel = selectModel;
          break;
        }
      }

      Widget widget = InkWell(
        onTap: () {
          if (controller.chooseType.value == 0) {
            RouteHelper.route(Routes.USER_INFO,
                arguments: {'userId': model.userId, 'type': 1, 'routeback': true});
          }
          if (controller.chooseType.value == 1 ||
              controller.chooseType.value == 2) {
            if (tempModel != null) {
              if (tempModel.chooseState == 2) {
                toast('不能取消当前成员');
                return;
              }
              controller.selectGroupList.remove(tempModel);
            } else {
              if (controller.chooseType.value == 2 &&
                  controller.selectGroupList.length == 10) {
                toast('最多选择10条会话');
                return;
              }
              MemberModel changeModel = MemberModel(model.userId);
              changeModel.name = model.name;
              changeModel.headimg = model.avatar;
              changeModel.imId = model.imId;
              controller.selectGroupList.add(changeModel);
            }
            controller.dealselectGroupList();
            controller.dataList.refresh();
          }
        },
        child: SettingWidget().backSettingWidget(
            controller.chooseType.value == 0
                ? ''
                : tempModel != null
                    ? AssetsRes.APPROVE_SELECTED
                    : AssetsRes.APPROVE_UNSELECTED,
            model.avatar,
            model.remark.isNotEmpty ? model.remark : model.name,
            '',
            false,
            56),
      );
      widgets.add(widget);
    }
    return widgets;
  }

  _backTextSpan() {
    return Container(
      width: double.infinity,
      color: ColorConfig.whiteColor,
      height: 38,
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(left: 15, right: 15),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: RichText(
            text: TextSpan(
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    eventBus.fire(SelectGroupmodel(
                        sign: Routes.GROUP_ADD_MEMBERS,
                        selectGroupList: controller.selectGroupList,
                        isChooseEnd: false));
                    Get.until((route) {
                      if (route.settings.name == Routes.GROUP_ADD_MEMBERS) {
                        return true;
                      }
                      return false;
                    });
                  },
                text: '联系人',
                style: const TextStyle(
                    fontSize: 14, color: ColorConfig.themeCorlor),
                children: [
              TextSpan(
                  recognizer: TapGestureRecognizer()..onTap = () {},
                  text: ' > 我的好友',
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.desTextColor))
            ])),
      ),
    );
  }

  backTitleString() {
    switch (controller.chooseType.value) {
      case 0:
        return '好友列表';
      case 1:
        return '选择好友';
      case 2:
        return '选择好友';
      default:
        return '';
    }
  }

  backGroupMemberString() {
    switch (controller.chooseType.value) {
      case 0:
        return '搜索好友';
      case 1:
        return '搜索好友';
      case 2:
        return '搜索群组成员';
      default:
        return '';
    }
  }
}
