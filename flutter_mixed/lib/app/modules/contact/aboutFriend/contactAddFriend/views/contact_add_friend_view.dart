import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/contact_model.dart';
import 'package:get/get.dart';
import 'package:sticky_headers/sticky_headers/widget.dart';
import '../../../../../common/config/string_const.dart';
import '../../../../../routes/app_pages.dart';
import '../controllers/contact_add_friend_controller.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';

class ContactAddFriendView extends GetView<ContactAddFriendController> {
  const ContactAddFriendView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: ColorConfig.backgroundColor,
          appBar:
              TitleBar().backAppbar(context, '联系人', false, [], onPressed: () {
            Get.back();
          }),
          body: ListView.builder(
            itemBuilder: (context, index) {
              Map map = controller.dataList[index];
              String letter = map.keys.first;
              List modelList = map[letter];

              return StickyHeader(
                  header: Container(
                    padding: EdgeInsets.only(left: 15),
                    height: 20,
                    child: Text(
                      letter,
                      style: TextStyle(
                          fontSize: 14, color: ColorConfig.desTextColor),
                    ),
                  ),
                  content: Column(
                    children: backWidgetList(modelList),
                  ));
            },
            itemCount: controller.dataList.length,
          ),
        ));
  }

  backWidgetList(modelList) {
    List<Widget> widgets = [];
    for (var i = 0; i < modelList.length; i++) {
      ContactModel model = modelList[i];
      Widget widget = InkWell(
        child: getContactWidgetList(model),
      );
      widgets.add(widget);
    }
    return widgets;
  }

  getContactWidgetList(ContactModel model) {
    int status = 100; //未注册
    Map? userMap;
    if (controller.userList.isNotEmpty) {
      for (var i = 0; i < controller.userList.length; i++) {
        Map userDic = controller.userList[i];
        if (userDic['mobile'] == model.phone) {
          userMap = userDic;
          status = userDic['status'];
        }
      }
    }
    return Container(
      width: double.infinity,
      height: 54,
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.only(left: 15, right: 40),
      child: InkWell(
        onTap: () {
          if (controller.type == 1) {
            if (status == 100) {
              toast('该联系人未注册$appName');
            } else {
              Get.back(result: model);
            }
          }
        },
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              child: Image.asset('assets/images/3.0x/contact_moren_head.png'),
            ),
            SizedBox(
              width: 10,
            ),
            Expanded(
                child: Container(
              child: Text(
                model.name,
                style:
                    TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
              ),
            )),
            SizedBox(
              width: 10,
            ),
            Offstage(
              offstage: controller.type==1&&(status==1||status==2),
              child: InkWell(
              onTap: () {
                if (status == 1) {
                  RouteHelper.route(Routes.USER_INFO,
                      arguments: {'userId': userMap!['id'],'type':1});
                }
              },
              child: Container(
                alignment: Alignment.center,
                width: 60,
                height: 30,
                decoration: BoxDecoration(
                    border: Border.all(
                        width: 1,
                        color: status == 1
                            ? ColorConfig.themeCorlor
                            : ColorConfig.whiteColor),
                    borderRadius: BorderRadius.circular(2)),
                child: Text(
                  status == 100
                      ? '未注册'
                      : status == 1
                          ? '添加'
                          : '已添加',
                  style: TextStyle(
                      fontSize: 14,
                      color: status == 1
                          ? ColorConfig.themeCorlor
                          : ColorConfig.desTextColor),
                ),
              ),
            ),
            )
          ],
        ),
      ),
    );
  }

}
