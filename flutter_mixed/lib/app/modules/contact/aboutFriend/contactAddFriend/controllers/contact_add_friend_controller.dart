import 'dart:convert';

import 'package:fast_contacts/fast_contacts.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/contact_model.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:get/get.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/string-util.dart';

class ContactAddFriendController extends GetxController {

  RxList dataList = [].obs;
  RxList userList = [].obs;//已注册用户列表
  int type = 0;//0通讯录添加好友1外部联系人
  @override
  void onInit() {
    super.onInit();
    type = Get.arguments['type'];

    PermissionUtil.requestContactsPermissionStatus().then((r){
         if(r.isGranted){
           openAddressBook();
         }else{
           toast('请打开通讯录权限才能使用通讯录功能');
         }
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

//获取通讯录好友
  openAddressBook() async {
    getContactData();
  }

  getContactData() async {
    
    List letterList = BaseInfo().letterList;
  
    List<Contact> contacts = await FastContacts.getAllContacts();
    List phoneList = [];
    for (var i = 0; i < contacts.length; i++) {
      Contact item = contacts[i];
      List phones = item.phones;
      StructuredName? structuredName = item.structuredName;

      String name =
          '${structuredName?.familyName ??''}${structuredName?.middleName ?? ''}${structuredName?.givenName ??""}';
      String firstWord = (StringUtil.isEmpty(name)) ? '' : PinyinHelper.getFirstWordPinyin(name)[0].toUpperCase();
      String letter = (StringUtil.isEmpty(name)) ? '' : PinyinHelper.getPinyin(name).toLowerCase();

      for (var j = 0; j < phones.length; j++) {
        Phone phone = phones[j];
        ContactModel model = ContactModel(name, '', letter);
        String number = phone.number.replaceAll(' ', '');
        number = number.replaceAll('+86', '');
        if (number.length == 11) {
          model.phone = number;
          phoneList.add(number);
          for (var k = 0; k < letterList.length; k++) {
            Map dataMap = letterList[k];
            if (dataMap.containsKey(firstWord)) {
              List dataList = dataMap[firstWord];
              dataList.add(model);
            }
          }
        }
      }
    }

    List tempList = [];
    for (var i = 0; i < letterList.length; i++) {
      
      Map map = letterList[i];
      String letter = map.keys.first;
      List modelList = map[letter];
      modelList.sort((a, b) => a.letter.compareTo(b.letter));
      if(modelList.isNotEmpty){
        tempList.add(map);
      }
    }
    dataList.value = tempList;
    if(phoneList.isNotEmpty){
      contactMatch(phoneList);
    }
  }

  //匹配手机号
  contactMatch(List list) async {
    DioUtil().post(
        LoginApi.CONTACTMATCH, jsonEncode(list), true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        userList.value = data['data'];
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
