import 'dart:convert';
import 'dart:io';

import 'package:fast_contacts/fast_contacts.dart' as fast;
import 'package:flutter_contacts/flutter_contacts.dart' as flutter;
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/contact_model.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:get/get.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/string-util.dart';

class ContactAddFriendController extends GetxController {

  RxList dataList = [].obs;
  RxList userList = [].obs;//已注册用户列表
  int type = 0;//0通讯录添加好友1外部联系人
  @override
  void onInit() {
    super.onInit();
    type = Get.arguments['type'];

    PermissionUtil.requestContactsPermissionStatus().then((r){
         if(r.isGranted){
           openAddressBook();
         }else{
           toast('请打开通讯录权限才能使用通讯录功能');
         }
    });
  }

  //获取通讯录好友
  openAddressBook() async {
    getContactData();
  }

  getContactData() async {
    List letterList = BaseInfo().letterList;
    if (Platform.isIOS) {
      List<fast.Contact> contacts = await fast.FastContacts.getAllContacts();
      _processContacts<fast.Contact>(
        contacts,
        letterList,
        (item) {
          final fast.StructuredName? structuredName = item.structuredName;
          return '${structuredName?.familyName ?? ''}${structuredName?.middleName ?? ''}${structuredName?.givenName ?? ''}';
        },
        (item) => item.phones.map((p) => p.number).toList(),
      );
    } else {
      List<flutter.Contact> contacts = await flutter.FlutterContacts.getContacts(withProperties: true);
      _processContacts<flutter.Contact>(
        contacts,
        letterList,
        (item) => item.displayName,
        (item) => item.phones.map((p) => p.number).toList(),
      );
    }
  }

  // 公共联系人处理方法
  void _processContacts<T>(
    List<T> contacts,
    List letterList,
    String Function(T) getName,
    List<String> Function(T) getPhones,
  ) {
    List phoneList = [];
    for (var i = 0; i < contacts.length; i++) {
      final item = contacts[i];
      final String name = getName(item);
      final List<String> phones = getPhones(item);
      final String firstWord = (StringUtil.isEmpty(name)) ? '' : PinyinHelper.getFirstWordPinyin(name)[0].toUpperCase();
      final String letter = (StringUtil.isEmpty(name)) ? '' : PinyinHelper.getPinyin(name).toLowerCase();
      for (var j = 0; j < phones.length; j++) {
        String number = phones[j].replaceAll(' ', '').replaceAll('+86', '');
        if (number.length == 11) {
          ContactModel model = ContactModel(name, '', letter);
          model.phone = number;
          phoneList.add(number);
          bool isHave = false;
          for (var k = 0; k < letterList.length; k++) {
            Map dataMap = letterList[k];
            if (dataMap.containsKey(firstWord)) {
              List dataList = dataMap[firstWord];
              dataList.add(model);
              isHave = true;
              break;
            }
          }
          if (!isHave) {
            // 非字母归为#分组
            Map dataMap = letterList.last;
            List dataList = dataMap['#'];
            dataList.add(model);
          }
        }
      }
    }
    List tempList = [];
    for (var i = 0; i < letterList.length; i++) {
      Map map = letterList[i];
      String letter = map.keys.first;
      List modelList = map[letter];
      modelList.sort((a, b) => a.letter.compareTo(b.letter));
      if (modelList.isNotEmpty) {
        tempList.add(map);
      }
    }
    dataList.value = tempList;
    if (phoneList.isNotEmpty) {
      contactMatch(phoneList);
    }
  }


  //匹配手机号
  contactMatch(List list) async {
    DioUtil().post(
        LoginApi.CONTACTMATCH, jsonEncode(list), true, () {
    }).then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        userList.value = data['data'];
        dataList.refresh();
      } else {
        toast('[${data['msg']}');
      }
    });
  }
}


class ContactBody {
  String? name;
  String? phone;

  ContactBody(this.name, this.phone);
}