import 'dart:async';

import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../../main.dart';
import '../../../../../common/api/Define.dart';
import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/storage.dart';
import '../../../model/org/org_model.dart';

class PendingListController extends GetxController {
  List titleList = ['好友申请', '外部联系人邀请', '企业加入申请'];

  StreamSubscription? subscription;
  RxList orgList = [].obs;
  RxInt showCount = 3.obs;

  RxInt friendCount = 0.obs;
  RxInt externalCount = 0.obs;
  RxMap orgCountMap = {}.obs;
  @override
  void onInit() {
    super.onInit();
    subscription = eventBus.on<Map>().listen((event) {
      if(event != null){
        if (event['waitCount'] == 1) {
          getPendingListCount();
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    getAllCompany();
    getPendingListCount();
  }

  @override
  void onClose() {
    subscription!.cancel();
    super.onClose();
  }

  getPendingListCount() {
    DioUtil().get(ORGApi.GETWAITLIST, null, true, () {
    },isShowLoading: false).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataMap = data['data'];
        friendCount.value = dataMap['friendApplyNumber'];
        externalCount.value = dataMap['contactsInviteNumber'];

        var orgappnumber = dataMap['orgApplyNumber'];
        if(orgappnumber != null){
          orgCountMap.value = dataMap['orgApplyNumber'];
        }

        showCount.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getAllCompany() async {
    Map dataDic = await UserDefault.getData(Define.ORGLIST);

    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }

    List allOrModelList = [];
    if (companies.isNotEmpty) {
      allOrModelList =
          companies.map((item) => OrgModel.fromJson(item)).toList();
    }

    for (var i = 0; i < allOrModelList.length; i++) {
      OrgModel model = allOrModelList[i];
      if (model.deptId == '0' ||
          (model.power ?? '').contains('1') ||
          (model.power ?? '').contains('-1')) {
        orgList.add(model);
      }
    }
    showCount.refresh();
  }
}
