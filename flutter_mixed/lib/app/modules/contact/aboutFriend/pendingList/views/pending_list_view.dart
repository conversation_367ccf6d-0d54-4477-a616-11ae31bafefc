import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:get/get.dart';
import 'package:sticky_headers/sticky_headers.dart';
import '../controllers/pending_list_controller.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../common/config/config.dart';

class PendingListView extends GetView<PendingListController> {
  const PendingListView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {

    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar:
              TitleBar().backAppbar(context, '新的申请', false, [], onPressed: () {
            Get.back();
          }),
          body: Container(
              color: ColorConfig.backgroundColor,
              child: ListView.builder(
                  itemCount: controller.showCount.value,
                  itemBuilder: (context, index) {
                    return StickyHeader(
                      header: Container(
                          padding: EdgeInsets.symmetric(horizontal: 15 , vertical: 6),
                          child: Column(
                            children: [
                              if(index > 0)...[
                                15.gap,
                              ],
                              Container(
                                height: 18,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  controller.titleList[index],
                                  style: TextStyle(
                                      color: ColorConfig.mainTextColor,
                                      fontSize: 13),
                                ),
                              ),
                            ],
                          )),
                      content: backWidgetWithIndex(index),
                    );
                  })),
        ));
  }

  backWidgetWithIndex(int index) {
    if (index == 0) {
      return InkWell(
        onTap: () {
          Get.toNamed('/org-apply-detail', arguments: {'type': 0});
        },
        child: backBaseWidgetWithInfo(
            'assets/images/3.0x/pending_friend_apply.png',
            '新的好友申请',
            controller.friendCount.value),
      );
    }
    if (index == 1) {
      return InkWell(
        onTap: () {
          Get.toNamed('/org-apply-detail', arguments: {'type': 1});
        },
        child: backBaseWidgetWithInfo(
            'assets/images/3.0x/pending_external_invite.png',
            '外部联系人',
            controller.externalCount.value),
      );
    }
    if (index == 2) {
      return Column(
        children: backOrgWidget(),
      );
    }
  }

  backOrgWidget() {
    List<Widget> lists = [];
    for (var i = 0; i < controller.orgList.length; i++) {
      OrgModel model = controller.orgList[i];
      lists.add(InkWell(
        onTap: () {
          Get.toNamed('/org-apply-detail',
              arguments: {'type': 2, 'companyId': model.companyId});
        },
        child: backBaseWidgetWithInfo(model.logo, model.name,
            controller.orgCountMap[model.companyId] ?? 0),
      ));
    }

    return lists;
  }
}

backBaseWidgetWithInfo(String headImageName, String content, int count) {
  return Container(
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.only(left: 15, right: 15),
      height: 56,
      width: double.infinity,
      child: Row(
        children: [
          headImageName.startsWith('http')
              ? Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      image: DecorationImage(
                          image: SettingWidget.backImageProvider(headImageName))),
                )
              : SizedBox(
                  width: 24,
                  height: 24,
                  child: Image.asset(headImageName),
                ),
          SizedBox(
            width: 8,
          ),
          Expanded(
              child: Container(
            child: Text(
              content,
              style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          )),
          Offstage(
            offstage: count == 0,
            child: Container(
              padding: EdgeInsets.all(2),
              alignment: Alignment.center,
              constraints: BoxConstraints(minWidth: 16),
              height: 16,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: ColorConfig.deleteCorlor),
              child: Text(
                count < 100 ? '$count' : '99+',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12, color: ColorConfig.whiteColor,height: 1),
              ),
            ),
          ),
          SizedBox(
            width: 8,
          ),
          SizedBox(
            width: 9,
            height: 17,
            child: Image.asset('assets/images/3.0x/mine_right.png'),
          )
        ],
      ));
}
