import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../../../common/api/LoginApi.dart';
import '../../../../../common/widgets/widgets.dart';
import '../../../../../routes/app_pages.dart';
import '../../../../../utils/http.dart';
import '../../../../../utils/string-util.dart';
import '../../../../gallery/gallery.dart';
import '../../../../home/<USER>/home_controller.dart';

class FriendApplyDetailController extends GetxController {

  List titleList = ['新的好友', '外部联系人', '企业加入申请'];
  int type = 0; //0好友申请1外部联系人邀请2企业加入申请
  String companyId = '';
  RxList dataList = [].obs;
  RxString nameStr = ''.obs;
  int stauts = 0;
  String recordId = '';

  String logoStr = '';
  String contentStr = '';

  RxInt buttonStatus = 0.obs; //0未处理1已处理
  RxString agreeStr = ''.obs;
  RxString refuseStr = ''.obs;
  StreamSubscription? subscription;
  String userId = ''; //公司数据里的申请人

  RxBool isNative = false.obs; //是否是原生跳转

  Map? friendMap; //好友数据用来跳转单聊页面
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      type = Get.arguments['type'];
      if (type == 0) {
        stauts = 3;
      }
      if (type == 1) {
        stauts = 4;
      }
      if (type == 2) {
        companyId = Get.arguments['companyId'];
        stauts = 2;
      }
      recordId = Get.arguments['recordId'];
    }

    subscription = eventBus.on<Map>().listen((event) {
      if (event == null) return;
      var route = event['route'];
      print('friend-apply-detail------$event');
      if (route == Routes.FRIEND_APPLY_DETAIL) {
        var arguments = event['arguments'];
        recordId = arguments['recordId'];
        type = arguments['type'];
        if (type == 0) {
          stauts = 3;
        }
        if (type == 1) {
          stauts = 4;
        }
        if (type == 2) {
          companyId = arguments['companyId'];
          stauts = 2;
        }
        isNative.value = true;

        if (type == 0) {
          getFriendPendingDetail();
        } else {
          getPendingDetail();
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (type == 0) {
      getFriendPendingDetail();
    } else {
      getPendingDetail();
    }
  }

    //点击了头像
  tapAvatar(){
    if (StringUtil.isEmpty(logoStr)) return;
    var galleryList = <GalleryItem>[];
    galleryList.add(GalleryItem('',logoStr));
    Navigator.push(Get.context!,
        PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
          return FadeTransition(
              opacity: animation, child: GalleryPage(galleryList, 0));
        }));
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

  getFriendPendingDetail() {
    if (recordId.isEmpty) {
      return;
    }
    DioUtil()
        .get('${LoginApi.IM_FRIEND_APPLY_INFO}/$recordId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        Map dataMap = data['data'];
        friendMap ??= dataMap;

        logoStr = dataMap['headimg'];
        nameStr.value = dataMap['name'];
        contentStr = dataMap['message'];
        if (dataMap['status'] == 0) {
          buttonStatus.value = 0;
        } else {
          buttonStatus.value = 1;
          if (dataMap['status'] == 1) {
            agreeStr.value = '已同意';
          }
          if (dataMap['status'] == 2) {
            agreeStr.value = '已忽略';
          }

          if (dataMap['status'] == 3) {
            agreeStr.value = '已失效';
          }

          refuseStr.value = BaseInfo()
              .formatTimestamp(dataMap['updateTime'], 'yyyy-MM-dd HH:mm');
        }
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getPendingDetail() {
    if (recordId.isEmpty) {
      return;
    }
    DioUtil()
        .get('${ORGApi.GETWAITDETAIL}/status/$stauts/record/$recordId', null,
            true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        if (stauts == 4) {
          Map dataMap = data['data'];
          logoStr = dataMap['orgLogo'];
          nameStr.value = dataMap['orgName'];
          contentStr = '${nameStr.value}邀请你成为外部联系人';

          //active 0已确认 1未处理 2已忽略 3已失效

          if (dataMap['active'] == 1) {
            buttonStatus.value = 0;
          } else {
            buttonStatus.value = 1;
            if (dataMap['active'] == 0) {
              agreeStr.value = '已确认';
            }
            if (dataMap['active'] == 2) {
              agreeStr.value = '已忽略';
            }

            if (dataMap['active'] == 3) {
              agreeStr.value = '已失效';
            }
            refuseStr.value = BaseInfo()
                .formatTimestamp(dataMap['createTime'], 'yyyy-MM-dd HH:mm');
          }
        }
        if (stauts == 2) {
          Map dataMap = data['data'];
          logoStr = dataMap['avatar'];
          nameStr.value = dataMap['name'];
          contentStr = dataMap['message'];
          if (dataMap['status'] == 0) {
            buttonStatus.value = 0;
            userId = dataMap['userId'];
            if (dataMap['orgId'] != null) {
              companyId = dataMap['orgId'];
            }
          } else {
            buttonStatus.value = 1;
            if (dataMap['status'] == 1) {
              agreeStr.value = '已同意';
            }
            if (dataMap['status'] == 2) {
              agreeStr.value = '已忽略';
            }

            if (dataMap['status'] == 3) {
              agreeStr.value = '已失效';
            }

            String timeStr = BaseInfo()
                .formatTimestamp(dataMap['createTime'], 'yyyy-MM-dd HH:mm');
            refuseStr.value = '处理人:${dataMap['handlerName']} $timeStr';
          }
        }
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

//处理外部联系人
  handExternal(int code) {
    Map param = {'code': code, 'id': recordId};
    DioUtil().post(ORGApi.POST_EXTERNAL_HAND, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        if (1 == code) {
          toast('已同意当前合作企业激活邀请!');
          //刷新公司数据
          Channel().invoke(Channel_Native_RereshPending, data);
          Channel().invoke(Channel_Native_RereshOrgData, data);
        } else {
          toast('已忽略当前激活邀请!');
        }
        getPendingDetail();
        eventBus.fire({'waitCount': 1});
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //同意企业申请
  agreeOrgJoin() {
    Map param = {
      'companyId': companyId,
      'recordId': recordId,
      'userId': userId
    };
    DioUtil().put(ORGApi.AGREEJOIN, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        getPendingDetail();
        eventBus.fire({'waitCount': 1});
        Channel().invoke(Channel_Native_RereshPending, data);
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //忽略企业申请
  refuseOrgJoin() {
    Map param = {
      'companyId': companyId,
      'recordId': recordId,
      'userId': userId
    };
    DioUtil().put(ORGApi.IGNORERECORD, param, true, () {}).then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        getPendingDetail();
        eventBus.fire({'waitCount': 1});
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //处理好友申请
  dealFriendApply(int code) {
    DioUtil()
        .get('${LoginApi.IM_FRIEND_APPLY}/$recordId/$code', null, true, () {})
        .then((data) async{
      if (data == null) return;
      if (data!['code'] == 1) {
        eventBus.fire({'waitCount': 1});
        //刷新好友数据
        Channel().invoke(Channel_Native_RereshPending, data);
        Channel().invoke(Channel_Native_RereshFriend, data);
        if (code == 1) {
          HomeController homeController = Get.find();
          await homeController.getFriendList();
          friendMap!['judgeFriend'] = 1;
          logger('=========friendMap===$friendMap');
          var session = await friendMap!.createSinglePageParam();
          await friendMap!.sendMsgAfterFriend();
          RouteHelper.routeTotag(
          ChatPage(tag: session.sessionId), Routes.IM_CHAGE_PAGE,
          arguments: session, binding: ChatBinding(tag: session.sessionId));
        }
        getFriendPendingDetail();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
