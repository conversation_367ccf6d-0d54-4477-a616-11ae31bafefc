import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../../../plugin/focus_detector/focus_detector.dart';
import '../../../../../common/channel/channel.dart';
import '../controllers/friend_apply_detail_controller.dart';
import '../../../../../common/config/config.dart';
import '../../../../../common/widgets/widgets.dart';
import 'package:extended_image/extended_image.dart';

class FriendApplyDetailView extends GetView<FriendApplyDetailController> {
  FriendApplyDetailView({Key? key}) : super(key: key);

  FriendApplyDetailController applyDetailController = Get.find();
  @override
  Widget build(BuildContext context) {
    return FocusDetector(
        onFocusGained: () {
          if (applyDetailController.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 1});
          }
        },
        onFocusLost: () {
          if (applyDetailController.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 0});
          }
        },
        child: GetBuilder(
            global: false,
            init: applyDetailController,
            builder: (logic) {
              return Scaffold(
                  backgroundColor: ColorConfig.backgroundColor,
                  extendBodyBehindAppBar: true,
                  appBar: PreferredSize(
                      preferredSize: const Size.fromHeight(44),
                      child: AppBar(
                        title: Text(
                          '申请详情',
                          style: const TextStyle(
                              color: ColorConfig.mainTextColor, fontSize: 16),
                        ),
                        centerTitle: true,
                        leading: IconButton(
                          onPressed: () {
                            if (applyDetailController.isNative.value) {
                              applyDetailController.onClose();
                              Channel().invoke(Channel_Native_Back, {});
                            } else {
                              Get.back();
                            }
                          },
                          icon: SizedBox(
                            width: 24,
                            height: 24,
                            child: Image.asset(
                                'assets/images/3.0x/pic_return.png'),
                          ),
                        ),
                        backgroundColor: Color(0x00FFFFFF),
                        elevation: 0,
                      )),
                  body: Column(
                    children: [
                      Container(
                          width: double.infinity,
                          height: DeviceUtils().width.value * 145 / 375,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                                fit: BoxFit.fitWidth,
                                image: AssetImage(
                                    'assets/images/3.0x/org_detail_back.png')),
                          )),
                      Column(
                        children: [
                          Container(
                            height: 0, //30为投诉
                            padding: EdgeInsets.only(left: 15, right: 15),
                            child: Text(
                              '',
                              textAlign: TextAlign.left,
                              style: TextStyle(color: Colors.red, fontSize: 13),
                            ), //对方曾被他人投诉，请核实身份再通过验证，谨防诈骗
                          ),
                          Container(
                            color: Colors.white,
                            width: double.infinity,
                            height: 136,
                            padding: EdgeInsets.fromLTRB(15, 30, 15, 30),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                InkWell(
                                  onTap: () {
                                    applyDetailController.tapAvatar();
                                  },
                                  child: Container(
                                  padding: const EdgeInsets.all(1),
                                  width: 76,
                                  height: 76,
                                  alignment: Alignment.centerLeft,
                                  decoration: BoxDecoration(
                                      image: DecorationImage(
                                          image: SettingWidget.backImageProvider(applyDetailController.logoStr)),
                                      borderRadius: BorderRadius.circular(38),
                                      border: Border.all(
                                          color: ColorConfig.lineColor,
                                          width: 1)),
                                ),
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                Expanded(
                                    child: Container(
                                  padding: EdgeInsets.only(top: 10, bottom: 10),
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    applyDetailController.nameStr.value,
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                        color: ColorConfig.mainTextColor,
                                        fontSize: 14),
                                  ),
                                ))
                              ],
                            ),
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            width: double.infinity,
                            height: 25,
                            padding: EdgeInsets.only(left: 15, right: 15),
                            child: Text(
                              '验证申请',
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                  color: ColorConfig.desTextColor,
                                  fontSize: 12),
                            ),
                          ),
                          Container(
                            alignment: Alignment.centerLeft,
                            color: Colors.white,
                            width: double.infinity,
                            constraints: BoxConstraints(minHeight: 60),
                            padding: EdgeInsets.only(
                                left: 15, right: 15, top: 10, bottom: 10),
                            child: Text(
                              applyDetailController.contentStr,
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            ),
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Offstage(
                            offstage: applyDetailController.type != 1,
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.only(left: 15, right: 15),
                              child: Text(
                                '同意之后，即可成为本企业的外部联系人员参与审批、任务等工作协作',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.desTextColor),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Container(
                            width: double.infinity,
                            height: 38,
                            padding: EdgeInsets.only(left: 25, right: 25),
                            child: CupertinoButton(
                                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                color:
                                    applyDetailController.buttonStatus.value ==
                                            0
                                        ? ColorConfig.themeCorlor
                                        : ColorConfig.backgroundColor,
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(5)),
                                pressedOpacity: 0.5,
                                child: Text(
                                  applyDetailController.buttonStatus.value == 0
                                      ? '同意'
                                      : applyDetailController.agreeStr.value,
                                  style: TextStyle(
                                      color: applyDetailController
                                                  .buttonStatus.value ==
                                              0
                                          ? ColorConfig.whiteColor
                                          : ColorConfig.mainTextColor,
                                      fontSize: 14),
                                ),
                                onPressed: () {
                                  if (applyDetailController
                                              .buttonStatus.value ==
                                          0 &&
                                      applyDetailController
                                          .nameStr.value.isNotEmpty) {
                                    if (applyDetailController.stauts == 4) {
                                      applyDetailController.handExternal(1);
                                    }
                                    if (applyDetailController.stauts == 2) {
                                      applyDetailController.agreeOrgJoin();
                                    }
                                    if (applyDetailController.stauts == 3) {
                                      applyDetailController.dealFriendApply(1);
                                    }
                                  }
                                }),
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Container(
                            margin: EdgeInsets.only(left: 25, right: 25),
                            width: double.infinity,
                            height: 38,
                            padding: EdgeInsets.all(1),
                            decoration: BoxDecoration(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(5)),
                                border: Border.all(
                                    color: applyDetailController
                                                .buttonStatus.value ==
                                            0
                                        ? ColorConfig.themeCorlor
                                        : ColorConfig.backgroundColor,
                                    width: 1)),
                            child: CupertinoButton(
                                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                color:
                                    applyDetailController.buttonStatus.value ==
                                            0
                                        ? ColorConfig.whiteColor
                                        : ColorConfig.backgroundColor,
                                pressedOpacity: 0.5,
                                child: Text(
                                  applyDetailController.buttonStatus.value == 0
                                      ? '忽略'
                                      : applyDetailController.refuseStr.value,
                                  style: TextStyle(
                                      color: applyDetailController
                                                  .buttonStatus.value ==
                                              0
                                          ? ColorConfig.themeCorlor
                                          : ColorConfig.desTextColor,
                                      fontSize: 14),
                                ),
                                onPressed: () {
                                  if (applyDetailController
                                              .buttonStatus.value ==
                                          0 &&
                                      applyDetailController
                                          .nameStr.value.isNotEmpty) {
                                    if (applyDetailController.stauts == 4) {
                                      applyDetailController.handExternal(2);
                                    }
                                    if (applyDetailController.stauts == 2) {
                                      applyDetailController.refuseOrgJoin();
                                    }
                                    if (applyDetailController.stauts == 3) {
                                      applyDetailController.dealFriendApply(2);
                                    }
                                  }
                                }),
                          )
                        ],
                      )
                    ],
                  ));
            }));
  }
}
