import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/friend_pending_model.dart';
import 'package:flutter_mixed/app/utils/http.dart';
import 'package:get/get.dart';

import '../../../../../common/widgets/widgets.dart';

class FriendPendingController extends GetxController {

  RxList dataList = [].obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    getFriendPendingList();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getFriendPendingList(){
    DioUtil().get('${ORGApi.GETWAITLIST}/3/1/999', null, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataDic = data['data'];
        List friendList = dataDic['friendTodoList'];
        dataList.value = friendList.map((item) => FriendPendingModel.fromJson(item)).toList();
      } else {
        toast('${data['msg']}');
      }
    });

  }


   agreeeBefriend(FriendPendingModel model){

    Map param = {'targetUserId':model.userId,'recordId':model.recordId};
    DioUtil().post(LoginApi.ABOUTFRIEND, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        getFriendPendingList();
      } else {
        toast('${data['msg']}');
      }
    });

  }
  
}
