import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/friend_pending_model.dart';

import 'package:get/get.dart';

import '../controllers/friend_pending_controller.dart';

class FriendPendingView extends GetView<FriendPendingController> {
  const FriendPendingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.whiteColor,
      extendBodyBehindAppBar: true,
      appBar: TitleBar().backAppbar(context, '新的好友', false, [], onPressed: (){
        Get.back();
      }),
      body: ListView.builder(

        itemCount: controller.dataList.length,
        itemBuilder: (context,index){
          FriendPendingModel model = controller.dataList[index];
          return getWidgetForPending(model);
      }),
    ));
  }

  getWidgetForPending(FriendPendingModel model){
    List statusList = ['同意','已同意','已忽略','已过期'];
    return Container(
      width: double.infinity,
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [

          ImageLoader(url: model.avatar,
            width: 40,
            height: 40,
            isCircle: true,
          ),

          SizedBox(
            width: 10,
          ),
          Expanded(child: Column(
            children: [
              Container(
                width: double.infinity,
                height: 22,
                child: Text(model.name,style: TextStyle(fontSize: 14,color: ColorConfig.mainTextColor),),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: Text(model.message,style: TextStyle(fontSize: 14,color: ColorConfig.desTextColor),)),
                  SizedBox(
                    width: 10,
                  ),
                  InkWell(
                    onTap: () {
                      if(model.status == 0){
                        controller.agreeeBefriend(model);
                      }
                    },
                    child: Container(
                    alignment: Alignment.center,
                    width: 45,
                    height: 22,
                    decoration: BoxDecoration(
                      border: Border.all(width: 1,color:model.status==0?ColorConfig.themeCorlor:ColorConfig.whiteColor),
                      borderRadius: BorderRadius.circular(4)
                    ),
                    child: Text(statusList[model.status],style: TextStyle(fontSize: 14,color:model.status==0? ColorConfig.themeCorlor:ColorConfig.desTextColor),),
                  ),
                  )
                ],
              )
            ],
          ))
        ],
      ),
    );
  }
}
