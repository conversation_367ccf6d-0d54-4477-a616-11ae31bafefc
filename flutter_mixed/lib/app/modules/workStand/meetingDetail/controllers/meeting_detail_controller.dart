import 'dart:async';
import 'dart:convert';
import 'dart:ui' as ui;

import 'package:clipboard/clipboard.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/dialog/msg-dialog.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/modules/helper/photo_manager.dart';
import 'package:flutter_mixed/app/modules/workStand/dialog/meeting_share_dialog.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';
import '../../models/meeting_list_model.dart';

class MeetingDetailController extends GetxController {

  RxInt type = 0.obs; //0二维码 1详情 2添加到会议列表
  StreamSubscription? subscription;
  RxBool isNative = false.obs; //是否是原生跳转
  String meetingId = '';
  MeetingListModel? listModel;
  final GlobalKey codeKey = GlobalKey();
  String showMeetingId = '';

  bool isShowShare = false;
  bool isShowMore = false;

  bool isAddList = false; //是否添加到了会议列表
  bool isShowAddButton = false; //是否显示添加按钮

  bool isReceiveMeetingDeleted = false;
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      if (Get.arguments['meetingId'] != null) {
        meetingId = Get.arguments['meetingId'];
      }
      if (Get.arguments['type'] != null) {
        type.value = Get.arguments['type'];
      }
    }
    subscription = eventBus.on<Map>().listen((event) {
      logger('evetn-------$event');
      if (event['native'] == 1) {
        if (event['route'] == Routes.MEETING_DETAIL) {
          isNative.value = true;
          Map? nativeArgument;
          if (event['arguments'] is String) {
            nativeArgument = jsonDecode(event['arguments']);
          } else {
            nativeArgument = event['arguments'];
          }
          meetingId = nativeArgument!['meetingId'];
          dynamic paramType = nativeArgument['type'];
          if (paramType is int) {
            type.value = paramType;
          } else if (paramType is double) {
            type.value = paramType.toInt();
          }
          if (type.value != 1) {
            isShowShare = false;
            isShowMore = false;
          }
          update();
          if (meetingId.isNotEmpty) {
            getMeetingDetail(type.value == 1);
          }
        }
      }
      if (event['receiveMeetingDeleted'] != null) {
        if (isReceiveMeetingDeleted) return;
        if (!isReceiveMeetingDeleted) {
          isReceiveMeetingDeleted = true;
        }
        String deleteId = event['receiveMeetingDeleted'];
        if (deleteId == meetingId) {
          if (isNative.value) {
            onClose();

            Channel().invoke(Channel_Native_Back, {});
            logger('我执行了Channel().invoke(Channel_Native_Back');
          } else {
            Get.back();
          }
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (meetingId.isNotEmpty) {
      getMeetingDetail(true);
    }
  }

  @override
  void onClose() {
    super.onClose();
    subscription!.cancel();
  }

  getMeetingDetail(bool isRequestSetting) {
    DioUtil().get('${MeetingApi.GET_MEETING_DETAIL}/$meetingId', null, true,
        () {
    }).then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map<String, dynamic> dataMap = data['data'];
        listModel = MeetingListModel.fromJson(dataMap);
        if (type.value == 2) {
          if (listModel!.status == 1) {
            logger('会议已结束');
            MsgDiaLog('会议提示', '此会议已经结束', '取消', '返回', () {
              Navigator.of(Get.context!).pop();
            }, () {
              if (isNative.value) {
                Navigator.of(Get.context!).pop();
                onClose();
                Channel().invoke(Channel_Native_Back, {});
              } else {
                Get.back();
              }
            }).show();
            return;
          } else {
            //判断是否添加到会议列表
            if (listModel!.isList == 1) {
              isAddList = true;
            } else {
              isShowAddButton = true;
            }
          }
        }
        meetingId = listModel!.meetingId;
        Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
        String myUserId = tokenInfo['userId'];
        if (listModel!.hostUserId == myUserId) {
          if (listModel!.createTime > DateTime.now().millisecondsSinceEpoch) {
            isShowMore = true;
          } else {
            isShowMore = false;
          }
          isShowShare = true;
        } else {
          if (isRequestSetting) {
            getMeetingSetting();
          }
        }
        String tempMeetingStr = '';
        for (var i = 0; i < meetingId.length; i++) {
          String character = meetingId.getRange(i, i + 1);
          if (i != meetingId.length - 1 && (i + 1) % 3 == 0) {
            tempMeetingStr = '$tempMeetingStr$character ';
          } else {
            tempMeetingStr = '$tempMeetingStr$character';
          }
        }
        showMeetingId = tempMeetingStr;
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getMeetingSetting() {
    DioUtil().get('${MeetingApi.GET_MEETING_SETTING}/$meetingId', null, true,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map<String, dynamic> dataMap = data['data'];
        isShowShare = dataMap['inviter'] == 1 ? true : false;

        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //点击了取消会议
  didTapDeleteMeeting() async {
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    String myUserId = tokenInfo['userId'];
    if (myUserId == listModel!.hostUserId) {
      MsgDiaLog('取消会议', '你是会议的发起人，取消后其他人员将无法入会，确认取消吗？', '返回', '取消会议', () {
        Navigator.of(Get.context!).pop();
      }, () {
        Navigator.of(Get.context!).pop();
        _sureDeleteMeeting();
      }, rightColor: ColorConfig.deleteCorlor)
          .show();
    }
  }

  _sureDeleteMeeting() {
    DioUtil().delete(
        '${MeetingApi.MEETING_CANCEL}/${listModel!.meetingId}', null, true,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        if (isNative.value) {
          onClose();
          Channel().invoke(Channel_Native_Back, {});
        } else {
          Get.back(result: 1);
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  checkIsHaveUnFinishedMeeting() {
    DioUtil().get('${MeetingApi.MEETING_REMOVE_USER}/$meetingId', null, true,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        dynamic dataCode = data['data'];
        if (dataCode is int) {
          if (dataCode == 700) {
            MsgDiaLog('', '你有会议正在进行，如想进入新的会议，请先结束正在进行的会议', '确定', '', () {
              Navigator.of(Get.context!).pop();
            }, () {})
                .show();
          } else {
            getMeetingSign();
          }
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getMeetingSign() {
    DioUtil().get('${MeetingApi.GET_MEETING_SIGN}/$meetingId', null, true,
        () {
    }).then((data) {
      if (data == null) return;

      if (data != null && data['code'] == 1) {
        Map dataMap = data['data'];
        dataMap['isVoice'] = 0;
        dataMap['isVideo'] = 0;
        dataMap['isSpeaker'] = 1;
        Channel().invoke(Channel_jumpMeeting, dataMap);
        eventBus.fire({'refreshMeetingList': 1});
      } else {
        toast('${data!['msg']}');
      }
    });
  }

  //点击了分享按钮
  showShareDialog() async {
    if (listModel == null) return;
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    String myUserName = tokenInfo['name'];
    String linkUrl =
        '${Host.WEBHOST}/${Host.MEETINGLINK}/?meetingId=$meetingId';
    MeetingShareDialog().show((onTapIndex) {
      logger('点击了$onTapIndex');
      String startStr =
          BaseInfo().formatTimestamp(listModel!.createTime, 'yyyy/MM/dd HH:mm');
      String endStr =
          BaseInfo().formatTimestamp(listModel!.finishTime, 'yyyy/MM/dd HH:mm');
      List startArray = startStr.split(' ');
      List endArray = endStr.split(' ');
      if (startArray.first == endArray.first) {
        endStr = endArray.last;
      }
      if (onTapIndex == 0) {
        //微信分享 // \n点击链接进入会议：$linkUrl\n\n
        String linkContent =
            '$myUserName 邀请你参加担当会议\n会议主题：${listModel!.topic}\n会议时间：$startStr-$endStr\n担当会议：${listModel!.meetingId}\n';
        if (listModel!.password.isNotEmpty) {
          linkContent = '$linkContent会议密码：${listModel!.password}\n';
        }
        FlutterClipboard.copy(linkContent).then((value) {
          toast('复制成功');
        });
        // BaseInfo().shareTextToWechat(meetingId);
        // String description = '会议已经开始，请尽快加入会议';
        // if (DateTime.now().millisecondsSinceEpoch < listModel!.createTime) {
        //   description = '会议时间:$startStr-$endStr\n会议ID:$showMeetingId';
        // }

        // BaseInfo().shareLinkToWechat(linkUrl, listModel!.topic, description,
        //     'assets/images/3.0x/about_icon.png');
      }
      // if (onTapIndex == 1) {
      //   //复制链接

      //   String linkContent =
      //       '$myUserName 邀请你参加担当会议\n会议主题：${listModel!.topic}\n会议时间：$startStr-$endStr\n\n点击链接进入会议：$linkUrl\n\n担当会议：${listModel!.meetingId}\n';
      //   if (listModel!.password.isNotEmpty) {
      //     linkContent = '$linkContent会议密码：${listModel!.password}\n';
      //   }
      //   FlutterClipboard.copy(linkContent).then((value) {
      //     toast('复制成功');
      //   });
      // }
      // if (onTapIndex == 2) {
      //   //二维码
      //   Get.toNamed(Routes.MEETING_DETAIL,
      //       arguments: {'meetingId': meetingId, 'type': 0},
      //       preventDuplicates: false);
      // }
    });
  }

  //点击了取消会议
  didClickCancelMeeting(context) {
    SettingWidget().showCupertinoActionSheetForPage(context, ['取消会议'],
        (int value) {
      didTapDeleteMeeting();
    });
  }

  //将会议添加到会议列表
  meetingAddList() {
    DioUtil().post('${MeetingApi.MEETING_ADD_LIST}/$meetingId', null, true,
        () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        isAddList = true;
        isShowAddButton = false;
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //跳转密码页面
  goToVerifyPwd(int type) async {
    var result = await Get.toNamed(Routes.MEETING_JOIN,
        arguments: {'meetingId': meetingId, 'type': type},
        preventDuplicates: false);
    if (result == 1) {
      isAddList = true;
      isShowAddButton = false;
      update();
    }
  }

  // 保存到手机
  savePicture() async {
    RenderRepaintBoundary boundary =
        codeKey.currentContext?.findRenderObject() as RenderRepaintBoundary;
    ui.Image image =
        await boundary.toImage(pixelRatio: ui.window.devicePixelRatio);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();
    // 保存到相册
    final result = await PhotoHelper.saveToGallery(pngBytes);
    String alertDoc = result ? "保存成功" : "保存失败";
    toast(alertDoc);
  }
}
