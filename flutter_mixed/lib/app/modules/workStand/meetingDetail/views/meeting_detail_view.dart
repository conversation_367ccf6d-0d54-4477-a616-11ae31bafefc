import 'dart:io';

import 'package:clipboard/clipboard.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../../../plugin/focus_detector/focus_detector.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/config/config.dart';
import '../../../../common/config/string_const.dart';
import '../../../../routes/app_pages.dart';
import '../controllers/meeting_detail_controller.dart';

class MeetingDetailView extends GetView<MeetingDetailController> {
  MeetingDetailView({Key? key}) : super(key: key);
  final meetingDetailController = Get.find<MeetingDetailController>();
  @override
  Widget build(BuildContext context) {
    return FocusDetector(
        onFocusGained: () {
          if (meetingDetailController.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 1});
          }
        },
        onFocusLost: () {
          if (meetingDetailController.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 0});
          }
        },
        child: GetBuilder(
            global: false,
            init: meetingDetailController,
            builder: (logic) {
              return Scaffold(
                  backgroundColor: ColorConfig.mybackgroundColor,
                  appBar: TitleBar().backAppbar(
                      context,
                      meetingDetailController.type.value == 0
                          ? '会议二维码'
                          : meetingDetailController.type.value == 1
                              ? '会议详情'
                              : meetingDetailController.type.value == 2
                                  ? '担当会议'
                                  : '',
                      false,
                      meetingDetailController.type.value == 1
                          ? meetingDetailController.isShowMore &&
                                  meetingDetailController.isShowShare
                              ? [
                                  Container(
                                    width: 36,
                                    height: 24,
                                    child: CupertinoButton(
                                        padding: const EdgeInsets.fromLTRB(
                                            12, 0, 0, 0),
                                        onPressed: () {
                                          meetingDetailController
                                              .showShareDialog();
                                        },
                                        child: Image.asset(
                                            "assets/images/3.0x/meeting_share.png")),
                                  ),
                                  Container(
                                    width: 52,
                                    height: 24,
                                    child: CupertinoButton(
                                        padding: const EdgeInsets.fromLTRB(
                                            12, 0, 12, 0),
                                        onPressed: () {
                                          meetingDetailController
                                              .didClickCancelMeeting(context);
                                        },
                                        child: Image.asset(
                                            "assets/images/3.0x/meeting_more.png")),
                                  )
                                ]
                              : meetingDetailController.isShowMore
                                  ? [
                                      Container(
                                        width: 52,
                                        height: 24,
                                        child: CupertinoButton(
                                            padding: const EdgeInsets.fromLTRB(
                                                12, 0, 12, 0),
                                            onPressed: () {
                                              meetingDetailController
                                                  .didClickCancelMeeting(
                                                      context);
                                            },
                                            child: Image.asset(
                                                "assets/images/3.0x/meeting_more.png")),
                                      )
                                    ]
                                  : meetingDetailController.isShowShare
                                      ? [
                                          Container(
                                            width: 52,
                                            height: 24,
                                            child: CupertinoButton(
                                                padding:
                                                    const EdgeInsets.fromLTRB(
                                                        12, 0, 12, 0),
                                                onPressed: () {
                                                  meetingDetailController
                                                      .showShareDialog();
                                                },
                                                child: Image.asset(
                                                    "assets/images/3.0x/meeting_share.png")),
                                          )
                                        ]
                                      : []
                          : [], onPressed: () {
                    if (meetingDetailController.isNative.value) {
                      meetingDetailController.onClose();
                      Channel().invoke(Channel_Native_Back, {});
                    } else {
                      Get.back();
                    }
                  }),
                  body: meetingDetailController.listModel == null
                      ? Container()
                      : Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Column(
                              children: [
                                16.gap,
                                ClipPath(
                                  clipper: MyCliper(
                                      157,
                                      meetingDetailController.type.value == 0
                                          ? 8
                                          : 0,
                                      16),
                                  child: Container(
                                    width: double.infinity,
                                    margin: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    decoration: BoxDecoration(
                                        image: const DecorationImage(
                                            fit: BoxFit.cover,
                                            image: AssetImage(
                                                'assets/images/3.0x/meeting_detail_top.png')),
                                        borderRadius: meetingDetailController
                                                    .type.value ==
                                                0
                                            ? const BorderRadius.only(
                                                topLeft: Radius.circular(16),
                                                topRight: Radius.circular(16))
                                            : BorderRadius.circular(16)),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        16.gap,
                                        Container(
                                          alignment: Alignment.center,
                                          width: double.infinity,
                                          padding: const EdgeInsets.only(
                                              left: 16, right: 16),
                                          height: 22,
                                          child: Text(
                                            meetingDetailController
                                                .listModel!.topic,
                                            overflow: TextOverflow.ellipsis,
                                            style: const TextStyle(
                                                fontSize: 14,
                                                color:
                                                    ColorConfig.mainTextColor),
                                          ),
                                        ),
                                        Container(
                                          padding: const EdgeInsets.only(
                                              left: 16, right: 16),
                                          child: Offstage(
                                            offstage: meetingDetailController
                                                    .type.value ==
                                                1,
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                10.gap,
                                                InkWell(
                                                  onTap: () => _copyMeetingId(),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Container(
                                                        height: 22,
                                                        child: Text(
                                                          meetingDetailController
                                                              .showMeetingId,
                                                          style: const TextStyle(
                                                              fontSize: 16,
                                                              color: ColorConfig
                                                                  .themeCorlor),
                                                        ),
                                                      ),
                                                      4.gap,
                                                      SizedBox(
                                                        width: 24,
                                                        height: 24,
                                                        child: Image.asset(
                                                            'assets/images/3.0x/meeting_copyLink24.png'),
                                                      )
                                                    ],
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                        10.gap,
                                        Container(
                                          padding: const EdgeInsets.only(
                                              left: 16, right: 16),
                                          width: double.infinity,
                                          height: 59,
                                          child: Row(
                                            children: [
                                              Column(
                                                children: [
                                                  4.gap,
                                                  Container(
                                                    alignment: Alignment.center,
                                                    height: 26,
                                                    child: Text(
                                                      meetingDetailController
                                                          .listModel!
                                                          .createTimeStr,
                                                      style: const TextStyle(
                                                          fontSize: 22,
                                                          color: ColorConfig
                                                              .mainTextColor),
                                                    ),
                                                  ),
                                                  4.gap,
                                                  Container(
                                                    height: 17,
                                                    child: Text(
                                                      meetingDetailController
                                                          .listModel!
                                                          .createDate,
                                                      style: const TextStyle(
                                                          fontSize: 12,
                                                          color: ColorConfig
                                                              .desTextColor),
                                                    ),
                                                  )
                                                ],
                                              ),
                                              Flexible(
                                                  child: Column(
                                                children: [
                                                  Expanded(
                                                      flex: 1,
                                                      child: Container(
                                                        alignment:
                                                            Alignment.center,
                                                        child: Text(
                                                          meetingDetailController
                                                              .listModel!
                                                              .statusString,
                                                          style: TextStyle(
                                                              fontSize: 10,
                                                              color: meetingDetailController
                                                                  .listModel!
                                                                  .textColor),
                                                        ),
                                                      )),
                                                  Expanded(
                                                      flex: 1,
                                                      child: Container(
                                                        alignment:
                                                            Alignment.center,
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 8,
                                                                right: 8),
                                                        child: Text(
                                                          meetingDetailController
                                                              .listModel!
                                                              .doneTimeString,
                                                          style: const TextStyle(
                                                              fontSize: 14,
                                                              color: ColorConfig
                                                                  .themeCorlor),
                                                        ),
                                                      )),
                                                  Expanded(
                                                      flex: 1,
                                                      child: Container()),
                                                ],
                                              )),
                                              Column(
                                                children: [
                                                  4.gap,
                                                  Container(
                                                    alignment: Alignment.center,
                                                    height: 26,
                                                    child: Text(
                                                      meetingDetailController
                                                          .listModel!
                                                          .endTimeStr,
                                                      style: const TextStyle(
                                                          fontSize: 22,
                                                          color: ColorConfig
                                                              .mainTextColor),
                                                    ),
                                                  ),
                                                  4.gap,
                                                  Container(
                                                    height: 17,
                                                    child: Text(
                                                      meetingDetailController
                                                          .listModel!.endDate,
                                                      style: const TextStyle(
                                                          fontSize: 12,
                                                          color: ColorConfig
                                                              .desTextColor),
                                                    ),
                                                  )
                                                ],
                                              )
                                            ],
                                          ),
                                        ),
                                        16.gap,
                                        Offstage(
                                            offstage: !meetingDetailController
                                                .isAddList,
                                            child: InkWell(
                                              onTap: () {
                                                Get.toNamed(Routes.MEETING_LIST,
                                                    preventDuplicates: false);
                                              },
                                              child: Container(
                                                color: ColorConfig.whiteColor,
                                                width: double.infinity,
                                                height: 50,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    SizedBox(
                                                      width: 16,
                                                      height: 16,
                                                      child: Image.asset(
                                                          'assets/images/3.0x/login_selected.png'),
                                                    ),
                                                    8.gap,
                                                    Container(
                                                      child: const Text(
                                                        '已添加，查看会议列表',
                                                        style: TextStyle(
                                                            fontSize: 12,
                                                            color: ColorConfig
                                                                .themeCorlor),
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ))
                                      ],
                                    ),
                                  ),
                                ),
                                meetingDetailController.type.value == 1
                                    ? 16.gap
                                    : 0.gap,
                                meetingDetailController.type.value == 0
                                    ? RepaintBoundary(
                                        key: meetingDetailController.codeKey,
                                        child: ClipPath(
                                          clipper: MyCliper(0, 8, 16),
                                          child: Container(
                                            width:
                                                DeviceUtils().width.value - 32,
                                            height: 359,
                                            decoration: const BoxDecoration(
                                                color: ColorConfig.whiteColor,
                                                borderRadius: BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(16),
                                                    bottomRight:
                                                        Radius.circular(16))),
                                            alignment: Alignment.center,
                                            margin: const EdgeInsets.only(
                                                left: 16, right: 16),
                                            child: Container(
                                              padding: const EdgeInsets.only(
                                                  top: 50),
                                              child: Column(
                                                children: [
                                                  QrImageView(
                                                    data:
                                                        '${Host.WEBHOST}/${Host.MEETINGLINK}/?meetingId=${meetingDetailController.meetingId}',
                                                    size: 229,
                                                    embeddedImage: const AssetImage(
                                                        'assets/images/3.0x/about_icon.png'),
                                                  ),
                                                  8.gap,
                                                  Container(
                                                    height: 22,
                                                    child:  const Text(
                                                      '请使用手机端「${appName}APP」扫码入会',
                                                      style: TextStyle(
                                                          fontSize: 14,
                                                          color: ColorConfig
                                                              .mainTextColor),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      )
                                    : meetingDetailController.type.value == 1
                                        ? Container(
                                            margin: const EdgeInsets.only(
                                                left: 16, right: 16),
                                            padding: const EdgeInsets.only(
                                                left: 16, right: 16),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                color: ColorConfig.whiteColor),
                                            child: _createSender())
                                        : Container()
                              ],
                            ),

                            // meetingDetailController.type.value == 0
                            //     ? 20.gap
                            //     : 120.gap,

                            Expanded(child: Container()),

                            Offstage(
                              offstage:
                                  meetingDetailController.type.value == 2 &&
                                      !meetingDetailController.isShowAddButton,
                              child: InkWell(
                                onTap: () async {
                                  if (meetingDetailController.type.value == 0) {
                                    meetingDetailController.savePicture();
                                  }
                                  if (meetingDetailController.type.value == 1) {
                                    meetingDetailController
                                        .checkIsHaveUnFinishedMeeting();
                                  }
                                  if (meetingDetailController.type.value == 2) {
                                    if (meetingDetailController
                                        .listModel!.password.isEmpty) {
                                      meetingDetailController.meetingAddList();
                                    } else {
                                      meetingDetailController.goToVerifyPwd(2);
                                    }
                                  }
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      color: ColorConfig.themeCorlor,
                                      borderRadius: BorderRadius.circular(4)),
                                  alignment: Alignment.center,
                                  height: 44,
                                  margin: const EdgeInsets.only(
                                      left: 16, right: 16),
                                  child: Text(
                                    meetingDetailController.type.value == 0
                                        ? '保存至相册'
                                        : meetingDetailController.type.value ==
                                                1
                                            ? '加入会议'
                                            : meetingDetailController
                                                        .type.value ==
                                                    2
                                                ? '添加到会议列表'
                                                : '',
                                    style: const TextStyle(
                                        fontSize: 16,
                                        color: ColorConfig.whiteColor),
                                  ),
                                ),
                              ),
                            ),
                            5.gap,
                            Offstage(
                              offstage: meetingDetailController.type.value != 2,
                              child: InkWell(
                                onTap: () async {
                                  if (meetingDetailController
                                      .listModel!.password.isEmpty) {
                                    meetingDetailController
                                        .checkIsHaveUnFinishedMeeting();
                                  } else {
                                    meetingDetailController.goToVerifyPwd(1);
                                  }
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          width: 1,
                                          color: ColorConfig.themeCorlor),
                                      color: ColorConfig.whiteColor,
                                      borderRadius: BorderRadius.circular(4)),
                                  alignment: Alignment.center,
                                  height: 44,
                                  margin: const EdgeInsets.only(
                                      left: 16, right: 16),
                                  child: const Text(
                                    '加入会议',
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: ColorConfig.themeCorlor),
                                  ),
                                ),
                              ),
                            ),

                            25.gap
                          ],
                        ));
            }));
  }

  // 不建议 滚动布局嵌套使用，使用后需要约束宽高
  Widget _createSender() {
    List titleList = ['发起人', '会议号', '会议密码'];
    List nameList = [
      meetingDetailController.listModel!.hostName,
      meetingDetailController.showMeetingId,
      meetingDetailController.listModel!.password
    ];

    return Container(
      height: 150,
      child: ListView.builder(
          itemCount: nameList.length,
          itemBuilder: (ctx, i) {
            return Container(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.only(top: 8, bottom: 8),
                    height: 49,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              width: 80,
                              child: Text(
                                titleList[i],
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.mainTextColor),
                              ),
                            ),
                            Flexible(
                                child: GestureDetector(
                              onTap: () {
                                if (i == 1) {
                                  _copyMeetingId();
                                }
                              },
                              child: Container(
                                child: Text(
                                  nameList[i],
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                ),
                              ),
                            )),
                            8.gap,
                            Offstage(
                              offstage: i != 0,
                              child: Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    image: DecorationImage(
                                        image: SettingWidget.backImageProvider(meetingDetailController.listModel?.avatar))),
                              ),
                            )
                          ],
                        )),
                        i == 1
                            ? SizedBox(
                                width: 24,
                                height: 24,
                                child: GestureDetector(
                                  onTap: () => _copyMeetingId(),
                                  child: Image.asset(
                                      'assets/images/3.0x/meeting_copyLink24.png'),
                                ),
                              )
                            : Container()
                      ],
                    ),
                  ),
                  Offstage(
                    offstage: i == titleList.length - 1,
                    child: const Divider(
                      height: 1,
                      color: ColorConfig.backgroundColor,
                    ),
                  )
                ],
              ),
            );
          }),
    );
  }

  _copyMeetingId() {
    FlutterClipboard.copy(meetingDetailController.meetingId).then((value) {
      toast('复制成功');
    });
  }
}
