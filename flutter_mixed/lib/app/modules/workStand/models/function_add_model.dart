class FunctionAddModel {
  String? moduleInfo;
  String? orgId;
  String? userId;

  FunctionAddModel({this.moduleInfo, this.orgId, this.userId});

  FunctionAddModel.fromJson(Map<String, dynamic> json) {
    moduleInfo = json['moduleInfo'] ?? '[]';
    orgId = json['orgId'] ?? '';
    userId = json['userId'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['moduleInfo'] = moduleInfo;
    data['orgId'] = orgId;
    data['userId'] = userId;
    return data;
  }
}
