class CommentModel {
  String? approveId;
  String? commentId;
  String content = ''; //需要选择的节点此数据必为空
  int createTime = 0;
  Map user = {}; //0不需要选择 1需要

  CommentModel({this.commentId});

  CommentModel.fromJson(Map<String, dynamic> json) {
    approveId = json['approveId'];
    commentId = json['commentId'];
    content = json['content'];
    createTime = json['createTime'];
    user = json['user'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['approveId'] = approveId;
    data['commentId'] = commentId;
    data['content'] = content;
    data['createTime'] = createTime;
    data['user'] = user;
  

    return data;
  }
}