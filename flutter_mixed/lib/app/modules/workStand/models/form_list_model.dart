import 'package:flutter_mixed/app/modules/workStand/models/form_model.dart';

class FormListModel {
  String modelFormId = '';
  String modelFormName = '';
  List widgetList = [];
  int status = 0;//1可填写0不可填写
  FormListModel(this.modelFormId);
  FormListModel.fromJson(Map<String, dynamic> json) {
    modelFormId = json['modelFormId'];
    modelFormName = json['modelFormName'];
    status = json['status'];
    List mapList = json['widgetList'];
    widgetList = mapList.map((e) => FormWidetModel.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['modelFormId'] = modelFormId;
    data['modelFormName'] = modelFormName;
    data['widgetList'] = widgetList;
    data['status'] = status;
    return data;
  }
}
