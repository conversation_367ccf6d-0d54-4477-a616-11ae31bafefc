import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../common/base_info/info.dart';

class FormWidetModel {
  String? widgetId;
  String? title;
  String? prompt;
  String? required;
  int? type; //1.文本类型；2.长文本类型；3.时间点类型；4.时间段类型；5.时间长度类型；6.单选框类型；
  //7.多选框类型； 8.人员选择类型； 9.关键字选择类型；10图片类型；11.地点类型；12.文件类型；13考勤审批套件时间选择14城市选择15、4.0版时间长度类
  //16、4.0版单选框174.0版多选框18.4.0版关键字选择类型
  int? createTime;
  String? suffix;
  int? wordLimit;
  int? keyBoard;
  int? dateChoose;
  /** 时间的选择:0.代表所有；1.代表天；2.代表时分 **/
  int? addressChoose;

  ///** 是否获允许手动定位:0.否；1.是 **/
  int? nowChoose;
  String? secTitle;
  String? secPrompt;
  int? chooseCount;
  String? chooseSort;
  int? personType;
  int? wordType;
  /** 文字类型:0.姓名类；1.职位类；2.部门类 **/
  int? autoDept;
  int? status;
  String? thirdTitle;
  int? rule;
  int? isWidgetmodel;
  String? modelKey;
  int? maxNum;
  int? isLimit;
  int? dayNumber;
  String? limitRemind;
  List? chooseFrameList;

  //自定义字段
  String showContent = ''; //展示的数据
  String content = ''; //传给后台的数据
  List contentList = []; //用于传值
  List fileList = []; //用于展示
  /** 经度  时间段代表开始时间**/
  String longitudeStr = '';

  /** 维度 时间段代表结束时间**/
  String latitudeStr = '';

  /** 地址信息 **/
  String address = '';

  /** 定位信息 **/
  String positioning = '';

  String modelFormName = ''; //表单名称
  int modelType = 1; //1审批 2办理
  Map? workUser; //办理人

  int isDone = 0; //是否填写完成

  ChinaTextEditController? msgController;
  List<ChinaTextEditController> textECList = [];
  List<FocusNode> focusNodeList = [];
  FocusNode focusNode = FocusNode();

  FormWidetModel(
      {this.widgetId,
      this.title,
      this.prompt,
      this.required,
      this.type,
      this.createTime,
      this.suffix,
      this.wordLimit,
      this.keyBoard,
      this.dateChoose,
      this.addressChoose,
      this.nowChoose,
      this.secTitle,
      this.secPrompt,
      this.chooseCount,
      this.chooseSort,
      this.personType,
      this.wordType,
      this.autoDept,
      this.status,
      this.thirdTitle,
      this.rule,
      this.isWidgetmodel,
      this.modelKey,
      this.maxNum,
      this.isLimit,
      this.dayNumber,
      this.limitRemind,
      this.chooseFrameList});

  FormWidetModel.fromJson(Map<String, dynamic> json, {int isCache = 0}) {
    widgetId = json['widgetId'];
    title = json['title'] ?? '';
    prompt = json['prompt'] ?? '';
    required = json['required'] ?? '';
    type = json['type'] ?? 1;
    createTime = json['createTime'] ?? 0;
    suffix = json['suffix'] ?? '';
    wordLimit = json['wordLimit'] ?? 0;
    keyBoard = json['keyBoard'] ?? 0;
    dateChoose = json['dateChoose'] ?? 0;
    addressChoose = json['addressChoose'] ?? 0;
    nowChoose = json['nowChoose'] ?? 0;
    secTitle = json['secTitle'] ?? '';
    secPrompt = json['secPrompt'] ?? '';
    chooseCount = json['chooseCount'] ?? 0;
    chooseSort = json['chooseSort'] ?? '';
    personType = json['personType'] ?? 0;
    wordType = json['wordType'] ?? 0;
    autoDept = json['autoDept'] ?? 0;
    status = json['status'] ?? 0;
    thirdTitle = json['thirdTitle'] ?? '';
    rule = json['rule'] ?? 0;
    isWidgetmodel = json['isWidgetmodel'] ?? 0;
    modelKey = json['modelKey'] ?? '0';
    maxNum = json['maxNum'] ?? 0;
    isLimit = json['isLimit'] ?? 0;
    dayNumber = json['dayNumber'] ?? 0;
    limitRemind = json['limitRemind'] ?? '';
    chooseFrameList = json['chooseFrameList'] ?? [];

    if (json['content'] == null) {
      content = '';
    } else {
      content = json['content'];
    }

    if (json['isDone'] == null) {
      isDone = 0;
    } else {
      isDone = json['isDone'];
    }

    if (type == 13 || type == 19 || type == 20) {
      if (isCache == 1) {
        contentList = json['contentList'] ?? [];
        if (contentList.isNotEmpty) {
          textECList.clear();
          focusNodeList.clear();
          for (var i = 0; i < contentList.length; i++) {
            ChinaTextEditController textEditingController =
                ChinaTextEditController();
            textEditingController.text = contentList[i];
            textECList.add(textEditingController);
            FocusNode node = FocusNode();
            focusNodeList.add(node);
          }
        }
      } else {
        contentList = [''];
        ChinaTextEditController textEditingController =
            ChinaTextEditController();
        textECList = [textEditingController];
        FocusNode node = FocusNode();
        focusNodeList = [node];
      }
    } else {
      contentList = [];
    }

    if (type == 5 || type == 15) {
      if (dateChoose == 0) {
        contentList = [0, 0, 0];
      }
      if (dateChoose == 1) {
        contentList = [0];
      }
      if (dateChoose == 2) {
        contentList = [0, 0];
      }
    }
    if (isCache == 1) {
      contentList = json['contentList'] ?? [];
      showContent = json['showContent'] ?? '';
      longitudeStr = json['longitudeStr'] ?? '';
      latitudeStr = json['latitudeStr'] ?? '';
      address = json['address'] ?? '';
      positioning = json['positioning'] ?? '';
    }
  }

  Map<String, dynamic> toJson({int isCache = 0}) {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['widgetId'] = widgetId;
    data['title'] = title;
    data['prompt'] = prompt;
    data['required'] = required;
    data['type'] = type;
    data['createTime'] = createTime;
    data['suffix'] = suffix;
    data['wordLimit'] = wordLimit;
    data['keyBoard'] = keyBoard;
    data['dateChoose'] = dateChoose;
    data['addressChoose'] = addressChoose;
    data['nowChoose'] = nowChoose;
    data['secTitle'] = secTitle;
    data['secPrompt'] = secPrompt;
    data['chooseCount'] = chooseCount;
    data['chooseSort'] = chooseSort;
    data['personType'] = personType;
    data['wordType'] = wordType;
    data['autoDept'] = autoDept;
    data['status'] = status;
    data['thirdTitle'] = thirdTitle;
    data['rule'] = rule;
    data['isWidgetmodel'] = isWidgetmodel;
    data['modelKey'] = modelKey;
    data['maxNum'] = maxNum;
    data['isLimit'] = isLimit;
    data['dayNumber'] = dayNumber;
    data['limitRemind'] = limitRemind;

    //data['chooseFrameList'] = chooseFrameList;

    isDone = 0;
    if (type == 4) {
      content = jsonEncode({'startTime': longitudeStr, 'endTime': latitudeStr});
      if (longitudeStr.isNotEmpty && latitudeStr.isNotEmpty) {
        isDone = 1;
      }
    } else if (type == 8) {
      List userIdList = [];
      for (var i = 0; i < contentList.length; i++) {
        MemberModel memberModel = contentList[i];
        userIdList.add(memberModel.userId);
      }
      content = jsonEncode(userIdList);
      if (contentList.isNotEmpty) {
        isDone = 1;
      }
    } else if (type == 10 ||
        type == 12 ||
        type == 13 ||
        type == 19 ||
        type == 20) {
      if (type == 10 || type == 12) {
        List tempList = List.from(contentList);
        content = jsonEncode(tempList);
        if (tempList.isNotEmpty) {
          isDone = 1;
        }
      } else {
        content = jsonEncode(contentList);

        List tempList = [];
        bool isHaveEmpty = false;
        for (var i = 0; i < contentList.length; i++) {
          String contentStr = contentList[i];
          if (contentStr.isEmpty) {
            isHaveEmpty = true;
          } else {
            tempList.add(contentStr);
          }
        }
        if (tempList.isEmpty) {
          tempList = [''];
        }
        content = jsonEncode(tempList);

        if (!isHaveEmpty) {
          isDone = 1;
        }
      }
    } else if (type == 15) {
      int time = 0;
      if (dateChoose == 0) {
        for (var i = 0; i < contentList.length; i++) {
          int index = contentList[i];
          if (i == 0) {
            time = index * 24 * 60 * 60;
          } else {
            if (i == 1) {
              time = time + index * 60 * 60;
            }
            if (i == 2) {
              time = time + index * 60;
            }
          }
        }
      } else if (dateChoose == 1) {
        int index = contentList.first;
        time = index * 24 * 60 * 60;
      } else if (dateChoose == 2) {
        for (var i = 0; i < contentList.length; i++) {
          int index = contentList[i];
          if (i == 0) {
            time = index * 60 * 60;
          }
          if (i == 1) {
            time = time + index * 60;
          }
        }
      }

      content = '${time * 1000}';

      if (time != 0) {
        isDone = 1;
      } else {
        content = '';
      }
    } else if (type == 17) {
      List idList = [];
      for (var i = 0; i < contentList.length; i++) {
        Map idMap = contentList[i];
        idList.add(idMap['frameId']);
      }
      content = jsonEncode(idList);
      if (contentList.isNotEmpty) {
        isDone = 1;
      }
    } else {
      if (content.isNotEmpty) {
        isDone = 1;
      }
    }
    if (isCache == 1) {
      if (type == 3 ||
          type == 4 ||
          type == 8 ||
          type == 9 ||
          type == 10 ||
          type == 12 ||
          type == 13 ||
          type == 18) {
        content = '';
        showContent = '';
        contentList = [];
        if (type == 13) {
          contentList = [''];
        }
      }

      data['contentList'] = contentList;
      data['showContent'] = showContent;
      data['chooseFrameList'] = chooseFrameList;
      if (type == 11 && addressChoose == 1) {
        data['longitudeStr'] = longitudeStr;
        data['latitudeStr'] = latitudeStr;
        data['address'] = address;
        data['positioning'] = positioning;
      }
    }

    data['content'] = content;
    data['isDone'] = isDone;
    return data;
  }

  Map<String, dynamic> toCopyJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['widgetId'] = widgetId;
    data['title'] = title;
    data['prompt'] = prompt;
    data['required'] = required;
    data['type'] = type;
    data['createTime'] = createTime;
    data['suffix'] = suffix;
    data['wordLimit'] = wordLimit;
    data['keyBoard'] = keyBoard;
    data['dateChoose'] = dateChoose;
    data['addressChoose'] = addressChoose;
    data['nowChoose'] = nowChoose;
    data['secTitle'] = secTitle;
    data['secPrompt'] = secPrompt;
    data['chooseCount'] = chooseCount;
    data['chooseSort'] = chooseSort;
    data['personType'] = personType;
    data['wordType'] = wordType;
    data['autoDept'] = autoDept;
    data['status'] = status;
    data['thirdTitle'] = thirdTitle;
    data['rule'] = rule;
    data['isWidgetmodel'] = isWidgetmodel;
    data['modelKey'] = modelKey;
    data['maxNum'] = maxNum;
    data['isLimit'] = isLimit;
    data['dayNumber'] = dayNumber;
    data['limitRemind'] = limitRemind;
    data['contentList'] = contentList;
    data['showContent'] = showContent;
    data['chooseFrameList'] = chooseFrameList;
    data['longitudeStr'] = longitudeStr;
    data['latitudeStr'] = latitudeStr;
    data['address'] = address;
    data['positioning'] = positioning;

    if (type == 4) {
      content = jsonEncode({'startTime': longitudeStr, 'endTime': latitudeStr});
    } else if (type == 8) {
      List userIdList = [];
      for (var i = 0; i < contentList.length; i++) {
        MemberModel memberModel = contentList[i];
        userIdList.add(memberModel.userId);
      }
      content = jsonEncode(userIdList);
    } else if (type == 10 ||
        type == 12 ||
        type == 13 ||
        type == 19 ||
        type == 20) {
      if (type == 10 || type == 12) {
        List tempList = List.from(contentList);
        content = jsonEncode(tempList);
      } else {
        content = jsonEncode(contentList);

        List tempList = [];
        bool isHaveEmpty = false;
        for (var i = 0; i < contentList.length; i++) {
          String contentStr = contentList[i];
          if (contentStr.isEmpty) {
            isHaveEmpty = true;
          } else {
            tempList.add(contentStr);
          }
        }
        if (tempList.isEmpty) {
          tempList = [''];
        }
        content = jsonEncode(tempList);
      }
    } else if (type == 15) {
      int time = 0;
      if (dateChoose == 0) {
        for (var i = 0; i < contentList.length; i++) {
          int index = contentList[i];
          if (i == 0) {
            time = index * 24 * 60 * 60;
          } else {
            if (i == 1) {
              time = time + index * 60 * 60;
            }
            if (i == 2) {
              time = time + index * 60;
            }
          }
        }
      } else if (dateChoose == 1) {
        int index = contentList.first;
        time = index * 24 * 60 * 60;
      } else if (dateChoose == 2) {
        for (var i = 0; i < contentList.length; i++) {
          int index = contentList[i];
          if (i == 0) {
            time = index * 60 * 60;
          }
          if (i == 1) {
            time = time + index * 60;
          }
        }
      }

      content = '${time * 1000}';

      if (time == 0) {
        content = '';
      }
    } else if (type == 17) {
      List idList = [];
      for (var i = 0; i < contentList.length; i++) {
        Map idMap = contentList[i];
        idList.add(idMap['frameId']);
      }
      content = jsonEncode(idList);
    }

    data['content'] = content;
    return data;
  }
}
