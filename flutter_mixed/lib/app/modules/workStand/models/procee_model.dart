class ProcessModel {
  String? processNodeId;
  String? nodeName;
  List assigneeList = []; //需要选择的节点此数据必为空
  int? handleType;
  int? isSubmitterChoose; //0不需要选择 1需要
  int? approveWay;
  int? chooseType; //1多选2单选
  int? scope;//1全公司2指定人员
  List memberList = [];
  int isCreate = 0;

  //详情里此model返回数据结构变化
  List approveAssigneeList = [];
  /* 
  {
            "assignee": {
              "userId": "string",
              "name": "string",
              "headimg": "string"
            },
            "status": 0,////1.已同意 2.已拒绝 3.审批中 4.办理中 5.待审批 6.待办理 7.被退回 8.被撤回 9.已发起
            "time": 0
          }
  */

  //自定义参数

  ProcessModel({this.processNodeId});

  ProcessModel.fromJson(Map<String, dynamic> json) {
    processNodeId = json['processNodeId'] ?? '';
    nodeName = json['nodeName'] ?? '';
    assigneeList = json['assigneeList'] ?? [];
    handleType = json['handleType'];
    isSubmitterChoose = json['isSubmitterChoose'] ?? 0;
    approveWay = json['approveWay'] ?? 0;
    chooseType = json['chooseType'] ?? 2;
    scope = json['scope'] ?? 0;
    memberList = json['memberList'] ?? [];
    approveAssigneeList = json['approveAssigneeList'] ?? [];
    isCreate = json['isCreate'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['processNodeId'] = processNodeId;
    data['nodeName'] = nodeName;
    data['assigneeList'] = assigneeList;
    data['handleType'] = handleType;
    data['isSubmitterChoose'] = isSubmitterChoose;
    data['approveWay'] = approveWay;
    data['chooseType'] = chooseType;
    data['scope'] = scope;
    data['memberList'] = memberList;
    data['approveAssigneeList'] = approveAssigneeList;
    data['isCreate'] = isCreate;
    return data;
  }
}
