import 'dart:io';

class FileModel {
  String? fileId;
  String fileName = '';
  String filePath = '';//原始地址
  String savePath = '';//保存地址
  File? file;
  String hash = '';
  int fileType = 0;
  int progress = 0;

  FileModel({this.fileId});

  FileModel.fromJson(Map<String, dynamic> json) {
    fileId = json['fileId'] ?? '';
    fileName = json['fileName'] ?? '';
    filePath = json['filePath'] ?? '';
    file = json['file'];
    hash = json['hash'] ?? '';
    fileType = json['fileType'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileId'] = fileId;
    data['fileName'] = fileName;
    data['filePath'] = filePath;
    data['hash'] = hash;
    data['fileType'] = fileType;

    return data;
  }
    Map<String, dynamic> toUploadJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileId'] = fileId;
    data['fileName'] = fileName;
    data['hash'] = hash;
    data['fileType'] = fileType;

    return data;
  }
}
