class AppproveCorgModel {
  String? orgId = '';
  String? corgId = '';
  String? orgName = '';
  AppproveCorgModel({this.orgId,this.corgId,this.orgName});
  AppproveCorgModel.fromJson(Map<String, dynamic> json) {
    orgId = json['orgId'] ?? '';
    corgId = json['corgId'] ?? '';
    orgName = json['orgName'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['orgId'] = orgId;
    data['corgId'] = corgId;
    data['orgName'] = orgName;
    return data;
  }
}
