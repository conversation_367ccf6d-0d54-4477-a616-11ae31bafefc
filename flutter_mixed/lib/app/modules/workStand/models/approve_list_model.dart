import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../common/config/config.dart';

class ApproveListModel {
  String? approveId;
  String? approveAssigneeId;
  String approveName = '';
  String? approveNodeId;
  int? createTime;
  String? modelLogo;
  String oneApproveContent = '';
  String twoApproveContent = '';
  String threeApproveContent = '';

  int status = 0; //1通过；2.拒绝；3.进行中；5.撤回；6.退回 100.金蝶处理中
  int type = 0; //1.审批 2.办理

  int notRead = 0; //0已读1未读
  int needSignature = 0; //0无需签名1需签名
  List briefContent = []; //列表内容

  //金蝶字段
  String orgName = '';
  String detailUrl = '';
  String orgId = '';
  String corgId = '';
  int source = 1; //1.担当 2.金蝶财务 3.金蝶HR
  //自定义参数

  bool isSelected = false; //是否选中
  List lightWords = []; //高亮词组
  String? statusName;
  Map<String, dynamic> richMap = {};

  ApproveListModel({this.approveId});

  ApproveListModel.fromJson(Map<String, dynamic> json) {
    approveId = json['approveId'] ?? '';
    approveAssigneeId = json['approveAssigneeId'] ?? '';
    approveName = json['approveName'] ?? '';
    approveNodeId = json['approveNodeId'] ?? '';
    createTime = json['createTime'] ?? 0;
    modelLogo = json['modelLogo'] ?? '';
    oneApproveContent = json['oneApproveContent'] ?? '';
    twoApproveContent = json['twoApproveContent'] ?? '';
    threeApproveContent = json['threeApproveContent'] ?? '';
    status = json['status'] ?? 0;
    type = json['type'] ?? 0;
    notRead = json['notRead'] ?? 0;
    needSignature = json['needSignature'] ?? 0;

    orgName = json['orgName'] ?? '';
    detailUrl = json['detailUrl'] ?? '';
    orgId = json['orgId'] ?? '';
    corgId = json['corgId'] ?? '';
    source = json['source'] ?? 1;
    briefContent = json['briefContent'] ?? [];
    lightWords = json['lightWords'] ?? [];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['approveId'] = approveId;
    data['approveAssigneeId'] = approveAssigneeId;
    data['approveName'] = approveName;
    data['approveNodeId'] = approveNodeId;
    data['createTime'] = createTime;
    data['modelLogo'] = modelLogo;
    data['oneApproveContent'] = oneApproveContent;
    data['twoApproveContent'] = twoApproveContent;
    data['threeApproveContent'] = threeApproveContent;
    data['status'] = status;
    data['type'] = type;
    data['needSignature'] = needSignature;

    data['orgName'] = orgName;
    data['detailUrl'] = orgName;
    data['orgId'] = orgId;
    data['corgId'] = corgId;
    data['source'] = source;
    data['briefContent'] = briefContent;
    data['lightWords'] = lightWords;
    return data;
  }

  Color approveStatusColor() {
    return status == 1
        ? ColorConfig.friendColor
        : status == 2
            ? ColorConfig.deleteCorlor
            : status == 3
                ? ColorConfig.themeCorlor
                : status == 200
                    ? ColorConfig.themeCorlor
                    : ColorConfig.desTextColor;
  }

  backRichText(String text, List searchList) {
    if (richMap.keys.contains(text)) {
      return richMap[text];
    }
    List<TextSpan> lists = [];
    List textList = [text];
    for (var i = 0; i < searchList.length; i++) {
      String searchText = searchList[i];
      List splitAllList = [];
      for (var j = 0; j < textList.length; j++) {
        String textStr = textList[j];
        List splitList = textStr.split(searchText);
        if (splitList.isEmpty) {
          splitAllList.add(textStr);
        } else {
          for (var k = 0; k < splitList.length; k++) {
            splitAllList.add(splitList[k]);
            if (k < splitList.length - 1) {
              splitAllList.add(searchText);
            }
          }
        }
      }
      textList = splitAllList;
      textList.removeWhere((element) => element.length == 0);
    }
    for (var i = 0; i < textList.length; i++) {
      String textStr = textList[i];

      lists.add(TextSpan(
          text: textStr,
          style: TextStyle(
              fontSize: 14,
              color: searchList.contains(textStr)
                  ? ColorConfig.themeCorlor
                  : ColorConfig.mainTextColor)));
    }
    richMap[text] = lists;
    return lists;
  }
}
