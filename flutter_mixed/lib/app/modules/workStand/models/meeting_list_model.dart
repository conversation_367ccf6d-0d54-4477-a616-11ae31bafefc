import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

class MeetingListModel {
  String hostName = '';
  String topic = '';
  String type = '';
  int createTime = 0;
  int finishTime = 0;
  int status = 0;//1已结束
  String hostUserId = '';
  String avatar = '';
  String password = '';
  String meetingId = '';
  int isList = 0; //是否在会议列表

  //自定义字段
  String statusString = ''; //未开始 进行中 已结束 即将开始(15分钟内)
  String createDate = '';
  String endDate = '';
  String createTimeStr = '';
  String endTimeStr = '';
  String msgString = '';
  String doneTimeString = '';
  Color backColor = Colors.transparent;
  Color textColor = Colors.transparent;

  MeetingListModel.fromJson(Map<String, dynamic> json) {
    hostName = json['hostName'] ?? '';
    topic = json['topic'] ?? '';
    type = json['type'] ?? '';
    createTime = json['createTime'] ?? 0;
    finishTime = json['finishTime'] ?? 0;
    isList = json['isList'] ?? 0;
    status = json['status'] ?? 0;
    hostUserId = json['hostUserId'] ?? '';
    avatar = json['avatar'] ?? '';
    password = json['password'] ?? '';
    meetingId = json['meetingId'] ?? '';
    int nowTime = DateTime.now().millisecondsSinceEpoch;
    if (nowTime < createTime) {
      statusString = '未开始';
      backColor = ColorConfig.backgroundColor;
      textColor = ColorConfig.desTextColor;
      if (createTime - nowTime <= 15 * 60 * 1000) {
        statusString = '即将开始';
        backColor = Color(0x300168FD);
        textColor = ColorConfig.themeCorlor;

        String compareStr = BaseInfo().compareTime(createTime, nowTime);
        if(compareStr.isEmpty){
          compareStr = '1分钟';
        }
        msgString = '$compareStr后';
      }
    } else {
      if (status == 0) {
        statusString = '已开始';
        backColor = Color(0x33FF9C40);
        textColor = ColorConfig.newFriendColor;
        //msgString = '已进行${BaseInfo().compareTime(nowTime, createTime)}';
      } else {
        statusString = '已结束';
        backColor = ColorConfig.backgroundColor;
        textColor = ColorConfig.desTextColor;
      }
    }
    createTimeStr = BaseInfo().formatTimestamp(createTime, 'HH:mm');
    endTimeStr = BaseInfo().formatTimestamp(finishTime, 'HH:mm');
    createDate = BaseInfo().formatTimestamp(createTime, 'yyyy年MM月dd日');
    endDate = BaseInfo().formatTimestamp(finishTime, 'yyyy年MM月dd日');
    doneTimeString = BaseInfo().compareTime(createTime, finishTime);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['hostName'] = hostName;
    data['topic'] = topic;
    data['createTime'] = createTime;
    data['finishTime'] = finishTime;
    data['createTime'] = createTime;
    data['status'] = status;
    data['hostUserId'] = hostUserId;
    data['avatar'] = avatar;
    data['password'] = password;
    data['meetingId'] = meetingId;
    data['isList'] = isList;
    return data;
  }
}
