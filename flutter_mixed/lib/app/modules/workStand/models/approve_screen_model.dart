class ApproveScreenModel {
  String? groupId;
  String? name;
  List<ListOrgApproveStatisticList>? listOrgApproveStatisticList;
  List<Groups>? allOrgGroupModelList;
  int? count;

  //自定义字段
  String? orgId;
  String? corgId;
  String? orgName;
  ApproveScreenModel(
      {this.groupId,
      this.name,
      this.listOrgApproveStatisticList,
      this.allOrgGroupModelList,this.count});

  ApproveScreenModel.fromJson(Map<String, dynamic> json) {
    groupId = json['id'] ?? '';
    name = json['name'] ?? '';
    count = json['count']??0;
    if (json['listOrgApproveStatisticList'] != null) {
      listOrgApproveStatisticList = <ListOrgApproveStatisticList>[];
      json['listOrgApproveStatisticList'].forEach((v) {
        listOrgApproveStatisticList!
            .add(ListOrgApproveStatisticList.fromJson(v));
      });
    }
    if (json['allOrgGroupModelList'] != null) {
      allOrgGroupModelList = <Groups>[];
      json['allOrgGroupModelList'].forEach((v) {
        allOrgGroupModelList!.add(Groups.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = groupId;
    data['name'] = name;
    data['count'] = count;
    if (listOrgApproveStatisticList != null) {
      data['listOrgApproveStatisticList'] =
          listOrgApproveStatisticList!.map((v) => v.toJson()).toList();
    }
    if (allOrgGroupModelList != null) {
      data['allOrgGroupModelList'] =
          allOrgGroupModelList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListOrgApproveStatisticList {
  String? groupId;
  String? orgId;
  String? name;
  List<Groups>? groups;
  int? count;
  String? corgId;

  ListOrgApproveStatisticList(
      {this.orgId, this.name, this.groups, this.count, this.corgId});

  ListOrgApproveStatisticList.fromJson(Map<String, dynamic> json) {
    orgId = json['orgId'] ?? '';
    name = json['name'] ?? '';
    if (json['groups'] != null) {
      groups = <Groups>[];
      json['groups'].forEach((v) {
        groups!.add(Groups.fromJson(v));
      });
    }
    count = json['count'] ?? 0;
    corgId = json['corgId'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['orgId'] = orgId;
    data['name'] = name;
    if (groups != null) {
      data['groups'] = groups!.map((v) => v.toJson()).toList();
    }
    data['count'] = count;
    data['corgId'] = corgId;
    return data;
  }
}

class Groups {
  String? groupName;
  List<Models>? models;
  int? count;

  Groups({this.groupName, this.models, this.count});

  Groups.fromJson(Map<String, dynamic> json) {
    groupName = json['groupName'] ?? '';
    if (json['models'] != null) {
      models = <Models>[];
      json['models'].forEach((v) {
        models!.add(Models.fromJson(v));
      });
    }
    count = json['count'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['groupName'] = groupName;
    if (models != null) {
      data['models'] = models!.map((v) => v.toJson()).toList();
    }
    data['count'] = count;
    return data;
  }
}

class Models {
  String? modelName;
  int? count;

  //自定义字段
  String groupName = '';//所属组名称
  bool isCurrentSrceenModel = false;
  Models({this.modelName, this.count});

  Models.fromJson(Map<String, dynamic> json) {
    modelName = json['modelName'] ?? '';
    count = json['count'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['modelName'] = modelName;
    data['count'] = count;
    return data;
  }
}


