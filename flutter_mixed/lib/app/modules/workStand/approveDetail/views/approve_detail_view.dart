import 'dart:convert';
import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/keyboard_focus_widget.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/dialog/approve_dialog.dart';
import 'package:flutter_mixed/app/modules/workStand/models/form_list_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/form_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/procee_model.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

import '../../../../../plugin/focus_detector/focus_detector.dart';
import '../../../../common/dialog/choosepicker-dialog.dart';
import '../../../../common/dialog/datepicker-dialog.dart';
import '../../../../common/dialog/msg-dialog.dart';
import '../../../../common/widgets/watermark.dart';
import '../../../../routes/app_pages.dart';
import '../../../contact/model/org/dept_members_model.dart';
import '../../../contact/model/org/org_model.dart';
import '../../common/focus_switch_widget.dart';
import '../controllers/approve_detail_controller.dart';

class ApproveDetailView extends StatelessWidget {
  bool showFocusbar = false;

  ApproveDetailView();

  // ApproveDetailView1({Key? key}) : super(key: key);
  final approveDetailController = Get.find<ApproveDetailController>();
  List approveStatusList = ['', '已通过', '已拒绝', '进行中', '', '已撤回', '已退回'];
  List approveStatusColorList = [
    ColorConfig.whiteColor,
    ColorConfig.externalLevelNormal,
    ColorConfig.deleteCorlor,
    ColorConfig.themeCorlor,
    ColorConfig.whiteColor,
    ColorConfig.btnGrayColor,
    ColorConfig.btnGrayColor
  ];
  @override
  Widget build(BuildContext context) {
    return FocusDetector(
        onFocusGained: () {
          if (approveDetailController.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 1});
          }
        },
        onFocusLost: () {
          if (approveDetailController.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 0});
          }
        },
        child: GetBuilder(
            global: false,
            init: approveDetailController,
            builder: (logic) {
              return KeyBoardWidget(
                  child: Scaffold(
                    backgroundColor: ColorConfig.backgroundColor,
                    extendBodyBehindAppBar: true,
                    appBar: TitleBar().backAppbar(
                        context,
                        '审批详情',
                        false,
                        backgroundColor: ColorConfig.backgroundColor,
                        approveDetailController.isAuth &&
                                approveDetailController.approveId.isNotEmpty
                            ? [
                                Container(
                                  width: 60,
                                  child: CupertinoButton(
                                      padding:
                                          const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                      pressedOpacity: 0.5,
                                      child: const Text(
                                        '导出',
                                        style: TextStyle(
                                            color: ColorConfig.mainTextColor,
                                            fontSize: 14),
                                      ),
                                      onPressed: () {
                                        Get.toNamed(Routes.APPROVE_EXPORT,
                                            arguments: {
                                              'approveId':
                                                  approveDetailController
                                                      .approveId
                                            });
                                      }),
                                )
                              ]
                            : [], onPressed: () {
                      if (approveDetailController.isNative.value) {
                        approveDetailController.onClose();
                        Channel().invoke(Channel_Native_Back, {});
                      } else {
                        Get.back();
                      }
                    }),
                    // body: _waterMarkWidget(),
                    body: _page(),
                  ),
                  changed: (v) {
                    showFocusbar = (v == 1);
                    approveDetailController.update();
                  });
            }));
  }

  _waterMarkWidget() {
    return Stack(
      children: [
        IgnorePointer(
          child: WaterMark(
            painter: TextWaterMarkPainter(
              text: 'ljt 18600259596',
              padding: const EdgeInsets.all(18),
              textStyle: const TextStyle(
                color: Colors.black26,
              ),
              rotate: -15,
            ),
          ),
        ),
        _page(),
      ],
    );
  }

  _page() {
    if (approveDetailController.approveInfoLoad &&
        approveDetailController.commonDataLoad) {
      return Stack(
        children: [
          Column(
            children: [
              Expanded(
                  child: ListView(
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(
                        top: 8, left: 8, right: 8, bottom: 8),
                    child: Container(
                      alignment: Alignment.centerLeft,
                      width: double.infinity,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorConfig.whiteColor),
                      // padding: EdgeInsets.all(16),
                      child: Row(
                        children: [
                          12.gap,
                          Expanded(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              12.gap,
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                      child: Container(
                                    height: 28,
                                    child: Text(
                                      approveDetailController
                                          .dataMap['modelName'],
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w500,
                                          color: ColorConfig.mainTextColor),
                                    ),
                                  )),
                                  const SizedBox(
                                    width: 8,
                                  ),
                                  Container(
                                    alignment: Alignment.center,
                                    padding: const EdgeInsets.only(
                                        left: 8, right: 8),
                                    // width: 44,
                                    height: 22,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                            width: 1,
                                            color: approveStatusColorList[
                                                approveDetailController
                                                    .dataMap['status']])),
                                    child: Text(
                                      approveDetailController
                                                  .dataMap['status'] ==
                                              null
                                          ? ''
                                          : approveStatusList[
                                              approveDetailController
                                                  .dataMap['status']],
                                      style: TextStyle(
                                          fontSize: 11,
                                          color: approveStatusColorList[
                                              approveDetailController
                                                  .dataMap['status']]),
                                    ),
                                  )
                                ],
                              ),
                              8.gap,
                              Container(
                                alignment: Alignment.centerLeft,
                                height: 24,
                                child: Text(
                                  approveDetailController.initiatorName,
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: ColorConfig.mainTextColor),
                                ),
                              ),
                              Container(
                                alignment: Alignment.centerLeft,
                                height: 22,
                                child: Text(
                                   StringUtil.isEmpty(approveDetailController.corgName) ? '': '审批归属公司:${approveDetailController.corgName}',
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                ),
                              ),
                              12.gap,
                            ],
                          )),
                          12.gap
                        ],
                      ),
                    ),
                  ),
                  //审批内容 ---
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsets.only(left: 8, right: 8, bottom: 8),
                    child: Column(
                      children: backApproveContentWidget(),
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    padding:
                         EdgeInsets.only(left: 8, right: 8, bottom: approveDetailController.submitList.isEmpty ? 0 : 8),
                    child: Column(
                      children: backWorkerContent(),
                    ),
                  ),
                  Container(
                    width: double.infinity,
                    margin:
                        const EdgeInsets.only(left: 8, right: 8, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: ColorConfig.whiteColor),
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          height: 46,
                          padding: const EdgeInsets.only(left: 12, right: 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                alignment: Alignment.centerLeft,
                                child: const Text(
                                  '审批流程',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: ColorConfig.mainTextColor),
                                ),
                              )
                            ],
                          ),
                        ),
                        Column(
                          children: backFinishListWidget(),
                        )
                      ],
                    ),
                  ),
                  Offstage(
                    offstage: approveDetailController.commentList.isEmpty,
                    child: Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(left: 8, right: 8),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorConfig.whiteColor),
                      child: Column(
                        children: [
                          Container(
                            width: double.infinity,
                            height: 46,
                            padding: const EdgeInsets.only(left: 12, right: 12),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  child: const Text(
                                    '评论',
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                )
                              ],
                            ),
                          ),
                          4.gap,
                          Column(
                            children: backCommentListWidget(),
                          )
                        ],
                      ),
                    ),
                  )
                ],
              )),
              FocusSwitchBar(
                showBar: showFocusbar,
                preFocus: () {
                  approveDetailController.lastFocus();
                },
                nextFocus: () {
                  approveDetailController.nextFocus();
                },
              ),
              Offstage(
                offstage: approveDetailController.buttonList.isEmpty,
                child: Container(
                  width: double.infinity,
                  height: 60 + DeviceUtils().bottom.value,
                  color: ColorConfig.whiteColor,
                  padding: EdgeInsets.only(
                      left: 16, right: 16, bottom: DeviceUtils().bottom.value),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: backButtonsWidget(),
                  ),
                ),
              )
            ],
          ),
        ],
      );
    }

    return Container(); //commonEmpty('暂无数据', "")
  }

  backButtonsWidget() {
    List<Widget> lists = [];
    if (approveDetailController.buttonList.contains(1) &&
        approveDetailController.buttonList.length > 3) {
      lists.add(InkWell(
        onTap: () {
          SettingWidget().showCupertinoActionSheetForPage(
              Get.context!, ['评论', '加审', '撤回'], (value) async {
            if (value == 0) {
              var result = await Get.toNamed('/approve-comment', arguments: {
                'orgId': approveDetailController.orgId,
                'approveId': approveDetailController.approveId
              });
              if (result != null && result['add'] != null) {
                approveDetailController.getCommentData();
              }
            }
            if (value == 1) {
              var result = await Get.toNamed(Routes.APPROVE_ADDITIONAL,
                  arguments: {
                    'approveId': approveDetailController.dataMap['approveId'],
                    'approveNodeId':
                        approveDetailController.dataMap['approveNodeId'],
                    'approveAssigneeId':
                        approveDetailController.dataMap['approveAssigneeId'],
                    'companyId': approveDetailController.orgId,
                    'isNeedSign': approveDetailController.isNeedSign
                  },
                  preventDuplicates: false);
              if (result != 0) {
                approveDetailController.getApproveInfo();
              }
            }
            if (value == 2) {
              MsgDiaLog('你确定要撤回这条审批么?', '', '取消', '确定', () {
                Navigator.of(Get.context!).pop();
              }, () {
                Navigator.of(Get.context!).pop();
                approveDetailController.withDrawApprove();
              }).show();
            }
          });
        },
        child: backFunctionButton('approve_more', '更多'),
      ));
      lists.add(InkWell(
        onTap: () {
          ApproveDialog(2).show(approveDetailController.approveId, false,
              (content, isUse, fileId) {
            approveDetailController.putApproveFlow(2, content, isUse, fileId);
          });
        },
        child: backApproveButton('拒绝', ColorConfig.deleteCorlor,
            (DeviceUtils().width.value - 30 - 60) * 0.5),
      ));

      lists.add(InkWell(
        onTap: () {
          ApproveDialog(1).show(approveDetailController.approveId,
              approveDetailController.isNeedSign, (content, isUse, fileId) {
            approveDetailController.putApproveFlow(1, content, isUse, fileId);
          });
        },
        child: backApproveButton('同意', ColorConfig.themeCorlor,
            (DeviceUtils().width.value - 30 - 60) * 0.5),
      ));
    } else {
      for (var i = 0; i < approveDetailController.buttonList.length; i++) {
        int type = approveDetailController.buttonList[i];
        if (type > 2) {
          if (type == 3) {
            lists.add(InkWell(
              onTap: () async {
                var result = await Get.toNamed(Routes.APPROVE_ADDITIONAL,
                    arguments: {
                      'approveId': approveDetailController.dataMap['approveId'],
                      'approveNodeId':
                          approveDetailController.dataMap['approveNodeId'],
                      'approveAssigneeId':
                          approveDetailController.dataMap['approveAssigneeId'],
                      'companyId': approveDetailController.orgId,
                      'isNeedSign': approveDetailController.isNeedSign
                    },
                    preventDuplicates: false);
                if (result != null) {
                  if (result != 0) {
                    approveDetailController.getApproveInfo();
                  }
                }
              },
              child: backFunctionButton('approve_addAppro', '加审'),
            ));
          }
          if (type == 4) {
            lists.add(InkWell(
              onTap: () {
                MsgDiaLog('你确定要催办这条审批么?', '', '取消', '确定', () {
                  Navigator.of(Get.context!).pop();
                }, () {
                  Navigator.of(Get.context!).pop();
                  approveDetailController.urgingApprove();
                }).show();
              },
              child: backFunctionButton('approve_remind', '催办'),
            ));
          }
          if (type == 5) {
            lists.add(InkWell(
              onTap: () {
                MsgDiaLog('你确定要撤回这条审批么?', '', '取消', '确定', () {
                  Navigator.of(Get.context!).pop();
                }, () {
                  Navigator.of(Get.context!).pop();
                  approveDetailController.withDrawApprove();
                }).show();
              },
              child: backFunctionButton('approve_withdraw', '撤回'),
            ));
          }
          if (type == 6) {
            lists.add(InkWell(
              onTap: () async {
                var result = await Get.toNamed('/approve-comment', arguments: {
                  'orgId': approveDetailController.orgId,
                  'approveId': approveDetailController.approveId
                });
                if (result != null && result['add'] != null) {
                  approveDetailController.getCommentData();
                }
              },
              child: backFunctionButton('approve_comment', '评论'),
            ));
          }
          if (type == 7) {
            lists.add(InkWell(
              onTap: () {
                approveDetailController.isCanResubmit();
              },
              child: backApproveButton(
                  '重新提交',
                  ColorConfig.themeCorlor,
                  (DeviceUtils().width.value -
                              30 -
                              (approveDetailController.buttonList.length - 1) *
                                  60) *
                          0.5 -
                      20),
            ));
          }
        } else if (type == 1) {
          lists.add(InkWell(
            onTap: () {
              ApproveDialog(2).show(approveDetailController.approveId, false,
                  (content, isUse, fileId) {
                approveDetailController.putApproveFlow(
                    2, content, isUse, fileId);
              });
            },
            child: backApproveButton(
                '拒绝',
                ColorConfig.deleteCorlor,
                (DeviceUtils().width.value -
                            30 -
                            (approveDetailController.buttonList.length - 1) *
                                60) *
                        0.5 -
                    20),
          ));
          lists.add(InkWell(
            onTap: () {
              ApproveDialog(1).show(approveDetailController.approveId,
                  approveDetailController.isNeedSign, (content, isUse, fileId) {
                approveDetailController.putApproveFlow(
                    1, content, isUse, fileId);
              });
            },
            child: backApproveButton(
                '同意',
                ColorConfig.themeCorlor,
                (DeviceUtils().width.value -
                            30 -
                            (approveDetailController.buttonList.length - 1) *
                                60) *
                        0.5 -
                    20),
          ));
        } else if (type == 2) {
          lists.add(InkWell(
            onTap: () {
              approveDetailController.jugeDataIsDone();
            },
            child: backApproveButton(
                '提交',
                ColorConfig.themeCorlor,
                (DeviceUtils().width.value -
                            30 -
                            (approveDetailController.buttonList.length - 1) *
                                60) *
                        0.5 -
                    20),
          ));
        }
      }
    }

    return lists;
  }

  //评论
  backCommentListWidget() {
    List<Widget> lists = [];
    for (var i = 0; i < approveDetailController.commentList.length; i++) {
      Map commentMap = approveDetailController.commentList[i];
      lists.add(backCommentWidget(commentMap));
    }
    return lists;
  }

  backApproveContentWidget() {
    List<Widget> lists = [];
    for (var i = 0; i < approveDetailController.contentModelList.length; i++) {
      lists.add(Column(
        children:
            backFormContentWidget(approveDetailController.contentModelList[i]),
      ));
    }
    return lists;
  }

  backFormContentWidget(List formList) {
    List<Widget> lists = [];
    for (var i = 0; i < formList.length; i++) {
      lists.add(backWidgetModelWidget(formList[i]));
    }
    return lists;
  }

  backWidgetModelWidget(List modelList) {
    FormWidetModel model = modelList[0];
    return Column(
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: ColorConfig.whiteColor),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                height: 46,
                padding: const EdgeInsets.only(left: 12, right: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Container(
                      alignment: Alignment.centerLeft,
                      child: model.modelType == 1
                          ? const Text(
                              '审批内容',
                              style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: ColorConfig.mainTextColor),
                            )
                          : model.modelType == 2
                              ? Row(
                                  children: [
                                    Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          image: DecorationImage(
                                              image: SettingWidget.backImageProvider(model.workUser![
                                                          'headimg'])
                                                 )),
                                    ),
                                    8.gap,
                                    Expanded(
                                        child: Text(
                                      '${model.workUser!['name']}办理的${model.modelFormName}',
                                      style: const TextStyle(
                                          fontSize: 16,
                                          color: ColorConfig.mainTextColor),
                                    ))
                                  ],
                                )
                              : Container(),
                    ))
                    //折叠功能按钮预留
                  ],
                ),
              ),
              Offstage(
                offstage: 1 == 0, //是否折叠
                child: Column(
                  children: backModelListWidget(modelList),
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  backModelListWidget(List modelList) {
    List<Widget> lists = [];
    for (var i = 0; i < modelList.length; i++) {
      FormWidetModel model = modelList[i];

      String contentStr = model.content.isEmpty
          ? model.type == 1 || model.type == 2
              ? '未填写'
              : '未选择'
          : model.content;
      if (model.type == 1) {
        if (model.content.isNotEmpty && model.suffix!.isNotEmpty) {
          contentStr = '${model.content}${model.suffix}';
        }
      }
      if (model.type == 3) {
        if (model.content.isNotEmpty) {
          contentStr = BaseInfo().formatTimestamp(
              int.parse(model.content),
              model.dateChoose == 0
                  ? 'yyyy-MM-dd HH:mm'
                  : model.dateChoose == 1
                      ? 'yyyy-MM-dd'
                      : 'HH:mm');
        }
        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 4) {
        if (model.content.isNotEmpty) {
          Map contentMap = jsonDecode(model.content);
          dynamic startStr = contentMap['startTime'].toString();
          dynamic endStr = contentMap['endTime'].toString();

          if (startStr.isNotEmpty) {
            startStr = BaseInfo().formatTimestamp(
                int.parse(startStr),
                model.dateChoose == 0
                    ? 'yyyy-MM-dd HH:mm'
                    : model.dateChoose == 1
                        ? 'yyyy-MM-dd'
                        : 'HH:mm');
          } else {
            startStr = '未选择';
          }
          lists.add(backWidgetForDetail(model.title!, startStr));
          if (endStr.isNotEmpty) {
            endStr = BaseInfo().formatTimestamp(
                int.parse(endStr),
                model.dateChoose == 0
                    ? 'yyyy-MM-dd HH:mm'
                    : model.dateChoose == 1
                        ? 'yyyy-MM-dd'
                        : 'HH:mm');
          } else {
            endStr = '未选择';
          }
          lists.add(backWidgetForDetail(model.secTitle!, endStr));
        }
      } else if (model.type == 8) {
        if (model.content.isNotEmpty) {
          List contentList = jsonDecode(model.content);
          List nameList = [];
          if (contentList.isNotEmpty) {
            for (var i = 0; i < contentList.length; i++) {
              Map contentMap = contentList[i];
              nameList.add(contentMap['name']);
            }
            contentStr = nameList.join('、');
          }
        }

        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 9) {
        if (model.content.isNotEmpty) {
          if (model.wordType == 0) {
            Map nameMap = jsonDecode(model.content);
            contentStr = nameMap['name'];
          }
        }

        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 10) {
        List contentList = jsonDecode(model.content);
        if (contentList.isNotEmpty) {
          lists.add(backImageShowWidget(model.title!, contentList));
        } else {
          lists.add(backWidgetForDetail(model.title!, '未上传'));
        }
      } else if (model.type == 11) {
        if (model.content.isNotEmpty) {
          Map nameMap = jsonDecode(model.content);
          contentStr = nameMap['address'];
        }

        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 12) {
        List contentList = jsonDecode(model.content);
        if (contentList.isNotEmpty) {
          lists.add(backFileShowWidget(model.title!, contentList));
        } else {
          lists.add(backWidgetForDetail(model.title!, '未上传'));
        }
      } else if (model.type == 13) {
        if (model.content.isNotEmpty) {
          List contentList = jsonDecode(model.content);
          List timeList = [];
          for (var i = 0; i < contentList.length; i++) {
            if (contentList[i] != '') {
              dynamic x = contentList[i].toString();
              String timeStr = BaseInfo().formatTimestamp(
                  int.parse(x),
                  model.dateChoose == 0
                      ? 'yyyy-MM-dd HH:mm'
                      : model.dateChoose == 1
                          ? 'yyyy-MM-dd'
                          : 'HH:mm');
              timeList.add(timeStr);
            }
          }

          if (timeList.isNotEmpty) {
            contentStr = timeList.join('、');
          }
        }

        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 15) {
        if (model.content.isNotEmpty) {
          DateTime time =
              DateTime.fromMillisecondsSinceEpoch(int.parse(model.content));
          DateTime zeroTime = DateTime.fromMillisecondsSinceEpoch(0);
          var differenceDay = time.difference(zeroTime);

          DateTime hourTime = DateTime.fromMillisecondsSinceEpoch(
              int.parse(model.content) -
                  differenceDay.inDays * 24 * 60 * 60 * 1000);
          var differenceHour = hourTime.difference(zeroTime);

          DateTime minuteTime = DateTime.fromMillisecondsSinceEpoch(
              int.parse(model.content) -
                  differenceDay.inDays * 24 * 60 * 60 * 1000 -
                  differenceHour.inHours * 60 * 60 * 1000);
          var differenceMinute = minuteTime.difference(zeroTime);
          if (model.dateChoose == 0) {
            contentStr =
                '${differenceDay.inDays}天${differenceHour.inHours}小时${differenceMinute.inMinutes}分钟';
          }
          if (model.dateChoose == 1) {
            contentStr = '${differenceDay.inDays}天';
          }
          if (model.dateChoose == 2) {
            contentStr =
                '${differenceHour.inHours}小时${differenceMinute.inMinutes}分钟';
          }
        }
        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 17) {
        List contentList = jsonDecode(model.content);
        if (contentList.isNotEmpty) {
          contentStr = contentList.join('、');
        }

        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 18) {
        if (model.content.isNotEmpty) {
          if (model.wordType == 0) {
            Map nameMap = jsonDecode(model.content);

            contentStr = nameMap['name'];
          }
          if (model.wordType == 2) {
            Map nameMap = jsonDecode(model.content);

            contentStr = nameMap['deptName'];
          }
        }

        lists.add(backWidgetForDetail(model.title!, contentStr));
      } else if (model.type == 19 || model.type == 20) {
        if (model.content.isEmpty) {
          lists.add(backWidgetForDetail(model.title!, contentStr));
        } else {
          lists.addAll(backMoreTextWidget(model));
        }
      } else {
        lists.add(backWidgetForDetail(model.title!, contentStr));
      }
    }
    return lists;
  }

  //多文本样式
  backMoreTextWidget(FormWidetModel model) {
    List contentList = jsonDecode(model.content);
    List<Widget> lists = [];
    for (var i = 0; i < contentList.length; i++) {
      String str = contentList[i];
      String contentStr = str.isEmpty ? '未填写' : str;
      if (str.isNotEmpty && model.suffix!.isNotEmpty) {
        contentStr = '$str${model.suffix}';
      }
      lists.add(backWidgetForDetail('${model.title!}${i + 1}', contentStr));
    }
    return lists;
  }

  //文件样式
  backFileShowWidget(String topStr, List contentList) {
    FormWidetModel model = FormWidetModel();
    model.type = 12;
    model.fileList = contentList;
    return Container(
      constraints: const BoxConstraints(minHeight: 40),
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 24,
            child: Text(
              topStr,
              style: const TextStyle(
                  fontSize: 16, color: ColorConfig.desTextColor),
            ),
          ),
          const SizedBox(
            height: 4,
          ),
          Column(
            children: backImageData(model, isShowUpload: false),
          )
        ],
      ),
    );
  }

  //图片样式
  backImageShowWidget(String topStr, List contentList) {
    return Container(
      constraints: const BoxConstraints(minHeight: 40),
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 24,
            child: Text(
              topStr,
              style: const TextStyle(
                  fontSize: 16, color: ColorConfig.desTextColor),
            ),
          ),
          const SizedBox(
            height: 4,
          ),
          GridView.count(
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            padding: EdgeInsets.zero,
            childAspectRatio: 1,
            shrinkWrap: true,
            children: backImageCell(contentList),
          )
        ],
      ),
    );
  }

  backImageCell(List contentList) {
    List<Widget> lists = [];

    List urlList = [];
    for (var i = 0; i < contentList.length; i++) {
      Map dataMap = contentList[i];
      String url = dataMap['url'];
      urlList.add(url);
    }
    for (var i = 0; i < contentList.length; i++) {
      Map dataMap = contentList[i];
      String url = dataMap['url'];
      lists.add(InkWell(
        onTap: () {
          Get.toNamed('/common-scroll-pic',
              arguments: {'imageUrls': urlList, 'currentIndex': i});
        },
        child: Container(
          padding: const EdgeInsets.all(4),
          child: ImageLoader(
            url: url,
            radius: 0,
          ),
        ),
      ));
    }
    return lists;
  }

  //上下文字样式
  backWidgetForDetail(String topStr, String bottomStr) {
    return Container(
      constraints: const BoxConstraints(minHeight: 62),
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 22,
            child: Text(
              topStr,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.msgTextColor),
            ),
          ),
          const SizedBox(
            height: 4,
          ),
          Container(
            alignment: Alignment.centerLeft,
            constraints: const BoxConstraints(minHeight: 18),
            child: Text(
              bottomStr,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
        ],
      ),
    );
  }

  backFinishListWidget() {
    List<Widget> lists = [];
    for (var i = 0;
        i < approveDetailController.processModelList.length + 1;
        i++) {
      if (i == approveDetailController.processModelList.length) {
        lists.add(Container(
          width: double.infinity,
          padding: const EdgeInsets.only(left: 8, right: 12),
          child: Column(
            children: [
              Container(
                height: 24,
                child: Row(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: 16,
                      height: 24,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                            border: Border.all(
                                width: 1, color: ColorConfig.lineColor),
                            borderRadius: BorderRadius.circular(4)),
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Expanded(
                        child: Container(
                      alignment: Alignment.centerLeft,
                      height: 24,
                      child: const Text(
                        '结束',
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      ),
                    ))
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                height: approveDetailController.endStr.value.isEmpty ? 12 : 24,
                padding: const EdgeInsets.only(left: 24),
                child: Text(
                  approveDetailController.endStr.value.isEmpty
                      ? ''
                      : '审批用时:${approveDetailController.endStr.value}', //不足1分按1分算
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.desTextColor),
                ),
              )
            ],
          ),
        ));
      } else {
        ProcessModel model = approveDetailController.processModelList[i];
        lists.add(backFinishProcess(model));
      }
    }
    return lists;
  }

  //流程
  backFinishProcess(ProcessModel model) {
    return Container(
      constraints: const BoxConstraints(minHeight: 90),
      width: double.infinity,
      padding: const EdgeInsets.only(left: 8, right: 12),
      child: IntrinsicHeight(
        child: Row(
          children: [
            Container(
              width: 16,
              height: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: 16,
                    height: 24,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                          border: Border.all(
                              width: 1, color: ColorConfig.lineColor),
                          borderRadius: BorderRadius.circular(4)),
                    ),
                  ),
                  Expanded(
                      child: Container(
                    width: 1,
                    height: double.infinity,
                    child: DashedLine(
                      height: 3,
                      color: ColorConfig.lineColor,
                    ),
                  ))
                ],
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Expanded(
                child: Column(
              children: [
                Container(
                  width: double.infinity,
                  alignment: Alignment.centerLeft,
                  height: 24,
                  child: Text(
                    model.isCreate == 1
                        ? '发起'
                        : model.handleType == 1
                            ? '审批${model.approveWay == 1 ? '(依次审批)' : model.approveWay == 2 ? '(会审)' : ''}'
                            : model.handleType == 2
                                ? '办理'
                                : model.handleType == 3
                                    ? '抄送'
                                    : '',
                    style: const TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Column(
                  children:
                      backFinishContentListWidget(model.approveAssigneeList),
                ),
              ],
            ))
          ],
        ),
      ),
    );
  }

  backFinishContentListWidget(List approveProcessList) {
    List<Widget> lists = [];
    for (var i = 0; i < approveProcessList.length; i++) {
      lists.add(backFinishContent(approveProcessList[i], 0));
    }
    return lists;
  }

  backLine() {
    List<Widget> lists = [];
    for (var i = 0; i < 99; i++) {
      lists.add(Column(
        children: [
          Container(
            width: 1,
            height: 3,
            color: ColorConfig.lineColor,
          ),
          const SizedBox(
            height: 2,
          )
        ],
      ));
    }
    return lists;
  }

  //评论
  backCommentWidget(Map commentMap) {
    return Container(
      constraints: const BoxConstraints(minHeight: 74),
      width: double.infinity,
      padding: const EdgeInsets.only(left: 12, right: 12, bottom: 12),
      child: backFinishContent(commentMap, 1),
    );
  }

  backFinishContent(Map approveProcessMap, int type) {
    //type 0流程 1评论
    Map userMap = {}; //人员信息
    String bottomStr = ''; //流程代表审批状态，评论代表评论内容
    String timeStr = ''; //时间
    String commnetStr = '';

    String retrialStr = '';
    String signatureImageUrl = '';

    List comentList = []; //评论图片数组
    Color statusColor = ColorConfig.themeCorlor;
    if (type == 0) {
      List statusList = [
        '',
        '已同意',
        '已拒绝',
        '审批中',
        '办理中',
        '待审批',
        '待办理',
        '被退回',
        '被撤回',
        '已发起',
        '无需审批',
        '无需审批',
        '已抄送',
        '待抄送',
        '已办理',
        '自动同意'
      ];
      List statusColorList = [
        ColorConfig.whiteColor,
        ColorConfig.externalLevelNormal,
        ColorConfig.deleteCorlor,
        ColorConfig.themeCorlor,
        ColorConfig.themeCorlor,
        ColorConfig.themeCorlor,
        ColorConfig.themeCorlor,
        ColorConfig.btnGrayColor,
        ColorConfig.btnGrayColor,
        ColorConfig.externalLevelNormal,
        ColorConfig.btnGrayColor,
        ColorConfig.btnGrayColor,
        ColorConfig.externalLevelNormal,
        ColorConfig.themeCorlor,
        ColorConfig.externalLevelNormal,
        ColorConfig.externalLevelNormal,
      ];
      userMap = approveProcessMap['assignee'];
      int stauts = approveProcessMap['status'];
      //1.已同意 2.已拒绝 3.审批中 4.办理中 5.待审批 6.待办理 7.被退回 8.被撤回 9.已发起
      if (stauts < statusList.length) {
        bottomStr = statusList[stauts];
        statusColor = statusColorList[stauts];
      }

      //判断是否显示由何人加审
      int retrial = approveProcessMap['retrial'];
      if (retrial == 1) {
        dynamic addMap = approveProcessMap['addUser'];
        if (addMap != null) {
          retrialStr = '由${addMap['name']}加审';
        }
      }

      if (approveProcessMap['time'] != 0) {
        timeStr = BaseInfo()
            .formatTimestamp(approveProcessMap['time'], 'yyyy.MM.dd HH:mm');
      }
      commnetStr = approveProcessMap['content'];
      signatureImageUrl = approveProcessMap['signatureImageUrl'];
    } else {
      userMap = approveProcessMap['user'];
      bottomStr = approveProcessMap['content'];
      if (approveProcessMap['createTime'] != 0) {
        timeStr = BaseInfo().formatTimestamp(
            approveProcessMap['createTime'], 'yyyy.MM.dd HH:mm');
      }
      comentList = approveProcessMap['fileList'];
    }

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 58),
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: SettingWidget.backImageProvider(userMap['headimg'])),
                borderRadius: BorderRadius.circular(20)),
          ),
          const SizedBox(
            width: 8,
          ),
          Expanded(
              child: Column(
            children: [
              Container(
                width: double.infinity,
                // height: 22,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Container(
                          alignment: Alignment.centerLeft,
                          child: RichText(
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                  text: userMap['name'],
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                  children: [
                                    TextSpan(
                                        text: ' $retrialStr',
                                        style: const TextStyle(
                                            fontSize: 14,
                                            color: ColorConfig.desTextColor))
                                  ]))),
                    ),
                    if (timeStr != null && timeStr != '') ...[
                      Container(
                        // color: Colors.red,
                        width: 105,
                        alignment: Alignment.centerRight,
                        child: Text(
                          timeStr,
                          style: const TextStyle(
                              fontSize: 12, color: ColorConfig.desTextColor),
                          textAlign: TextAlign.right,
                        ),
                      )
                    ],
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                constraints: const BoxConstraints(minHeight: 18),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                        child: Container(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        bottomStr,
                        style: TextStyle(fontSize: 12, color: statusColor),
                      ),
                    )),
                  ],
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              Offstage(
                offstage: commnetStr.isEmpty,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.fromLTRB(8, 5, 8, 5),
                  //constraints: BoxConstraints(minHeight: 30),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: ColorConfig.backgroundColor),
                  child: Text(
                    commnetStr,
                    style: const TextStyle(
                        fontSize: 12, color: ColorConfig.mainTextColor),
                  ),
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              Offstage(
                offstage: signatureImageUrl.isEmpty,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      child: const Text(
                        '签字:',
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      ),
                    ),
                    ImageLoader(
                      url: signatureImageUrl,
                      height: 30,
                      boxFit: BoxFit.fitHeight,
                      filterQuality: FilterQuality.high,
                      radius: 0,
                    ),
                  ],
                ),
              ),
              Offstage(
                offstage: comentList.isEmpty,
                child: GridView.count(
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 6,
                  padding: EdgeInsets.zero,
                  childAspectRatio: 1,
                  shrinkWrap: true,
                  children: backImageCell(comentList),
                ),
              )
            ],
          )),
        ],
      ),
    );
  }

  //按钮
  backFunctionButton(String imageName, String text) {
    return Container(
      width: 44,
      height: 44,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 24,
            height: 24,
            child: Image.asset('assets/images/3.0x/$imageName.png'),
          ),
          Container(
            alignment: Alignment.center,
            width: 44,
            height: 16,
            child: Text(
              text,
              style: const TextStyle(
                  fontSize: 10, color: ColorConfig.mainTextColor),
            ),
          )
        ],
      ),
    );
  }

  //审批按钮
  backApproveButton(String text, Color color, double width) {
    print('width=======$width');
    return Container(
      alignment: Alignment.center,
      width: width,
      height: 40,
      decoration: BoxDecoration(
          color: text.contains('提交') ? color : Colors.transparent,
          border: Border.all(width: 1, color: color),
          borderRadius: BorderRadius.circular(4)),
      child: Text(
        text,
        style: TextStyle(
            fontSize: 16,
            color: text.contains('提交') ? ColorConfig.whiteColor : color),
      ),
    );
  }

  //办理人办理的表单
  backWorkerContent() {
    List<Widget> lists = [];
    for (var i = 0; i < approveDetailController.submitList.length; i++) {
      FormListModel listModel = approveDetailController.submitList[i];
      lists.add(Column(
        children: [
          const SizedBox(
            height: 1,
          ),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: listModel.status == 1
                    ? ColorConfig.whiteColor
                    : ColorConfig.btnGrayColor),
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  height: 46,
                  padding: const EdgeInsets.only(left: 12, right: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          listModel.modelFormName,
                          style: const TextStyle(
                              fontSize: 16, 
                              fontWeight: FontWeight.w500,
                              color: ColorConfig.mainTextColor),
                        ),
                      )
                    ],
                  ),
                ),
                Offstage(
                  offstage: 1 == 0, //是否折叠
                  child: Listener(
                    onPointerUp: (event) {
                      if (listModel.status == 0) {
                        toast('此表单不能填写');
                      }
                    },
                    child: AbsorbPointer(
                      absorbing: listModel.status == 0,
                      child: Column(
                        children: backWidgetWithFromData(listModel.widgetList),
                      ),
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ));
    }
    return lists;
  }

  backWidgetWithFromData(List modelList) {
    List<Widget> lists = [];

    for (var i = 0; i < modelList.length; i++) {
      FormWidetModel model = modelList[i];
      Widget widget = Container();
      if (model.type == 1) {
        widget = backWidgetForShortText(model);
      } else if (model.type == 2) {
        widget = backWidgetForLongText(model);
      } else if (model.type == 3 || model.type == 4) {
        widget = backWidgetForTime(model);
      } else if (model.type == 5 || model.type == 15) {
        //时间长度类5旧版 15新版
        widget = InkWell(
          onTap: () async {
            List dataList = [];

            List dayList = [];
            for (var i = 0; i < 365; i++) {
              dayList.add('$i天');
            }
            List hourList = [];
            for (var i = 0; i < 24; i++) {
              hourList.add('$i小时');
            }
            List minList = [];
            for (var i = 0; i < 60; i++) {
              minList.add('$i分钟');
            }
            if (model.dateChoose == 0) {
              dataList.add(dayList);
              dataList.add(hourList);
              dataList.add(minList);
            }
            if (model.dateChoose == 1) {
              dataList.add(dayList);
            }
            if (model.dateChoose == 2) {
              dataList.add(hourList);
              dataList.add(minList);
            }
            ChoosePickerDialog(dataList, model.contentList)
                .show((List indexList) {
              print('indexList---$indexList');
              bool isZ = true;
              for (var i = 0; i < indexList.length; i++) {
                if (indexList[i] > 0) {
                  isZ = false;
                }
              }
              if (isZ) {
                toast('请选择时长');
                return;
              } else {
                model.showContent = '';
                List showList = ['天', '小时', '分钟'];
                model.contentList = indexList;
                if (model.dateChoose == 0) {
                  model.showContent =
                      '${indexList[0]}天${indexList[1]}小时${indexList[2]}分钟';
                }
                if (model.dateChoose == 1) {
                  model.showContent = '${indexList[0]}天';
                }
                if (model.dateChoose == 2) {
                  model.showContent = '${indexList[0]}小时${indexList[1]}分钟';
                }

                if (model.type == 5) {
                  model.content = model.showContent;
                }
              }
              approveDetailController.update();
            });
          },
          child: backWidgetForChoose(
              model.title!,
              model.showContent.isEmpty ? model.prompt! : model.showContent,
              model.required!),
        );
      } else if (model.type == 6 || model.type == 16) {
        widget = InkWell(
          onTap: () async {
            var result =
                await Get.toNamed('/approve-choose-option', arguments: {
              'chooseCount': 1,
              'list': model.chooseFrameList,
              'selectList': model.contentList,
              'type': 0
            });
            model.contentList = result['list'];
            Map contentMap = model.contentList[0];
            if (model.type == 6) {
              model.content = contentMap['title'];
              model.showContent = contentMap['title'];
            }
            if (model.type == 16) {
              model.showContent = contentMap['title'];
              model.content = contentMap['frameId'];
            }

            approveDetailController.update();
          },
          child: backWidgetForChoose(
              model.title!,
              model.showContent.isEmpty ? model.prompt! : model.showContent,
              model.required!),
        );
      } else if (model.type == 7 || model.type == 17) {
        widget = InkWell(
          onTap: () async {
            var result =
                await Get.toNamed('/approve-choose-option', arguments: {
              'chooseCount': model.chooseCount,
              'list': model.chooseFrameList,
              'selectList': model.contentList,
              'type': 0
            });
            model.contentList = result['list'];

            List titleList = [];
            for (var i = 0; i < model.contentList.length; i++) {
              Map contentMap = model.contentList[i];
              titleList.add(contentMap['title']);
            }
            model.showContent = titleList.join('、');
            if (model.type == 7) {
              model.content = titleList.join('、');
            }

            approveDetailController.update();
          },
          child: backWidgetForChoose(
              model.title!,
              model.showContent.isEmpty ? model.prompt! : model.showContent,
              model.required!),
        );
      } else if (model.type == 8) {
        widget = InkWell(
          onTap: () async {
            var result =
                await Get.toNamed('/choose-all-org-members', arguments: {
              'model': OrgModel(approveDetailController.orgId),
              'type': 1,
              'chooseType': model.personType! + 1,
              'selectList': List.from(model.contentList)
            });
            model.contentList = result['list'];

            List nameList = [];
            for (var i = 0; i < model.contentList.length; i++) {
              MemberModel memberModel = model.contentList[i];
              nameList.add(memberModel.name);
            }

            model.showContent = nameList.join('、');
            approveDetailController.update();
          },
          child: backWidgetForChoose(
              model.title!,
              model.showContent.isEmpty ? model.prompt! : model.showContent,
              model.required!),
        );
      } else if (model.type == 9 || model.type == 18) {
        if (model.wordType == 0) {
          model.content = jsonEncode([approveDetailController.userId]);
          model.showContent = approveDetailController.name;
          if (model.type == 18) {
            model.content = approveDetailController.userId;
          }
        }
        if (model.wordType == 1) {
          model.content = '无';
          model.showContent = '未选择';
        }
        if (model.wordType == 2) {
          model.content = approveDetailController.currentDeptDic['name'];
          model.showContent = approveDetailController.currentDeptDic['name'];
          if (model.type == 18) {
            model.content = approveDetailController.currentDeptDic['deptId'];
          }
        }

        widget = InkWell(
          onTap: () async {},
          child: backWidgetForAuto(model),
        );
      } else if (model.type == 10 || model.type == 12) {
        widget = backWidgetForImage(model);
      } else if (model.type == 11) {
        String showContent = model.prompt!;

        if (model.addressChoose == 0 &&
            approveDetailController.postionInfo['address'] != null) {
          showContent = approveDetailController.postionInfo['address'];
          model.showContent = showContent;
          model.content = jsonEncode(approveDetailController.postionInfo.value);
        } else {
          if (model.addressChoose == 1) {
            showContent = model.showContent;
          }
        }
        widget = InkWell(
          onTap: () async {
            if (model.addressChoose == 1) {
              var result = await Channel().invokeMap(
                  Channel_Native_Approve_Postion,
                  model.content.isEmpty ? {} : jsonDecode(model.content));
              Map resultMap = result!;

              model.showContent = resultMap['address'];
              model.content = jsonEncode(resultMap);
              approveDetailController.update();
            }
          },
          child:
              backWidgetForChoose(model.title!, showContent, model.required!),
        );
      } else if (model.type == 13 || model.type == 19 || model.type == 20) {
        widget = backMackUpclockWidget(model);
      } else if (model.type == 14) {
        widget = InkWell(
          onTap: () async {
            var result = await Get.toNamed('/choose-city');
            if (result != null && result['city'] != null) {
              model.content = result['city'];
              approveDetailController.update();
            }
          },
          child: backWidgetForChoose(
              model.title!,
              model.content.isEmpty ? model.prompt! : model.content,
              model.required!),
        );
      } else {
        widget = Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(left: 15, right: 15),
          height: 48,
          margin: const EdgeInsets.only(top: 8),
          child: const Text('无对应类型控件'),
        );
      }
      lists.add(widget);
    }
    return lists;
  }

  //9自动获取固定数据 /** 文字类型:wordType 0.姓名类；1.职位类；2.部门类 **/
  backWidgetForAuto(FormWidetModel model) {
    return Column(
      children: [
        const SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          height: 48,
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                  width: 110,
                  height: 48,
                  alignment: Alignment.centerLeft,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: model.title,
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: model.required == '1' ? '*' : '',
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ]))),
              Expanded(
                  child: Container(
                alignment: Alignment.centerRight,
                child: Text(
                  model.showContent,
                  maxLines: 2,
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.desTextColor),
                ),
              ))
            ],
          ),
        )
      ],
    );
  }

  //1短文本类型
  backWidgetForShortText(FormWidetModel model, {int index = 0}) {
    ChinaTextEditController msgController = ChinaTextEditController();
    FocusNode focusNode = FocusNode();
    if (model.type == 1) {
      model.msgController ??= ChinaTextEditController();
      model.msgController!.text = model.showContent;
      model.msgController!.value = TextEditingValue(
          text: model.showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream,
              offset: model.showContent.length)));
      msgController = model.msgController!;
      focusNode = model.focusNode;
    } else if (model.type == 19) {
      String showContent = model.contentList[index];
      msgController = model.textECList[index];
      msgController.text = showContent;
      msgController.value = TextEditingValue(
          text: showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream, offset: showContent.length)));
      focusNode = model.focusNodeList[index];
    }
    String suffix = model.suffix!;
    return Column(
      children: [
        const SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Container(
                      width: 110,
                      height: 48,
                      alignment: Alignment.centerLeft,
                      child: RichText(
                          maxLines: 2,
                          text: TextSpan(
                              text: model.title,
                              style: const TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.mainTextColor),
                              children: [
                                TextSpan(
                                    text: model.required == '1' ? '*' : '',
                                    style: const TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.deleteCorlor))
                              ]))),
                  Offstage(
                    offstage: suffix.isEmpty,
                    child: Container(
                        width: 110,
                        height: 48,
                        alignment: Alignment.centerLeft,
                        child: RichText(
                            maxLines: 2,
                            text: TextSpan(
                              text: suffix,
                              style: const TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.mainTextColor),
                            ))),
                  )
                ],
              ),
              Expanded(
                  child: Container(
                height: suffix.isEmpty ? 48 : 96,
                alignment: Alignment.centerRight,
                padding: EdgeInsets.only(top: suffix.isEmpty ? 0 : 48),
                child: TextField(
                  onSubmitted: (value) {},
                  onTap: () {},
                  focusNode: focusNode,
                  onChanged: (value) {
                    //变化时给model赋值
                    if (model.type == 1) {
                      model.showContent = value;
                      model.content = value;
                    } else if (model.type == 19) {
                      model.contentList[index] = value;
                    }
                  },
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(model.wordLimit)
                  ],
                  controller: msgController,
                  keyboardType: model.keyBoard == 2
                      ? const TextInputType.numberWithOptions(decimal: true)
                      : TextInputType.text,
                  textInputAction: TextInputAction.unspecified,
                  textAlign: TextAlign.end,
                  // controller: controller.nameController,
                  style: const TextStyle(
                    color: ColorConfig.mainTextColor,
                    fontSize: 14,
                  ),
                  decoration: InputDecoration(
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: model.prompt,
                      hintStyle: const TextStyle(
                        color: ColorConfig.desTextColor,
                        fontSize: 14,
                      )),
                ),
              ))
            ],
          ),
        )
      ],
    );
  }

  //2长文本类型
  backWidgetForLongText(FormWidetModel model, {int index = 0}) {
    ChinaTextEditController msgController = ChinaTextEditController();
    FocusNode focusNode = FocusNode();
    if (model.type == 2) {
      model.msgController ??= ChinaTextEditController();
      model.msgController!.text = model.showContent;
      model.msgController!.value = TextEditingValue(
          text: model.showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream,
              offset: model.showContent.length)));
      msgController = model.msgController!;
      focusNode = model.focusNode;
    } else if (model.type == 20) {
      String showContent = model.contentList[index];
      msgController = model.textECList[index];
      msgController.text = showContent;
      msgController.value = TextEditingValue(
          text: showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream, offset: showContent.length)));
      focusNode = model.focusNodeList[index];
    }
    return Column(
      children: [
        const SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          constraints: const BoxConstraints(minHeight: 130),
          padding: const EdgeInsets.only(left: 15, right: 15, bottom: 10),
          child: Column(
            children: [
              Container(
                  alignment: Alignment.centerLeft,
                  width: double.infinity,
                  height: 44,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: model.title,
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: model.required == '1' ? '*' : '',
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ]))),
              Container(
                child: TextField(
                  onSubmitted: (value) {},
                  onChanged: (value) {
                    if (model.type == 2) {
                      model.showContent = value;
                      model.content = value;
                    } else if (model.type == 20) {
                      model.contentList[index] = value;
                    }
                  },
                  controller: msgController,
                  maxLines: null,
                  minLines: 1,
                  focusNode: focusNode,
                  // maxLength: 30,
                  textInputAction: TextInputAction.unspecified,
                  style: const TextStyle(
                    color: ColorConfig.mainTextColor,
                    fontSize: 14,
                  ),
                  decoration: InputDecoration(
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: model.prompt,
                      hintStyle: const TextStyle(
                        color: ColorConfig.desTextColor,
                        fontSize: 14,
                      )),
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  //4时间段类型 （1时间点2时间段）(0限时提交1正常提交)
  backWidgetForTime(FormWidetModel model) {
    int cellCount = 1;
    if (model.type == 4) {
      cellCount = 2;
    }
    if (model.isLimit! > 0) {
      cellCount += 1;
    }
    String rightStr = model.prompt!;
    String bottomRightStr = model.secPrompt!;
    if (model.content.isNotEmpty) {
      if (model.type == 3) {
        rightStr = model.showContent;
      }
    }
    if (model.type == 4) {
      if (model.longitudeStr.isNotEmpty) {
        rightStr = BaseInfo().formatTimestamp(
            int.parse(model.longitudeStr),
            model.dateChoose == 0
                ? 'yyyy-MM-dd HH:mm'
                : model.dateChoose == 1
                    ? 'yyyy-MM-dd'
                    : 'HH:mm');
      }
      if (model.latitudeStr.isNotEmpty) {
        bottomRightStr = BaseInfo().formatTimestamp(
            int.parse(model.latitudeStr),
            model.dateChoose == 0
                ? 'yyyy-MM-dd HH:mm'
                : model.dateChoose == 1
                    ? 'yyyy-MM-dd'
                    : 'HH:mm');
      }
    }
    return Column(
      children: [
        const SizedBox(
          height: 1,
        ),
        Container(
          height: (cellCount - 1) * 49 + 48,
          child: Column(
            children: [
              Offstage(
                  offstage: model.isLimit == 0,
                  child: Column(
                    children: [
                      Container(
                        height: 48,
                        child: Container(
                          color: ColorConfig.whiteColor,
                          height: 48,
                          padding: const EdgeInsets.only(left: 15, right: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                width: 110,
                                height: 48,
                                alignment: Alignment.centerLeft,
                                child: const Text(
                                  '限时提交',
                                  maxLines: 2,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                              ),
                              Expanded(
                                  child: Container(
                                alignment: Alignment.centerRight,
                                child: Text(
                                  model.limitRemind!,
                                  maxLines: 2,
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                ),
                              ))
                            ],
                          ),
                        ),
                      ),
                      const Divider(
                        height: 1,
                        color: ColorConfig.backgroundColor,
                      )
                    ],
                  )),
              InkWell(
                onTap: () {
                  if (model.type == 3 && model.nowChoose != 1) {
                    DDDatePickerDialog(model.content.isEmpty
                            ? DateTime.now()
                            : DateTime.fromMillisecondsSinceEpoch(
                                int.parse(model.content)))
                        .show((time) {
                      model.content = time;
                      model.showContent = BaseInfo().formatTimestamp(
                          int.parse(model.content),
                          model.dateChoose == 0
                              ? 'yyyy-MM-dd HH:mm'
                              : model.dateChoose == 1
                                  ? 'yyyy-MM-dd'
                                  : 'HH:mm');
                      approveDetailController.update();
                    }, model.dateChoose!);
                  }
                  if (model.type == 4) {
                    DDDatePickerDialog(model.longitudeStr.isEmpty
                            ? DateTime.now()
                            : DateTime.fromMillisecondsSinceEpoch(
                                int.parse(model.longitudeStr)))
                        .show((time) {
                      model.longitudeStr = time;
                      approveDetailController.update();
                    }, model.dateChoose!);
                  }
                },
                child: backWidgetForChoose(
                    model.title!, rightStr, model.required!,isHiddenArrow: model.type == 3 && model.nowChoose == 1),
              ),
              Offstage(
                offstage: model.type == 3,
                child: Column(
                  children: [
                    const Divider(
                      height: 1,
                      color: ColorConfig.backgroundColor,
                    ),
                    InkWell(
                      onTap: () {
                        if (model.type == 4) {
                          DDDatePickerDialog(model.latitudeStr.isEmpty
                                  ? DateTime.now()
                                  : DateTime.fromMillisecondsSinceEpoch(
                                      int.parse(model.latitudeStr)))
                              .show((time) {
                            model.latitudeStr = time;
                            approveDetailController.update();
                          }, model.dateChoose!);
                        }
                      },
                      child: backWidgetForChoose(
                          model.secTitle!, bottomRightStr, model.required!),
                    ),
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  //10图片类型
  backWidgetForImage(FormWidetModel model) {
    return Column(
      children: [
        const SizedBox(
          height: 1,
        ),
        Container(
          width: double.infinity,
          color: ColorConfig.whiteColor,
          padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                height: 48,
                alignment: Alignment.centerLeft,
                child: RichText(
                    maxLines: 2,
                    text: TextSpan(
                        text: model.title,
                        style: const TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                        children: [
                          TextSpan(
                              text: model.required == '1' ? '*' : '',
                              style: const TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.deleteCorlor))
                        ])),
              ),
              Column(
                children: backImageData(model),
              )
            ],
          ),
        )
      ],
    );
  }

  backImageData(FormWidetModel model, {bool isShowUpload = true}) {
    //isShowUpload 是否是办理表单
    List<Widget> lists = [];

    if (model.fileList.isNotEmpty) {
      for (var i = 0; i < model.fileList.length; i++) {
        Map fileMap = model.fileList[i];
        String fileName = fileMap['fileName'];
        dynamic path = fileMap['filePath'];
        dynamic fileSize = fileMap['fileSize'] ?? 0;
        if (!isShowUpload) {
          path = fileMap['url'];
        }
        String iconStr = BaseInfo().panFileType(fileName);
        lists.add(Column(
          children: [
            InkWell(
                onTap: () {
                  List fileList = [];
                  for (var j = 0; j < model.fileList.length; j++) {
                    Map fileMap = model.fileList[j];
                    fileList.add(fileMap['filePath']);
                  }

                  if (model.type == 10) {
                    Get.toNamed(Routes.COMMON_SCROLL_PIC,
                        arguments: {'imageUrls': fileList, 'currentIndex': i});
                  }
                  if (model.type == 12) {
                    if (isShowUpload) {
                      approveDetailController.preivewFile(path);
                    } else {
                      if (BaseInfo().canShowPreView(fileName)) {
                        List extList = fileName.split('.');
                        String ext = '${extList.last}';

                        //approveDetailController.getPreviewUrl(path, fileName);
                        approveDetailController.getDownLoadPath(
                            path, ext, fileSize);
                      } else if (BaseInfo().isImageFormat(fileName)) {
                        Get.toNamed(Routes.COMMON_SCROLL_PIC, arguments: {
                          'imageUrls': [path],
                          'currentIndex': 0
                        });
                      } else {
                        toast('格式不支持,请到PC端预览');
                      }
                    }
                  }
                },
                child: Container(
                  width: double.infinity,
                  height: 46,
                  padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: ColorConfig.backgroundColor),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        child: model.type == 10
                            ? ExtendedImage.file(File(path))
                            : Image.asset(iconStr),
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      Expanded(
                          child: Container(
                        child: Text(
                          fileName,
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                        ),
                      )),
                      Offstage(
                        offstage: !isShowUpload,
                        child: InkWell(
                          onTap: () {
                            model.fileList.removeAt(i);
                            model.contentList.removeAt(i);
                            approveDetailController.update();
                          },
                          child: Container(
                            width: 20,
                            height: 20,
                            child: Image.asset(
                                'assets/images/3.0x/approve_delete.png'),
                          ),
                        ),
                      )
                    ],
                  ),
                )),
            const SizedBox(
              height: 8,
            )
          ],
        ));
      }
    }

    Widget upWidget = InkWell(
      onTap: () {
        if (model.type == 10) {
          SettingWidget().showCupertinoActionSheetForPage(
              Get.context!, ['拍照', '从相册选取'], (value) {
            if (value == 0) {
              approveDetailController.tackPhotoWithCamera(model);
            } else {
              approveDetailController.pickPhonto(model);
            }
          });
        } else {
          approveDetailController.pickFile(model);
        }
      },
      child: Container(
        width: double.infinity,
        height: 46,
        padding: const EdgeInsets.only(bottom: 16),
        child: Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 18,
                height: 18,
                child: Image.asset('assets/images/3.0x/approve_upLoad.png'),
              ),
              Container(
                child: Text(
                  model.type == 10 ? ' 上传图片' : ' 上传附件',
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.themeCorlor),
                ),
              )
            ],
          ),
        ),
      ),
    );
    if (isShowUpload) {
      lists.add(upWidget);
    }

    return lists;
  }

  //13补卡样式控件
  backMackUpclockWidget(FormWidetModel model) {
    return Column(
      children: [
        const SizedBox(
          height: 1,
        ),
        Column(
          children: backMakeupTime(model),
        ),
        model.contentList.length == model.maxNum &&
                model.type != 19 &&
                model.type != 20
            ? Container(
                alignment: Alignment.center,
                height: 40,
                child: Text(
                  '最多可提交 ${model.maxNum} 个补卡时间',
                  style: const TextStyle(
                      fontSize: 12, color: ColorConfig.desTextColor),
                ),
              )
            : InkWell(
                onTap: () {
                  model.contentList.add('');
                  if (model.type == 19 || model.type == 20) {
                    model.textECList.add(ChinaTextEditController());
                    model.focusNodeList.add(FocusNode());
                  }
                  approveDetailController.update();
                },
                child: Container(
                  height: 40,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                      ),
                      Container(
                        child: Text(
                          model.type == 13
                              ? ' 添加补卡'
                              : model.type == 19
                                  ? ' 添加短文本'
                                  : model.type == 20
                                      ? ' 添加长文本'
                                      : '',
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.themeCorlor),
                        ),
                      )
                    ],
                  ),
                ),
              )
      ],
    );
  }

  backMakeupTime(FormWidetModel model) {
    List<Widget> lists = [];
    for (var i = 0; i < model.contentList.length; i++) {
      lists.add(Column(
        children: [
          Container(
            width: double.infinity,
            height: 50,
            padding: const EdgeInsets.only(left: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: model.type == 13
                              ? '时间 ${i + 1}'
                              : model.type == 19
                                  ? '短文本 ${i + 1}'
                                  : model.type == 20
                                      ? '长文本 ${i + 1}'
                                      : '',
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: model.required == '1' ? '*' : '',
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ])),
                ),
                Offstage(
                  offstage: model.contentList.length == 1,
                  child: InkWell(
                    onTap: () {
                      model.contentList.removeAt(i);
                      if (model.type == 19 || model.type == 20) {
                        model.textECList.removeAt(i);
                        model.focusNodeList.removeAt(i);
                      }
                      approveDetailController.update();
                    },
                    child: Container(
                      width: 50,
                      height: 50,
                      alignment: Alignment.center,
                      child: SizedBox(
                        width: 18,
                        height: 18,
                        child: Image.asset(
                            'assets/images/3.0x/approve_delete.png'),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          InkWell(
            onTap: () {
              if (model.type == 13) {
                String timeSatmp = model.contentList[i];
                DDDatePickerDialog(timeSatmp.isEmpty
                        ? DateTime.now()
                        : DateTime.fromMillisecondsSinceEpoch(
                            int.parse(timeSatmp)))
                    .show((time) {
                  model.contentList[i] = time;
                  approveDetailController.update();
                }, model.dateChoose!);
              }
            },
            child: model.type == 13
                ? backWidgetForChoose(
                    model.title!,
                    model.contentList[i] == ''
                        ? '请选择'
                        : BaseInfo().formatTimestamp(
                            int.parse(model.contentList[i]),
                            model.dateChoose == 0
                                ? 'yyyy-MM-dd HH:mm'
                                : model.dateChoose == 1
                                    ? 'yyyy-MM-dd'
                                    : 'HH:mm'),
                    model.required!)
                : model.type == 19
                    ? backWidgetForShortText(model, index: i)
                    : model.type == 20
                        ? backWidgetForLongText(model, index: i)
                        : Container(),
          ),
        ],
      ));
    }
    return lists;
  }

  backWidgetForChoose(String leftStr, String rightStr, String required, {bool isHiddenArrow = false}) {
    return Column(
      children: [
        const SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          height: 48,
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                  width: 110,
                  height: 48,
                  alignment: Alignment.centerLeft,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: leftStr,
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: required == '1' ? '*' : '',
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ]))),
              Expanded(
                  child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                      child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      rightStr,
                      maxLines: 2,
                      style: const TextStyle(
                          fontSize: 14, color: ColorConfig.desTextColor),
                    ),
                  )),
                  Container(
                    child: isHiddenArrow?Container():Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 5,
                        ),
                        SizedBox(
                          width: 9,
                          height: 17,
                          child: Image.asset('assets/images/3.0x/mine_right.png'),
                        )
                      ],
                    ),
                  )
                ],
              ))
            ],
          ),
        )
      ],
    );
  }
}
