import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/cos/pan_datasource.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/workStand/dialog/approve_dialog.dart';
import 'package:flutter_mixed/app/modules/workStand/models/form_list_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/form_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/procee_model.dart';
import 'package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart';
import 'package:flutter_mixed/app/utils/cos_helper.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/base_info/info.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../permission/permission_util.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';
import '../../models/filemodel.dart';

class ApproveDetailController extends GetxController {
  String orgId = '';
  String? corgId = '';
  String? corgName = '';
  String approveId = ''; //审批id
  String name = '';
  String userId = '';
  String uploadBucket = '';
  RxList deptList = [].obs;
  Map currentDeptDic = {};
  List upModelArray = [];
  Completer? completer;

  RxMap dataMap = {}.obs;
  RxList contentModelList = [].obs; //展示表单的数组
  String initiatorName = ''; //发起人姓名

  RxList processModelList = [].obs; //流程数组
  RxList commentList = [].obs; //评论数组
  RxList buttonList = [].obs; //底部按钮数组//1.同意拒绝 2.办理提交 3.加审 4.催办 5.撤回 6.评论

  RxList submitList = [].obs; //办理人提交的表单数组

  bool approveInfoLoad = false;
  bool commonDataLoad = false;

  StreamSubscription? subscription;
  Map signMap = {};
  bool isNeedSign = false;
  ApproveDialog? approveDialog;

  RxString endStr = ''.obs; //审批用时

  RxBool isNative = false.obs; //是否是原生跳转

  RxMap postionInfo = {}.obs;

  bool isAuth = true; //导出权限

  List<FocusNode> focusNodeList = [];

  @override
  void onInit() async {
    super.onInit();

    if (Get.arguments != null) {
      orgId = Get.arguments['orgId'];
      approveId = Get.arguments['approveId'];
    }
    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    name = userInfo['name'];
    userId = userInfo['userId'];
    // Map cosInfo = await UserDefault.getData(Define.COSTOKENKEY);
    // uploadBucket = cosInfo['bucket'];

    subscription = eventBus.on<Map>().listen((event) {
      print('evetn-------$event');
      if (event['approveId'] != null && event['fileInfo'] != null) {
        if (event['approveId'] == approveId) {
          Map fileInfo = event['fileInfo'];
          signMap = fileInfo;
          FileModel fileModel = FileModel();
          fileModel.progress = 0;
          fileModel.savePath = signMap['filePath'];
          fileModel.filePath = signMap['filePath'];
          fileModel.fileName = signMap['fileName'];
          fileModel.hash = BaseInfo().generateMD5(signMap['filePath']);
          fileModel.fileType = 0;
          upModelArray.clear();
          upModelArray.add(fileModel);
          getFileIds(FormWidetModel());
        }
      }
      if (event['native'] == 1) {
        if (event['route'] == Routes.APPROVE_DETAIL) {
          isNative.value = true;

          Map? nativeArgument;
          if (event['arguments'] is String) {
            nativeArgument = jsonDecode(event['arguments']);
          } else {
            nativeArgument = event['arguments'];
          }

          orgId = nativeArgument!['orgId'];
          approveId = nativeArgument['approveId'];

          // Map userInfo = event['userInfo'];
          // name = userInfo['name'];
          // userId = userInfo['userId'];

          // uploadBucket = event['bucket'];

          getApproveInfo();
          getCommentData();
          getDepts();
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();

    if (orgId.isNotEmpty) {
      getApproveInfo();
      getCommentData();
      getDepts();
    }
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

  //获取所在所有部门
  getDepts() {
    DioUtil().get('${ORGApi.GETDEPTS}/$orgId', null, true, () {},isShowLoading: false).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        deptList.value = data['data'];
        if (deptList.isNotEmpty) {
          currentDeptDic = deptList[0];
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取审批详情
  getApproveInfo() {
    DioUtil()
        .get('${ApproveApi.APPROVEINFO}/$approveId', null, true, () {},isShowLoading: false)
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        focusNodeList.clear();
        approveInfoLoad = true;
        update();

        contentModelList.clear();
        dataMap.value = data['data'];

        List approveContentList = dataMap['approveContentList'];

        buttonList.value = dataMap['buttonList'];

        Map initiatorMap = dataMap['initiator'];
        initiatorName = initiatorMap['name'];

        bool isHavePositonAuto = false;
        for (var i = 0; i < approveContentList.length; i++) {
          Map contentMap = approveContentList[i];

          List modelFormContentList = contentMap['modelFormContentList'];

          List fromModelList = [];
          for (var j = 0; j < modelFormContentList.length; j++) {
            Map modelMap = modelFormContentList[j];

            List widgetContentList = modelMap['widgetContentList'];

            List widgetModelList = [];
            for (var k = 0; k < widgetContentList.length; k++) {
              Map<String, dynamic> widgetMap = widgetContentList[k];

              FormWidetModel model = FormWidetModel.fromJson(widgetMap);
              model.modelFormName = modelMap['modelFormName'];
              model.modelType = contentMap['type'];
              model.workUser = contentMap['user'];
              widgetModelList.add(model);
              if (model.type == 11 && model.addressChoose == 0) {
                isHavePositonAuto = true;
              }
              if (model.type == 1 || model.type == 2) {
                focusNodeList.add(model.focusNode);
              }
            }
            fromModelList.add(widgetModelList);
          }
          contentModelList.add(fromModelList);
        }

        //判断是否有自动获取地点widget
        if (isHavePositonAuto) {
          var result =
              await Channel().invokeMap(Channel_Native_Approve_PostionAuto);
          postionInfo.value = result ?? {};
          update();
        }
        //审批结束时间
        if (dataMap['endTime'] > 0) {
          int doneTime = dataMap['endTime'] - dataMap['createTime'];
          DateTime time = DateTime.fromMillisecondsSinceEpoch(doneTime);
          DateTime zeroTime = DateTime.fromMillisecondsSinceEpoch(0);
          var differenceDay = time.difference(zeroTime);

          DateTime hourTime = DateTime.fromMillisecondsSinceEpoch(
              doneTime - differenceDay.inDays * 24 * 60 * 60 * 1000);
          var differenceHour = hourTime.difference(zeroTime);

          DateTime minuteTime = DateTime.fromMillisecondsSinceEpoch(doneTime -
              differenceDay.inDays * 24 * 60 * 60 * 1000 -
              differenceHour.inHours * 60 * 60 * 1000);
          var differenceMinute = minuteTime.difference(zeroTime);

          int minutes = differenceMinute.inMinutes + 1; //不到一分按照一分钟算
          int hour = differenceHour.inHours;
          int day = differenceDay.inDays;
          if (minutes == 60) {
            minutes = 0;
            hour += 1;
          }
          if (hour == 24) {
            hour = 0;
            day += 1;
          }
          endStr.value =
              '${day == 0 ? '' : '$day天'}${hour == 0 ? '' : '$hour小时'}${minutes == 0 ? '' : '$minutes分钟'}';
        }

        //办理人表单数据
        List workerModelList = dataMap['modelFormList'];
        if (workerModelList.isNotEmpty) {
          submitList.value =
              workerModelList.map((e) => FormListModel.fromJson(e)).toList();
        }

        //流程
        List approveProcessList = dataMap['approveProcessList'];
        processModelList.value =
            approveProcessList.map((e) => ProcessModel.fromJson(e)).toList();

        //是否需要签名
        isNeedSign = dataMap['needSignature'] == 1 ? true : false;

        //刷新未读
        if (isNative.value) {
          Channel().invoke(Channel_Native_ApproveUnread,
              {'orgId': orgId, 'handleId': approveId});
        }

        //获取子公司名称
        if (dataMap['corgInfo'] != null) {
          corgId = dataMap['corgInfo']['id'];
          corgName = dataMap['corgInfo']['name'];
        }else{
          if (dataMap['orgInfo'] != null) {
            corgId = dataMap['orgInfo']['id'];
            corgName = dataMap['orgInfo']['name'];
          }
        }
        Channel().invoke(Channel_sendIM_synchronous, {'handleId': approveId});
        bool isWorkFlow = Get.isRegistered<WorkFlowController>();
        if (isWorkFlow) {
          WorkFlowController workFlowController = Get.find();
          workFlowController.getAllCompanyUntreated();
        }
        eventBus.fire({'refreshUnreadCount': 1});
        eventBus.fire({'listPage_refreshNotRead': approveId});
        update();
      } else {
        toast('${data['msg']}');
        if (data['code'] == 2005) {
          //撤回的审批被重新提交
          if (isNative.value) {
            onClose();
            dispose();
            Channel().invoke(Channel_Native_Back, {});
          } else {
            Get.back();
          }
        }
      }
    });
  }

  //撤回审批
  withDrawApprove() {
    DioUtil()
        .get('${ApproveApi.APPROVEWITHDRAW}/$approveId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('撤回成功');
        eventBus.fire({'refreshApproveListPage': 4});
        if (isNative.value) {
          Channel().invoke(Channel_Native_Back, {});
        } else {
          if (isNative.value) {
            onClose();
            dispose();
            Channel().invoke(Channel_Native_Back, {});
          } else {
            Get.back(result: {'withDraw': approveId});
          }
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取评论
  getCommentData() {
    DioUtil()
        .get('${ApproveApi.APPROVECOMMENT}/$approveId', null, true, () {},isShowLoading: false)
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        commonDataLoad = true;
        update();
        commentList.value = data['data'];
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //催办审批
  urgingApprove() {
    DioUtil()
        .get('${ApproveApi.APPROVEURGING}/$approveId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('催办成功');
      } else {
        toast('${data['msg']}');
      }
    });
  }

  jugeDataIsDone() {
    bool isDone = true;
    bool isWrong = false; //时间段类结束时间是否小于开始时间
    String errorMsg = ''; //错误提示
    for (var i = 0; i < submitList.length; i++) {
      FormListModel listModel = submitList[i];
      List modelList = listModel.widgetList;
      for (var j = 0; j < modelList.length; j++) {
        FormWidetModel model = modelList[j];
        if (model.required == '1') {
          Map modelMap = model.toJson();
          if (modelMap['isDone'] == 0) {
            isDone = false;
          }
        }
        if (model.type == 4) {
          if (model.longitudeStr.isNotEmpty && model.latitudeStr.isNotEmpty) {
            if (int.parse(model.latitudeStr) <= int.parse(model.longitudeStr)) {
              isWrong = true;
              errorMsg = '${model.secTitle}需大于${model.title}';
            }
          }
        }
      }
    }
    if (!isDone) {
      toast('有必填项未选择');
      return;
    }
    if (isWrong) {
      toast(errorMsg);
      return;
    }
    submitApprove();
  }

  //办理人提交审批
  submitApprove() {
    List modelFormContentList = [];
    for (var i = 0; i < submitList.length; i++) {
      Map formContentMap = {};

      List widgetContentList = [];
      FormListModel listModel = submitList[i];
      formContentMap['modelFormName'] = listModel.modelFormName;
      List modelList = listModel.widgetList;
      for (var j = 0; j < modelList.length; j++) {
        FormWidetModel model = modelList[j];
        Map modelMap = model.toJson();
        modelMap.remove('isDone');
        widgetContentList.add(modelMap);
      }
      formContentMap['widgetContentList'] = widgetContentList;
      modelFormContentList.add(formContentMap);
    }

    Map param = {
      'approveAssigneeId': dataMap['approveAssigneeId'],
      'approveId': dataMap['approveId'],
      'approveNodeId': dataMap['approveNodeId'],
      'orgId': orgId,
      'modelFormContentList': modelFormContentList
    };
    DioUtil().put(ApproveApi.APPROVETRANSACT, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('办理成功');
        if (isNative.value) {
          onClose();
          dispose();
          Channel().invoke(Channel_Native_Back, {});
        } else {
          Get.back();
        }
        eventBus.fire({
          'refreshApproveListPage': 2,
          'deleteId': dataMap['approveAssigneeId']
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //签名回掉
  postSignatureFile(FileModel fileModel) {
    Map param = {
      'type': 1,
      'orgId': orgId,
      'signatureFile': {
        'fileName': fileModel.fileName,
        'fileId': fileModel.fileId,
        'fileType': fileModel.fileType,
        'hash': fileModel.hash
      }
    }; //type1 手机签名 2pc扫码签名
    DioUtil().post(ApproveApi.APPROVESIGNCODE, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        fileModel.fileId = data['data'];
        eventBus.fire({
          'eventName': 'approvedialog',
          'signPath': fileModel.filePath,
          'fileId': fileModel.fileId
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //审批同意或拒绝
  putApproveFlow(int opinion, String content, bool isUse, String fileId) {
    //option 1同意 2拒绝

    Map param = {
      'approveAssigneeId': dataMap['approveAssigneeId'],
      'approveId': dataMap['approveId'],
      'approveNodeId': dataMap['approveNodeId'],
      'opinion': opinion,
      'content': content,
      'source': 1
    };
    if (isNeedSign && opinion == 1) {
      param['lastSignature'] = isUse ? 1 : 0;
      param['signatureFileId'] = fileId;
    }

    eventBus.fire({'listPage_IsNeedRefreshScreenData': 0});
    DioUtil().put(ApproveApi.APPROVEFLOW_V2, param, true, () {
      eventBus.fire({'listPage_IsNeedRefreshScreenData': 1});
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        eventBus.fire({'listPage_IsNeedRefreshScreenData': 1});
        eventBus.fire({
          'refreshApproveListPage': 2,
          'deleteId': dataMap['approveAssigneeId']
        });
        eventBus.fire({'refreshUnreadCount': 1});
        if (isNative.value) {
          print('审批 同意或拒绝，调用原生...');
          Channel().invoke(Channel_Native_Back, {});
          onClose();
          dispose();
        } else {
          print('审批 同意或拒绝，非原生...');
          Get.back();
        }
      } else {
        eventBus.fire({'listPage_IsNeedRefreshScreenData': 1});
        toast('${data['msg']}');
      }
    });
  }

  //以下和发起审批相同待封装

  tackPhotoWithCamera(FormWidetModel model) async {
    var r = await PermissionUtil.checkCameraPermission(Get.context!,
        tip: cameraPhotoPermissionTip);
    if (!r) return;
    File? file;
    if (Platform.isIOS) {
      //iOS使用插件会出现相机功能一直启用，右上角绿点一直展示的问题
      var result = await Channel()
          .invokeMap(Channel_call_iOS_camera, {'allowsEditing': 0});

      String filePath = result!['filePath'];
      file = File(filePath);
    } else {
      final AssetEntity? entity =
          await CameraPicker.pickFromCamera(Get.context!);

      file = await entity!.file;
    }
    if (file == null) return;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    await startDealFile(fileModel);
    update();
    getFileIds(model);
  }

  pickPhonto(FormWidetModel model) async {
    var r = await PermissionUtil.checkGalleryPermission(Get.context!,
        tip: takePhotoPermissionTip);
    if (!r) return;

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: const AssetPickerConfig(
            maxAssets: 9, requestType: RequestType.image));
    if (assets == null) return;
    for (var i = 0; i < assets.length; i++) {
      AssetEntity entity = assets[i];
      File? file = await entity.file;

      FileModel fileModel = FileModel();
      fileModel.hash = '';
      fileModel.fileId = '';
      List fileNameList = file!.path.split('/');
      fileModel.fileName = fileNameList.last;
      fileModel.fileType = 0;
      fileModel.filePath = file.path;
      fileModel.file = file;

      await startDealFile(fileModel);
    }
    update();
    getFileIds(model);
  }

  //选择文件
  pickFile(FormWidetModel model) async {
    FilePickerResult? result;
    try {
      result = await FilePicker.platform.pickFiles();
    } catch (e) {
      toast('请在担当权限设置中开启：允许使用文件和文档权限');
    }

    if (result != null) {
      File file = File(result.files.single.path!);
      FileModel fileModel = FileModel();
      fileModel.hash = '';
      fileModel.fileId = '';
      List fileNameList = file.path.split('/');
      fileModel.fileName = fileNameList.last;
      fileModel.fileType = 1;
      fileModel.filePath = file.path;
      fileModel.file = file;

      await startDealFile(fileModel);
    } else {
      // User canceled the picker
    }
    update();
    getFileIds(model);
  }

  //获取wps查看附件的url
  getPreviewUrl(String fileUrl, String fileName) {
    // Map params = {'fileUrl': fileUrl, 'ext': ext};
    List extList = fileName.split('.');
    String ext = '${extList.last}';
    DioUtil()
        .get('${PanApi.GETFILEPREVIEWURL}?fileUrl=$fileUrl&ext=$ext', null,
            true, () {})
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        //跳转原生web
        Map dataDic = data['data'];
        String wpsUrl = dataDic['wpsUrl'];
        String token = dataDic['token'];
        int platform = 1;
        if (Platform.isIOS) {
          platform = 2;
        }
        //获取appVersion
        String appVersion = await BaseInfo().getAppVersion();
        //获取path
        String previewPath = '';
        if (EnvConfig.mEnv == Env.Product) {
          previewPath = 'preview-file-h5';
        } else {
          previewPath = 'ddbes-preview-file-h5';
        }
        openWebView({
          'url':
          '${Host.WEBHOST}/$previewPath/index.html#/?platform=$platform&appVersion=$appVersion',
          'isWebNavigation': 1,
          'fileInfo': {'fileName': fileName, 'url': wpsUrl, 'token': token}
        });

      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取下载地址
  getDownLoadPath(String url, String ext, int fileSize) async {
    List fileIdList = url.split('?');
    String startStr = fileIdList.first;
    List startList = startStr.split('/');
    String endPath = '${startList.last}.$ext';
    String docment = (await getApplicationDocumentsDirectory()).path;
    String savePath = '$docment/${Define.APPROVEDETAILPATH}/$endPath';
    String loadPath = '$docment/${Define.APPROVELOADTPATH}/$endPath';
    File saveFile = File(savePath);
    if (saveFile.existsSync()) {
      preivewFile(savePath);
    } else {
      int maxSize = 200 * 1024 * 1024; //最大预览
      if (maxSize < fileSize) {
        toast('文件过大,请到PC端预览');
        return;
      }
      Directory directory = Directory('$docment/${Define.APPROVEDETAILPATH}');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      Directory loadDirectory =
          Directory('$docment/${Define.APPROVELOADTPATH}');
      if (!await loadDirectory.exists()) {
        await loadDirectory.create(recursive: true);
      } else {
        File loadFile = File(loadPath);
        if (loadFile.existsSync()) {
          await loadFile.delete();
        }
      }
      downLoadFiel(url, savePath, loadPath);
    }
  }

  void preivewFile(String path) async {
    CosHelper.openFile(path);
  }

  //下载附件并查看
  downLoadFiel(String url, String savePath, loadPath) {
    DioUtil().downLoadFile(url, savePath, loadPath, () {
      toast('获取文件失败');
    }, (finishPath) {
      preivewFile(finishPath);
    });
  }

  Future startDealFile(FileModel fileModel) async {
    completer = Completer<String>();
    await saveFile(fileModel).then((value) {
      fileModel.savePath = value;
      fileModel.hash = BaseInfo().generateMD5(value);
      upModelArray.add(fileModel);
      if (completer!.isCompleted == false) {
        completer!.complete('');
      }
    });
    return completer!.future;
  }

  //保存图片
  Future<String> saveFile(FileModel fileModel) async {
    File file = fileModel.file!;
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;

    String startPath = '$docment/approve/images';
    if (fileModel.fileType == 1) {
      startPath = '$docment/approve/files';
    }
    String filePath = '$startPath/$fileName';

    Directory directory = Directory(startPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  //获取fileId
  getFileIds(FormWidetModel model) async{
    try {
      var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await panDatasource.getPanFields(upModelArray.length);
      if (resp.success()) {
        List? idList = resp.data;
        if (idList == null) return;
        for (var i = 0; i < idList.length; i++) {
          FileModel fileModel = upModelArray[i];
          fileModel.fileId = idList[i];
        }
        dealUploadList(model);
      }else{
        upModelArray.clear;
        toast(resp.msg);
      }     
    } catch (e) {
      upModelArray.clear;
      toast(LoginApi.ERROR_MSG);
    }
  }

  //处理上传数组
  dealUploadList(FormWidetModel model) {
    List cosUpList = [];
    for (var i = 0; i < upModelArray.length; i++) {
      FileModel fileModel = upModelArray[i];
      if (fileModel.fileId != null) {
        cosUpList.add(fileModel);
      }
      
    }
    startUploadFileList(cosUpList, model);
  }

  startUploadFileList(List cosList, FormWidetModel model) async {
    if (cosList.isEmpty) {
      return;
    }
    upModelArray.clear();
    Get.loading();

    List fileIds = [];
    await CosManager().initPans();
    String? bucket = await CosManager().bucket();
    Future.wait(cosList.map((fileModel) async {
      if (fileModel is FileModel) {
        var result = await CosUploadHelper.nativeUpload(
            fileModel.savePath, fileModel.fileId, bucket);
        if (result != null) {
          fileIds.add(result);
        }
      }
      return;
    })).then((results) {
      _dealUploadData(fileIds, cosList, model);
      Get.dismiss();
    }).catchError((error) {
      Get.dismiss();
    });
  }

  _dealUploadData(List fileIds, List cosList, FormWidetModel model) {
    if (fileIds.isNotEmpty) {
      //上传成功
      for (FileModel cosFileModel in cosList) {
        bool isHave = false;
        for (String cosFileId in fileIds) {
          if (cosFileId == cosFileModel.fileId) {
            isHave = true;
            print('-----上传成功--${cosFileModel.filePath}');
            cosFileModel.progress = 100;
            if (model.widgetId != null) {
              File saveFile = File(cosFileModel.savePath);
              cosFileModel.progress = 100;

              model.fileList.add(cosFileModel.toJson());
              model.contentList.add(cosFileModel.toUploadJson());
              update();
              saveFile.delete();
            } else {
              //签名上传成功
              postSignatureFile(cosFileModel);
            }
          }
        }
        if (!isHave) {
          //失败的数据
        }
      }
    } else {
      //toast('上传失败');
    }
  }

  void nextFocus() {
    if (focusNodeList.isEmpty) return;
    var size = focusNodeList.length;
    for (int i = 0; i < size; i++) {
      if (focusNodeList[i].hasFocus) {
        if (i + 1 == size) {
          focusNodeList[0].requestFocus();
        } else {
          focusNodeList[i + 1].requestFocus();
        }
      }
    }
  }

  void lastFocus() {
    if (focusNodeList.isEmpty) return;
    var size = focusNodeList.length;
    for (int i = 0; i < size; i++) {
      if (focusNodeList[i].hasFocus) {
        if (i == 0) {
          focusNodeList[size - 1].requestFocus();
        } else {
          focusNodeList[i - 1].requestFocus();
        }
      }
    }
  }

  //校验是否可以重新发起
  isCanResubmit() {
    DioUtil()
        .get('${ApproveApi.APPROVERESUBMIT}/$approveId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        reSendApprove(data['data']);
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //重新发起
  reSendApprove(List reSendList) async {
    Map dataDic = await UserDefault.getData(Define.ORGLIST);
    OrgModel? orgModel;
    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }
    for (var element in companies) {
      if (element['companyId'] == orgId) {
        Map<String, dynamic> orgDic = element;
        orgModel = OrgModel.fromJson(orgDic);
      }
    }
    if (orgModel == null) {
      toast('你已不在此公司');
      return;
    }
    var result = await Get.toNamed(Routes.SEND_APPROVE, arguments: {
      'orgModel': orgModel,
      'orgId': orgId,
      'corgId':corgId,
      'modelId': dataMap['modelId'],
      'approveId': approveId,
      'sendType': 1,
      'contentList': List.from(reSendList)
    });
    if (result != null && result['data'] != null) {
      approveId = result['data'];
      getApproveInfo();
      getCommentData();
    }
  }
}
