import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';

import 'package:get/get.dart';
import 'package:syncfusion_flutter_core/theme.dart';

import '../../../../common/config/config.dart';
import '../controllers/approve_export_controller.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:syncfusion_flutter_pdfviewer/src/theme/theme.dart';

class ApproveExportView extends GetView<ApproveExportController> {
   ApproveExportView({Key? key}) : super(key: key);
  ApproveExportController approveExportController = Get.find();

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      global: false,
      init: approveExportController,
      builder: (logic){
      return Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          appBar: TitleBar().backAppbar(
              context,
              '',
              false,
              approveExportController.url.value.isNotEmpty
                  ? [
                      Container(
                        width: 60,
                        child: CupertinoButton(
                            padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            pressedOpacity: 0.5,
                            child: const Text(
                              '保存',
                              style: TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14),
                            ),
                            onPressed: () {
                              approveExportController.judeIsHaveEmail();
                            }),
                      )
                    ]
                  : [], onPressed: () {
            Get.back();
          }),
          body: SfPdfViewerTheme(
            data:  const SfPdfViewerThemeData(progressBarColor: ColorConfig.backgroundColor,
                      backgroundColor: ColorConfig.backgroundColor,),
            child: approveExportController.hasPdfPath() ? SfPdfViewer.file(File(approveExportController.url.value ??'')):Container()
          ),
        );
    });
  }
}
