import 'dart:io';

import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/http.dart';

class ApproveExportController extends GetxController {

  String approveId = '';
  String approvePdfId = '';
  int currentIndex = 0;
  RxString url = ''.obs;
  String email = '';
  @override
  void onInit() async {
    super.onInit();
    approveId = Get.arguments['approveId'];
    
    dynamic userInfo = await UserDefault.getData(Define.TOKENKEY);
    dynamic userEmail = userInfo['email'];
    if (userEmail == null) {
      email = '';
    } else {
      email = userEmail;
    }
  }

  @override
  void onReady() {
    super.onReady();
    getApprovePDF();
  }

  @override
  void onClose() {
    super.onClose();
  }

  //获取审批pdf
  getApprovePDF() {
    DioUtil().get('${ApproveApi.APPROVEPDFPREVIEW}/$approveId', null, true, () {
    }).then((data) {
      if(data == null) return;
      if (data['code'] == 1) {
        dynamic dataMap = data['data'];
        String downLoadUrl = dataMap['url'];
        approvePdfId = dataMap['approvePdfId'];
        getDownLoadPath(downLoadUrl, 'pdf');
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //判断是否存在邮箱
  judeIsHaveEmail() {
    if (email.isEmpty) {
      toast('还未设置邮箱，请到app个人资料中设置');
      return;
    } else {
       sureExportToEmail();
    }
  }

  //获取审批pdf
  sureExportToEmail() {
    DioUtil().get('${ApproveApi.APPROVEEXPORT}/$approvePdfId', null, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('已成功导入到你的邮箱');
        Get.back();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getDownLoadPath(String downLoadUrl, String ext) async {
    List fileIdList = downLoadUrl.split('?');
    String startStr = fileIdList.first;
    List startList = startStr.split('/');
    String endPath = '${startList.last}.$ext';
    String docment = (await getApplicationDocumentsDirectory()).path;
    String savePath = '$docment/${Define.APPROVEEXPORTPATH}/$endPath';
    String loadPath = '$docment/${Define.APPROVELOADTPATH}/$endPath';
    File saveFile = File(savePath);
    if (saveFile.existsSync()) {
      url.value = savePath;
      update();
    } else {
      Directory directory = Directory('$docment/${Define.APPROVEEXPORTPATH}');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      Directory loadDirectory = Directory('$docment/${Define.APPROVELOADTPATH}');
      if (!await loadDirectory.exists()) {
        await loadDirectory.create(recursive: true);
      }else{
        File loadFile = File(loadPath);
        if (loadFile.existsSync()) {
          await loadFile.delete();
        }
      }
      downLoadFiel(downLoadUrl, savePath,loadPath);
    }
  }

  //下载附件并查看
  downLoadFiel(String downLoadUrl, String savePath, String loadPath) async{
    DioUtil().downLoadFile(downLoadUrl, savePath,loadPath ,() {
      toast('获取文件失败');
    }, (finishPath) {
      print('finishPath ===== $finishPath');
      url.value = finishPath;
      update();
    });
  }

  bool hasPdfPath() {
    return !StringUtil.isEmpty(url.value);
  }

}
