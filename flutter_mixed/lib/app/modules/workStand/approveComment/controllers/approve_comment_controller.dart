import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/cos/pan_datasource.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../../../common/api/LoginApi.dart';
import '../../../../common/base_info/info.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../permission/permission_util.dart';
import '../../../../utils/http.dart';
import '../../models/filemodel.dart';

class ApproveCommentController extends GetxController {
  TextEditingController msgController = TextEditingController();
  String approveId = '';
  String orgId = '';

  List upModelArray = [];
  Completer? completer;
  String uploadBucket = '';
  RxList finishedArray = [].obs;
  @override
  void onInit() async {
    super.onInit();
    approveId = Get.arguments['approveId'];
    orgId = Get.arguments['orgId'];
    // Map cosInfo = await UserDefault.getData(Define.COSTOKENKEY);
    // uploadBucket = cosInfo['bucket'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    msgController.dispose();
  }

  //添加评论
  addComment() {
    List fileList = [];
    for (var i = 0; i < finishedArray.length; i++) {
      FileModel fileModel = finishedArray[i];
      Map fileMap = fileModel.toJson();
      fileMap.remove('filePath');
      fileMap.remove('file');
      fileList.add(fileMap);
    }
    Map param = {
      'approveId': approveId,
      'orgId': orgId,
      'content': msgController.text,
      'fileList': fileList
    };

    if (msgController.text.isEmpty && fileList.isEmpty) {
      toast('请输入评论内容或上传图片');
      return;
    }

    DioUtil().post(ApproveApi.APPROVECOMMENT, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Get.back(result: {'add': 1});
      } else {
        toast('${data['msg']}');
      }
    });
  }

  pickPhoto() async {
    var r = await PermissionUtil.checkGalleryPermission(Get.context!);
    if (!r) return;

    upModelArray.clear();
    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: const AssetPickerConfig(
            maxAssets: 9, requestType: RequestType.image));
    if (assets == null) return;
    for (var i = 0; i < assets.length; i++) {
      AssetEntity entity = assets[i];
      File? file = await entity.file;

      FileModel fileModel = FileModel();
      fileModel.hash = '';
      fileModel.fileId = '';
      List fileNameList = file!.path.split('/');
      fileModel.fileName = fileNameList.last;
      fileModel.fileType = 0;
      fileModel.filePath = file.path;
      fileModel.file = file;

      await startDealFile(fileModel);
    }
    getFileIds();
  }

  Future startDealFile(FileModel fileModel) async {
    completer = Completer<String>();
    await saveFile(fileModel).then((value) {
      fileModel.savePath = value;
      fileModel.hash = BaseInfo().generateMD5(value);
      upModelArray.add(fileModel);
      if (completer!.isCompleted == false) {
        completer!.complete('');
      }
    });
    return completer!.future;
  }

  //保存图片
  Future<String> saveFile(FileModel fileModel) async {
    File file = fileModel.file!;
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;
    String filePath = '$docment/approve/images/$fileName';
    Directory directory = Directory('$docment/approve/images');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  //获取fileId
  getFileIds() async{
    try {
      var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await panDatasource.getPanFields(upModelArray.length);
      if (resp.success()) {
        List? idList = resp.data;
        if (idList == null) return;
        for (var i = 0; i < idList.length; i++) {
          FileModel fileModel = upModelArray[i];
          fileModel.fileId = idList[i];
        }
        dealUploadList();
      }else{
        upModelArray.clear;
        toast(resp.msg);
      }     
    } catch (e) {
      upModelArray.clear;
      toast(LoginApi.ERROR_MSG);
    }
  }

  //处理上传数组
  dealUploadList() {
    List cosList = [];
    for (var i = 0; i < upModelArray.length; i++) {
      FileModel fileModel = upModelArray[i];
      if (fileModel.fileId != null) {
        cosList.add(fileModel);
      }
    }
    startUploadFileList(cosList);
  }

  startUploadFileList(List cosList) async {
    if (cosList.isEmpty) {
      return;
    }
    Get.loading();
    List fileIds = [];
    await CosManager().initPans();
    String? bucket = await CosManager().bucket();
    Future.wait(cosList.map((fileModel) async {
      if (fileModel is FileModel) {
        var result = await CosUploadHelper.nativeUpload(
            fileModel.savePath, fileModel.fileId, bucket);
        if (result != null) {
          fileIds.add(result);
        }
      }
      return ;
    })).then((results) {
      _dealUploadData(fileIds, cosList);
      Get.dismiss();
    }).catchError((error) {
      Get.dismiss();
    });
  }

  _dealUploadData(List fileIds, List cosList) {
    if (fileIds.isNotEmpty) {
      //上传成功
      for (FileModel cosFileModel in cosList) {
        bool isHave = false;
        for (String cosFileId in fileIds) {
          if (cosFileId == cosFileModel.fileId) {
            isHave = true;
            logger('-----上传成功--${cosFileModel.filePath}');
            cosFileModel.progress = 100;
            finishedArray.add(cosFileModel);
            upModelArray.remove(cosFileModel);
            update();
          }
        }
        if (!isHave) {
          //失败的数据
          upModelArray.remove(cosFileModel);
        }
      }
    } else {
      //toast('上传失败');
    }
  }
}
