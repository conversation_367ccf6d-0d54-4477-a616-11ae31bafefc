import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/workStand/models/filemodel.dart';

import 'package:get/get.dart';

import '../../../../common/config/config.dart';
import '../controllers/approve_comment_controller.dart';

class ApproveCommentView extends GetView<ApproveCommentController> {
   ApproveCommentView({Key? key}) : super(key: key);
   ApproveCommentController approveCommentController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      global: false,
      init: approveCommentController,
      builder: (logic){
      return Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(context, '评论', false, [
            Container(
              width: 60,
              child: CupertinoButton(
                  padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                  pressedOpacity: 0.5,
                  child: const Text(
                    '发表',
                    style: TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  ),
                  onPressed: () {
                    approveCommentController.addComment();
                  }),
            )
          ], onPressed: () {
            Get.back();
          }),
          body: ListView(
            children: [
              Container(
                color: ColorConfig.whiteColor,
                width: DeviceUtils().width.value,
                height: 180,
                padding: EdgeInsets.all(16),
                child: Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border:
                          Border.all(width: 1, color: ColorConfig.lineColor)),
                  child: TextField(
                    onSubmitted: (value) {},
                    onChanged: (value) {},
                    controller: approveCommentController.msgController,
                    maxLines: null,
                    minLines: 1,

                    inputFormatters: [LengthLimitingTextInputFormatter(200)],
                    textInputAction: TextInputAction.done,
                    style: const TextStyle(
                      color: ColorConfig.mainTextColor,
                      fontSize: 14,
                    ),

                    decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                        border: OutlineInputBorder(borderSide: BorderSide.none),
                        hintText: '请输入评论内容,最多200字',
                        hintStyle: TextStyle(
                          color: ColorConfig.desTextColor,
                          fontSize: 14,
                        )),
                  ),
                ),
              ),
              SizedBox(
                height: 10,
              ),
              backWidgetForImage()
            ],
          ),
        );
    });
  }

  backWidgetForImage() {
    return Container(
      width: double.infinity,
      color: ColorConfig.whiteColor,
      padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: 48,
            alignment: Alignment.centerLeft,
            child: Text(
              '添加图片',
              style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
          Column(
            children: backImageData(),
          )
        ],
      ),
    );
  }

  backImageData() {
    List<Widget> lists = [];
    if (approveCommentController.finishedArray.isNotEmpty) {
      for (var i = 0; i < approveCommentController.finishedArray.length; i++) {
        FileModel fileModel = approveCommentController.finishedArray[i];
        String fileName = fileModel.filePath.split('/').last;

        lists.add(Column(
          children: [
            Container(
              width: double.infinity,
              height: 46,
              padding: EdgeInsets.fromLTRB(12, 7, 12, 7),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: ColorConfig.backgroundColor),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    child: ExtendedImage.file(fileModel.file!),
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  Expanded(
                      child: Container(
                    child: Text(
                      fileName,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 14, color: ColorConfig.mainTextColor),
                    ),
                  )),
                  InkWell(
                    onTap: () {
                 
                      approveCommentController.finishedArray.removeAt(i);
                      approveCommentController.update();
                    },
                    child: Container(
                    width: 20,
                    height: 20,
                    child: Image.asset('assets/images/3.0x/approve_delete.png'),
                  ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 8,
            )
          ],
        ));
      }
    }

    Widget upWidget = InkWell(
      onTap: () {
        approveCommentController.pickPhoto();
      },
      child: Container(
        width: double.infinity,
        height: 46,
        padding: EdgeInsets.only(bottom: 16),
        child: Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 18,
                height: 18,
                child: Image.asset('assets/images/3.0x/approve_upLoad.png'),
              ),
              Container(
                child: Text(
                  ' 上传图片',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.themeCorlor),
                ),
              )
            ],
          ),
        ),
      ),
    );
    lists.add(upWidget);
    return lists;
  }
}
