import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/manage/company_model.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';
import '../../../contact/model/org/org_model.dart';

class WorkFlowController extends GetxController {
  OrgModel? currentModel;
  var empty = ''.obs;
  String? currentOrgId;
  RxList dataList = [].obs;
  RxList managerList = [].obs;
  RxList tooList = [].obs;
  String userId = '';
  RxList orgList = [].obs;

  RxInt status = 0.obs;

  // RxList bannerList = [{"picture": "https://img1.baidu.com/it/u=3539595421,*********&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500"},{"picture": "https://img1.baidu.com/it/u=3539595421,*********&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500"}].obs;
  RxList bannerList = [].obs;
  RxString content = ''.obs; //公告显示内容
  RxBool isHaveAuth = false.obs; //是否有组织权限

  RxList unreadList = [].obs; //全部未读
  CancelToken? cacheToken;
  CancelToken? dataToken;
  CancelToken? unreadToken;
  RxInt currentHaveApprove = 0.obs;

  bool isShowUpdateDialog = false;
  List unTreatedList = [];

  CancelToken? companyUnreadCancelToken;
  
  List topKeys = [];//常用应用数组 key
  RxList topList = [].obs;//处理后的常用数组
  List allList = [];//所有模块数组
  CancelToken? topFunctionCancelToken;
  TabController? tabController;
  RxInt currentIndex = 1.obs;

  List recentlyKeys = [];
  List recentlyList = [];

  @override
  void onInit() async {
    super.onInit();
    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    userId = userInfo['userId'] ?? '';
    currentOrgId = await UserDefault.getData('${Define.CURRENTORGID}-$userId');
    dynamic banners = await UserDefault.getData(Define.BANNERINFO);
    if (banners != null) {
      bannerList.value = banners;
    }

  }

  @override
  void onReady() {
    super.onReady();
    getAllCompany();
    getAllCompanyUntreated();
  }

  @override
  void onClose() {

    super.onClose();
  }

  //处理原生传递的数据
  reciveNativeData(Map dataMap) async {
    //处理banner
    var banners = dataMap['bannerList'];
    if (banners != null) {
      bannerList.value = banners;
      await UserDefault.setData(Define.BANNERINFO, banners);
    }
    bannerList.refresh();
  }

  //获取公司数据
  getAllCompany({VoidCallback? callback}) async {
    dynamic dataDic = await UserDefault.getData(Define.ORGLIST);
    if (dataDic == null) {
      return;
    }

    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }

    List allOrModelList = [];

    if (companies.isNotEmpty) {
      allOrModelList =
          companies.map((item) => OrgModel.fromJson(item)).toList();
      orgList.value = allOrModelList;
      if (currentOrgId == null) {
        currentModel = allOrModelList.first;
      } else {
        bool isHave = false;
        for (var i = 0; i < allOrModelList.length; i++) {
          OrgModel model = allOrModelList[i];
          if (model.companyId == currentOrgId) {
            isHave = true;
            currentModel = model;
          }
        }

        if (isHave == false) {
          currentModel = allOrModelList.first;
        }
      }
    } else {
      currentModel = null;
    }
    if (currentModel == null) {
      status.value = 0;
    } else {
      status.value = 1;
      dealData();
    }

    if (currentModel != null) {
      Channel().invoke(Channel_Native_RefreshCurrentOrg,
          {'companyId': currentModel?.companyId});
    }

    CompanyModelHelper.orgModel = currentModel;

    status.refresh();
  }

  dealData() {
    unreadList.clear;
    topKeys.clear();
    topList.clear;
    allList.clear;
    recentlyList.clear();
    recentlyKeys.clear();
    checkCurrentAuth();
    dealApproveUnreadCount();
    //dealOrgPower();
    getFunctionCacheDate();
    getTopFunctionData();
  }

  checkCurrentAuth() {
    isHaveAuth.value = false;
    if (currentModel!.deptId == '0' || currentModel!.power.contains('-1')) {
      isHaveAuth.value = true;
    } else {
      if (currentModel!.power.contains('1')) {
        isHaveAuth.value = true;
      }
    }
  }

  dealApproveUnreadCount() async {
    dynamic unreadCountMap =
        await UserDefault.getData(Define.APPROVE_TOTAL_LUNREADKEY);

    if (unreadCountMap != null && unreadCountMap is Map) {
      if (unreadCountMap['total'] != null) {
        currentHaveApprove.value = unreadCountMap['total'];
      }
    }

    status.refresh();
  }

  dealOrgPower() {
    List twoList = [];

    for (var i = 0; i < managerList.length; i++) {
      Map toolMap = managerList[i];
      backRouteNameWithMap(toolMap);
    }

    dataList.clear();
    if (managerList.isNotEmpty) {
      dataList.add({'title': '管理企业', 'data': managerList});
    }

    if (EnvConfig.mEnv != Env.Product) {
      Map<String, dynamic> argument = {'companyId': currentModel!.companyId};
      twoList.add({
        'name': 'web端测试',
        'icon': 'assets/images/3.0x/workflow_home_approve.png',
        'routeName': Routes.WEB_TEST,
        'argument': argument
      });
    }

    for (var i = 0; i < tooList.length; i++) {
      Map toolMap = tooList[i];
      backRouteNameWithMap(toolMap);
    }

    twoList.addAll(tooList);

    dataList.add({'title': '全部应用', 'data': twoList});

    _dealTopFunctionList();
    _dealRecentList();
    
  }

  backRouteNameWithMap(Map toolMap) {
    String url = toolMap['webUrl'];
    if (url.startsWith('http')) {
      toolMap['routeName'] = Channel_openWebView; //跳转webView的方法名称
    } else if (url.startsWith('flutter://')) {
      //跳转flutter
      if (url.contains('routeName=')) {
        List urlList = url.split('routeName=');
        String routeName = urlList.last;
        toolMap['routeName'] = routeName;
      }
    } else if (url.startsWith('native://')) {
      if (url.contains('method=')) {
        List urlList = url.split('method=');
        String methodeName = urlList.last;
        toolMap['routeName'] = methodeName;
      }
    }
  }

  //获取模块缓存接口
  getFunctionCacheDate() {
    if (cacheToken != null) {
      cacheToken!.cancel();
    }
    cacheToken = CancelToken();
    DioUtil()
        .get('${ORGApi.GET_FUNC_CACHE}/${currentModel!.companyId}', null, true,
            () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: cacheToken)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        if (userId.isEmpty) {
          Map userInfo = await UserDefault.getData(Define.TOKENKEY);
          if (userInfo != null) {
            userId = userInfo['userId'] ?? '';
          }
        }
        int cacheTemp = data['data'];
        Map? functMap = await UserDefault.getData(
            'functionDataV4-$userId-${currentModel!.companyId}');
        if (functMap == null) {
          getFunctionData(cacheTemp);
        } else {
          int temp = functMap['temp'];
          if (temp == cacheTemp) {
            managerList.value = functMap['managementOrgtList'];
            tooList.value = functMap['toolsList'];
            dealOrgPower();
            //未读数数据获取
            getFunctionUnReadCount();
          } else {
            getFunctionData(cacheTemp);
          }
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取模块数据
  getFunctionData(int temp) {
    if (dataToken != null) {
      dataToken!.cancel();
    }
    dataToken = CancelToken();
    DioUtil()
        .get('${ORGApi.GET_WORK_TOOL}/${currentModel!.companyId}', null, true,
            () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: dataToken)
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataMap = data['data'];
        dataMap['temp'] = temp;
        managerList.value = dataMap['managementOrgtList'];
        tooList.value = dataMap['toolsList'];
        dealOrgPower();
        getFunctionUnReadCount();
        await UserDefault.setData(
            'functionDataV4-$userId-${currentModel!.companyId}', dataMap);
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取模块未读数
  getFunctionUnReadCount() {
    if (currentModel == null) return;
    if (unreadToken != null) {
      unreadToken!.cancel();
    }
    unreadToken = CancelToken();
    DioUtil()
        .get('${ORGApi.GET_FUNC_UNREADCOUNT}/${currentModel!.companyId}', null,
            true, () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: unreadToken)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        dynamic dataMap = data['data'];
        if (dataMap == null) return;
        String requestOrgId = dataMap['orgId']??'';
        if (requestOrgId == currentModel!.companyId) {
          if (dataMap['untreatedVOList'] != null) {
            unreadList.value = dataMap['untreatedVOList'];
          } else {
            unreadList.value = [];
          }
        }
        // String requestOrgId = '';
        // for (var element in dataMap.keys) {
        //   if (element != 'toolsList') {
        //     requestOrgId = element;
        //   }
        // }
        // if (requestOrgId == currentModel!.companyId) {
        //   if (dataMap['toolsList'] != null) {
        //     unreadList.value = dataMap['toolsList'];
        //   }else{
        //     unreadList.value = [];
        //   }
        // }

        status.refresh();
      } else {
        // toast('${data['msg']}');
      }
    });
  }

  //模块入口跳转逻辑
  workToolJump(Map toolMap) async {
    int kingdeeTokenType = toolMap['kingdeeTokenType'] ?? 0;
    int backspaceKey =
        toolMap['backspaceKey'] ?? 0; //1原生navigation,0web自己的navigation
    String url = toolMap['webUrl'];
    if (kingdeeTokenType == 2) {
      //拼接金蝶token
      var result =
          await Channel().invokeMap(Channel_Native_KingDeeToken);
      if (result!['token'] == '') {
        toast('获取信息失败');
        return;
      } else {
        String kingDeeToken = result['token'];
        url = '$url$kingDeeToken';
      }
    } else if (kingdeeTokenType == 3) {
      //拼接手机号
      Map userInfo = await UserDefault.getData(Define.TOKENKEY);
      String mobile = userInfo['mobile'];
      url = '$url$mobile';
    } else {
      //不拼接参数
    }

    //获取platform
    int platform = 1;
    if (Platform.isIOS) {
      platform = 2;
    }
    //获取appVersion
    String appVersion = await BaseInfo().getAppVersion();

    if (url.contains('?')) {
      url = '$url&platform=$platform&appVersion=$appVersion';
    } else {
      url = '$url?platform=$platform&appVersion=$appVersion';
    }

    openWebView({
      'url': url,
      'title': toolMap['name'],
      'isWebNavigation': backspaceKey == 1 ? 0 : 1,
      'orgId': currentModel!.companyId
    });

  }

  //获取所有公司未读
  getAllCompanyUntreated() {
    if (companyUnreadCancelToken != null) {
      companyUnreadCancelToken!.cancel();
    }
    companyUnreadCancelToken = CancelToken();
    DioUtil()
        .get(ORGApi.GET_ALL_COMPANY_UNTREATED, null, true, () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: companyUnreadCancelToken)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        unTreatedList = data['data'];
      }
    });
  }

  //获取常用应用
  getTopFunctionData() async{
    if (currentModel == null) return;
    topKeys.clear();
    try {
      
      if (topFunctionCancelToken != null) {
        topFunctionCancelToken?.cancel();
      }
      topFunctionCancelToken = CancelToken();
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource.getTopFunctionData(currentModel!.companyId,topFunctionCancelToken);
      logger('=====resp===${resp.data}');
      if (resp.success()) {
        if (resp.data is String){
          topKeys = jsonDecode(resp.data);
          _dealTopFunctionList();
        }
      }
    } catch (e) {

    }
  }

  //处理最近使用
  _dealRecentList() async{
    recentlyList.clear();
    recentlyKeys.clear();
    dynamic cacheRecentlyList = await UserDefault.getData(
        'recently-$userId-${currentModel!.companyId}');
    if (cacheRecentlyList != null) {
      //获取最近使用
      for (var i = 0; i < cacheRecentlyList.length; i++) {
        String key = cacheRecentlyList[i];
        for (var j = 0; j < allList.length; j++) {
          Map item = allList[j];
          if (item['key'] == key) {
            recentlyList.add(item);
            recentlyKeys.add(key);
            break;
          }
        }
      }
    }
    if (recentlyList.isNotEmpty) {
      currentIndex.value = 0;
      tabController?.animateTo(0);
    } else {
      currentIndex.value = 1;
      tabController?.animateTo(1);
    }
    dataList.refresh();
  }
  //处理常用列表
  _dealTopFunctionList(){
    topList.clear();
    allList.clear();

    //获取所有模块
    for (var j = 0; j < dataList.length; j++) {
      Map dataMap = dataList[j];
      List itemList = dataMap['data'];
      for (var k = 0; k < itemList.length; k++) {
        Map item = itemList[k];
        allList.add(item);
      }
    }

    //获取常用
    for (var i = 0; i < topKeys.length; i++) {
      String key = topKeys[i];
      for (var j = 0; j < allList.length; j++) {
        Map item = allList[j];
        if (item['key'] == key) {
          topList.add(item);
        }
      }
    }
    dataList.refresh();
  }

  receiveRefreshNotice(companyId) {
    if (currentModel == null) return;
    if (companyId == currentModel!.companyId) {
      getFunctionUnReadCount();
    }
    getAllCompanyUntreated();
  }

  // forceRefresh
  forceRefresh() {
    getFunctionUnReadCount();
    getAllCompanyUntreated();
  }
}
