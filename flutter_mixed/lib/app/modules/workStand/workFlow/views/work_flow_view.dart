import 'dart:io';

import 'package:card_swiper/card_swiper.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/common/expanded_viewport.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/im/widget/choose_org_component.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/statis/statistics_helper.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';
import 'package:kumi_popup_window/kumi_popup_window.dart';

import '../../../../../plugin/focus_detector/focus_detector.dart';
import '../../../../../res/assets_res.dart';
import '../../../../common/config/config.dart';
import '../controllers/work_flow_controller.dart';
import 'package:badges/badges.dart' as badge;

class WorkFlowView extends GetView<WorkFlowController> {
  WorkFlowView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    WorkFlowController controller = Get.put(WorkFlowController());
    return FocusDetector(
        onFocusGained: () {
          controller.dealApproveUnreadCount();
          // controller.getFunctionUnReadCount();
          // controller.getAllCompanyUntreated();
        },
        child: Obx(() => Scaffold(
              backgroundColor: ColorConfig.backgroundColor,
              body: workBody(context),
            )));
  }

  Widget workBody(BuildContext context) {
    if (controller.status.value == 0) return _emptyPage();
    return _worktTab(context, ScrollPhysics());
    return EasyRefresh.builder(onRefresh: () async {
      controller.getAllCompany();
    }, childBuilder: (ctx, physics) {
      return _worktTab(context, physics);
    });
  }

  Widget _emptyPage() {
    return Container(
      alignment: Alignment.center,
      child: Stack(
        children: [
          Image.asset(AssetsRes.IC_EMPTY_COMPANY, width: 783, height: 575),
          Container(
            margin: const EdgeInsets.only(top: 50),
            alignment: Alignment.center,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                30.gap,
                Text((controller.empty ?? '').toString()),
                const Text(
                  '你还没有加入工作企业',
                  style: TextStyle(fontSize: 17, color: Colors.black),
                ),
                15.gap,
                Container(
                  alignment: Alignment.center,
                  child: const Text(
                    '马上尝试创建或加入一个企业吧\n加入企业后，你即可开始使用考勤、公告等功能',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ),
                20.gap,
                CupertinoButton(
                    padding: const EdgeInsets.only(
                      top: 5,
                      bottom: 5,
                    ),
                    color: Colors.blue,
                    pressedOpacity: 0.5,
                    child: Container(
                      height: 38,
                      width: 160,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.blue),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(8)),
                      ),
                      child: const Text(
                        '创建企业',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    onPressed: () async {
                      Get.toNamed(Routes.ORG_CREATE);
                      // createTeamCallBack?.call();
                    }),
                20.gap,
                CupertinoButton(
                    padding: const EdgeInsets.only(top: 5, bottom: 5),
                    color: Colors.white,
                    pressedOpacity: 0.5,
                    child: Container(
                      height: 44,
                      width: 160,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.blue),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(8)),
                      ),
                      child: const Text(
                        '加入企业',
                        style: TextStyle(color: Colors.blue),
                      ),
                    ),
                    onPressed: () async {
                      RouteHelper.route(Routes.SEARCH_ORG, arguments: {'type': 0});
                      // joinTeamCallBack?.call();
                    }),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _worktTab(BuildContext context, ScrollPhysics physics) {
    return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            44.gap,
            // 当前企业按钮（可下拉）
            InkWell(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () {
                popComponent(context);
              },
              child: Container(
                width: double.infinity,
                height: 56,
                padding: const EdgeInsets.only(left: 15, right: 15),
                child: Row(
                  children: [
                    ImageLoader(
                      url: controller.currentModel?.logo,
                      width: 40,
                      height: 40,
                      isCircle: true,
                      border: 1,
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Container(
                        constraints: BoxConstraints(
                            maxWidth:
                                DeviceUtils().width.value - 30 - 40 - 8 - 20),
                        child: Text(
                          controller.currentModel!.name,
                          style: const TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                        )),
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: Image.asset(
                          'assets/images/3.0x/workflow_home_down.png'),
                    )
                  ],
                ),
              ),
            ),
            // banner
            Expanded(
                child: ListView(
                  children: [
                    Container(
              decoration: const BoxDecoration(
                  color: ColorConfig.whiteColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  )),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  16.gap,
                  Offstage(
                    offstage: controller.bannerList.isEmpty,
                    // offstage: true,
                    child: Container(
                      width: double.infinity,
                      height: (DeviceUtils().width.value - 32) * 148 / 343,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Stack(
                        children: [
                          Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              height: 16,
                              child: Container(
                                width: double.infinity,
                                height: 16,
                                decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Color(0x33C4D6FB),
                                      Color(0x33DEECFD),
                                    ],
                                  ),
                                ),
                              )),
                          Swiper(
                            // pagination: SwiperPagination(),
                            // control: SwiperControl(),
                            loop: controller.bannerList.length > 1,
                            itemCount: controller.bannerList.length,
                            itemBuilder: (context, index) {
                              Map bannerMap = controller.bannerList[index];

                              return ImageLoader(
                                url: bannerMap['picture'],
                                boxFit: BoxFit.cover,
                              );
                            },
                            onTap: (index) {
                              Map bannerMap = controller.bannerList[index];
                              String url = bannerMap['url'];
                              if (url.isNotEmpty) {
                                openWebView({
                                  'url': url,
                                  'isWebNavigation': 0,
                                  'title': bannerMap['name']
                                });
                              }
                            },
                          )
                        ],
                      ),
                    ),
                  ),

                  // 公告栏
                  Offstage(
                    offstage: controller.isHaveAuth.value == false &&
                        controller.currentModel!.content.isEmpty,
                    // offstage: false,
                    child: Container(
                      width: double.infinity,
                      height: 36,
                      margin: const EdgeInsets.only(left: 16, right: 16),
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Color(0x33E7F4FE),
                            Color(0x33C2BFFB),
                          ],
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          8.gap,
                          SizedBox(
                            width: 14,
                            height: 14,
                            child: Image.asset(
                                'assets/images/3.0x/workflow_home_announce.png'),
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Expanded(
                              child: GestureDetector(
                            onTap: () {
                              if (controller.currentModel!.content.isNotEmpty &&
                                  controller.currentModel!.noticeId != '0') {
                                Channel().invoke(Channel_Native_announce, {
                                  'type': 2,
                                  'isHaveAuth':
                                      controller.isHaveAuth.value ? 1 : 0,
                                  'orgId': controller.currentModel!.companyId,
                                  'noticeId': controller.currentModel!.noticeId
                                });
                              }
                            },
                            child: Container(
                              child: Text(
                                controller.currentModel!.content.isEmpty
                                    ? '暂无公告'
                                    : controller.currentModel!.content,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                    fontSize: 13,
                                    color: ColorConfig.mainTextColor),
                              ),
                            ),
                          )),
                          GestureDetector(
                            onTap: () {
                              if (controller.currentModel!.content.isEmpty &&
                                  controller.isHaveAuth.value) {
                                Channel().invoke(Channel_Native_announce, {
                                  'type': 0,
                                  'isHaveAuth':
                                      controller.isHaveAuth.value ? 1 : 0,
                                  'orgId': controller.currentModel!.companyId
                                });
                              } else {
                                Channel().invoke(Channel_Native_announce, {
                                  'type': 1,
                                  'isHaveAuth':
                                      controller.isHaveAuth.value ? 1 : 0,
                                  'orgId': controller.currentModel!.companyId
                                });
                              }
                            },
                            child: Container(
                              width: 60,
                              alignment: Alignment.centerRight,
                              child: Text(
                                controller.currentModel!.content.isEmpty &&
                                        controller.isHaveAuth.value
                                    ? '立即创建'
                                    : '更多',
                                style: const TextStyle(
                                    fontSize: 13,
                                    color: ColorConfig.themeCorlor),
                              ),
                            ),
                          ),
                          8.gap
                        ],
                      ),
                    ),
                  ),
                   _buildTopFuntion(),
                 Expanded(child: DefaultTabController(
                        initialIndex: controller.currentIndex.value,
                        length: 3,
                        child: Builder(builder: (context) {
                          controller.tabController =
                              DefaultTabController.of(context);

                          controller.tabController!.addListener(() {
                            controller.currentIndex.value =
                                controller.tabController!.index;
                            controller.dataList.refresh();
                          });
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                  color: ColorConfig.whiteColor,
                                  height: 48,
                                  width: double.infinity,
                                  padding: const EdgeInsets.only(
                                      left: 16, right: 16),
                                  child: TabBar(
                                    onTap: (value) {},
                                    indicatorColor: ColorConfig.themeCorlor,
                                    indicatorWeight: 1,
                                    labelPadding: const EdgeInsets.all(0),
                                    indicatorPadding: const EdgeInsets.symmetric(horizontal: 30),
                                    tabAlignment: TabAlignment.start,
                                    dividerHeight: 0,
                                    isScrollable: true,
                                    tabs: [
                                      Container(
                                        width: 80,
                                        alignment: Alignment.center,
                                        child: Text(
                                          '最近使用',
                                          style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: controller
                                                          .currentIndex.value ==
                                                      0
                                                  ? FontWeight.w500
                                                  : FontWeight.w400,
                                              color: controller
                                                          .currentIndex.value ==
                                                      0
                                                  ? ColorConfig.themeCorlor
                                                  : ColorConfig.mainTextColor),
                                        ),
                                      ),
                                      Container(
                                        width: 80,
                                        alignment: Alignment.center,
                                        child: Text(
                                          '全部应用',
                                          style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: controller
                                                          .currentIndex.value ==
                                                      1
                                                  ? FontWeight.w500
                                                  : FontWeight.w400,
                                              color: controller
                                                          .currentIndex.value ==
                                                      1
                                                  ? ColorConfig.themeCorlor
                                                  : ColorConfig.mainTextColor),
                                        ),
                                      ),
                                      Container(
                                        width: 80,
                                        alignment: Alignment.center,
                                        child: Text(
                                          '管理员',
                                          style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: controller
                                                          .currentIndex.value ==
                                                      2
                                                  ? FontWeight.w500
                                                  : FontWeight.w400,
                                              color: controller
                                                          .currentIndex.value ==
                                                      2
                                                  ? ColorConfig.themeCorlor
                                                  : ColorConfig.mainTextColor),
                                        ),
                                      )
                                    ],
                                  )),
                              const Divider(color: ColorConfig.backgroundColor,height: 1,),
                              Container(
                                      width: double.infinity,
                                      child: TabBarView(
                                          children: [Container(height: 2,),Container(height: 2,),Container(height: 2,)])),
                              
                            ],
                          );
                        })))
                ],
              ),
            )
                  ],
                ))
          ],
        );
  }

  _buildTabbarView(){
    List recentlyList = [];
    List allList = [];
    List managerList = [];
    if (controller.dataList.length == 2) {
      allList = controller.dataList.last['data'];
      managerList = controller.dataList.first['data'];
    }else if(controller.dataList.length == 1){
      allList = controller.dataList.last['data'];
    }
    List tabbarList = [recentlyList,allList,managerList];
    List<Widget> lists = [];
    for (var i = 0; i < tabbarList.length; i++) {
      List item = tabbarList[i];
      lists.add(Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          16.gap,
          GridView.count(
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          padding: EdgeInsets.zero,
          childAspectRatio: 1,
          shrinkWrap: true,
          children: backItemWidget(item),
        )
        ],
      ));
    }
    return lists;
  }

  //常用模块
  _buildTopFuntion() {
    if (controller.topList.isEmpty) {
      return _buildAddTopFunctionWidget();
    } else {
      return Container(
        padding: const EdgeInsets.only(top: 16),
        child: GridView.count(
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          padding: EdgeInsets.zero,
          childAspectRatio: 1,
          shrinkWrap: true,
          children: backItemWidget(controller.topList, isAddFunction: true),
        ),
      );
    }
  }

  _buildAddTopFunctionWidget() {
    return InkWell(
      onTap: () {
        RouteHelper.route(Routes.ADD_TOP_FUNCTION,arguments: {
          'topList': controller.topList,
          'allList': controller.allList,
          'orgId': controller.currentModel?.companyId
        });
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16,vertical: 12),
        width: double.infinity,
        height: 38,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: ColorConfig.backgroundColor),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: Image.asset(AssetsRes.WORKFLOW_ADD_BLUE),
            ),
            4.gap,
            Container(
              child: const Text(
                '添加常用应用',
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: ColorConfig.themeCorlor),
              ),
            )
          ],
        ),
      ),
    );
  }

  backItemWidget(List itemList,{bool isAddFunction = false}) {
    //isAddFunction 是否是添加常用应用
    List tempList = List.from(itemList);
    List<Widget> lists = [];
    if (isAddFunction) {
      tempList.add({
        'name': '添加常用',
        'icon': AssetsRes.WORKFLOW_ADD_FUNCTION,
        'routeName': Routes.ADD_TOP_FUNCTION,
        'argument': {
          'topList': controller.topList,
          'allList': controller.allList,
          'orgId': controller.currentModel?.companyId
        }
      });
    }
    for (var i = 0; i < tempList.length; i++) {
      dynamic dataMap = tempList[i];
      dynamic url = dataMap['icon'] ?? '';
      dynamic routeName = dataMap['routeName'];
      dynamic argument = dataMap['argument'];
      int unreadCount = 0;
      if (dataMap['key'] != null) {
        for (var j = 0; j < controller.unreadList.length; j++) {
          Map unreadMap = controller.unreadList[j];
          if (dataMap['key'] == unreadMap['serviceName']) {
            dynamic unRead = unreadMap['count'];
            if (unRead is int) {
              unreadCount = unRead;
            }
            break;
          }
        }
      }
      if (dataMap['key'] == 'approve') {
        unreadCount = controller.currentHaveApprove.value;
      }
      lists.add(InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          if (routeName == null) return;
          // RouteUtil.routePath(Routes.GROUP_MEMBER_PAGE, arguments: {'groupId': '2621224384051810301'});
          // return;
          logger("routeName:>>>> ${routeName}");

          if (routeName.startsWith('/')) {
            //跳转flutter页面

            argument['isFromWork'] = 1;

            Get.toNamed(routeName,
                arguments: argument, preventDuplicates: false);
          } else {
            //routeName为跳转原生的方法名称,arguments为参数
            print("routeName: ${routeName}");
            if (routeName == Channel_openWebView) {
              controller.workToolJump(dataMap);
            } else {
              Channel().invoke(routeName, argument);
            }
          }
        },
        child: Container(
          child: Column(
            children: [
              4.gap,
              Container(
                width: 48,
                height: 48,
                child:  badge.Badge(
                  badgeStyle: badge.BadgeStyle(
                    shape: badge.BadgeShape.circle,
                    badgeColor: ColorConfig.deleteCorlor,
                    borderRadius: BorderRadius.circular(12),
                    elevation: 0,
                  ),
                  position: badge.BadgePosition.topEnd(top: -4, end: -6),
                  showBadge: unreadCount > 0,
                  child: ImageLoader(
                    url: url,
                    radius: 6,
                    width: 48,
                    height: 48,),
                ),
              ),
              8.gap,
              Container(
                constraints:
                BoxConstraints(maxWidth: DeviceUtils().width.value * 0.24),
                height: 22,
                child: Text(
                  dataMap['name'],
                  style:
                  const TextStyle(fontSize: 13, color: ColorConfig.mainTextColor),
                ),
              )
            ],
          ),
        ),
      ));
    }
    return lists;
  }

  popComponent(context) async {
    double height =
        DeviceUtils().height.value - 56 - 44 - 56 - DeviceUtils().bottom.value;
    showPopupWindow(
      context,
      gravity: KumiPopupGravity.centerTop,
      bgColor: const Color(0x88000000),
      clickOutDismiss: true,
      clickBackDismiss: true,
      customAnimation: false,
      customPop: false,
      customPage: false,
      underStatusBar: false,
      underAppBar: false,
      offsetX: 0,
      offsetY: 100,
      duration: const Duration(milliseconds: 200),
      childFun: (pop) {
        return Container(
          width: DeviceUtils().width.value,
          height: height > 56.0 * controller.orgList.length
              ? 56.0 * controller.orgList.length
              : height,
          padding: const EdgeInsets.only(left: 15, right: 15),
          key: GlobalKey(),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 7,
                offset: const Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              children: backOrgWidget(context),
            ),
          ),
        );
      },
    );
  }

  backOrgWidget(context) {
    List<Widget> lists = [];

    for (var i = 0; i < controller.orgList.length; i++) {
      int haveApprove = 0;
      OrgModel model = controller.orgList[i];
      for (var j = 0; j < controller.unTreatedList.length; j++) {
        Map unreadMap = controller.unTreatedList[j];
        if (model.companyId == unreadMap['orgId']) {
          if (model.corgId.isEmpty) {
            haveApprove = unreadMap['number'] ?? 0;
          } else {
            if (model.corgId == unreadMap['corgId']) {
              haveApprove = unreadMap['number'] ?? 0;
            }
          }
        }
      }
      model.chooseState = 0;
      if (model.companyId == controller.currentModel!.companyId) {
        model.chooseState = 1;
      }
      model.haveApprove = haveApprove;
      lists.add(InkWell(
        onTap: () async {
          if (controller.cacheToken != null) {
            controller.cacheToken!.cancel();
          }
          if (controller.dataToken != null) {
            controller.dataToken!.cancel();
          }
          if (controller.unreadToken != null) {
            controller.unreadToken!.cancel();
          }
          if (controller.topFunctionCancelToken != null) {
            controller.topFunctionCancelToken!.cancel();
          }
          if (controller.currentIndex.value != 1) {
            controller.currentIndex.value = 1;
            controller.tabController?.animateTo(1);
          }
          
          controller.currentOrgId = model.companyId;
          controller.currentModel = model;
          controller.dealData();
          await UserDefault.setData(
              '${Define.CURRENTORGID}-${controller.userId}', model.companyId);
          Channel().invoke(
              Channel_Native_RefreshCurrentOrg, {'companyId': model.companyId});
          await UserDefault.removeData(Define.APPROVE_SEND_CACHEKEY);
          Navigator.of(context).pop('');
        },
        child: ChooseOrgComponentWidget(model),
      ));
    }

    return lists;
  }
}
