import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/modules/workStand/models/meeting_list_model.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';

import '../../../../common/channel/channel.dart';
import '../../../../common/config/config.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../routes/app_pages.dart';
import '../controllers/meeting_list_controller.dart';

class MeetingListView extends GetView<MeetingListController> {
  MeetingListView({Key? key}) : super(key: key);
  final meetingListController = Get.find<MeetingListController>();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: meetingListController,
        builder: (logic) {
          return Scaffold(
            backgroundColor: ColorConfig.backgroundColor,
            appBar: TitleBar().backAppbar(context, '担当会议', false, [],
                onPressed: () {
              if (meetingListController.isNative.value) {
                meetingListController.onClose();
                Channel().invoke(Channel_Native_Back, {});
              } else {
                Get.back();
              }
            }),
            body: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: EdgeInsets.only(top: 16),
                  color: ColorConfig.whiteColor,
                  width: double.infinity,
                  height: 114,
                  child: Row(
                    children: [
                      Expanded(
                          flex: 1,
                          child: InkWell(
                            onTap: () {
                              Get.toNamed(Routes.MEETING_JOIN,
                                  arguments: {'type': 0},
                                  preventDuplicates: false);
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 54,
                                  height: 54,
                                  child: Image.asset(
                                      'assets/images/3.0x/meeting_list_join.png'),
                                ),
                                8.gap,
                                Container(
                                  height: 20,
                                  child: Text(
                                    '加入会议',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                )
                              ],
                            ),
                          )),
                      Expanded(
                          flex: 1,
                          child: InkWell(
                            onTap: () {
                              Get.toNamed(Routes.MEETING_JOIN,
                                  arguments: {'type': 3},
                                  preventDuplicates: false);
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 54,
                                  height: 54,
                                  child: Image.asset(
                                      'assets/images/3.0x/meeting_list_quick.png'),
                                ),
                                8.gap,
                                Container(
                                  height: 20,
                                  child: Text(
                                    '快速会议',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                )
                              ],
                            ),
                          )),
                      Expanded(
                          flex: 1,
                          child: InkWell(
                            onTap: () {
                              Get.toNamed(Routes.MEETING_RESERVE,preventDuplicates: false);
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 54,
                                  height: 54,
                                  child: Image.asset(
                                      'assets/images/3.0x/meeting_list_reserve.png'),
                                ),
                                8.gap,
                                Container(
                                  height: 20,
                                  child: Text(
                                    '预约会议',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                )
                              ],
                            ),
                          ))
                    ],
                  ),
                ),
                Expanded(
                    child: meetingListController.dataList.isEmpty
                        ? _noDataWidget()
                        : ListView.builder(
                            itemCount: meetingListController.dataList.length,
                            itemBuilder: (context, index) {
                              Map dataMap =
                                  meetingListController.dataList[index];
                              return Column(
                                children: [
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    height: 38,
                                    margin: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    padding: const EdgeInsets.only(
                                        left: 16, right: 16, top: 8, bottom: 8),
                                    child: Text(
                                      dataMap['date'],
                                      style: const TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.mainTextColor),
                                    ),
                                  ),
                                  Container(
                                    margin: const EdgeInsets.only(
                                        left: 16, right: 16),
                                    decoration: BoxDecoration(
                                        color: ColorConfig.whiteColor,
                                        borderRadius: BorderRadius.circular(8)),
                                    child: Column(
                                      children: _getWidgetList(dataMap['list']),
                                    ),
                                  ),
                                  8.gap
                                ],
                              );
                            })),
              ],
            ),
          );
        });
  }

  _getWidgetList(List modelList) {
    List<Widget> lists = [];
    for (var i = 0; i < modelList.length; i++) {
      MeetingListModel listModel = modelList[i];
      String tempMeetingStr = '';
      for (var i = 0; i < listModel.meetingId.length; i++) {
        String character = listModel.meetingId.getRange(i, i + 1);
        if (i != listModel.meetingId.length - 1 && (i + 1) % 3 == 0) {
          tempMeetingStr = '$tempMeetingStr$character ';
        } else {
          tempMeetingStr = '$tempMeetingStr$character';
        }
      }
      lists.add(InkWell(
        onTap: () async {
          var result = await Get.toNamed(Routes.MEETING_DETAIL,
              arguments: {'type': 1, 'meetingId': listModel.meetingId},
              preventDuplicates: false);
          if (result != null) {
            meetingListController.getMeetingList();
          }
        },
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.only(left: 16, right: 16),
              child: Row(
                children: [
                  Expanded(
                      child: Column(
                    children: [
                      8.gap,
                      Container(
                        height: 22,
                        child: Row(
                          children: [
                            Flexible(
                                child: Container(
                              child: Text(
                                listModel.topic,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.mainTextColor),
                              ),
                            )),
                            8.gap,
                            Container(
                              alignment: Alignment.center,
                              padding: const EdgeInsets.only(left: 8, right: 8),
                              height: 18,
                              decoration: BoxDecoration(
                                  color: listModel.backColor,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Text(
                                listModel.statusString,
                                style: TextStyle(
                                    fontSize: 10, color: listModel.textColor),
                              ),
                            ),
                            8.gap,
                            Container(
                              child: Text(
                                listModel.msgString,
                                style: TextStyle(
                                    fontSize: 10, color: listModel.textColor),
                              ),
                            )
                          ],
                        ),
                      ),
                      4.gap,
                      Container(
                        height: 22,
                        child: Row(
                          children: [
                            Container(
                              child: Text(
                                '${listModel.createTimeStr} - ${listModel.endTimeStr}',
                                style: const TextStyle(
                                    fontSize: 13,
                                    color: ColorConfig.desTextColor),
                              ),
                            ),
                            8.gap,
                            Container(
                              child: Text('ID: $tempMeetingStr',
                                  style: const TextStyle(
                                      fontSize: 13,
                                      color: ColorConfig.desTextColor)),
                            )
                          ],
                        ),
                      ),
                      8.gap,
                    ],
                  )),
                  8.gap,
                  SizedBox(
                    width: 9,
                    height: 17,
                    child: Image.asset('assets/images/3.0x/mine_right.png'),
                  )
                ],
              ),
            ),
            Offstage(
              offstage: i == modelList.length - 1,
              child: const Divider(
                height: 1,
                color: ColorConfig.backgroundColor,
              ),
            )
          ],
        ),
      ));
    }
    return lists;
  }

  _noDataWidget() {
    return Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 130,
            height: 130,
            child: Image.asset('assets/images/3.0x/meeting_noRecord.png'),
          ),
          Container(
            alignment: Alignment.center,
            height: 20,
            child: Text(
              '暂无会议',
              style: TextStyle(fontSize: 14, color: ColorConfig.desTextColor),
            ),
          ),
          100.gap
        ],
      ),
    );
  }
}
