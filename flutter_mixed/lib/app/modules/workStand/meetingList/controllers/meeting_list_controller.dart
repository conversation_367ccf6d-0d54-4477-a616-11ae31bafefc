import 'dart:async';

import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/workStand/models/meeting_list_model.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../utils/http.dart';
import '../../../../../logger/logger.dart';

class MeetingListController extends GetxController {

  StreamSubscription? subscription;
  RxBool isNative = false.obs; //是否是原生跳转
  RxList dataList = [].obs;
  @override
  void onInit() {
    super.onInit();
    subscription = eventBus.on<Map>().listen((event) {
      if (event['refreshMeetingList'] != null) {
        getMeetingList();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    getMeetingList();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getMeetingList() {
    DioUtil()
        .get('${MeetingApi.GET_MEETING_LIST}?current=1&size=9999', null, true,
            () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataMap = data['data'];
        List tempDataList = dataMap['records'];

        List totalList = [];
        String dateStr = '';
        Map lastMap = {};
        for (var i = 0; i < tempDataList.length; i++) {
          MeetingListModel listModel =
              MeetingListModel.fromJson(tempDataList[i]);
          String timeStr =
              BaseInfo().formatTimestamp(listModel.createTime, 'yyyy年MM月dd日');
          if (BaseInfo().isToday(listModel.createTime)) {
            timeStr = '今天';
          }
          if (BaseInfo().isTomorrow(listModel.createTime)) {
            timeStr = '明天';
          }
          if (BaseInfo().isTheDayAfterTomorrow(listModel.createTime)) {
            timeStr = '后天';
          }
          if (timeStr != dateStr) {
            lastMap = {
              'date': timeStr,
              'list': [listModel]
            };
            dateStr = timeStr;
            totalList.add(lastMap);
          } else {
            List lastList = lastMap['list'];
            lastList.add(listModel);
          }
        }
        dataList.value = totalList;
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
