import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/manage/company_model.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/views/approve_function_view.dart';
import 'package:flutter_mixed/app/modules/workStand/approveList/controllers/approve_list_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/approveList/views/approve_list_view.dart';
import 'package:get/get.dart';

import '../../../contact/model/org/org_model.dart';
import '../../workFlow/controllers/work_flow_controller.dart';

class ApproveHomeController extends GetxController {

  PageController pageController = PageController();

  RxInt currentIndex = 0.obs;

  var bottomBars =  [
    BottomNavigationBarItem(
      icon: SizedBox(
        width: 24,
        height: 24,
        child: Image.asset(
            'assets/images/3.0x/workflow_function_add0.png'),
      ),
      activeIcon: SizedBox(
        width: 24,
        height: 24,
        child: Image.asset(
            'assets/images/3.0x/workflow_function_add.png'),
      ),
      label: "发起审批",
    ),
    BottomNavigationBarItem(
        icon: SizedBox(
          width: 24,
          height: 24,
          child: Image.asset(
              'assets/images/3.0x/workflow_function_list0.png'),
        ),
        activeIcon: SizedBox(
          width: 24,
          height: 24,
          child: Image.asset(
              'assets/images/3.0x/workflow_function_list.png'),
        ),
        label: "审批中心"),
  ];

  // 审批主页，两个子 child（发起审批、审批列表）
  var approveChildItems = [
    const ApproveFunctionView(),
    ApproveListView()
  ];

  String orgId = '';
  OrgModel? model;

  RxInt currentType = 0.obs; //0审批模式1编辑模式
  RxBool isSelctAll = false.obs; //是否全选

  RxString showContent = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    print('审批 home ， onInit');

    try {
      orgId = Get.arguments['orgId'];
      currentIndex.value = Get.arguments['index'];
      if (Get.arguments['model'] != null) {
        model = Get.arguments['model'];
      } else {
        int isFromWork = Get.arguments['isFromWork'];
        if (isFromWork == 1) {
          WorkFlowController workFlowController = Get.find();
          model = workFlowController.currentModel;
        }
      }
    } catch (e) {}

    // print('异常的时候： aaa, orgId = ${orgId}');
    //
    // if(orgId == "" || orgId == null){
    //   print('CompanyModelHelper.orgMod......');
    //
    //   orgId = CompanyModelHelper.orgModel!.companyId;
    //   currentIndex.value = 0;
    //   model = CompanyModelHelper.orgModel;
    //
    //   print('异常的时候： orgId = ${orgId}');
    // }
  }

  @override
  void onReady() {
    super.onReady();
    Get.lazyPut(() => ApproveListController());
  }

  @override
  void onClose() {
    CompanyModelHelper.approveCenterTabIndex = 0;
    super.onClose();
  }

  settingCurrtenType(int modelType) {
    if (modelType == 0) {
      settingSelectButton(false);
    }
    currentType.value = modelType;
  }

  settingCurrentCount(String showCount) {
    showContent.value = showCount;
  }

  settingSelectButton(bool isSelect) {
    isSelctAll.value = isSelect;
    isSelctAll.refresh();
  }
}
