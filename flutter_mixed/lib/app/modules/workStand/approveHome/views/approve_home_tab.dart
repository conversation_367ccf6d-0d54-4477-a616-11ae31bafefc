import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/keep_alive_wrapper.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/manage/company_model.dart';
import 'package:flutter_mixed/app/modules/workStand/approveList/views/approve_list_view.dart';
import 'package:get/get.dart';

import '../../approveList/controllers/approve_list_controller.dart';

/// 审批主页， 套壳使用
class ApproveMainTabWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return ApproveHomeTabViewState();
  }
}

class ApproveHomeTabViewState extends State<ApproveMainTabWidget>
    with AutomaticKeepAliveClientMixin {
  ApproveListController? approveListController;
  
  @override
  void initState() {
    super.initState();
    CompanyModelHelper.approveListController ??= Get.find<ApproveListController>();
    approveListController = CompanyModelHelper.approveListController;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ApproveListView(
      isCentre: true,
      approveListController: approveListController,
    );
  }

  @override
  bool get wantKeepAlive => true;
}


class ApproveMainTabWidget1 extends StatelessWidget {

  const ApproveMainTabWidget1({super.key});

  @override
  Widget build(BuildContext context) {
    CompanyModelHelper.approveListController ??= Get.find<ApproveListController>();
    var approveListController = CompanyModelHelper.approveListController;
    return KeepAliveWrapper(child: ApproveListView(
      isCentre: true,
      approveListController: approveListController,
    ));
  }


}