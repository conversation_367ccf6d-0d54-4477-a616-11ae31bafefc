import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/app_scaffold.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/main.dart';

import 'package:get/get.dart';

import '../../../../common/config/config.dart';
import '../controllers/approve_home_controller.dart';

class ApproveHomeView extends GetView<ApproveHomeController> {

  //ApproveHomeController approveHomeController = Get.put(ApproveHomeController());

  ApproveHomeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => AppScaffold(
        showToolBar: false,
        bottomNavigationBar: Container(
          width: DeviceUtils().width.value,
          color: ColorConfig.whiteColor,
          // height: 49 + DeviceUtils().bottom.value,
          child: controller.currentType.value == 1
              ? Container(         
            margin: EdgeInsets.only(
                left: 8,
                right: 8,
                top: 8,
                bottom: DeviceUtils().bottom.value + 10),
            color: ColorConfig.whiteColor,
            child: SettingWidget().backApproveEditState(controller.showContent.value,controller.isSelctAll.value?1:0, () {
              //全选
              if(controller.isSelctAll.value){
                controller.isSelctAll.value = false;
              }else{
                controller.isSelctAll.value = true;
              }
              controller.isSelctAll.refresh();
              eventBus.fire({'selectAll':controller.isSelctAll.value?1:0});
            }, () {
              //拒绝
              eventBus.fire({'approveListOption':2});
            }, () {
              //同意
              eventBus.fire({'approveListOption':1});
            }),
          )
              : BottomNavigationBar(
              backgroundColor: ColorConfig.whiteColor,
              type: BottomNavigationBarType.fixed,
              currentIndex: controller.currentIndex.value,
              onTap: (index) {
                controller.pageController.jumpToPage(index);
                controller.update();
              },
              items: controller.bottomBars),
        ),
        body: PageView.builder(
            physics: const NeverScrollableScrollPhysics(),
            controller: controller.pageController,
            onPageChanged: (index){
              controller.currentIndex.value = index;
              controller.update();
            },
            itemBuilder: (ctx , index){
              return controller.approveChildItems[index];
            })));
  }
}
