import 'package:flutter_mixed/app/modules/workStand/approveFunction/controllers/approve_function_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/approveList/controllers/approve_list_controller.dart';
import 'package:get/get.dart';

import '../controllers/approve_home_controller.dart';

class ApproveHomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ApproveHomeController>(
      () => ApproveHomeController(),
    );
    // Get.lazyPut(() => ApproveFunctionController());
    // Get.lazyPut(() => ApproveListController());
  }
}
