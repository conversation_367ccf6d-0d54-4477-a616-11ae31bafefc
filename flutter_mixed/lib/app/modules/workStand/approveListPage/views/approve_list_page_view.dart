import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/local_notification/notification_task.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/modules/place_holder/common_empty.dart';
import 'package:flutter_mixed/app/modules/workStand/approveHome/controllers/approve_home_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/approveList/controllers/approve_list_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/dialog/approve_screen_dialog.dart';
import 'package:flutter_mixed/app/modules/workStand/dialog/approve_search_org.dart';
import 'package:flutter_mixed/app/modules/workStand/models/approve_list_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/approve_screen_model.dart';
import 'package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart';
import 'package:flutter_mixed/app/retrofit/datasource/approve_datasource.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import 'package:get/get.dart';
import 'package:kumi_popup_window/kumi_popup_window.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../../../main.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/config/config.dart';
import '../../../../common/widgets/image_loader.dart';
import '../../../../common/widgets/smart_refresh.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import 'package:badges/badges.dart' as badge;

import '../../dialog/approve_dialog.dart';

class ApproveListPageView extends StatefulWidget {
  final argument;
  ApproveListPageView(this.argument);

  @override
  ApproveListPageViewState createState() => ApproveListPageViewState();
}

class ApproveListPageViewState extends State<ApproveListPageView>
    with AutomaticKeepAliveClientMixin {
  List statusList = [
    {'modelId': 0, 'modelName': '全部状态'},
    {'modelId': 1, 'modelName': '已通过'},
    {'modelId': 2, 'modelName': '未通过'},
    {'modelId': 3, 'modelName': '进行中'},
    {'modelId': 5, 'modelName': '已撤回'},
    {'modelId': 6, 'modelName': '已退回'}
  ];

  var statusMap = {
    '0': '全部状态',
    '1': '已通过',
    '2': '未通过',
    '3': '进行中',
    '5': '已撤回',
    '6': '已退回',
    '200': '系统审批中'
  };
  int jumpType = 0;
  bool isSearch = false;
  List<ApproveScreenModel> searchOrgData = []; //搜索模式下的公司数据
  String orgId = '';
  String keyword = '';
  RxList<ApproveListModel> dataList = <ApproveListModel>[].obs;
  late StreamSubscription subscription;
  String modelId = ''; //模版id 审批按模版类型筛选(加班，补卡等)
  int page = 1;
  int maxPageSize = 500;
  List modelList = [
    {'modelId': '', 'modelName': '全部审批'}
  ]; //可筛选的模版集合

  String modelChoose = '全部审批'; //模版筛选当前内容
  int currentStatus = 0; //当前筛选审批状体
  int listType = 0; //当前审批类型
  bool isShowFooter = true;

  int notRead = 0; //0全部1未读 抄送用到
  List orgList = [
    {'orgId': '0', 'name': '全部公司'}
  ]; //全部子公司数据
  String corgId = '0'; //当前子公司id

  RxBool isShowBottom = false.obs; //是否显示底部全选控件

  RxBool isSelctAll = false.obs; //是否全选
  List selectList = [];
  ScrollController screenScrollController = ScrollController();
  String? currentOrgId; //全公司模式集团id
  String? currentOrgGroupId; //全公司模式组id
  String? currentCorgId; //全公司模式子公司id
  String? currentGroupName; //全公司模式当前模版分组名称
  String? currentModelName; //全公司模式当前模版名称
  RxList approveSceenList = [].obs; //所有筛选数据

  RxString currentScreenName = ''.obs; //当前搜索的系名称或公司名称 待办已办
  RxList currentGroups = [].obs; //当前筛选各模版数据

  //ApproveScreenModel? resetModel; //筛选列表初始展示数据，当前需求:展示第一个系或公司

  List screenOrgList = []; //全部公司列表
  RxString screenOrgName = '全部'.obs; //公司列表选中的公司名称
  bool isRequestModel = false;
  List templateList = []; //所有模版

  List modelNames = []; ////筛选控件模版数组

  String approveString = '全部状态'; //审批状态名称

  RefreshController refreshController = RefreshController();
  ScrollController listScrollController = ScrollController();
  bool isLastData = false; //标记待办是否加载到最后一页
  List batchList = []; //请求接口前记录批量审批待处理的数据，收到im消息9002时不做处理

  bool isAutherFirst = false; //是否是有权限的人第一次进入 是则直接弹出筛选
  int popup = 0; //0无权限 1有权限

  ApproveListModel? kingdeeModel; //记录被点击的金蝶数据
  bool isCentre = false;

  ApproveListController? approveListController;
  CancelToken? screenCancelToken;
  bool isNeedRefresh = true;

  // 调用一次，防止耗时
  final screenWidth = DeviceUtils().width.value;
  AutoScrollController autoScrollController =
      AutoScrollController(axis: Axis.horizontal);

  Timer? timer;

  CancelToken? listCancelToken;
  @override
  void initState() {
    super.initState();
    listType = widget.argument['listType'];
    orgId = widget.argument['orgId'];
    isCentre = widget.argument['isCentre'] ?? false;
    approveListController = widget.argument['approveListController'];
    keyword = approveListController!.keyword;
    jumpType = widget.argument['type'];
    isSearch = widget.argument['isSearch'];
    if (listType != 2) {
      maxPageSize = 50;
    }
    subscription = eventBus.on<Map>().listen((event) {
      if (event['keyword'] != null) {
        keyword = event['keyword'];
        if (event['isLoad'] == null && isSearch) {
          page = 1;
          getApproveListData(isTextFieldSearch: true);
        }
      }
      if (event['refreshApproveListPage'] != null) {
        if (event['refreshApproveListPage'] == listType) {
          if (event['isSearch'] != null) {
            if (!isSearch && event['isSearch']) {
              //搜索页面批量审批刷新非搜索页面
              page = 1;
              getApproveListData(isResetPage: false);

              if (listType == 2 || listType == 3) {
                _getAllScreenData();
              }
              if (approveListController!.isShowCancel.value) {
                approveListController!.isShowCancel.value = false;
                approveListController!.settingRightButton(listType);
                approveListController!.isShowCancel.refresh;
              }
            }
            return;
          }

          bool isHave = false;
          if (event['deleteId'] != null && listType == 2) {
            String deleteId = event['deleteId'];
            if (!batchList.contains(deleteId)) {
              ApproveListModel? listTempModel;
              for (var i = 0; i < dataList.length; i++) {
                ApproveListModel listModel = dataList[i];
                if (listModel.approveAssigneeId == deleteId) {
                  listTempModel = listModel;
                  isHave = true;
                  break;
                }
              }
              if (listTempModel != null) {
                dataList.remove(listTempModel);
              }
            }
          }
          if (!isHave) {
            page = 1;
            if (listType == 2) {
              eventBus.fire({'refreshApproveListPage': 3});
            }
          }
          getApproveListData(isResetPage: isHave);

          if (listType == 2 || listType == 3 && !isSearch) {
            _getAllScreenData();
          }
        }
      }

      //批量审批
      if (event['selectAll'] != null) {
        if (isSearch) {
          return;
        }
        if (listType == 2) {
          int selectAll = event['selectAll']; //0取消全选1全选
          if (selectAll == 0) {
            if (jumpType != 2) {
              _clearSelectedModel();
            }
          } else {
            List tempList = [];
            for (var i = 0; i < dataList.length; i++) {
              ApproveListModel listModel = dataList[i];
              if (listModel.type != 2) {
                listModel.isSelected = true;
                tempList.add(listModel);
              }
              if (tempList.length == maxPageSize) {
                break;
              }
            }
          }

          approveListController!.isShowCancel.value = !_selectIsEmpty();
          approveListController!.update();
          _refresh();
          if (jumpType != 2) {
            ApproveHomeController homeController = Get.find();
            homeController
                .settingCurrentCount('${_backSelectLength()}/$maxPageSize');
          }
        }
      }
      if (event['approveListOption'] != null && listType == 2) {
        if (jumpType != 4) {
          int option = event['approveListOption'];
          _isNeedApproveContent(option);
        }
      }
      if (event['listPage_IsCancelChoose'] != null && listType == 2) {
        if (event['isSearch'] != isSearch) {
          return;
        }
        if (event['isSearch']) {
          _clearSelectedModel();
          _refresh();
        } else {
          _clearSelectedModel();
          _refresh();
        }
      }
      if (event['listPage_refreshScreenData'] != null) {
        //待后端有刷新红点逻辑后删除
        if ((listType == 2 || listType == 3)) {
          if (isNeedRefresh) {
            _getAllScreenData();
          }
        } else {
          if (isNeedRefresh) {
            _getAllOrgScreenData(isRefrshData: false);
          }
        }
      }
      if (event['listPage_IsNeedRefreshScreenData'] != null) {
        //待后端有刷新红点逻辑后删除
        if (event['listPage_IsNeedRefreshScreenData'] == 0) {
          isNeedRefresh = false;
        } else {
          isNeedRefresh = true;
        }
      }
      if (event['listPage_refreshNotRead'] != null) {
        if (listType != 2) {
          String approveId = event['listPage_refreshNotRead'];
          for (var i = 0; i < dataList.length; i++) {
            ApproveListModel model = dataList[i];
            if (model.approveId == approveId) {
              model.notRead = 0;
              _refresh();
            }
          }
        }
      }
      if (event['list_viewShowAll'] != null) {
        if (popup == 1 && listType == 2) {
          if (!isAutherFirst) {
            isAutherFirst = true;
            _popScreenDialog();
          }
        }
      }
      // if (event['listPage_kingdee_status'] != null) {
      //   //判断金蝶审批状态
      //   if (kingdeeModel != null && listType == 2) {
      //     _getApproveStatus();
      //   }
      // }
      if (event['listPageView_showScrren'] != null) {
        //弹出筛选弹框
        if (!isAutherFirst && approveSceenList.isNotEmpty) {
          isAutherFirst = true;
          _popScreenDialog(isPowerFirst: true);
        }
      }
      //刷新对应红点
      if (event['refresh_list_red_point'] != null) {
        String refreshId = event['refresh_list_red_point'];
        if (refreshId.isEmpty) {
          return;
        }
        if (listType != 2) {
          if (refreshId == '$listType') {
            //目前只用于listType为5抄送的时候
            for (var i = 0; i < dataList.length; i++) {
              ApproveListModel model = dataList[i];
              model.notRead = 0;
            }
          } else {
            for (var i = 0; i < dataList.length; i++) {
              ApproveListModel model = dataList[i];
              if (model.approveId == refreshId) {
                model.notRead = 0;
                break;
              }
            }
          }
          _refresh();
        }
      }
      if (event['deleteKingdeeApprove'] != null && listType == 2) {
        //删除金蝶对应审批
        var kingDeeApproveId = event['deleteKingdeeApprove']['approveId'];
        var kingDeeAproveAssigneeId =
            event['deleteKingdeeApprove']['approveAssigneeId'];
        //var opinion = event['deleteKingdeeApprove']['opinion'];
        if (kingDeeApproveId != null) {
          for (var i = 0; i < dataList.length; i++) {
            ApproveListModel model = dataList[i];
            if (model.approveId == kingDeeApproveId) {
              dataList.remove(model);
              break;
            }
          }
          if (selectList.isNotEmpty) {
            for (var i = 0; i < selectList.length; i++) {
              ApproveListModel model = selectList[i];
              if (model.approveId == kingDeeApproveId) {
                selectList.remove(model);
                break;
              }
            }
            if (selectList.isEmpty) {
              _clearSelectedModel();
            }
          }
          _refresh();
          _changeKingdeeApproveStatus(kingDeeApproveId, kingDeeAproveAssigneeId);
        }
      }
    });

    if (jumpType != 2) {
      if (listType == 2 || listType == 3) {
        getApproveListData();
        _getAllScreenData(mustRefresh: true);
      } else {
        _getAllOrgScreenData();
      }
    } else {
      getApproveListData();
      getAllChildOrgs();
    }
    if (isSearch) {
      if (listType == 2 || listType == 3) {
        _getSearchOrgData();
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    timer?.cancel();
    subscription.cancel();
    refreshController.dispose();
    screenScrollController.dispose();
    listScrollController.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Obx(() => Scaffold(
        backgroundColor: ColorConfig.backgroundColor,
        body: InkWell(
            onTap: () {
              approveListController!.node.unfocus();
            },
            child: getWidgetBody())));
  }

  getWidgetBody() {
    return Column(
      children: [
        Container(
            alignment: Alignment.centerLeft,
            color: ColorConfig.whiteColor,
            width: double.infinity,
            height: 48,
            padding: const EdgeInsets.only(left: 15, right: 15),
            child: _backScreenWidget()),
        Offstage(
          offstage: listType != 5,
          child: Container(
            width: double.infinity,
            height: 38,
            padding: const EdgeInsets.only(left: 15, right: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    if (notRead == 0) {
                      notRead = 1;
                    } else {
                      notRead = 0;
                    }
                    page = 1;
                    dataList.refresh();
                    getApproveListData();
                  },
                  child: Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: Image.asset(notRead == 0
                            ? 'assets/images/3.0x/login_unselect.png'
                            : 'assets/images/3.0x/login_selected.png'),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Container(
                        child: const Text(
                          '只看未读',
                          style: TextStyle(
                              fontSize: 13, color: ColorConfig.desTextColor),
                        ),
                      )
                    ],
                  ),
                ),
                InkWell(
                    onTap: () {
                      signAllRead();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                              'assets/images/3.0x/approve_signRead.png'),
                        ),
                        const SizedBox(
                          width: 4,
                        ),
                        Container(
                          child: const Text(
                            '全标已读',
                            style: TextStyle(
                                fontSize: 13, color: ColorConfig.desTextColor),
                          ),
                        )
                      ],
                    ))
              ],
            ),
          ),
        ),
        Expanded(
          child: SmartRefresher(
            controller: refreshController,
            enablePullDown: true,
            enablePullUp: true,
            header: smartRefresherHeader(),
            footer: smartRefreshFooter(),
            onRefresh: () async {
              page = 1;
              getApproveListData();
            },
            onLoading: () async {
              page++;
              getApproveListData(isResetPage: isLastData);
            },
            child: _buildListView(),
          ),
        ),
        Offstage(
          offstage: (jumpType == 0 && !isSearch) ||
              listType != 2 ||
              !isShowBottom.value,
          child: Container(
            //  height: 56,
            padding: EdgeInsets.only(
                left: 8,
                right: 8,
                top: 8,
                bottom: DeviceUtils().bottom.value + 10),
            color: ColorConfig.whiteColor,
            child: SettingWidget().backApproveEditState(
                '${_backSelectLength()}/$maxPageSize', isSelctAll.value ? 1 : 0,
                () {
              if (isSelctAll.value) {
                _clearSelectedModel();
              } else {
                isSelctAll.value = true;
                List tempList = [];
                for (var i = 0; i < dataList.length; i++) {
                  ApproveListModel listModel = dataList[i];
                  if (listModel.type != 2) {
                    listModel.isSelected = true;
                    tempList.add(listModel);
                  }
                  if (tempList.length == maxPageSize) {
                    break;
                  }
                }
              }

              approveListController?.isShowCancel.value = !_selectIsEmpty();
              approveListController?.update();
              _refresh();

              if (jumpType == 4 && !isSearch) {
                HomeController homeController = Get.find();
                homeController.hiddenBottomBar = !_selectIsEmpty();
                homeController.update();
              }
            }, () {
              _isNeedApproveContent(2);
            }, () {
              _isNeedApproveContent(1);
            }),
          ),
        )
      ],
    );
  }

  _backScreenWidget() {
    if (jumpType != 2) {
      if (isSearch) {
        return _backSearchPageScreenWidget();
      } else {
        return _backAllOrgScreen();
      }
    } else {
      //合作团队
      return _backSingleOrgScreen();
    }
  }

  //搜索页面筛选控件
  _backSearchPageScreenWidget() {
    return InkWell(
      onTap: () {
        if (listType == 2 || listType == 3) {
          _popSearchOrgDataDialog();
        } else {
          popComponent(context, 10);
        }
      },
      child: Container(
        padding: const EdgeInsets.only(left: 8, right: 8),
        alignment: Alignment.center,
        height: 30,
        decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: ColorConfig.lineColor),
            borderRadius: BorderRadius.circular(15)),
        child: Text(
          screenOrgName.value,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style:
              const TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
        ),
      ),
    );
  }

  _popSearchOrgDataDialog() {
    if (searchOrgData.isEmpty) {
      toast('尚未获取到公司数据，请稍后再试');
      return;
    }
    ;
    ApproveSearchOrgDialog(selectMap: {
      'groupId': currentOrgGroupId,
      'orgId': currentOrgId,
      'corgId': currentCorgId
    }).show(searchOrgData, (Map orgMap) {
      currentOrgGroupId = orgMap['groupId'];
      currentOrgId = orgMap['orgId'];
      currentCorgId = orgMap['corgId'];
      screenOrgName.value = orgMap['name'];
      page = 1;
      _clearSelectedModel();
      getApproveListData();
    });
  }

  _backAllOrgScreen() {
    if (listType == 2 || listType == 3) {
      return Row(
        children: [
          InkWell(
            onTap: () {
              if (approveSceenList.isEmpty) {
                toast('尚未获取到筛选数据，请稍后再试');
                return;
              }
              _popScreenDialog();
            },
            child: Container(
              padding: const EdgeInsets.only(left: 5, right: 5),
              height: double.infinity,
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    child:
                        Image.asset('assets/images/3.0x/external_screen.png'),
                  ),
                  Container(
                    constraints: const BoxConstraints(maxWidth: 130),
                    padding: const EdgeInsets.only(left: 5, right: 5),
                    child: Text(
                      currentScreenName.value,
                      style: const TextStyle(
                          overflow: TextOverflow.ellipsis,
                          fontSize: 14,
                          color: ColorConfig.mainTextColor),
                    ),
                  )
                ],
              ),
            ),
          ),
          Expanded(
              child: ListView(
            scrollDirection: Axis.horizontal,
            controller: autoScrollController,
            children: _backScrollList(),
          ))
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
              flex: 1,
              child: InkWell(
                onTap: () {
                  popComponent(context, 10);
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 8, right: 8),
                  alignment: Alignment.center,
                  height: 30,
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 0.5, color: ColorConfig.lineColor),
                      borderRadius: BorderRadius.circular(15)),
                  child: Text(
                    screenOrgName.value,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                ),
              )),
          15.gap,
          Expanded(
              flex: 1,
              child: InkWell(
                onTap: () {
                  popComponent(context, 11);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 30,
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 0.5, color: ColorConfig.lineColor),
                      borderRadius: BorderRadius.circular(15)),
                  child: Text(
                    currentModelName ?? '全部审批',
                    style: const TextStyle(
                        overflow: TextOverflow.ellipsis,
                        fontSize: 14,
                        color: ColorConfig.mainTextColor),
                  ),
                ),
              )),
          listType == 4 ? 15.gap : 0.gap,
          Expanded(
              flex: listType == 4 ? 1 : 0,
              child: InkWell(
                onTap: () {
                  popComponent(context, 1);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: listType == 4 ? 30 : 0,
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 0.5, color: ColorConfig.lineColor),
                      borderRadius: BorderRadius.circular(15)),
                  child: Text(
                    listType == 4 ? approveString : '',
                    style: const TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                ),
              ))
        ],
      );
    }
  }

  _backScrollList() {
    List<Widget> lists = [];
    for (var index = 0; index < modelNames.length; index++) {
      Models templateModel = modelNames[index];
      String showText = templateModel.modelName!;
      if (templateModel.count! > 0) {
        showText = '${templateModel.modelName} ${templateModel.count}';
      }
      templateModel.isCurrentSrceenModel =
          templateModel.groupName == currentGroupName &&
              templateModel.modelName == currentModelName;
      lists.add(AutoScrollTag(
        key: ValueKey(index),
        controller: autoScrollController,
        index: index,
        child: InkWell(
          onTap: () {
            if (templateModel.isCurrentSrceenModel) {
              currentGroupName = null;
              currentModelName = null;
            } else {
              currentGroupName = templateModel.groupName;
              currentModelName = templateModel.modelName;
            }
            _clearSelectedModel();
            _refresh();
            page = 1;
            getApproveListData();
          },
          child: Container(
            alignment: Alignment.center,
            margin: const EdgeInsets.fromLTRB(0, 9, 5, 9),
            padding: const EdgeInsets.only(left: 8, right: 8),
            height: 30,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: templateModel.isCurrentSrceenModel
                    ? ColorConfig.mybackgroundColor
                    : ColorConfig.backgroundColor),
            child: Text(
              showText,
              style: TextStyle(
                  fontSize: 14,
                  color: templateModel.isCurrentSrceenModel
                      ? ColorConfig.themeCorlor
                      : ColorConfig.mainTextColor),
            ),
          ),
        ),
      ));
    }
    return lists;
  }

  _backSingleOrgScreen() {
    String modelString = '';
    for (var element in modelList) {
      if (modelId == element['modelId']) {
        modelString = element['modelName'];
        break;
      }
    }
    String corgName = '';
    for (var element in orgList) {
      if (corgId == element['orgId']) {
        corgName = element['name'];
        break;
      }
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            if (isSearch) {
              popComponent(Get.context, 2);
            } else {
              popComponent(Get.context, 0);
            }
          },
          child: Container(
            alignment: Alignment.center,
            width: !isSearch ? (screenWidth - 45) * 0.5 : screenWidth - 30,
            height: 30,
            decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: ColorConfig.lineColor),
                borderRadius: BorderRadius.circular(15)),
            child: Text(
              isSearch ? corgName : modelString,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
        ),
        SizedBox(
          width: !isSearch ? 15 : 0,
        ),
        InkWell(
          onTap: () {
            popComponent(Get.context, 2);
          },
          child: Container(
            alignment: Alignment.center,
            width: !isSearch ? (screenWidth - 45) * 0.5 : 0,
            height: !isSearch ? 30 : 0,
            decoration: BoxDecoration(
                border: Border.all(width: 0.5, color: ColorConfig.lineColor),
                borderRadius: BorderRadius.circular(15)),
            child: Text(
              corgName,
              style: const TextStyle(
                  fontSize: 14, color: Color.fromARGB(255, 5, 9, 17)),
            ),
          ),
        )
      ],
    );
  }

  _buildListView() {
    if (dataList.isEmpty) {
      return Container(
        alignment: Alignment.center,
        child: CommonEmpty(_backNoDataText()),
      );
    }
    return ListView.builder(
        itemCount: dataList.length,
        controller: listScrollController,
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: false,
        itemBuilder: (context, index) {
          ApproveListModel model = dataList[index];
          return Container(
            width: double.infinity,
            margin: const EdgeInsets.only(left: 10, right: 10, top: 10),
            child: Stack(
              children: [
                InkWell(
                  onTap: () async {
                    bool isNotRead = false;

                    if (listType != 2) {
                      if (model.notRead == 1) {
                        isNotRead = true;
                      }
                      model.notRead = 0;
                      _refresh();
                    }

                    if (model.source == 1) {
                      kingdeeModel = null;
                      var result = await Get.toNamed('/approve-detail',
                          arguments: {
                            'orgId': jumpType != 2
                                ? model.orgId
                                : widget.argument['orgId'],
                            'approveId': model.approveId
                          },
                          preventDuplicates: false);
                      if (result != null && result['withDraw'] != null) {
                        dataList.remove(model);
                        _refresh();
                      }
                    } else if (model.source == 2 || model.source == 3) {
                      logger('====mmodel===${model.toJson()}');
                      if (listType == 2) {
                        kingdeeModel = model;
                      }

                      String url = model.detailUrl;
                      if (model.source == 2) {
                        var result = await Channel().invoke(Channel_Native_KingDeeToken);

                        if (result!['token'] == '') {
                          toast('获取信息失败');
                          return;
                        } else {
                          String kingDeeToken = result['token'];
                          url = '$url$kingDeeToken';
                        }
                      }

                      int platform = 1;
                      if (Platform.isIOS) {
                        platform = 2;
                      }
                      //获取appVersion
                      String appVersion = await BaseInfo().getAppVersion();

                      if (url.contains('?')) {
                        url = '$url&platform=$platform&appVersion=$appVersion';
                      } else {
                        url = '$url?platform=$platform&appVersion=$appVersion';
                      }

                      var param = {
                        'url': url,
                        'title': model.source == 2
                            ? '金蝶财务'
                            : model.source == 3
                            ? '金蝶HR'
                            : '',
                        'isWebNavigation': 0,
                        'orgId': model.orgId
                      };
                      openWebView(param);

                      // openWebView({
                      //   'url': url,
                      //   'title': model.source == 2
                      //       ? '金蝶财务'
                      //       : model.source == 3
                      //       ? '金蝶HR'
                      //       : '',
                      //   'isWebNavigation': 0,
                      //   'orgId': model.orgId
                      // });

                      if (isNotRead) {
                        bool isWorkFlow =
                            Get.isRegistered<WorkFlowController>();
                        if (isWorkFlow) {
                          WorkFlowController workFlowController = Get.find();
                          workFlowController.getAllCompanyUntreated();
                        }
                        eventBus.fire({'refreshUnreadCount': 1});
                      }
                    }
                  },
                  child: _buildApproveItemWidget(model),
                ),
                if (listType == 2 && model.type != 2) ...[
                  Positioned(
                      top: 0,
                      right: 0,
                      height: 52,
                      width: 52,
                      child: InkWell(
                        onTap: () {
                          if (!model.isSelected) {
                            if (_backSelectLength() < maxPageSize) {
                              model.isSelected = true;
                            } else {
                              toast('最多批量处理$maxPageSize条审批');
                            }
                          } else {
                            model.isSelected = false;
                          }
                          if (isSearch) {
                            isShowBottom.value = !_selectIsEmpty();
                            logger('isSHowb===$isShowBottom');
                            _refresh();
                          } else {
                            if (jumpType == 0) {
                              if (isCentre) {
                              } else {
                                ApproveHomeController approveHomeController =
                                    Get.find();
                                if (_selectIsEmpty()) {
                                  approveHomeController.settingCurrtenType(0);
                                } else {
                                  approveHomeController.settingCurrtenType(1);
                                }
                                approveHomeController.settingCurrentCount(
                                    '${_backSelectLength()}/$maxPageSize');
                              }
                            } else {
                              if (jumpType == 4) {
                                HomeController homeController = Get.find();
                                homeController.hiddenBottomBar =
                                    !_selectIsEmpty();
                                homeController.update();
                              }
                              isShowBottom.value = !_selectIsEmpty();
                              _refresh();
                            }
                          }

                          approveListController!.isShowCancel.value =
                              !_selectIsEmpty();
                          approveListController!.update();
                          _refresh();
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 52,
                          height: 52,
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: Image.asset(!model.isSelected
                                ? 'assets/images/3.0x/approve_unselected.png'
                                : 'assets/images/3.0x/approve_selected.png'),
                          ),
                        ),
                      ))
                ]
              ],
            ),
          );
        });
  }

  _buildApproveItemWidget(ApproveListModel model) {
    // 优化： 移入model 中
    // String statusString = '';
    // if (listType != 2) {
    //   if (model.source == 2 || model.source == 3) {
    //     statusString = '金蝶审批';
    //     if (model.status == 100) {
    //       statusString = '金蝶处理中';
    //     }
    //   }
    //   if (model.source == 1) {
    //     for (var statusMap in statusList) {
    //       if (model.status == statusMap['modelId']) {
    //         statusString = statusMap['modelName'];
    //         break;
    //       }
    //     }
    //   }
    // }
    // model.statusName = statusString;
    return Container(
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      width: double.infinity,
      padding: const EdgeInsets.only(top: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
              child: Column(
            children: [
              Container(
                padding: const EdgeInsets.only(left: 10, right: 10),
                // height: 40,
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      child: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          ImageLoader(
                            url: model.modelLogo,
                            width: 40,
                            height: 40,
                            radius: 20,
                            clearMemoryCacheWhenDispose: true,
                          ),
                          Positioned(
                              top: -4,
                              right: -4,
                              width: 8,
                              height: 8,
                              child: Offstage(
                                offstage: model.notRead == 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4),
                                      color: ColorConfig.deleteCorlor),
                                ),
                              ))
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Expanded(
                        child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                                child: Container(
                              alignment: Alignment.centerLeft,
                              // height: 20,
                              child: RichText(
                                text: TextSpan(
                                    children: model.backRichText(
                                        model.approveName, model.lightWords)),
                                overflow: TextOverflow.ellipsis,
                              ),
                            )),
                            const SizedBox(
                              width: 8,
                            ),
                            Offstage(
                              offstage: listType == 2,
                              child: Container(
                                // height: 20,
                                padding:
                                    const EdgeInsets.only(left: 5, right: 5),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(2),
                                    color: ColorConfig.whiteColor,
                                    border: Border.all(
                                        width: 1,
                                        color: model.source == 2 ||
                                                model.source == 3
                                            ? ColorConfig.themeCorlor
                                            : model.status == 1
                                                ? ColorConfig.friendColor
                                                : model.status == 2
                                                    ? ColorConfig.deleteCorlor
                                                    : model.status == 3
                                                        ? ColorConfig
                                                            .themeCorlor
                                                        : ColorConfig
                                                            .desTextColor)),
                                child: Text(
                                  model.statusName ?? '',
                                  style: TextStyle(
                                      fontSize: 12,
                                      color:
                                          model.source == 2 || model.source == 3
                                              ? ColorConfig.themeCorlor
                                              : model.approveStatusColor()),
                                ),
                              ),
                            )
                          ],
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          // height: 20,
                          child: Row(
                            children: [
                              Text(
                                BaseInfo().formatTimestamp(
                                    model.createTime!, 'yyyy/MM/dd HH:mm'),
                                style: const TextStyle(
                                    fontSize: 12,
                                    color: ColorConfig.desTextColor),
                              ),
                              8.gap,
                              model.source == 1
                                  ? 0.5.gap
                                  : model.source == 2 || model.source == 3
                                      ? Flexible(
                                          child: Container(
                                          padding: const EdgeInsets.only(
                                              left: 6, right: 6),
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(2),
                                              color: ColorConfig
                                                  .mybackgroundColor),
                                          child: Text(
                                            model.orgName,
                                            overflow: TextOverflow.ellipsis,
                                            style: const TextStyle(
                                                fontSize: 12,
                                                color: ColorConfig.themeCorlor),
                                          ),
                                        ))
                                      : 0.5.gap,
                              8.gap,
                              Offstage(
                                offstage: model.source == 1,
                                child: Container(
                                  height: 16,
                                  padding:
                                      const EdgeInsets.only(left: 6, right: 6),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(2),
                                      color: ColorConfig.mybackgroundColor),
                                  child: Text(
                                    model.source == 2
                                        ? '金蝶财务'
                                        : model.source == 3
                                            ? '金蝶HR'
                                            : '',
                                    style: const TextStyle(
                                        fontSize: 12,
                                        color: ColorConfig.themeCorlor),
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    )),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Column(
                children: _backApproveContent(model),
              ),
              Offstage(
                offstage: widget.argument['listType'] != 2 ||
                    model.source != 1 ||
                    model.type == 1,
                child: const Divider(
                  color: ColorConfig.lineColor,
                  //color: Colors.transparent,
                  height: 1,
                ),
              ),
              Offstage(
                  offstage: widget.argument['listType'] != 2 ||
                      model.source != 1 ||
                      model.type == 1,
                  child: InkWell(
                    onTap: () async {
                      if (listType != 2) {
                        model.notRead = 0;
                      }
                      var result = await Get.toNamed('/approve-detail',
                          arguments: {
                            'orgId': jumpType != 2
                                ? model.orgId
                                : widget.argument['orgId'],
                            'approveId': model.approveId
                          },
                          preventDuplicates: false);
                      if (result != null && result['withDraw'] != null) {
                        dataList.remove(model);
                        _refresh();
                      }
                    },
                    child: Container(
                      alignment: Alignment.center,
                      width: double.infinity,
                      height: 40,
                      child: const Text(
                        '去办理',
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.themeCorlor),
                      ),
                    ),
                  ))
            ],
          ))
        ],
      ),
    );
  }

  _backOldList(ApproveListModel model) {
    return [
      Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 10, right: 10),
        // height: 20,
        child: Text(
          model.oneApproveContent,
          overflow: TextOverflow.ellipsis,
          maxLines: model.source == 1 ? 1 : 6,
          style: TextStyle(
              fontSize: 14,
              color: ColorConfig.mainTextColor,
              height: model.source == 1 ? 1 : 2),
        ),
      ),
      const SizedBox(
        height: 10,
      ),
      Offstage(
        offstage: model.twoApproveContent.isEmpty,
        child: Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(left: 10, right: 10),
          // height: 20,
          child: Text(
            model.twoApproveContent,
            overflow: TextOverflow.ellipsis,
            style:
                const TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
          ),
        ),
      ),
      SizedBox(
        height: model.twoApproveContent.isEmpty ? 0 : 10,
      ),
      Offstage(
        offstage: model.threeApproveContent.isEmpty,
        child: Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(left: 10, right: 10),
          // height: 20,
          child: Text(
            model.threeApproveContent,
            overflow: TextOverflow.ellipsis,
            style:
                const TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
          ),
        ),
      ),
      SizedBox(
        height: model.threeApproveContent.isEmpty ? 0 : 10,
      )
    ];
  }

  //审批列表内容控件
  _backApproveContent(ApproveListModel model) {
    List<Widget> lists = [];
    List outList = model.briefContent;
    if (outList.isNotEmpty) {
      for (var i = 0; i < outList.length; i++) {
        List inList = outList[i];
        if (inList.isNotEmpty) {
          int type = 0; //0最多两行展示的长内容 1一行两个内容样式
          String leftStr = inList.first;
          String rightStr = '';
          if (inList.length > 1) {
            type = 1;
            rightStr = inList[1];
          }

          lists.add(Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 10, right: 10),
            margin: const EdgeInsets.only(bottom: 10),
            child: type == 0
                ? RichText(
                    text: TextSpan(
                        children:
                            model.backRichText(leftStr, model.lightWords)),
                    overflow: TextOverflow.ellipsis,
                    maxLines: model.source == 1 ? 2 : 6,
                  )
                : Row(
                    children: [
                      Expanded(
                          flex: 1,
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: RichText(
                              text: TextSpan(
                                  children: model.backRichText(
                                      leftStr, model.lightWords)),
                              overflow: TextOverflow.ellipsis,
                            ),
                          )),
                      10.gap,
                      Expanded(
                          flex: 1,
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: RichText(
                              text: TextSpan(
                                  children: model.backRichText(
                                      rightStr, model.lightWords)),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ))
                    ],
                  ),
          ));
        }
      }
      return lists;
    }
  }

  getAllChildOrgs() {
    DioUtil()
        .get('${ORGApi.GET_ALL_CHILDORGS}/$orgId', null, true, () {},
            isShowLoading: false)
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        List allList = [
          {'orgId': '0', 'name': '全部公司'}
        ];
        allList.addAll(data['data']);
        orgList = allList;
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getApproveListData(
      {bool isTextFieldSearch = false,
      bool isLoading = false,
      bool isResetPage = false}) {
    _getListPageWithAllOrgModel(
        isTextFieldSearch: isTextFieldSearch,
        isLoading: isLoading,
        isResetPage: isResetPage);
  }

  _composeStatusName() {
    if (dataList.isEmpty) return;
    String statusString = '';
    dataList.value.forEach((model) {
      if (listType != 2) {
        if (model.source == 2 || model.source == 3) {
          statusString = '金蝶审批';
          if (model.status == 100) {
            statusString = '金蝶处理中';
          }
        } else if (model.source == 1) {
          statusString = statusMap[model.status.toString()] ?? '';
        }
        if (!StringUtil.isEmpty(statusString)) {
          model.statusName = statusString;
        }
      }
    });
  }

  _getListPageWithAllOrgModel(
      {bool isTextFieldSearch = false,
      bool isLoading = false,
      bool isResetPage = false}) {
    // isTextFieldSearch 逻辑变更 当搜索内容置为空点搜索 重置搜索内容 isRestPage 点击审批后重新计算page
    // if (isTextFieldSearch && keyword.isEmpty && isSearch) {
    //   _resetInitState();

    //   if (listType != 2 && listType != 3) {
    //     if (screenOrgList.isNotEmpty) {
    //       Map orgMap = screenOrgList.first;
    //       screenOrgName.value = orgMap['name'];
    //       currentOrgId = orgMap['orgId'];
    //       currentCorgId = orgMap['corgId'];
    //     }
    //     currentStatus = 0;
    //     _dealApproveString();
    //   }
    // }
    if (isSearch && keyword.isEmpty) {
      //搜索模式必须有搜索内容
      dataList.clear();
      _refresh();
      refreshController.refreshCompleted();
      return;
    }
    if (page == 1) {
      isLastData = false;
    }

    if (isResetPage && listType == 2) {
      int currentSize = dataList.length ~/ maxPageSize;
      page = currentSize + 1;
    }

    String url = ApproveApi.APPROVELIST_SEARCH;
    if (!isSearch) {
      keyword = '';
      url = ApproveApi.APPROVELIST_NEW;
    }
    if (jumpType == 2) {
      currentOrgGroupId = null;
      currentOrgId = widget.argument['orgId'];
      if (corgId != '0') {
        currentCorgId = corgId;
      } else {
        currentCorgId = null;
      }
      currentGroupName = null;
    }
    if (currentOrgGroupId != null) {
      if (currentOrgGroupId!.isEmpty) {
        currentOrgGroupId = null;
      }
    }
    if (currentOrgId != null) {
      if (currentOrgId!.isEmpty) {
        currentOrgId = null;
      }
    }
    if (currentCorgId != null) {
      if (currentCorgId!.isEmpty) {
        currentCorgId = null;
      }
    }
    if (listType == 1 || listType == 4 || listType == 5) {
      if (currentOrgId == null) {
        return;
      }
    }
    Map param = {
      'keyword': keyword,
      'listType': listType,
      'orgId': currentOrgId,
      'corgId': currentCorgId,
      'orgGroupId': currentOrgGroupId,
      'groupName': currentGroupName,
      'modelName': currentModelName,
      'notRead': notRead,
      'page': page,
      'length': maxPageSize,
    };
    param.removeWhere((key, value) => value == null);
    if (listType == 4 && !isSearch) {
      param.addAll({'status': currentStatus});
    }

    if (listCancelToken != null) {
      listCancelToken!.cancel();
    }
    listCancelToken = CancelToken();
    DioUtil().post(url, param, true, () {
      refreshController.refreshFailed();
      refreshController.loadFailed();
    }, isShowLoading: isLoading, cancelToken: listCancelToken).then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        Map dataMap = data['data'];
        if (dataMap == null) return;
        List userApproveItemList = dataMap['userApproveItemList'] ?? [];
        List lightWords = dataMap['lightWords'] ?? [];
        if (page == 1) {
          isLastData = false;
          dataList.clear();
          if (!isTextFieldSearch) {
            eventBus.fire({'refreshUnreadCount': 1});
          }
          if (!isResetPage) {
            if (listScrollController.hasClients) {
              listScrollController.jumpTo(0);
            }
          }
        }

        if (isResetPage && listType == 2) {
          List<ApproveListModel> dataTempList = [];
          for (var i = 0; i < maxPageSize * (page - 1); i++) {
            dataTempList.add(dataList[i]);
          }
          dataList.value = dataTempList;
        }
        for (var i = 0; i < userApproveItemList.length; i++) {
          Map<String, dynamic> approveDic = userApproveItemList[i];
          approveDic['lightWords'] = lightWords;
          dataList.add(ApproveListModel.fromJson(approveDic));
        }
        _dealNewData();
        refreshController.refreshCompleted();
        refreshController.loadComplete();
        if (userApproveItemList.length < maxPageSize) {
          //refreshController.loadNoData();
          isLastData = true;
        }

        //判断是否是 待办-模版筛选
        if (jumpType != 2 &&
            dataList.isEmpty &&
            page == 1 &&
            currentModelName != null &&
            listType == 2 &&
            !isSearch) {
          toast('$currentModelName审批已全部批完，请重新筛选');

          _resetInitState();
          getApproveListData();
        }
        _refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  _resetInitState() {
    // if (resetModel != null) {
    //   if (resetModel!.groupId!.isNotEmpty) {
    //     currentOrgGroupId = resetModel!.groupId;
    //     currentOrgId = null;
    //     currentCorgId = null;
    //     currentScreenName.value = resetModel!.name!;
    //   } else {
    //     currentOrgGroupId = null;
    //     currentOrgId = resetModel!.orgId;
    //     if (resetModel!.corgId!.isNotEmpty) {
    //       currentCorgId = resetModel!.corgId!;
    //     } else {
    //       currentCorgId = null;
    //     }

    //     currentScreenName.value = resetModel!.orgName!;
    //   }

    //   currentGroupName = null;
    //   currentModelName = null;
    // }

    currentOrgGroupId = null;
    currentOrgId = null;
    currentCorgId = null;
    currentGroupName = null;
    currentModelName = null;
    currentScreenName.value = '全部';
    _getAllScreenData();
  }

  //chooseType 0筛选模版内容 1筛选审批状态2公司筛选; 10全公司模式公司列表11全公司模式模版数据
  popComponent(context, chooseType) {
    if (jumpType == 2) {
      modelList = approveListController!.modellList;
      if (modelList.isEmpty) {
        toast('未获取到模版筛选数据，请稍后再试');
        return;
      }
    }
    if (chooseType == 11) {
      if (isRequestModel) {
        toast('未获取到模版筛选数据,请稍后再试');
        return;
      }
    }
    List dataList = [];
    if (chooseType == 0) {
      dataList = List.from(modelList);
    } else if (chooseType == 1) {
      dataList = List.from(statusList);
    } else if (chooseType == 2) {
      dataList = List.from(orgList);
    } else if (chooseType == 10) {
      dataList = List.from(screenOrgList);
    } else if (chooseType == 11) {
      dataList = List.from(templateList);
    }
    if (dataList.isEmpty) return;
    double height = DeviceUtils().height.value -
        DeviceUtils().top.value -
        44 -
        142 -
        DeviceUtils().bottom.value;
    double sizeHeight =
        height > 44 * dataList.length + 8 ? 44.0 * dataList.length + 8 : height;
    showPopupWindow(
      context,
      gravity: KumiPopupGravity.centerTop,
      bgColor: const Color(0x88000000),
      clickOutDismiss: true,
      clickBackDismiss: true,
      customAnimation: false,
      customPop: false,
      customPage: false,
      underStatusBar: false,
      underAppBar: true,
      offsetX: 10,
      offsetY: 142,
      duration: const Duration(milliseconds: 200),
      childSize: Size(screenWidth - 20, sizeHeight),
      childFun: (pop) {
        return Container(
          width: screenWidth - 20,
          height: sizeHeight,
          padding: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 7,
                offset: const Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              children: backChooseModelWidget(chooseType),
            ),
          ),
        );
      },
    );
  }

  backChooseModelWidget(int chooseType) {
    List<Widget> lists = [];
    List chooseDataList = [];
    String contentStr = '';
    if (chooseType == 0) {
      chooseDataList = List.from(modelList);
    } else if (chooseType == 1) {
      chooseDataList = List.from(statusList);
    } else if (chooseType == 2) {
      chooseDataList = List.from(orgList);
    } else if (chooseType == 10) {
      chooseDataList = List.from(screenOrgList);
    } else if (chooseType == 11) {
      chooseDataList = List.from(templateList);
    }
    for (var i = 0; i < chooseDataList.length; i++) {
      dynamic modelMap = chooseDataList[i];

      bool isCurrent = false; //是否是之前的选择
      int count = 0; //chooseType为10的未读数
      if (chooseType == 0) {
        contentStr = modelMap['modelName'];
        if (modelId == modelMap['modelId']) {
          isCurrent = true;
        }
      }
      if (chooseType == 1) {
        contentStr = modelMap['modelName'];
        if (currentStatus == modelMap['modelId']) {
          isCurrent = true;
        }
      }
      if (chooseType == 2) {
        contentStr = modelMap['name'];
        if (corgId == modelMap['orgId']) {
          isCurrent = true;
        }
      }
      if (chooseType == 10) {
        contentStr = modelMap['name'];
        count = modelMap['count'];
        if (currentOrgId == modelMap['orgId'] &&
            currentCorgId == modelMap['corgId']) {
          isCurrent = true;
        }
      }
      if (chooseType == 11) {
        contentStr = modelMap;
        if (i == 0 && currentModelName == null) {
          isCurrent = true;
        } else {
          if (currentModelName == modelMap) {
            isCurrent = true;
          }
        }
      }
      lists.add(InkWell(
        onTap: () {
          page = 1;
          if (chooseType == 0) {
            if (modelId != modelMap['modelId']) {
              modelId = modelMap['modelId'];
              currentModelName = modelMap['modelName'];
              if (modelId == '') {
                currentModelName = null;
              }
              getApproveListData();
            }
          }
          if (chooseType == 1) {
            if (currentStatus != modelMap['modelId']) {
              currentStatus = modelMap['modelId'];
              _dealApproveString();
              getApproveListData();
            }
          }
          if (chooseType == 2) {
            if (corgId != modelMap['orgId']) {
              corgId = modelMap['orgId'];
              getApproveListData();
            }
          }
          if (chooseType == 10) {
            if (currentOrgId != modelMap['orgId'] ||
                currentCorgId != modelMap['corgId']) {
              currentOrgId = modelMap['orgId'];
              currentCorgId = modelMap['corgId'];

              currentModelName = null;
              screenOrgName.value = modelMap['name'];
              getApproveListData();
              _getAllModelScreenData(currentOrgId!, currentCorgId!);
            }
          }
          if (chooseType == 11) {
            if (i == 0) {
              if (currentModelName != null) {
                currentModelName = null;
                _refresh();
                getApproveListData();
              }
            } else if (currentModelName != modelMap) {
              currentModelName = modelMap;
              _refresh();
              getApproveListData();
            }
          }

          Navigator.of(context).pop('');
        },
        child: Column(
          children: [
            Container(
              width: double.infinity,
              height: 36,
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: Container(
                padding: const EdgeInsets.only(left: 8, right: 8),
                height: double.infinity,
                alignment: Alignment.center,
                color: isCurrent
                    ? ColorConfig.mybackgroundColor
                    : ColorConfig.whiteColor,
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Text(
                      contentStr,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 15,
                          color: isCurrent
                              ? ColorConfig.themeCorlor
                              : ColorConfig.mainTextColor),
                    ),
                    if (!isSearch)
                      Positioned(
                          top: 0,
                          right: 0,
                          child: _badge(
                              Key('$listType-$i-$count'), count, Container()))
                  ],
                ),
              ),
            ),
            8.gap
          ],
        ),
      ));
    }
    return lists;
  }

  //审批同意或拒绝
  putApproveFlow(ApproveListModel model, int opinion) {
    //option 1同意 2拒绝

    if (opinion == 1 && model.needSignature == 1) {
      toast('请前往审批详情签名审批');
      return;
    }

    Map param = {
      'approveAssigneeId': model.approveAssigneeId,
      'approveId': model.approveId,
      'approveNodeId': model.approveNodeId,
      'orgId': orgId,
      'opinion': opinion,
      'content': '',
      'source': model.source
    };

    DioUtil().put(ApproveApi.APPROVEFLOW_V2, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        dataList.remove(model);
        _refresh();
        eventBus.fire({'refreshApproveListPage': 3});
        eventBus.fire({'refreshUnreadCount': 1});
        if (listType == 2 || listType == 3) {
          _getAllScreenData();
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //全标已读 抄送
  signAllRead() {
    if (jumpType == 2) {
      currentOrgId = widget.argument['orgId'];
      if (corgId != '0') {
        currentCorgId = corgId;
      } else {
        currentCorgId = currentOrgId;
      }
    }

    String url =
        '${ApproveApi.APPROVEALLSIGNREAD_v2}/orgid/$currentOrgId/corgid/$currentCorgId/listtype/5';
    DioUtil().delete(url, null, true, () {}).then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        Channel().invoke(Channel_sendIM_synchronous, {'handleId': '5'});
        for (var i = 0; i < dataList.length; i++) {
          ApproveListModel model = dataList[i];
          model.notRead = 0;
        }
        bool isWorkFlow = Get.isRegistered<WorkFlowController>();
        if (isWorkFlow) {
          WorkFlowController workFlowController = Get.find();
          workFlowController.getAllCompanyUntreated();
        }
        eventBus.fire({'refresh_list_red_point': '5'});
        eventBus.fire({'refreshUnreadCount': 1});
        _getAllOrgScreenData(isRefrshData: false);
        _refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  _isNeedApproveContent(int option) {
    if (_selectIsEmpty()) {
      toast('没有选中的待处理审批');
      return;
    }
    List msgList = [
      '处理成功',
      '已过滤选中中包含的手写签名和待办理审批',
      '已过滤选中中包含的待办理审批',
      '已过滤选中中包含的手写签名审批'
    ];
    int state = 0; //0全部为待审批1其中包含了待办理和需要签名的审批2只包含了待办理的审批3只包含了需签名的审批
    List canDealList = [];
    for (ApproveListModel selectModel in _getAllSelected()) {
      if (selectModel.needSignature == 1 && option == 1) {
        if (state == 2 || state == 1) {
          state = 1;
        } else {
          state = 3;
        }
      } else if (selectModel.type == 2) {
        if (state == 3 || state == 1) {
          state = 1;
        } else {
          state = 2;
        }
      } else {
        canDealList.add(selectModel);
      }
    }

    if (canDealList.isEmpty) {
      if (state > 0) {
        toast(msgList[state]);
        return;
      }
    }
    //是否需要审批意见 -- 拒绝需要
    if (option == 2) {
      ApproveDialog(2).show('', false, (content, isUse, fileId) {
        batchDealApprove(option, canDealList, msgList[state], content: content);
      });
    } else {
      batchDealApprove(
        option,
        canDealList,
        msgList[state],
      );
    }
  }

  //批量审批
  batchDealApprove(int option, List canDealList, String msg,
      {String content = ''}) {
    //1同意 2拒绝

    List batchDealInfoList = [];
    List batchTemp = [];
    for (ApproveListModel dealModel in canDealList) {
      batchDealInfoList.add({
        'approveAssigneeId': dealModel.approveAssigneeId,
        'approveId': dealModel.approveId,
        'approveNodeId': dealModel.approveNodeId,
        'source': dealModel.source
      });
      batchTemp.add(dealModel.approveAssigneeId);
    }
    batchList = batchTemp;

    Map param = {
      'batchDealInfoList': batchDealInfoList,
      'content': content,
      'opinion': option,
    };

    isNeedRefresh = false;

    Get.tagLoading(tag: ApproveApi.APPROVEBATCH_V2, text: '处理中...');
    DioUtil().post(ApproveApi.APPROVEBATCH_V2, param, true, () {
      isNeedRefresh = true;
      Get.tagDissmiss(tag: ApproveApi.APPROVEBATCH_V2);
    }, isShowLoading: false).then((data) {
      Get.tagDissmiss(tag: ApproveApi.APPROVEBATCH_V2);
      if (data == null) return;
      if (data!['code'] == 1) {
        Map infoData = data['data'];
        List sucessList = infoData['successList'];
        List failList = infoData['failList'];
        if (sucessList.isEmpty) {
          toast('批量审批失败');
        } else {
          List tempList = List.from(dataList);
          for (var i = 0; i < tempList.length; i++) {
            ApproveListModel totalModel = tempList[i];

            for (var j = 0; j < sucessList.length; j++) {
              String sucApproveId = sucessList[j];
              if (totalModel.approveId == sucApproveId) {
                totalModel.approveId = '';
              }
            }
          }
          dataList.clear();
          for (var i = 0; i < tempList.length; i++) {
            ApproveListModel totalModel = tempList[i];
            if (totalModel.approveId!.isNotEmpty) {
              dataList.add(totalModel);
            }
          }
        }
        isSelctAll.value = false;
        isShowBottom.value = false;

        if (sucessList.length != selectList.length) {
          toast('$msg (${sucessList.length}/${selectList.length})');
        }
        _clearSelectedModel();
        _refresh();

        timer = Timer(const Duration(milliseconds: 300), () {
          isNeedRefresh = true;
          getApproveListData(isResetPage: true);

          if (jumpType == 4 && !isSearch) {
            HomeController homeController = Get.find();
            homeController.hiddenBottomBar = !_selectIsEmpty();
            homeController.update();
          }
          if (jumpType != 2 && !isSearch) {
            bool isApproveHome = Get.isRegistered<ApproveHomeController>();
            if (isApproveHome) {
              ApproveHomeController homeController = Get.find();
              homeController
                  .settingCurrentCount('${_backSelectLength()}/$maxPageSize');
              homeController.settingSelectButton(false);
              homeController.settingCurrtenType(0);
            }
          }

          approveListController!.isShowCancel.value = !_selectIsEmpty();
          approveListController!.update();
          eventBus.fire({'refreshApproveListPage': 2, 'isSearch': isSearch});
          eventBus.fire({'refreshApproveListPage': 3});
          eventBus.fire({'refreshUnreadCount': 2});
          bool isWorkFlow = Get.isRegistered<WorkFlowController>();
          if (isWorkFlow) {
            WorkFlowController workFlowController = Get.find();
            workFlowController.getAllCompanyUntreated();
          }
          if (listType == 2 || listType == 3) {
            _getAllScreenData(mustRefresh: true);
          }
          _refresh();
        });
      } else {
        toast('${data['msg']}');
        isNeedRefresh = true;
      }
    });
  }

  //全部公司模式筛选数据
  _getAllScreenData({bool mustRefresh = false}) {
    if (listType != 2 && listType != 3) {
      return;
    }
    try {
      
    if (mustRefresh) {
      screenCancelToken = null;
    } else {
      if (screenCancelToken != null) {
        screenCancelToken!.cancel();
      }
      screenCancelToken = CancelToken();
    }

    DioUtil()
        .get('${ApproveApi.APPROVEPENDINGSCREENDATA}/$listType', null, true,
            () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: screenCancelToken)
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        Map dataMap = data['data'];
        List groupDataList = dataMap['orgGroupStatisticList'];
        List totalList = dataMap['allOrgGroupModelList'];
        List<Groups> allOrgGroupModelList = [];
        int totalCount = dataMap['count'];
        popup = dataMap['popup'];
        for (var i = 0; i < totalList.length; i++) {
          Map<String, dynamic> totalMap = totalList[i];
          Groups model = Groups.fromJson(totalMap);
          allOrgGroupModelList.add(model);
        }

        List allDataList = [];
        ApproveScreenModel allOrgScreenModel = ApproveScreenModel(
            groupId: '',
            name: '全部',
            allOrgGroupModelList: allOrgGroupModelList,
            listOrgApproveStatisticList: [],
            count: totalCount);
        allDataList.add(allOrgScreenModel);
        if (currentOrgGroupId == null && currentOrgId == null) {
          currentScreenName.value = '全部';
          currentGroups.value = allOrgScreenModel.allOrgGroupModelList!;
          _dealCurrentGroupData();
        }

        for (var i = 0; i < groupDataList.length; i++) {
          Map<String, dynamic> groupMap = groupDataList[i];
          ApproveScreenModel screenModel =
              ApproveScreenModel.fromJson(groupMap);
          if (screenModel.groupId!.isEmpty) {
            //除了各系的其他公司

            for (var j = 0;
                j < screenModel.listOrgApproveStatisticList!.length;
                j++) {
              ListOrgApproveStatisticList orgDataModel =
                  screenModel.listOrgApproveStatisticList![j];
              ApproveScreenModel orgScreenModel = ApproveScreenModel(
                  groupId: '',
                  name: orgDataModel.name,
                  listOrgApproveStatisticList: [],
                  allOrgGroupModelList: orgDataModel.groups,
                  count: orgDataModel.count);
              orgScreenModel.orgId = orgDataModel.orgId;
              orgScreenModel.corgId = orgDataModel.corgId;
              orgScreenModel.orgName = orgDataModel.name;
              allDataList.add(orgScreenModel);

              ApproveScreenModel oneModel = allDataList.first;
              List<ListOrgApproveStatisticList> allStatisList =
                  oneModel.listOrgApproveStatisticList!;
              allStatisList.add(orgDataModel);
              if (currentOrgGroupId == null && currentOrgId != null) {
                //当前为某公司数据
                if (currentOrgId == orgDataModel.orgId) {
                  if ((currentCorgId == null && orgDataModel.corgId!.isEmpty) ||
                      (currentCorgId != null &&
                          currentCorgId == orgDataModel.corgId)) {
                    currentGroups.value = orgDataModel.groups!;
                    _dealCurrentGroupData();
                  }
                }

                _refresh();
              }
              // if (resetModel == null && i == 0 && j == 0) {
              //   currentGroups.value = orgDataModel.groups!;
              //   _dealCurrentGroupData();
              //   resetModel = orgScreenModel;
              //   currentScreenName.value = orgScreenModel.orgName!;
              //   currentOrgId = orgScreenModel.orgId;
              //   if (orgScreenModel.corgId!.isEmpty) {
              //     currentCorgId = null;
              //   } else {
              //     currentCorgId = orgScreenModel.corgId;
              //   }
              //   getApproveListData();
              // }
            }
          } else {
            allDataList.add(screenModel);
            for (var j = 0;
                j < screenModel.listOrgApproveStatisticList!.length;
                j++) {
              ListOrgApproveStatisticList orgDataModel =
                  screenModel.listOrgApproveStatisticList![j];
              orgDataModel.groupId = screenModel.groupId;
              ApproveScreenModel oneModel = allDataList.first;
              List<ListOrgApproveStatisticList> allStatisList =
                  oneModel.listOrgApproveStatisticList!;
              allStatisList.add(orgDataModel);

              if ((currentOrgGroupId != null &&
                  currentOrgGroupId == screenModel.groupId &&
                  currentOrgId != null)) {
                if (currentOrgId == orgDataModel.orgId) {
                  if ((currentCorgId == null && orgDataModel.corgId!.isEmpty) ||
                      (currentCorgId != null &&
                          currentCorgId == orgDataModel.corgId)) {
                    currentGroups.value = orgDataModel.groups!;
                    _dealCurrentGroupData();
                  }
                }
              }
            }

            if ((currentOrgGroupId != null &&
                currentOrgId == null &&
                currentOrgGroupId == screenModel.groupId)) {
              currentGroups.value = screenModel.allOrgGroupModelList!;
              _dealCurrentGroupData();
            }
            // if (resetModel == null && i == 0) {
            //   currentGroups.value = screenModel.allOrgGroupModelList!;
            //   currentScreenName.value = screenModel.name!;
            //   resetModel = screenModel;
            //   currentOrgGroupId = screenModel.groupId;
            //   currentOrgId = null;
            //   currentCorgId = null;
            //   getApproveListData();
            //   _dealCurrentGroupData();
            // }
          }
        }
        approveSceenList.value = allDataList;
        if (popup == 1 && listType == 2) {
          HomeController homeController = Get.find();
          if (!homeController.isFinishMeeting) {
            return;
          }
          if (approveListController != null) {
            if (listType == approveListController!.currentListType.value) {
              if (!isAutherFirst) {
                isAutherFirst = true;
                _popScreenDialog(isPowerFirst: true);
              }
            }
          }
        }
      }
    });
    } catch(e) {
      //NotificationApproveHelper.dealApproveWaitInfo();
    }
    
  }

  //弹出筛选
  _popScreenDialog({bool isPowerFirst = false}) {
    //isPowerFirst 是否是有审批权限的人进入home页的弹出
    if (jumpType == 4 && NotificationTask.cachePayLoadModel != null && isPowerFirst) {
      //离线跳转 不弹弹框
      return;
    }
    if (jumpType == 2 || isSearch) return;
    ApproveScreenDialog().show(approveSceenList, listType == 2,
        (Map backMap, List<Groups> screenGroups) {
      currentOrgGroupId = backMap['groupId'];
      currentOrgId = backMap['orgId'];
      currentCorgId = backMap['corgId'];
      currentGroupName = backMap['groupName'];
      currentModelName = backMap['modelName'];
      currentScreenName.value = backMap['screenName'];
      currentGroups.value = screenGroups;
      _dealCurrentGroupData(isNeedScroll: true);

      page = 1;
      _clearSelectedModel();
      _getListPageWithAllOrgModel();
    },
        emptyStr: listType == 2
            ? '暂无待审批'
            : listType == 3
                ? '暂无已审批'
                : '');
   // NotificationApproveHelper.dealApproveWaitInfo();
  }

  //获取搜索模式公司数据
  _getSearchOrgData() {
    if (listType != 2 && listType != 3) {
      return;
    }
    DioUtil()
        .get('${ApproveApi.APPROVESEARCHORGDATA}/$listType', null, true, () {},
            isShowLoading: false, isShowErrorToast: false, isNeedRetry: true)
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        Map allData = data['data'];
        List orgGroupStatisticBriefList = allData['orgGroupStatisticBriefList'];
        searchOrgData.clear();
        for (var i = 0; i < orgGroupStatisticBriefList.length; i++) {
          Map<String, dynamic> groupMap = orgGroupStatisticBriefList[i];
          ApproveScreenModel model = ApproveScreenModel.fromJson(groupMap);
          if (model.groupId!.isNotEmpty) {
            List<ListOrgApproveStatisticList> list =
                model.listOrgApproveStatisticList!;
            ListOrgApproveStatisticList org = ListOrgApproveStatisticList();
            org.orgId = '';
            org.corgId = '';
            org.name = '全部公司';
            list.insert(0, org);
            for (var j = 0; j < list.length; j++) {
              ListOrgApproveStatisticList statistic = list[j];
              statistic.groupId = model.groupId;
            }
            model.listOrgApproveStatisticList = list;

            searchOrgData.add(model);
          } else {
            for (var j = 0;
                j < model.listOrgApproveStatisticList!.length;
                j++) {
              ListOrgApproveStatisticList orgModel =
                  model.listOrgApproveStatisticList![j];
              ApproveScreenModel screenModel = ApproveScreenModel();
              screenModel.groupId = '';
              screenModel.orgId = orgModel.orgId;
              screenModel.corgId = orgModel.corgId;
              screenModel.name = orgModel.name;
              searchOrgData.add(screenModel);
            }
          }
        }
        ApproveScreenModel allScreenModel = ApproveScreenModel();
        allScreenModel.groupId = '';
        allScreenModel.orgId = '';
        allScreenModel.corgId = '';
        allScreenModel.name = '全部';
        searchOrgData.insert(0, allScreenModel);
      }
    });
  }

  //获取全部公司列表(全公司模式)
  _getAllOrgScreenData({bool isRefrshData = true}) {
    DioUtil()
        .get('${ApproveApi.APPROVEALLORGDATA}/$listType', null, true, () {},
            isShowLoading: false, isShowErrorToast: false, isNeedRetry: true)
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        List orgData = data['data'];
        screenOrgList = orgData;

        if (isRefrshData) {
          if (orgData.isNotEmpty) {
            Map orgMap = orgData.first;
            screenOrgName.value = orgMap['name'];
            currentOrgId = orgMap['orgId'];
            currentCorgId = orgMap['corgId'];
            getApproveListData();
            _getAllModelScreenData(orgMap['orgId'], orgMap['corgId']);
          }
        }
      }
    });
  }

  //获取全部模版数据(全公司模式)
  _getAllModelScreenData(String clickOrgId, String clickCorgId) {
    isRequestModel = true;
    String url =
        '${ApproveApi.APPROVEMODELSCREENDATA}/$listType/$clickOrgId/$clickCorgId';
    DioUtil()
        .get(url, null, true, () {},
            isShowLoading: false, isShowErrorToast: false)
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        templateList = data['data'];
        templateList.insert(0, '全部审批');
        isRequestModel = false;
      }
    });
  }

  //修改当前审批状态(全公司模式)
  _changeKingdeeApproveStatus(String approveId,String? approveAssigneeId) async {
    var datasource = ApproveDataSource(retrofitDio, baseUrl: Host.HOST);
    var resp = await datasource.changeKingdeeStatus(approveId, approveAssigneeId);
    if (resp.success()) {
      _getAllSelected();
      _refresh();
      getApproveListData(isResetPage: true);
      eventBus.fire({'refreshApproveListPage': 3});
      eventBus.fire({'refreshUnreadCount': 1});
      _getAllScreenData();
    }
  }

  //处理currentGroup
  _dealCurrentGroupData({bool isNeedScroll = false}) {
    List modelnamesTemp = [];
    int needScrollIndex = 0;
    for (var i = 0; i < currentGroups.length; i++) {
      Groups groups = currentGroups[i];
      for (var j = 0; j < groups.models!.length; j++) {
        Models model = groups.models![j];
        model.groupName = groups.groupName!;
        modelnamesTemp.add(model);
        if (groups.groupName == currentGroupName &&
            model.modelName == currentModelName) {
          needScrollIndex = modelnamesTemp.length - 1;
        }
      }
    }
    modelNames = modelnamesTemp;
    _refresh();
    if (isNeedScroll) {
      Future.delayed(const Duration(milliseconds: 300)).then((value) {
        autoScrollController.scrollToIndex(needScrollIndex,
            preferPosition: AutoScrollPosition.begin);
      });
    }
  }

  _dealApproveString() {
    for (var element in statusList) {
      if (currentStatus == element['modelId']) {
        approveString = element['modelName'];
        break;
      }
    }
  }

  _badge(Key key, int count, Widget child) {
    var value = '';
    var shape = badge.BadgeShape.circle;
    if (count <= 0) {
      value = '';
    } else if (count > 99) {
      value = '99+';
      shape = badge.BadgeShape.square;
    } else {
      value = '$count';
      shape = badge.BadgeShape.circle;
    }
    double fontSize = 10;
    return Padding(
        padding: const EdgeInsets.all(0),
        child: badge.Badge(
          // shape: BadgeShape.circle,
          ignorePointer: false,
          badgeAnimation: const badge.BadgeAnimation.scale(
            animationDuration: Duration(seconds: 1),
            colorChangeAnimationDuration: Duration(seconds: 1),
            loopAnimation: false,
            curve: Curves.fastOutSlowIn,
            colorChangeAnimationCurve: Curves.easeInCubic,
          ),
          badgeStyle: badge.BadgeStyle(
            shape: shape,
            badgeColor: ColorConfig.deleteCorlor,
            padding: const EdgeInsets.all(1),
            borderRadius: BorderRadius.circular(12),
            // borderSide: BorderSide(color: ColorConfig.deleteCorlor, width: 1),
            elevation: 0,
          ),
          badgeContent: Container(
            padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
            key: key,
            constraints: const BoxConstraints(minWidth: 22),
            child: Center(
              child: Text(
                value,
                style: TextStyle(fontSize: fontSize, color: Colors.white),
              ),
            ),
          ),
          showBadge: count > 0,
          position: badge.BadgePosition.topEnd(top: -6, end: -20),
          child: child,
        ));
  }

  //无数据文字判断
  _backNoDataText() {
    if (isSearch) {
      if (keyword.isEmpty) {
        return '输入关键词查找审批';
      } else {
        return '暂无搜索结果';
      }
    } else {
      return '暂无数据';
    }
  }

  //选择是否为空
  _selectIsEmpty() {
    List selectedList = _getAllSelected();
    return selectedList.isEmpty;
  }

  _backSelectLength() {
    List selectedList = _getAllSelected();
    return selectedList.length;
  }

  _clearSelectedModel() {
    List selectedList = _getAllSelected();
    for (var i = 0; i < selectedList.length; i++) {
      ApproveListModel model = selectedList[i];
      model.isSelected = false;
    }
    selectList.clear();
    isSelctAll.value = false;
    isShowBottom.value = false;

    approveListController!.isShowCancel.value = false;
    approveListController!.update();
    //批量选择状态
    try {
      if (jumpType == 4) {
        HomeController homeController = Get.find();
        homeController.hiddenBottomBar = !_selectIsEmpty();
        homeController.update();
      } else {
        if (jumpType != 2) {
          ApproveHomeController homeController = Get.find();
          homeController.settingCurrtenType(0);
          homeController.settingCurrentCount('0/$maxPageSize');
        }
      }
    } catch (e) {}
  }

  _dealNewData() {
    if (selectList.isNotEmpty) {
      for (var i = 0; i < dataList.length; i++) {
        ApproveListModel dataModel = dataList[i];
        for (var j = 0; j < selectList.length; j++) {
          ApproveListModel selectedModel = selectList[j];
          if (dataModel.approveAssigneeId == selectedModel.approveAssigneeId) {
            dataModel.isSelected = true;
            break;
          }
        }
      }
    }
    _getAllSelected();
  }

  _getAllSelected() {
    selectList = dataList.where((model) {
      return model.isSelected;
    }).toList();
    return selectList;
  }

  _refresh() {
    _composeStatusName();
    dataList.refresh();
  }
}
