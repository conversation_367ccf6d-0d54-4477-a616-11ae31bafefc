import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';

class ApproveAdditionalController extends GetxController {

  TextEditingController msgController = TextEditingController();
  String companyId = '';
  String approveId = '';
  String approveNodeId = '';
  String approveAssigneeId = '';

  List postionContentList = [
    '前加审\n在我之前审批',
    '后加审\n加审后自动通过审批,并流转至被加审人',
    '并加审\n和我共同审批'
  ];
  List approveWayContentList = [
    '依次审批\n按选择审批人顺序依次审批通过',
    '会审\n需所有人通过',
    '或审\n一人通过即可'
  ];
  RxList approveWayList = [].obs; //1.串审 2.会审   审批方式
  RxList positionList = [].obs; //1.之前 2.之后 3.并加审  加审方式

  RxList memberList = [].obs; //选择的人员大于1人 出现审批方式，选择并加审隐藏审批方式
  RxInt currentApproveWay = 0.obs; //当前审批方式
  RxInt currentPosition = 0.obs; //当前加审方式

  bool isNeedSign = false;
  @override
  void onInit() {
    super.onInit();
    companyId = Get.arguments['companyId'];
    approveId = Get.arguments['approveId'];
    approveNodeId = Get.arguments['approveNodeId'];
    approveAssigneeId = Get.arguments['approveAssigneeId'];
    isNeedSign = Get.arguments['isNeedSign'];
  }

  @override
  void onReady() {
    super.onReady();
    verifyAdditional();
  }

  @override
  void onClose() {
    msgController.dispose();
    super.onClose();
  }

  //校验当前可选的加审方式
  verifyAdditional() {
    Map param = {
      'approveId': approveId,
      'approveAssigneeId': approveAssigneeId,
      'approveNodeId': approveNodeId
    };

    DioUtil().post(ApproveApi.APPROVEVERIFYADDITIONAL, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        approveWayList.value = data['data']['approveWay'];
        positionList.value = data['data']['position'];
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //确定加审
  sureAdditional(
      {String content = '', bool isUse = false, String fileId = ''}) {
    if (memberList.isEmpty) {
      toast('请选择加审人员');
      return;
    }
    if (currentPosition.value == 0) {
      toast('请选择加审方式');
      return;
    }
    if (memberList.length > 1 && currentPosition.value != 3) {
      if (currentApproveWay.value == 0) {
        toast('请选择审批方式');
        return;
      }
    }

    List addAssigneeList = [];
    for (var i = 0; i < memberList.length; i++) {
      MemberModel model = memberList[i];
      addAssigneeList.add(model.userId);
    }

    Map param = {
      'addAssigneeList': addAssigneeList,
      'approveId': approveId,
      'approveAssigneeId': approveAssigneeId,
      'approveNodeId': approveNodeId,
      'orgId': companyId,
      'approveWay': currentApproveWay.value,
      'position': currentPosition.value
    };
    if (currentPosition.value == 2) {
      //后加审默认同意此审批
      param['content'] = content;
      if (isNeedSign) {
        param['lastSignature'] = isUse ? 1 : 0;
        param['signatureFileId'] = fileId;
      }
    }

    DioUtil().post(ApproveApi.APPROVEADDITIONAL, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        toast('加审成功');
        eventBus.fire({'refreshUnreadCount': 1});
        eventBus.fire({'refreshApproveListPage': 2,'deleteId':approveAssigneeId});
        Get.back(result: {'refresh': 1});
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
