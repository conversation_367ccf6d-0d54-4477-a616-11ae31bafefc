import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';

import 'package:get/get.dart';

import '../../../contact/model/org/org_model.dart';
import '../../dialog/approve_dialog.dart';
import '../controllers/approve_additional_controller.dart';

class ApproveAdditionalView extends GetView<ApproveAdditionalController> {
  ApproveAdditionalView({Key? key}) : super(key: key);

  ApproveAdditionalController approveAdditionalController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: approveAdditionalController,
        builder: (logic) {
          String potionStr = '请选择';
          if (approveAdditionalController.currentPosition.value > 0) {
            String textSpan = approveAdditionalController.postionContentList[
                approveAdditionalController.currentPosition.value - 1];
            List textList = textSpan.split('\n');
            potionStr = textList.first;
          }
          String approveWayStr = '请选择';
          if (approveAdditionalController.currentApproveWay.value > 0) {
            String textSpan = approveAdditionalController.approveWayContentList[
                approveAdditionalController.currentApproveWay.value - 1];
            List textList = textSpan.split('\n');
            approveWayStr = textList.first;
          }

          return Scaffold(
              backgroundColor: ColorConfig.backgroundColor,
              extendBodyBehindAppBar: true,
              appBar: TitleBar().backAppbar(context, '加审', false, [],
                  onPressed: () {
                Get.back(result: 0);
              }),
              body: ListView(
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  InkWell(
                    onTap: () async {
                      var result = await Get.toNamed('/choose-all-org-members',
                          arguments: {
                            'model':
                                OrgModel(approveAdditionalController.companyId),
                            'type': 1,
                            'chooseType': 2,
                            'selectList':
                                approveAdditionalController.memberList.value
                          });
                      if (result != null && result['list'] != null) {
                        approveAdditionalController.memberList.value =
                            result['list'];
                        approveAdditionalController.update();
                      }
                    },
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: double.infinity,
                      height: 56,
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                              height: double.infinity,
                              alignment: Alignment.centerLeft,
                              child: RichText(
                                  maxLines: 2,
                                  text: const TextSpan(
                                      text: '加审人员',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.mainTextColor),
                                      children: [
                                        TextSpan(
                                            text: '*',
                                            style: TextStyle(
                                                fontSize: 14,
                                                color:
                                                    ColorConfig.deleteCorlor))
                                      ]))),
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: Image.asset(
                                'assets/images/3.0x/workflow_function_add.png'),
                          )
                        ],
                      ),
                    ),
                  ),
                  Column(
                    children: backUserList(),
                  ),
                  const Divider(
                    indent: 15,
                    color: ColorConfig.backgroundColor,
                    height: 1,
                  ),
                  InkWell(
                    onTap: () {
                      List contentList = [];

                      for (var j = 0;
                          j < approveAdditionalController.positionList.length;
                          j++) {
                        int status =
                            approveAdditionalController.positionList[j];
                        if (status > 0) {
                          contentList.add(approveAdditionalController
                              .postionContentList[status - 1]);
                        }
                      }
                      SettingWidget().showCupertinoActionSheetForPage(
                          context, contentList, (value) {
                        int statusValue =
                            approveAdditionalController.positionList[value];
                        approveAdditionalController.currentPosition.value =
                            statusValue;
                        approveAdditionalController.update();
                      });
                    },
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: double.infinity,
                      height: 56,
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      child: Row(
                        children: [
                          Container(
                              width: 100,
                              height: double.infinity,
                              alignment: Alignment.centerLeft,
                              child: RichText(
                                  maxLines: 2,
                                  text: const TextSpan(
                                      text: '加审方式',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.mainTextColor),
                                      children: [
                                        TextSpan(
                                            text: '*',
                                            style: TextStyle(
                                                fontSize: 14,
                                                color:
                                                    ColorConfig.deleteCorlor))
                                      ]))),
                          Expanded(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                alignment: Alignment.centerRight,
                                child: Text(
                                  potionStr,
                                  maxLines: 2,
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                ),
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              SizedBox(
                                width: 9,
                                height: 17,
                                child: Image.asset(
                                    'assets/images/3.0x/mine_right.png'),
                              )
                            ],
                          ))
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Offstage(
                    offstage: approveAdditionalController.memberList.length <
                            2 ||
                        approveAdditionalController.currentPosition.value == 3,
                    child: InkWell(
                      onTap: () {
                        List contentList = [];

                        for (var j = 0;
                            j <
                                approveAdditionalController
                                    .approveWayList.length;
                            j++) {
                          int status =
                              approveAdditionalController.approveWayList[j];
                          if (status > 0) {
                            contentList.add(approveAdditionalController
                                .approveWayContentList[status - 1]);
                          }
                        }
                        SettingWidget().showCupertinoActionSheetForPage(
                            context, contentList, (value) {
                          int statusValue =
                              approveAdditionalController.approveWayList[value];
                          approveAdditionalController.currentApproveWay.value =
                              statusValue;
                          approveAdditionalController.update();
                        });
                      },
                      child: Container(
                        color: ColorConfig.whiteColor,
                        width: double.infinity,
                        height: 56,
                        padding: const EdgeInsets.only(left: 16, right: 16),
                        child: Row(
                          children: [
                            Container(
                                width: 100,
                                height: double.infinity,
                                alignment: Alignment.centerLeft,
                                child: RichText(
                                    maxLines: 2,
                                    text: const TextSpan(
                                        text: '审批方式',
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: ColorConfig.mainTextColor),
                                        children: [
                                          TextSpan(
                                              text: '*',
                                              style: TextStyle(
                                                  fontSize: 14,
                                                  color:
                                                      ColorConfig.deleteCorlor))
                                        ]))),
                            Expanded(
                                child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Container(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    approveWayStr,
                                    maxLines: 2,
                                    style: const TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.desTextColor),
                                  ),
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                SizedBox(
                                  width: 9,
                                  height: 17,
                                  child: Image.asset(
                                      'assets/images/3.0x/mine_right.png'),
                                )
                              ],
                            ))
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    height: 44,
                    child: CupertinoButton(
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                        color: ColorConfig.themeCorlor,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)),
                        pressedOpacity: 0.5,
                        child: const Text(
                          '加审',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        onPressed: () {
                          if (approveAdditionalController
                                  .currentPosition.value ==
                              2) {
                            ApproveDialog(1).show(
                                approveAdditionalController.approveId,
                                approveAdditionalController.isNeedSign,
                                (content, isUse, fileId) {
                              approveAdditionalController.sureAdditional(
                                  content: content,
                                  isUse: isUse,
                                  fileId: fileId);
                            });
                          } else {
                            approveAdditionalController.sureAdditional();
                          }
                        }),
                  )
                ],
              ));
        });
  }

  backUserList() {
    List<Widget> lists = [];
    for (var i = 0; i < approveAdditionalController.memberList.length; i++) {
      MemberModel model = approveAdditionalController.memberList[i];
      lists.add(Column(
        children: [
          Container(
            color: ColorConfig.whiteColor,
            width: double.infinity,
            height: 56,
            padding: const EdgeInsets.only(left: 15, right: 15),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      image: DecorationImage(
                          image: SettingWidget.backImageProvider(model.headimg))),
                ),
                const SizedBox(
                  width: 8,
                ),
                Expanded(
                    child: Container(
                  child: Text(
                    model.name,
                    style: const TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                )),
                InkWell(
                  onTap: () {
                    approveAdditionalController.memberList.removeAt(i);
                    approveAdditionalController.update();
                  },
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: Image.asset('assets/images/3.0x/approve_delete.png'),
                  ),
                )
              ],
            ),
          ),
          const Divider(
            indent: 15,
            height: 1,
            color: ColorConfig.lineColor,
          )
        ],
      ));
    }

    return lists;
  }
}
