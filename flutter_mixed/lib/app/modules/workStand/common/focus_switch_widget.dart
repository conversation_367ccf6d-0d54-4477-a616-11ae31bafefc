import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../main.dart';


// 切换焦点和控制软键盘等
class FocusSwitchBar extends StatelessWidget {

  bool? showBar;
  VoidCallback? preFocus;
  VoidCallback? nextFocus;

  FocusSwitchBar({super.key , this.showBar , this.preFocus , this.nextFocus});

  @override
  Widget build(BuildContext context) {
    if(showBar == false) return Container();
    return Row(
      children: [
        MaterialButton(
          onPressed: () => preFocus?.call(),
          child: const Text(
            '上一项',
            style: TextStyle(fontSize: 13, color: Colors.grey),
          ),
        ),
        2.gap,
        MaterialButton(
          onPressed: () => nextFocus?.call(),
          child: const Text(
            '下一项',
            style: TextStyle(fontSize: 13, color: Colors.grey),
          ),
        ),
        Expanded(child: Container()),
        MaterialButton(
          onPressed: () {
            hideKeyboard(Get.context!);
          },
          child: const Text(
            '完成',
            style: TextStyle(fontSize: 13, color: Colors.grey),
          ),
        ),
      ],
    );
  }
}


void hideKeyboard(BuildContext context) {
  FocusScopeNode currentFocus = FocusScope.of(context);
  if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
    FocusManager.instance.primaryFocus!.unfocus();
  }
}