import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/dialog/datepicker-dialog.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';

import '../controllers/meeting_reserve_controller.dart';

class MeetingReserveView extends GetView<MeetingReserveController> {
  MeetingReserveView({Key? key}) : super(key: key);
  final meetingReserveController = Get.find<MeetingReserveController>();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: meetingReserveController,
        builder: (logic) {
          return Scaffold(
            backgroundColor: ColorConfig.backgroundColor,
            appBar: TitleBar().backAppbar(context, '预约会议', false, [
              Container(
                width: 60,
                child: CupertinoButton(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    pressedOpacity: 0.5,
                    child: const Text(
                      '完成',
                      style: TextStyle(
                          color: ColorConfig.themeCorlor, fontSize: 16),
                    ),
                    onPressed: () {
                      meetingReserveController.reserveMeeting();
                    }),
              )
            ], onPressed: () {
              Get.back();
            }),
            body: Column(
              children: [
                10.gap,
                Container(
                  alignment: Alignment.centerLeft,
                  color: ColorConfig.whiteColor,
                  height: 52,
                  padding: EdgeInsets.only(left: 16, right: 16),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: Image.asset(
                            'assets/images/3.0x/meeting_reverve_topic.png'),
                      ),
                      6.gap,
                      Expanded(
                          child: Container(
                        child: TextField(
                          onChanged: (value) {},
                          // inputFormatters: [
                          //   LengthLimitingTextInputFormatter(10)
                          // ],
                          textInputAction: TextInputAction.done,
                          controller: meetingReserveController.topicController,
                          style: const TextStyle(
                            color: ColorConfig.mainTextColor,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsets.only(top: 0, bottom: 0),
                              border: const OutlineInputBorder(
                                  borderSide: BorderSide.none),
                              hintText: '请输入会议主题',
                              hintStyle: const TextStyle(
                                color: ColorConfig.desTextColor,
                                fontSize: 16,
                              )),
                        ),
                      ))
                    ],
                  ),
                ),
                10.gap,
                InkWell(
                  onTap: () {
                    DateTime? currentDate;
                    DateTime? maxDate;

                    DateTime minDate = DateTime.fromMillisecondsSinceEpoch(
                          BaseInfo().getMeetingTime());
                    if (meetingReserveController.createTime == 0) {
                      currentDate = minDate;
                    } else {
                      currentDate = DateTime.fromMillisecondsSinceEpoch(
                          meetingReserveController.createTime);
                    }

                    if (meetingReserveController.finishTime != 0) {
                      maxDate = DateTime.fromMillisecondsSinceEpoch(
                          meetingReserveController.finishTime - 15 * 60 * 1000);
                    }

                    DDDatePickerDialog(currentDate).show((time) {
                      int timeInt = int.parse(time);
                      meetingReserveController.createTime = timeInt;
                      meetingReserveController.startTimeStr =
                          BaseInfo().formatTimestamp(timeInt, 'yyyy-MM-dd HH:mm');
                      meetingReserveController.update();
                    }, 0, isMeeting: true, minDate: minDate, maxDate: maxDate);
                  },
                  child: Container(
                    height: 52,
                    padding: EdgeInsets.only(left: 16, right: 16),
                    color: ColorConfig.whiteColor,
                    child: Row(
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '开始时间',
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.mainTextColor),
                          ),
                        ),
                        Expanded(
                            child: Container(
                          alignment: Alignment.centerRight,
                          child: Text(
                            meetingReserveController.startTimeStr,
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.mainTextColor),
                          ),
                        )),
                        10.gap,
                        SizedBox(
                          width: 9,
                          height: 17,
                          child:
                              Image.asset('assets/images/3.0x/mine_right.png'),
                        )
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: meetingReserveController.createTime == 0,
                  child: Column(
                    children: [
                      Container(
                        color: ColorConfig.backgroundColor,
                        height: 1,
                        margin: EdgeInsets.only(left: 16, right: 16),
                      ),
                      InkWell(
                        onTap: () {
                          DateTime minDate = DateTime.fromMillisecondsSinceEpoch(
                                meetingReserveController.createTime+15*60*1000);
                          DateTime? maxDate;
                          DateTime? currentDate;
                          if (meetingReserveController.finishTime == 0) {
                            currentDate = minDate;
                          } else {
                            currentDate = DateTime.fromMillisecondsSinceEpoch(
                                meetingReserveController.finishTime);
                          }

                          DDDatePickerDialog(currentDate).show((time) {
                            int timeInt = int.parse(time);
                            meetingReserveController.finishTime = timeInt;
                            meetingReserveController.endTimeStr = BaseInfo()
                                .formatTimestamp(timeInt, 'yyyy-MM-dd HH:mm');
                            meetingReserveController.update();
                          }, 0,
                              isMeeting: true,
                              minDate: minDate,
                              maxDate: maxDate);
                        },
                        child: Container(
                          height: 52,
                          padding: EdgeInsets.only(left: 16, right: 16),
                          color: ColorConfig.whiteColor,
                          child: Row(
                            children: [
                              Container(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  '结束时间',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                              ),
                              Expanded(
                                  child: Container(
                                alignment: Alignment.centerRight,
                                child: Text(meetingReserveController.endTimeStr,style: TextStyle(fontSize: 14,color: ColorConfig.mainTextColor),),
                              )),
                              10.gap,
                              SizedBox(
                                width: 9,
                                height: 17,
                                child: Image.asset(
                                    'assets/images/3.0x/mine_right.png'),
                              )
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                10.gap,
                Container(
                  color: ColorConfig.whiteColor,
                  height: 52,
                  padding: EdgeInsets.only(left: 16, right: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          '会议密码',
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                        ),
                      ),
                      Container(
                          width: 80,
                          height: 44,
                          alignment: Alignment.centerRight,
                          child: CupertinoSwitch(
                              activeColor: ColorConfig.themeCorlor,
                              trackColor: ColorConfig.lineColor,
                              value: meetingReserveController.isSettingPwd,
                              onChanged: (value) {
                                meetingReserveController.isSettingPwd = value;
                                if (value) {
                                  meetingReserveController.pwdController.text =
                                      BaseInfo().getRandomStr();
                                }
                                meetingReserveController.update();
                              }))
                    ],
                  ),
                ),
                Offstage(
                  offstage: !meetingReserveController.isSettingPwd,
                  child: Column(
                    children: [
                      Container(
                        color: ColorConfig.backgroundColor,
                        height: 1,
                        margin: EdgeInsets.only(left: 16, right: 16),
                      ),
                      Container(
                        padding: EdgeInsets.only(left: 16, right: 16),
                        color: ColorConfig.whiteColor,
                        height: 52,
                        child: TextField(
                          onChanged: (value) {},
                          inputFormatters: [
                            LengthLimitingTextInputFormatter(6)
                          ],
                          textInputAction: TextInputAction.done,
                          controller: meetingReserveController.pwdController,
                          keyboardType: TextInputType.number,
                          style: const TextStyle(
                            color: ColorConfig.mainTextColor,
                            fontSize: 14,
                          ),
                          decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsets.only(top: 0, bottom: 0),
                              border: const OutlineInputBorder(
                                  borderSide: BorderSide.none),
                              hintText: '请输入会议密码',
                              hintStyle: const TextStyle(
                                color: ColorConfig.desTextColor,
                                fontSize: 16,
                              )),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  color: ColorConfig.backgroundColor,
                  height: 1,
                  margin: EdgeInsets.only(left: 16, right: 16),
                ),
                Container(
                  color: ColorConfig.whiteColor,
                  height: 52,
                  padding: EdgeInsets.only(left: 16, right: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          '成员入会静音',
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                        ),
                      ),
                      Container(
                          width: 80,
                          height: 44,
                          alignment: Alignment.centerRight,
                          child: CupertinoSwitch(
                              activeColor: ColorConfig.themeCorlor,
                              trackColor: ColorConfig.lineColor,
                              value: meetingReserveController.isCloseSound,
                              onChanged: (value) {
                                meetingReserveController.isCloseSound = value;
                                meetingReserveController.update();
                              }))
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
}
