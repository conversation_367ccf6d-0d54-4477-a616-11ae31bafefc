import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';

class MeetingReserveController extends GetxController {

  TextEditingController topicController = TextEditingController();
  TextEditingController pwdController = TextEditingController();
  int createTime = 0;
  int finishTime = 0;
  String startTimeStr = '';
  String endTimeStr = '';
  bool isSettingPwd = false;
  bool isCloseSound = false;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    topicController.dispose();
    pwdController.dispose();
  }

  //预约会议
  reserveMeeting() {
    if (topicController.text.isEmpty) {
      toast('请输入会议主题');
      return;
    }
    if (createTime == 0) {
      toast('请设置会议开始时间');
      return;
    }
    if (finishTime == 0) {
      toast('请设置会议结束时间');
      return;
    }
    if (isSettingPwd) {
      if (pwdController.text.length < 4 || pwdController.text.length > 6) {
        toast('请输入4~6位密码');
        return;
      }
    }
    Map param = {
      'topic': topicController.text,
      'createTime': createTime,
      'finishTime': finishTime,
      'sound': isCloseSound ? 1 : 0
    };
    if(isSettingPwd){
      param.addAll({'password':pwdController.text});
    }
    DioUtil().post(MeetingApi.MEETING_RESERVE, param, true, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        eventBus.fire({'refreshMeetingList':1});
        Get.back();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
