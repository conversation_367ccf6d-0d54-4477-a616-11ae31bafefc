import 'dart:async';
import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/dialog/msg-dialog.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';

class MeetingJoinController extends GetxController {

  RxInt type = 0.obs; //0加入会议 1输入密码 2将会议添加至会议列表 3快速会议
  TextEditingController meetingController = TextEditingController();
  bool isVoice = false;
  bool isVideo = false;
  bool isSpeaker = true;
  FocusNode node = FocusNode();
  String meetingId = '';
  bool isReceiveMeetingDeleted = false;
  late StreamSubscription subscription;
  @override
  void onInit() {
    super.onInit();
    type.value = Get.arguments['type'];
    if (Get.arguments['isVoice'] != null) {
      isVoice = Get.arguments['isVoice'];
    }
    if (Get.arguments['isVideo'] != null) {
      isVideo = Get.arguments['isVideo'];
    }
    if (Get.arguments['isSpeaker'] != null) {
      isSpeaker = Get.arguments['isSpeaker'];
    }
    if (Get.arguments['meetingId'] != null) {
      meetingId = Get.arguments['meetingId'];
    }
    node.requestFocus();
    subscription = eventBus.on<Map>().listen((event) {
      if (event['receiveMeetingDeleted'] != null) {
        if (isReceiveMeetingDeleted) return;
        if (!isReceiveMeetingDeleted) {
          isReceiveMeetingDeleted = true;
        }
        String deleteId = event['receiveMeetingDeleted'];
        if (deleteId == meetingId) {
          Get.back();
        }
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (type.value == 3) {
      getLoginName();
    }
  }

  @override
  void onClose() {
    super.onClose();
    meetingController.dispose();
    subscription.cancel();
  }

  getLoginName() async {
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    String myUserName = tokenInfo['name'];
    meetingController.text = '$myUserName的快速会议';
    update();
  }

  checkMeetingIsHavePwd() async {
    meetingId = meetingController.text;
    node.unfocus();
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    Map param = {
      'meetingId': meetingController.text,
      'userId': tokenInfo['userId']
    };
    DioUtil().post(MeetingApi.MEETING_CHECK, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        int dataCode = data['data'];
        // MEETING_EXISTENCE(101,"会议存在"),
// MEETING_NOT_EXISTENCE(102,"会议不存在"),
// MEETING_PASSWORD(103,"会议存在密码"),
// MEETING_PASSWORD_NOT(104,"会议不存在密码"),
// MEETING_ENDED(105,"会议已结束")    MEETING_PASSWORD_NOT_NEED(106,"是主持人进入会议不需要密码" ),),

        if (dataCode == 102) {
          toast('会议不存在');
        } else if (dataCode == 103) {
          Get.toNamed(Routes.MEETING_JOIN,
              arguments: {
                'type': 1,
                'isVoice': isVoice,
                'isVideo': isVideo,
                'isSpeaker': isSpeaker,
                'meetingId': meetingController.text,
              },
              preventDuplicates: false);
        } else if (dataCode == 104) {
          checkIsHaveUnFinishedMeeting();
        } else if (dataCode == 105) {
          toast('会议已结束');
        } else if (dataCode == 106) {
          checkIsHaveUnFinishedMeeting();
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  verifyMeetingPwd() async {
    node.unfocus();
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    Map param = {
      'meetingId': meetingId,
      'userId': tokenInfo['userId'],
      'password': meetingController.text
    };
    DioUtil()
        .post(MeetingApi.MEETING_VERIFY_PWD, param, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        if (type.value == 1) {
          checkIsHaveUnFinishedMeeting();
        } else {
          meetingAddList();
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  checkIsHaveUnFinishedMeeting() {
    DioUtil()
        .get('${MeetingApi.MEETING_REMOVE_USER}/$meetingId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        dynamic dataCode = data['data'];
        if (dataCode is int) {
          if (dataCode == 700) {
            MsgDiaLog('', '你有会议正在进行，如想进入新的会议，请先离开正在进行的会议', '确定', '', () {
              Navigator.of(Get.context!).pop();
            }, () {})
                .show();
          } else {
            getMeetingSign();
          }
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  getMeetingSign() {
    DioUtil()
        .get('${MeetingApi.GET_MEETING_SIGN}/$meetingId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataMap = data['data'];
        dataMap['isVoice'] = isVoice ? 1 : 0;
        dataMap['isVideo'] = isVideo ? 1 : 0;
        dataMap['isSpeaker'] = isSpeaker ? 1 : 0;
        Channel().invoke(Channel_jumpMeeting, dataMap);
        eventBus.fire({'refreshMeetingList': 1});
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //将会议添加到会议列表
  meetingAddList() {
    DioUtil()
        .post('${MeetingApi.MEETING_ADD_LIST}/$meetingId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Get.back(result: 1);
      } else {
        toast('${data['msg']}');
      }
    });
  }

//快速会议
  quickMeeting() {
    node.unfocus();
    Map param = {'topic': meetingController.text};
    DioUtil().post(MeetingApi.MEETING_QUICK, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        meetingId = data['data'];
        checkIsHaveUnFinishedMeeting();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
