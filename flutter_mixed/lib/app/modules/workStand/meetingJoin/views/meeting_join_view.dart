import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

import 'package:get/get.dart';

import '../../../../routes/app_pages.dart';
import '../controllers/meeting_join_controller.dart';

class MeetingJoinView extends GetView<MeetingJoinController> {
  MeetingJoinView({Key? key}) : super(key: key);
  final meetingJoinController = Get.find<MeetingJoinController>();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: meetingJoinController,
        builder: (logic) {
          return Scaffold(
            backgroundColor: ColorConfig.blackColor,
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(44),
                child: AppBar(
                  title: Text(
                    '',
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  centerTitle: true,
                  leading: IconButton(
                    onPressed: () {
                      Get.back();
                    },
                    icon: SizedBox(
                      width: 20,
                      height: 20,
                      child:
                          Image.asset('assets/images/3.0x/org_white_back.png'),
                    ),
                  ),
                  backgroundColor: Color(0x00FFFFFF),
                  elevation: 0,
                )),
            body: Column(
              children: [
                35.gap,
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    backTextfiledText(),
                    style:
                        TextStyle(fontSize: 14, color: ColorConfig.whiteColor),
                  ),
                ),
                16.gap,
                Container(
                  margin: EdgeInsets.only(left: 16, right: 16),
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 1, color: ColorConfig.whiteColor),
                      borderRadius: BorderRadius.circular(4)),
                  child: TextField(
                    onTap: () {},
                    onChanged: (value) {},
                    inputFormatters: [LengthLimitingTextInputFormatter(backMaxLength())],
                    keyboardType: meetingJoinController.type.value == 3
                        ? TextInputType.text
                        : TextInputType.phone,
                    focusNode: meetingJoinController.node,
                    textAlign: TextAlign.center,
                    controller: meetingJoinController.meetingController,
                    style: TextStyle(
                        color: ColorConfig.whiteColor,
                        fontSize: 17,
                        letterSpacing:
                            meetingJoinController.type.value == 3 ? 0 : 8),
                    decoration: InputDecoration(
                        contentPadding:
                            const EdgeInsets.only(top: 0, bottom: 0),
                        border: const OutlineInputBorder(
                            borderSide: BorderSide.none),
                        hintText: '',
                        hintStyle: const TextStyle(
                          color: ColorConfig.whiteColor,
                          fontSize: 17,
                        )),
                  ),
                ),
                150.gap,
                Offstage(
                  offstage: meetingJoinController.type.value != 0 &&
                      meetingJoinController.type.value != 3,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          meetingJoinController.isVoice =
                              !meetingJoinController.isVoice;
                          meetingJoinController.update();
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              color: Color(0x33FFFFFF)),
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: Image.asset(meetingJoinController.isVoice
                                ? 'assets/images/3.0x/meeting_join_voice.png'
                                : 'assets/images/3.0x/meeting_join_novoice.png'),
                          ),
                        ),
                      ),
                      24.gap,
                      GestureDetector(
                        onTap: () {
                          meetingJoinController.isVideo =
                              !meetingJoinController.isVideo;
                          meetingJoinController.update();
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              color: Color(0x33FFFFFF)),
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: Image.asset(meetingJoinController.isVideo
                                ? 'assets/images/3.0x/meeting_join_video.png'
                                : 'assets/images/3.0x/meeting_join_novideo.png'),
                          ),
                        ),
                      ),
                      24.gap,
                      GestureDetector(
                        onTap: () {
                          meetingJoinController.isSpeaker =
                              !meetingJoinController.isSpeaker;
                          meetingJoinController.update();
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              color: Color(0x33FFFFFF)),
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: Image.asset(meetingJoinController.isSpeaker
                                ? 'assets/images/3.0x/meeting_join_speaker.png'
                                : 'assets/images/3.0x/meeting_join_nospeaker.png'),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                // Offstage(
                //   offstage: meetingJoinController.type.value == 0,
                //   child: 48.gap,
                // ),
                meetingJoinController.type.value == 0 ||
                        meetingJoinController.type.value == 3
                    ? 0.gap
                    : 48.gap,
                16.gap,
                InkWell(
                  onTap: () {
                    if (meetingJoinController.type.value == 0) {
                      if (meetingJoinController
                          .meetingController.text.isEmpty) {
                        toast('请输入会议号');
                        return;
                      }
                      meetingJoinController.checkMeetingIsHavePwd();
                    }
                    if (meetingJoinController.type.value == 1 ||
                        meetingJoinController.type.value == 2) {
                      if (meetingJoinController
                          .meetingController.text.isEmpty) {
                        toast('请输入密码');
                        return;
                      }
                      meetingJoinController.verifyMeetingPwd();
                    }
                    if (meetingJoinController.type.value == 3) {
                      if (meetingJoinController
                          .meetingController.text.isEmpty) {
                        toast('请输入会议主题');
                        return;
                      }
                      meetingJoinController.quickMeeting();
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(left: 16, right: 16),
                    decoration: BoxDecoration(
                        color: ColorConfig.themeCorlor,
                        borderRadius: BorderRadius.circular(4)),
                    alignment: Alignment.center,
                    height: 44,
                    child: Text(
                      meetingJoinController.type.value == 2
                          ? '添加到会议列表'
                          : '加入会议',
                      style: TextStyle(
                          fontSize: 16, color: ColorConfig.whiteColor),
                    ),
                  ),
                )
              ],
            ),
          );
        });
  }

  backTextfiledText() {
    switch (meetingJoinController.type.value) {
      case 0:
        return '输入会议号加入会议';
      case 1:
        return '输入会议密码';
      case 2:
        return '输入会议密码';
      case 3:
        return '';
      default:
    }
  }

    backMaxLength() {
    switch (meetingJoinController.type.value) {
      case 0:
        return 9;
      case 1:
        return 6;
      case 2:
        return 6;
      case 3:
        return 100;
      default:
    }
  }
}
