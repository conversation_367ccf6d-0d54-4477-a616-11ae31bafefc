import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_detail_resp.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/widget/regimes_detail_head_view.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/widget/regimes_file_widget.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

import 'package:get/get.dart';

import '../controllers/regimes_detail_page_controller.dart';

class RegimesDetailPageView extends GetView<RegimesDetailPageController> {
  RegimesDetailPageView({super.key});
  RegimesDetailPageController regimesDetailPageController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        global: false,
        init: regimesDetailPageController,
        builder: (_) {
          return ToolBar(
            appBarBackgroundColor: ColorConfig.backgroundColor,
            title: '',
            body: regimesDetailPageController.detailResp == null
                ? Container()
                : SingleChildScrollView(
                    child: Column(
                    children: [
                      _backRegimesTitle(),
                      _backWhiteContainer(),
                    ],
                  )),
          );
        });
  }

  //规章制度主题
  _backRegimesTitle() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.fromLTRB(24, 8, 24, 8),
      child: Text(
        maxLines: null,
        regimesDetailPageController.detailResp?.name ?? '',
        style: const TextStyle(fontSize: 24, color: ColorConfig.mainTextColor,fontWeight: FontWeight.w500),
      ),
    );
  }

  //白色底部区域
  _backWhiteContainer() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: ColorConfig.whiteColor),
      child: Column(
        children: [
          RegimesDetailHeadView(
            resp: regimesDetailPageController.detailResp,
          ),
          _backContentWidget(),
          _backFileWidget(),
        ],
      ),
    );
  }

  //内容控件
  _backContentWidget() {
    if (StringUtil.isEmpty(regimesDetailPageController.detailResp?.content)) {
      return Container();
    }
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 24, 12, 24),
      child: HtmlWidget(regimesDetailPageController.detailResp!.content!),
    );
  }

  _backFileWidget() {
    if (regimesDetailPageController.detailResp?.fileVOS == null) {
      return Container();
    } else {
      if (regimesDetailPageController.detailResp!.fileVOS!.isEmpty) {
        return Container();
      }
    }
    return Column(
      children: [
        Container(
            color: ColorConfig.whiteColor,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            height: 36,
            child: Text(
              '${regimesDetailPageController.detailResp!.fileVOS!.length}个附件',
              style: const TextStyle(
                  fontSize: 12, color: ColorConfig.desTextColor),
            )),
        Column(
          children: _backFileList(),
        ),
        25.gap
      ],
    );
  }

  _backFileList() {
    List<Widget> lists = [];
    for (var i = 0;
        i < regimesDetailPageController.detailResp!.fileVOS!.length;
        i++) {
      RegimesFileVO? fileVO =
          regimesDetailPageController.detailResp!.fileVOS![i];
      lists.add(Column(
        children: [
          InkWell(
            onTap: () {
              regimesDetailPageController.tapFile(fileVO);
            },
            child: Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Container(
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: ColorConfig.backgroundColor),
                child: RegimesFileWidget(fileVO),
              ),
            ),
          ),
          8.gap
        ],
      ));
    }
    return lists;
  }
}
