import 'dart:io';

import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_detail_resp.dart';
import 'package:flutter_mixed/app/retrofit/datasource/rule_datasource.dart';
import 'package:flutter_mixed/app/utils/cos_helper.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

class RegimesDetailPageController extends GetxController {

  String ruleId = '';
  RegimesDetailResp? detailResp;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments['ruleId'] != null) {
      ruleId = Get.arguments['ruleId'];
    }
  }

  @override
  void onReady() {
    super.onReady();
    _getRuleInfo();
  }

  _getRuleInfo() async {
    try {
      Get.loading();
      var ruleDatasource = RuleDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await ruleDatasource.getRuleInfo(ruleId);
      Get.dismiss();
      logger('========resp======${resp.data?.toJson()}');
      if (resp.success()) {
        if (resp.data == null) return;
        detailResp = resp.data;
        update();
      } else {
        toast(resp.msg);
      }
    } catch (e) {
      logger('===detail==error===$e====');
       Get.dismiss();
    }
  }

  tapFile(RegimesFileVO? fileVO) async {
    if (fileVO == null ||
        StringUtil.isEmpty(fileVO.name) ||
        StringUtil.isEmpty(fileVO.oss)) {
      return;
    }

    List nameList = fileVO.name!.split('.');
    String ext = nameList.last;
    String endPath = '${fileVO.oss}.$ext';
    String docment = (await getApplicationDocumentsDirectory()).path;
    String savePath = '$docment/${Define.REGIMESLOADTPATH}/$endPath';
    File saveFile = File(savePath);
    if (saveFile.existsSync()) {
      CosHelper.openFile(savePath);
    } else {
      int maxSize = 200 * 1024 * 1024; //最大预览
      if (maxSize < (fileVO.size ?? 0)) {
        toast('文件过大,请到PC端预览');
        return;
      } else {
        Directory loadDirectory =
            Directory('$docment/${Define.REGIMESLOADTPATH}');
        if (!await loadDirectory.exists()) {
          await loadDirectory.create(recursive: true);
        }
        _downLoad(fileVO.oss!, savePath);
      }
    }
  }

  _downLoad(String fileId, String savePath) async {
    try {
      Get.loading();
      await CosManager().initPans();
      var bucket = await CosManager().bucket();
      if (bucket == null) return;
      await CosDownLoadUtil.downLoad(bucket, fileId, savePath, success: (complete){
        CosHelper.openFile(savePath);
      });
    } finally{
      Get.dismiss();
    }

  }

  @override
  void onClose() {
    super.onClose();
  }
}
