class RegimesListModel {
  String? parentId; //父节点id 一级菜单传'0'
  String? query; //模糊查询
  String? orgId; //公司id
  int? page;
  int? size;

  RegimesListModel(
      {this.parentId, this.query, this.orgId, this.page, this.size});

  RegimesListModel.fromJson(Map<String, dynamic> json) {
    parentId = json['parentId'] ?? '';
    query = json['query'] ?? '';
    orgId = json['orgId'] ?? '';
    page = json['page'] ?? 0;
    size = json['size'] ?? 0;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = Map<String, dynamic>();
    json['parentId'] = parentId;
    json['query'] = query;
    json['orgId'] = orgId;
    json['page'] = page;
    json['size'] = size;
    return json;
  }
}
