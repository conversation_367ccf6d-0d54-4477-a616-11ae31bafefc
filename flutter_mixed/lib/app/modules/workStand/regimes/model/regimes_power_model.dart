import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_org_model.dart';

class RegimesPowerModel {
  String? userId;
  String? orgId;
  int? power;
  List<RegimesOrgModel?>? orgVOS;

  RegimesPowerModel({this.userId, this.orgId, this.power});

  RegimesPowerModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'] ?? '';
    orgId = json['orgId'] ?? '';
    power = json['power'] ?? 0;
    if (json['orgVOS'] != null) {
      orgVOS = <RegimesOrgModel>[];
      json['orgVOS'].forEach((v) {
        orgVOS!.add(RegimesOrgModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = Map<String, dynamic>();
    json['userId'] = userId;
    json['orgId'] = orgId;
    json['power'] = power;
    json['orgVOS'] = orgVOS?.map((v) => v?.toJson()).toList();
    return json;
  }
}
