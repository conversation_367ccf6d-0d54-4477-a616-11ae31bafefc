class RegimesFileVO {
  String? ruleId;
  String? name;
  String? oss;
  int? size;

  RegimesFileVO({this.ruleId, this.name, this.oss,this.size});

  RegimesFileVO.fromJson(Map<String, dynamic> json) {
    ruleId = json['ruleId'] ?? '';
    name = json['name'] ?? '';
    oss = json['oss'] ?? '';
    size = json['size'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['ruleId'] = ruleId;
    data['name'] = name;
    data['oss'] = oss;
    data['size'] = size;
    return data;
  }
}

class RegimesOperatorVO {
  String? ruleId;
  String? userId;
  String? userName;

  RegimesOperatorVO({this.ruleId, this.userId, this.userName});

  RegimesOperatorVO.fromJson(Map<String, dynamic> json) {
    ruleId = json['ruleId'];
    userId = json['userId'];
    userName = json['userName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['ruleId'] = ruleId;
    data['userId'] = userId;
    data['userName'] = userName;
    return data;
  }
}

class RegimesDetailResp {
  int? activeEtime; //生效结束时间
  int? activeStime; //生效开始时间
  String? categorizeId; //分类id
  String? categorizeName; //分类名称
  String? content; //内容
  int? createTime; //创建时间
  String? creator; //创建者姓名
  String? creatorAvatar; //创建者头像
  String? creatorId; //创建者id
  String? delStatus; //删除状态 是否删除 0 未删除 1 已删除
  String? deptId; //部门id
  String? deptName; //部门名称
  int? download; //是否可以下载
  List<RegimesFileVO?>? fileVOS;
  String? id; //制度id
  String? name; //制度名称
  List<RegimesOperatorVO?>? operatorVOS; //操作人信息
  String? orgId; //公司id
  String? orgName; //公司名称
  int? publishTime; //发布时间
  String? recycleTime; //回收站剩余时间
  int? repealTime; //废止时间
  String? ruleNo; //编号
  String? scopeId; //范围id
  String? scopeName; //范围名称
  int? status; //状态 1 发布 2草稿箱 3回收站 4已删除 5废止

  RegimesDetailResp(
      {this.activeEtime,
      this.activeStime,
      this.categorizeId,
      this.categorizeName,
      this.content,
      this.createTime,
      this.creator,
      this.creatorAvatar,
      this.creatorId,
      this.delStatus,
      this.deptId,
      this.deptName,
      this.download,
      this.fileVOS,
      this.id,
      this.name,
      this.operatorVOS,
      this.orgId,
      this.orgName,
      this.publishTime,
      this.recycleTime,
      this.repealTime,
      this.ruleNo,
      this.scopeId,
      this.scopeName,
      this.status});

  RegimesDetailResp.fromJson(Map<String, dynamic> json) {
    activeEtime = json['activeEtime'];
    activeStime = json['activeStime'];
    categorizeId = json['categorizeId'];
    categorizeName = json['categorizeName'];
    content = json['content'];
    createTime = json['createTime'];
    creator = json['creator'];
    creatorAvatar = json['creatorAvatar'];
    creatorId = json['creatorId'];
    delStatus = json['delStatus'];
    deptId = json['deptId'];
    deptName = json['deptName'];
    download = json['download'];
    if (json['fileVOS'] != null) {
      fileVOS = <RegimesFileVO>[];
      json['fileVOS'].forEach((v) {
        fileVOS!.add(RegimesFileVO.fromJson(v));
      });
    }
    id = json['id'];
    name = json['name'];
    if (json['operatorVOS'] != null) {
      operatorVOS = <RegimesOperatorVO>[];
      json['operatorVOS'].forEach((v) {
        operatorVOS!.add(RegimesOperatorVO.fromJson(v));
      });
    }
    orgId = json['orgId'];
    orgName = json['orgName'];
    publishTime = json['publishTime'];
    recycleTime = json['recycleTime'];
    repealTime = json['repealTime'];
    ruleNo = json['ruleNo'];
    scopeId = json['scopeId'];
    scopeName = json['scopeName'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['activeEtime'] = activeEtime;
    data['activeStime'] = activeStime;
    data['categorizeId'] = categorizeId;
    data['categorizeName'] = categorizeName;
    data['content'] = content;
    data['createTime'] = createTime;
    data['creator'] = creator;
    data['creatorAvatar'] = creatorAvatar;
    data['creatorId'] = creatorId;
    data['delStatus'] = delStatus;
    data['deptId'] = deptId;
    data['deptName'] = deptName;
    data['download'] = download;
    data['fileVOS'] =
        fileVOS != null ? fileVOS!.map((v) => v?.toJson()).toList() : null;
    data['id'] = id;
    data['name'] = name;
    data['operatorVOS'] = operatorVOS != null
        ? operatorVOS!.map((v) => v?.toJson()).toList()
        : null;
    data['orgId'] = orgId;
    data['orgName'] = orgName;
    data['publishTime'] = publishTime;
    data['recycleTime'] = recycleTime;
    data['repealTime'] = repealTime;
    data['ruleNo'] = ruleNo;
    data['scopeId'] = scopeId;
    data['scopeName'] = scopeName;
    data['status'] = status;
    return data;
  }
}
