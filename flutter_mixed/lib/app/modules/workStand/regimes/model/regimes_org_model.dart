class RegimesOrgModel {
  String? orgId;
  String? name;
  String? logo;
  int? type;

  //自定义字段
  int status = 0;//0未选中，1选中

  RegimesOrgModel({this.orgId, this.name, this.logo});

  RegimesOrgModel.fromJson(Map<String, dynamic> json) {
    orgId = json['orgId'] ?? '';
    name = json['name'] ?? '';
    logo = json['logo'] ?? '';
    type = json['type'] ?? 0;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = Map<String, dynamic>();
    json['orgId'] = orgId;
    json['name'] = name;
    json['logo'] = logo;
    json['type'] = type;
    return json;
  }
}
