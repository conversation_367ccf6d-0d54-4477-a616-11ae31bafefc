class RegimesItemResp {
  String? ruleId; //分类代表parentId 制度代表ruleId
  String? name;
  String? type; //1分类 2制度


  RegimesItemResp(
      {this.ruleId, this.name, this.type});

  RegimesItemResp.fromJson(Map<String, dynamic> json) {
    ruleId = json['id'] ?? '';
    name = json['name'] ?? '';
    type = json['type'] ?? 0;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = Map<String, dynamic>();
    json['id'] = ruleId;
    json['name'] = name;
    json['type'] = type;
    return json;
  }
}