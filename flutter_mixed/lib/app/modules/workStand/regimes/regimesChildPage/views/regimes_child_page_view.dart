import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/smart_refresh.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_item_resp.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_keyword_model.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_list_model.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_org_model.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/widget/bread_crumb_navigation.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/widget/regimes_item.dart';
import 'package:flutter_mixed/app/retrofit/datasource/rule_datasource.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class RegimesChildPageView extends StatefulWidget {
  final argument;
  RegimesChildPageView({this.argument});

  @override
  RegimesChildPageViewState createState() => RegimesChildPageViewState();
}

class RegimesChildPageViewState extends State<RegimesChildPageView>
    with AutomaticKeepAliveClientMixin {
  List nameList = ['制度中心']; //面包屑数据
  bool isSearch = false; //是否为搜索模式
  int page = 1;
  int maxPageSize = 20;
  String parentId = '0'; //分类id
  String? keyword = ''; //搜索词
  bool isTop = true; //是否为搜索页面最上层级
  RegimesOrgModel? orgModel;
  String? orgId = '';
  List<RegimesItemResp?>? dataList = [];
  StreamSubscription? subscription; //搜索
  StreamSubscription? orgSubscription; //切换公司
  RefreshController? refreshController;
  ScrollController? listScrollController;

  @override
  void initState() {
    super.initState();
    logger('======widget===${widget.argument}');
    refreshController = RefreshController();
    listScrollController = ScrollController();
    isSearch = widget.argument['isSearch'] ?? false;
    orgModel = widget.argument['orgModel'];
    orgId = orgModel?.orgId;
    if (widget.argument['parentId'] != null) {
      parentId = widget.argument['parentId'];
    }
    if (widget.argument['nameList'] != null) {
      nameList = widget.argument['nameList'];
    }
    if (!isSearch) {
      _getRuleList();
      orgSubscription = eventBus.on<RegimesChangeOrgModel>().listen((event) {
        if (event.orgId?.isNotEmpty == true) {
          orgId = event.orgId;
          page = 1;
          _getRuleList();
        }
      });
    } else {
      if (nameList.length > 1) {
        _getRuleList();
      }
      subscription = eventBus.on<RegimesKeywordModel>().listen((event) {
        //收到了搜索指令 每次搜索回到第0页刷新
        keyword = event.keyword;
        if (nameList.length == 1) {
          //刷新
          if (StringUtil.isEmpty(keyword)) {
            setState(() {
              dataList = [];
            });
          } else {
            page = 1;
            keyword = event.keyword;
            _getRuleList();
          }
        } else {
          if (isTop) {
            //回到第0页
            _backToIndex(0);
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        _backBreadCrumbNavigation(),
        8.gap,
        Expanded(child: _backBody())
      ],
    );
  }

  //面包屑导航
  _backBreadCrumbNavigation() {
    if (nameList.length == 1) {
      return Container();
    }
    return Container(
      alignment: Alignment.centerLeft,
      width: double.infinity,
      height: 38,
      color: ColorConfig.whiteColor,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: BreadCrumbNavigation(nameList, (index) {
        _backToIndex(index);
      }),
    );
  }

  //导航至第x页
  _backToIndex(index) {
    if (isSearch) {
      Navigator.of(context).popUntil((route) {
        if (route.settings.arguments == null && index == 0) {
          return true;
        }
        var argument = route.settings.arguments as Map;
        List nameList = argument['nameList'];
        if (nameList.length == index + 1) {
          return true;
        } else {
          return false;
        }
      });
    } else {
      Get.until((route) {
        Map argument = route.settings.arguments as Map;
        if (argument['nameList'] == null && index == 0) {
          return true;
        }
        List nameList = argument['nameList'];
        if (nameList.length == index + 1) {
          return true;
        } else {
          return false;
        }
      });
    }
  }

  _backBody() {
    if (dataList?.isEmpty == true) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          color: ColorConfig.whiteColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))),
        child: const Text(
          '暂无数据',
          style: TextStyle(fontSize: 14, color: ColorConfig.msgTextColor),
        ),
      );
    }
    return Container(
      decoration: const BoxDecoration(
          color: ColorConfig.whiteColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))),
      child: SmartRefresher(
        controller: refreshController!,
        enablePullDown: true,
        enablePullUp: true,
        header: smartRefresherHeader(),
        footer: smartRefreshFooter(),
        onRefresh: () async {
          page = 1;
          _getRuleList();
        },
        onLoading: () async {
          page++;
          _getRuleList();
        },
        child: _backBodyWidget(),
      ),
    );
  }

  _backBodyWidget() {
    return MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: ListView.builder(
            controller: listScrollController,
            addAutomaticKeepAlives: false,
            addRepaintBoundaries: false,
            itemCount: dataList?.length,
            itemBuilder: (context, index) {
              RegimesItemResp? resp = dataList?[index];
              if (resp == null) {
                return Container();
              }
              return InkWell(
                onTap: () {
                  if (resp.type == '1') {
                    widget.argument['parentId'] = resp.ruleId;
                    List list = List.from(nameList);
                    if (resp.name == null) return;
                    list.add(resp.name);
                    widget.argument['nameList'] = list;
                    if (isSearch) {
                      isTop = false;
                      Navigator.of(context)
                          .pushNamed('/', arguments: widget.argument);
                    } else {
                      RouteHelper.route(Routes.REGIMES_MANAGE,
                          arguments: widget.argument);
                    }
                  } else if (resp.type == '2') {
                    RouteHelper.route(Routes.REGIMES_DETAIL_PAGE,
                        arguments: {'ruleId': resp.ruleId});
                  }
                },
                child: RegimesItem(
                  resp: resp,
                ),
              );
            }));
  }

  //获取列表数据
  _getRuleList() async {
    RegimesListModel model = RegimesListModel()
      ..orgId = orgId
      ..page = page
      ..size = maxPageSize;
    if (!StringUtil.isEmpty(keyword)) {
      model.query = keyword;
    }
    if (!isSearch || nameList.length > 1) {
      model.parentId = parentId;
    }
    logger('====model===${model.toJson()}');
    try {
      Get.loading();
      var ruleDatasource = RuleDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await ruleDatasource.getRuleList(model);
      Get.dismiss();
      if (resp.success()) {
        logger('====规章制度列表===success}');
        if (resp.data == null) {
          refreshController?.refreshFailed();
          refreshController?.loadFailed();
          return;
        }

        setState(() {
          if (page == 1) {
            dataList = resp.data ?? [];
            if (listScrollController!.hasClients) {
              listScrollController?.jumpTo(0);
            }
          } else {
            dataList!.addAll(resp.data!);
          }
          refreshController?.refreshCompleted();
          refreshController?.loadComplete();
        });
      }
    } catch (e) {
      Get.dismiss();
    }
  }

  @override
  void dispose() {
    super.dispose();
    refreshController?.dispose();
    listScrollController?.dispose();
    if (subscription != null) {
      subscription?.cancel();
    }
    if (orgSubscription != null) {
      orgSubscription?.cancel();
    }
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
