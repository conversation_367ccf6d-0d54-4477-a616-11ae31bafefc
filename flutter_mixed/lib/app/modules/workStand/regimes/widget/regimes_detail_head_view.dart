import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_detail_resp.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

class RegimesDetailHeadView extends StatefulWidget {
  RegimesDetailResp? resp;
  RegimesDetailHeadView({this.resp});
  @override
  RegimesDetailHeadViewState createState() => RegimesDetailHeadViewState();
}

class RegimesDetailHeadViewState extends State<RegimesDetailHeadView>
    with AutomaticKeepAliveClientMixin {
  bool isShow = false; //是否展示折叠部分 隐藏此功能
  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        _backNameWidget(),
        _backInfoWidget(),
        const Divider(
          indent: 0,
          color: ColorConfig.backgroundColor,
          height: 1,
        )
      ],
    );
  }

  _backNameWidget() {
    return InkWell(
      onTap: () {
        // setState(() {
        //   isShow = !isShow;
        // });
      },
      child: Container(
        width: double.infinity,
        height: 48,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            ImageLoader(
              url: widget.resp?.creatorAvatar ?? '',
              width: 20,
              height: 20,
              radius: 3,
            ),
            8.gap,
            Expanded(
                child: Row(
              children: [
                Flexible(
                    child: Container(
                  child: Text(
                    widget.resp?.creator ?? '',
                    style: const TextStyle(
                        fontSize: 12, color: ColorConfig.msgTextColor,fontWeight: FontWeight.w400),
                  ),
                )),
                8.gap,
                Container(
                  width: 75,
                  child: Text(
                    widget.resp?.publishTime != null
                        ? BaseInfo().formatTimestamp(
                            widget.resp!.publishTime!, 'yyyy/MM/dd')
                        : '',
                    style: const TextStyle(
                        fontSize: 12, color: ColorConfig.desTextColor,fontWeight: FontWeight.w400),
                  ),
                )
              ],
            )),
            Container(
              alignment: Alignment.center,
              width: 40,
              height: double.infinity,
              child: const Text(
                '',//isShow ? '收起' : '详情' 隐藏此功能
                style: TextStyle(
                    fontSize: 12, color: ColorConfig.themeCorlor),
              ),
            )
          ],
        ),
      ),
    );
  }

  _backInfoWidget() {
    // if (!isShow) {
    //   return Container();
    // }
    String timeStr = ''; //生效时间
    if (widget.resp?.activeStime != null && widget.resp?.activeEtime != null) {
      String startStr = BaseInfo().formatTimestamp(widget.resp!.activeStime!, 'yyyy/MM/dd');
      String endStr = BaseInfo().formatTimestamp(widget.resp!.activeEtime!, 'yyyy/MM/dd');
      if (widget.resp?.activeEtime == 33450336000000) {
        //3030年代表永久。。。
        endStr = '永久有效';
      }
      timeStr = '$startStr-$endStr';
    }
    return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
              decoration: BoxDecoration(
                  color: ColorConfig.backgroundColor,
                  borderRadius: BorderRadius.circular(4)),
              child: Column(
                children: [
                  _backSingleRowWidget('发布部门', widget.resp?.deptName ?? ''),
                  4.gap,
                  // _backSingleRowWidget(
                  //     '制度适用范围',
                  //     StringUtil.isEmpty(widget.resp?.scopeName)
                  //         ? '全组织'
                  //         : widget.resp!.scopeName!),
                  //4.gap,
                  _backSingleRowWidget(
                      '生效时间',
                      timeStr),
                  // 4.gap,
                  // _backSingleRowWidget('分类', widget.resp?.categorizeName ?? ''),
                ],
              ),
            ),
            16.gap
          ],
        ));
  }

  _backSingleRowWidget(String leftString, String rightStr) {
    return Container(
      height: 20,
      child: Row(
        children: [
          Container(
            width: 100,
            child: Text(
              leftString,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  fontSize: 12, color: ColorConfig.msgTextColor,fontWeight: FontWeight.w400),
            ),
          ),
          Expanded(
              child: Container(
            child: Text(
              rightStr,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  fontSize: 12, color: ColorConfig.mainTextColor,fontWeight: FontWeight.w400),
            ),
          ))
        ],
      ),
    );
  }
}
