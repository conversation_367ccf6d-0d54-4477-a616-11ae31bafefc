import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_org_model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

class RegimesChooseOrg extends StatefulWidget {
  final argument;
  Function chooseEnd;
  RegimesChooseOrg(this.argument, this.chooseEnd);
  @override
  RegimesChooseOrgState createState() => RegimesChooseOrgState();
}

class RegimesChooseOrgState extends State<RegimesChooseOrg>
    with AutomaticKeepAliveClientMixin {
  RxList<RegimesOrgModel?>? dataList = <RegimesOrgModel?>[].obs;
  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    dataList?.value = widget.argument['data'];
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Obx(() => Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(children: [
          GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(color: ColorConfig.maskColor),
          ),
          Positioned(
              top: 0,
              bottom: 0,
              left: 0,
              right: 68,
              child: GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  color: ColorConfig.backgroundColor,
                  child: Column(
                    children: [
                      (DeviceUtils().top.value + 44).gap,
                      _backOrgBodyWidget()
                    ],
                  ),
                ),
              ))
        ])));
  }

  _backOrgBodyWidget() {
    return MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: Expanded(
            child: ListView.builder(
                itemCount: dataList?.length ?? 0,
                itemBuilder: (context, index) {
                  if(dataList == null || dataList?.isEmpty == true) return Container();

                  RegimesOrgModel? model = dataList![index];
                  if(model == null) return Container();
                  return InkWell(
                    onTap: () {
                      widget.chooseEnd(model);
                      Get.back();
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 12 , right:  12 , bottom: 8),
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      width: double.infinity,
                      height: 48,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorConfig.whiteColor),
                      child: Row(
                        children: [
                          ImageLoader(
                            url: model?.logo,
                            width: 32,
                            height: 32,
                            radius: 6,
                          ),
                          8.gap,
                          Expanded(
                              child: Container(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  model?.name ?? '',
                                  maxLines: 2,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: model?.status == 1
                                          ? ColorConfig.themeCorlor
                                          : ColorConfig.mainTextColor),
                                ),
                              )),
                          8.gap,
                          Offstage(
                            offstage: model?.status != 1,
                            child: ImageLoader(
                              url: AssetsRes.REGIMES_ORG_CHECK,
                              width: 18,
                              height: 18,
                              radius: 0,
                            ),
                          )
                        ],
                      ),
                    ),
                  );
                })));
  }
}
