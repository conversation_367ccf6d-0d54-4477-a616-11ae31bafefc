import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_item_resp.dart';
import 'package:flutter_mixed/res/assets_res.dart';

class RegimesItem extends StatelessWidget {
  RegimesItemResp? resp;
  RegimesItem({this.resp});
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          height: 64,
          child: Row(
            children: [
              ImageLoader(
                url: resp?.type == '1'
                    ? AssetsRes.REGIMES_ICON_CATEGORY
                    : resp?.type == '2'
                        ? AssetsRes.REGIMES_ICON_FILE
                        : '',
                width: 32,
                height: 32,
                radius: 0,
              ),
              8.gap,
              Expanded(
                  child: Container(
                child: Text(
                  resp?.name ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              )),
              8.gap,
              const ImageLoader(
                url: AssetsRes.REGIMES_RIGHT_ARROW,
                width: 20,
                height: 20,
                radius: 0,
              )
            ],
          ),
        ),
        const Divider(
          indent: 56,
          color: ColorConfig.backgroundColor,
          height: 1,
        )
      ],
    );
  }
}
