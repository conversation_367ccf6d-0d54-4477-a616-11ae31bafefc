import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';

class BreadCrumbNavigation extends StatelessWidget {
  List nameList = [];
  Function onTap;
  BreadCrumbNavigation(this.nameList, this.onTap);
  @override
  // Widget build(BuildContext context) {
  //   return SingleChildScrollView(
  //     scrollDirection: Axis.horizontal,
  //     child: RichText(
  //         text: TextSpan(
  //             recognizer: TapGestureRecognizer()
  //               ..onTap = () {
  //                 onTap(0);
  //               },
  //             text: nameList[0],
  //             style: TextStyle(
  //                 fontSize: 14,
  //                 color: nameList.length == 1
  //                     ? ColorConfig.msgTextColor
  //                     : ColorConfig.themeCorlor),
  //             children: backTextSpan())),
  //   );
  // }

  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: nameList.length,
          itemBuilder: (context, index) {
            return _backItem(index);
          }),
    );
  }

  _backItem(int index) {
    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            child: InkWell(
              onTap: () {
                onTap(index);
              },
              child: Text(nameList[index],
                  style: TextStyle(
                      fontWeight: index == nameList.length - 1
                          ? FontWeight.w500
                          : FontWeight.w400,
                      fontSize: 14,
                      color: index == nameList.length - 1
                          ? ColorConfig.msgTextColor
                          : ColorConfig.themeCorlor)),
            ),
          ),
          4.gap,
          if (index != nameList.length - 1) ...[
            SizedBox(
              width: 16,
              height: 16,
              child: Image.asset(AssetsRes.REGIMES_BREAD_RIGHT),
            )
          ],
          4.gap
        ],
      ),
    );
  }

  List<TextSpan> backTextSpan() {
    List<TextSpan> list = [];
    for (var i = 0; i < nameList.length - 1; i++) {
      TextSpan textSpan = TextSpan(
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              if (i < nameList.length - 2) {
                onTap(i + 1);
              }
            },
          text: ' > ${nameList[i + 1]}',
          style: TextStyle(
              fontSize: 14,
              color: i == nameList.length - 2
                  ? ColorConfig.msgTextColor
                  : ColorConfig.themeCorlor));
      list.add(textSpan);
    }
    return list;
  }
}
