import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_detail_resp.dart';

class RegimesFileWidget extends StatelessWidget {
  RegimesFileVO? fileVO;
  RegimesFileWidget(this.fileVO);
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ImageLoader(
          url: BaseInfo().panFileType(fileVO?.name ?? ''),
          width: 40,
          height: 40,
          radius: 0,
        ),
        13.gap,
        Expanded(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              height: 22,
              child: Text(
                fileVO?.name ?? '',
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    fontSize: 14, color: ColorConfig.mainTextColor),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              height: 18,
              child: Text(
                BaseInfo().backSizeStr(fileVO?.size ?? 0),
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 12, color: ColorConfig.desTextColor),
              ),
            )
          ],
        ))
      ],
    );
  }
}
