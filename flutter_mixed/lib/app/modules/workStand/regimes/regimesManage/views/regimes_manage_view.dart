import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/regimesChildPage/views/regimes_child_page_view.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';

import '../controllers/regimes_manage_controller.dart';

class RegimesManageView extends GetView<RegimesManageController> {
  RegimesManageView({super.key});
  RegimesManageController regimesManageController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder<RegimesManageController>(
      global: false,
      init: regimesManageController,
      builder: (controller) {
        return Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          body: Column(
            children: [
              _backTopBarWidget(),
              _backSearchView(),
              _backTabContainer()
            ],
          ),
        );
      },
    );
  }

  //statusBar  navigationBar
  _backTopBarWidget() {
    if (!regimesManageController.isSearch &&
        regimesManageController.nameList?.length == 1) {
      return Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: ColorConfig.whiteColor,
          image: DecorationImage(
              image: AssetImage(AssetsRes.REGIMES_TITLE_BAR),
              fit: BoxFit.cover),
        ),
        child: Column(
          children: [(DeviceUtils().top.value).gap, _backOrgChooseWidget()],
        ),
      );
    } else {
      if (regimesManageController.isSearch) {
        return Container(
          color: ColorConfig.whiteColor,
          width: double.infinity,
          height: DeviceUtils().top.value,
        );
      } else {
        return Column(
          children: [
            Container(
              color: ColorConfig.whiteColor,
              width: double.infinity,
              height: DeviceUtils().top.value,
            ),
            _backNavigationBarWidget()
          ],
        );
      }
    }
  }

  //navigationBar
  _backNavigationBarWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      height: 44,
      color: ColorConfig.whiteColor,
      child: Row(
        children: [
          Expanded(
              child: GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(
                width: 44,
                height: 44,
                alignment: Alignment.centerLeft,
                child: navigationBackIcon),
          )),
          if ((regimesManageController.nameList ?? []).length > 1) ...[
            Expanded(
              flex: 2,
                child: Center(
              child: Text(
                regimesManageController.nameList!.last,
                style: const TextStyle(
                    fontSize: 16,
                    color: ColorConfig.mainTextColor,
                    fontWeight: FontWeight.w500),
              ),
            )),
            Spacer()
          ]
        ],
      ),
    );
  }

  _backOrgChooseWidget() {
    return GestureDetector(
      onTap: () {
        regimesManageController.tapOrgChoose();
      },
      child: Container(
        width: double.infinity,
        height: 44,
        padding: const EdgeInsets.only(left: 16, right: 6),
        child: Row(
          children: [
            Expanded(
                child: Row(
              children: [
                ImageLoader(
                  url: regimesManageController.currentModel?.logo ?? '',
                  width: 32,
                  height: 32,
                  radius: 6,
                ),
                8.gap,
                Expanded(
                    child: Row(
                  children: [
                    Flexible(
                        child: Container(
                      child: Text(
                        regimesManageController.currentModel?.name ?? '',
                        style: const TextStyle(
                            fontSize: 16,
                            color: ColorConfig.mainTextColor,
                            fontWeight: FontWeight.w500),
                      ),
                    )),
                    8.gap,
                    const ImageLoader(
                      url: AssetsRes.REGIMES_ORG_MENU,
                      width: 18,
                      height: 18,
                      radius: 0,
                    )
                  ],
                ))
              ],
            )),
            InkWell(
              onTap: () {
                Get.back();
              },
              child: Container(
                width: 44,
                height: 44,
                alignment: Alignment.center,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Image.asset(AssetsRes.REGIMES_CLOSE),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  //搜索控件
  _backSearchView() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      width: double.infinity,
      height: 50,
      color: ColorConfig.whiteColor,
      child: Row(
        children: [
          Expanded(
              child: Container(
                  //width: double.infinity,
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: ColorConfig.backgroundColor),
                  child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(
                          width: 12,
                        ),
                        SizedBox(
                          width: 19,
                          height: 19,
                          child:
                              Image.asset('assets/images/3.0x/org_search.png'),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Expanded(
                            child: Container(
                          alignment: Alignment.centerLeft,
                          child: TextField(
                            onSubmitted: (value) {
                              regimesManageController
                                  .tapSearchControllerSubmit();
                            },
                            onTap: () {
                              if (!regimesManageController.isSearch) {
                                regimesManageController.tapSearchController();
                              }
                            },
                            onChanged: (value) {
                              regimesManageController.update();
                            },
                            cursorColor: ColorConfig.themeCorlor,
                            focusNode: regimesManageController.node,
                            textInputAction: TextInputAction.search,
                            controller:
                                regimesManageController.searchController,
                            style: const TextStyle(
                              color: ColorConfig.mainTextColor,
                              fontSize: 14,
                            ),
                            decoration: const InputDecoration(
                                isCollapsed: true,
                                contentPadding:
                                    EdgeInsets.only(top: 0, bottom: 0),
                                border: OutlineInputBorder(
                                    borderSide: BorderSide.none),
                                hintText: '搜索制度名称',
                                hintStyle: TextStyle(
                                  color: ColorConfig.desTextColor,
                                  fontSize: 14,
                                )),
                          ),
                        )),
                        if (regimesManageController.isSearch) ...[
                          GestureDetector(
                            onTap: () {
                              regimesManageController.node?.unfocus();
                              Get.back();
                            },
                            child: Container(
                              child: Row(
                                children: [
                                  if(regimesManageController.searchController?.text.isNotEmpty == true)...[
                                    InkWell(
                                    onTap: () {
                                      regimesManageController.searchController?.text = '';
                                      regimesManageController.update();
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.fromLTRB(12, 7, 12, 7),
                                      child: Image.asset(AssetsRes.REGIMES_SEARCH_CLOSE),
                                    ),
                                  ),
                                  ],
                                  Container(
                                    width: 1,
                                    height: 12,
                                    color: ColorConfig.lineColor,
                                  ),
                                  Container(
                                    alignment: Alignment.center,
                                    width: 52,
                                    height: double.infinity,
                                    child: const Text('取消',
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: ColorConfig.themeCorlor)),
                                  )
                                ],
                              ),
                            ),
                          )
                        ]
                      ]))),
        ],
      ),
    );
  }

  //分类tab
  _backTabContainer() {
    var arg = Get.arguments ;
    if(arg == null){
      arg = Map();
    }

    var argument = Map.from(arg);
    argument['isFromWork'] = 0;
    argument['orgModel'] = regimesManageController.currentModel;
    if (regimesManageController.isSearch) {
      argument['keyword'] = regimesManageController.searchController?.text;
    }
    return Expanded(
        child: regimesManageController.isSearch
            ? Navigator(
                initialRoute: '/',
                onGenerateRoute: (RouteSettings settins) {
                  WidgetBuilder builder;
                  switch (settins.name) {
                    case '/':
                      builder = (context) => RegimesChildPageView(
                            argument: argument,
                          );
                      break;
                    default:
                      builder = (context) => RegimesChildPageView(
                            argument: argument,
                          );
                      break;
                  }
                  return MaterialPageRoute(builder: builder, settings: settins);
                },
              )
            : RegimesChildPageView(
                argument: argument,
              ));
  }
}
