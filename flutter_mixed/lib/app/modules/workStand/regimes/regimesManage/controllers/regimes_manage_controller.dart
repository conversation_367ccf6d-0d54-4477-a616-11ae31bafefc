import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_keyword_model.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_org_model.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/widget/regimes_choose_org.dart';
import 'package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart';
import 'package:flutter_mixed/app/retrofit/datasource/rule_datasource.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class RegimesManageController extends GetxController {

  OrgModel? model;
  TextEditingController? searchController;
  FocusNode? node;
  bool isSearch = false; //是否为搜索模式 搜索模式下跳页逻辑为底部body变化；非搜索模式为正常跳转页面
  TabController? tabController;
  int currentIndex = 0;
  List tabTexts = ['分类', '文档'];
  List dataList = [];
  List<RegimesOrgModel?>? powerOrgList = [];
  RegimesOrgModel? currentModel;
  BuildContext? navigatorContext;
  List? nameList = ['制度中心'];
  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController();
    node = FocusNode();
    if (Get.arguments['isFromWork'] != null) {
      var isFromWork = Get.arguments['isFromWork'];
      if (isFromWork == 1) {
        WorkFlowController workFlowController = Get.find();
        model = workFlowController.currentModel;
        currentModel = RegimesOrgModel()
          ..orgId = model?.companyId
          ..name = model?.name
          ..logo = model?.logo;
        getAllPowerOrg();
      }
    }
    if (Get.arguments['orgModel'] != null) {
      currentModel = Get.arguments['orgModel'];
    }
    if (Get.arguments['isSearch'] != null) {
      isSearch = Get.arguments['isSearch'];
    }
    if (Get.arguments['nameList'] != null) {
      nameList = Get.arguments['nameList'];
    }

    logger('=====argument==${Get.arguments}');
  }

  //获取此公司及其子公司集合
  getAllPowerOrg() async {
    if (model?.companyId == null) return;
    var ruleDatasource = RuleDataSource(retrofitDio, baseUrl: Host.HOST);
    var resp = await ruleDatasource.checkRulePower(model?.companyId);
    if (resp.success()) {
      if (resp.data == null) return;
      powerOrgList = resp.data?.orgVOS;
    }
  }

  //点击个公司选择
  tapOrgChoose() {
    if (powerOrgList?.isEmpty == true || powerOrgList == null) {
      toast('获取公司数据失败');
      return;
    }
    powerOrgList?.forEach((v) {
      if (v?.orgId == currentModel?.orgId) {
        v?.status = 1;
      } else {
        v?.status = 0;
      }
    });
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        RegimesChooseOrg({'data': powerOrgList}, (RegimesOrgModel? model) {
          if (model?.orgId != null) {
            if (currentModel?.orgId != model?.orgId) {
              eventBus.fire(RegimesChangeOrgModel(model!.orgId));
              currentModel = model;
              update();
            }
          }
        }));
  }

  //点击了搜索控件跳转搜索模式
  tapSearchController() {
    node?.unfocus();
    RouteHelper.route(Routes.REGIMES_MANAGE, arguments: {
      'orgModel': currentModel,
      'isSearch': true,
      'nameList': ['制度中心']
    });
  }

  //点击了搜索
  tapSearchControllerSubmit() {
    eventBus.fire(RegimesKeywordModel(searchController?.text));
  }

  @override
  void onReady() {
    super.onReady();
    if (isSearch && nameList?.length == 1) {
      node?.requestFocus();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
