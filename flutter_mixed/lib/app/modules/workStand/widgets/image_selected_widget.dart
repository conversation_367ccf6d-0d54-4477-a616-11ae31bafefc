import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';

class ImageSelectedWidget extends StatelessWidget {
  List? imageList = [];
  ImageSelectedWidget({super.key, this.imageList});
  @override
  Widget build(BuildContext context) {
    return _buildWidget();
  }

  _buildWidget() {
    if (imageList == null) return Container();
    if (imageList?.isEmpty == true) return Container();
    return Container(
      color: ColorConfig.whiteColor,
      width: double.infinity,
      height: 88,
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 20,
            child: const Text(
              '已选择',
              style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
          Expanded(
              child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: imageList?.length,
                      itemBuilder: ((context, index) {
                        String imageUrl = imageList![index];
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8)),
                              child: ImageLoader(
                                url: imageUrl,
                                width: 32,
                                height: 32,
                                radius: 8,
                              ),
                            ),
                            12.gap
                          ],
                        );
                      }))),
              SizedBox(
                width: 20,
                height: 20,
                child: Image.asset(AssetsRes.REGIMES_RIGHT_ARROW),
              )
            ],
          )),
        ],
      ),
    );
  }
}
