import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

class AddTopFunctionItem extends StatelessWidget{
  Map? item;
  Function? onAddPressed;
  AddTopFunctionItem({this.item,this.onAddPressed});
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    if (item == null) {
      return Container();
    }
    return _buildWidget(item!);
  }

  _buildWidget(Map item){
    return Container(
      padding: const EdgeInsets.only(bottom: 8),
      width: double.infinity,
      height: 68,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            ImageLoader(
              url: item['icon'],
              width: 44,
              height: 44,
              radius: 8,
            ),
            12.gap,
            Expanded(
                child: Container(
              child: Text(
                item['name'],
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    fontSize: 16, color: ColorConfig.mainTextColor),
              ),
            )),
            12.gap,
            if (item['selected'] == true) ...[
              Container(
                alignment: Alignment.centerRight,
                child: const Text(
                  '已添加',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.desTextColor),
                ),
              )
            ],
            if (item['selected'] != true) ...[
              InkWell(
                onTap: () {
                  item['selected'] = true;
                  if (onAddPressed != null) {
                    onAddPressed!(item);
                  }
                },
                child: Container(
                  width: 48,
                  height: 26,
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 1, color: ColorConfig.themeCorlor),
                      borderRadius: BorderRadius.circular(4)),
                  alignment: Alignment.center,
                  child: const Text(
                    '添加',
                    style:
                        TextStyle(fontSize: 14, color: ColorConfig.themeCorlor),
                  ),
                ),
              )
            ]
          ],
        ),
      ),
    );
  }

}