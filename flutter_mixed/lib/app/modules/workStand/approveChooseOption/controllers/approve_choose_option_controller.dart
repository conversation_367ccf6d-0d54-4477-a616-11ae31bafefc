import 'package:get/get.dart';

class ApproveChooseOptionController extends GetxController {

  RxList dataList = [].obs;
  List selectList = [];
  int chooseCount = 1; //限制个数

  int type = 0; //0选择框类1选择指定成员
  @override
  void onInit() {
    super.onInit();

    selectList = Get.arguments['selectList'];
    chooseCount = Get.arguments['chooseCount'];
    dataList.value = Get.arguments['list'];
    type = Get.arguments['type'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
