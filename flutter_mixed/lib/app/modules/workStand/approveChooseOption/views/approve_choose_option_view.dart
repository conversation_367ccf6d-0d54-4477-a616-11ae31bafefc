import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';

import 'package:get/get.dart';

import '../../../../common/config/config.dart';
import '../controllers/approve_choose_option_controller.dart';

class ApproveChooseOptionView extends GetView<ApproveChooseOptionController> {
  const ApproveChooseOptionView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          extendBodyBehindAppBar: true,
          appBar: TitleBar().backAppbar(context, '请选择', false, [
            Container(
              width: 60,
              child: CupertinoButton(
                  padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                  pressedOpacity: 0.5,
                  child: const Text(
                    '确定',
                    style: TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  ),
                  onPressed: () {
                    Get.back(result: {'list': controller.selectList});
                  }),
            )
          ], onPressed: () {
            Get.back();
          }),
          body: ListView.builder(
              itemCount: controller.dataList.length,
              itemBuilder: ((context, index) {
                Map dataMap = {};
                int temp = -1;
                MemberModel? memberModel;
                if (controller.type == 0) {
                  dataMap = controller.dataList[index];

                  for (var i = 0; i < controller.selectList.length; i++) {
                    Map map = controller.selectList[i];
                    if (map['frameId'] == dataMap['frameId']) {
                      temp = i;
                    }
                  }
                }
                if (controller.type == 1) {
                  memberModel = controller.dataList[index];

                  for (var i = 0; i < controller.selectList.length; i++) {
                    MemberModel selectModel = controller.selectList[i];
                    if (selectModel.userId == memberModel!.userId) {
                      temp = i;
                    }
                  }
                }

                return Column(
                  children: [
                    SizedBox(
                      height: 1,
                    ),
                    InkWell(
                      onTap: () {
                        if (controller.type == 0) {
                          if (controller.chooseCount == 1) {
                            if (temp == -1) {
                              controller.selectList.clear();
                              controller.selectList.add(dataMap);
                            }
                          } else {
                            if (temp != -1) {
                              controller.selectList.removeAt(temp);
                            } else {
                              if (controller.selectList.length ==
                                  controller.chooseCount) {
                                toast(
                                    '最多选择${controller.chooseCount}个选项');
                                return;
                              }
                              controller.selectList.add(dataMap);
                            }
                          }
                        }
                        if(controller.type == 1){
                          if(controller.chooseCount == 1){
                                                 if (temp == -1) {
                              controller.selectList.clear();
                              controller.selectList.add(memberModel);
                            }
                          }else{
                            if(temp != -1){
                              controller.selectList.removeAt(temp);
                            }else{
                              controller.selectList.add(memberModel);
                            }
                          }
                        }

                        controller.dataList.refresh();
                      },
                      child: SettingWidget().backSettingWidget(
                          temp != -1
                              ? 'assets/images/3.0x/login_selected.png'
                              : 'assets/images/3.0x/login_unselect.png',
                          controller.type==0?'':memberModel!.headimg,
                         controller.type==0? dataMap['title']:memberModel!.name,
                          '',
                          false,
                          56),
                    )
                  ],
                );
              })),
        ));
  }
}
