import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../../common/api/Define.dart';
import '../../../../utils/storage.dart';
import '../../../contact/model/org/org_model.dart';
import '../../approveList/controllers/approve_list_controller.dart';

class CompanyModelHelper {
  static OrgModel? orgModel;

  static List companyModelList = [];
  static ApproveListController? approveListController;
  static int approveCenterTabIndex = 0;

  static bool needUpdateApproveCenterTab = false;

  static Future<List> createCompanyModel() async {
    List authList = [2, 3, 4, 5]; //全公司模式
    dynamic dataDic = await UserDefault.getData(Define.ORGLIST);
    if (dataDic == null || dataDic == "") return [];
    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }
    for (var i = 0; i < companies.length; i++) {
      Map<String, dynamic> orgMap = companies[i];
      if (i == 0) {
        orgModel ??= OrgModel.fromJson(orgMap);
      }

      String power = orgMap['power'];
      if (orgMap['deptId'] == '0') {
        authList = [2, 3, 4, 5, 1];
        break;
      } else {
        if (power.contains('-1') || power.contains('7')) {
          authList = [2, 3, 4, 5, 1];
          break;
        }
      }
    }
    companyModelList = authList;
    eventBus.fire({'approveListView_dealAuthList': 1});

    return authList;
  }
}
