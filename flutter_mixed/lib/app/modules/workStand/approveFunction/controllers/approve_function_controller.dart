import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/manage/company_model.dart';
import 'package:flutter_mixed/app/retrofit/datasource/approve_datasource.dart';
import 'package:flutter_mixed/app/retrofit/entity/approve/approve_models_resp.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/http.dart';
import '../../../contact/model/org/org_model.dart';
import '../../workFlow/controllers/work_flow_controller.dart';

/// 发起审批 controller
class ApproveFunctionController extends GetxController
    with GetTickerProviderStateMixin {
  final modelGroupHeight = 40.0;
  final modelItemHeight = 64.0;

  StreamSubscription? subscription;
  List totalList = [
    {'listType': 1, 'name': '全部审批'},
    {'listType': 2, 'name': '待办'},
    {'listType': 3, 'name': '已办'},
    {'listType': 4, 'name': '已发起'},
    {'listType': 5, 'name': '抄送我'}
  ];
  RxList topList = [].obs;
  RxList tabList = [].obs;

  RxBool isLoading = true.obs;

  late TabController tabController =
      TabController(length: groupModels.length, vsync: this);
  final scrollController = ScrollController();
  bool isTabClicked = false;
  String orgId = '';
  RxString preStr = ''.obs;

  OrgModel? orgModel;
  RxList unreadCountList = [].obs;

  CancelToken? unreandCancelToken;
  

  List<ModelGroup> groupModels = [];
  List models = [];

  @override
  void onInit() async {
    super.onInit();

    scrollController.addListener(onScroll);

    orgId = Get.arguments['orgId'];
    if (Get.arguments['model'] != null) {
      orgModel = Get.arguments['model'];
    } else {
      var isFromWork = Get.arguments['isFromWork'];
      if (isFromWork == 1) {
        WorkFlowController workFlowController = Get.find();
        orgModel = workFlowController.currentModel;
      }
    }

    getModelList();

    List authList = await CompanyModelHelper.createCompanyModel();

    topList.clear();
    for (var i = 0; i < authList.length; i++) {
      int listType = authList[i];
      for (var j = 0; j < totalList.length; j++) {
        Map totalMap = totalList[j];
        if (totalMap['listType'] == listType) {
          topList.add(totalMap);
        }
      }
    }

    subscription = eventBus.on<Map>().listen((event) {
      if (event['refreshUnreadCount'] != null) {
        if (unreandCancelToken != null) {
          unreandCancelToken!.cancel();
        }
        getAllUnread();
      }
    });
  }

  var t1 ;

  @override
  void onReady() {
    super.onReady();
    getAllUnread();
    t1 = DateTime.now().millisecond;
  }

  @override
  void onClose() {
    subscription?.cancel();
    scrollController.removeListener(() {});
    scrollController.dispose();
    tabController.dispose();
    super.onClose();
  }

  // 根据滚动位置获取 index
  void onScroll() {
    if (isTabClicked) return;
    var offset = scrollController.offset;
    var positionOffset = 0.0;
    for (var i = 0; i < groupModels.length; i++) {
      positionOffset += modelGroupHeight;
      positionOffset += groupModels[i].modelList.length * modelItemHeight;
      if (positionOffset > offset) {
        tabController.animateTo(i);
        break;
      }
    }
  }

  // tab 点击监听
  tabControllerOnTap(index) {
    var scrollList = groupModels.sublist(0, index);
    var offset = 0.0;
    for (var i = 0; i < scrollList.length; i++) {
      offset += modelGroupHeight;
      var groupSize = scrollList[i].modelList.length;
      offset += groupSize * modelItemHeight;
    }
    isTabClicked = true;
    scrollController
        .animateTo(offset,
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut)
        .then((value) {
      isTabClicked = false;
    });
  }

  _composeExpandList(List<ApproveModelResp> groupList) {
    groupModels.clear();

    groupList.forEach((group) {
      var modelGroup = ModelGroup()
        ..modelGroupId = group.modelGroupId
        ..modelGroupName = group.modelGroupName;
      List<ModelItem> items = [];
      var modelList = group.modelList ?? [];
      modelList.forEach((child) {
        var item = ModelItem()
          ..modelId = child.modelId
          ..modelName = child.modelName
          ..modelLogo = child.modelLogo
          ..modelDesc = child.modelDesc
          ..visibleAble = child.visibleAble
          ..status = child.status
          ..version = child.version;
        items.add(item);
      });
          modelGroup.modelList
        ..clear()
        ..addAll(items);
      groupModels.add(modelGroup);
    });

    models.clear();
    groupModels.forEach((e) {
      models.add(e);
      models.addAll(e.modelList);
    });
    update();
  }

  //获取模版列表
  getModelList() async {

    var datasource = ApproveDataSource(retrofitDio, baseUrl: Host.HOST);
    var resp = await datasource.getApproveModels(orgId);
    if(resp.success()){
      var data = resp.data;

      isLoading.value = false;

      _composeExpandList(data);

      tabList.clear();
      for (var i = 0; i < groupModels.length; i++) {
        var group = groupModels[i];
        tabList.add(group.modelGroupName ?? '');
      }
      tabController = TabController(length: groupModels.length, vsync: this);

      update();

    }else {
      toast(resp.msg);
    }
  }

  //获取未读
  getAllUnread() {
    String url = ApproveApi.APPROVEHOMEPAGEUNREADV2;
    unreandCancelToken = CancelToken();
    DioUtil()
        .get(url, null, true, () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: unreandCancelToken)
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataMap = data['data'];
        List countList = [
          dataMap['allRedPointCount'],
          dataMap['backlogRedPointCount'],
          dataMap['finishRedPointCount'],
          dataMap['createRedPointCount'],
          dataMap['ccRedPointCount']
        ];
        int totalCount = 0;
        for (var i = 0; i < countList.length; i++) {
          int unreadCount = countList[i];
          totalCount += unreadCount;
        }

        //Map unreadMap = await UserDefault.getData(Define.APPROVE_TOTAL_LUNREADKEY);
        //unreadMap[orgId] = totalCount;
        Map unreadMap = {};
        unreadMap['total'] = totalCount;
        await UserDefault.setData(Define.APPROVE_TOTAL_LUNREADKEY, unreadMap);
        bool isHaveWorkFlow = Get.isRegistered<WorkFlowController>();
        if (isHaveWorkFlow) {
          WorkFlowController workController = Get.find();
          workController.dealApproveUnreadCount();
        }
        unreadCountList.value = countList;
        topList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}

class ModelGroup {
  String? modelGroupId;
  String? modelGroupName;
  List<ModelItem> modelList = <ModelItem>[];
}

class ModelItem {
  String? modelId;
  String? modelName;
  String? modelLogo;
  String? modelDesc;
  int? visibleAble;
  int? status;
  int? version;
}
