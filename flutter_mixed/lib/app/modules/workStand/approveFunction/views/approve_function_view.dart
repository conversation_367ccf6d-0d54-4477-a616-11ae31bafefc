import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/app_scaffold.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/manage/company_model.dart';
import 'package:flutter_mixed/app/modules/workStand/approveHome/controllers/approve_home_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/approveList/controllers/approve_list_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/sendApprove/controllers/send_approve_controller.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import '../../../../routes/app_pages.dart';
import '../../../place_holder/common_empty.dart';
import '../controllers/approve_function_controller.dart';
import 'package:badges/badges.dart' as badge;

/// 发起审批 tab
class ApproveFunctionView extends StatefulWidget {
  const ApproveFunctionView({super.key});

  @override
  State<StatefulWidget> createState() => ApproveFunctionState();
}

class ApproveFunctionState extends State<ApproveFunctionView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return InnerApproveFunctionView();
  }

  @override
  bool get wantKeepAlive => true;
}

class InnerApproveFunctionView extends GetView<ApproveFunctionController> {
  ApproveFunctionController approveFunctionController =
      Get.put(ApproveFunctionController());

  InnerApproveFunctionView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => AppScaffold(
          title: "审批",
          body: Column(
            children: [
              SizedBox(
                height: DeviceUtils().top.value + 44,
              ),
              Container(
                width: double.infinity,
                color: ColorConfig.whiteColor,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 15,
                    ),
                    Container(
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: backWidgetForList(),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Offstage(
                offstage: controller.tabList.isEmpty,
                child: Container(
                    width: DeviceUtils().width.value,
                    color: ColorConfig.whiteColor,
                    height: 48,
                    padding: const EdgeInsets.only(left: 15, right: 15),
                    child: TabBar(
                      labelPadding: const EdgeInsets.all(0),
                      controller: controller.tabController,
                      tabs: backTabbar(),
                      isScrollable: true,
                      indicatorColor: ColorConfig.themeCorlor,
                      indicatorWeight: 1,
                      labelColor: ColorConfig.themeCorlor,
                      unselectedLabelColor: ColorConfig.mainTextColor,
                      onTap: controller.tabControllerOnTap,
                      dividerHeight: 0,
                      tabAlignment: TabAlignment.start,
                    )),
              ),
              Expanded(
                  child: Container(
                      color: ColorConfig.whiteColor,
                      width: double.infinity,
                      alignment: Alignment.topLeft,
                      child: Stack(
                        children: [
                          MediaQuery.removePadding(
                              removeTop: true,
                              context: context,
                              child: _buildApproveModelListView()),
                          if (controller.isLoading.value) ...[
                            Container(
                              alignment: Alignment.center,
                              child: const CupertinoActivityIndicator(),
                            )
                          ]
                        ],
                      ))),
              const Divider(
                color: ColorConfig.lineColor,
                height: 1,
              ),
            ],
          ),
        ));
  }

  // 审批模版列表
  _buildApproveModelListView() {
    if (!controller.isLoading.value && controller.models.isEmpty) {
      return Container(
        alignment: Alignment.center,
        child: const CommonEmpty('暂无模版'),
      );
    }
    return ListView.builder(
        // scrollDirection: controller.scrollDirection,
        physics: const ClampingScrollPhysics(),
        controller: controller.scrollController,
        itemCount: controller.models.length,
        itemBuilder: (ctx, index) {
          var item = controller.models[index];
          return _buildModelItem(item);
        });
  }

  _buildModelItem(dynamic model) {
    if (model is ModelGroup) {
      return Container(
        padding: const EdgeInsets.only(left: 15, right: 15),
        alignment: Alignment.centerLeft,
        width: 200,
        height: controller.modelGroupHeight,
        child: Text(model.modelGroupName ?? ''),
      );
    } else if (model is ModelItem) {
      return Container(
        width: double.infinity,
        height: controller.modelItemHeight,
        padding: const EdgeInsets.only(left: 15, right: 15),
        child: Column(
          children: [
            InkWell(
              onTap: () async {
                bool isHaveSend = Get.isRegistered<
                    SendApproveController>(); //提交后迅速进入执行上个页面close的问题
                if (!isHaveSend) {
                  Get.toNamed(Routes.SEND_APPROVE, arguments: {
                    'model': controller.orgModel,
                    'orgId': controller.orgId,
                    'modelId': model.modelId
                  });
                }
              },
              child: Container(
                width: double.infinity,
                height: 56,
                // padding: EdgeInsets.all(7),
                decoration: BoxDecoration(
                    border: Border.all(width: 1, color: ColorConfig.lineColor),
                    borderRadius: BorderRadius.circular(8)),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    7.gap,
                    ImageLoader(
                      url: model.modelLogo ?? '',
                      width: 40,
                      height: 40,
                      radius: 8,
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          // height: 20,
                          child: Text(
                            model.modelName ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                fontSize: 14, color: ColorConfig.mainTextColor),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          // height: 20,
                          child: Text(
                            model.modelDesc ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                fontSize: 13, color: ColorConfig.desTextColor),
                          ),
                        ),
                      ],
                    ))
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 8,
            )
          ],
        ), //backGridView(i + 3)
      );
    }
    return Container();
  }

  List<Widget> backTabbar() {
    List<Widget> list = [];
    for (var i = 0; i < controller.tabList.length; i++) {
      Widget tabText = Container(
        alignment: Alignment.center,
        width: (DeviceUtils().width.value - 30) * 0.2,
        child: Text(controller.tabList[i],
            maxLines: 2,
            style: const TextStyle(
                color: ColorConfig.mainTextColor, fontSize: 14)),
      );
      list.add(tabText);
    }
    return list;
  }

  // 顶部按钮： 待办、已办，已发起、抄送我
  backWidgetForList() {
    List<Widget> widgets = [];
    for (var i = 0; i < controller.topList.length; i++) {
      int count = 0;
      Map topMap = controller.topList[i];
      int listType = topMap['listType'];
      if (controller.unreadCountList.isNotEmpty) {
        count = controller.unreadCountList[listType - 1];
      }
      Widget widget = InkWell(
        onTap: () {
          try {
            /// 切换 待办/已办状态
            ApproveHomeController approveHomeController = Get.find();
            approveHomeController.pageController.jumpToPage(1);
            // approveHomeController.update();
          } catch (e) {}
          try {
            CompanyModelHelper.approveCenterTabIndex = i; // 选择（待办，已办，已发起灯）
            // ApproveListController centerController = Get.find();
            // centerController.currentIndex.value = i;
            eventBus.fire(EventRefreshApproveListTabIndex(i));
          } catch (e) {}
        },
        child: Container(
          width: 60,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _badge(
                  Key('$i-$count'),
                  count,
                  Container(
                    alignment: Alignment.center,
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: ColorConfig.backgroundColor),
                    child: Container(
                      constraints: const BoxConstraints(minWidth: 16),
                      child: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            child: Image.asset(
                                'assets/images/3.0x/workflow_function_${topMap['listType'] - 1}.png'),
                          ),
                        ],
                      ),
                    ),
                  )),
              8.gap,
              Container(
                alignment: Alignment.topCenter,
                child: Text(
                  topMap['name'],
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              ),
              8.gap
            ],
          ),
        ),
      );
      widgets.add(widget);
    }
    return widgets;
  }

  _badge(Key key, int count, Widget child) {
    var value = '';
    var shape = badge.BadgeShape.circle;

    if (count <= 0) {
      value = '';
    } else if (count > 99) {
      value = '99+';
      shape = badge.BadgeShape.square;
    } else {
      value = '$count';
      shape = badge.BadgeShape.circle;
    }

    var badgeStyle = badge.BadgeStyle(
      shape: shape,
      badgeColor: ColorConfig.deleteCorlor,
      padding: EdgeInsets.all(1),
      borderRadius: BorderRadius.circular(12),
      // borderSide: BorderSide(color: ColorConfig.deleteCorlor, width: 1),
      elevation: 0,
    );

    if(shape == badge.BadgeShape.circle){
      badgeStyle = badge.BadgeStyle(
        shape: shape,
        badgeColor: ColorConfig.deleteCorlor,
        padding: EdgeInsets.all(1),
        elevation: 0,
      );
    }
    double fontSize = 10;
    return Padding(
        padding: EdgeInsets.all(0),
        child: badge.Badge(
          // shape: BadgeShape.circle,
          ignorePointer: false,
          badgeAnimation: const badge.BadgeAnimation.scale(
            animationDuration: Duration(seconds: 1),
            colorChangeAnimationDuration: Duration(seconds: 1),
            loopAnimation: false,
            curve: Curves.fastOutSlowIn,
            colorChangeAnimationCurve: Curves.easeInCubic,
          ),
          badgeStyle: badgeStyle,
          badgeContent: Container(
            padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
            key: key,
            constraints: BoxConstraints(minWidth: 22),
            child: Center(
              child: Text(
                value,
                style: TextStyle(fontSize: fontSize, color: Colors.white),
              ),
            ),
          ),
          showBadge: count > 0,
          position: badge.BadgePosition.topEnd(top: -9, end: -12),
          child: child,
        ));
  }
}
