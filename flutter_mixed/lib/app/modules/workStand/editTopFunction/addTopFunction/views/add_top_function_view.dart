import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/workStand/widgets/add_top_function_item.dart';
import 'package:flutter_mixed/app/modules/workStand/widgets/image_selected_widget.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';

import 'package:get/get.dart';

import '../controllers/add_top_function_controller.dart';

class AddTopFunctionView extends GetView<AddTopFunctionController> {
  const AddTopFunctionView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          appBar:
              TitleBar().backAppbar(context, '添加常用', false, [], onPressed: () {
            Get.back();
          }),
          body: Column(
            children: [
              _buildSearchWidget(),
              Offstage(
                offstage: controller.imageList.isEmpty,
                child: _buildSelectedWidget(),
              ),
              8.gap,
              Expanded(child: _buildAllFunction())
            ],
          ),
        ));
  }

  //搜索控件
  _buildSearchWidget() {
    return Container(
        width: double.infinity,
        color: ColorConfig.whiteColor,
        height: 56,
        padding: const EdgeInsets.fromLTRB(15, 8, 15, 8),
        child: SettingWidget().backSearchWidget(
            controller.textEditingController ?? TextEditingController(),
            controller.node ?? FocusNode(),
            '搜索应用',
            onSub: (value) {}, onTap: () async {
          controller.node?.unfocus();
          RouteHelper.route(Routes.SEARCH_TOP_FUNCTION,arguments: {
            'allList': controller.allList,
            'topList': controller.topList,
            'orgId': controller.orgId
          });
        }));
  }

  //选择控件
  _buildSelectedWidget() {
    return InkWell(
      onTap: () {
        controller.enterEditPage();
      },
      child: Container(
        width: double.infinity,
        color: ColorConfig.whiteColor,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: ImageSelectedWidget(
          imageList: controller.imageList,
        ),
      ),
    );
  }

  //应用列表
  _buildAllFunction() {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          color: ColorConfig.whiteColor),
      child: Column(
        children: [
          Container(
            height: 38,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            alignment: Alignment.centerLeft,
            child: const Row(
              children: [
                Text(
                  '全部应用',
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: ColorConfig.mainTextColor),
                ),
              ],
            ),
          ),
          const Divider(
            color: ColorConfig.backgroundColor,
            height: 1,
          ),
          8.gap,
          Expanded(
              child: ListView.builder(
                  itemCount: controller.allList.length,
                  itemBuilder: (context, index) {
                    Map item = controller.allList[index];
                    return AddTopFunctionItem(item: item,onAddPressed: (item){
                      controller.addTopFunctionData(item);
                    },);
                  }))
        ],
      ),
    );
  }
}
