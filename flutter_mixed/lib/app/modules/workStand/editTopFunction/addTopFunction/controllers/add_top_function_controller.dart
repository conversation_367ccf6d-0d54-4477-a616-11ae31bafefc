import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/modules/workStand/models/function_add_model.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class AddTopFunctionController extends GetxController {
  //TODO: Implement AddTopFunctionController

  RxList topList = [].obs; //常用
  RxList allList = [].obs; //所有
  String? orgId = '';
  TextEditingController? textEditingController;
  FocusNode? node;
  List imageList = [];
  @override
  void onInit() {
    super.onInit();
    textEditingController = TextEditingController();
    node = FocusNode();
    if (Get.arguments['topList'] != null) {
      topList = Get.arguments['topList'];
    }
    if (Get.arguments['allList'] != null) {
      allList.value = Get.arguments['allList'];
    }
    if (Get.arguments['orgId'] != null) {
      orgId = Get.arguments['orgId'];
    }
    _dealFuntionData();
  }

  _dealFuntionData() {
    imageList.clear();
    for (var i = 0; i < topList.length; i++) {
      Map topItem = topList[i];
      imageList.add(topItem['icon']);
      topItem['selected'] = true;
      for (var j = allList.length -1; j >= 0; j--) {
        Map item = allList[j];
        if (item['key'] == topItem['key']){
          allList.removeAt(j);
          break;
        }
      }
    }
    allList.insertAll(0, topList);
  }

  //添加常用应用
  addTopFunctionData(Map item) async{
    try {
      if (orgId == null) return;
      topList.add(item);
      List keyList = [];
      for (var i = 0; i < topList.length; i++) {
        String key = topList[i]['key'];
        keyList.add(key);
      }
      imageList.add(item['icon']);
      allList.refresh();
      String userId = await UserHelper.getUid();
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      FunctionAddModel model = FunctionAddModel(moduleInfo: jsonEncode(keyList), orgId: orgId, userId: userId);
      var resp = await wrokDatasource.addTopFunctionData(model);
      if (!resp.success()) {
        topList.remove(item);
        imageList.remove(item['icon']);
        allList.refresh();
      }
    } catch (e) {
        topList.remove(item);
        imageList.remove(item['icon']);
        allList.refresh();
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    textEditingController?.dispose();
    node?.unfocus();
  }
}
