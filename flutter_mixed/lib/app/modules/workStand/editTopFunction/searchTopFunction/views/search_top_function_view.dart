import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/modules/place_holder/common_empty.dart';
import 'package:flutter_mixed/app/modules/workStand/widgets/add_top_function_item.dart';

import 'package:get/get.dart';

import '../controllers/search_top_function_controller.dart';

class SearchTopFunctionView extends GetView<SearchTopFunctionController> {
  const SearchTopFunctionView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        appBar:
            TitleBar().backAppbar(context, '搜索应用', false, [], onPressed: () {
          Get.back();
        }),
        body: Column(
          children: [_buildSearchWidget(),_buildBody()],
        )));
  }

  //搜索控件
  _buildSearchWidget() {
    return Container(
        width: double.infinity,
        color: ColorConfig.whiteColor,
        height: 56,
        padding: const EdgeInsets.fromLTRB(15, 8, 15, 8),
        child: SettingWidget().backSearchWidget(
            controller.textEditingController ?? TextEditingController(),
            controller.node ?? FocusNode(),
            '搜索应用',
            onSub: (value) {
              controller.didSearch(value);
            },
            onTap: () async {}));
  }

  _buildBody(){
    if (controller.searchList.isEmpty && !controller.isSearch) {
      return Container();
    }else if(controller.searchList.isEmpty){
      return Expanded(child: Container(
        color: ColorConfig.whiteColor,
          width: double.infinity,
          height: double.infinity,
          alignment: Alignment.center,
          child: const CommonEmpty('未找到符合条件的应用'),
        ));
    }else{
      return Expanded(child: ListView.builder(
        itemCount: controller.searchList.length,
        itemBuilder: (context,index){
        Map item = controller.searchList![index];
        return Container(
          color: ColorConfig.whiteColor,
          child: AddTopFunctionItem(item: item,onAddPressed: (item){
          controller.searchList.refresh();
          controller.addTopFunctionData(item);
        },),
        );
      }));
    }
  }
}
