import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/modules/workStand/models/function_add_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/refresh_top_function.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class SearchTopFunctionController extends GetxController {
  //TODO: Implement SearchTopFunctionController

  List allList = [];
  RxList topList = [].obs;
  String? orgId = '';
  TextEditingController? textEditingController;
  FocusNode? node;
  RxList searchList = [].obs;
  bool isSearch = false;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments['allList'] != null) {
      allList = Get.arguments['allList'];
    }
    if (Get.arguments['topList'] != null) {
      topList = Get.arguments['topList'];
    }
    if (Get.arguments['orgId'] != null) {
      orgId = Get.arguments['orgId'];
    }
    textEditingController = TextEditingController();
    node = FocusNode();
  }

  //添加常用应用
  addTopFunctionData(Map item) async {
    try {
      if (orgId == null) return;
      topList.insert(0, item);
      List keyList = [];
      for (var i = 0; i < topList.length; i++) {
        String key = topList[i]['key'];
        keyList.add(key);
      }
      searchList.refresh();
      String userId = await UserHelper.getUid();
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      FunctionAddModel model = FunctionAddModel(
          moduleInfo: jsonEncode(keyList), orgId: orgId, userId: userId);
      var resp = await wrokDatasource.addTopFunctionData(model);
      if (!resp.success()) {
        topList.remove(item);
        searchList.refresh();
      }else{
        eventBus.fire(RefreshTopFunction(topList: topList.value));
      }
    } catch (e) {
      topList.remove(item);
      searchList.refresh();
    }
  }

  didSearch(String text) {
    isSearch = true;
    searchList.clear();
    for (var i = 0; i < allList.length; i++) {
      Map item = allList[i];
      String name = item['name'] ?? '';
      if (name.contains(text)) {
        searchList!.add(item);
      }
    }
    searchList.refresh();
  }

  @override
  void onReady() {
    super.onReady();
    node?.requestFocus();
  }

  @override
  void onClose() {
    super.onClose();
    textEditingController?.dispose();
    node?.unfocus();
  }
}
