import 'dart:convert';

import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/refresh_notice_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/function_add_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/refresh_top_function.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_swipe_action_cell/core/controller.dart';
import 'package:get/get.dart';

class TopFunctionListController extends GetxController {
  //TODO: Implement TopFunctionListController

  RxList topList = [].obs;
  String? orgId;

  SwipeActionController? swipeController;
  @override
  void onInit() {
    super.onInit();
    swipeController = SwipeActionController();
    if (Get.arguments['topList'] != null) {
      topList = RxList.from(Get.arguments['topList']);
    }

    if (Get.arguments['orgId'] != null) {
      orgId = Get.arguments['orgId'];
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    swipeController?.dispose();
  }

  // 删除列表项
  void removeItem(int index,Map item) {
    if (index >= 0 && index < topList.length) {
      topList.removeAt(index);
      _addTopFunctionData();
    }
  }

  // 重新排序列表项
  void reorderItems(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = topList.removeAt(oldIndex);
    topList.insert(newIndex, item);
    _addTopFunctionData();
  }

    //添加常用应用
  _addTopFunctionData() async{
    try {
      if (orgId == null) return;
      List keyList = [];
      for (var i = 0; i < topList.length; i++) {
        String key = topList[i]['key'];
        keyList.add(key);
      }
      topList.refresh();
      String userId = await UserHelper.getUid();
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      FunctionAddModel model = FunctionAddModel(moduleInfo: jsonEncode(keyList), orgId: orgId, userId: userId);
      var resp = await wrokDatasource.addTopFunctionData(model);
      if (!resp.success()) {
        toast('删除失败');
      }else{
        eventBus.fire(RefreshTopFunction(topList: topList.value));
      }
    } catch (e) {
      toast('删除失败');
    }
  }
}
