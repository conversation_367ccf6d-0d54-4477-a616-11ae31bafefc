import 'package:flutter_swipe_action_cell/core/controller.dart';
import 'package:get/get.dart';

class TopFunctionListController extends GetxController {
  //TODO: Implement TopFunctionListController

  RxList topList = [].obs;
  List imageList = [];
  String? orgId;

  SwipeActionController? swipeController;
  @override
  void onInit() {
    super.onInit();
    swipeController = SwipeActionController();
    if (Get.arguments['topList'] != null) {
      topList = Get.arguments['topList'];
    }

    if (Get.arguments['imageList'] != null) {
      imageList = Get.arguments['imageList'];
    }

    if (Get.arguments['orgId'] != null) {
      orgId = Get.arguments['orgId'];
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    swipeController?.dispose();
  }

  // 删除列表项
  void removeItem(int index,Map item) {
    if (index >= 0 && index < topList.length) {
      topList.removeAt(index);
      imageList.removeAt(index);
    }
  }

  // 重新排序列表项
  void reorderItems(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = topList.removeAt(oldIndex);
    topList.insert(newIndex, item);
  }
}
