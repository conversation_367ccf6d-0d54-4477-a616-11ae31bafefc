import 'package:get/get.dart';

class TopFunctionListController extends GetxController {
  //TODO: Implement TopFunctionListController

  RxList topList = [].obs;
  List imageList = [];
  String? orgId;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments['topList'] != null) {
      topList = Get.arguments['topList'];
    }

    if (Get.arguments['imageList'] != null) {
      imageList = Get.arguments['imageList'];
    }

    if (Get.arguments['orgId'] != null) {
      orgId = Get.arguments['orgId'];
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
