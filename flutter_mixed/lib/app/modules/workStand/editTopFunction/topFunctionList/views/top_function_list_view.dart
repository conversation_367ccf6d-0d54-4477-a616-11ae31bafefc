import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_swipe_action_cell/flutter_swipe_action_cell.dart';

import 'package:get/get.dart';

import '../controllers/top_function_list_controller.dart';

class TopFunctionListView extends GetView<TopFunctionListController> {
  const TopFunctionListView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      appBar: TitleBar().backAppbar(context, '常用设置', false, [], onPressed: (){
        Get.back();
      }),
      body: Column(
        children: [
          8.gap,
          _buildMsgWidget(),
          16.gap,
          Expanded(child: _buildFunctionList()),
        ],
      ),
    ));
  }

  _buildMsgWidget(){
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(8),
      width: double.infinity,
      height: 22,
      child: const Text('已添加常用应用',style: TextStyle(fontSize: 12,color: ColorConfig.msgTextColor),),
    );
  }

  _buildFunctionList(){
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorConfig.whiteColor
      ),
      child: ReorderableListView.builder(
        itemCount: controller.topList.length,
        onReorder: (int oldIndex, int newIndex) {
          controller.reorderItems(oldIndex, newIndex);
        },
        itemBuilder: (context, index) {
          final item = controller.topList[index];
          return _buildFunctionItem(item, index);
        },
      ),
    );
  }

  _buildFunctionItem(dynamic item, int index){
    return SwipeActionCell(
      key: ValueKey(item['id'] ?? index),
      trailingActions: [
        SwipeAction(
          title: "删除",
          onTap: (CompletionHandler handler) async {
            // 删除操作
            controller.removeItem(index);
            handler(true);
          },
          color: Colors.red,
        ),
      ],
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
        decoration: const BoxDecoration(
          color: ColorConfig.whiteColor,
        ),
        child: Row(
          children: [
            InkWell(
              onTap: () {
                // 第56行点击按钮时触发滑动删除
                // 这里可以通过编程方式触发滑动效果
              },
              child: ImageLoader(
                url: item['icon'] ?? '',
                width: 24,
                height: 24,
                radius: 0,
              ),
            ),
            12.gap,
            Expanded(
              child: Text(
                item['name'] ?? '',
                style: const TextStyle(
                    fontSize: 16, color: ColorConfig.mainTextColor),
              ),
            ),
            12.gap,
            // 拖拽手柄图标
            const Icon(
              Icons.drag_handle,
              color: ColorConfig.msgTextColor,
              size: 24,
            )
          ],
        ),
      ),
    );
  }
}
