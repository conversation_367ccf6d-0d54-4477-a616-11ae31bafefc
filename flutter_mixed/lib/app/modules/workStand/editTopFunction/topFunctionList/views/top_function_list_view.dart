import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:flutter_swipe_action_cell/flutter_swipe_action_cell.dart';

import 'package:get/get.dart';

import '../controllers/top_function_list_controller.dart';

class TopFunctionListView extends GetView<TopFunctionListController> {
  const TopFunctionListView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          appBar:
              TitleBar().backAppbar(context, '常用设置', false, [], onPressed: () {
            Get.back();
          }),
          body: Column(
            children: [
              8.gap,
              _buildMsgWidget(),
              Expanded(child: _buildFunctionList())
            ],
          ),
        ));
  }

  _buildMsgWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(8),
      width: double.infinity,
      height: 38,
      alignment: Alignment.centerLeft,
      child: const Text(
        '已添加常用应用',
        style: TextStyle(fontSize: 12, color: ColorConfig.msgTextColor),
      ),
    );
  }

  _buildFunctionList() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ReorderableListView.builder(
        itemCount: controller.topList.length,
        onReorder: (int oldIndex, int newIndex) {
          controller.reorderItems(oldIndex, newIndex);
        },
        itemBuilder: (context, index) {
          final item = controller.topList[index];
          return _buildFunctionItem(item, index);
        },
      ),
    );
  }

  _buildFunctionItem(dynamic item, int index) {
    //final SwipeActionController swipeController = SwipeActionController();
    logger('=====shuxinle');
    return SwipeActionCell(
      key: ValueKey(item['key']),
      controller: controller.swipeController,
      index: index,
      backgroundColor: Colors.transparent,
      trailingActions: [
        SwipeAction(
          title: "删除",
          onTap: (CompletionHandler handler) async {
            // 删除操作
            controller.removeItem(index,item);
            //handler(true);
          },
          color: ColorConfig.deleteCorlor,
        ),
      ],
      child: Container(
        height: 60,
        decoration: BoxDecoration(
            color: ColorConfig.whiteColor,
            borderRadius: BorderRadius.only(
              topLeft: index == 0 ? const Radius.circular(16) : Radius.zero,
              topRight: index == 0 ? const Radius.circular(16) : Radius.zero,
              bottomLeft: index == controller.topList.length - 1
                  ? const Radius.circular(16)
                  : Radius.zero,
              bottomRight: index == controller.topList.length - 1
                  ? const Radius.circular(16)
                  : Radius.zero,
            )),
        child: Column(
          children: [
            Expanded(
                child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                16.gap,
                InkWell(
                  onTap: () {
                    // 第84行点击按钮时从右侧滑出删除按钮
                    controller.swipeController?.openCellAt(
                        index: index, trailing: true, animated: true);
                  },
                  child: const ImageLoader(
                    url: AssetsRes.WORKFLOW_CELL_DELETE,
                    width: 24,
                    height: 24,
                    radius: 0,
                  ),
                ),
                12.gap,
                ImageLoader(
                  url: item['icon'] ?? '',
                  width: 36,
                  height: 36,
                  radius: 6,
                ),
                12.gap,
                Expanded(
                  child: Text(
                    item['name'] ?? '',
                    style: const TextStyle(
                        fontSize: 16, color: ColorConfig.mainTextColor),
                  ),
                ),
                12.gap,
                // 拖拽手柄图标
                const ImageLoader(
                  url: AssetsRes.WORKFLOW_CELL_EDIT,
                  width: 24,
                  height: 24,
                  radius: 0,
                ),
                16.gap
              ],
            )),
            Divider(
              color: index == controller.topList.length - 1
                  ? Colors.transparent
                  : ColorConfig.backgroundColor,
              height: 0.5,
              indent: 52,
            )
          ],
        ),
      ),
    );
  }
}
