import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

import 'package:get/get.dart';

import '../controllers/top_function_list_controller.dart';

class TopFunctionListView extends GetView<TopFunctionListController> {
  const TopFunctionListView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      appBar: TitleBar().backAppbar(context, '常用设置', false, [], onPressed: (){
        Get.back();
      }),
      body: Column(
        children: [
          8.gap,
          _buildMsgWidget()
        ],
      ),
    ));
  }

  _buildMsgWidget(){
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(8),
      width: double.infinity,
      height: 22,
      child: const Text('已添加常用应用',style: TextStyle(fontSize: 12,color: ColorConfig.msgTextColor),),
    );
  }

  _buildFunctionList(){
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorConfig.whiteColor
      ),
    );
  }

  _buildFunctionItem(){
    return Container(
      padding: EdgeInsets.fromLTRB(16, 12, 16, 12),
      child: Row(
        children: [
          InkWell(
            onTap: () {
              
            },
            child: ImageLoader(
              width: 24,
            ),
          )
        ],
      ),
    );
  }
}
