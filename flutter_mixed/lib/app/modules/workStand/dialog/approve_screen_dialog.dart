import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/models/approve_screen_model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:badges/badges.dart' as badge;

class ApproveScreenDialog {
  TabController? tabController;
  List allDataList = [];

  RxInt currentIndex = 0.obs; //当前tab选中的值
  RxInt currentOrgListIndex = 0.obs; //当前左侧列表选中的值
  String groupName = '';
  String modelName = '';
  String emptyMsg = '';

  show(List totalList, bool isShowR, Function onSurePressed,
      {String emptyStr = ''}) {
    allDataList = List.from(totalList);

    emptyMsg = emptyStr;
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(() => Scaffold(
              backgroundColor: Colors.transparent,
              body: Stack(
                children: [
                  Container(color: ColorConfig.maskColor),
                  Positioned(
                      top: DeviceUtils().top.value + 44 + 60 + 48,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: Container(
                        decoration: const BoxDecoration(
                            color: ColorConfig.whiteColor,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8),
                                topRight: Radius.circular(8))),
                        child: Column(
                          children: [
                            SizedBox(
                              width: double.infinity,
                              height: 44,
                              child: Row(
                                children: [
                                  6.gap,
                                  InkWell(
                                    onTap: () {
                                      hide();
                                    },
                                    child: Container(
                                      width: 44,
                                      height: 44,
                                      alignment: Alignment.center,
                                      child: SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: Image.asset(
                                            'assets/images/3.0x/contact_dept_close.png'),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                      child: Container(
                                    alignment: Alignment.center,
                                    child: const Text(
                                      '筛选',
                                      style: TextStyle(
                                          fontSize: 16,
                                          color: ColorConfig.mainTextColor),
                                    ),
                                  )),
                                  40.gap
                                ],
                              ),
                            ),
                            const Divider(
                              color: ColorConfig.lineColor,
                              height: 1,
                            ),
                            Expanded(
                                child: DefaultTabController(
                                    initialIndex: currentIndex.value,
                                    length: allDataList.length,
                                    child: Builder(builder: ((context) {
                                      tabController =
                                          DefaultTabController.of(context);
                                      tabController!.addListener(() {
                                        currentIndex.value =
                                            tabController!.index;
                                        currentOrgListIndex.value = 0;
                                        groupName = '';
                                        modelName = '';
                                      });
                                      return Column(
                                        children: [
                                          Container(
                                              width: double.infinity,
                                              height: 48,
                                              padding: const EdgeInsets.only(
                                                  left: 15, right: 15),
                                              child: TabBar(
                                                labelPadding:
                                                    const EdgeInsets.all(0),
                                                tabs: backTabbar(),
                                                isScrollable: true,
                                                indicatorColor:
                                                    ColorConfig.themeCorlor,
                                                indicatorWeight: 1,
                                                labelColor:
                                                    ColorConfig.themeCorlor,
                                                unselectedLabelColor:
                                                    ColorConfig.mainTextColor,
                                                dividerHeight: 0,
                                                tabAlignment:
                                                    TabAlignment.start,
                                              )),
                                          Expanded(
                                              child: SizedBox(
                                            child: TabBarView(
                                                children: getWidgetList()),
                                          )),
                                        ],
                                      );
                                    })))),
                            const Divider(
                              color: ColorConfig.lineColor,
                              height: 1,
                            ),
                            Container(
                              alignment: Alignment.center,
                              width: double.infinity,
                              height: 54,
                              child: Container(
                                alignment: Alignment.center,
                                width: double.infinity,
                                height: 44,
                                child: Row(
                                  children: [
                                    16.gap,
                                    Expanded(
                                        flex: 1,
                                        child: InkWell(
                                          onTap: () {
                                            tabController!.animateTo(0);
                                            currentIndex.value = 0;
                                            currentOrgListIndex.value = 0;
                                            groupName = '';
                                            modelName = '';
                                            DeviceUtils().top.refresh();
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            height: 38,
                                            decoration: BoxDecoration(
                                                border: Border.all(
                                                    width: 1,
                                                    color:
                                                        ColorConfig.lineColor),
                                                borderRadius:
                                                    BorderRadius.circular(6)),
                                            child: const Text(
                                              '重置',
                                              style: TextStyle(
                                                  fontSize: 14,
                                                  color: ColorConfig
                                                      .mainTextColor),
                                            ),
                                          ),
                                        )),
                                    16.gap,
                                    Expanded(
                                        flex: 1,
                                        child: InkWell(
                                          onTap: () {
                                            ApproveScreenModel screenModel =
                                                allDataList[currentIndex.value];
                                            String? groupId;
                                            String? orgId;
                                            String? corgId;
                                            String? screenName;
                                            List<Groups> screenGroups = [];

                                            if (currentOrgListIndex.value ==
                                                0) {
                                              groupId = screenModel.groupId;
                                              orgId = screenModel.orgId;
                                              corgId = screenModel.corgId;
                                              if (screenModel.orgId != null) {
                                                screenName =
                                                    screenModel.orgName;
                                              } else {
                                                screenName = screenModel.name;
                                              }

                                              screenGroups = screenModel
                                                  .allOrgGroupModelList!;
                                            } else {
                                              List<ListOrgApproveStatisticList>
                                                  listOrgApproveStatisticList =
                                                  screenModel
                                                      .listOrgApproveStatisticList!;
                                              int index =
                                                  currentOrgListIndex.value -
                                                      1; //此数据不包含全部公司数据
                                              ListOrgApproveStatisticList?
                                                  statisticList;
                                              if (listOrgApproveStatisticList
                                                      .length >
                                                  index) {
                                                statisticList =
                                                    listOrgApproveStatisticList[
                                                        index];

                                                groupId =
                                                    statisticList.groupId ??
                                                        screenModel.groupId;
                                                orgId = statisticList.orgId;
                                                corgId = statisticList.corgId;
                                                screenName = statisticList.name;
                                                screenGroups =
                                                    statisticList.groups!;
                                              }
                                            }

                                            // if (screenModel.orgId != null) {
                                            //   //其他公司数据
                                            //   orgId = screenModel.orgId;
                                            //   corgId = screenModel.corgId;
                                            //   screenName = screenModel.orgName;
                                            //   screenGroups = screenModel
                                            //       .allOrgGroupModelList!;
                                            // } else {
                                            //   groupId = screenModel.groupId;
                                            //   if (currentOrgListIndex.value !=
                                            //       0) {
                                            //     List<ListOrgApproveStatisticList>
                                            //         listOrgApproveStatisticList =
                                            //         screenModel
                                            //             .listOrgApproveStatisticList!;
                                            //     int index =
                                            //         currentOrgListIndex.value -
                                            //             1; //此数据不包含全部公司数据
                                            //     ListOrgApproveStatisticList?
                                            //         statisticList;
                                            //     if (listOrgApproveStatisticList
                                            //             .length >
                                            //         index) {
                                            //       statisticList =
                                            //           listOrgApproveStatisticList[
                                            //               index];
                                            //       orgId = statisticList.orgId;
                                            //       corgId = statisticList.corgId;
                                            //       screenName =
                                            //           statisticList.name;
                                            //       screenGroups =
                                            //           statisticList.groups!;
                                            //     }
                                            //   } else {
                                            //     screenGroups = screenModel
                                            //         .allOrgGroupModelList!;
                                            //     screenName = screenModel.name;
                                            //   }
                                            //}
                                            Map backMap = {
                                              'groupId': groupId,
                                              'orgId': orgId,
                                              'corgId': corgId,
                                              'groupName': groupName,
                                              'modelName': modelName,
                                              'screenName': screenName
                                            };
                                            backMap.removeWhere((key, value) {
                                              return value == null ||
                                                  value.isEmpty;
                                            });

                                            onSurePressed(
                                                backMap, screenGroups);
                                            hide();
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            height: 38,
                                            decoration: BoxDecoration(
                                                color: ColorConfig.themeCorlor,
                                                borderRadius:
                                                    BorderRadius.circular(6)),
                                            child: const Text(
                                              '确定',
                                              style: TextStyle(
                                                  fontSize: 14,
                                                  color:
                                                      ColorConfig.whiteColor),
                                            ),
                                          ),
                                        )),
                                    16.gap,
                                  ],
                                ),
                              ),
                            ),
                            DeviceUtils().bottom.value.gap
                          ],
                        ),
                      ))
                ],
              ),
            )));
  }

  List<Widget> backTabbar() {
    List<Widget> list = [];
    for (var i = 0; i < allDataList.length; i++) {
      ApproveScreenModel screenModel = allDataList[i];
      Widget tabText = Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 16, right: 16),

        // width: allDataList.length > 5
        //     ? (DeviceUtils().width.value - 30) * 0.22
        //     : (DeviceUtils().width.value - 30) * 0.2,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 120),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Text(screenModel.name!,
                  maxLines: 1,
                  style: const TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: ColorConfig.mainTextColor,
                      fontSize: 14)),
              Positioned(
                  top: 0,
                  right: -8,
                  width: 8,
                  height: 8,
                  child: Offstage(
                    offstage: screenModel.count == 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                          color: ColorConfig.deleteCorlor,
                          borderRadius: BorderRadius.circular(4)),
                    ),
                  ))
            ],
          ),
        ),
      );
      list.add(tabText);
    }
    return list;
  }

  List<Widget> getWidgetList() {
    List<Widget> list = [];
    for (var i = 0; i < allDataList.length; i++) {
      Widget listWidget = getWidgetWithOrgApproveData(allDataList[i], i);
      list.add(listWidget);
    }
    return list;
  }

  getWidgetWithOrgApproveData(
      ApproveScreenModel screenModel, int approveDataIndex) {
    List<ListOrgApproveStatisticList> orgDataList =
        screenModel.listOrgApproveStatisticList!;
    List tempList = List.from(orgDataList);
    ListOrgApproveStatisticList allStatistic = ListOrgApproveStatisticList(
        name: '全部公司',
        groups: screenModel.allOrgGroupModelList,
        count: screenModel.count);
    tempList.insert(0, allStatistic);

    ListOrgApproveStatisticList? currentStatisctic;
    if (tempList.length > currentOrgListIndex.value) {
      currentStatisctic = tempList[currentOrgListIndex.value];
    } else {
      currentStatisctic = tempList.first;
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Row(
        children: [
          Offstage(
            offstage: screenModel.orgId != null,
            child: Container(
                color: ColorConfig.backgroundColor,
                width: allStatistic.count! > 0 ? 120 : 95,
                child: MediaQuery.removePadding(
                  removeTop: true,
                  removeBottom: true,
                  context: Get.context!,
                  child: ListView.builder(
                      itemCount: tempList.length,
                      itemBuilder: ((context, index) {
                        ListOrgApproveStatisticList dataStatistic =
                            tempList[index];
                        return InkWell(
                          onTap: () {
                            if (index != currentOrgListIndex.value) {
                              groupName = '';
                              modelName = '';
                            }
                            currentOrgListIndex.value = index;
                            DeviceUtils().top.refresh();
                          },
                          child: Container(
                            padding: const EdgeInsets.only(right: 16),
                            alignment: Alignment.center,
                            height: 54,
                            color: currentOrgListIndex.value == index
                                ? ColorConfig.whiteColor
                                : Colors.transparent,
                            child: Row(
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  width: 16,
                                  child: Container(
                                    decoration: BoxDecoration(
                                        color:
                                            currentOrgListIndex.value == index
                                                ? ColorConfig.themeCorlor
                                                : Colors.transparent,
                                        borderRadius:
                                            BorderRadius.circular(1.5)),
                                    width: 3,
                                    height: 14,
                                  ),
                                ),
                                Container(
                                    width: 62,
                                    child: Text(
                                      dataStatistic.name!,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.desTextColor),
                                    )),
                                3.gap,
                                _badge(
                                    Key('$index-${dataStatistic.count}'),
                                    dataStatistic.count!,
                                    SizedBox(
                                      width: 1,
                                      height: 54,
                                    ),
                                    maxShowInt: 99,
                                    top: 18.5,
                                    start: 0)
                              ],
                            ),
                          ),
                        );
                      })),
                )),
          ),
          Expanded(
              child: currentStatisctic!.groups!.isEmpty
                  ? Container(
                      padding: const EdgeInsets.only(left: 15, right: 15),
                      height: 30,
                      alignment: Alignment.center,
                      child: Text(
                        emptyMsg,
                        style: const TextStyle(
                            fontSize: 14, color: ColorConfig.desTextColor),
                      ),
                    )
                  : Container(
                      child: MediaQuery.removePadding(
                      removeTop: true,
                      removeBottom: true,
                      context: Get.context!,
                      child: ListView.builder(
                          padding: const EdgeInsets.only(left: 15, right: 15),
                          itemCount: currentStatisctic!.groups!.length,
                          itemBuilder: ((context, index) {
                            Groups groups = currentStatisctic!.groups![index];
                            String modelName = groups.groupName!;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  height: 38,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    modelName,
                                    style: const TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.mainTextColor),
                                  ),
                                ),
                                GridView.count(
                                  physics: const NeverScrollableScrollPhysics(),
                                  crossAxisCount:
                                      screenModel.orgId != null ? 3 : 2,
                                  crossAxisSpacing: 26,
                                  mainAxisSpacing: 26,
                                  padding: const EdgeInsets.all(0),
                                  childAspectRatio: 2.8,
                                  shrinkWrap: true,
                                  children: backTemplateModelList(
                                      groups.models!,
                                      groups.groupName!,
                                      '$approveDataIndex-$index'),
                                ),
                                21.gap
                              ],
                            );
                          })),
                    )))
        ],
      ),
    );
  }

  backTemplateModelList(List models, String groupsName, String keyStart) {
    List<Widget> lists = [];
    for (var i = 0; i < models.length; i++) {
      Models model = models[i];
      String templateName = model.modelName!;
      int count = model.count!;

      lists.add(InkWell(
        onTap: () {
          groupName = groupsName;
          modelName = templateName;
          DeviceUtils().top.refresh();
        },
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
              padding: const EdgeInsets.only(left: 6, right: 6),
              // height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: groupName == groupsName && modelName == templateName
                      ? ColorConfig.mybackgroundColor
                      : ColorConfig.backgroundColor,
                  borderRadius: BorderRadius.circular(4)),
              child: Text(
                templateName,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    fontSize: 14,
                    color: groupName == groupsName && modelName == templateName
                        ? ColorConfig.themeCorlor
                        : ColorConfig.desTextColor),
              ),
            ),
            Positioned(
                top: 0,
                right: 0,
                child: _badge(
                    Key('$keyStart-$i-$groupName-$templateName-$count'),
                    count,
                    Container(),
                    top: -9,
                    start: count > 99 ? -15 : -12.5,
                    backColor: ColorConfig.lightRedCorlor,
                    textColor: ColorConfig.deleteCorlor))
          ],
        ),
      ));
    }
    return lists;
  }

  _badge(Key key, int count, Widget child,
      {int maxShowInt = 99,
      double top = 0,
      double start = 0,
      Color backColor = ColorConfig.deleteCorlor,
      Color textColor = ColorConfig.whiteColor}) {
    var value = '';
    var shape = badge.BadgeShape.circle;

    if (count <= 0) {
      value = '';
    } else if (count > 99) {
      shape = badge.BadgeShape.square;
      value = '$count';
      if (count > maxShowInt) {
        value = '$maxShowInt+';
      }
    } else {
      value = '$count';
      shape = badge.BadgeShape.circle;
    }
    double fontSize = 10;
    return Padding(
        padding: EdgeInsets.all(0),
        child: badge.Badge(
          // shape: BadgeShape.circle,
          ignorePointer: false,
          badgeAnimation: const badge.BadgeAnimation.scale(
            animationDuration: Duration(seconds: 1),
            colorChangeAnimationDuration: Duration(seconds: 1),
            loopAnimation: false,
            curve: Curves.fastOutSlowIn,
            colorChangeAnimationCurve: Curves.easeInCubic,
          ),
          badgeStyle: badge.BadgeStyle(
            shape: shape,
            badgeColor: backColor,
            padding: EdgeInsets.all(1),
            borderRadius: BorderRadius.circular(12),
            elevation: 0,
          ),
          badgeContent: Container(
            padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
            key: key,
            constraints: BoxConstraints(minWidth: 22),
            child: Center(
              child: Text(
                value,
                style: TextStyle(fontSize: fontSize, color: textColor),
              ),
            ),
          ),
          showBadge: count > 0,
          position: badge.BadgePosition.topStart(top: top, start: start),
          child: child,
        ));
  }

  hide() {
    Get.back();
  }
}
