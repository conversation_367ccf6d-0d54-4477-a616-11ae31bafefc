import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:get/get.dart';

class MeetingShareDialog {
  List dataList = [
   // {'icon': 'assets/images/3.0x/meeting_share_wechat.png', 'name': '微信'},
    {'icon': 'assets/images/3.0x/meeting_share_copyLink.png', 'name': '复制链接'},
    //{'icon': 'assets/images/3.0x/meeting_share_qrCode.png', 'name': '二维码'}
  ];
  Function? onTapIndex;
  show(Function onTap) {
    onTapIndex = onTap;
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Scaffold(
          backgroundColor: Colors.transparent,
          body: Stack(
            children: [
              Container(
                  alignment: Alignment.center, color: ColorConfig.maskColor),
              Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  height: DeviceUtils().bottom.value + 160,
                  child: Container(
                    color: ColorConfig.whiteColor,
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(left: 8, right: 8),
                          height: 52,
                          child: Row(
                            children: [
                              40.gap,
                              Expanded(
                                  child: Container(
                                alignment: Alignment.center,
                                child: const Text(
                                  '分享',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: ColorConfig.mainTextColor),
                                ),
                              )),
                              InkWell(
                                onTap: () {
                                  hide();
                                },
                                child: SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: Image.asset(
                                      'assets/images/3.0x/contact_dept_close.png'),
                                ),
                              )
                            ],
                          ),
                        ),
                        const Divider(
                          height: 1,
                          color: ColorConfig.lineColor,
                        ),
                        Container(
                          padding: const EdgeInsets.only(top: 16),
                          height: 107,
                          child: GridView.count(
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: 4,
                            padding: EdgeInsets.zero,
                            childAspectRatio: 1,
                            shrinkWrap: true,
                            children: backCellWidget(),
                          ),
                        )
                      ],
                    ),
                  ))
            ],
          ),
        ));
  }

  backCellWidget() {
    List<Widget> lists = [];
    for (var i = 0; i < dataList.length; i++) {
      Map dataMap = dataList[i];
      String icon = dataMap['icon'];
      String name = dataMap['name'];
      lists.add(InkWell(
        onTap: () {
          onTapIndex!(i);
          hide();
        },
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                  color: ColorConfig.backgroundColor,
                  borderRadius: BorderRadius.circular(8)),
              width: 56,
              height: 56,
              child: SizedBox(
                width: 28,
                height: 28,
                child: Image.asset(icon),
              ),
            ),
            Container(
              alignment: Alignment.center,
              height: 28,
              child: Text(
                name,
                style: const TextStyle(
                    fontSize: 13, color: ColorConfig.mainTextColor),
              ),
            )
          ],
        ),
      ));
    }
    return lists;
  }

  hide() {
    Get.back();
  }
}
