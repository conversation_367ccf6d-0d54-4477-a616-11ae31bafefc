import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:get/get.dart';

import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/config/config.dart';
import '../../../common/widgets/widgets.dart';
import '../../../utils/http.dart';

class ApproveDialog {
  int status = 0;
  bool isNeedSign = false;
  RxString signUrl = ''.obs; //上次签名
  RxString signPath = ''.obs; // 手写签名
  String fileId = '';
  StreamSubscription? subscription;
  TextEditingController contentController = TextEditingController();
  ApproveDialog(this.status);
  show(String approveId, bool isNeedSign, Function onPressed) {
    subscription = eventBus.on<Map>().listen((event) {
      if (event['eventName'] != null) {
        if (event['eventName'] == 'approvedialog') {
          signPath.value = event['signPath'];
          fileId = event['fileId'];
        }
      }
    });
    if(isNeedSign) getLastSign();
    double screenW = DeviceUtils().width.value;
    double screenH = DeviceUtils().height.value;

    double padding = 20;
    double width = DeviceUtils().width.value - padding * 2;
    double height = 300;

    RxBool isUseLastSign = false.obs;
    if(status == 1) contentController.text = '同意。';
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(() => Scaffold(
              backgroundColor: Colors.transparent,
              body: Stack(
                children: [
                  Container(
                      alignment: Alignment.center, color:ColorConfig.maskColor),
                  Positioned(
                      left: padding,
                      top: (screenH - height) * 0.5,
                      width: width,
                      //  height: height,
                      child: Container(
                        width: double.infinity,
                        //  height: height,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorConfig.whiteColor,
                        ),
                        child: Column(
                          children: [
                            SizedBox(
                              height: 15,
                            ),
                            Container(
                              alignment: Alignment.center,
                              height: 50,
                              child: Text(
                                status == 1 ? '同意审批意见' : '拒绝审批意见',
                                style: TextStyle(
                                    fontSize: 16,
                                    color: status == 1
                                        ? ColorConfig.themeCorlor
                                        : ColorConfig.deleteCorlor),
                              ),
                            ),
                            Container(
                              height: 120,
                              color: ColorConfig.backgroundColor,
                              margin: EdgeInsets.only(left: 15, right: 15),
                              child: TextField(
                                onSubmitted: (value) {},
                                onChanged: (value) {},
                                controller: contentController,
                                maxLines: null,
                                minLines: 1,
                                // maxLength: 30,
                                textInputAction: TextInputAction.done,
                                style: const TextStyle(
                                  color: ColorConfig.mainTextColor,
                                  fontSize: 14,
                                ),

                                decoration: InputDecoration(
                                    contentPadding:
                                        EdgeInsets.only(left: 15, right: 15),
                                    border: OutlineInputBorder(
                                        borderSide: BorderSide.none),
                                    hintText: '请输入',
                                    hintStyle: TextStyle(
                                      color: ColorConfig.desTextColor,
                                      fontSize: 14,
                                    )),
                              ),
                            ),
                            Offstage(
                              offstage: !isNeedSign,
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.all(15),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    signUrl.value.isEmpty
                                        ? Container(
                                            width: 10,
                                          )
                                        : InkWell(
                                            onTap: () {
                                              signPath.value = ''; //点击此按钮清除手写签名
                                              if (isUseLastSign.value) {
                                                isUseLastSign.value = false;
                                              } else {
                                                isUseLastSign.value = true;
                                              }
                                            },
                                            child: Container(
                                              child: Row(
                                                children: [
                                                  Container(
                                                    width: 16,
                                                    height: 16,
                                                    child: Image.asset(isUseLastSign
                                                            .value
                                                        ? 'assets/images/3.0x/login_selected.png'
                                                        : 'assets/images/3.0x/login_unselect.png'),
                                                  ),
                                                  Container(
                                                    child: Text(
                                                      ' 使用上次签名',
                                                      style: TextStyle(
                                                          fontSize: 12,
                                                          color: ColorConfig
                                                              .mainTextColor),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            Channel().invoke(
                                                Channel_Native_Approve_sign,
                                                {'approveId': approveId});
                                          },
                                          child: Container(
                                            child: Text(
                                              '手写签名',
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  color: ColorConfig
                                                      .mainTextColor),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 3,
                                        ),
                                        Container(
                                          width: 9,
                                          height: 17,
                                          child: Image.asset(
                                              'assets/images/3.0x/mine_right.png'),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Offstage(
                              offstage: signPath.value.isEmpty &&
                                  !isUseLastSign.value,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 15,
                                  ),
                                  Container(
                                    child: Text(
                                      '签字:',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.mainTextColor),
                                    ),
                                  ),

                                  signPath.value.isNotEmpty ? ImageLoader(url: signPath.value, height: 40,)
                                    : ImageLoader(url: signUrl.value, height: 40, boxFit: BoxFit.fitHeight,
                                  filterQuality: FilterQuality.high,)

                                ],
                              ),
                            ),
                            Container(
                              height: 44,
                              child: Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      hide();
                                    },
                                    child: Container(
                                      width: width * 0.5,
                                      alignment: Alignment.center,
                                      child: Text(
                                        '取消',
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: ColorConfig.desTextColor),
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      if (isNeedSign) {
                                        if (!isUseLastSign.value &&
                                            signPath.value.isEmpty) {
                                          toast('需要签名后才能进行审批');
                                          return;
                                        }
                                      }
                                      onPressed(contentController.text,
                                          isUseLastSign.value, fileId);
                                      hide();
                                    },
                                    child: Container(
                                      width: width * 0.5,
                                      alignment: Alignment.center,
                                      child: Text(
                                        status == 1 ? '同意' : '拒绝',
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: status == 1
                                                ? ColorConfig.themeCorlor
                                                : ColorConfig.deleteCorlor),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ))
                ],
              ),
            )));
  }

  hide() {
    Get.back();
  }

  getLastSign() async {
    DioUtil().get(ApproveApi.APPROVELASTSIGN, null, true, () {
    }).then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        signUrl.value = data['data'];
      } else {
        if (data['code'] != 2003) {
          toast('${data['msg']}');
        } else {
          signUrl.value = '';
        }
      }
    });
  }
}
