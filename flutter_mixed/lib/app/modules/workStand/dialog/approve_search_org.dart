import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/workStand/models/approve_screen_model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:badges/badges.dart' as badge;
import 'package:kumi_popup_window/kumi_popup_window.dart';

class ApproveSearchOrgDialog {
  List<ApproveScreenModel> allDataList = [];
  RxList<ListOrgApproveStatisticList> orgList =
      <ListOrgApproveStatisticList>[].obs; //当前选中系中的公司
  String groupId = '';
  String orgId = '';
  String corgId = '';
  String groupName = '';
  Map? selectMap; //选中的数据
  ApproveSearchOrgDialog({this.selectMap});
  double screenWidth = DeviceUtils().width.value;
  double height = DeviceUtils().height.value -
      DeviceUtils().top.value -
      44 -
      142 -
      DeviceUtils().bottom.value; //剩余高度
  double topPadding = 8.0;
  double containerHeight = 36.0;
  double bottomPadding = 8.0;

  show(List<ApproveScreenModel> totalList, Function onSurePressed) {
    allDataList = List.from(totalList);

    if (selectMap != null) {
      if (selectMap!['groupId'] != null) {
        groupId = selectMap!['groupId'];
      }
      if (selectMap!['orgId'] != null) {
        orgId = selectMap!['orgId'];
      }
      if (selectMap!['corgId'] != null) {
        corgId = selectMap!['corgId'];
      }
    }
    if (groupId.isNotEmpty) {
      for (var i = 0; i < allDataList.length; i++) {
        ApproveScreenModel model = allDataList[i];
        if (model.groupId == groupId) {
          groupName = model.name!;
          for (var j = 0; j < model.listOrgApproveStatisticList!.length; j++) {
            ListOrgApproveStatisticList orgModel =
                model.listOrgApproveStatisticList![j];
            if (orgModel.orgId == orgId && orgModel.corgId == corgId) {
              orgList.value = model.listOrgApproveStatisticList!;
              break;
            }
          }
        }
      }
    }
    double sizeHeight = (containerHeight + bottomPadding) * 7.5 + topPadding;
    double realHeight =
        (containerHeight + bottomPadding) * allDataList.length + topPadding;

    if (height < realHeight) {
      sizeHeight = height;
    } else {
      if (realHeight > sizeHeight) {
        sizeHeight = realHeight;
      }
    }
    showPopupWindow(
      Get.context!,
      gravity: KumiPopupGravity.centerTop,
      bgColor: const Color(0x88000000),
      clickOutDismiss: true,
      clickBackDismiss: true,
      customAnimation: false,
      customPop: false,
      customPage: false,
      underStatusBar: false,
      underAppBar: true,
      offsetX: 10,
      offsetY: 142,
      duration: const Duration(milliseconds: 200),
      childSize: Size(screenWidth - 20, sizeHeight),
      childFun: (pop) {
        return Container(
          padding: EdgeInsets.only(top: topPadding),
          width: screenWidth - 20,
          height: sizeHeight,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 7,
                offset: const Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: Obx(() => Row(
                children: orgList.isNotEmpty
                    ? [
                        _backLeftWidget(onSurePressed, true),
                        Container(
                          width: 1,
                          height: double.infinity,
                          color: ColorConfig.backgroundColor,
                        ),
                        Expanded(
                            flex: 2,
                            child: MediaQuery.removePadding(
                                removeTop: true,
                                context: Get.context!,
                                child: ListView.builder(
                                    itemCount: orgList.length,
                                    itemBuilder: ((context, index) {
                                      ListOrgApproveStatisticList orgModel =
                                          orgList[index];
                                      bool isSelect =
                                          groupId == orgModel.groupId &&
                                              orgId == orgModel.orgId &&
                                              corgId == orgModel.corgId;
                                      return InkWell(
                                          onTap: () {
                                            onSurePressed({
                                              'groupId': orgModel.groupId,
                                              'orgId': orgModel.orgId,
                                              'corgId': orgModel.corgId,
                                              'name': orgModel.orgId!.isNotEmpty
                                                  ? orgModel.name
                                                  : groupName
                                            });
                                            hide();
                                          },
                                          child: Column(
                                            children: [
                                              Container(
                                                alignment: Alignment.center,
                                                height: containerHeight +
                                                    2 * bottomPadding,
                                                margin: const EdgeInsets.only(
                                                    left: 8, right: 8),
                                                padding: const EdgeInsets.only(
                                                    left: 16, right: 16),
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                    color: isSelect
                                                        ? ColorConfig
                                                            .mybackgroundColor
                                                        : ColorConfig
                                                            .backgroundColor),
                                                child: Text(
                                                  orgModel.name!,
                                                  maxLines: 2,
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      color: isSelect
                                                          ? ColorConfig
                                                              .themeCorlor
                                                          : ColorConfig
                                                              .mainTextColor),
                                                ),
                                              ),
                                              bottomPadding.gap
                                            ],
                                          ));
                                    }))))
                      ]
                    : [_backLeftWidget(onSurePressed, false)],
              )),
        );
      },
    );
  }

  _backLeftWidget(Function onSurePressed, bool isLeft) {
    return Expanded(
        flex: 1,
        child: MediaQuery.removePadding(
            removeTop: true,
            context: Get.context!,
            child: ListView.builder(
                itemCount: allDataList.length,
                itemBuilder: ((context, index) {
                  ApproveScreenModel model = allDataList[index];
                  bool isSelect = false;
                  if (groupId.isNotEmpty) {
                    isSelect = groupId == model.groupId;
                  } else {
                    isSelect = orgId == model.orgId && corgId == model.corgId;
                  }

                  return InkWell(
                      onTap: () {
                        if (model.groupId!.isEmpty) {
                          onSurePressed({
                            'groupId': '',
                            'orgId': model.orgId,
                            'corgId': model.corgId,
                            'name': model.name
                          });
                          hide();
                        } else {
                          groupId = model.groupId!;
                          groupName = model.name!;
                          orgList.value = model.listOrgApproveStatisticList!;
                        }
                      },
                      child: _backTextWidget(model.name!, isSelect, isLeft));
                }))));
  }

  _backTextWidget(String text, bool isSelect, bool isLeft) {
    Color outColor = ColorConfig.whiteColor;
    Color inColor = ColorConfig.whiteColor;
    if (isLeft) {
      if (isSelect) {
        outColor = ColorConfig.mybackgroundColor;
        inColor = ColorConfig.mybackgroundColor;
      }
    } else {
      if (isSelect) {
        outColor = ColorConfig.whiteColor;
        inColor = ColorConfig.mybackgroundColor;
      }
    }
    return Column(
      children: [
        Container(
          width:double.infinity,
          height: containerHeight,
          padding: const EdgeInsets.only(left: 8, right: 8),
          color: outColor,
          child: Container(
            alignment: isLeft ? Alignment.centerLeft : Alignment.center,
            padding: const EdgeInsets.only(left: 8, right: 8),
            width:double.infinity,
            height: double.infinity,
            color: inColor,
            child: Text(
              text,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: 13,
                  color: isSelect
                      ? ColorConfig.themeCorlor
                      : ColorConfig.mainTextColor),
            ),
          ),
        ),
        bottomPadding.gap
      ],
    );
  }

  hide() {
    Get.back();
  }
}
