import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/manage/company_model.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../../common/api/Define.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/http.dart';
import '../../../../utils/storage.dart';
import '../../../contact/model/org/org_model.dart';
import '../../approveListPage/views/approve_list_page_view.dart';
import '../../workFlow/controllers/work_flow_controller.dart';

// 审批中心 controller
class ApproveListController extends GetxController {
  StreamSubscription? subscription;
  StreamSubscription? subscription1;
  TextEditingController searchController = TextEditingController();
  FocusNode node = FocusNode();
  List totalList = [
    {'listType': 1, 'name': '全部审批'},
    {'listType': 2, 'name': '待办'},
    {'listType': 3, 'name': '已办'},
    {'listType': 4, 'name': '已发起'},
    {'listType': 5, 'name': '抄送我'}
  ];
  RxList tabList = [].obs;
  List modellList = [];
  String keyword = '';
  String orgId = '';

  RxInt currentIndex = 0.obs;

  OrgModel? orgModel;
  RxList unreadCountList = [].obs;

  int type = 0; //0审批模版跳转1im跳转2合作企业跳转4审批中心
  bool isSearch = false; //是否为搜索模式
  RxBool isNative = false.obs; //是否是原生跳转
  RxInt imListType = 1.obs; //im跳转到指定的listType

  RxInt currentListType = 0.obs; //当前选中的类型
  RxBool isShowCancel = false.obs; //是否显示取消选择按钮

  TabController? tabController;

  PageController pageController = PageController();

  List<Widget> tabWidgets = [];

  bool isCentre = false;

  CancelToken? unreandCancelToken;
  @override
  void onInit() async {
    super.onInit();
    logger('审批中心 init..........');
    if (Get.arguments != null) {
      if (Get.arguments['orgId'] != null) {
        orgId = Get.arguments['orgId'];
      }

      if (Get.arguments['type'] != null) {
        type = Get.arguments['type'];
      }
      if (Get.arguments['isSearch'] != null) {
        isSearch = Get.arguments['isSearch'];
      }
      if (Get.arguments['currentIndex'] != null) {
        currentIndex.value = Get.arguments['currentIndex'];
      }
      if (Get.arguments['model'] != null) {
        orgModel = Get.arguments['model'];
        dealListTypeData();
      } else {
        if (Get.arguments['isFromWork'] != null) {
          int isFromWork = Get.arguments['isFromWork'];
          if (isFromWork == 1) {
            WorkFlowController workFlowController = Get.find();
            orgModel = workFlowController.currentModel;
          }
        }
        dealListTypeData();
      }
    }

    if (orgId == "" || orgId == null) {
      orgModel = CompanyModelHelper.orgModel;
      orgId = CompanyModelHelper.orgModel?.companyId ?? '';
      await CompanyModelHelper.createCompanyModel();
      dealListTypeData();
    }

    subscription1 =
        eventBus.on<EventRefreshApproveListTabIndex>().listen((event) {
      if (type == 4) return;
      try {
        currentIndex.value = event.index;
        tabController?.animateTo(event.index);
        update();
      } catch (e) {}
    });

    subscription = eventBus.on<Map>().listen((event) async {
      //logger('event====$event');
      if (event['refreshUnreadCount'] != null) {
        if (event['refreshUnreadCount'] == 2) {
          //强刷
          getAllUnread(mustRefresh: true);
        } else {
          if (unreandCancelToken != null) {
            unreandCancelToken!.cancel();
          }
          getAllUnread();
        }
      }
      if (event['native'] == 1) {
        if (event['route'] == Routes.APPROVE_LIST) {
          isNative.value = true;
          type = 1;

          Map? nativeArgument;
          if (event['arguments'] is String) {
            nativeArgument = jsonDecode(event['arguments']);
          } else {
            nativeArgument = event['arguments'];
          }
          orgId = nativeArgument!['orgId'];
          imListType.value = nativeArgument['listType'];

          //处理公司数据
          Map dataDic = await UserDefault.getData(Define.ORGLIST);
          List companies = [];
          if (dataDic['companies'] != null) {
            companies = dataDic['companies'];
          }
          for (var element in companies) {
            if (element['companyId'] == orgId) {
              Map<String, dynamic> orgDic = element;
              orgModel = OrgModel.fromJson(orgDic);
            }
          }

          dealListTypeData();
          getAllUnread();
          getAllApproveListModel();
        }
      }
      if (event['approveListView_dealAuthList'] != null) {
        if (orgId == "" || orgId == null) {
          orgModel = CompanyModelHelper.orgModel;
          orgId = CompanyModelHelper.orgModel?.companyId ?? '';
        }

        dealListTypeData();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    if (isSearch) {
      node.requestFocus();
    }
    getAllUnread();
    getAllApproveListModel();
  }

  @override
  void onClose() {
    // Get.delete<ApproveListController>(force: true);
    subscription?.cancel();
    // searchController?.dispose();
    // tabController?.dispose();
    subscription1?.cancel();
    super.onClose();
  }

  //处理数据
  dealListTypeData() async {
    List authList = [2, 3, 4, 5];

    //合作企业单独判断固定2，3，5
    if (type == 2) {
      authList = [2, 3, 5];
    } else {
      if (authList.isNotEmpty) {
        authList = CompanyModelHelper.companyModelList;
        if (!isSearch) {
          if (type != 4) {
            currentIndex.value = CompanyModelHelper.approveCenterTabIndex;
          }
        }
      } else {
        dynamic dataDic = await UserDefault.getData(Define.ORGLIST);
        List companies = [];
        if (dataDic['companies'] != null) {
          companies = dataDic['companies'];
        }
        for (var i = 0; i < companies.length; i++) {
          Map orgMap = companies[i];
          String power = orgMap['power'];
          if (orgMap['deptId'] == '0') {
            authList = [2, 3, 4, 5, 1];
            break;
          } else {
            if (power.contains('-1') || power.contains('7')) {
              authList = [2, 3, 4, 5, 1];
              break;
            }
          }
        }
      }
    }
    tabList.clear();
    for (var i = 0; i < authList.length; i++) {
      int listType = authList[i];
      if (listType == imListType.value && isNative.value) {
        currentIndex.value = i;
      }
      for (var j = 0; j < totalList.length; j++) {
        Map totalMap = totalList[j];
        if (totalMap['listType'] == listType) {
          tabList.add(totalMap);
        }
      }
    }
    if (tabList.isNotEmpty) {
      Map tabMap = tabList[currentIndex.value];
      currentListType.value = tabMap['listType'];
    }

    // tabList.refresh();
    update();
    getListPageView();
  }

  //获取筛选模版
  getAllApproveListModel() {
    if (type != 2) {
      return;
    }
    DioUtil()
        .get('${ApproveApi.APPROVELISTALLMODEL}/$orgId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
        modellList = data['data'];
        modellList.insert(0, {'modelId': '', 'modelName': '全部审批'});
      } else {
        toast('${data['msg']}');
      }
    });
  }

//获取未读
  getAllUnread({bool mustRefresh = false}) {
    String url = '${ApproveApi.APPROVEHOMEPAGEUNREAD}/$orgId';
    if (type != 2) {
      url = ApproveApi.APPROVEHOMEPAGEUNREADV2;
    }
    unreandCancelToken = CancelToken();
    if (mustRefresh) {
      unreandCancelToken = null; //强刷不受token影响
    }

    DioUtil()
        .get(url, null, true, () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: unreandCancelToken)
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataMap = data['data'];
        List countList = [
          dataMap['allRedPointCount'],
          dataMap['backlogRedPointCount'],
          dataMap['finishRedPointCount'],
          dataMap['createRedPointCount'],
          dataMap['ccRedPointCount']
        ];
        int totalCount = 0;
        for (var i = 0; i < countList.length; i++) {
          dynamic unreadCount = countList[i];
          int temp = unreadCount ?? 0;
          totalCount += temp;
        }

        //Map unreadMap = await UserDefault.getData(Define.APPROVE_TOTAL_LUNREADKEY);
        //unreadMap[orgId] = totalCount;
        Map unreadMap = {};
        unreadMap['total'] = totalCount;
        await UserDefault.setData(Define.APPROVE_TOTAL_LUNREADKEY, unreadMap);
        bool isHaveWorkFlow = Get.isRegistered<WorkFlowController>();
        if (isHaveWorkFlow) {
          WorkFlowController workController = Get.find();
          workController.dealApproveUnreadCount();
        }
        unreadCountList.value = countList;
        //tabList.refresh();
        logger('unreadCountList===');
        update();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //处理按钮逻辑
  settingRightButton(int listType) {
    currentListType.value = listType;
    //currentListType.refresh();
    update();
    eventBus.fire({
      'listPage_IsCancelChoose': 1,
      'isSearch': isSearch,
      'controller': this
    });
    if (listType == 2) {
      eventBus.fire({'list_viewShowAll': 1});
    }
  }

  getListPageView() {
    logger('getListPageView===$tabList');
    List<Widget> lists = [];

    for (var i = 0; i < tabList.length; i++) {
      Map tabMap = tabList[i];
      lists.add(ApproveListPageView({
        'listType': tabMap['listType'],
        'orgId': orgId,
        'type': type,
        'isSearch': isSearch,
        'approveListController': this
      }));
    }
    tabWidgets = lists;
    update();
  }
}

class EventRefreshApproveListTabIndex {
  int index = 0;
  EventRefreshApproveListTabIndex(this.index);
}
