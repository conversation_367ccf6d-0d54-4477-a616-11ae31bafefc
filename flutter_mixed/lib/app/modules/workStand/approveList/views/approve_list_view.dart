import 'dart:io';

import 'package:badges/badges.dart' as badge;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

import '../../../../../main.dart';
import '../../../../../plugin/focus_detector/focus_detector.dart';
import '../../../../common/channel/channel.dart';
import '../../../../common/config/config.dart';
import '../controllers/approve_list_controller.dart';

/// 审批中心( 1 【审批】模块的tab ； 2 单独审批中心页面（公司跳转）)
class ApproveListView extends GetView<ApproveListController> {
  ApproveListView({Key? key, this.isCentre, this.approveListController})
      : super(key: key);

  ApproveListController? approveListController;

  bool? isCentre; //是否是审批中心

  @override
  Widget build(BuildContext context) {
    approveListController ??= controller;

    if (isCentre == true) {
      approveListController!.type = 4;
    }

    if (isCentre == true) {
      return _buildWidget(context);
    }

    return FocusDetector(
        onFocusGained: () {
          if (approveListController!.isNative.value && Platform.isIOS) {
            Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 1});
          }

          //后端暂无触发机制，后改
          approveListController!.getAllUnread();
          eventBus.fire({'listPage_refreshScreenData': 1});
          //eventBus.fire({'listPage_kingdee_status': 1});
        },
        onFocusLost: () {
          bool isHave = Get.isRegistered<ApproveListController>();
          if (isHave) {
            if (approveListController!.isNative.value &&
                Platform.isIOS &&
                isHave) {
              Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 0});
            }
          }
        },
        child: _buildWidget(context));
  }

  Widget _buildWidget(BuildContext context) {
    return GetBuilder(
        global: false,
        init: approveListController,
        builder: (logic) {
          return Scaffold(
            backgroundColor: ColorConfig.backgroundColor,
            extendBodyBehindAppBar: false,
            appBar: TitleBar().backAppbar(
                context,
                '审批中心',
                isCentre == true,
                approveListController!.currentListType.value != 2
                    ? []
                    : approveListController!.isShowCancel.value
                        ? [
                            Container(
                              width: 100,
                              child: CupertinoButton(
                                  padding:
                                      const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                  pressedOpacity: 0.5,
                                  child: const Text(
                                    '取消选择',
                                    style: TextStyle(
                                        color: ColorConfig.themeCorlor,
                                        fontSize: 14),
                                  ),
                                  onPressed: () {
                                    approveListController!.isShowCancel.value =
                                        false;
                                    approveListController!.settingRightButton(
                                        approveListController!
                                            .currentListType.value);
                                    approveListController!.isShowCancel
                                        .refresh();
                                  }),
                            )
                          ]
                        : [], onPressed: () {
              if (approveListController!.isNative.value) {
                Channel().invoke(Channel_Native_Back, {});
                approveListController!.onClose();
              } else {
                Get.back();
              }
            }),
            body: approveListController!.orgId == ''
                ? Container(
                    color: approveListController!.tabList.length == 1
                        ? ColorConfig.whiteColor
                        : ColorConfig.whiteColor,
                  )
                : Column(
                    children: [
                      Container(
                        width: double.infinity,
                        color: ColorConfig.whiteColor,
                        height: 50,
                        padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                        child: SettingWidget().backSearchWidget(
                            approveListController!.searchController,
                            approveListController!.node,
                            '输入关键词查找审批', onSub: (value) {
                          approveListController!.keyword = value;
                          eventBus.fire({'keyword': value});
                        }, onTap: () {
                          if (!approveListController!.isSearch) {
                            approveListController!.node.unfocus();
                            Get.toNamed(Routes.APPROVE_LIST,
                                arguments: {
                                  'type': approveListController!.type,
                                  'isSearch': true,
                                  'orgId': approveListController!.orgId,
                                  'currentIndex':
                                      approveListController!.currentIndex.value
                                },
                                preventDuplicates: false);
                          }
                        }, onChange: (value) {
                          approveListController!.keyword = value;
                          eventBus.fire({'keyword': value, 'isLoad': false});
                        }),
                      ),
                      Expanded(
                          child: DefaultTabController(
                              initialIndex:
                                  approveListController!.currentIndex.value,
                              length: approveListController!.tabList.length,
                              child: Builder(builder: (context) {
                                approveListController!.tabController =
                                    DefaultTabController.of(context);

                                approveListController!.tabController!
                                    .addListener(() {
                                  approveListController!.currentIndex.value =
                                      approveListController!
                                          .tabController!.index;
                                  Map tabMap = approveListController!.tabList[
                                      approveListController!
                                          .currentIndex.value];

                                  approveListController!
                                      .settingRightButton(tabMap['listType']);
                                  
                                });
                                return Column(
                                  children: [
                                    Container(
                                        color: ColorConfig.whiteColor,
                                        height: 48,
                                        padding: EdgeInsets.only(
                                            left: 15, right: 15),
                                        child: TabBar(
                                          onTap: (value) {},
                                          indicatorColor:
                                              ColorConfig.themeCorlor,
                                          indicatorWeight: 1,
                                          labelPadding: EdgeInsets.all(0),
                                          tabs: backTabbar(),
                                          isScrollable: true,
                                          dividerHeight: 0,
                                          tabAlignment: TabAlignment.start,
                                        )),
                                    Expanded(
                                        child: SizedBox(
                                            width: double.infinity,
                                            // child: PageView.builder(
                                            //      itemCount: approveListController?.tabWidgets.length,
                                            //      onPageChanged: (index){
                                            //        approveListController?.tabController?.animateTo(index);
                                            //      },
                                            //     controller: approveListController?.pageController,
                                            //     itemBuilder: (ctx , index){
                                            //   return approveListController?.tabWidgets[index];
                                            // })
                                            child: TabBarView(
                                                children: approveListController
                                                        ?.tabWidgets ??
                                                    []))),
                                    //
                                  ],
                                );
                              }))),
                    ],
                  ),
          );
        });
  }

  List<Widget> backTabbar() {
    List<Widget> list = [];
    for (var i = 0; i < approveListController!.tabList.length; i++) {
      int count = 0;
      Map tabMap = approveListController!.tabList[i];
      int listType = tabMap['listType'];
      if (approveListController!.unreadCountList.isNotEmpty) {
        count = approveListController!.unreadCountList[listType - 1];
      }
      Widget tabText = Container(
          alignment: Alignment.center,
          width: (DeviceUtils().width.value - 30) /
              approveListController!.tabList.length,
          child: !approveListController!.isSearch
              ? _badge(
                  Key('$i-$count'),
                  count,
                  Text(
                    tabMap['name'],
                    style: TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  ),
                  isAll: listType == 1)
              : Text(
                  tabMap['name'],
                  style:
                      TextStyle(color: ColorConfig.mainTextColor, fontSize: 14),
                ));
      list.add(tabText);
    }
    return list;
  }

  _badge(Key key, int count, Widget child, {bool isAll = false}) {
    var value = '';
    var shape = badge.BadgeShape.circle;
    if (count <= 0) {
      value = '';
    } else if (count > 99) {
      value = '99+';
      shape = badge.BadgeShape.square;
    } else {
      value = '$count';
      shape = badge.BadgeShape.circle;
    }
    double fontSize = 10;
    return Padding(
        padding: EdgeInsets.all(0),
        child: badge.Badge(
          // shape: BadgeShape.circle,
          ignorePointer: false,
          badgeAnimation: const badge.BadgeAnimation.scale(
            animationDuration: Duration(seconds: 1),
            colorChangeAnimationDuration: Duration(seconds: 1),
            loopAnimation: false,
            curve: Curves.fastOutSlowIn,
            colorChangeAnimationCurve: Curves.easeInCubic,
          ),
          badgeStyle: badge.BadgeStyle(
            shape: shape,
            badgeColor: ColorConfig.deleteCorlor,
            padding: EdgeInsets.all(1),
            borderRadius: BorderRadius.circular(12),
            // borderSide: BorderSide(color: ColorConfig.deleteCorlor, width: 1),
            elevation: 0,
          ),
          badgeContent: Container(
            padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
            key: key,
            constraints: BoxConstraints(minWidth: 22),
            child: Center(
              child: Text(
                value,
                style: TextStyle(fontSize: fontSize, color: Colors.white),
              ),
            ),
          ),
          showBadge: count > 0,
          position: badge.BadgePosition.topEnd(
              top: -6, end: count > 99 && !isAll ? -23 : -20),
          child: child,
        ));
  }
}
