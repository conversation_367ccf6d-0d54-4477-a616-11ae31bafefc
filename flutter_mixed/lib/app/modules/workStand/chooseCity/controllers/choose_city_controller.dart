import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:get/get.dart';

class ChooseCityController extends GetxController {

  RxList dataList = [].obs;
  RxList sonList = [].obs;
  RxString cityString = ''.obs;

  RxInt faIndex = 0.obs;
  RxInt sonIndex = 0.obs;
 
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    getCitysData();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getCitysData() async {
    String jsonString = await rootBundle.loadString('assets/city.json');
    Map<String, dynamic> rootMap = jsonDecode(jsonString);
    List cityList = rootMap['root']['province'];
    for (var i = 0; i < cityList.length; i++) {
      Map currentDic = cityList[i];
      String province = currentDic['nameA'];
      if (province.contains('北京') || province.contains('天津') || province.contains('上海') || province.contains('重庆') || province.contains('香港') || province.contains('澳门') || province.contains('台湾')) {

        currentDic['name'] = [{'nameA': currentDic['nameA']}];

      }
    }
    print('cityList ----- $cityList');
    Map currentDic = cityList[0];
    sonList.value = currentDic['name'];
    Map sonDic = sonList[0];
    cityString.value = sonDic['nameA'];
    dataList.value = List.from(cityList);
  }
}
