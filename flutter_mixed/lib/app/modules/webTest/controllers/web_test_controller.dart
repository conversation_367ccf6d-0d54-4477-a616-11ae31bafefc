import 'dart:io';

import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../common/base_info/info.dart';
import '../../../common/channel/channel.dart';

class WebTestController extends GetxController {

  final count = 0.obs;
  String companyId = '';
  @override
  void onInit() {
    super.onInit();
    companyId = Get.arguments['companyId'];
    logger('web==test==companyId===$companyId');
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void increment() => count.value++;

    //模块入口跳转逻辑
  workToolJump(String url) async {

    //获取platform
    int platform = 1;
    if (Platform.isIOS) {
      platform = 2;
    }
    //获取appVersion
    String appVersion = await BaseInfo().getAppVersion();

    if (url.contains('?')) {
      url = '$url&platform=$platform&appVersion=$appVersion';
    } else {
      url = '$url?platform=$platform&appVersion=$appVersion';
    }

    openWebView({
      'url': url,
      'title': '',
      'isWebNavigation': 1,
      'orgId': companyId
    });
  }
}
