import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';

import 'package:get/get.dart';

import '../../../common/config/config.dart';
import '../controllers/web_test_controller.dart';

class WebTestView extends GetView<WebTestController> {
  const WebTestView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          height: 50,
          margin: EdgeInsets.only(left: 15, right: 15),
          padding: EdgeInsets.only(left: 15, right: 15),
          decoration: BoxDecoration(
            border: Border.all(width: 1,color: ColorConfig.themeCorlor),
            borderRadius: BorderRadius.circular(4)
          ),
          child: TextField(
            onSubmitted: (value) {
              controller.workToolJump(value);
            },
            textInputAction: TextInputAction.done,
            style: const TextStyle(
              color: ColorConfig.mainTextColor,
              fontSize: 17,
            ),
            decoration: InputDecoration(
                contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                border: const OutlineInputBorder(borderSide: BorderSide.none),
                hintText: '请输入地址',
                hintStyle: const TextStyle(
                  color: ColorConfig.desTextColor,
                  fontSize: 17,
                )),
          ),
        ),
      ),
    );
  }
}
