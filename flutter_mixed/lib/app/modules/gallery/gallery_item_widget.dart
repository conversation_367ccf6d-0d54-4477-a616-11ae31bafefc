
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/im/utils/file_util.dart';

import '../../common/cos/cos_manager.dart';
import '../../im/ui/chat/common/picture_manager.dart';
import 'gallery.dart';

class DownloadablePhotoView extends StatefulWidget {
  final GalleryItem item;

  const DownloadablePhotoView({super.key, required this.item});

  @override
  State createState() => _DownloadablePhotoViewState();
}

class _DownloadablePhotoViewState extends State<DownloadablePhotoView> {

  @override
  void initState() {
    super.initState();
    _downPreview();
  }

  _downPreview() async {
    var g = widget.item;
    String? path = await FileThumbHelper.previewPathByFileId(g.fileId);
    var exist = await FileUtil.isExist(path);
    if(!exist) {
       CosDownLoadUtil.cacheThumb(g.fileId, (prop) async {

      } , success: (){
             if(mounted) {
               setState(() {
                 g.isDownloaded = true;
               });
             }
           },

           folder: IMAGE_PREVIEW_FOLDER);
    }else {
      if(mounted) {
        setState(() {
        g.isDownloaded = true;
      });
      }
    }
  }

  @override
  Widget build(BuildContext context) {

    if(!widget.item.isDownloaded){
      return const Center(child: CircularProgressIndicator());
    }

    return FutureBuilder(future: _buildPreviewUrl(widget.item.fileId), builder: (ctx ,snap){
      return ImageLoader(
        url: snap.data,
        isCircle: false,
        radius: 0,
      );
    });
  }

  Future _buildPreviewUrl(String? fileId) async {
    String? path = await FileThumbHelper.previewPathByFileId(fileId);
    return path;
  }
}
