import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/widget/some_widget.dart';
import 'package:flutter_mixed/app/modules/helper/photo_manager.dart';
import 'package:flutter_mixed/app/utils/http.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

import '../../../main.dart';
import '../../common/config/config.dart';
import '../../common/event/event.dart';
import '../../common/widgets/widgets.dart';
import '../../im/im_translate_helper.dart';
import '../../im/route/route_helper.dart';
import '../../routes/app_pages.dart';
import '../../../logger/logger.dart';
import 'gallery_item_widget.dart';


/// getx 暂不支持 hero， 所以独立使用画廊
class GalleryPage extends StatefulWidget {

  final List<GalleryItem> imageUrls;
  int currentIndex;

  GalleryPage(this.imageUrls, this.currentIndex, {super.key});

  @override
  State<StatefulWidget> createState() => _GalleryState();
}

class _GalleryState extends State<GalleryPage> {

  StreamSubscription? _withDrawSubscription;
  bool isHttp = false;
  @override
  void initState() {
    super.initState();
    var item = widget.imageUrls[0];
    if (!StringUtil.isEmpty(item.url)) {
      if (item.url!.startsWith('http')) {
        isHttp = true;
      }
    }

    _withDrawSubscription = eventBus.on<EventWithDraw>().listen((withDrawData){
      var currentItem = widget.imageUrls[widget.currentIndex];
      if(currentItem.msgId == withDrawData.message.msgId){
        toast('消息已撤回');
        Navigator.of(context).pop();
      }else {
        setState(() {
          widget.imageUrls.removeWhere((t)=> t.msgId == withDrawData.message.msgId);
        });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _withDrawSubscription?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return ToolBar(
      appBarBackgroundColor: ColorConfig.blackColor,
      backgroundColor: ColorConfig.blackColor,
      titleWidget:  Text(isHttp ? '' : '查看图片', style: TextStyle(color: Colors.white , fontSize: 15),),
      body: InkWell(
        onTap: () => Navigator.of(context).pop(),
        onLongPress: (){
          if (isHttp) {
            _saveAvatar();
          }else{
            _showMenu();
          }
        },
        child: Column(
          children: [
            Expanded(child: PhotoViewGallery.builder(
              scrollPhysics: const BouncingScrollPhysics(),
              builder: (BuildContext context, int index) {
                var item = widget.imageUrls[index];

                return PhotoViewGalleryPageOptions.customChild(
                    child: isHttp ? ImageLoader(
                      url: item.url,
                      isCircle: false,
                      radius: 0,
                      boxFit: BoxFit.contain,
                    ): DownloadablePhotoView(item: item),
                    // heroAttributes: PhotoViewHeroAttributes(tag: widget.imageUrls[index].msgId ?? ''),
                    initialScale: PhotoViewComputedScale.contained * 0.95,
                );
              },
              itemCount: widget.imageUrls.length,
              onPageChanged: (int index) {

                widget.currentIndex = index;
                setState(() {});
              },
              pageController:
              PageController(initialPage: widget.currentIndex),
            )),
            Offstage(
              offstage: isHttp,
              child: Container(
              alignment: Alignment.bottomCenter,
              padding: const EdgeInsets.only(bottom: 20),
              child: Text('${widget.currentIndex + 1}/${widget.imageUrls.length}' , style: const TextStyle(fontSize: 15 , color: Colors.white),),
            ),
            ),
          ],
        ),
      )
    );
  }

  _saveAvatar(){
    Get.bottomSheet(
        Container(
          width: double.infinity,
          color: Colors.white,
          padding: EdgeInsets.only(bottom: 15),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: double.infinity,
                child: TextButton(onPressed: () {
                  _getDownLoadPath();
                }, child: const Text('保存图片', style: TextStyle(fontSize: 16),)),
              ),

              line,

              SizedBox(
                width: double.infinity,
                child: TextButton(
                    onPressed: (){
                      Get.back();
                    }, child: const Text('取消' ,style: TextStyle(fontSize: 16))),
              ),
            ],
          ),
        )
    );
  }

  _getDownLoadPath() async {
    Get.back();
    var url = widget.imageUrls[widget.currentIndex].url;
    if(StringUtil.isEmpty(url)) return;
    List startList = url!.split('/');
    String endPath = '${startList.last}';
    String docment = (await getApplicationDocumentsDirectory()).path;
    String savePath = '$docment/mine/images/$endPath';
    String loadPath = '$docment/mine/load/images/$endPath';
    File saveFile = File(savePath);
    if (saveFile.existsSync()) {
      _saveImage(savePath);
    } else {
      Directory directory = Directory('$docment/mine/images');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      Directory loadDirectory =
          Directory('$docment//mine/load/images');
      if (!await loadDirectory.exists()) {
        await loadDirectory.create(recursive: true);
      } else {
        File loadFile = File(loadPath);
        if (loadFile.existsSync()) {
          await loadFile.delete();
        }
      }
      _downLoadFiel(url, savePath, loadPath);
    }
  }

  _downLoadFiel(String url, String savePath, loadPath) {
    DioUtil().downLoadFile(url, savePath, loadPath, () {
      toast('获取图片失败');
    }, (finishPath) {
      _saveImage(finishPath);
    });
  }

  _saveImage(String savePath) async{
    var r = await PhotoHelper.saveImageUrlToGallery(savePath);
    if(r){
      toast('保存成功');
    }else{
      toast('保存失败');
    }
  }

  _showMenu() {
    Get.bottomSheet(
        Container(
          width: double.infinity,
          color: Colors.white,
          padding: EdgeInsets.only(bottom: 15),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: double.infinity,
                child: TextButton(onPressed: () async {
                      Get.back();
                      var path = widget.imageUrls[widget.currentIndex].url;
                      var r = await PhotoHelper.saveImageUrlToGallery(path);
                      if(r){
                        toast('保存成功');
                      }else{
                        toast('保存失败');
                      }
                }, child: const Text('保存图片', style: TextStyle(fontSize: 16),)),
              ),

              line,

              SizedBox(
                width: double.infinity,
                child: TextButton(onPressed: () async {
                  Get.back();
                  transLate(widget.imageUrls[widget.currentIndex].msgId ??'');
                }, child: const Text('转发图片', style: TextStyle(fontSize: 16),)),
              ),

              line,

              SizedBox(
                width: double.infinity,
                child: TextButton(
                    onPressed: (){
                      Get.back();
                    }, child: const Text('取消' ,style: TextStyle(fontSize: 16))),
              ),
            ],
          ),
        )
    );
  }

  transLate(String msgId) async {
    var uid = await UserHelper.getUid();
    var msgList = await DbHelper.getMessageByUidAndMsgId(uid, msgId);
    if(msgList.isEmpty) return;
    var msg = msgList.first;
    // var session = await DbHelper.getSessionByOwnerId2SessionId(uid, msg.sessionId ??'');
    // if(session == null) return;

    // 单条转发
    var params = {
      'type': 2,
      'forwardType': 1, // 单条转发
      'forwardText': '',
    };
    var trans = await RouteHelper.route(Routes.GROUP_ADD_MEMBERS, arguments: params);
    if(trans == null) return;
    logger('选择用户转发：${trans.toString()}');
    var model = TransLateModel.fromJson(trans);
    logger('model = $model');
    await model.translate(msg);
    toast('图片已发送');
  }

}


class GalleryItem {
  String? msgId;
  String? url;
  String? fileId;
  bool isDownloaded = false;

  GalleryItem(this.msgId, this.url);
}