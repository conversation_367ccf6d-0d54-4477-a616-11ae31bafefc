import "dart:async";
import "dart:convert";
import "dart:io";

import "package:dio/dio.dart";
import "package:flutter/material.dart";
import "package:flutter_mixed/app/common/api/Define.dart";
import "package:flutter_mixed/app/common/base_info/info.dart";
import "package:flutter_mixed/app/common/cos/cos_manager.dart";
import "package:flutter_mixed/app/common/user/user_helper.dart";
import "package:flutter_mixed/app/db/db_helper.dart";
import "package:flutter_mixed/app/db/org/db_org_model.dart";
import "package:flutter_mixed/app/db/org/org_model_ext.dart";
import "package:flutter_mixed/app/db/user/db_user_model.dart";
import "package:flutter_mixed/app/db/user/member_model_ext.dart";
import "package:flutter_mixed/app/im/constant/ImMsgConstant.dart";
import "package:flutter_mixed/app/im/constant/constant_tcp_util.dart";
import "package:flutter_mixed/app/im/constant/im_cache_global.dart";
import "package:flutter_mixed/app/im/db/db_helper.dart" as DBImHelper;
import "package:flutter_mixed/app/im/db/entity/group.dart";
import "package:flutter_mixed/app/im/db/entity/session.dart";
import "package:flutter_mixed/app/im/ext/session_ext.dart";
import "package:flutter_mixed/app/im/manager/im_initializer.dart";
import "package:flutter_mixed/app/im/model/im_badge.dart";
import "package:flutter_mixed/app/im/model/im_session_info.dart";
import "package:flutter_mixed/app/im/request/datasource/im_datasource.dart";
import "package:flutter_mixed/app/im/request/entity/six_in_one_req.dart";
import "package:flutter_mixed/app/im/request/entity/six_in_one_resp.dart";
import "package:flutter_mixed/app/im/ui/base/im_base_controller.dart";
import "package:flutter_mixed/app/im/ui/session_list/session_controller.dart";
import "package:flutter_mixed/app/im/web_link/web_link.dart";
import "package:flutter_mixed/app/modules/contact/aboutFriend/myFriend/controllers/my_friend_controller.dart";
import "package:flutter_mixed/app/modules/contact/aboutOrg/orgList/controllers/org_list_controller.dart";
import "package:flutter_mixed/app/modules/contact/contact_home/controllers/contact_controller.dart";
import 'package:flutter_mixed/app/modules/contact/contact_home/views/contact_view.dart';
import "package:flutter_mixed/app/modules/contact/model/org/all_org_model.dart";
import "package:flutter_mixed/app/modules/contact/model/org/all_org_user.dart";
import "package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart";
import "package:flutter_mixed/app/modules/contact/model/org/org_model.dart";
import 'package:flutter_mixed/app/modules/mine/mineHome/controllers/mine_home_controller.dart';
import 'package:flutter_mixed/app/modules/mine/mineHome/views/mine_home_view.dart';
import "package:flutter_mixed/app/modules/real_name/auth_status/auth_status_controller.dart";
import "package:flutter_mixed/app/modules/real_name/input_idcard/real_name_controller.dart";
import "package:flutter_mixed/app/modules/workStand/approveFunction/manage/company_model.dart";
import "package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart";
import "package:flutter_mixed/app/modules/workStand/workFlow/views/work_flow_view.dart";
import "package:flutter_mixed/app/permission/permission_util.dart";
import "package:flutter_mixed/app/push/push_manager.dart";
import "package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart";
import "package:flutter_mixed/app/retrofit/entity/work/group_response.dart";
import "package:flutter_mixed/app/statis/statistics_helper.dart";
import "package:flutter_mixed/app/utils/net_util.dart";
import "package:flutter_mixed/app/utils/storage.dart";
import "package:flutter_mixed/main.dart";
import 'package:get/get.dart';

// import "package:tencentcloud_cos_sdk_plugin/cos.dart";
// import "package:tencentcloud_cos_sdk_plugin/pigeon.dart";
import "../../../common/api/LoginApi.dart";
import "../../../common/channel/channel.dart";
import "../../../common/config/config.dart";
import "../../../common/dialog/msg-dialog.dart";
import "../../../common/dialog/update-dialog.dart";
import "../../../common/widgets/widgets.dart";
import "../../../im/db/db_helper.dart";
import "../../../im/im_client_manager.dart";
import "../../../im/widget/message_tab_view.dart";
import "../../../retrofit/datasource/real_name_datasource.dart";
import "../../../routes/app_pages.dart";
import "../../../statis/statics_log.dart";
import "../../../utils/http.dart";
import "../../../../logger/logger.dart";
import "../../contact/aboutGroup/myGroup/controllers/my_group_controller.dart";
import "../../networkListen/controllers/network_listen_controller.dart";
import "../../workStand/approveHome/views/approve_home_tab.dart";

class HomeController extends BaseController {
  RxInt currentIndex = 0.obs;

  int? unreadMessageCount;

  final initMap = <int, bool>{};

  RxMap currentView = {
    0: MessageTabView(),
    1: WorkFlowView(),
    2: ApproveMainTabWidget(),
    3: ContactView(),
    4: MineHomeView(),
  }.obs;

  List<Widget> pages = [
    // NewsHomeView(),
    MessageTabView(),
    WorkFlowView(),
    ContactView(),
    MineHomeView(),
  ];

  RxString sessionToken = ''.obs;
  RxString cdnDomain = ''.obs;
  RxInt expiredTime = 0.obs;
  RxBool authStatus = false.obs;

  bool hiddenBottomBar = false;
  Map? updateData;
  bool isShowUpdateDialog = false;
  bool isFinishMeeting = false;

  String waitMeetingId = '';

  CancelToken? unreadCancelToken;

  bool isComplete = false;//是否执行过六合一，执行过再请求此接口如为强制更新在弹出提示框
  CancelToken? sixCancelToken;
  CancelToken? reportCancelToken;
  @override
  void onInit() {
    super.onInit();

    DbHelper.initDb();

    Get.put(NetworkListenController());

    initMap[0] = true;
    initMap[1] = true;

    ImClientManager.instance.connectIm();

    UserHelper.getUid().then((uid) => StatisticsHelper.signIn(uid));

    PermissionUtil.requestNotificationPermission();

    PushManager().initFactoryPush();
  }

  bool hasInit = false;

  bool isShowRealName = false;

  List<Widget> createChildren() {
    final result = <Widget>[];
    for (var i = 0; i < pages.length; ++i) {
      final w = pages[i];
      if (initMap[i] == true || i == pages.length-1) { 
        result.add(w);
      } else {
        result.add(Container());
      }
    }
    return result;
  }

  @override
  void onReady() {
    super.onReady();
    //settingCos();
    //getAllCosUpload();
    getIsHaveApproveCentre();
    getAllCompanies();
    getFriendUpdateVersion();
    getAllGroup();
    getUserInfo();
    //getCosSetting();
    getAllUnread();
    _listenerMsgUnRead();
    CosManager().initPans();
    _channelNativeHomeOnready();
    _deleteMsgId();

  }

  _deleteMsgId(){
    try {
      DbHelper.deleteNoticeMsgWithMsgId('2BBu9aOlSpx');
      DbHelper.upDateSessionWithContent(ConstantImMsgType.WorkMsgSessionType, '8888', 1745325260043, '');
    } catch (e) {
      logger('====shanchu===error===$e');
    }
  }
  //通知原生home onReady了
  _channelNativeHomeOnready(){
    Channel().invoke(FLUTTER_HOME_ONREADAY);
  }

  _listenerMsgUnRead() {
    // 调用六合一接口，排除 推送 业务
    getSixMergeData();

    // StatisticsHelper.timingUploadLog();

    if (Platform.isAndroid) {
      getIMdata(1);
    }
  }

  @override
  void onClose() {
    CompanyModelHelper.approveListController = null;
    super.onClose();
    logger('=======homeController----onClose---');
    //ImClientManager.instance.disConnect();
  }

  /*settingCos() async {
    WidgetsFlutterBinding.ensureInitialized();
    await Cos().initWithSessionCredential(FetchCredentials());
    await Cos().registerDefaultService(CosRegister().serviceConfig);
    await Cos().registerDefaultTransferManger(
        CosRegister().serviceConfig, CosRegister().transferConfig);
    logger('---cos初始化---');
  }*/

  getMessageUnread() async {}

  updateMsgUnread(int count) {
    if (unreadMessageCount == count) return;
    unreadMessageCount = count;
    update();
    if (Platform.isAndroid) {
      // 暂时不设置  数量不对
      // FactoryPush().setAppBade(count);
    }else{
      Channel().invokeMap(CHANNEL_SET_APP_BADGE,{'count':count});
    }
    _putReportAppBadge();
  }

  //上报未读数
  _putReportAppBadge() async{
    try{
      if (EnvConfig.mEnv == Env.TokenProduct) return;
      if (reportCancelToken != null) {
        reportCancelToken?.cancel();
      }
      reportCancelToken = CancelToken();
      var datasource = ImDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);
      var resp = await datasource.putReportBadge(AppBadge(badge: unreadMessageCount),reportCancelToken!);
      logger('=====app_badge===$resp');
    }catch(e){
      logger('=====app_badge=error==$e');
    }
  }

  enterMeeting(String meetingId) {
    waitMeetingId = meetingId;
    if (isFinishMeeting) {
      goCanAddListPage(meetingId);
    }
  }

  goCanAddListPage(String meetingId, {bool isPaste = false}) async {
    if (waitMeetingId.isNotEmpty) {
      waitMeetingId = '';
      if (isFinishMeeting) {
        Channel().invoke(Channel_callNative_jumpRoute, {
          'route': Routes.MEETING_DETAIL,
          'arguments': {'meetingId': meetingId, 'type': 2}
        });
      }
    } else {
      if (!isFinishMeeting) return;
      //剪切板逻辑
      // if (isPaste) {
      //   ClipboardData? clipboardData =
      //       await Clipboard.getData(Clipboard.kTextPlain);
      //   if (clipboardData != null) {
      //     String content = clipboardData.text!;
      //     List contentList = content.split('\n');
      //     for (String element in contentList) {
      //       if (element.contains(Host.MEETINGLINK)) {
      //         List meetingList = element.split('meetingId=');
      //         if (meetingList.length > 1) {
      //           String endStr = meetingList.last;

      //           if (endStr.contains('&')) {
      //             List endList = endStr.split('&');
      //             endStr = endList.first;
      //           }
      //           Clipboard.setData(const ClipboardData(text: ''));
      //           Channel().invoke(Channel_callNative_jumpRoute, {
      //             'route': Routes.MEETING_DETAIL,
      //             'arguments': {'meetingId': endStr, 'type': 2}
      //           });
      //           break;
      //         }
      //       }
      //     }
      //   }
      // }
    }
  }

  //获取好友版本
  getFriendUpdateVersion() {
    DioUtil()
        .get(LoginApi.IM_FRIENDS_VERSION, null, true, () {},
            isShowLoading: false, isShowErrorToast: false)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        String versionString = data['data'];
        dynamic saveVersion = await UserDefault.getData(Define.FRIENDVERSION);
        logger('saveVersion===$saveVersion');
        if (saveVersion != versionString) {
          getFriendList(version: versionString);
        }
      } else {
        logger('${LoginApi.IM_FRIENDS_VERSION}: ${data['msg']}');
        toast(data['msg']);
      }
    });
  }

  //获取好友列表
  Future<void> getFriendList({String version = ''}) async {
    try {
      final data = await DioUtil()
          .get(LoginApi.IM_FRIENDS_LIST, null, true, () {},
              isShowLoading: false, isShowErrorToast: false);

      if (data == null) return;

      if (data['code'] == 1) {
        List friendList = data['data'];
        await UserDefault.setData(Define.FRIENDLIST, friendList);

        var friendData =
            Platform.isAndroid ? jsonEncode(friendList) : friendList;

        Channel()
            .invoke(Channel_data_toNative, {'data': friendData, 'type': 2});

        bool isHaveFriend = Get.isRegistered<MyFriendController>();
        if (isHaveFriend) {
          MyFriendController friendController = Get.find();
          friendController.dealFriendData();
        }

        if (version.isNotEmpty) {
          await UserDefault.setData(Define.FRIENDVERSION, version);
        }
      } else {
        toast(data['msg']);
      }
    } catch (e) {
      logger('Error in getFriendList: $e');
    }
  }

  //获取所有公司
  getAllCompanies({bool isSaveUsers = false}) async {
    try {
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource.getAllCompanies();
      if (resp.success()) {
        if (resp.data == null) return;
        AllOrgModel model = resp.data;
        Map<String, dynamic> dataDic = model.toJson();
        await UserDefault.setData(Define.ORGLIST, dataDic);

        var companyData = Platform.isAndroid ? jsonEncode(model) : dataDic;

        Channel()
            .invoke(Channel_data_toNative, {'data': companyData, 'type': 0});
        bool isHave = Get.isRegistered<OrgListController>();
        if (isHave) {
          OrgListController orgListController = Get.find();
          orgListController.getAllCompany();
        }

        bool isHaveContact = Get.isRegistered<ContactController>();
        if (isHaveContact) {
          ContactController contactController = Get.find();
          contactController.dealMainOrg(dataDic['mainCompanyId']);
        }

        bool isHaveWorkFlow = Get.isRegistered<WorkFlowController>();
        if (isHaveWorkFlow) {
          WorkFlowController workController = Get.find();
          workController.getAllCompany();
        }

        bool isHaveMine = Get.isRegistered<MineHomeController>();
        if (isHaveMine) {
          MineHomeController mineHomeController = Get.find();
          mineHomeController.dealMainOrg('');
        }
        CompanyModelHelper.createCompanyModel();

        bool isHaveSession = Get.isRegistered<SessionController>();
        if (isHaveSession) {
          SessionController sessionController = Get.find();
          sessionController.loadSession();
        }

        //加入数据库
        if (isSaveUsers) {
          String myUserId = await UserHelper.getUid();
          List<DBOrgModel> models = [];
          List<String> companyIds = [];
          if (model.companies != null) {
            if (model.companies!.isNotEmpty) {
              for (var i = 0; i < model.companies!.length; i++) {
                OrgModel? orgModel = model.companies![i];
                if (orgModel != null) {
                  DBOrgModel dbModel = orgModel.getDBOrgModel(myUserId);
                  models.add(dbModel);
                  companyIds.add(orgModel.companyId);
                }
              }
              await DBHelper.insertOrg(models);
            }
          }
          getCompanyUsers(companyIds, myUserId);
        }
      } else {
        toast(resp.msg);
      }
    } on DioException catch (error) {
      //toast(LoginApi.ERROR_MSG);
    }
  }

  //获取公司人员
  getCompanyUsers(List<String> companyIds, String myUserId) async {
    if (companyIds.isNotEmpty) {
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource.getOrgUsers(companyIds);
      if (resp.success()) {
        List<AllOrgUserModel?>? allModel = resp.data;
        if (allModel == null) return;
        List<String> allUser = [];
        for (var i = 0; i < allModel.length; i++) {
          AllOrgUserModel? userModel = allModel[i];
          if (userModel == null) break;
          List<MemberModel?>? members = userModel.staffVOList;
          if (members == null) break;
          List<String> users = [];
          List<DBUserModel> dbUsers = [];
          for (var j = 0; j < members.length; j++) {
            MemberModel? userModel = members[j];
            if (userModel == null) break;
            users.add(userModel.userId);
            DBUserModel dbUserModel = userModel.getDBUserModel(myUserId);
            dbUsers.add(dbUserModel);
          }
          allUser.addAll(users);
          String userStr = users.join(',');
          //更新公司表
          DBHelper.updateUsers(myUserId, userModel.orgId!, userStr);
          if (dbUsers.isNotEmpty) {
            DBHelper.insertMemberList(dbUsers);
          }
        }
        //删除没有的公司和人员数据
        _deleteCompanyData(companyIds, myUserId);
        _deleteUserData(allUser, myUserId);
      }
    } else {
      //删除公司表和人员表
      DBHelper.clearLocalOrg(myUserId);
      DBHelper.clearLocalUser(myUserId);
    }
  }

  //删除公司数据
  _deleteCompanyData(List<String> companyIds, String myUserId) async {
    List<DBOrgModel> dbOrgs = await DBHelper.getAllLocalOrg(myUserId);
    for (var i = 0; i < dbOrgs.length; i++) {
      DBOrgModel dbModel = dbOrgs[i];
      if (!companyIds.contains(dbModel.companyId)) {
        DBHelper.deleteLocalOrg(myUserId, dbModel.companyId);
      }
    }
  }

  //删除人员数据
  _deleteUserData(List<String> users, String myUserId) async {
    List<DBUserModel> dbUsers = await DBHelper.getAllLocalUser(myUserId);
    for (var i = 0; i < dbUsers.length; i++) {
      DBUserModel dbModel = dbUsers[i];
      if (!users.contains(dbModel.userId)) {
        DBHelper.deleteLocalUser(myUserId, dbModel.userId);
      }
    }
  }

  //获取所有公司审批未读数
  // getAllCompanyApproveUnread() async {
  //   Map dataDic = await UserDefault.getData(Define.ORGLIST);

  //   List companies = [];
  //   if (dataDic['companies'] != null) {
  //     companies = dataDic['companies'];
  //   }
  //   List orgIdList = [];
  //   for (Map element in companies) {
  //     orgIdList.add(element['companyId']);
  //   }

  //   DioUtil().post(ApproveApi.ALLAPPROVEUNREAD, jsonEncode(orgIdList), true,
  //       () {
  //     toast(LoginApi.ERROR_MSG);
  //   }, isShowLoading: false).then((data) async {
  //     logger('Http response: $data');
  //     if (data!['code'] == 1) {
  //       List dataList = data['data'];
  //       Map unreadMap = {};
  //       for (Map element in dataList) {
  //         unreadMap[element['orgId']] = element['count'];
  //       }
  //       await UserDefault.setData(Define.APPROVELUNREADKEY, unreadMap);
  //     } else {
  //       toast('${data['msg']}');
  //     }
  //   });
  // }

  //获取所有群组
  getAllGroup() async {
    try {
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource.getGroup('0');
      if (resp.success()) {
        await UserDefault.setData(Define.GROUPLIST, resp.data);
        List<GroupRespItem> list = resp.data;

        var groupData = Platform.isAndroid
            ? jsonEncode(list)
            : list.map((e) => e.toJson()).toList();

        Channel().invoke(Channel_data_toNative, {'data': groupData, 'type': 1});
        bool isHaveMyGroup = Get.isRegistered<MyGroupController>();
        if (isHaveMyGroup) {
          MyGroupController myGroupController = Get.find();
          myGroupController.getAllGroup();
        }
        _updateSessionNameAndLogo(list);
        _insertGroupDataToDb(list);
      } else {
        //toast(LoginApi.ERROR_MSG);
      }
    } on DioException catch (error) {
      //toast(LoginApi.ERROR_MSG);
    }
  }

  //更新群组头像和名称
  _updateSessionNameAndLogo(List<GroupRespItem> list) async{
    var ownId = await UserHelper.getUid();
    var sessions = await DbHelper.getSessionListByOwnerId(ownId);
    for (var i = 0; i < sessions.length; i++) {
      Session session = sessions[i];
      if (session.isGroupChat()) {
        for (var j = 0; j < list.length; j++) {
          GroupRespItem item = list[j];
          if (item.groupId == session.sessionId && (session.headerUrl != item.logo || session.name != item.name)) {
              DbHelper.upDateSessionSessionNameAndLogo(
            item.groupId ?? '', item.name ?? '', item.logo ?? '',ownId);
          }
        }

      }
    }
  }

  _insertGroupDataToDb(List<GroupRespItem> list) async {
    var groupDbDataList = _buildGroupDbData(list);
    await DBImHelper.DbHelper.insertAllGroupInTransaction(groupDbDataList);
  }

  List<Group> _buildGroupDbData(List<GroupRespItem> list) {
    List<Group> groupDbDataList = [];
    for (var i = 0; i < list.length; i++) {
      GroupRespItem item = list[i];
      Group group = Group(
        item.groupId!,
        uid: ownerId,
        name: item.name,
        logo: item.logo,
        type: item.type,
        createUserId: item.createUserId,
        orgId: item.orgId,
      );
      groupDbDataList.add(group);
    }
    return groupDbDataList;
  }

  /* //获取腾讯云配置
  getCosSetting() async {
    DioUtil().get(LoginApi.FILE_GET_SETTING, null, true, () {
      toast(LoginApi.ERROR_MSG);
    }, isShowLoading: false).then((data) async {
      logger('Http response: $data');
      if (data!['code'] == 1) {
        Map dataDic = data['data'];
        CosXmlServiceConfig serviceConfig = CosXmlServiceConfig(
          region: dataDic['region'],
          isDebuggable: true,
          isHttps: true,
        );
        await Cos().registerDefaultService(serviceConfig);
        await UserDefault.setData(Define.COSSETTINGKEY, dataDic);
      } else {
        toast('${data['msg']}');
      }
    });
  }*/

  //获取所有腾讯云上传

  getAllCosUpload() {
    DioUtil()
        .get(PanApi.GET_TOTAL_TOKEN, null, true, () {}, isShowLoading: false)
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataDic = data['data'];
        await UserDefault.setData(Define.COSTOKENKEY, dataDic);
        // await Cos().initWithSessionCredential(FetchCredentials());
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取个人资料
  getUserInfo() {
    DioUtil()
        .get(LoginApi.GETPERSALINFO, null, true, () {}, isShowLoading: false)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        Map tokenDic = await UserDefault.getData(Define.TOKENKEY);
        tokenDic.addAll(data['data']);
        UserDefault.setData(Define.TOKENKEY, tokenDic);
        Channel().invoke(Channel_userdefult_tokenKey, tokenDic);
        bool isHaveMine = Get.isRegistered<MineHomeController>();
        if (isHaveMine) {
          MineHomeController mineHomeController = Get.find();
          mineHomeController.dealUserInfo();
        }
        //处理实名认证
        logger("====kyc==${tokenDic['kyc']}====risk===${tokenDic['riskLevel']}===");
        var status = await getAuthStatus();
        authStatus.value = status == 4;
        if(tokenDic['kyc'] == 0 && tokenDic['riskLevel'] >=2){
          if(isShowRealName){
            return;
          }
          if(status == 3){
            Get.toNamed(Routes.REALNAME_STATUS,arguments: {'canBack':false,'status':status});
          }else{
            Get.toNamed(Routes.REAL_NAME,arguments: {'canBack':false});
          }
          isShowRealName = true;
        }

      } else {
        toast('${data['msg']}');
      }
    });
  }
  Future<int> getAuthStatus() async{
    var realNameDataSource = RealNameDataSource(retrofitDio,baseUrl: Host.HOST);
    var resp = await realNameDataSource.getVerifyStatus();
    if(resp.success()){
      return resp.data?.status??0;
    }else{
      return 0;
    }
  }
  //获取未读
  getAllUnread() {
    if (unreadCancelToken != null) {
      unreadCancelToken!.cancel();
    }
    unreadCancelToken = CancelToken();
    DioUtil()
        .get(ApproveApi.APPROVEHOMEPAGEUNREADV2, null, true, () {},
            isShowLoading: false,
            isShowErrorToast: false,
            cancelToken: unreadCancelToken)
        .then((data) async {
      if (data != null && data['code'] == 1) {
        Map dataMap = data['data'];
        List countList = [
          dataMap['allRedPointCount'],
          dataMap['backlogRedPointCount'],
          dataMap['finishRedPointCount'],
          dataMap['createRedPointCount'],
          dataMap['ccRedPointCount']
        ];
        int totalCount = 0;
        for (var i = 0; i < countList.length; i++) {
          dynamic unreadCount = countList[i];
          int temp = unreadCount ?? 0;
          totalCount += temp;
        }

        // Map unreadMap = await UserDefault.getData(Define.APPROVE_TOTAL_LUNREADKEY);
        // unreadMap[orgId] = totalCount;
        Map unreadMap = {};
        unreadMap['total'] = totalCount;
        await UserDefault.setData(Define.APPROVE_TOTAL_LUNREADKEY, unreadMap);
        bool isHaveWorkFlow = Get.isRegistered<WorkFlowController>();
        if (isHaveWorkFlow) {
          WorkFlowController workController = Get.find();
          workController.dealApproveUnreadCount();
        }
      } else {

      }
    });
  }

  //获取tabbar是否审批中心有权限
  getIsHaveApproveCentre() {
    try {
      DioUtil()
        .get(ApproveApi.APPROVEMCENTRESHORTCUT, null, true, () {},
            isShowLoading: false, isShowErrorToast: false)
        .then((data) async {
      if (data == null) return;
      if (data!['code'] == 1) {
        int dataInt = data['data'];
        if (dataInt == 1) {
          pages = [
            MessageTabView(),
            WorkFlowView(),
            ApproveMainTabWidget(),
            ContactView(),
            MineHomeView(),
          ];
          initMap[2] = true;
          currentIndex.value = 2;
          update();
        }else{
          //NotificationApproveHelper.dealApproveWaitInfo();
        }
      } else {
        //NotificationApproveHelper.dealApproveWaitInfo();
      }
    });
    } catch (e) {
      //NotificationApproveHelper.dealApproveWaitInfo();
    }

  }

  //获取是否有异常退出的会议
  getIsHaveOutMeeting() {
    logger('getIsHaveOutMeeting======');
    isFinishMeeting = true;
    eventBus.fire({'listPageView_showScrren': 1});
    return;
    DioUtil().get(MeetingApi.MEETING_CHECK_OUT, null, true, () {
      isFinishMeeting = true;
      eventBus.fire({'listPageView_showScrren': 1});
      goCanAddListPage(waitMeetingId, isPaste: true);
    }, isShowLoading: false, isShowErrorToast: false).then((data) async {
      logger('Http response: $data');
      if (data == null) return;
      if (data['code'] == 1) {
        if (isFinishMeeting) return;

        Map dataMap = data['data'];
        String meetingId = dataMap['meetingId'];
        if (meetingId.isNotEmpty) {
          String leftStr = '取消';
          String rightStr = '重新入会';
          String type = dataMap['type'];
          if (type == '1') {
            //主持人
            leftStr = '重新入会';
            rightStr = '';
          }
          isFinishMeeting = true;
          MsgDiaLog('', '你有异常退出的会议,是否重新入会?', leftStr, rightStr,
              leftColor: type == '1'
                  ? ColorConfig.themeCorlor
                  : ColorConfig.mainTextColor, () {
            Navigator.of(Get.context!).pop();
            if (type == '1') {
              eventBus.fire({'listPageView_showScrren': 1});
              checkIsHaveUnFinishedMeeting(meetingId);
            } else {
              eventBus.fire({'listPageView_showScrren': 1});
              leaveMeeting(meetingId);
            }
          }, () {
            Navigator.of(Get.context!).pop();
            checkIsHaveUnFinishedMeeting(meetingId);
          }).show();
        } else {
          isFinishMeeting = true;
          goCanAddListPage(waitMeetingId, isPaste: true);
          eventBus.fire({'listPageView_showScrren': 1});
        }
      } else {
        goCanAddListPage(waitMeetingId, isPaste: true);
        isFinishMeeting = true;
        eventBus.fire({'listPageView_showScrren': 1});
        toast('${data['msg']}');
      }
    });
  }

  checkMeetingIsHavePwd(String meetingId) async {
    Map tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    Map param = {'meetingId': meetingId, 'userId': tokenInfo['userId']};
    DioUtil().post(MeetingApi.MEETING_CHECK, param, true, () {}).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        int dataCode = data['data'];
        // MEETING_EXISTENCE(101,"会议存在"),
// MEETING_NOT_EXISTENCE(102,"会议不存在"),
// MEETING_PASSWORD(103,"会议存在密码"),
// MEETING_PASSWORD_NOT(104,"会议不存在密码"),
// MEETING_ENDED(105,"会议已结束")    MEETING_PASSWORD_NOT_NEED(106,"是主持人进入会议不需要密码" ),),

        if (dataCode == 102) {
          toast('会议不存在');
        } else if (dataCode == 103) {
          Get.toNamed(Routes.MEETING_JOIN,
              arguments: {
                'type': 1,
                'isVoice': 0,
                'isVideo': 0,
                'isSpeaker': 1,
                'meetingId': meetingId,
              },
              preventDuplicates: false);
        } else if (dataCode == 104) {
          getMeetingSign(meetingId);
        } else if (dataCode == 105) {
          toast('会议已结束');
        } else if (dataCode == 106) {
          getMeetingSign(meetingId);
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //退出会议
  leaveMeeting(String meetingId) {
    DioUtil()
        .put('${MeetingApi.MEETING_LEAVE}/$meetingId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        // toast('已退出会议');
      } else {
        toast('${data['msg']}');
      }
    });
  }

  checkIsHaveUnFinishedMeeting(String meetingId) {
    DioUtil()
        .get('${MeetingApi.MEETING_REMOVE_USER}/$meetingId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        dynamic dataCode = data['data'];
        if (dataCode is int) {
          if (dataCode == 700) {
            MsgDiaLog('', '你有会议正在进行，如想进入新的会议，请先结束正在进行的会议', '确定', '', () {
              Navigator.of(Get.context!).pop();
            }, () {})
                .show();
          } else {
            getMeetingSign(meetingId);
          }
        }
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //进入会议
  getMeetingSign(String meetingId) {
    DioUtil()
        .get('${MeetingApi.GET_MEETING_SIGN}/$meetingId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        Map dataMap = data['data'];
        dataMap['isVoice'] = 0;
        dataMap['isVideo'] = 0;
        dataMap['isSpeaker'] = 1;
        Channel().invoke(Channel_jumpMeeting, dataMap);
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //处理更新信息
  dealUpdateData(Map argument) {
    //处理更新信息
    if (Platform.isIOS) {
      upgradeApp(argument);
    } else {
      getIsHaveOutMeeting();
    }
  }

  upgradeApp(Map dataMap) async {
    if (isShowUpdateDialog) return;
    int auditing = dataMap['auditing']; //** 0审核中 1审核通过 **/
    Map<String, dynamic> headerMap = await UserDefault.getHttpHeader();
    String version = headerMap['appVersion'];
    if (auditing == 1) {
      int status = dataMap['status'];
      /** 0是代表是当前版本 1是强制更新 2是非强制更新 **/
      if (status > 0) {
        if (status == 2) {
          //获取本地是否存储了不再更新的版本
          dynamic versionName =
              await UserDefault.getData(Define.UPDATE_VERSION);
          if (versionName != null) {
            if (versionName == version) {
              getIsHaveOutMeeting();
              return;
            }
          }
        }
        isShowUpdateDialog = true;
        UpdateDialog().show(dataMap, version);
      } else {
        getIsHaveOutMeeting();
      }
    } else {
      getIsHaveOutMeeting();
    }
  }

  bool isMustUpdate(Map dataMap){
    int auditing = dataMap['auditing']; //** 0审核中 1审核通过 **/
    if (auditing == 1) {
      int status = dataMap['status'];
      if (status == 1) {
        return true;
      }
    }
    return false;
  }

  //6合一
  getSixMergeData() async {
    var imSource = await ImDataSource(retrofitDio);
    // var result =
    //     await Channel().invoke(CHANNEL_NATIVE_REGISTRATION_ID);

    SixInOneReqBody reqBody = SixInOneReqBody(
        Platform.isAndroid
            ? ConsKeys.PHONE_CLIENT_ANDROID
            : ConsKeys.PHONE_CLIENT_IOS,
        "",
        NetUtil.netStr);
    try {
      logger('======6合一===body==${reqBody.toJson()}');
      if (sixCancelToken != null) {
        sixCancelToken!.cancel();
      }
      sixCancelToken = CancelToken();
      var resp = await imSource.fetchSixInOne(reqBody,sixCancelToken!);
      if (resp.success()) {
        if (resp.data == null) return;
        logger('======6合一===response==${resp.data.toJson()}');
        _preCacheDisturb(resp.data);
        Map<String, dynamic> dataDic = resp.data.toJson();
        cacheWhiteListData(resp.data.linkWarn , resp.data.linkWhiteList);
        try {
          WorkFlowController workFlowController = Get.find();
          workFlowController.reciveNativeData(dataDic);
        } catch (e) {
          logger(e);
        }
        if (!isComplete || isMustUpdate(dataDic)) {
          dealUpdateData(dataDic);
          var sixInOneData =
              Platform.isAndroid ? jsonEncode(dataDic) : dataDic;
          Channel().invoke(Channel_data_toNative, {'data': sixInOneData, 'type': 3});
        }

        Map cosSettingMap = {'pre': dataDic['pre']};
        await UserDefault.setData(Define.COSPREKEY, cosSettingMap);
        isComplete = true;
      }else{
        logger('======6合一===response==${resp.data.toJson()}');
      }
    } catch (e) {}
  }

  _preCacheDisturb(SixInOneResp r) async {
    var companies = r.ids?.map((e) => e ?? '').toList() ?? [];
    ImCacheData.instance.updateCompanyIds(companies);

    NotifyItemDisturb notifyItemDisturb = NotifyItemDisturb()
      ..approvalPushSwitch = r.approvalPushSwitch
      ..strangerSwitch = r.strangerSwitch
      ..inventorySwitch = r.inventorySwitch
      ..systemPushSwitch = r.systemPushSwitch
      ..ticketPushSwitch = r.ticketPushSwitch
      ..kingdeePushSwitch = r.kingdeePushSwitch
      ..trainingSwitch = r.trainingSwitch
      ..managementSwitch = r.managementSwitch
      ..rangeParkSwitch = r.rangeParkSwitch
      ..idcSwitch = r.idcSwitch;
    await ImCacheData.instance.updateNotifyDisturb(notifyItemDisturb);
    try {
      SessionController? sessionController = Get.find<SessionController>();
      sessionController.synchronizationNoticeDisturb();
    } catch (e) {
      
    }

  }

  //获取IM token等数据
  getIMdata(int loginInt) async {
    //loginInt 1登录 0重连
    DioUtil()
        .get(LoginApi.IM_USER_MINE, null, true, () {},
            isShowLoading: false, isShowErrorToast: false)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        var imData =
            Platform.isAndroid ? jsonEncode(data['data']) : data['data'];

        Channel().invoke(Channel_data_toNative,
            {'data': imData, 'type': 4, 'loginInt': loginInt});
      } else {
        toast('${data['msg']}');
      }
    });
  }



}
