import 'package:flutter_mixed/app/modules/contact/contact_home/controllers/contact_controller.dart';
import 'package:flutter_mixed/app/modules/mine/mineHome/controllers/mine_home_controller.dart';
import 'package:flutter_mixed/app/modules/news/newsHome/controllers/news_home_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/approveList/controllers/approve_list_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart';
import 'package:get/get.dart';

import '../controllers/home_controller.dart';


class HomeBinding extends Bindings {
  @override
  void dependencies() {
    // Get.lazyPut<HomeController>(
    //   () => HomeController(),
    // );
    Get.create(() => ApproveListController());
    Get.put(HomeController());
    
    // Get.lazyPut(() => WorkFlowController());
    // Get.lazyPut(() => NewsHomeController());
  }
}
