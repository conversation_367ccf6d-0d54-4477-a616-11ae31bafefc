import 'dart:io';

// import 'package:badges/src/badge.dart' as badge;
import 'package:badges/badges.dart' as badge;
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/widget/app_life.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../common/config/config.dart';
import '../controllers/home_controller.dart';

class HomeView extends StatefulWidget {
  const HomeView({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _HomeState();
  }
}

class _HomeState extends State<HomeView> with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<HomeController>(builder: (controller) {
      return AppLifeContainer(Theme(
        data: ThemeData(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            cardColor: Colors.transparent),
        child: Scaffold(
          backgroundColor: ColorConfig.backgroundColor,
          bottomNavigationBar: controller.hiddenBottomBar
              ? SizedBox(
            height: 0,
          )
              : BottomNavigationBar(
              backgroundColor: Color(0xF1FFFFFF),
              selectedItemColor: Color(0xFF2C67EC),
              type: BottomNavigationBarType.fixed,
              currentIndex: controller.currentIndex.value,
              onTap: (index) {
                controller.currentIndex.value = index;

                controller.initMap[index] = true;

                controller.update();
                if (Platform.isIOS) {
                  Channel().invoke(Channel_call_Play_sound, {});
                }
              },
              enableFeedback: false,
              items: controller.pages.length == 4
                  ? [
                getMainTab('assets/images/3.0x/home_news.png',
                    'assets/images/3.0x/home_news_selected.png', "消息",
                    type: 0,
                    unread: controller.unreadMessageCount ?? 0),
                getMainTab(
                  'assets/images/3.0x/home_workflow.png',
                  'assets/images/3.0x/home_workflow_selected.png',
                  "工作",
                ),
                getMainTab(
                    'assets/images/3.0x/home_contact.png',
                    'assets/images/3.0x/home_contact_selected.png',
                    "通讯录"),
                getMainTab(
                    'assets/images/3.0x/home_mine.png',
                    'assets/images/3.0x/home_mine_selected.png',
                    "我的"),
              ]
                  : [
                getMainTab('assets/images/3.0x/home_news.png',
                    'assets/images/3.0x/home_news_selected.png', "消息",
                    type: 0,
                    unread: controller.unreadMessageCount ?? 0),

                getMainTab(
                  'assets/images/3.0x/home_workflow.png',
                  'assets/images/3.0x/home_workflow_selected.png',
                  "工作",
                ),

                /// 新
                getMainTab(
                    'assets/images/3.0x/home_approve.png',
                    'assets/images/3.0x/home_approve_selected.png',
                    "审批中心"),

                getMainTab(
                    'assets/images/3.0x/home_contact.png',
                    'assets/images/3.0x/home_contact_selected.png',
                    "通讯录"),

                getMainTab(
                    'assets/images/3.0x/home_mine.png',
                    'assets/images/3.0x/home_mine_selected.png',
                    "我的"),
              ]),
          // body: controller.currentView[controller.currentIndex.value],
          body: IndexedStack(
            index: controller.currentIndex.value,
            children: controller.createChildren(),
          ),
        ),
      ), resumed: (){
      },
      );
    });
  }

  @override
  bool get wantKeepAlive => true;
}

BottomNavigationBarItem getMainTab(
    String iconPath, String activeIcon, String title,
    {int type = 0, int unread = 0}) {
  return BottomNavigationBarItem(
      icon: _badge(
          Key('$unread-$iconPath-$title'),
          unread,
          SizedBox(
            width: 18,
            height: 18,
            child: Image.asset(iconPath),
          )),
      activeIcon: _badge(
          Key('$unread-$iconPath-$title'),
          unread,
          SizedBox(
            width: 22,
            height: 22,
            child: Image.asset(activeIcon),
          )),
      label: title);
}

// _badge(int count, Widget child) {
//   var value = '';
//   double fontSize = 10;

//   var shape = badge.BadgeShape.circle;

//   if (count <= 0) {
//     value = '';
//   } else if (count > 99) {
//     value = '99+';
//     fontSize = 8;
//     shape = badge.BadgeShape.square;
//   } else {
//     value = '$count';
//     shape = badge.BadgeShape.circle;
//   }
//   return Padding(
//       padding: EdgeInsets.all(0),
//       child: badge.Badge(
//         // shape: BadgeShape.circle,
//         ignorePointer: false,
//         badgeStyle: badge.BadgeStyle(
//           shape: shape,
//           badgeColor: Colors.red,
//           padding: EdgeInsets.all(4),
//           borderRadius: BorderRadius.circular(8),
//           borderSide: BorderSide(color: Colors.red, width: 2),
//           elevation: 0,
//         ),
//         badgeContent: Text(
//           '$value',
//           style: TextStyle(fontSize: fontSize, color: Colors.white),
//         ),
//         showBadge: count > 0,
//         position: badge.BadgePosition.topEnd(top: -4, end: -12),
//         child: child,
//       ));
// }

_badge(Key key, int count, Widget child) {
  var value = '';
  var shape = badge.BadgeShape.square;
  if (count <= 0) {
    value = '';
    shape = badge.BadgeShape.circle;
  } else if (count > 99) {
    value = '99+';
    shape = badge.BadgeShape.square;
  } else {
    value = '$count';
    shape = badge.BadgeShape.circle;
  }
  double fontSize = 10;
  var badgeStyle;
  if (shape == badge.BadgeShape.circle) {
    badgeStyle = badge.BadgeStyle(
      shape: shape,
      badgeColor: ColorConfig.deleteCorlor,
      padding: EdgeInsets.all(1),
      elevation: 0,
    );
  } else {
    badgeStyle = badge.BadgeStyle(
      shape: shape,
      borderRadius: BorderRadius.circular(12),
      badgeColor: ColorConfig.deleteCorlor,
      padding: EdgeInsets.all(1),
      elevation: 0,
    );
  }

  return Padding(
      padding: EdgeInsets.all(0),
      child: badge.Badge(
        // shape: BadgeShape.circle,
        ignorePointer: false,
        // badgeAnimation: const badge.BadgeAnimation.scale(
        //   animationDuration: Duration(seconds: 1),
        //   colorChangeAnimationDuration: Duration(seconds: 1),
        //   loopAnimation: false,
        //   curve: Curves.fastOutSlowIn,
        //   colorChangeAnimationCurve: Curves.easeInCubic,
        // ),
        badgeStyle: badgeStyle,
        badgeContent: Container(
          padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
          key: key,
          constraints: BoxConstraints(minWidth: 22),
          child: Center(
            child: Text(
              value,
              style: TextStyle(fontSize: fontSize, color: Colors.white),
            ),
          ),
        ),
        showBadge: count > 0,
        position: badge.BadgePosition.topEnd(top: -4, end: -12),
        child: child,
      ));
}
