import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_item.dart';
import 'package:flutter_mixed/app/modules/invite_by_session/invite_by_session_controller.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';
import 'package:flutter_mixed/app/modules/group_member/icon_search_input_field.dart';

import '../../routes/route_util.dart';
class InviteBySessionPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<InviteBySessionController>(builder: (controller) {
      return ToolBar(
        title: controller.imagePath != null ? '分享至担当好友' : '邀请担当好友',
        backFunction: (){
          controller.navigateBack();
        },
        backgroundColor: Colors.white,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 5),
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Theme(
                data: ThemeData(
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                ), 
                child: InkWell(
                  onTap: (){
                     controller.sendInvite();
                  },
                  child: Text('发送', style: TextStyle(fontSize: 15 , color: controller.sendButtonTextColor()),),
                )
              ),
            ),
          ),
        ],
        body: Column(
          children: [
            _buildSearchBar(context , controller),
            10.gap,
            Expanded(
              child: ListView.builder(
                itemCount: controller.uiList.length,
                itemBuilder: (context, index) {
                  String letter = '';
                  SessionUIData memberModel = controller.uiList[index];
                  letter = memberModel.initial ?? '';
                  return controller.uiList.isEmpty ? 
                    Container(
                      width: double.infinity,
                      height: (DeviceUtils().height.value - 130) * 0.5,
                      alignment: Alignment.center,
                      child: const Text(
                        '暂无好友',
                        style:
                            TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                      ),
                    ) : StringUtil.isEmpty(memberModel.userId) ? 
                    Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.only(left: 15),
                      height: 24,
                      decoration: const BoxDecoration(
                        color: ColorConfig.backgroundColor,
                      ),
                      child: Row(
                        children: [
                          Text(
                            letter,
                            style: const TextStyle(
                                fontSize: 14, color: ColorConfig.subTitleTextColor),
                          ),
                        ],
                      ),
                    ) : 
                    Container(
                      color: ColorConfig.whiteColor,
                      margin: const EdgeInsets.only(left: 14, right: 14),
                      height: 56,
                      child: InkWell(
                        enableFeedback: false,
                        onTap: () {
                          controller.onItemSelect(memberModel);
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _checkBox(memberModel),
                            14.gap,
                            SessionAvatar(
                              Session(
                                sessionType: ConstantImMsgType.SSChatConversationTypeChat,
                                headerUrl: memberModel.avatar,
                                sessionId: memberModel.imId ?? '',
                                name: memberModel.name,
                              )
                            ),
                            8.gap,
                            Expanded(child: Text(memberModel.name ?? '', maxLines: 1, style: const TextStyle(
                              overflow: TextOverflow.ellipsis,
                              fontSize: 14, color: Color(0xff333333)
                            )))
                          ],
                        ),
                      ),
                    );
                }
              ),
            ),
          ],
        ),
      );
    });
  }

  _buildSearchBar(BuildContext context, InviteBySessionController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SearchInputField(
        isNeedTopMargin: false,
        hintText: '搜索担当好友',
        focusNode: controller.searchJobFocusNode,
        onChanged: (e) {
          controller.searchMembers(e);
        },
        textFieldContainer: controller.searchJobEditingController,
        clearInput: () {
          controller.clearInput();
        },
      ),
    );
  }

  _checkBox(SessionUIData model) {
    if(model.selected) return Image.asset(AssetsRes.APPROVE_SELECTED ,width: 17, height: 17,);
    return Image.asset(AssetsRes.APPROVE_UNSELECTED ,width: 17, height: 17,);
  }

}