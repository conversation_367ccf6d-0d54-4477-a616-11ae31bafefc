import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/convert/in_time_im_convert_to_session.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../../../main.dart';
import '../../common/channel/channel.dart';
import '../../common/cos/cos_manager.dart';
import '../../common/widgets/ios_native_route_focus.dart';
import '../../im/bridge_engine/bridge_engine.dart';
import '../../im/db/entity/message.dart';
import '../../im/ui/chat/message/custom_msg_model.dart';
import '../../routes/app_pages.dart';
import '../../utils/image_util.dart';
import '../../../logger/logger.dart';
import '../contact/model/org/org_model.dart';

import 'package:get/get.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:get/get.dart' as GroupGet;

class InviteBySessionController extends GetxController with MixNativeController{

  late OrgModel fromModel;

  List<SessionUIData> dataList = [];
  List<SessionUIData> uiList = [];

  TextEditingController searchJobEditingController = TextEditingController();

  FocusNode searchJobFocusNode = FocusNode();

  String currentKey = '';

  String? imagePath;

  StreamSubscription? subscription;

  @override
  void onInit() {
    super.onInit();

    if(Get.arguments is OrgModel){
      fromModel = Get.arguments;
      update();
    }

    if(Get.arguments is String){
      imagePath = Get.arguments;
    }

    subscription = GroupGet.Get.getOrEventParams(Routes.INVITE_BY_SESSION, arg: (arg) {
      if(arg is Map){
        imagePath = arg['localPath'];
      }
    } ,isNative: isNative);

    _dealFriendData();
  }

  _dealFriendData() async {
    List rawFriendList = await UserDefault.getData(Define.FRIENDLIST);
    List<UserModel> friendList = rawFriendList.map((e) => UserModel.fromJson(e)).toList();
    List<Map<String,List<SessionUIData>>> letterList = _createLetterSerList();
    for (var l in letterList) {
      var tempL = <SessionUIData>[];
      for (var m in friendList) {
        Map dataDic = {
          'userId': m.userId,
          'imId': m.imId,
          'name': m.name,
          'headimg': m.avatar
        };
        Session curSession  = await createSinglePageParam(dataDic);

        if(l.containsKey(m.initial)){
          tempL.add(SessionUIData(
            session: curSession,
            selected: false
          )
            ..userId = m.userId
            ..name = m.name
            ..avatar = m.avatar
            ..imId = m.imId
            ..initial = m.initial
            ..session = curSession
          );
          l[m.initial ?? ''] = tempL;
        }
      }
    }

    _removeNullMembers(letterList);

    for (var map in letterList) {
      var key = map.keys.first;
      var value = map[key];
      var l = value as List<SessionUIData>;
      dataList.add(SessionUIData(
        selected: false,
        session: Session(sessionId: '')
      )..initial = key);
  
      dataList.addAll(l);
    }

    uiList.clear();
    uiList.addAll(dataList);

    update();
  }

  navigateBack() {
    if (isNative.value) {
      onClose();
      Channel().invoke(Channel_Native_Back, {});
    } else {
      Get.back();
    }
  }

  showMembers() {
    if(StringUtil.isEmpty(currentKey)){
      searchJobEditingController.clear();
      searchJobFocusNode.unfocus();
      _buildOriginUIList();
    }else {
      var dulist = dataList.map((e) => e.userId).toSet();
      var copyList = dataList.map((e) => e).toList();
      copyList.retainWhere((element) => dulist.remove(element.userId));
      var result = copyList.where((element) => (element.name?? '').contains(currentKey)).toList();
      uiList.clear();
      uiList.addAll(result);
    }
    update();
  }

  _buildOriginUIList() {
    uiList.clear();
    uiList.addAll(dataList);
  }

  searchMembers(String key) {
    currentKey = key;
    showMembers();
  }

  clearInput() {
    currentKey = '';
    searchJobEditingController.clear();
    showMembers();
  }

  onItemSelect(SessionUIData model) async {
    var selects = uiList.where((e) => e.selected).toList();
    if(selects.length >= 10 && model.selected == false){
      toast('最多选择10个');
      return;
    }
    model.selected = !model.selected;

    update();
  }

  Color sendButtonTextColor() {
    var selects = uiList.where((e) => e.selected).toList();
    if(selects.isEmpty) return Colors.grey;
    return ColorConfig.themeCorlor;
  }

  sendInvite() async {
    var selects = uiList.where((e) => e.selected).toList();
    if(selects.isEmpty){
      toast('请至少选择一个会话');
      return;
    }

    if(imagePath == null){
      sendInviteSocket();
    }else{
      sendImage();
    }
  }

  Future createSinglePageParam(Map dataDic) async {
    var userId = dataDic['userId'];
    var name = dataDic['name'];
    var headimg = dataDic['headimg'];
    var sessionId = dataDic['imId'];
    var ownId = await UserHelper.getUid();
    var session =
        await DbHelper.getSessionByOwnerId2SessionId(ownId, sessionId);
    if (session == null) {
      session = await createNewSession(
          sessionId, headimg, name, 1, 1, '', '', 0, 0, 0, false, userId, 0);
      await DbHelper.insertSession(session);
    }
    return session;
  }


  Future sendImage() async {
    if(imagePath == null){
      toast('未找到要分享的图片');
      return;
    }

    var selects = uiList.where((e) => e.selected).toList();

    var shareBody = ShareImageBody(
      selecters: selects,
      path: imagePath
    );

    var body = jsonEncode(shareBody.toJson());

    var param = {
      'action': 'shareImage',
      'body': body
    };

    // invokeMainEngine(param);
    Channel().invoke(BRIDGET_TO_MAIN_NATIVE_ENGINE, param);
    navigateBack();
  }


  Future sendInviteSocket() async {
    var companyName = fromModel.name;
    var companyId = fromModel.companyId;
    var companyLogo = fromModel.logo;
    var map = {
      'text': '团队邀请【$companyName】',
      'extendTwo': companyId,
      'extendThree': companyName,
      'imgUrl': companyLogo
    };

    CustomMsgModel customMsgModel = CustomMsgModel.fromJson(map);

    var selects = uiList.where((e) => e.selected).toList();


    await Get.loading();
    await Future.forEach(selects, (sessionUI) async {
      var targetSessionId = sessionUI.session.sessionId;
      var targetSessionType = sessionUI.session.sessionType ?? 1;
      var msg = await sessionUI.session.cacheExtMessage(
        targetSessionId, 
        targetSessionType,
        map,
        ConstantImMsgType.SSChatMessageTypeInvite
      );
      await DbHelper.insertMsg(msg);
      await _updateSession(sessionUI.session , msg , text: customMsgModel.text);
      await sessionUI.session.sendExtBySocket(msg.cmdId ?? '', msg);
    });
    await Get.dismiss();
    Get.back();
  }

  _updateSession(Session session ,Message? message, {String? text}) async {
    if (message == null) return;
    var msgContent = await message.alias();
    session.msgContent = msgContent;
    session.msgType = message.msgType;
    if (text != null) {
      session.msgContent = text;
    }
    session.msgTime = message.sendTime;
    logger('发送消息的时候 存储session = ${session}');
    DbHelper.insertSession(session);
  }


  _createLetterSerList() {
    return BaseInfo.letters.map((e) {
      var map = HashMap<String,List<SessionUIData>>();
      map.putIfAbsent(e, () => []);
      return map;
    }).toList();
  }

  _removeNullMembers(List<Map<String,List<SessionUIData>>> letterList) {
    letterList.removeWhere((element) {
      var key = (element as Map).keys.first;
      if(element[key] is List){
        if( (element[key] as List).isEmpty ){
          return true;
        }
      }
      return false;
    });
  }


  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }

}


class SessionUIData {
  String? userId;
  String? name;
  String? imId;
  String? avatar;
  String? initial;
  Session session = Session(sessionId: "");
  bool selected = false;


  @override
  String toString() {
    return "{$userId , $avatar , $name ,$initial, $selected} ${session.toString()}";
  }

  SessionUIData({this.userId, this.name, this.imId, this.avatar, this.initial,
    required this.session, required this.selected});

  factory SessionUIData.fromJson(Map<String, dynamic> json) {
    return SessionUIData(
      userId: json['userId'],
      name: json['name'],
      imId: json['imId'],
      avatar: json['avatar'],
      initial: json['initial'],
      session: Session.fromJson(json['session']),
      selected: json['selected'],
    );
  }

  Map<String, dynamic> toJson() => {
        'userId': userId,
        'name': name,
        'imId': imId,
        'avatar': avatar,
        'initial': initial,
        'session': session.toJson(),
        'selected': selected,
      };
}

class ShareImageBody {
  List<SessionUIData>? selecters = <SessionUIData>[];
  String? path = '';

  ShareImageBody({this.selecters, this.path});

  factory ShareImageBody.fromJson(Map<String, dynamic> json) {
    return ShareImageBody(
      selecters: json['selecters'] == null ? List<SessionUIData>.from([]) :
      List<SessionUIData>.from(
          json['selecters'].map((x) => SessionUIData.fromJson(x))),
      path: json['path'],);
  }

  Map<String, dynamic> toJson() =>
      {'selecters': selecters?.map((e) => e.toJson()).toList(), 'path': path,};


}