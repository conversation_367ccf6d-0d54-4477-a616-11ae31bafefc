import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';

import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../common/channel/channel.dart';
import '../controllers/test_controller.dart';

class TestView extends GetView<TestController> {
  const TestView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TitleBar().backAppbar(context, '', true, [], onPressed: (){
          
        }),
        body: Column(
          children: [
            Container(
                child: Cupert<PERSON>Button(
                    child: Text('退出登录'),
                    onPressed: () async {
                      Channel().invoke('existLogin', {});
                      SharedPreferences preferences =
                          await SharedPreferences.getInstance();
                      await preferences.clear();
         
                      Get.toNamed('/login');
                    })),
          ],
        ));
  }
}
