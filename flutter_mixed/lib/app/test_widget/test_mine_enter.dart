

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../common/config/config.dart';
import '../routes/app_pages.dart';
import '../utils/cache_helper.dart';
import '../utils/login_util.dart';

class TestMineEnter extends StatelessWidget {

  const TestMineEnter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        10.gap,
        _buildEnter('测试_退出登录', () async {
          await LoginUtil.reLogin(invokeNative: false);
          Get.offAllNamed(Routes.LOGIN);
        }),
        _buildEnter('测试_清除缓存', () async {
          CacheHelper.clear();
        }),
      ],
    );
  }

  _buildEnter(String title , Function action) {
    return InkWell(
      onTap: (){
        action.call();
      },
      child: Container(
        color: ColorConfig.whiteColor,
        margin: EdgeInsets.only(bottom: 2),
        height: 48,
        padding: EdgeInsets.only(left: 24, right: 24),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              child: Image.asset('assets/images/3.0x/mineHome_about.png'),
            ),
            SizedBox(
              width: 8,
            ),
            Expanded(
                child: Container(
                  child: Text(
                    title,
                    style: TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                )),
            SizedBox(
              width: 8,
            ),
            SizedBox(
              width: 9,
              height: 17,
              child: Image.asset('assets/images/3.0x/mine_right.png'),
            ),
          ],
        ),
      ),
    );
  }

}