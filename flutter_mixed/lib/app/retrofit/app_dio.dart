import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/extension/http_extension.dart';

import 'interceptor/connect_error_interceptor.dart';



class AppDio with DioMixin implements Dio {
  AppDio._([BaseOptions? options]) {
    options = BaseOptions(
      baseUrl: '${Host.HOST}',
      contentType: 'application/json',
      connectTimeout: const Duration(seconds: 20),
      sendTimeout: const Duration(seconds: 20),
      receiveTimeout: const Duration(seconds: 20),
    );

    this.options = options;

    // 统一添加拦截器
    addInterceptors(isAddHeader: true);

    // !!!! 给 retrofit 添加网络错误拦截器，防止 异常抛出阻断业务，注： httpUtil 不添加，有额外处理逻辑
    interceptors.add(ConnectErrorInterceptor(this));
  }

  static Dio getInstance() => AppDio._();
}

