import 'dart:collection';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/utils/http.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../common/api/Define.dart';
import '../../common/widgets/widgets.dart';
import '../../utils/storage.dart';

var retryPaths = [ApproveApi.APPROVEALLORGDATA ,
  ApproveApi.APPROVESEARCHORGDATA,
];


var noTipPaths = [ MeetingApi.MEETING_CHECK_OUT , 'oauth/'];

bool matchPath(String path) {
  for(var e in retryPaths) {
    if(path.contains(e)){
      return true;
    }
  }
  return false;
}

bool errorMsgTip(String path) {
  for(var e in noTipPaths){
    if(path.contains(e)){
      return true;
    }
  }
  return false;
}

class HeaderInterceptor extends Interceptor {

  Dio dio;

  HeaderInterceptor(this.dio);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    await addHeader(options , matchPath(options.path),  true);
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // try {
    //   if(errorMsgTip(err.requestOptions.path)){
    //     super.onError(err, handler);
    //   }else{
    //     Get.dismiss;
    //     var r = createDioErrorResponse(err);
    //     handler.resolve(r);
    //     return;
    //   }
    // } catch (e) {
    //   print(e);
    // }
    super.onError(err, handler);
  }

  createDioErrorResponse(DioException err) {
    return Response(
        data: HashMap<String, dynamic>()
          ..putIfAbsent('code', () => 999)
          ..putIfAbsent('msg', () => err.message ?? '网络开小差了')
          ..putIfAbsent('data', () => ''),
        requestOptions: err.requestOptions);
  }
}


