import 'dart:collection';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:get/get_core/src/get_main.dart';

import 'header_interceptor.dart';


class ConnectErrorInterceptor extends Interceptor {

  Dio dio;

  ConnectErrorInterceptor(this.dio);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    try {
      if(errorMsgTip(err.requestOptions.path)){
        super.onError(err, handler);
      }else{
        Get.dismiss;
        var r = createDioErrorResponse(err);
        handler.resolve(r);
        return;
      }
    } catch (e) {
      print(e);
    }
    super.onError(err, handler);
  }

  createDioErrorResponse(DioException err) {
    return Response(
        data: HashMap<String, dynamic>()
          ..putIfAbsent('code', () => 999)
          ..putIfAbsent('msg', () => '担当：网络开小差了')
          ..putIfAbsent('data', () => ''),
        requestOptions: err.requestOptions);
  }
}


