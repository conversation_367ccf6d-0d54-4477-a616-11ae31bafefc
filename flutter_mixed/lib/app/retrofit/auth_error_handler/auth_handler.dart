
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:get/get.dart';

import '../../common/dialog/reset_login_dialog.dart';
import '../../../logger/logger.dart';
import '../../utils/login_util.dart';


class AuthErrorHandler {
  AuthErrorHandler._(); // 私有构造函数
  static final AuthErrorHandler _instance = AuthErrorHandler._(); // 单例实例
  factory AuthErrorHandler() => _instance; // 获取实例的方法

  bool _isHandlingAuthError = false; // 防止重复触发标志

  // 这个方法由拦截器调用，它是集中处理逻辑的入口
  Future<void> handleAuthRequired(int code, String message) async {
    if (_isHandlingAuthError) {
      logger('AuthErrorHandler: 已经在处理认证失败，忽略重复通知 (code: $code)');
      return;
    }
    _isHandlingAuthError = true;
    logger('AuthErrorHandler: 触发认证失败处理流程 (code: $code)...');

    try {
      if (Get.isDialogOpen == true) {
        try { await Get.dismiss(); } catch(e) { logger('AuthErrorHandler: 关闭对话框失败: $e'); }
      }

      await LoginUtil.reLogin(invokeNative: true); // 执行核心重新登录逻辑

      showReLoginDialog(message); // 显示给用户的弹框

      // --- 结束集中处理逻辑 ---

    } catch (e) {
      logger('AuthErrorHandler: 处理流程中发生错误: $e');
      // 处理处理流程本身的异常
    } finally {
      _isHandlingAuthError = false;
      logger('AuthErrorHandler: 认证失败处理流程结束.');
    }
  }

// void handleGenericError(String message) { ... }
}

