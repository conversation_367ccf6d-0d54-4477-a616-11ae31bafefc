import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/add_permission_resp.dart';
import 'package:flutter_mixed/app/retrofit/entity/org_permission/edit_permission_resp.dart';
import 'package:retrofit/retrofit.dart';

import '../../common/api/LoginApi.dart';
import '../entity/approve/approve_models_resp.dart';
import '../entity/base_resp.dart';
import '../entity/org_permission/org_permission_resp.dart';
part 'org_permission_datasource.g.dart';

@RestApi()
abstract class OrgPermissionDatasource {

  factory OrgPermissionDatasource(Dio dio, {String baseUrl}) = _OrgPermissionDatasource;

  @GET('${ORGApi.GET_ORG_PERMISSIONS}/{orgid}')
  Future<BaseResp<List<OrgPermissionResp>>> getOrgPermissions(@Path("orgid") String? orgid);

  @POST('${ORGApi.PERMISSIONS_EDIT}/{orgid}')
  Future<BaseResp<dynamic>> addOrgPermissions(@Path("orgid") String? orgid,@Body() AddPermissionResp body);

  @PUT('${ORGApi.PERMISSIONS_EDIT}/{orgId}')
  Future<BaseResp<dynamic>> editOrgPermissions(@Path("orgId") String? orgId,@Body() EditPermissionResp body);

  @DELETE('${ORGApi.PERMISSIONS_EDIT}/{orgId}/{managerId}')
  Future<BaseResp<dynamic>> deleteOrgPermissions(@Path("orgId") String? orgId,@Path("managerId") String? managerId);
}
