import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../../common/api/LoginApi.dart';
import '../../modules/contact/model/org/all_org_model.dart';
import '../entity/approve/approve_models_resp.dart';
import '../entity/base_resp.dart';
import '../entity/group/group_info.dart';
import '../entity/group/reset_group_manager.dart';
import '../entity/work/group_response.dart';
part 'approve_datasource.g.dart';

@RestApi()
abstract class ApproveDataSource {
  factory ApproveDataSource(Dio dio, {String baseUrl}) = _ApproveDataSource;

  @GET('${ApproveApi.APPROVEMODELLIST}/{orgid}')
  Future<BaseResp<List<ApproveModelResp>>> getApproveModels(@Path("orgid") String? orgid);

  @PUT('${ApproveApi.APPROVEMCURRENTSTATUS}/{approveId}')
  Future<BaseResp<dynamic>> changeKingdeeStatus(@Path("approveId") String approveId, @Query("approveAssigneeId") String? approveAssigneeId);

}
