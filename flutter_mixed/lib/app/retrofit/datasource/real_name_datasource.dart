import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/retrofit/entity/real_name/cos_token_req.dart';
import 'package:flutter_mixed/app/retrofit/entity/real_name/upload_pic_req.dart';
import 'package:flutter_mixed/app/retrofit/entity/real_name/verify_idcard_req.dart';
import 'package:retrofit/http.dart';
import '../entity/base_resp.dart';
import '../entity/real_name/verify_resp.dart';
import '../entity/real_name/very_status_resp.dart';

part 'real_name_datasource.g.dart';

@RestApi()
abstract class RealNameDataSource{
  factory RealNameDataSource(Dio dio, {String baseUrl}) = _RealNameDataSource;

  @POST(LoginApi.VERIFY_IDCARD)
  Future<BaseResp<VerifyResponse?>> verifyIdcard(@Body() VerifyIdcardRequest body);

  @POST(LoginApi.REALNAME_UPLOAD)
  Future<BaseResp<VerifyResponse?>> uploadPic(@Body() UploadPicReq body);

  @GET(LoginApi.REALNAME_VERIFY_STATUS)
  Future<BaseResp<VerifyStatusResp?>> getVerifyStatus();

  @POST(LoginApi.REALNAME_COS_TOKEN)
  Future<BaseResp<VerifyResponse?>> getCosToken(@Body() CosTokenRequest body);

}
