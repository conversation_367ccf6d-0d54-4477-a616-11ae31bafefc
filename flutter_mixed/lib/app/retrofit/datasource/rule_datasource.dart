import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_detail_resp.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_item_resp.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_list_model.dart';
import 'package:flutter_mixed/app/modules/workStand/regimes/model/regimes_power_model.dart';
import 'package:flutter_mixed/app/retrofit/entity/base_resp.dart';
import 'package:retrofit/http.dart';

part 'rule_datasource.g.dart';

@RestApi()
abstract class RuleDataSource {
  factory RuleDataSource(Dio dio, {String baseUrl}) = _RuleDataSource;

  @GET('${RuleApi.RULE_ORG_POWER}/{orgId}')
  Future<BaseResp<RegimesPowerModel?>> checkRulePower(@Path("orgId") String? orgId);

  @POST(RuleApi.RULE_CATEGORY_LIST)
  Future<BaseResp<List<RegimesItemResp?>?>> getRuleList(@Body() RegimesListModel body);

  @GET('${RuleApi.RULE_INFO}/{ruleId}')
  Future<BaseResp<RegimesDetailResp?>> getRuleInfo(@Path("ruleId") String? ruleId);

}
