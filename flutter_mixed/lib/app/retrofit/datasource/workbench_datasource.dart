import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/all_org_user.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/search_org_members_model.dart';
import 'package:flutter_mixed/app/modules/mine/model/park_address_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/function_add_model.dart';
import 'package:retrofit/retrofit.dart';

import '../../common/api/LoginApi.dart';
import '../../im/model/im_session_info.dart';
import '../../modules/contact/model/org/all_org_model.dart';
import '../../modules/workStand/models/appprove_corg_model.dart';
import '../entity/base_resp.dart';
import '../entity/group/group_info.dart';
import '../entity/group/reset_group_manager.dart';
import '../entity/work/group_response.dart';
import '../entity/ai/ai_power_resp.dart';
part 'workbench_datasource.g.dart';

@RestApi()
abstract class WorkbenchDataSource {
  factory WorkbenchDataSource(Dio dio, {String baseUrl}) = _WorkbenchDataSource;

  @GET('relation/groups/v1/type/{type}')
  Future<BaseResp<List<GroupRespItem>>> getGroup(
    @Path("type") String? type,
  );

  @GET(ORGApi.GET_ALL_COMPANY_INFO)
  Future<BaseResp<AllOrgModel>> getAllCompanies();

  @GET('relation/groups/v1/{groupId}')
  Future<BaseResp<List<GroupInfo>>> getGroupInfo(
    @Path("groupId") String? groupId,
  );

  // 设置/移除管理员
  @POST('relation/groups/v2/Management')
  Future<BaseResp<dynamic>> resetManager(@Body() ResetGroupManagerReq body);

  // 批量删除群成员
  @DELETE('relation/groups/v1/member')
  Future<BaseResp<String>> removeGroupMembers(
      @Body() RemoveGroupMembersReq body);

  // 转让群组
  @PUT('relation/groups/v1')
  Future<BaseResp<dynamic>> transformCreate(@Body() TransformCreateReq body);

  // 修改群组名称 //flutter pub run build_runner build --delete-conflicting-outputs
  @PUT(LoginApi.IM_GROUP_CREATE)
  Future<BaseResp<dynamic>> changeGroupName(@Body() ChangeGroupNameReq body);

  // 生成群二维码
  @POST(LoginApi.IM_GROUP_QR_CODE)
  Future<BaseResp<String>> getGroupQrCode(@Body() GroupQrCodeReq body);

  // 扫描二维码获取详情
  @GET('${LoginApi.IM_GROUP_QR_CODE}/{qr}')
  Future<BaseResp<GroupQrCodeDataModel?>> getGroupQrCodeData(
      @Path("qr") String? qr);

  // 扫描二维码进群
  @GET('${LoginApi.IM_GROUP_QR_CODE_JOIN}/{qr}')
  Future<BaseResp<dynamic>> scanGroupQrCodeJoin(@Path("qr") String? qr);

  //获取公司所有人员
  @GET(ORGApi.GETORGUSERS)
  Future<BaseResp<List<AllOrgUserModel?>?>> getOrgUsers(
      @Query("orgIds") List<String> orgIds);

  //获取金蝶token
  @GET(ApproveApi.KINGDEE_TOKEN)
  Future<BaseResp<dynamic>> getKingDeeToken();

  // 获取个人资料
  @GET('${ORGApi.COMPANYUSERINFO}/{userId}')
  Future<BaseResp<SearchMemberModel?>> getUserInfo(@Path("userId") String userId , @Query("orgId") String? orgId , @Query("deptId") String? deptId, @Query("type") int? type);

  //获取领餐状态
  @POST('park/park/meals/pickup')
  Future<BaseResp<dynamic>> getMealsStatus(@Body() ParkAddressModel addressModel);

  //获取员工福利入口权限
  @GET('park/park/pay/israngeparkuser')
  Future<BaseResp<dynamic>> getIsRangeParkUser();

  @GET(ORGApi.AI_ENTRY_LIST)
  Future<BaseResp<List<AIPowerItem>>> getAiEntryList();

  //通过sessionId查询头像名称
  @POST(LoginApi.GETINFOWITHIMID)
  Future<BaseResp<List<ImSessionInfo?>?>> getInfoWithSessionIds(@Body() List<String> sessionIds);

  @GET('org/company/approve/{orgId}/v1')
  Future<BaseResp<List<AppproveCorgModel?>?>> getCorgIds(@Path("orgId") String orgId);

  //获取常用应用
  @GET('${ORGApi.GET_TOP_FUNCTION}/{orgId}')
  Future<BaseResp<dynamic>> getTopFunctionData(@Path("orgId") String orgId,@CancelRequest() CancelToken? cancelToken);

  //添加常用应用
  @POST(ORGApi.GET_TOP_FUNCTION)
  Future<BaseResp<dynamic>> addTopFunctionData(@Body() FunctionAddModel model);
}
