/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class ModelItem {
  String? modelId;
  String? modelName;
  String? modelLogo;
  String? modelDesc;
  int? visibleAble;
  int? status;
  int? version;

  ModelItem({this.modelId, this.modelName, this.modelLogo, this.modelDesc, this.visibleAble, this.status, this.version});

  ModelItem.fromJson(Map<String, dynamic> json) {
    modelId = json['modelId'];
    modelName = json['modelName'];
    modelLogo = json['modelLogo'];
    modelDesc = json['modelDesc'];
    visibleAble = json['visibleAble'];
    status = json['status'];
    version = json['version'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['modelId'] = modelId;
    data['modelName'] = modelName;
    data['modelLogo'] = modelLogo;
    data['modelDesc'] = modelDesc;
    data['visibleAble'] = visibleAble;
    data['status'] = status;
    data['version'] = version;
    return data;
  }
}

class ApproveModelResp {
  String? modelGroupId;
  String? modelGroupName;
  List<ModelItem>? modelList;
  int? status;

  ApproveModelResp({this.modelGroupId, this.modelGroupName, this.modelList, this.status});

  ApproveModelResp.fromJson(Map<String, dynamic> json) {
    modelGroupId = json['modelGroupId'];
    modelGroupName = json['modelGroupName'];
    if (json['modelList'] != null) {
      modelList = <ModelItem>[];
      json['modelList'].forEach((v) {
        modelList!.add(ModelItem.fromJson(v));
      });
    }
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['modelGroupId'] = modelGroupId;
    data['modelGroupName'] = modelGroupName;
    data['modelList'] =modelList != null ? modelList!.map((v) => v?.toJson()).toList() : null;
    data['status'] = status;
    return data;
  }
}

