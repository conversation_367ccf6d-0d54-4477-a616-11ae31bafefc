
import 'package:json_annotation/json_annotation.dart';
part 'base_resp.g.dart';


@JsonSerializable(genericArgumentFactories: true)
class BaseResp<T> {

  int code;
  String msg;
  T data;

  BaseResp(this.code, this.msg, this.data);

  factory BaseResp.fromJson(Map<String, dynamic> json,
      T Function(dynamic json) fromJsonT,) =>
      _$BaseRespFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$BaseRespToJson(this, toJsonT);


  bool success() {
    return code == 1;
  }

  @override
  String toString() {
    StringBuffer sb = StringBuffer('{');
    sb.write("\"code\":$code");
    sb.write(",\"msg\":\"$msg\"");
    sb.write(",\"data\":\"$data\"");
    sb.write('}');
    return sb.toString();
  }

}