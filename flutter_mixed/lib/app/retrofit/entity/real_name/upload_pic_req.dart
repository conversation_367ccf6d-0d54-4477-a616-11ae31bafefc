class UploadPicReq{
  String? pic;//本⼈照⽚对象存储key。例：ddbes-kyc/131003200808088888
  String? pic1;//身份证正⾯照对象存储key。例：ddbes-kyc/131003200808088888-1
  String? pic2;//身份证背⾯照对象存储key。例：ddbes-kyc/131003200808088888-2

  UploadPicReq({
    this.pic,
    this.pic1,
    this.pic2,
  });

  factory UploadPicReq.fromJson(Map<String, dynamic> json) {
    return UploadPicReq(
      pic: json['pic'],
      pic1: json['pic1'],
      pic2: json['pic2'],
    );
  }

  Map<String, dynamic> toJson() => {
        'pic': pic,
        'pic1': pic1,
        'pic2': pic2,
      };
}