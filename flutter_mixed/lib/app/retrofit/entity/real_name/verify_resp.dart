import 'package:flutter_mixed/app/common/cos/real_name_cos.dart';

import '../../../common/cos/cosPanResp.dart';

class VerifyResponse{
  int? status; //若身份证姓名匹配，此值返回1，可进⾏下⼀步提交身份证照⽚提交完正反⾯后，此值返回2，可进⾏下⼀步提交本⼈照⽚；提交完
               //本⼈照⽚后，此值返回3，进⼊⼈⼯审核阶段
  RealNameCos? token; //上传身份证正反⾯⾄对象存储所需的STS认证体
  String? keyPrefix;//上传时fileId字段所需前缀
  String? bucket;

  VerifyResponse({
    this.status,
    this.token,
    this.keyPrefix,
    this.bucket,
  });

  factory VerifyResponse.fromJson(Map<String, dynamic> json) {
    return VerifyResponse(
      status: json['status'],
      token: json['token'] == null ? null : RealNameCos.fromJson(json['token']),
      keyPrefix: json['keyPrefix'],
      bucket: json['bucket'],
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'token': token?.toJson(),
        'keyPrefix': keyPrefix,
        'bucket': bucket,
      };
}