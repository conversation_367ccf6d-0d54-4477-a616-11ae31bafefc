class VerifyStatusResp{
  int? status; //3进⼊⼈⼯审核阶段；4审核完成，此时通过第⼀阶段的接⼝调⽤kyc状态为1
  String? idCardNum;
  String? name;
  int? createTime; //提交时间

  VerifyStatusResp({
    this.status,
    this.idCardNum,
    this.name,
    this.createTime,
  });

  factory VerifyStatusResp.fromJson(Map<String, dynamic> json) {
    return VerifyStatusResp(
      status: json['status'],
      idCardNum: json['idCardNum'],
      name: json['name'],
      createTime: json['createTime'],
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'idCardNum': idCardNum,
        'name': name,
        'createTime': createTime,
      };
}