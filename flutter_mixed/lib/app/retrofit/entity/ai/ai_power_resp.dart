class AIPowerItem {
  String? powerId;
  String? webUrl;
  String? agent;

  AIPowerItem({this.powerId, this.webUrl, this.agent});

  AIPowerItem.fromJson(Map<String, dynamic> json) {
    powerId = json['powerId'];
    webUrl = json['webUrl'];
    agent = json['agent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['powerId'] = powerId;
    data['webUrl'] = webUrl;
    data['agent'] = agent;
    return data;
  }
}
