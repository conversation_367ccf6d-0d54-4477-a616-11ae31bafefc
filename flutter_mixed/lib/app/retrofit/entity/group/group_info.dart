import 'package:flutter_mixed/logger/logger.dart';

class GroupInfo {
  String? groupId;
  String? name;
  String? logo;
  String? createUserId;
  int? createTime;
  int? type;
  String? orgId;
  int? receiveMode;
  int? status;
  List<User?>? users;
  int? banned;

  int? addMember; // 仅群主和管理可以添加群成员 0不开启 1开启
  int? hintMember; // 仅群主和管理可以@所有人 0不开启 1开启
  int? voice; // 仅群主和管理开启语音通话 0不开启 1开启

  GroupInfo(
      {this.groupId,
      this.name,
      this.logo,
      this.createUserId,
      this.createTime,
      this.type,
      this.orgId,
      this.receiveMode,
      this.status,
      this.users,
      this.banned,
      this.addMember,
      this.hintMember,
      this.voice});

  GroupInfo.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    name = json['name'];
    logo = json['logo'];
    createUserId = json['createUserId'];
    createTime = json['createTime'];
    type = json['type'];
    orgId = json['orgId'];
    receiveMode = json['receiveMode'];
    status = json['status'];
    if (json['users'] != null) {
      users = <User>[];
      json['users'].forEach((v) {
        users!.add(User.fromJson(v));
      });
    }
    banned = json['banned'];
    addMember = json['addMember'];
    hintMember = json['hintMember'];
    voice = json['voice'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['groupId'] = groupId;
    data['name'] = name;
    data['logo'] = logo;
    data['createUserId'] = createUserId;
    data['createTime'] = createTime;
    data['type'] = type;
    data['orgId'] = orgId;
    data['receiveMode'] = receiveMode;
    data['status'] = status;
    data['users'] =
        users != null ? users!.map((v) => v?.toJson()).toList() : null;
    data['banned'] = banned;
    data['addMember'] = addMember;
    data['hintMember'] = hintMember;
    data['voice'] = voice;
    return data;
  }
}

class User {
  String? id;
  String? headimg;
  String? version;
  String? name;
  int? identity; // 代表用户身份 0 普通用户，  1： 群主， 2：管理员
  String? initial;

  User({this.id, this.headimg, this.version, this.name, this.identity});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    headimg = json['headimg'];
    version = json['version'];
    name = json['name'];
    identity = json['identity'];
    initial = json['initial'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['headimg'] = headimg;
    data['version'] = version;
    data['name'] = name;
    data['identity'] = identity;
    data['initial'] = initial;
    return data;
  }

  @override
  String toString() {
    return 'id: $id , headimg: $headimg , name: $name , identity: $identity';
  }
}

class ChangeGroupNameReq {
  String groupId = "";
  String? name;
  String? groupAvatar;
  String? colour;
  String? logoText;

  ChangeGroupNameReq(this.groupId,
      {this.name, this.groupAvatar, this.colour, this.logoText});

  ChangeGroupNameReq.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    name = json['name'];
    groupAvatar = json['groupAvatar'];
    colour = json['colour'];
    logoText = json['logoText'];
  }

  Map<String, dynamic> toJson() => {
        'groupId': groupId,
        'name': name,
        'groupAvatar': groupAvatar,
        'colour': colour,
        'logoText': logoText
      };
}

class GroupQrCodeReq {
  String groupId = "";
  int failureTime = 1; //0 永久 1 7天 2 30天

  GroupQrCodeReq(this.groupId, this.failureTime);

  GroupQrCodeReq.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    failureTime = json['failureTime'];
  }

  Map<String, dynamic> toJson() => {
        'groupId': groupId,
        'failureTime': failureTime,
      };
}

class GroupQrCodeDataModel {
  String code = "";
  String createUserName = "";
  String logo = "";
  String name = "";
  String groupId = "";
  int members = 0;
  int exist = 0; //0在此群组 1不在

  GroupQrCodeDataModel(this.code, this.createUserName, this.logo, this.name,
      this.groupId, this.members, this.exist);

  GroupQrCodeDataModel.fromJson(Map<String, dynamic> json) {
    code = json['code'] ?? '';
    createUserName = json['createUserName'] ?? '';
    logo = json['logo'] ?? '';
    name = json['name'] ?? '';
    groupId = json['groupId'] ?? '';
    members = json['members'] ?? 0;
    exist = json['exist'] ?? 0;
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'createUserName': createUserName,
        'logo': logo,
        'name': name,
        'groupId': groupId,
        'members': members,
        'exist': exist
      };
}
