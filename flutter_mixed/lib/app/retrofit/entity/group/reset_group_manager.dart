


class ResetGroupManagerReq {

  String groupId = "";
  int operation = 0;   // 2添加为管理 0移除管理
  String targetUserId = "";


  ResetGroupManagerReq();

  ResetGroupManagerReq.fromJson(Map<String, dynamic> json) {
      groupId = json['groupId'];
      operation = json['operation'];
      targetUserId =  json['targetUserId'];
  }

  Map<String, dynamic> toJson() => {
        'groupId': groupId,
        'operation': operation,
        'targetUserId': targetUserId,
      };
}


class RemoveGroupMembersReq {
  String groupId = "";
  List<String> userIds = [];

  RemoveGroupMembersReq.fromJson(Map<String, dynamic> json) {
      groupId = json['groupId'];
      userIds = json['userIds'];
  }

  Map<String, dynamic> toJson() => {
        'groupId': groupId,
        'userIds': userIds,
      };

  RemoveGroupMembersReq();
}

class TransformCreateReq {
  String groupId = "";
  String name = "";  // 群名称
  String targetUserId = "";

  TransformCreateReq(this.groupId, this.name, this.targetUserId);

  factory TransformCreateReq.fromJson(Map<String, dynamic> json) {
    return TransformCreateReq(
      json['groupId'],
      json['name'],
      json['targetUserId'],
    );
  }

  Map<String, dynamic> toJson() => {
        'groupId': groupId,
        'name': name,
        'targetUserId': targetUserId,
      };
}