


/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class GroupRespItem {
  String? groupId;
  String? name;
  String? logo;
  String? createUserId;
  int? type;
  String? orgId;
  String? initial;

  GroupRespItem({this.groupId, this.name, this.logo, this.createUserId, this.type, this.orgId, this.initial});

  GroupRespItem.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    name = json['name'];
    logo = json['logo'];
    createUserId = json['createUserId'];
    type = json['type'];
    orgId = json['orgId'];
    initial = json['initial'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['groupId'] = groupId;
    data['name'] = name;
    data['logo'] = logo;
    data['createUserId'] = createUserId;
    data['type'] = type;
    data['orgId'] = orgId;
    data['initial'] = initial;
    return data;
  }
}

