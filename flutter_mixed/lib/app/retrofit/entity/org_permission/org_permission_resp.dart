
import 'dart:core';

class OrgPermissionResp{
  String? managerName;
  String? managerPower;
  List<String>? userIds;
  String? userNames;
  String? managerId;

  OrgPermissionResp({this.managerName, this.managerPower, this.userIds,
      this.userNames, this.managerId});

  factory OrgPermissionResp.fromJson(Map<String, dynamic> json) {
    return OrgPermissionResp(
      managerName: json['managerName'],
      managerPower: json['managerPower'],
      userIds: json['userIds']?.cast<String>(),
      userNames: json['userNames'],
      managerId: json['managerId'],
    );
  }

  Map<String, dynamic> toJson() => {
        'managerName': managerName,
        'managerPower': managerPower,
        'userIds': userIds,
       // 'userNames': userNames,
        'managerId': managerId,
      };
}