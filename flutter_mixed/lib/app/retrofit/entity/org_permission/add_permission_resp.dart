import 'package:flutter_mixed/app/retrofit/entity/org_permission/base_permission_resp.dart';

//新增权限接口model
class AddPermissionResp extends BasePermissionResp {
  AddPermissionResp({super.managerName, super.managerPower, super.userIds});

  factory AddPermissionResp.fromJson(Map<String, dynamic> json) {
    return AddPermissionResp(
        managerName: json['managerName'],
        managerPower: json['managerPower'],
        userIds: json['userIds']?.cast<String>());
  }
  @override
  Map<String, dynamic> toJson() => {
        'managerName': managerName,
        'managerPower': managerPower,
        'userIds': userIds,
      };
}
