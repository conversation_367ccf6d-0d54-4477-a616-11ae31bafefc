class BasePermissionResp{
  String? managerName;
  String? managerPower;
  List<String>? userIds;
  String? managerId;

  BasePermissionResp({this.managerName, this.managerPower, this.userIds,this.managerId});

  factory BasePermissionResp.fromJson(Map<String, dynamic> json) {
    return BasePermissionResp(
      managerName: json['managerName'],
      managerPower: json['managerPower'],
      userIds: json['userIds']?.cast<String>(),
      managerId: json['managerId']
    );
  }

  Map<String, dynamic> toJson() => {
        'managerName': managerName,
        'managerPower': managerPower,
        'userIds': userIds,
        'managerId':managerId
      };
}