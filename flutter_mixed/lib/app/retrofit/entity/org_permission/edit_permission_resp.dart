import 'package:flutter_mixed/app/retrofit/entity/org_permission/base_permission_resp.dart';

class EditPermissionResp extends BasePermissionResp {
  EditPermissionResp(
      {super.managerName, super.managerPower, super.userIds, super.managerId});
  factory EditPermissionResp.fromJson(Map<String, dynamic> json) {
    return EditPermissionResp(
        managerName: json['managerName'],
        managerPower: json['managerPower'],
        userIds: json['userIds']?.cast<String>(),
        managerId: json['managerId']);
  }
  @override
  Map<String, dynamic> toJson() => {
        'managerName': managerName,
        'managerPower': managerPower,
        'userIds': userIds,
        'managerId': managerId
      };
}
