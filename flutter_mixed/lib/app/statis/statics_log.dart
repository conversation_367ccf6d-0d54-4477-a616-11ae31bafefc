
import 'package:flutter_mixed/app/statis/statistics_helper.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

/// 借助getx 路由 实现 pv 监控
class PageTimeTracker {
  static final Map<String, Stopwatch> _pageStopwatches = {};
  static String? _currentRoute;

  // 开始计时（进入页面时调用）
  static void startTracking(String routeName) {
    _currentRoute = routeName;
    _pageStopwatches[routeName] = Stopwatch()..start();
    StatisticsHelper.onEvent(StatisticsEventConstant.PAGE_START);

    // print('⏱️ 页面 [$routeName] 开始计时');
  }

  // 结束计时（离开页面时调用）
  static void endTracking(String routeName) {
    final stopwatch = _pageStopwatches[routeName];
    if (stopwatch != null && stopwatch.isRunning) {
      stopwatch.stop();
      final duration = stopwatch.elapsed;
      // logger('路由： 页面 [$routeName] 停留时长: ${duration.inSeconds}秒 ${duration.inMilliseconds}ms');
      _pageStopwatches.remove(routeName);
      var p = {
        'during': '${duration.inSeconds}秒 ${duration.inMilliseconds}ms'
      };
      StatisticsHelper.onEventMap(StatisticsEventConstant.PAGE_END, p);
    }
  }

  // 全局路由监听
  static void trackAllPages(Routing? routing) {
    if(routing == null) return;
    // 离开旧页面时结束计时
    if (_currentRoute != null && _currentRoute != routing.current) {
      endTracking(_currentRoute!);
    }

    // 进入新页面时开始计时
    if (routing.current != null) {
      startTracking(routing.current!);
    }
  }
}