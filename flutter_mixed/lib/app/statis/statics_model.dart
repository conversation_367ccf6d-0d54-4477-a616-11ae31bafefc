

class StaticsLog {
  String? url;

  String? method;

  int? code;

  String? time;

  String? msg;

  String? request;

  String? response;

  String? extra;

  StaticsLog(
      this.url,{
        this.method,
        this.code,
        this.time,
        this.msg,
        this.extra
      }
      );

  Map<String, dynamic> toMap() => {
    'url': url ?? '',
    'method': method ?? '',
    'code': code ?? 1,
    'msg': msg ?? '',
    'time': time ?? '',
    'extra': extra ?? '',
    'request': request ?? '',
    'response': response ?? ''
  };
  StaticsLog.fromJson(Map<String, dynamic> json) {
    url = json['url'];
    method = json['method'];
    code = json['code'];
    msg = json['msg'];
    time = json['time'];
    extra = json['extra'];
    request = json['request'];
    response = json['response'];
  }

  @override
  String toString() {
    return 'StaticsLog{ url: $url, method: $method, code: $code , time: $time, msg: $msg, request: $request, response: $response, extra: $extra}';
  }
}