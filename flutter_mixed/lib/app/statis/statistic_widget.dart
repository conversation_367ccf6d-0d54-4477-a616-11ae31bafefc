

// lib/utils/tracking_wrapper.dart
import 'package:flutter/widgets.dart';
import 'package:flutter_mixed/app/statis/statistics_helper.dart';

/// 事件包装器
class TrackingWrapper {
  // 同步操作包装器
  static R trackSync<R>({
    required R Function() action,
    required String eventName,
    Map<String, dynamic>? params,
    Map<String, dynamic>? context,
    bool immediate = false,
  }) {
    try {
      final result = action();

      StatisticsHelper.onEventMap(eventName , params);

      return result;
    } catch (e, stack) {

      StatisticsHelper.onEvent(eventName , value: stack.toString());

      rethrow;
    }
  }

  // 异步操作包装器
  static Future<R> trackAsync<R>({
    required Future<R> Function() action,
    required String eventName,
    Map<String, dynamic>? params,
    Map<String, dynamic>? context,
    bool immediate = false,
    bool trackSuccess = true,
    bool trackError = true,
  }) async {
    try {
      final result = await action();
      if (trackSuccess) {

        StatisticsHelper.onEventMap(eventName , params);

      }
      return result;
    } catch (e, stack) {
      if (trackError) {

        StatisticsHelper.onEvent(eventName , value: stack.toString());

      }
      rethrow;
    }
  }

  // 页面级包装器（暂留，不需要调用，已有路由监控）
  static Widget wrapPage({
    required Widget child,
    required String pageName,
    Map<String, dynamic>? pageParams,
  }) {
    return _PageTrackingWrapper(
      pageName: pageName,
      pageParams: pageParams,
      child: child,
    );
  }
}

class _PageTrackingWrapper extends StatefulWidget {
  final String pageName;
  final Map<String, dynamic>? pageParams;
  final Widget child;

  const _PageTrackingWrapper({
    required this.pageName,
    required this.child,
    this.pageParams,
  });

  @override
  State<_PageTrackingWrapper> createState() => _PageTrackingWrapperState();
}

class _PageTrackingWrapperState extends State<_PageTrackingWrapper> {
  @override
  void initState() {
    super.initState();
    StatisticsHelper.onEvent(StatisticsEventConstant.PAGE_START);
  }

  @override
  void dispose() {
    StatisticsHelper.onEvent(StatisticsEventConstant.PAGE_END);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}