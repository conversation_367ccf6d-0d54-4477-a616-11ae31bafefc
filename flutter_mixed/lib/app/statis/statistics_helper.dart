import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

import '../common/api/LoginApi.dart';
import '../common/config/config.dart';
import '../utils/storage.dart';

const _androidKey = "66178728940d5a4c493d4b23";
const _iOsKey = "66382148cac2a664de2a5399";

/// 数据统计及其埋点相关（采用kibana 查询 ）
/// 注解+代码生成在Dart中实现方法拦截的代价较高，不值得投入
/// 所以采用  1 埋点控件包装器， 2 直接在事件方法内部调用埋点方法， 3 getx路由监听pv
class StatisticsHelper {

  /// 在授权[隐私协议]之后调用
  static init({bool? isAuto = false}) {
    UmengCommonSdk.initCommon(_androidKey, _iOsKey, 'dd_official');
    if(isAuto == true){
      autoCollectPageInfo();
    }else {
      manualCollectPageInfo();
    }
    preInitCommonParams();
  }

  static signIn(String uid) {
    if(StringUtil.isEmpty(uid)) return;
    UmengCommonSdk.onProfileSignIn(uid);
  }

  static signOff() {
    UmengCommonSdk.onProfileSignOff();
  }

  static autoCollectPageInfo() {
    UmengCommonSdk.setPageCollectionModeAuto();
  }

  static manualCollectPageInfo() {
    UmengCommonSdk.setPageCollectionModeManual();
  }

  // 实时埋点，取消自动上传方案
  static onEvent(String op , {String? value}) async {
    var params = await preInitCommonParams();

    var singleEvent = HashMap<String,dynamic>()
      ..putIfAbsent('time', () => _formatYYYYMMDDHHMMSS())
      ..addAll(params);

    UmengCommonSdk.onEvent(op, singleEvent);
    // logger('umeng_event >  $key : $singleEvent');

    singleEvent
      ..putIfAbsent('page', () => Get.currentRoute)
      ..putIfAbsent('op', () => op);
    if(!StringUtil.isEmpty(value)){
      singleEvent.putIfAbsent('extra', () => value);
    }

    logger('埋点 ---------  ${singleEvent}');
  }

  // 实时埋点，取消自动上传方案
  static onEventMap(String op , Map<String,dynamic>? arguments) async {
    var params = await preInitCommonParams();
    var mapEvent = HashMap<String,dynamic>()
      ..addAll(params)
      ..addAll({'time':_formatYYYYMMDDHHMMSS()});

    UmengCommonSdk.onEvent(op, mapEvent);

    mapEvent
      ..putIfAbsent('page', () => Get.currentRoute)
      ..putIfAbsent('op', () => op);

    if(arguments != null && arguments.isNotEmpty){
      mapEvent.putIfAbsent('extra', () => json.encode(arguments));
    }
    logger('埋点 ---------  ${mapEvent}');
  }

  static HashMap<String,dynamic> commonParams = HashMap<String,dynamic>();

  // 常规参数
  static Future<HashMap<String,dynamic>> preInitCommonParams() async {
    if(commonParams.isNotEmpty) return commonParams;
    var map = HashMap<String,dynamic>();
    var header = await UserDefault.getHttpHeader() ?? Map();
    var osV = await DeviceUtils.getSystemVersion();
    var did = kDebugMode ? await DeviceUtils.getUDIDStr(): (header['deviceId'] ?? '');
    if (Platform.isIOS) {
      did = header['device'] ?? '';
    }
    var v = kDebugMode ? Host.APPVERSION : header["appVersion"] ?? '';
    var uid = await UserHelper.getUid();
    map..putIfAbsent("os", () => Platform.isIOS ? 'ios' : 'android',)
      ..putIfAbsent("version", () => v)
      ..putIfAbsent("os_version", () => osV)
      ..putIfAbsent("user_id", () => uid)
      ..putIfAbsent("net", () => BaseInfo().getCurrentNetStr())
      ..putIfAbsent("device_id", () => did);
    commonParams = map;
    return commonParams;
  }

  /// 定期上传log（弃用）
  // static Future timingUploadLog() async {
  //   if(kDebugMode || EnvConfig.mEnv != Env.Product) return;
  //   var nowT = DateTime.now().millisecondsSinceEpoch;
  //   var beforeNowS = nowT-7*24*3600*1000;
  //   List<ReqLog> dataList = await DBHelper.findReqLogsByStartTime(beforeNowS,nowT);
  //   if (dataList.isNotEmpty) {
  //     var req = dataList.map((e){
  //       Map<String,dynamic> reqMap = e.toMap();
  //       reqMap.remove('id');
  //       return reqMap;
  //     }).toList();
  //     Map param = {'requestRecordDTOS':req};
  //     DioUtil().post(LoginApi.LOG_REQUEST, param, true, () {},isShowLoading: false,isShowErrorToast:false)
  //         .then((data) {
  //       if(data == null) return;
  //       if (data['code'] == 1) {
  //         DBHelper.delReqLogsByStartTime(beforeNowS,nowT);
  //       }
  //     });
  //   }
  // }

  static String _formatYYYYMMDDHHMMSS() {
    return DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now());
  }


}

class StatisticsEventConstant {

  // op , 类型字段
  static String REQ_LOG = "req_log";
  static String RESP_LOG = "resp_log";
  static String PAGE_START = "page_start_log";
  static String PAGE_END = "page_end_log";

  // oa 业务数据
  static String CLICK = "click";
  static String LONG_CLICK = "long_click";
  static String ZONE_ERROR = "error";
  static String API = "api";


}