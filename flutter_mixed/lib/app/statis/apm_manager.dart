
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:umeng_apm_sdk/umeng_apm_sdk.dart';

class ApmManager {

  static void initApm(Function(NavigatorObserver?) app , {bool? debug = false}) {
      if(debug == true){
        app(null);
      }else {
        final UmengApmSdk umengApmSdk = UmengApmSdk(
            projectType: 1   // 0 flutter app, 1 module
            , name: 'dd_flutter_module'
            , bver: '4.1.9'
            , enableLog: kDebugMode
            , env: UmengApmEnv.Release
            , flutterVersion: '3.27.0'
            ,enableTrackingPageFps: kDebugMode
            ,enableTrackingPagePerf: kDebugMode
            // ,initFlutterBinding:
            , onError: (ex , stack){
          // logger("打印异常===========>$ex");
          // logger("打印异常堆栈===========>$stack");
          _reportException(ex ,stack);
        }
        );
        umengApmSdk.init(appRunner: (observer) async {
           app(observer);
           return Container();
        });
      }
  }

  static _reportException(Object ex, dynamic stack) {
    if(ex.toString()?.contains('MissingPluginException') == true){
      logger('===========插件丢失，不上报================');
      return;
    }
    var exception = '$ex \n $stack';
    Channel().invoke(reportException, {
      'error_stack': exception
    });
  }
}

class MyApmWidgetsFlutterBinding extends ApmWidgetsFlutterBinding {
  @override
  void handleAppLifecycleStateChanged(AppLifecycleState state) {
    // 添加自己的实现逻辑
    print('AppLifecycleState changed to $state');
    super.handleAppLifecycleStateChanged(state);
  }

  static WidgetsBinding? ensureInitialized() {
    MyApmWidgetsFlutterBinding();
    return WidgetsBinding.instance;
  }
}