


import 'dart:collection';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:performance_fps/fps_computer.dart';

class FpsMonitor {

  init(bool isDebug) {
    if(isDebug){
      // 调用插件
      Fps.instance.registerCallBack((fps, dropCount) {
        // current fps
        debugPrint('fps ===> $fps');
      });

      // 自定义
      // WidgetsFlutterBinding.ensureInitialized().addTimingsCallback(
      //     _onReportTimings);
    }
  }

  FpsMonitor.__internal();

  factory FpsMonitor() => _instance;

  static final FpsMonitor _instance = FpsMonitor.__internal();

  static const maxframes = 120; // 100 帧足够了，对于 60 fps 来说
  static const _frameInterval = const Duration(
      microseconds: Duration.microsecondsPerSecond ~/ 60);

  final lastFrames = ListQueue<FrameTiming>(maxframes);

  void _onReportTimings(List<FrameTiming> timings) {
    print('原始帧率：${timings.length}');
    //把 Queue 当作堆栈用
    for (FrameTiming timing in timings) {
      //时间从小到大，也就表示老帧在前面小索引位置，新帧在后面的大索引位置
      // print(timing.timestampInMicroseconds(FramePhase.buildStart));
      // 时间大的(新帧)被安排在队列头部， 时间小的(老帧)被安排在队列尾部
      lastFrames.addFirst(timing);
    }

    // 只保留 maxframes
    while (lastFrames.length > maxframes) {
      //移除队列尾部的老帧
      lastFrames.removeLast();
    }

    print('fps=======:' + fps.toString());
  }

  double get fps {
    var lastFramesSet = <FrameTiming>[];
    for (FrameTiming timing in lastFrames) {
      if (lastFramesSet.isEmpty) {
        lastFramesSet.add(timing);
      } else {
        var lastStart = lastFramesSet.last.timestampInMicroseconds(
            FramePhase.buildStart);
        // 相邻两帧如果开始结束相差时间过大，比如大于 frameInterval * 2,是不同绘制时间段产生的,放在一起肯定会拉低fps
        // 因为相当于有一段时间fps=0,根本没有产生绘制
        if (lastStart -
            timing.timestampInMicroseconds(FramePhase.rasterFinish) >
            (_frameInterval.inMicroseconds * 2)) {
          // in different set
          // print('break...');
          break;
        }
        // print('add...');
        lastFramesSet.add(timing);
      }
    }
    var framesCount = lastFramesSet.length;

    var costCount = lastFramesSet.map((t) {
      // 耗时超过 frameInterval 会导致丢帧
      // 下面这个公式算出来的是实际上应该发生的理论总帧数，结合图理解
      return (t.totalSpan.inMicroseconds ~/ _frameInterval.inMicroseconds) + 1;
    }).fold(0, (a, b) => (a as int) + b);


    return framesCount * 60 / costCount;
  }

}




