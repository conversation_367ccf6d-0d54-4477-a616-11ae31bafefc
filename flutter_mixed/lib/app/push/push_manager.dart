import 'dart:convert';
import 'dart:io';

import 'package:factory_push/factory_push.dart';
import 'package:factory_push/factory_push_platform_interface.dart';

import '../../main.dart';
import '../common/channel/channel.dart';
import '../im/request/datasource/im_datasource.dart';
import '../im/request/entity/push_token_req.dart';
import '../../logger/logger.dart';

/// push 设备管理相关操作
class PushManager {

  String factory = ''; //推送厂商
  String pushToken = '';

  initFactoryPush() async{
    //厂商推送
    var validateNativeExist = await Channel().invoke(Channel_getNativeDeviceId);
    if(validateNativeExist != null){
      factory = await FactoryPush().getSystem()??"";
      pushToken = await FactoryPush().initFactoryPush(pushCallback)??"";
      logger("======收到pushtoken===$pushToken==================");
      if(pushToken.isNotEmpty){
        _reportPushToken();
      }
    }else {
      logger('未加载插件...');
    }
  }

  /// client 1 ios  2 android
  /// factory 厂商名称
  Future<PushTokenReqBody> makeTokenInfo() async {
    var nativeClientCode = await Channel().invoke(Channel_version_code);
    var client = Platform.isIOS? 1 : 2;
    return PushTokenReqBody(client: client, versionCode: nativeClientCode??0,
        channel: factory, deviceToken: pushToken);
  }

  void _reportPushToken() async{
    var imSource = ImDataSource(retrofitDio);
    PushTokenReqBody tokenInfo = await makeTokenInfo();
    try {
      logger('=========push token body==${tokenInfo.toJson()}');
      var resp = await imSource.reportPushToken(tokenInfo);
      if (resp.success()) {
        logger('======push token body response==${resp.data}');
      }else{
        logger('========push token ==${resp.msg}');
      }
    } catch (e) {
      logger("===report push token err===${e}");
    }
  }

  //这个callback目前没办法使用  插件无法开启主引擎
  PushCallback pushCallback = (String pushMsg){
    Map<String, dynamic> dataMap = json.decode(pushMsg);
    logger('=========push router=====> $dataMap');
  };
}
