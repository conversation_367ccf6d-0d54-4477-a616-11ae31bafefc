import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'im_platform_plugin_platform_interface.dart';

class ImPlatformPlugin {
  Future<String?> getPlatformVersion() {
    return ImPlatformPluginPlatform.instance.getPlatformVersion();
  }

  Widget backImPlatformView() {
    final Map<String, dynamic> creationParams = <String, dynamic>{};
    if (Platform.isAndroid) {
      return AndroidView(
        viewType: "im_platform_view",
        creationParams: creationParams,
        creationParamsCodec: const StandardMessageCodec(),
        gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
          Factory<OneSequenceGestureRecognizer>(
            () => EagerGestureRecognizer(),
          ),
        },
      );
    } else {
      return UiKitView(
        viewType: "im_platform_view",
        creationParams: creationParams,
        creationParamsCodec: const StandardMessageCodec(),
        gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
          Factory<OneSequenceGestureRecognizer>(
            () => EagerGestureRecognizer(),
          ),
        },
      );
    }
  }
}
