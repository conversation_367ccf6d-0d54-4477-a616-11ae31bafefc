import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'im_platform_plugin_method_channel.dart';

abstract class ImPlatformPluginPlatform extends PlatformInterface {
  /// Constructs a ImPlatformPluginPlatform.
  ImPlatformPluginPlatform() : super(token: _token);

  static final Object _token = Object();

  static ImPlatformPluginPlatform _instance = MethodChannelImPlatformPlugin();

  /// The default instance of [ImPlatformPluginPlatform] to use.
  ///
  /// Defaults to [MethodChannelImPlatformPlugin].
  static ImPlatformPluginPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [ImPlatformPluginPlatform] when
  /// they register themselves.
  static set instance(ImPlatformPluginPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
