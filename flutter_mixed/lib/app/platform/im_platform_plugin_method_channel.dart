import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'im_platform_plugin_platform_interface.dart';

/// An implementation of [ImPlatformPluginPlatform] that uses method channels.
class MethodChannelImPlatformPlugin extends ImPlatformPluginPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('im_platform_plugin');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
