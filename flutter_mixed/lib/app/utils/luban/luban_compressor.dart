import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image/image.dart' as img;
import 'package:exif/exif.dart';
import 'package:path_provider/path_provider.dart';

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img; // 确保引入image库
import 'package:exif/exif.dart';          // 需要添加exif库依赖

class LubanCompressor {
  /// 压缩入口方法
  static Future<File?> compressImage(
      File file, {
        int maxLongSide = 1440,
        int targetSizeKB = 300,
        double sharpenAmount = 0.6,
      }) async {
    try {
      Uint8List bytes = await file.readAsBytes();
      bytes = await _correctExifOrientation(bytes);
      bytes = await _resizeImage(bytes, maxLongSide: maxLongSide);
      bytes = await _compressWithDynamicQuality(bytes, targetSizeKB: targetSizeKB);
      // bytes = _applySharpen(bytes, amount: sharpenAmount);
      final outputFile = await _saveToTemp(bytes);
      return outputFile;
    } catch (e) {
      print('压缩失败: $e');
      return null;
    }
  }

  /// EXIF方向校正
  static Future<Uint8List> _correctExifOrientation(Uint8List bytes) async {
    final exifData = await readExifFromBytes(bytes);
    final orientation = exifData['Image Orientation']?.printable ?? '1';

    final image = img.decodeImage(bytes);
    if (image == null) return bytes;

    final rotatedImage = _applyExifRotation(image, orientation);
    return Uint8List.fromList(img.encodeJpg(rotatedImage));
  }

  /// EXIF旋转处理实现
  static img.Image _applyExifRotation(img.Image image, String orientation) {
    switch (orientation) {
      case '2': return img.flipHorizontal(image);
      case '3': return img.copyRotate(image, angle: 180);
      case '4': return img.flipVertical(image);
      case '5': return img.copyRotate(img.flipHorizontal(image), angle: -90);
      case '6': return img.copyRotate(image, angle: -90);
      case '7': return img.copyRotate(img.flipHorizontal(image), angle: 90);
      case '8': return img.copyRotate(image, angle: 90);
      default: return image;
    }
  }

  /// 图片缩放
  static Future<Uint8List> _resizeImage(Uint8List bytes, {required int maxLongSide}) async {
    final image = img.decodeImage(bytes);
    if (image == null) return bytes;

    final scale = _calculateScale(
      width: image.width,
      height: image.height,
      maxLongSide: maxLongSide,
    );

    final resized = img.copyResize(
      image,
      width: (image.width * scale).round(),
      height: (image.height * scale).round(),
      interpolation: img.Interpolation.cubic,
    );

    return Uint8List.fromList(img.encodeJpg(resized));
  }

  /// 比例计算（原算法核心逻辑）
  static double _calculateScale({required int width, required int height,
    required int maxLongSide , int minShortSide = 720,}) {
    final isPortrait = height > width;
    final longSide = isPortrait ? height : width;
    final shortSide = isPortrait ? width : height;

    // 计算两个比例取最小值
    final scaleByLongSide = longSide > maxLongSide ? maxLongSide / longSide : 1.0;
    final scaleByShortSide = shortSide < minShortSide ? minShortSide / shortSide : 1.0;

    return min(scaleByLongSide, scaleByShortSide);
  }

  /// 动态质量压缩
  static Future<Uint8List> _compressWithDynamicQuality(
      Uint8List bytes, {
        required int targetSizeKB,
      }) async {
    Uint8List result = bytes;
    int quality = 90;

    while (quality >= 50) { // 提高最低质量阈值
      result = await FlutterImageCompress.compressWithList(
        result,
        quality: quality,
        minHeight: 1080,    // 提升最小分辨率
        minWidth: 1080,
        format: CompressFormat.jpeg,
      );

      final currentSizeKB = result.length ~/ 1024;
      if (currentSizeKB <= targetSizeKB) break;

      // 更平缓的质量衰减曲线
      final oversizeRatio = currentSizeKB / targetSizeKB;
      quality -= (oversizeRatio > 2) ? 10 : (oversizeRatio > 1.5 ? 5 : 3);
    }
    return result;
  }

  /// 锐化处理（使用image库正确方法）
  static Uint8List _applySharpen(Uint8List bytes, {double amount = 1.0}) {
    final image = img.decodeImage(bytes);
    if (image == null) return bytes;

    // 使用卷积核实现锐化
    img.convolution(image, filter: [0, -1, 0, -1, 5, -1, 0, -1, 0], div: 1, offset: 0);
    return Uint8List.fromList(img.encodeJpg(image));
  }

  /// 保存临时文件
  static Future<File> _saveToTemp(Uint8List bytes) async {
    final dir = await getTemporaryDirectory();
    return File('${dir.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg')
      ..writeAsBytes(bytes);
  }
}