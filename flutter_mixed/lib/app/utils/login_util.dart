import 'package:factory_push/factory_push.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/constant/im_share.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/statis/statistics_helper.dart';
import 'package:flutter_mixed/app/utils/cache_helper.dart';
import 'package:flutter_mixed/app/utils/storage.dart';

import '../common/api/Define.dart';

class LoginUtil {
  
  // 当前是否登录
  static Future<bool> isLogin() async {
    var userInfo = await UserDefault.getData(Define.TOKENKEY);
    return userInfo != null;
  }

  // 调用重新登录流程 ：[清除本地 token]，[调用 native 清除登录状态]， [跳转到登录页]
  static reLogin({bool invokeNative = false}) async {
    // 清除mmkv中的图片数据
    await CacheHelper.clearAllImageMMkvCaches();

    // 清除本地token
    await UserDefault.removeData(Define.TOKENKEY);
    UserDefault.removeData(Define.ORGLIST);
    UserDefault.removeData(Define.FRIENDVERSION);
    UserDefault.removeData(Define.FRIENDLIST);
    UserDefault.removeData(Define.APPROVE_SEND_CACHEKEY);
    UserDefault.removeData(Define.APPROVE_TOTAL_LUNREADKEY);
    // UserDefault.removeData(Define.HAS_READ_PRIVACY_POLICY);

    ImShareValues.clearImToken();
    ImClientManager.instance.disConnect();
    if(invokeNative){
      Channel().invoke(Channel_logOut, {});
      FactoryPush().endFactoryPush();
    }

    StatisticsHelper.signOff();
  }


  static Future tokenEmpty() async {
    dynamic userInfo = await UserDefault.getData(Define.TOKENKEY);
    return userInfo == null;
  }


}
