import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/extension/http_extension.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/logger/logger_helper.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../main.dart';
import '../common/api/LoginApi.dart';
import '../im/constant/im_share.dart';
import '../../logger/logger.dart';

class Method {
  static const String get = "GET";
  static const String post = "POST";
  static const String put = "PUT";
  static const String head = "HEAD";
  static const String delete = "DELETE";
  static const String patch = "PATCH";
}

class DioUtil {
  static String udidStr = '';
  static final DioUtil _instance = DioUtil._init();
  static late Dio _dio;
  static BaseOptions _options = getDefOptions();

  factory DioUtil() {
    return _instance;
  }

  getDio() => _dio;

  DioUtil._init() {
    _dio = Dio();

    // 统一添加拦截器
    _dio.addInterceptors(isAddHeader: true);
  }

  static BaseOptions getDefOptions() {
    BaseOptions options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 20);
    options.receiveTimeout = const Duration(seconds: 20);
    options.baseUrl = '';

    Map<String, dynamic> headers = <String, dynamic>{};
    //设置请求的header
    //headers['Authorization'] = token;

    // if(Platform.isAndroid){
    //   headers['appVersion'] = Host.APPVERSION;
    //   headers['platform'] = Platform.isIOS ? 'ios' : 'android';
    //   headers['clientCode'] = Host.CLIENTCODE;
    // }

    options.headers = headers;
    return options;
  }

  setOptions(BaseOptions options) {
    _options = options;
    _dio.options = _options;
  }

  Future<Map<String, dynamic>?> get(
      String path, params, bool isNeedToken, Function errorCallback,
      {bool isShowLoading = true,
      bool isShowErrorToast = true,
      CancelToken? cancelToken,
      bool isNeedRetry = false}) {
    return request(path, Method.get, isNeedToken, params, errorCallback,
        isShowLoading: isShowLoading,
        isShowErrorToast: isShowErrorToast,
        cancelToken: cancelToken,
        isNeedRetry: isNeedRetry);
  }

  Future<Map<String, dynamic>?> post(
      String path, params, isNeedToken, Function errorCallback,
      {bool isShowLoading = true,
      bool isShowErrorToast = true,
      CancelToken? cancelToken,
      bool isNeedRetry = false}) {
    return request(path, Method.post, isNeedToken, params, errorCallback,
        isShowLoading: isShowLoading,
        isShowErrorToast: isShowErrorToast,
        cancelToken: cancelToken,
        isNeedRetry: isNeedRetry);
  }

  Future<Map<String, dynamic>?> put(
      String path, params, isNeedToken, Function errorCallback,
      {bool isShowLoading = true,
      bool isShowErrorToast = true,
      CancelToken? cancelToken,
      bool isNeedRetry = false}) {
    return request(path, Method.put, isNeedToken, params, errorCallback,
        isShowLoading: isShowLoading,
        isShowErrorToast: isShowErrorToast,
        cancelToken: cancelToken,
        isNeedRetry: isNeedRetry);
  }

  Future<Map<String, dynamic>?> delete(
      String path, params, isNeedToken, Function errorCallback,
      {bool isShowLoading = true,
      bool isShowErrorToast = true,
      CancelToken? cancelToken,
      bool isNeedRetry = false}) {
    return request(path, Method.delete, isNeedToken, params, errorCallback,
        isShowLoading: isShowLoading,
        isShowErrorToast: isShowErrorToast,
        cancelToken: cancelToken,
        isNeedRetry: isNeedRetry);
  }

  Future<Map<String, dynamic>?> patch(
      String path, params, isNeedToken, Function errorCallback,
      {bool isShowLoading = true,
      bool isShowErrorToast = true,
      bool isNeedRetry = false}) {
    return request(path, Method.patch, isNeedToken, params, errorCallback,
        isShowLoading: isShowLoading,
        isShowErrorToast: isShowErrorToast,
        isNeedRetry: isNeedRetry);
  }

  Future<Map<String, dynamic>?> request(String path, String method,
      bool isNeedToken, parmas, Function errorCallback,
      {bool isShowLoading = true,
      bool isShowErrorToast = true,
      CancelToken? cancelToken,
      bool isNeedRetry = false}) async {
    setOptions(_options);
    if (isShowLoading) {
      Get.loading();
    }

    // addHeader(_dio , path, isNeedRetry, isNeedToken);

    late Response response;
    if (!path.startsWith('http')) {
      if (path.startsWith('oauth/')) {
        path = Host.TOKENHOST + path;
      } else {
        path = Host.HOST + path;
      }
    }

    if (enableLog) {
      logger(
          'path --- $path;parms ---- $parmas;header--${_dio.options.headers}');
    }

    if (parmas == null) {
      try {
        response = await _dio.request(path,
            options: Options(method: method), cancelToken: cancelToken);

        if (enableLog && Platform.isIOS) {
          logger('path--$path---response  $response');
        }
        //if (isShowLoading) {
          await Get.dismiss();
        //}
        Map<String, dynamic> jsonData = json.decode(response.toString());
        if (enableLog) {
          logger(jsonData);
        }
        return jsonData;
      } on DioError catch (error) {
        await Get.dismiss();
        // logger('path--$path--error -- ${error.response}');
        if (isShowErrorToast && error.type != DioExceptionType.cancel) {
          toast(LoginApi.ERROR_MSG);
        }
        errorCallback();
      }
    } else {
      try {
        response = await _dio.request(path,
            data: parmas,
            options: Options(method: method),
            cancelToken: cancelToken);
        if (enableLog && Platform.isIOS) {
          logger('path--$path---response  $response');
        }
        await Get.dismiss();
        if (response != null) {
          Map<String, dynamic> jsonData = json.decode(response.toString());
          if (enableLog) {
            logger(jsonData);
          }
          return jsonData;
        } else {
          return null;
        }
      } on DioError catch (error) {
        await Get.dismiss();
        String errorMsg = LoginApi.ERROR_MSG;
        logger(
            'DioError-----path--$path--param--$parmas----${error.message}-----${error.response}----${error.type}---$error');

        try {
          dynamic jsonData = json.decode(error.response.toString());
          if (path.contains('oauth/openid/token')) {
            errorCallback(jsonData);
          } else {
            if (jsonData != null && jsonData['msg'] != null) {
              errorMsg = jsonData['msg'];
            }
            errorCallback();
            if (isShowErrorToast && error.type != DioExceptionType.cancel) {
              toast(errorMsg);
            }
          }
        } catch (e) {
          if (path.contains('oauth/openid/token')) {
            errorCallback(errorCallback(error.response.toString()));
          } else {
            errorCallback();
          }
        }
      }
    }
  }

  //下载文件
  void downLoadFile(String url, String savePath, String loadPath,
      Function errorCallback, Function finishCallback,
      {bool isShowLoading = true}) async {
    Get.loading(dismissible: true, loadingText: '');
    try {
      Dio().downloadUri(
        Uri.parse(url),
        loadPath,
        onReceiveProgress: (count, total) async {
          if (count == total) {
            Get.dismiss();
            logger('------下载完成---');
            await File(loadPath).rename(savePath);
            finishCallback(savePath);
          }
        },
      );
    } on DioError catch (e) {
      Get.dismiss();
      errorCallback();
    }
  }
}

addHeader(RequestOptions options, bool isNeedRetry, bool isNeedToken) async {
  
  Map<String, dynamic> headers = options.headers;

  Map<String, dynamic> headerMap = await UserDefault.getHttpHeader();
  headers = headerMap;
  options.headers.addAll(headers);
  options.extra.addAll({'ro_disable_retry': !isNeedRetry});
  if (isNeedToken == false) {
    if (headers['Authorization'] != null) {
      options.headers.remove('Authorization');
    }
  } else {
    var smartPath = partPath(options.path);
    logger('smartPath ====== $smartPath');
    var imToken = await ImShareValues.getImtoken();
    headers['ImToken'] = imToken;
    if (smartPath.startsWith('oauth') &&
        !smartPath.startsWith('oauth/qrcode/v1')) {
      headers['Authorization'] = 'Basic YXBwOmFwcA==';
      options.headers = headers;
    } else {
      dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
      String token = '';
      if (tokenInfo != null) {
        token = tokenInfo['access_token'];
      }
      logger('http ==> token: $token , imToken: ${imToken}');
      headers['Authorization'] = 'Bearer $token';
      options.headers = headers;
    }
  }

   logger('打印header： ${options.path} , ${options.headers}');
   logger('打印data： ${options.data} ');
}

String partPath(String path) {
  if (path.startsWith('http')) {
    var scheme = path.split('//')[1];
    return scheme.replaceAll('${scheme.split('/')[0]}/', '');
  } else {
    return path;
  }
}
