import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../modules/workStand/common/focus_switch_widget.dart';


class DialogHud {

  static TransitionBuilder init() => FlutterSmartDialog.init(

    builder: (ctx , child) {
      final MediaQueryData data = MediaQuery.of(ctx);
      return MediaQuery(data: data.copyWith(textScaleFactor:1.35.maxScale,), child: GestureDetector(
        onTap: () => {hideKeyboard(ctx)},
        child: child,
      ));
    } ,

    loadingBuilder: (msg) => CommonLoadingWidget(msg: msg),

    toastBuilder: (msg) => CustomToastWidget(msg: msg)



  );


  static showLoading({String msg ='loading...',bool clickMaskDismiss = false}) => SmartDialog.showLoading(backDismiss: false , clickMaskDismiss: clickMaskDismiss,msg: msg);


  static showToast({String msg =''}) => SmartDialog.showToast(msg, alignment: Alignment.center);


  static dismiss({String tag = ''}) => SmartDialog.dismiss(tag:  tag);
}


class CustomToastWidget extends StatelessWidget {
  const CustomToastWidget({Key? key, required this.msg}) : super(key: key);

  final String msg;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 24),
      decoration: BoxDecoration(
        color: Color(0xA6000000),
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Text(msg, style: TextStyle(color: Colors.white, fontSize: 14)),
    );
  }
}



class CommonLoadingWidget extends StatelessWidget {
  const CommonLoadingWidget({Key? key, required this.msg}) : super(key: key);
  ///loading msg
  final String msg;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        //loading animation
        CircularProgressIndicator(
          strokeWidth: 3,
          valueColor: AlwaysStoppedAnimation(Color(0xE6A96A2F)),
        ),

        if(!StringUtil.isEmpty(msg))...[
          Container(
            margin: EdgeInsets.only(top: 20),
            child: Text(msg, style: TextStyle(color: Colors.black)),
          ),
        ]

      ]),
    );
  }
}