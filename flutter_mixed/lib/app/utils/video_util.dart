

import 'dart:typed_data';

import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_video_info/flutter_video_info.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_compress/video_compress.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'dart:io';
import 'dart:ui' as ui;


class VideoUtil {

  // 获取视频第一帧
  static Future<File?> getVideoFirstFrame(String videoPath) async {
    try {
      var uint8list = await VideoThumbnail.thumbnailData(video: videoPath , imageFormat: ImageFormat.PNG , quality: 50,
        maxWidth: 800
      );
      if(uint8list == null) return null;
      Directory applicationDir = await getTemporaryDirectory();
      bool isDirExist = await Directory(applicationDir.path).exists();
      if (!isDirExist) Directory(applicationDir.path).create();
      File coverImageFile = await File(
          applicationDir.path + "/${DateTime.now().toIso8601String()}.png")
          .writeAsBytes(uint8list);
      return coverImageFile;
    }catch(e){
      logger('getVideoFirstFrame $e');
      return null;
    }

  }

  // 获取视频信息
  static Future<VideoInfo> getVideoInfo(String videoPath) async {
    final videoInfo = FlutterVideoInfo();
    var info = await videoInfo.getVideoInfo(videoPath);
    return VideoInfo(info?.filesize ?? 0 , info?.width ?? 100 , info?.height ?? 100 ,
        Duration(seconds: (info?.duration ?? 0).toInt()));
  }

  static Future<Map<String, int>> getImageDimensions(File imageFile) async {
    final Uint8List bytes = await imageFile.readAsBytes();
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image image = frameInfo.image;
    return {
      'width': image.width,
      'height': image.height,
    };
  }

  static Future compressVideo(String videoPath) async {
    try{
      MediaInfo? mediaInfo = await VideoCompress.compressVideo(
        videoPath,
        quality: VideoQuality.MediumQuality,
        deleteOrigin: false, // It's false by default
      );
      if(mediaInfo == null) return videoPath;
      return mediaInfo.path;
    }catch(e){
      logger('compressVideo error: $e');
      return videoPath;
    }
  }

}

class VideoInfo {
  String? fileId;
  String? coverFileId;
  int size = 0;
  int width = 0;
  int height = 0;
  Duration duration;

  int? coverWidth;
  int? coverHeight;

  String? cover;

  VideoInfo(this.size, this.width, this.height, this.duration , {this.fileId});

  @override
  String toString() {
    return 'VideoInfo{fileId: $fileId, coverFileId: $coverFileId, size: $size, width: $width, height: $height, duration: $duration, coverWidth: $coverWidth, coverHeight: $coverHeight}';
  }
}