import 'package:connectivity_plus/connectivity_plus.dart';

/// 网络工具
class NetUtil {


  static Future<String> connectedType() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    var netType = "NONE";
    if (connectivityResult == ConnectivityResult.mobile) {
      netType = "MOBILE";
    } else if (connectivityResult == ConnectivityResult.wifi) {
      netType = "WIFI";
    } else {
      netType = "NONE";
    }
    return netType;
  }

  static Future<bool> connected() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }
    static String netStr = 'Unknown';

}


