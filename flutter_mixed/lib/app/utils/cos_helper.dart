import 'dart:io';

import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

import '../common/channel/channel.dart';

/// 腾讯云 辅助类
class CosHelper {
  static Future uploadFileList(int? type, List? fileList) async {
    var uploadResult =
        await Channel().invokeMap(Channel_Native_UploadFileList, {
      'type': type,
      'fileList': fileList,
    });

    if (uploadResult == null) return [];
    return uploadResult;
  }

  static move2Img(File source) async {
    Directory tempDir = await getTemporaryDirectory();
    int timeFileName = DateTime.now().millisecondsSinceEpoch;
    String toFilePath = "${tempDir.path}/$timeFileName.jpg";
    File tempFile = await moveFile(source, toFilePath);
    return tempFile;
  }

  static void openFile(String path) async {
    OpenFilex.open(path);
    // if (Platform.isAndroid) {
    //   OpenFilex.open(path);
    // }
    // if (Platform.isIOS) {
    //   Channel().invoke(Channel_openWebView, {
    //     'url': path,
    //     'title': '文件预览',
    //     'isWebNavigation': 0,
    //   });
    // }
  }
}

Future<File> moveFile(File sourceFile, String newPath) async {
  final newFile = await sourceFile.copy(newPath);
  return newFile;
}
