
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:url_launcher/url_launcher.dart';

class SystemUtil {

  static void copy2Clipboard(String? content){
    if(content == null || content.isEmpty) return;
    Clipboard.setData(ClipboardData(text:content));
  }

  static tel(String mobile) {
    if(mobile.isEmpty) return;
    launch('tel:$mobile');
  }

  static sendSms(String? mobile , String? content) async {
    if(StringUtil.isEmpty(mobile)) return;
    if (Platform.isAndroid) {
      var uri = 'sms:$mobile?body=$content';
      await launch(uri);
    }
  }
}