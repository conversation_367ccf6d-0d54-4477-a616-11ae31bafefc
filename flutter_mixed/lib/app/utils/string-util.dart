
import 'package:uuid/uuid.dart';

class StringUtil {
  // 邮箱判断
  static bool isEmail(String input) {
    String regexEmail =
        "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}\$";
    if (input == null || input.isEmpty) return false;
    return RegExp(regexEmail).hasMatch(input);
  }

  static final String TEL_REGEX = '^1[3-9][0-9]\\d{8}\$';
  // 纯数字
  static final String DIGIT_REGEX = "^[0-9]{1,}\$";

  // 纯字母
  static final String LETTER_REGEX = "[a-zA-Z]+";

  // 大写和小写同时存在
  static final String LETTER_A = "(?=.*[a-z])(?=.*[A-Z])";

  // 仅仅包含字母和数字
  static final String LETTER_DIGIT_REGEX = "^[a-z0-9A-Z]{6,16}\$";

    // 仅仅包含字母、数字和中文
  static final String NAME_DIGIT_REGEX = "^[a-z0-9A-Z\u4e00-\u9fa5]{1,30}\$";

  static bool numberIsOk(String input, String typeStr) {
    if (input == null || input.isEmpty) return false;
    return RegExp(typeStr).hasMatch(input);
  }

  static bool chaIsOk(String input) {
    return numberIsOk(input, LETTER_A);
  }

  static bool isPwd(String? input) {
    if(input == null) return false;
    var onlyDig = input.contains(RegExp(r'[0-9]'));
    var onlyLetter = input.contains(RegExp(".*[a-zA-Z]+.*"));
    return onlyDig && onlyLetter;
  }

  static bool isEmpty(String? value) {
    if(value == null || value == '' || value == "null") return true;
    return false;
  }

  //密码级别
  static int backLevelWithPwd(String pwdStr) {
    int totalGrade = 0;
    int lengGrade = 0;
    int chaGrade = 0;
    int digGrade = 0;
    int rewardGrade = 0;
    int level = 1;
    if (pwdStr.length >= 8) {
      lengGrade = 30;
    }
    if (pwdStr.length <= 4) {
      lengGrade = 5;
    }
    if (pwdStr.length >= 5 && pwdStr.length <= 7) {
      lengGrade = 10;
    }
    if (!numberIsOk(pwdStr, DIGIT_REGEX)) {
      if (numberIsOk(pwdStr, LETTER_A)) {
        chaGrade = 30;
      } else {
        chaGrade = 10;
      }
    }

    int digit = 0;
    for (int i = 0; i < pwdStr.length; i++) {
      String c = pwdStr[i];
      if (numberIsOk(c, DIGIT_REGEX)) {
        digit++;
      }
    }

    if (digit >= 1 && digit < 3) {
      digGrade = 10;
    }
    if (digit >= 3) {
      digGrade = 25;
    }

    if (numberIsOk(pwdStr, LETTER_DIGIT_REGEX)) {
      if (numberIsOk(pwdStr, LETTER_A)) {
        rewardGrade = 5;
      } else {
        rewardGrade = 2;
      }
    }
    totalGrade = lengGrade + chaGrade + digGrade + rewardGrade;
    if (totalGrade >= 25 && totalGrade < 50) {
      level = 2;
    }
    if (totalGrade >= 50 && totalGrade < 60) {
      level = 3;
    }
    if (totalGrade >= 60 && totalGrade < 70) {
      level = 4;
    }
    if (totalGrade >= 70 && totalGrade < 80) {
      level = 5;
    }
    if (totalGrade >= 80 && totalGrade < 90) {
      level = 6;
    }
    if (totalGrade >= 90) {
      level = 7;
    }

    return level;
  }

  // 某字符是否为全角符号
  static bool isFullWidth(String character) {
    if (character.length > 1) {
      return true;
    }
    var regex = RegExp(r'[\u4e00-\u9fff\uFF00-\uFFEF]');
    return regex.hasMatch(character);
  }


  static String getUUID() {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    return uuid;
  }
}
