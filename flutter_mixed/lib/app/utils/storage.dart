import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../common/api/Define.dart';
import '../common/api/LoginApi.dart';
import '../common/channel/channel.dart';
import '../common/config/config.dart';
// 使用方法
// 获取 var userInfo = await UserDefault.getData("userInfo");
// 设置 UserDefault.setData("userInfo", {"name":"xxx"});
// 删除 UserDefault.removeData("userInfo");
class UserDefault{
  static Future<bool> setData(String key,dynamic value) async{
    var prefs=await SharedPreferences.getInstance();
    return await prefs.setString(key, json.encode(value));
  }
  static Future getData(String key) async{
    var prefs = await SharedPreferences.getInstance();
    if(prefs == null){
      return null;
    }

    String? tempData=prefs.getString(key);
    if(tempData == null){
      return null;
    }else{
      return json.decode(tempData);
    }
  }
  static removeData(String key) async{
    var prefs=await SharedPreferences.getInstance();
    prefs.remove(key);
  }

  static Future<String?> getSettingConfig(String key) async {
    var prefs = await SharedPreferences.getInstance();
    if(prefs == null){
      return null;
    }
    return prefs.getString(key);
  }

  static saveSettingConfig(String key , String value) async {
    var prefs = await SharedPreferences.getInstance();
    if(prefs == null){
      return;
    }
    await prefs.setString(key, value);
  }



  static getHttpHeader() async {
    if(kDebugMode) {
      var deviceId = await DeviceUtils.getUDIDStr();
      var appVersion = Host.APPVERSION;
      var clientCode = Host.CLIENTCODE;
      try{
        var nativeAppVersion = await Channel().invoke(Channel_version_name);
        var nativeClientCode = await Channel().invoke(Channel_version_code);
        if(nativeAppVersion != null && nativeClientCode != null){
          appVersion = nativeAppVersion;
          clientCode = nativeClientCode.toString();
        }
      }catch(e){}

      var version = await DeviceUtils.getSystemVersion();
      return {
        'device': deviceId,
        'appVersion':appVersion,
        'platform':Platform.isIOS ? 'ios' : 'android',
        'clientCode':clientCode,
        'version': version,
      };
    };
    if(Platform.isIOS) {
      var temp =  await UserDefault.getData(Define.HTTPHEADER);
      return temp;
    }

    if(headerTmp != null && headerTmp.isNotEmpty) return headerTmp;
    var temp =  await UserDefault.getData(Define.HTTPHEADER);
    headerTmp = temp;
    return headerTmp;
  }

  static Map<String,dynamic> headerTmp = Map();

  static storeHeader(dynamic header) {
    if(header == null) return;
    if(Platform.isAndroid){
      var a = header.cast<String,dynamic>();
      headerTmp = a;
    }

  }
}