
import 'dart:collection';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/painting.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:mmkv/mmkv.dart';

import '../im/ui/chat/common/picture_manager.dart';
import '../im/utils/file_util.dart';

class CacheHelper{

  static final mmkvFlutterArea = "dd_flutter_mmkv";

  static initMemoryConfig() async {
    PaintingBinding.instance.imageCache
        ..maximumSize = 100
        ..maximumSizeBytes = 350 << 20;
  }

  static clear() async{
    clearMemoryImageCache();
    await FileThumbHelper.clearImageThumbs();
    await clearDiskCachedImages();
    FileUtil.clearDirectoryFile();
    clearAllImageMMkvCaches();
  }

  // 清除图片内存缓存
  static clearImageLoadMemory() async {
    clearMemoryImageCache();
  }

  static saveImageByKey(String fileId ,String url) async {
    var userId = await UserHelper.getUid();
    if(userId == null || userId == '') return;
    var mmkv = MMKV.defaultMMKV(cryptKey: mmkvFlutterArea);
    mmkv.encodeString('${userId}_${fileId}', url);
  }

  static getImageByKey(String fileId) async {
    var userId = await UserHelper.getUid();
    if(userId == null || userId == '') return '';
    var mmkv = MMKV.defaultMMKV(cryptKey: mmkvFlutterArea);
    return mmkv.decodeString('${userId}_${fileId}');
  }

  static Future<bool> existImageCache(String fileId) async {
    var userId = await UserHelper.getUid();
    if(userId == null || userId == '') return false;
    var mmkv = MMKV.defaultMMKV(cryptKey: mmkvFlutterArea);
    return mmkv.containsKey('${userId}_${fileId}');
  }

  static clearAllImageMMkvCaches() async {
    var mmkv = MMKV.defaultMMKV(cryptKey: mmkvFlutterArea);
    List<String> keys = mmkv.allKeys;
    var userId = await UserHelper.getUid();
    var imageKeys = keys.where((key) => key.startsWith('${userId}_')).toList();
    mmkv.removeValues(imageKeys);
  }





}

final HashMap<String,dynamic> objectStore = HashMap();
