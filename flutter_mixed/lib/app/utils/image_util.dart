import 'dart:typed_data';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:get/get_core/src/get_main.dart';

import '../im/db/db_helper.dart';
import '../../logger/logger.dart';
import 'luban/luban_compressor.dart';


class ImageUtil {
  // 获取图片尺寸
  static Future<Map<String, int>> getImageDimensions(File imageFile) async {
    final Uint8List bytes = await imageFile.readAsBytes();
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image image = frameInfo.image;
    return {
      'width': image.width,
      'height': image.height,
    };
  }

  // 获取图片信息
  static Future<ImageInfo> getImageInfo(String imagePath, {String? fileId}) async {
    File imageFile = File(imagePath);
    if (!await imageFile.exists()) {
      throw Exception('图片文件不存在: $imagePath');
    }

    // 获取图片尺寸
    Map<String, int> dimensions = await getImageDimensions(imageFile);
    int width = dimensions['width']!;
    int height = dimensions['height']!;

    // 返回图片信息
    return ImageInfo( width, height, fileId)..path = imagePath;
  }

  // 压缩图片
  static Future<File?> compressImg(String filePath) async {
    // todo
    // var f = await nativeCompressFile(filePath);
    var f = await LubanCompressor.compressImage(File(filePath));
    return f;
  }

  static Future removeDupImageTempImg(String cmdId, String tempMsgId) async {
    var msgList = await DbHelper.queryMsgListByCmdId(cmdId);
    logger('收到单群聊响应的时候 已插入的有几条${msgList.length}');

    if(msgList.length > 1){
      var succeed = msgList.where((m) => m.isSuccess == 1).toList().length;
      if(succeed > 0){
        var error = msgList.where((m) => m.isSuccess != 1).toList().firstOrNull;
        if(error != null){
          await DbHelper.deleteMsgReal(error);
        }
      }
    }
    // var old = msgList.where((e) => e.msgId == tempMsgId).toList().firstOrNull;
    // if(old != null){
    //   await DbHelper.deleteMsgReal(old);
    // }
  }
}

class ImageInfo {
  String? fileId;
  int width; // 图片宽度
  int height; // 图片高度
  String? path;

  ImageInfo(this.width, this.height, this.fileId);

  @override
  String toString() {
    return 'ImageInfo{fileId: $fileId, width: $width, height: $height}';
  }
}