import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/utils/loading/custom_loading.dart';

class LoadingUtils {
  static OverlayEntry? _overlayEntry;

  static showLoading(BuildContext context,{String loadingText = "加载中..."}){
    if(_overlayEntry != null) return;
    _overlayEntry = OverlayEntry(
        builder: (context) => CustomLoading(loadingText: loadingText)
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  static hideLoading(){
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}