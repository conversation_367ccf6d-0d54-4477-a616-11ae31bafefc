import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomLoading extends StatelessWidget{
  const CustomLoading({super.key, required this.loadingText});

  final String loadingText;
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CupertinoActivityIndicator(),
          SizedBox(height: 16,),
          Text(loadingText,style: TextStyle(color: Colors.grey),)
        ],
      ),
    );
  }



}