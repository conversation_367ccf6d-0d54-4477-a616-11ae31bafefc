import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/im/utils/json_parse.dart';
import 'package:flutter_mixed/app/statis/statistics_helper.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../common/user/user_helper.dart';
import '../../../statis/statics_model.dart';
import '../../http.dart';

/// 埋点 ：  监控接口的
class StatisticInterceptor extends Interceptor {
  StatisticInterceptor();

  LogErrorRequest log = LogErrorRequest();

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    _logResponse(response);
    super.onResponse(response, handler);
    _resetErrorLog();
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    _logError(err);
    super.onError(err, handler);
    _resetErrorLog();
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // _logRequest(options);
    super.onRequest(options, handler);
  }

  _logError(DioException err) async {
    try {
      var errorCode = err.response?.statusCode ?? 200;
      if (errorCode != 200) {
        var errorResponse = err.response;
        var errorMsg = '';
        if (errorResponse != null && errorResponse.data != null) {
          errorMsg = errorResponse.data['error'];
        }
        var respLog = StaticsLog(err.response?.realUri.path ?? '')
          ..code = errorCode
          ..response = err.response?.data ??''
          ..request = _parseRequestParams(err.requestOptions)
          ..msg = errorMsg;

        log.response = respLog;

        _uploadErrorRequest();
      }
    }catch(e){}
  }

  _logResponse(Response response) async {
    try {
      var result = response.data;
      if (result != null) {
        var code = result['code'];
        // 只在请求异常的时候上报
        if (code != 1) {
          var msg = result['msg'] ?? '';
          var responseLog = StaticsLog(response.realUri.path)
            ..code = code
            ..request = _parseRequestParams(response.requestOptions)
            ..response = jsonEncode(response.data)
            ..method = response.requestOptions.method
            ..msg = msg;

          log.response = responseLog;

          await _uploadErrorRequest();
        }
      }
    }catch(e){
      print('_logResponse >>>>>>>>>>>>>> $e');
    }
  }

  _logRequest(RequestOptions options) async {
    try{
      var reqLog = StaticsLog(options.path)
        ..method = options.method
        ..request = _parseRequestParams(options);
      log.request = reqLog;
    }catch(e){
      print('_logRequest >>>>>>>>>>>>>> $e');
    }
  }

  /// 无response体的时候为无需上传，重置
  Future _uploadErrorRequest() async {

    if(log.request != null){
      var reqMap = log.request!.toMap();
      var req = await StatisticsHelper.onEventMap(StatisticsEventConstant.API, reqMap);
    }

    if(log.response != null){
      var respMap = log.response!.toMap();
      await StatisticsHelper.onEventMap(StatisticsEventConstant.API, respMap);
    }
  }

  _resetErrorLog() {
    // log..request = null
    //   ..response = null;
  }


  String _parseRequestParams(RequestOptions options) {
    try {
      dynamic params;

      // GET 请求参数可能存在于 queryParameters 或 data
      if (options.method.toUpperCase() == 'GET') {
        params = options.queryParameters.isNotEmpty
            ? options.queryParameters
            : (options.data is Map ? options.data : null);
      }
      // 非 GET 请求（POST/PUT/DELETE等）
      else {
        if (options.data is Map) {
          params = options.data;
        } else if (options.data is FormData) {
          // 解析 FormData 的字段和文件
          final formData = options.data as FormData;
          params = {
            'fields': Map.fromEntries(formData.fields),
            'files': formData.files.map((f) => f.key).toList(),
          };
        } else if (options.data != null) {
          // 其他类型（如 String、List 等）
          params = options.data.toString();
        }
      }

      // 转换为可读字符串
      return params?.toString() ?? 'No Params';
    } catch (e) {
      return 'Parse Params Error: $e';
    }
  }


  _parseParam1(RequestOptions options) {
    dynamic parameters;
    if(options.method == Method.get){
      parameters = options.queryParameters;
      logger('log===> $options');
    }else if(options.method == Method.post){
      if(options.data is Map){
        parameters = options.data ?? {};
      }else if(options.data is FormData){
        parameters = options.data as FormData;
      }
    }
    // logger('打点： 参数$parameters');
    return parameters.toString();
  }


}

class LogErrorRequest {
  StaticsLog? request;
  StaticsLog? response;
}