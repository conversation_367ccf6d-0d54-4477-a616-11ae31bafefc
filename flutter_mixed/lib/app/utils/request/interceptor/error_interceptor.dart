import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/utils/file_util.dart';
import 'package:flutter_mixed/app/retrofit/auth_error_handler/auth_handler.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/logger/logger.dart';

class ErrorInterceptor extends Interceptor {
  ErrorInterceptor();

  var errHandler = AuthErrorHandler();

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    try {
      if (response.statusCode != 200) {
        logger('响应异常： ${response.requestOptions.path} : ${response.statusCode}');
      }

      var result = response.data;
      if (result != null) {
        var code = result['code'];

        if (code == -3 || code == -2) {
          logger('认证失败======${response.requestOptions.path}');
          try{
            var token = await UserHelper.getAccessToken();
            var log = '${token}: ${response.requestOptions.path} -- ${response.requestOptions.headers['Authorization']}\n';
            FileUtil.writeStringToFile('ddd_error_expired.txt', log);
            if (StringUtil.isEmpty(token)) {
              return;
            }
          }catch(e){}

          var msg = result['msg'];
          errHandler.handleAuthRequired(code, msg);

          handler.reject(
            DioError(
              requestOptions: response.requestOptions,
              response: response,
              type: DioErrorType.badResponse,
              error: '',
            ),
          );
          return;
        }
      }
    } catch (e) {
      logger('catch ==> $e==${response.requestOptions.path}');
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    logger('onError ==> ${err.message}');
    super.onError(err, handler);
  }

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    super.onRequest(options, handler);
  }
}
