import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../common/api/Define.dart';
import '../../storage.dart';


final refreshTokenDio = Dio();

class RefreshTokenInterceptor extends QueuedInterceptorsWrapper {

  final int _ExpiredCode = 401;

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    super.onResponse(response, handler);
  }

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if (tokenInfo != null) {
      String refreshToken = tokenInfo['refresh_token'];

      Map<String, dynamic> requestHeader = {};
      requestHeader.addAll(options.headers);
      requestHeader['Authorization'] = 'Basic YXBwOmFwcA==';
      refreshTokenDio.options.headers = requestHeader;
      int nowTime = DateTime.now().millisecondsSinceEpoch;//当前时间
      int loginTime = 0;//登录时间
      if (tokenInfo['loginTime'] != null) {
        loginTime = tokenInfo['loginTime'];
      }
      int expire = tokenInfo['expires_in'];//系统返回的有效期 秒
      double customExpire = 0.7*expire*1000;//自定义过期时间 0.7*expire*1000
      if (EnvConfig.mEnv == Env.Product) {
        customExpire = 0.7*expire*1000;
      }
      if (nowTime - loginTime > customExpire) {
        try {
          logger('RefreshTokenInterceptor===${options.path}===${options.headers}');
          Response httpResponse = await refreshTokenDio.request(
              '${Host.TOKENHOST}oauth/token?grant_type=refresh_token&refresh_token=$refreshToken',
              options: Options(method: 'POST'));
          logger('获取RefreshToken接口： ${httpResponse.toString()}');
          Map<String, dynamic> jsonData = json.decode(httpResponse.toString());
          if (jsonData['access_token'] != null) {
            logger('===refresh===');
            tokenInfo.addAll(jsonData);
            tokenInfo['loginTime'] = nowTime;
            await UserDefault.setData(Define.TOKENKEY, tokenInfo);
            Channel().invoke(Channel_userdefult_tokenKey, tokenInfo);
            // return handler.next(options);

          }
        } catch (e) {}
      }
      dynamic auth = options.headers['Authorization'];
        if (auth != null) {
          if (auth != 'Basic YXBwOmFwcA==') {
            options.headers['Authorization'] =
                'Bearer ${tokenInfo['access_token']}';
          }
        }
    }
    logger('RefreshTokenInterceptorEnd===${options.path}===${options.headers}');
    handler.next(options);
    // super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
       if(err.response?.statusCode != _ExpiredCode) {
          return super.onError(err, handler);
       }
       await _refreshToken(refreshTokenDio , DateTime.now().millisecondsSinceEpoch);
       return handler.next(err);
  }


  // 请求刷新token
  _refreshToken(Dio dio , int nowTime) async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if(tokenInfo == null) return;
    String refreshToken = tokenInfo['refresh_token'];
    Response httpResponse = await dio.request(
        '${Host.TOKENHOST}oauth/token?grant_type=refresh_token&refresh_token=$refreshToken',
        options: Options(method: 'POST'));
    logger('获取RefreshToken接口： ${httpResponse.toString()}');
    Map<String, dynamic> jsonData = json.decode(httpResponse.toString());
    if (jsonData['access_token'] != null) {
      tokenInfo.addAll(jsonData);
      tokenInfo['loginTime'] = nowTime;
      await UserDefault.setData(Define.TOKENKEY, tokenInfo);
      await Channel().invoke(Channel_userdefult_tokenKey, tokenInfo);
    }
  }

}
