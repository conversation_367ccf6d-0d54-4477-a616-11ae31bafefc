

import 'package:flutter/material.dart';
import 'package:flutter_mixed/logger/logger.dart';

// 表情面板
class EmojiPanel extends StatelessWidget {

  Function(String) onEmojiTap;

  List<String> emojiList = [];

  EmojiPanel(this.onEmojiTap , this.emojiList);

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      padding: const EdgeInsets.only(top: 10,left: 18 ,right: 18),
      color: const Color(0xF8F8F8F8),
      height: 200,
      child:GridView.builder(
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 8,
            crossAxisSpacing: 1,
            mainAxisSpacing: 1,
            childAspectRatio: 1.0,
          ),
          itemCount: emojiList.length,
          itemBuilder: (ctx, index) {
            var item = emojiList[index];
            return _buildEmojiItem(item);
          })

    );
  }

  _buildEmojiItem(String emoji) {
    return InkWell(
      onTap: (){
        onEmojiTap(emoji);
      },
      child: Text(emoji , style: TextStyle(fontSize: 24),),
    );
  }

}