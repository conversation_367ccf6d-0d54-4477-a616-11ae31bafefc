import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/im/model/im_badge.dart';
import 'package:flutter_mixed/app/im/request/entity/push_token_req.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

import '../../../retrofit/entity/base_resp.dart';
import '../../../retrofit/entity/work/group_response.dart';
import '../../constant/im_share.dart';
import '../entity/check_stranger_resp.dart';
import '../entity/im_socket_resp.dart';
import '../entity/im_token_resp.dart';
import '../entity/offline_group_chat_resp.dart';
import '../entity/offline_single_chat_resp.dart';
import '../entity/six_in_one_req.dart';
import '../entity/six_in_one_resp.dart';
part 'im_datasource.g.dart';


@RestApi()
abstract class ImDataSource {

  factory ImDataSource(Dio dio, {String baseUrl}) = _ImDataSource;


  @GET('relation/im/users/v1/mine')
  Future<BaseResp<ImTokenResp>> fetchImToken();


  @GET('router/node/v1')
  Future<BaseResp<ImSocketResp>> fetchSocketLink();

  // 单聊离线消息
  @GET('conversation/c2c/v1/{sessionId}/{page}/{pageSize}')
  Future<BaseResp<List<OfflineSingleChatResp>>> fetchOfflineSingleChatList(@Path("sessionId") String sessionId , @Path("page") int page
      , @Path("pageSize") int pageSize , @Query("order") int order , @Query("timeStart") int timeStart, @Query("timeEnd") int timeEnd);

  // 离线群聊消息
  @GET('conversation/group/v1/{groupId}/{page}/{pageSize}')
  Future<BaseResp<List<OfflineGroupChatResp>>> fetchOfflineGroupChatList(@Path("groupId") String groupId , @Path("page") int page
      , @Path("pageSize") int pageSize , @Query("order") int order , @Query("timeStart") int timeStart, @Query("timeEnd") int timeEnd);

  // 六合一接口
  @POST('user/user/start/v2')
  Future<BaseResp<SixInOneResp>> fetchSixInOne(@Body() SixInOneReqBody req,@CancelRequest() CancelToken cancelToken);

  // 上报离线推送token接口
  @POST('user/user/device/token/v1')
  Future<BaseResp<dynamic>> reportPushToken(@Body() PushTokenReqBody req);

  @GET('user/user/set/settings/stranger/{targetId}')
  Future<BaseResp<CheckStrangerResp>> checkStranger(@Path("targetId") String targetId );

  // 根据图片fileId ，获取 文件url
  @GET('file/url/v1/{fileId}')
  Future<BaseResp<String>> getFileUrl(@Path("fileId") String fileId );

  // 根据获取fileid
  @POST('file/id/v1')
  Future<BaseResp<String>> getFileId();

  //上报app badge
  @PUT(LoginApi.APP_BADGE_UPLOAD)
  Future<BaseResp<dynamic>> putReportBadge(@Body() AppBadge body,@CancelRequest() CancelToken cancelToken);

}