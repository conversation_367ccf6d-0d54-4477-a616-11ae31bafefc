import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';

import '../../../retrofit/entity/base_resp.dart';
import '../entity/apply_tip_off_req.dart';
import '../entity/group_brief_item_resp.dart';
import '../entity/group_info_resp.dart';
import '../entity/tip_off_resp.dart';

part 'relation_datasource.g.dart';

// http.HOST   dev http://*********:8768/
@RestApi()
abstract class RelationDatasource {

  factory RelationDatasource(Dio dio, {String baseUrl}) = _RelationDatasource;

  // 获取群组信息
  @GET('relation/groups/v1/{groupId}')
  Future<BaseResp<List<GroupItemResp>>> getGroupInfo(@Path("groupId") String groupId);


  // 解散群组
  @DELETE('relation/groups/v1/{groupId}')
  Future<BaseResp<dynamic>> dissolveGroup(@Path("groupId") String groupId);

  // 退出群组
  @DELETE('relation/groups/v1/quit/{groupId}')
  Future<BaseResp<dynamic>> quitGroup(@Path("groupId") String groupId);


  // 修改群组信息（名称, 等）
  @PUT('relation/groups/v1')
  Future<BaseResp<dynamic>> updateGroup(@Body() dynamic req);

  // 获取投诉显示详情
  @GET("relation/tipoff/options/v1")
  Future<BaseResp<List<TipOffItemResp>>> getComplainContentList();

  // 提交投诉内容( 文本 + id)
  @POST("relation/tipoff/record/v1")
  Future<BaseResp<dynamic>> submitComplain(@Body() TipOffCommitReq req);

  // 群管理
  @POST("relation/groups/v2/group/Management")
  Future<BaseResp<dynamic>> updateGroupManage(@Body() GroupManageItem req);

  // 获取群简略信息
  @POST("relation/groups/v1/groups/brief")
  Future<BaseResp<List<GroupBriefItem>>> getGroupBriefs(@Body() List<String> groups);

}