import 'package:dio/dio.dart' hide Headers;
import 'package:flutter_mixed/app/im/request/entity/forward_record_resp.dart';
import 'package:retrofit/retrofit.dart';

import '../../../retrofit/entity/base_resp.dart';
import '../entity/im_offline_notice_resp.dart';
import '../entity/im_offline_session_resp.dart';
import '../entity/msg_top_resp.dart';
import '../entity/offline_group_chat_resp.dart';
import '../entity/offline_notice_msg.dart';
import '../entity/offline_notice_session_reqbody.dart';
import '../entity/offline_session_reqbody.dart';

part 'offline_datasource.g.dart';

@RestApi()
abstract class OfflineDataSource {
  factory OfflineDataSource(Dio dio, {String baseUrl}) = _OfflineDataSource;

  // 离线会话  使用 Host.IM_OFFLINE_HOST
  @POST('conversation/v1')
  Future<BaseResp<List<ImOfflineSessionResp>>> getOfflineSessionList(
      @Body() OfflineSessionReq req);

  // 离线通知   Host.IM_OFFLINE_HOST
  @POST('conversation/notice/v1')
  Future<BaseResp<List<ImOfflineNoticeResp>>> getOfflineNoticeList(
      @Body() OfflineNoticeSessionReqBody req,@CancelRequest() CancelToken cancelToken);

  // 系统消息，通知消息，工作通知等
  // tag：公司id
  // @GET('conversation/notice/v1/{page}/{pageSize}/{tag}/{order}/{timeStart}/{timeEnd}/{type}')
  @GET('conversation/notice/v1/{page}/{pageSize}')
  Future<BaseResp<List<OfflineNoticeItemResp>>> getOfflineNoticeMsg(
      @Path("page") int page,
      @Path("pageSize") int pageSize,
      @Query("tag") String? tag,
      @Query("order") int order,
      @Query("timeStart") int timeStart,
      @Query("timeEnd") int timeEnd,
      @Query("type") int type,
      @CancelRequest() CancelToken cancelToken);

  // 设置置顶
  @POST('conversation/top/v1')
  Future<BaseResp<dynamic>> createMsgTops(@Body() MsgTopResp req);

  // 删除置顶
  @DELETE('conversation/top/v1')
  Future<BaseResp<dynamic>> delMsgTops(@Body() MsgTopReq req);

  @POST('conversation/forward/v1')
  Future<BaseResp<List<OfflineGroupChatResp>>> fetchForwardMsgList(
      @Body() FetchForwardRecord req);

  //获取审批离线未读
  @POST('conversation/notice/v1/count')
  Future<BaseResp<List<ListItem>>> getOfflineApproveUnread(
      @Body() OfflineNoticeSessionReqBody req);

  //获取单条通知详情
  @GET('conversation/notice/v1/{msgId}')
  Future<BaseResp<OfflineNoticeItemResp>> getSingleNoticeMsg(
      @Path("msgId") String msgId);

  //单群聊会话、置顶合并接口
  @POST('conversation/v2')
  Future<BaseResp<ImOfflineSessionV2Resp?>> getOfflineSessionListV2(@Body() OfflineSessionReq req);

  //查询当前会话的已读时间
  @GET('conversation/chat/v1/read')
  Future<BaseResp<dynamic>> getSessionReadTime(@Query("userId") String userId);

  //更新当前会话已读时间
  @PUT('conversation/chat/v1/read')
  Future<BaseResp<dynamic>> setSessionReadTime(@Body() Map<String,dynamic> userMap);
}
