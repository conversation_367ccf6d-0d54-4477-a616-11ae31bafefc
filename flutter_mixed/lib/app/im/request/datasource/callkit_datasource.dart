import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';

import '../../../retrofit/entity/base_resp.dart';
import '../entity/check_im_cal_resp.dart';

part 'callkit_datasource.g.dart';


/// 音视频会议相关 关接口  : baseUrl : Host.HOST
@RestApi()
abstract class CallKitDataSource {

  factory CallKitDataSource(Dio dio, {String baseUrl}) = _CallKitDataSource;

  // 获取当前群组的会议详情
  @GET('callkit/meeting/groupMeetings/{groupId}')
  Future<BaseResp<CheckImMeetingDetailResp>> checkGroupCallDetail(@Path("groupId") String groupId);


}