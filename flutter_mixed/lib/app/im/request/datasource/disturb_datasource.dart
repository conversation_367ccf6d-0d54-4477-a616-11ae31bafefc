import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

import '../../../retrofit/entity/base_resp.dart';
import '../../../retrofit/entity/work/group_response.dart';
import '../../constant/im_share.dart';
import '../entity/distrub_request_resp.dart';
import '../entity/im_socket_resp.dart';
import '../entity/im_token_resp.dart';
import '../entity/offline_group_chat_resp.dart';
import '../entity/offline_single_chat_resp.dart';
import '../entity/six_in_one_req.dart';
import '../entity/six_in_one_resp.dart';
part 'disturb_datasource.g.dart';


/// 免打扰相关接口  : baseUrl : Host.ROUTERHOST
@RestApi()
abstract class DisturbDataSource {

  factory DisturbDataSource(Dio dio, {String baseUrl}) = _DisturbDataSource;

  // 获取单聊的免打扰状态
  @GET('mute/single/v1')
  Future<BaseResp<List<String>>> getSingleDisturbState();

  // 单聊 开启免打扰
  @POST('mute/single/v1')
  Future<BaseResp<dynamic>> openSingleDisturb(@Body() OpenSingleDisturbReq req);

  // 单聊 关闭免打扰
  @DELETE('mute/single/v1/{target}')
  Future<BaseResp<dynamic>> closeSingleDisturb(@Path("target") String target);

  // 获取群聊的免打扰状态
  @GET('mute/group/v1')
  Future<BaseResp<List<String>>> getGroupDisturbState();

  // 群聊 开启免打扰
  @POST('mute/group/v1')
  Future<BaseResp<dynamic>> openGroupDisturb(@Body() OpenSingleDisturbReq req);

  // 群聊 关闭免打扰
  @DELETE('mute/group/v1/{target}')
  Future<BaseResp<dynamic>> closeGroupDisturb(@Path("target") String target);

  // 通知免打扰修改
  @POST('user/user/set/push/settings/v2')
  Future<BaseResp<dynamic>> modifyNoticeDisturb(@Body() NoticeDisturbReq req);

  // 工作通知免打扰修改
  @POST('user/user/set/push/settings')
  Future<BaseResp<dynamic>> modifyWorkNoticeDisturb(@Body() List<String> req);
}