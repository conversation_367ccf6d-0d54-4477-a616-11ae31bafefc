/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class GroupItemResp {
  String? groupId;
  String? name;
  String? logo;
  String? createUserId;
  int? createTime;
  int? type;
  String? orgId;
  int? receiveMode;
  int? status;
  List<GroupUser>? users = [];
  int? banned;
  int? addMember;
  int? hintMember;
  int? voice;
  String? colour;
  String? logoText;

  // 本地使用
  int? isAdmin = 0;

  // 是否解散了
  bool isDissolve() => status == 1;

  // 是否被踢出
  bool isKicked(uid) {
    if (users == null) return false;
    return !users!.any((item) => item.id == uid);
  }

  GroupItemResp({this.groupId, this.name, this.logo, this.createUserId, this.createTime, this.type, this.orgId, this.receiveMode, this.status, this.users, this.banned, this.addMember, this.hintMember, this.voice, this.colour, this.logoText});

  GroupItemResp.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    name = json['name'];
    logo = json['logo'];
    createUserId = json['createUserId'];
    createTime = json['createTime'];
    type = json['type'];
    orgId = json['orgId'];
    receiveMode = json['receiveMode'];
    status = json['status'];
    if (json['users'] != null) {
      users = <GroupUser>[];
      json['users'].forEach((v) {
        users!.add(GroupUser.fromJson(v));
      });
    }
    banned = json['banned'];
    addMember = json['addMember'];
    hintMember = json['hintMember'];
    voice = json['voice'];
    colour = json['colour'];
    logoText = json['logoText'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['groupId'] = groupId;
    data['name'] = name;
    data['logo'] = logo;
    data['createUserId'] = createUserId;
    data['createTime'] = createTime;
    data['type'] = type;
    data['orgId'] = orgId;
    data['receiveMode'] = receiveMode;
    data['status'] = status;
    data['users'] =users != null ? users!.map((v) => v?.toJson()).toList() : null;
    data['banned'] = banned;
    data['addMember'] = addMember;
    data['hintMember'] = hintMember;
    data['voice'] = voice;
    data['colour'] = colour;
    data['logoText'] = logoText;
    return data;
  }
}

class GroupUser {
  String? id;
  String? headimg;
  String? version;
  String? name;
  int? identity; //  0 普通用户，  1： 群主， 2：管理员
  String? initial;

  int? type = 0;  // 0:成员； 1 添加按钮；  2 删除按钮

  GroupUser({this.id, this.headimg, this.version, this.name, this.identity, this.initial});

  bool isMember() => type == 0;
  bool isAddButton() => type == 1;
  bool isRemoveButton() => type == 2;

  GroupUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    headimg = json['headimg'];
    version = json['version'];
    name = json['name'];
    identity = json['identity'];
    initial = json['initial'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['headimg'] = headimg;
    data['version'] = version;
    data['name'] = name;
    data['identity'] = identity;
    data['initial'] = initial;
    return data;
  }

  @override
  String toString() {
    return 'GroupUser{id: $id, headimg: $headimg, version: $version, name: $name, identity: $identity, initial: $initial, type: $type}';
  }
}

