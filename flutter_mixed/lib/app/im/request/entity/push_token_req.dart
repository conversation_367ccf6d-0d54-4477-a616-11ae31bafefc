

class PushTokenReqBody {

  int client = 0;
  int versionCode = 0;
  String channel = '';
  String deviceToken = '';


  PushTokenReqBody({required this.client, required this.versionCode, required this.channel,
      required this.deviceToken});

  factory PushTokenReqBody.fromJson(Map<String, dynamic> json) {
    return PushTokenReqBody(
      client: json['client'],
      versionCode: json['versionCode'],
      channel: json['channel'],
      deviceToken: json['deviceToken'],
    );
  }

  Map<String, dynamic> toJson() => {
        'client': client,
        'versionCode': versionCode,
        'channel': channel,
        'deviceToken': deviceToken,
      };
}