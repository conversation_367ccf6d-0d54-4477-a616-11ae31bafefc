

/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/

class CheckImMeetingDetailResp {
  String? duration;
  String? jobId;
  String? endJobId;
  String? createUserId;
  int? createTime;
  String? groupId;
  String? members;
  List<MeetingMember?>? meetingMember;
  String? id;
  String? type;
  int? roomId;
  String? status;

  CheckImMeetingDetailResp({this.duration, this.jobId, this.endJobId, this.createUserId, this.createTime, this.groupId, this.members, this.meetingMember, this.id, this.type, this.roomId, this.status});

  CheckImMeetingDetailResp.fromJson(Map<String, dynamic> json) {
    duration = json['duration'];
    jobId = json['jobId'];
    endJobId = json['endJobId'];
    createUserId = json['createUserId'];
    createTime = json['createTime'];
    groupId = json['groupId'];
    members = json['members'];
    if (json['meetingMember'] != null) {
      meetingMember = <MeetingMember>[];
      json['meetingMember'].forEach((v) {
        meetingMember!.add(MeetingMember.fromJson(v));
      });
    }
    id = json['id'];
    type = json['type'];
    roomId = json['roomId'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['duration'] = duration;
    data['jobId'] = jobId;
    data['endJobId'] = endJobId;
    data['createUserId'] = createUserId;
    data['createTime'] = createTime;
    data['groupId'] = groupId;
    data['members'] = members;
    data['meetingMember'] =meetingMember != null ? meetingMember!.map((v) => v?.toJson()).toList() : null;
    data['id'] = id;
    data['type'] = type;
    data['roomId'] = roomId;
    data['status'] = status;
    return data;
  }
}

class MeetingMember {
  String? name;
  String? meetingId;
  String? avatar;
  String? userId;
  String? status;

  MeetingMember({this.name, this.meetingId, this.avatar, this.userId, this.status});

  MeetingMember.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    meetingId = json['meetingId'];
    avatar = json['avatar'];
    userId = json['userId'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['name'] = name;
    data['meetingId'] = meetingId;
    data['avatar'] = avatar;
    data['userId'] = userId;
    data['status'] = status;
    return data;
  }
}
