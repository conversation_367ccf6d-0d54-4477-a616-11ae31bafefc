
///{
//     "code": 1,
//     "msg": "请求成功",
//     "data": {
//         "versionName": "4.1.2",
//         "desc": "提高了用户体验",
//         "downUrl": "https://cdn.ddbes.com/Android/app-4.1.2.apk",
//         "status": 0,
//         "currentDesc": "提高了用户体验",
//         "auditing": 1,
//         "updateToken": 1,
//         "bannerList": [
//             {
//                 "picture": "https://cdn.ddbes.com/BANNER/banner-4.0.0.png",
//                 "url": "",
//                 "id": "2603727576346133502",
//                 "name": "4.0"
//             }
//         ],
//         "ids": [
//             "805"
//         ],
//         "approvalPushSwitch": 0,
//         "systemPushSwitch": 0,
//         "region": "ap-chongqing",
//         "appId": "1304188286",
//         "pre": "https://cdn.ddbes.com",
//         "tel": "010-56938606",
//         "linkWarn": "该网页不是由担当办公提供，担当办公无法确保内容的安全性，如果要继续访问，请注意保护好个人信息。近期平台多发刷单诈骗，手工活诈骗，期货诈骗，冒充军人诈骗，银行卡信用卡解封诈骗，请注意防范。",
//         "linkWhiteList": [
//             "ddbes.com",
//             "ddbes.cn"
//         ]
//     }
// }

class SixInOneResp {
  String? versionName;
  String? desc;
  String? downUrl;
  int? status;
  String? currentDesc;
  int? auditing;  // /**是否通过审核： 0 审核中， 1 通过*/
  int? updateToken;
  List<BannerList?>? bannerList;
  List<String?>? ids = [];   // 开启了免打扰的公司(群组)id

  int? approvalPushSwitch;  /**审批通知免打扰开关 0 关闭 1 开启 */
  int? systemPushSwitch;   /**系统通知免打扰开关 0 关闭 1 开启 */
  int? ticketPushSwitch;   /**工单通知免打扰开关 0 关闭 1 开启 */

  int? trainingSwitch;  // 培训免打扰开关
  int? strangerSwitch;   // 陌生人免打扰开关
  int? inventorySwitch;  // 库存免打扰开关
  int? kingdeePushSwitch;  // 金蝶免打扰开关
  int? managementSwitch;  // 综合管理平台免打扰开关
  int? rangeParkSwitch;  // 园区通知免打扰开关
  int? idcSwitch;  // 知识库免打扰开关

  String? region;
  String? appId;
  String? pre;
  String? tel;
  String? linkWarn;
  List<String?>? linkWhiteList = [];

  SixInOneResp({this.versionName, this.desc, this.downUrl, this.status, this.currentDesc, this.auditing, this.updateToken, this.bannerList, this.ids
    , this.approvalPushSwitch, this.systemPushSwitch
    , this.ticketPushSwitch
    , this.trainingSwitch , this.strangerSwitch , this.inventorySwitch
    , this.kingdeePushSwitch
    , this.managementSwitch
    , this.rangeParkSwitch
    , this.idcSwitch
    , this.region, this.appId, this.pre, this.tel, this.linkWarn, this.linkWhiteList});

  SixInOneResp.fromJson(Map<String, dynamic> json) {
    versionName = json['versionName'];
    desc = json['desc'];
    downUrl = json['downUrl'];
    status = json['status'];
    currentDesc = json['currentDesc'];
    auditing = json['auditing'];
    updateToken = json['updateToken'];
    if (json['bannerList'] != null) {
      bannerList = <BannerList>[];
      json['bannerList'].forEach((v) {
        bannerList!.add(BannerList.fromJson(v));
      });
    }
    if (json['ids'] != null) {
      ids = <String>[];
      json['ids'].forEach((v) {
        ids!.add(v);
      });
    }
    approvalPushSwitch = json['approvalPushSwitch'];
    systemPushSwitch = json['systemPushSwitch'];
    ticketPushSwitch = json['ticketPushSwitch'];

    trainingSwitch = json['trainingSwitch'];
    strangerSwitch = json['strangerSwitch'];
    inventorySwitch = json['inventorySwitch'];
    kingdeePushSwitch = json['kingdeePushSwitch'];
    managementSwitch = json['managementSwitch'];
    rangeParkSwitch = json['rangeParkSwitch'];
    idcSwitch = json['idcSwitch'];

    region = json['region'];
    appId = json['appId'];
    pre = json['pre'];
    tel = json['tel'];
    linkWarn = json['linkWarn'];
    if (json['linkWhiteList'] != null) {
      linkWhiteList = <String>[];
      json['linkWhiteList'].forEach((v) {
        linkWhiteList!.add((v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['versionName'] = versionName;
    data['desc'] = desc;
    data['downUrl'] = downUrl;
    data['status'] = status;
    data['currentDesc'] = currentDesc;
    data['auditing'] = auditing;
    data['updateToken'] = updateToken;
    data['bannerList'] =bannerList != null ? bannerList!.map((v) => v?.toJson()).toList() : null;
    data['ids'] =ids != null ? ids!.map((v) => v).toList() : null;

    data['approvalPushSwitch'] = approvalPushSwitch;
    data['systemPushSwitch'] = systemPushSwitch;
    data['ticketPushSwitch'] = ticketPushSwitch;
    data['trainingSwitch'] = trainingSwitch;
    data['strangerSwitch'] = strangerSwitch;
    data['inventorySwitch'] = inventorySwitch;
    data['kingdeePushSwitch'] = kingdeePushSwitch;
    data['managementSwitch'] = managementSwitch;
    data['rangeParkSwitch'] = rangeParkSwitch;
    data['idcSwitch'] = idcSwitch;

    data['region'] = region;
    data['appId'] = appId;
    data['pre'] = pre;
    data['tel'] = tel;
    data['linkWarn'] = linkWarn;
    data['linkWhiteList'] =linkWhiteList != null ? linkWhiteList!.map((v) => v).toList() : null;
    return data;
  }
}

class BannerList {
  String? picture;
  String? url;
  String? id;
  String? name;

  BannerList({this.picture, this.url, this.id, this.name});

  BannerList.fromJson(Map<String, dynamic> json) {
    picture = json['picture'];
    url = json['url'];
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['picture'] = picture;
    data['url'] = url;
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}