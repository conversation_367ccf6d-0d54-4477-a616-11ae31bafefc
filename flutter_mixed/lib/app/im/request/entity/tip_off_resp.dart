

/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class TipOffItemResp {
  String? id;
  String? content;
  List<TipOff>? tipOff;

  TipOffItemResp({this.id, this.content, this.tipOff});

  TipOffItemResp.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    content = json['content'];
    if (json['tipOff'] != null) {
      tipOff = <TipOff>[];
      json['tipOff'].forEach((v) {
        tipOff!.add(TipOff.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['content'] = content;
    data['tipOff'] =tipOff != null ? tipOff!.map((v) => v?.toJson()).toList() : null;
    return data;
  }
}

class TipOff {
  String? id;
  String? content;

  bool selected = false;

  TipOff({this.id, this.content});

  TipOff.fromJson(Map<String, dynamic> json) {
    id = json['_id'];
    content = json['content'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['_id'] = id;
    data['content'] = content;
    return data;
  }
}

