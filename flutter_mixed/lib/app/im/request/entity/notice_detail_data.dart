import 'dart:convert';

import 'package:flutter_mixed/logger/logger.dart';

/// 通用通知模版实体
class NoticeDataDetail {
  String? routeId = "";
  String? userName = "";
  String? header = "";
  String? userId = "";
  String? handlerName = "";
  String? handlerAvatar = "";
  String? backgroundUrl =
      ""; //模板2的背景图片，通知模板2 的图片统一为 2:1的比例  默认992 X 496 px分辨率，模板内的图片等比放大
  String? promptTitle = ""; //模板2背景图片上显示的文字
  int? needClock = 0;
  int? typeId = 0;
  int? stteId = 0; //没有使用
//汇报时用到的字段
  String? modelId = "";
  String? modelName = "";
  String? reportContent = "";
  String? reportName = "";
//模板6,邀请提交汇报用到的字段
  String? avatar = "";
  String? end = "";
  String? endDay = "";
  int? holiday = 0;
  int? period = 0;
  int? pre = 0;
  String? start = "";
  String? startDay = "";
  List<String> week = [];
//去汇报统计
  String endTime = "";
  String? prompt = "";
  String? startTime = "";
//被授予权限去详情用到的字段
  String? permissions = "";
  String? link = ""; //直接跳转到指定的web地址

  List<int>? signatureButton = []; //里边的int代表buttonId,,有则需要跳转手写签名

  int? paramType = 0;
  String? routeName;
  Map<String, dynamic>? argument;
  String? nameBackColor;
  int? contentHtml;

  NoticeDataDetail(
      this.routeId,
      this.userName,
      this.header,
      this.userId,
      this.handlerName,
      this.handlerAvatar,
      this.backgroundUrl,
      this.promptTitle,
      this.needClock,
      this.typeId,
      this.stteId,
      this.modelName,
      this.reportContent,
      this.reportName,
      this.avatar,
      this.end,
      this.endDay,
      this.holiday,
      this.period,
      this.pre,
      this.start,
      this.startDay,
      this.week,
      this.endTime,
      this.prompt,
      this.startTime,
      this.permissions,
      this.link,
      this.signatureButton,
      this.paramType,
      this.routeName,
      this.argument,
      this.nameBackColor,
      this.contentHtml
      );

  NoticeDataDetail.fromJson(Map<String, dynamic> json) {
    routeId = json['routeId'] ?? '';
    userName = json['userName'] ?? '';
    header = json['header'] ?? '';
    userId = json['userId'] ?? '';
    handlerName = json['handlerName'] ?? '';
    handlerAvatar = json['handlerAvatar'] ?? '';
    backgroundUrl = json['backgroundUrl'] ?? '';
    promptTitle = json['promptTitle'] ?? '';
    needClock = json['needClock'] ?? 0;
    typeId = json['typeId'] ?? 0;
    stteId = json['stteId'] ?? 0;
    modelId = json['modelId'] ?? '';
    modelName = json['modelName'] ?? '';
    reportContent = json['reportContent'] ?? '';
    reportName = json['reportName'] ?? '';
    avatar = json['avatar'] ?? '';
    end = json['end'] ?? '';
    endDay = json['endDay'] ?? '';
    holiday = json['holiday'] ?? 0;
    period = json['period'] ?? 0;
    pre = json['pre'] ?? 0;
    start = json['start'] ?? '';
    startDay = json['startDay'] ?? '';
    week = json['week'] ?? [];
    endTime = json['endTime'] ?? '';
    prompt = json['prompt'] ?? '';
    startTime = json['startTime'] ?? '';
    permissions = json['permissions'] ?? '';
    link = json['link'] ?? '';
    signatureButton = json['signatureButton'] ?? [];
    paramType = json['paramType'] ?? 0;
    routeName = json['routeName'] ?? '';
    if (json['argument'] is String) {
      argument = jsonDecode(json['argument']) ?? '';
    } else if (json['argument'] is Map) {
      argument = json['argument'] ?? {};
    }
    nameBackColor = json['nameBackColor'] ?? '';

    if (json['contentHtml'] is String) {
      contentHtml = int.parse(json['contentHtml']);
    } else if (json['contentHtml'] is int) {
      contentHtml = json['contentHtml'];
    }else{
      contentHtml = 0;
    }
  }

  Map<String, dynamic> toJson() => {
        'routeId': routeId,
        'userName': userName,
        'header': header,
        'userId': userId,
        'handlerName': handlerName,
        'handlerAvatar': handlerAvatar,
        'backgroundUrl': backgroundUrl,
        'promptTitle': promptTitle,
        'needClock': needClock,
        'typeId': typeId,
        'stteId': stteId,
        'modelId': modelId,
        'modelName': modelName,
        'reportContent': reportContent,
        'reportName': reportName,
        'avatar': avatar,
        'end': end,
        'endDay': endDay,
        'holiday': holiday,
        'period': period,
        'pre': pre,
        'start': start,
        'startDay': startDay,
        'week': week,
        'endTime': endTime,
        'prompt': prompt,
        'startTime': startTime,
        'permissions': permissions,
        'link': link,
        'signatureButton': signatureButton,
        'paramType': paramType,
        'routeName': routeName,
        'argument': argument,
        'nameBackColor': nameBackColor,
        'contentHtml':contentHtml
      };
}
