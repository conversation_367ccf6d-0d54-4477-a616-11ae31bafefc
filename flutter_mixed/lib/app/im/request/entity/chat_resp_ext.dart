import 'dart:convert';

import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../utils/string-util.dart';

/// 单聊和群聊的离线实体，相同的扩展部分
mixin ChatRespExt {
  ExtGroupBean? ext; // 扩展类型
  FileGroupBean? file; // 文件
  ImageGroupBean? image; // 图像
  LocationGroupBean? location; // 位置
  VideoGroupBean? video; // 视频
  VoiceGroupBean? voice; // 音频
  WithDrawBean? withdraw; // 撤回

  CallMsgGroupBean? call;
}

class ExtGroupBean {
  String? ext;
  String? ext1;
  String? ext2;
  String? ext3;

  ExtGroupBean({this.ext1, this.ext2, this.ext3, this.ext});

  ExtGroupBean.fromJson(Map<String, dynamic> json) {
    ext = json['ext'];
    ext1 = json['ext1'];
    ext2 = json['ext2'];
    ext3 = json['ext3'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['ext'] = ext;
    data['ext1'] = ext1;
    data['ext2'] = ext2;
    data['ext3'] = ext3;
    return data;
  }
}

class FileGroupBean {
  String? fileId;
  int? fileSize;
  String? name;
  String? url;

  FileGroupBean(this.fileId, this.fileSize, this.name, this.url);

  FileGroupBean.fromJson(Map<String, dynamic> json) {
    fileId = json['fileId'];
    fileSize = json['fileSize'];
    name = json['name'];
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['fileId'] = fileId;
    data['fileSize'] = fileSize;
    data['name'] = name;
    data['url'] = url;
    return data;
  }
}

class ImageGroupBean {
  double? height;
  double? width;
  String? imageId;
  String? thumbnail;
  String? thumbnailUrl;
  String? url;

  ImageGroupBean(this.height, this.width, this.imageId, this.thumbnail,
      this.thumbnailUrl, this.url);

  ImageGroupBean.fromJson(Map<String, dynamic> json) {
    height = json['height'];
    width = json['width'];
    imageId = json['imageId'];
    thumbnail = json['thumbnail'];
    thumbnailUrl = json['thumbnailUrl'];
    url = json['url'];
  }

  Map<String, dynamic> toJson() => {
        'height': height,
        'width': width,
        'imageId': imageId,
        'thumbnail': thumbnail,
        'thumbnailUrl': thumbnailUrl,
        'url': url,
      };
}

class LocationGroupBean {
  String? address;
  double? latitude;
  double? longitude;
  String? title;
  String? uri;

  LocationGroupBean(
      {this.address, this.latitude, this.longitude, this.title, this.uri});

  factory LocationGroupBean.fromJson(Map<String, dynamic> json) {
    return LocationGroupBean(
      address: json['address'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      title: json['title'],
      uri: json['uri'],
    );
  }

  Map<String, dynamic> toJson() => {
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
        'title': title,
        'uri': uri,
      };
}

class VideoGroupBean {
  ImageGroupBean? cover;
  int? duration;
  String? fileId;
  int? fileSize;
  String? url;

  VideoGroupBean(
      {this.cover, this.duration, this.fileId, this.fileSize, this.url});

  factory VideoGroupBean.fromJson(Map<String, dynamic> json) {
    return VideoGroupBean(
      // cover: (json['cover']),
      cover: ImageGroupBean.fromJson(json['cover']),
      duration: json['duration'],
      fileId: json['fileId'],
      fileSize: json['fileSize'],
      url: json['url'],
    );
  }

  Map<String, dynamic> toJson() => {
        'cover': cover,
        'duration': duration,
        'fileId': fileId,
        'fileSize': fileSize,
        'url': url,
      };
}

class VoiceGroupBean {
  int? duration;
  String? voiceId;
  String? url;

  VoiceGroupBean({this.duration, this.voiceId, this.url});

  factory VoiceGroupBean.fromJson(Map<String, dynamic> json) {
    return VoiceGroupBean(
      duration: json['duration'],
      voiceId: json['voiceId'],
      url: json['url'],
    );
  }

  Map<String, dynamic> toJson() => {
        'duration': duration,
        'voiceId': voiceId,
        'url': url,
      };
}

// class NoticeMsg {
//   String? title;
//   String? subtitle;
//   String? context;
//   String? data;
//   String? ext;

//   NoticeMsg({this.title, this.subtitle, this.context, this.data, this.ext});

//   NoticeMsg.fromJson(Map<String, dynamic> json) {
//     title = json['title'];
//     subtitle = json['subtitle'];
//     context = json['context'];
//     data = json['data'];
//     ext = json['ext'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> map = Map<String, dynamic>();
//     map['title'] = title;
//     map['subtitle'] = subtitle;
//     map['context'] = context;
//     map['data'] = data;
//     map['ext'] = ext;
//     return map;
//   }
//}

class SenderUserInfo {
  String? nickname;
  String? userId;
  String? avatar;
  String? imUserId;

  SenderUserInfo({this.nickname, this.userId, this.avatar, this.imUserId});

  SenderUserInfo.fromJson(Map<String, dynamic> json) {
    nickname = json['nickname'];
    userId = json['userId'];
    avatar = json['avatar'];
    imUserId = json['imUserId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['nickname'] = nickname;
    data['userId'] = userId;
    data['avatar'] = avatar;
    data['imUserId'] = imUserId;
    return data;
  }
}

class GroupNoticeDataBean {
  String? content;
  int? msgType;
  String? name;
  String? userId;

  GroupNoticeDataBean.fromJson(Map<String, dynamic> json) {
    content = json['content'];
    userId = json['userId'];
    name = json['name'];
    userId = json['userId'];
    msgType = json['msgType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['content'] = content;
    data['msgType'] = msgType;
    data['name'] = name;
    data['userId'] = userId;
    return data;
  }
}

class InviteGroupNoticeContentBean {
  String? userId;
  String? name;

  InviteGroupNoticeContentBean({this.userId, this.name});

  factory InviteGroupNoticeContentBean.fromJson(Map<String, dynamic> json) {
    return InviteGroupNoticeContentBean(
      userId: json['userId'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() => {
        'userId': userId,
        'name': name,
      };
}

class ReNameReCreaterContentBean {
  int? color;
  String? content;
  int? symbol;

  ReNameReCreaterContentBean({this.color, this.content, this.symbol});

  factory ReNameReCreaterContentBean.fromJson(Map<String, dynamic> json) {
    return ReNameReCreaterContentBean(
      color: json['color'],
      content: json['content'],
      symbol: json['symbol'],
    );
  }

  Map<String, dynamic> toJson() => {
        'color': color,
        'content': content,
        'symbol': symbol,
      };
}

class GroupVoiceVideoReceiveMsgBean {
  int? callType = 0;
  String? content;
  String? meetingId = "";
  String? msgType = "";
  String? personList = "";
  String? sendId = "";

  GroupVoiceVideoReceiveMsgBean(
      {this.callType,
      this.content,
      this.meetingId,
      this.msgType,
      this.personList,
      this.sendId});

  factory GroupVoiceVideoReceiveMsgBean.fromJson(Map<String, dynamic> json) {
    return GroupVoiceVideoReceiveMsgBean(
      callType: json['callType'] is int
          ? json['callType']
          : int.parse((json['callType'] ?? '0')),
      content: json['content'],
      meetingId: json['meetingId'],
      msgType: json['msgType'] is String
          ? json['msgType']
          : '${json['msgType'] ?? 0}',
      personList: json['personList'],
      sendId: json['sendId'],
    );
  }

  Map<String, dynamic> toJson() => {
        'callType': callType,
        'content': content,
        'meetingId': meetingId,
        'msgType': msgType,
        'personList': personList,
        'sendId': sendId,
      };
}

class WithDrawBean {
  String? msgId = '';

  WithDrawBean({this.msgId});

  factory WithDrawBean.fromJson(Map<String, dynamic> json) {
    return WithDrawBean(
      msgId: json['msgId'],
    );
  }

  Map<String, dynamic> toJson() => {
        'msgId': msgId,
      };
}

class CallMsgGroupBean {
  String? content;
  String? metingId;
  int? type;

  CallMsgGroupBean({this.content, this.metingId, this.type});

  factory CallMsgGroupBean.fromJson(Map<String, dynamic> json) {
    return CallMsgGroupBean(
      content: json['content'],
      metingId: json['metingId'],
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() => {
        'content': content,
        'metingId': metingId,
        'type': type,
      };
}

// 聊天记录
class MsgUIRecord {
  String? title;
  int? sessionType;
  List<String>? msgIds = <String>[];
  List<String>? showText = <String>[];

  List<MessageItem> messages = [];

  MsgUIRecord({this.title, this.msgIds, this.showText, this.sessionType});

  factory MsgUIRecord.fromJson(Map<String, dynamic> json) {
    var msgidJson = json['msgIds'];
    var list = <String>[];
    if(msgidJson != null){
      list =  List<String>.from(
          json['msgIds'].map((e) => e?.toString() ?? '') // 将 null 转为空字符串
      );;
    }
    return MsgUIRecord(
        title: json['title'],
        sessionType: json['sessionType'],
        msgIds: list,
        showText: List<String>.from(json['showText']));
  }

  Map<String, dynamic> toJson() => {
        'title': title,
        'sessionType': sessionType,
        'msgIds': msgIds,
        'showText': showText,
      };

  @override
  String toString() {
    return 'MsgUIRecord{title: $title, sessionType: $sessionType, msgIds: $msgIds, showText: $showText}';
  }
}

class MsgBotUIData {
  String? noticeName;
  String? noticeTitle;
  String? noticeNameTextColor;
  String? noticeNameBgColor;

  String? botBtnText;
  List<BotOldItem> oldItems = [];

  // json 中 tempType == 7 时 才可以copy
  bool canCopy = false;

  MsgBotUIData parse(String? extendOne) {
    if (!StringUtil.isEmpty(extendOne)) {
      try {
        var jsonObj = json.decode(extendOne!);
        var noticeName = jsonObj['noticeName']; // 主标题
        var noticeTitle = jsonObj['noticeTitle']; // 副标题

        this
          ..noticeName = noticeName ?? ''
          ..noticeTitle = noticeTitle ?? '';

        try {
          var detail = jsonObj['detail'];
          var detailJsonObj = json.decode(detail);
          var nameBackColor = detailJsonObj['nameBackColor'];
          var nameTextColor = detailJsonObj['nameTextColor'];
          var contentHtml = detailJsonObj['contentHtml'];
          if (detailJsonObj['contentHtml'] is String) {
            contentHtml = int.parse(contentHtml);
          }
          if (contentHtml == 1) {
            this
              ..noticeNameTextColor = nameTextColor
              ..noticeNameBgColor = nameBackColor;
          }
        } catch (e) {
          print(e);
        }

        // 解析按钮
        var buttonsString = jsonObj['buttons'];
        try {
          var buttonJsonObj = json.decode(buttonsString);
          var buttons = List<BotButton>.from(
              buttonJsonObj.map((model) => BotButton.fromJson(model)));
          if (buttons.isNotEmpty) {
            var resultTitle = buttons[0].resultTitle;
            if (resultTitle != null) {
              this.botBtnText = resultTitle;
            }
          }
        } catch (e) {
          logger('解析机器人：buttons： ${buttonsString}');
        }

        //  值为7的时候为新版本bot样式，否则为含有html标签的老样式
        var tempType = jsonObj['tempType'];
        canCopy = (tempType == 7);
        if (tempType == 7) {
          var content = jsonObj['content'];
          var contentObj = json.decode(content);
          var newList = List<BotNewItem>.from(contentObj
              .map((model) => BotNewItem.fromJson(json.decode(model))));
          this.oldItems = newList
              .map((e) => BotOldItem(
                  type: e.type,
                  title: e.leftText,
                  content: e.rightText,
                  leftColor: e.leftColor,
                  rightColor: e.rightColor))
              .toList();
        } else {
          var content = jsonObj['content'];
          if (content is! String) {
            var list = List<String>.from(content);
            this.oldItems = list
                .map((e) => BotOldItem(
                      type: 3,
                      title: e,
                    ))
                .toList();
          } else {
            var content = jsonObj['content'];
            this.oldItems.add(BotOldItem(type: 3, content: content));
          }
        }
      } catch (e) {
        print('bot解析----$e');
      }
    }

    return this;
  }
}

class BotButton {
  String? resultTitle;
  BotButton({this.resultTitle});

  factory BotButton.fromJson(Map<String, dynamic> json) {
    return BotButton(
      resultTitle: json['resultTitle'],
    );
  }
  Map<String, dynamic> toJson() => {
        'resultTitle': resultTitle,
      };
}

class BotNewItem {
  int? type;
  String? leftColor;
  String? rightColor;
  String? leftText;
  String? rightText;

  BotNewItem(
      {this.type,
      this.leftColor,
      this.rightColor,
      this.leftText,
      this.rightText});

  factory BotNewItem.fromJson(Map<String, dynamic> json) {
    return BotNewItem(
      type: json['type'],
      leftColor: json['leftColor'],
      rightColor: json['rightColor'],
      leftText: json['leftText'],
      rightText: json['rightText'],
    );
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'leftColor': leftColor,
        'rightColor': rightColor,
        'leftText': leftText,
        'rightText': rightText,
      };
}

class BotOldItem {
  int? type;
  String? title;
  String? leftColor;
  String? rightColor;
  String? content;

  BotOldItem(
      {this.type, this.title, this.leftColor, this.rightColor, this.content});

  factory BotOldItem.fromJson(Map<String, dynamic> json) {
    return BotOldItem(
      type: json['type'],
      title: json['title'],
      leftColor: json['leftColor'],
      rightColor: json['rightColor'],
      content: json['content'],
    );
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'title': title,
        'leftColor': leftColor,
        'rightColor': rightColor,
        'content': content,
      };
}
