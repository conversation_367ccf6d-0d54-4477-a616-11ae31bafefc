
/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/

class OfflineSessionReq {
  List<Group?> group = [];
  List<Single?> single = [];

  OfflineSessionReq(this.group, this.single);

  OfflineSessionReq.fromJson(Map<String, dynamic> json) {
    if (json['group'] != null) {
      group = <Group>[];
      json['group'].forEach((v) {
        group.add(Group.fromJson(v));
      });
    }
    if (json['single'] != null) {
      single = <Single>[];
      json['single'].forEach((v) {
        single!.add(Single.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['group'] =group != null ? group!.map((v) => v?.toJson()).toList() : null;
    data['single'] =single != null ? single!.map((v) => v?.toJson()).toList() : null;
    return data;
  }

  @override
  String toString() {
    return 'OfflineSessionReq{group: $group, single: $single}';
  }
}


class Group {
  String? id;
  int? time;

  Group({this.id, this.time});

  Group.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['time'] = time;
    return data;
  }

  @override
  String toString() {
    return 'Group{id: $id, time: $time}';
  }
}


class Single {
  String? id;
  int? time;

  Single({this.id, this.time});

  Single.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['time'] = time;
    return data;
  }

  @override
  String toString() {
    return 'Single{id: $id, time: $time}';
  }
}

