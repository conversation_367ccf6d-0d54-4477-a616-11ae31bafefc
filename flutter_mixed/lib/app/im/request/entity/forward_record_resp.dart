


class FetchForwardRecord {
  int? type;
  List<String>? ids = [];

  FetchForwardRecord({this.type, this.ids});

  factory FetchForwardRecord.fromJson(Map<String, dynamic> json) {
    return FetchForwardRecord(
      type: json['type'],
      ids: json['ids'],
    );
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'ids': ids,
      };

  @override
  String toString() {
    return 'FetchForwardRecord{type: $type, ids: $ids}';
  }
}