

/*
//  单聊离线消息 resp
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
import 'dart:convert';

import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';

import 'chat_resp_ext.dart';

class OfflineSingleChatResp with ChatRespExt{
  int? type;
  String? msgId;
  String? userIdSmaller;
  String? userIdBigger;
  String? senderId;
  UserInfo? userInfo;
  String? msg;
  int? sendTime;
  int? conversationId;
  int? contextCase;
  ClientNoticeMsg? noticeMsg;

  OfflineSingleChatResp({this.type, this.msgId, this.userIdSmaller, this.userIdBigger,
    this.senderId, this.userInfo, this.msg, this.sendTime, this.conversationId, this.contextCase,
    this.noticeMsg
  });

  OfflineSingleChatResp.fromJson(Map<String, dynamic> json) {

    type = json['type'];
    msgId = json['msgId'];
    userIdSmaller = json['userIdSmaller'];
    userIdBigger = json['userIdBigger'];
    senderId = json['senderId'];
    userInfo = json['userInfo'] != null ? UserInfo?.fromJson(json['userInfo']) : null;
    msg = json['msg'];
    sendTime = json['sendTime'];
    conversationId = json['conversationId'];
    contextCase = json['contextCase'];
    ext = json['ext'] != null ? ExtGroupBean.fromJson(json['ext']) : null;
    file = json['file'] != null ? FileGroupBean.fromJson(json['file']) : null;
    image = json['image'] != null ? ImageGroupBean.fromJson((json['image'])): null;
    location = json['location'] != null ? LocationGroupBean.fromJson(json['location']) : null;
    video = json['video'] != null ? VideoGroupBean.fromJson((json['video'])) : null;
    voice = json['voice'] != null ? VoiceGroupBean.fromJson(json['voice']) : null;
    withdraw = json['withdraw'] != null ? WithDrawBean.fromJson(json['withdraw']) : null;
    call = json['call'] != null ? CallMsgGroupBean.fromJson(json['call']) : null;
    noticeMsg = json['noticeMsg'] != null ? ClientNoticeMsg?.fromJson(json['noticeMsg']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['type'] = type;
    data['msgId'] = msgId;
    data['userIdSmaller'] = userIdSmaller;
    data['userIdBigger'] = userIdBigger;
    data['senderId'] = senderId;
    data['userInfo'] = userInfo!.toJson();
    data['msg'] = msg;
    data['sendTime'] = sendTime;
    data['conversationId'] = conversationId;
    data['contextCase'] = contextCase;
    data['ext'] =  ext == null ? null : ext!.toJson();
    data['file'] = file == null ? null : file!.toJson();
    data['image'] = image == null ? null : image!.toJson();
    data['location'] =  location == null ? null : location!.toJson();
    data['video'] = video == null ? null : video!.toJson();
    data['voice'] = voice == null ? null : voice!.toJson();
    data['withdraw'] = withdraw == null ? null : withdraw!.toJson();
    data['call'] = call == null ? null : call!.toJson();
    data['noticeMsg'] = noticeMsg == null ? null :noticeMsg!.toJson();
    return data;
  }
}

class UserInfo {
  String? nickname;
  String? userId;
  String? avatar;
  String? imUserId;

  UserInfo({this.nickname, this.userId, this.avatar, this.imUserId});

  UserInfo.fromJson(Map<String, dynamic> json) {
    nickname = json['nickname'];
    userId = json['userId'];
    avatar = json['avatar'];
    imUserId = json['imUserId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['nickname'] = nickname;
    data['userId'] = userId;
    data['avatar'] = avatar;
    data['imUserId'] = imUserId;
    return data;
  }
}

