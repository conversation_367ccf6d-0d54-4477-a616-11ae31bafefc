

class GroupBriefItem {
  String? groupId;
  String? name;
  String? logo;

  GroupBriefItem({this.groupId, this.name, this.logo});

  GroupBriefItem.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    name = json['name'];
    logo = json['logo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['groupId'] = groupId;
    data['name'] = name;
    data['logo'] = logo;
    return data;
  }
}

class GroupManageItem {
  String? groupId;
  int? operation;
  int? type;

  GroupManageItem({this.groupId, this.operation, this.type});

  GroupManageItem.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    operation = json['operation'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['groupId'] = groupId;
    data['operation'] = operation;
    data['type'] = type;
    return data;
  }
}


