

class OpenSingleDisturbReq {

  String target = '';

  OpenSingleDisturbReq.fromJson(Map<String, dynamic> json) {
    target = json['target'] ?? '';
  }

  Map<String, dynamic> toJson() => {
        'target': target,
      };

  OpenSingleDisturbReq(this.target);
}



class NoticeDisturbReq{
  List<String> ids = [];
  int? systemPushSwitch = 0;
  int? approvalPushSwitch = 0;
  int? ticketPushSwitch = 0;
  int? inventorySwitch = 0;
  int? kingdeePushSwitch = 0;
  int? trainingSwitch = 0;
  int? managementSwitch = 0;
  int? rangeParkSwitch = 0;
  int? idcSwitch = 0;

  NoticeDisturbReq(
      this.ids, {this.systemPushSwitch,
    this.approvalPushSwitch,
    this.ticketPushSwitch,
    this.inventorySwitch,
    this.kingdeePushSwitch,
    this.trainingSwitch,
    this.managementSwitch,
    this.rangeParkSwitch,
    this.idcSwitch
    });

  NoticeDisturbReq.fromJson(Map<String, dynamic> json) {
      ids = json['ids'];
      systemPushSwitch = json['systemPushSwitch'];
      approvalPushSwitch = json['approvalPushSwitch'];
      ticketPushSwitch = json['ticketPushSwitch'];
      inventorySwitch = json['inventorySwitch'];
      kingdeePushSwitch = json['kingdeePushSwitch'];
      trainingSwitch = json['trainingSwitch'];
      managementSwitch = json['managementSwitch'];
      rangeParkSwitch = json['rangeParkSwitch'];
      idcSwitch = json['idcSwitch'];
  }

  Map<String, dynamic> toJson() => {
        'ids': ids,
        'systemPushSwitch': systemPushSwitch,
        'approvalPushSwitch': approvalPushSwitch,
        'ticketPushSwitch': ticketPushSwitch,
        'inventorySwitch': inventorySwitch,
        'kingdeePushSwitch': kingdeePushSwitch,
        'trainingSwitch': trainingSwitch,
        'managementSwitch': managementSwitch,
        'rangeParkSwitch': rangeParkSwitch,
        'idcSwitch': idcSwitch,
      };
}