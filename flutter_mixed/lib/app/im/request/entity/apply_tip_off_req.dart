class TipOffCommitReq {
  int? type = 0; // sessionType
  String? content = '';
  String? targetId = '';
  List<String> ids = [];

  TipOffCommitReq(this.type, this.content, this.targetId, this.ids);

  TipOffCommitReq.fromJson(Map<String, dynamic> json) {
    type = json['type'] ?? 0;
    content = json['content'] ?? '';
    targetId = json['targetId'] ?? '';
    ids = json['ids'] ?? [];
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'content': content,
        'targetId': targetId,
        'ids': ids,
      };
}
