import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_single_chat_resp.dart';

import 'chat_resp_ext.dart';

/// {
//             "type": 999,
//             "sendTime": 1711936089587,
//             "groupId": "2616961291855594493",
//             "senderUserInfo": {
//                 "nickname": "蒋俊尧",
//                 "userId": "2504315490218804221",
//                 "avatar": "https://cdn.ddbes.com/HEAD/ipWVksYJlHQwHdlZGM1604883581849.png",
//                 "imUserId": "20sobj2QEob"
//             },
//             "msgId": "28bYNq89FIb",
//             "noticeMsg": {
//                 "title": "变更通知",
//                 "subtitle": "【A1】-变更通知-变更开始",
//                 "context": "工单消息提醒",
//                 "data": "{\"buttons\":\"[{\\\"resultTitle\\\":\\\"查看变更详情\\\",\\\"buttonId\\\":1,\\\"resultColorType\\\":1}]\",\"msgType\":80000,\"companyLogo\":\"https://cdn.ddbes.com/LOGO/org-9.jpg\",\"companyName\":\"润泽科技发展有限公司\",\"classifyType\":-1,\"content\":\"<body style =\\\"font-size:14px;line-height:20px\\\" color=\\\"#1d2129\\\"><strong>变更工单号：</strong>RZYW-A1-ZNH-***********-2<br /><strong>变更名称：</strong>23<br /><strong>变更机房：</strong>A1<br /><strong>变更等级：</strong><font color = '#E37318'>中风险</font><br /><strong>变更专业：</strong>智能化<br /><strong>变更原因：</strong>23<br /><strong>变更方案：</strong>23<br /><strong>变更影响范围：</strong>23232323<br /><strong>变更告警影响情况：</strong>2323<br /><strong>变更类型：</strong>非计划型变更<br /><strong>变更计划开始时间：</strong>2024-04-08 12:15:18<br /><strong>变更计划结束时间：</strong>2024-04-10 15:10:31<br /><strong>变更实施人：</strong>彭江河、张永顺<br /><strong>变更开始时间：</strong>2024-04-01 09:48:07</body>\",\"noticeTitle\":\"\",\"companyId\":\"805\",\"cmdId\":\"2619261688150493181\",\"noticeName\":\"【A1】-变更通知-变更开始\",\"detail\":\"{\\\"nameBackColor\\\":\\\"#EEFAF2\\\",\\\"link\\\":\\\"http://10.0.1.10:9000/ddbes-work-order-mobile/index.html#/change/ticket-detail/2/2619261688150493181/0\\\",\\\"nameTextColor\\\":\\\"#2BA471\\\",\\\"link-pc\\\":\\\"http://oss.joinu.ltd:9000/ddbes-oa-web/work-order.html#/changeOrder/detail?ticketId=2619261688150493181\\\",\\\"contentHtml\\\":\\\"1\\\"}\",\"functionType\":1000,\"iconUrl\":\"https://cdn.ddbes.com/ICON/%E5%B7%A5%E5%8D%95%E9%80%9A%E7%9F%A560000.png\",\"tempType\":4}",
//                 "ext": ""
//             },
//             "contextCase": 11
//         }

/// 离线群聊 消息
class OfflineGroupChatResp with ChatRespExt {
  int? type;
  int? sendTime;
  String? groupId;
  SenderUserInfo? senderUserInfo;
  UserInfo? userInfo;
  String? msgId;
  ClientNoticeMsg? noticeMsg;
  String? msg;
  int? contextCase;

  OfflineGroupChatResp({this.type, this.sendTime, this.groupId, this.senderUserInfo
    , this.userInfo
    , this.msgId, this.noticeMsg,
    this.msg,
    this.contextCase
  });

  OfflineGroupChatResp.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    sendTime = json['sendTime'];
    groupId = json['groupId'];
    userInfo = json['userInfo'] != null ? UserInfo?.fromJson(json['userInfo']) : null;
    senderUserInfo = json['senderUserInfo'] != null ? SenderUserInfo?.fromJson(json['senderUserInfo']) : null;
    msgId = json['msgId'];
    noticeMsg = json['noticeMsg'] != null ? ClientNoticeMsg?.fromJson(json['noticeMsg']) : null;
    contextCase = json['contextCase'];
    msg = json['msg'];
    ext = json['ext'] != null ? ExtGroupBean.fromJson(json['ext']) : null;
    file = json['file'] != null ? FileGroupBean.fromJson(json['file']) : null;
    image = json['image'] != null ? ImageGroupBean.fromJson(json['image']): null;
    location = json['location'] != null ? LocationGroupBean.fromJson(json['location']) : null;
    video = json['video'] != null ? VideoGroupBean.fromJson(json['video']) : null;
    voice = json['voice'] != null ? VoiceGroupBean.fromJson(json['voice']) : null;
    withdraw = json['withdraw'] != null ? WithDrawBean.fromJson(json['withdraw']) : null;
    call = json['call'] != null ? CallMsgGroupBean.fromJson(json['call']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['type'] = type;
    data['sendTime'] = sendTime;
    data['groupId'] = groupId;
    data['userInfo'] = userInfo?.toJson();
    data['senderUserInfo'] = senderUserInfo?.toJson();
    data['msgId'] = msgId;
    data['noticeMsg'] = noticeMsg?.toJson();
    data['contextCase'] = contextCase;
    data['msg'] = msg;
    data['ext'] =  ext == null ? null : ext!.toJson();
    data['file'] = file == null ? null : file!.toJson();
    data['image'] = image == null ? null : image!.toJson();
    data['location'] =  location == null ? null : location!.toJson();
    data['video'] = video == null ? null : video!.toJson();
    data['voice'] = voice == null ? null : voice!.toJson();
    data['withdraw'] = withdraw == null ? null : withdraw!.toJson();
    data['call'] = call == null ? null : call!.toJson();
    return data;
  }
}


