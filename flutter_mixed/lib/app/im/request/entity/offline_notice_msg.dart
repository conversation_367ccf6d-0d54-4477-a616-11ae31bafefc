import 'dart:convert';
import 'dart:ui';
// import 'dart:core';

import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/im/convert/notice_temp_type.dart';
import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../../click_card/notice_funtion_type.dart';
import '../../constant/ImMsgConstant.dart';
import '../../constant/constant_tcp_util.dart';
import 'notice_detail_data.dart';

/// 离线的各种通知
class OfflineNoticeItemResp {
  ClientNoticeMsg? noticeMsg;
  String? appId;
  String? receiver;
  int? type;
  String? msgId;
  int? msgTime;

  bool hasAvatar() {
    return !StringUtil.isEmpty(noticeMsg?.getDataP()?.iconUrl);
  }

  String? getAvatar() {
    return noticeMsg?.getDataP()?.iconUrl;
  }

  OfflineNoticeItemResp(
      {this.noticeMsg,
      this.appId,
      this.receiver,
      this.type,
      this.msgId,
      this.msgTime});

  OfflineNoticeItemResp.fromJson(Map<String, dynamic> json) {
    noticeMsg = json['noticeMsg'] != null
        ? ClientNoticeMsg?.fromJson(json['noticeMsg'])
        : null;
    appId = json['appId'] ?? '';
    receiver = json['receiver'] ?? '';
    type = json['type'] ?? 1;
    msgId = json['msgId'] ?? '';
    msgTime = json['msgTime'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['noticeMsg'] = noticeMsg!.toJson();
    data['appId'] = appId;
    data['receiver'] = receiver;
    data['type'] = type;
    data['msgId'] = msgId;
    data['msgTime'] = msgTime;
    return data;
  }
}

class ClientNoticeMsg {
  String? title;
  String? subtitle;
  String? context;
  String? data;
  String? ext;
  int? tempStatus; //无值-1，默认0展示按钮，1按钮buttonId，2按钮buttonId

  NoticeData? getDataP() {
    if (StringUtil.isEmpty(data)) return null;
    Map<String, dynamic> map = json.decode(data!);
    return NoticeData.fromJson(map);
  }

  ClientNoticeMsg(
      {this.title,
      this.subtitle,
      this.context,
      this.data,
      this.ext,
      this.tempStatus});

  ClientNoticeMsg.fromJson(Map<String, dynamic> json) {
    title = json['title'] ?? '';
    subtitle = json['subtitle'] ?? '';
    context = json['context'] ?? '';
    data = json['data'] ?? '';
    ext = json['ext'] ?? '';
    tempStatus = json['tempStatus'] ?? -1;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> map = Map<String, dynamic>();
    map['title'] = title;
    map['subtitle'] = subtitle;
    map['context'] = context;
    map['data'] = data;
    map['ext'] = ext;
    map['tempStatus'] = tempStatus;
    return map;
  }
}

/*

// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class NoticeData {
  int? msgType;
  int? classifyType;
  int? tempType;
  String? noticeName;
  String? noticeTitle;
  String? content;
  String? iconUrl;
  String? companyId;
  String? companyLogo;
  String? companyName;
  String? detail;
  int? functionType;
  String? buttons;
  String? cmdId;

  NoticeData(
      {this.msgType,
      this.classifyType,
      this.tempType,
      this.noticeName,
      this.noticeTitle,
      this.content,
      this.iconUrl,
      this.companyId,
      this.companyLogo,
      this.companyName,
      this.detail,
      this.functionType,
      this.buttons,
      this.cmdId});

  List<String> getContent() {
    if (StringUtil.isEmpty(content)) return [];
    return json.decode(content!);
  }

  getContentValue() {
    var contentlist = getContent();
    if (contentlist.isEmpty) return '';
    var s = '';
    contentlist.forEach((element) {
      s += '$element\n';
    });
    return s;
  }

  //未完待续。。。
  getContentWithTempType() {
    //content早期有非json数组数据 为纯字符串情况
    List contentList = [];
    String contentStr = '';

    if (content!.startsWith('[')) {
      //json数组
      contentList = json.decode(content!);
    } else {
      contentStr = content!;
    }
    if (tempType == NoticeTempType.DDNotificationTemplateTypeEight ||
        tempType == NoticeTempType.DDNotificationTemplateTypeSeven) {
      return contentList;
    } else if (tempType == NoticeTempType.DDNotificationTemplateTypeFour) {
      if (contentList.isNotEmpty) {
        contentStr = backStrWithData(contentList);
      }
      return contentStr;
    } else {
      if (contentList.isNotEmpty) {
        contentStr = backStrWithData(contentList);
      }
      return contentStr;
    }
  }

  //字符串拼接情况---老审批数据
  backStrWithData(List contentList) {
    String contentStr = '';
    contentList.removeWhere((element) {
      if (element is String) {
        if (element.isEmpty) {
          return true;
        }
      }
      return false;
    });
    for (var i = 0; i < contentList.length; i++) {
      var element = contentList[i];
      if (element is String) {
        if (i == contentList.length - 1) {
          contentStr += element;
        } else {
          contentStr += '$element\n';
        }
      }
    }
    return contentStr;
  }

  List<NoticeButton> buttonList() {
    if (StringUtil.isEmpty(buttons)) return [];
    if (json.decode(buttons!) is List) {
      List jsons = json.decode(buttons!);
      return jsons.map((e) => NoticeButton.fromJson(e)).toList();
    }
    return [];
  }

  NoticeDataDetail? getDetail() {
    if (StringUtil.isEmpty(detail)) return null;
    if (json.decode(detail!) is Map) {
      Map<String, dynamic> map = json.decode(detail!);
      return NoticeDataDetail.fromJson(map);
    }
    return null;
  }

  NoticeData.fromJson(Map<String, dynamic> json) {
    msgType = json['msgType'] ?? 0;
    classifyType = json['classifyType'] ?? 0;
    tempType = json['tempType'] ?? 0;
    noticeName = json['noticeName'] ?? '';
    noticeTitle = json['noticeTitle'] ?? '';
    content = json['content'] ?? '';
    iconUrl = json['iconUrl'] ?? '';
    companyId = json['companyId'] ?? '';
    companyLogo = json['companyLogo'] ?? '';
    companyName = json['companyName'] ?? '';
    detail = json['detail'] ?? '';
    functionType = json['functionType'] ?? 0;
    buttons = json['buttons'] ?? '';
    cmdId = json['cmdId'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['msgType'] = msgType;
    data['classifyType'] = classifyType;
    data['tempType'] = tempType;
    data['noticeName'] = noticeName;
    data['noticeTitle'] = noticeTitle;
    data['content'] = content;
    data['iconUrl'] = iconUrl;
    data['companyId'] = companyId;
    data['companyLogo'] = companyLogo;
    data['companyName'] = companyName;
    data['detail'] = detail;
    data['functionType'] = functionType;
    data['buttons'] = buttons;
    data['cmdId'] = cmdId;
    return data;
  }

  @override
  String toString() {
    return 'NoticeData{msgType: $msgType, classifyType: $classifyType, tempType: $tempType, noticeName: $noticeName, noticeTitle: $noticeTitle, content: $content, iconUrl: $iconUrl, companyId: $companyId, companyLogo: $companyLogo, companyName: $companyName, detail: $detail, functionType: $functionType, buttons: $buttons, cmdId: $cmdId}';
  }
}

/*
 通知按钮 对应的 对象
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class NoticeButton {
  String? apiUrl;
  String? body;
  int? buttonId;
  int? colorType;
  int? methodType;
  String? name;
  bool? needToSignature;
  int? resultColorType;
  String? resultTitle;

  NoticeButton(
      {this.apiUrl,
      this.body,
      this.buttonId,
      this.colorType,
      this.methodType,
      this.name,
      this.needToSignature,
      this.resultColorType,
      this.resultTitle});

  NoticeButton.fromJson(Map<String, dynamic> json) {
    apiUrl = json['apiUrl'] ?? '';
    body = json['body'] ?? '';
    buttonId = json['buttonId'] ?? 1;
    colorType = json['colorType'] ?? 1;
    methodType = json['methodType'] ?? 1;
    name = json['name'] ?? '';
    needToSignature = json['needToSignature'] ?? false;
    resultColorType = json['resultColorType'] ?? 1;
    resultTitle = json['resultTitle'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['apiUrl'] = apiUrl;
    data['body'] = body;
    data['buttonId'] = buttonId;
    data['colorType'] = colorType;
    data['methodType'] = methodType;
    data['name'] = name;
    data['needToSignature'] = needToSignature;
    data['resultColorType'] = resultColorType;
    data['resultTitle'] = resultTitle;
    return data;
  }
}

class NoticeButtonBody {
  List<NoticeButton?>? myArray;

  NoticeButtonBody({this.myArray});

  NoticeButtonBody.fromJson(Map<String, dynamic> json) {
    if (json['MyArray'] != null) {
      myArray = <NoticeButton>[];
      json['MyArray'].forEach((v) {
        myArray!.add(NoticeButton.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['MyArray'] =
        NoticeButton != null ? myArray!.map((v) => v?.toJson()).toList() : null;
    return data;
  }
}

extension NoticeItemDataExt on OfflineNoticeItemResp {
  bool isUseApproveCard() =>
      noticeMsg?.getDataP()?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeOne ||
      noticeMsg?.getDataP()?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeTwo ||
      noticeMsg?.getDataP()?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeThree ||
      noticeMsg?.getDataP()?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeFour ||
      noticeMsg?.getDataP()?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeFive ||
      noticeMsg?.getDataP()?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeSeven ||
      noticeMsg?.getDataP()?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeEight; //是否使用审批控件1234578

  bool noneButton() {
    if (noticeMsg?.getDataP()?.tempType == -1 ||
        noticeMsg?.getDataP()?.buttonList().isEmpty == true) return true;
    return false;
  }

  backShowButton() {
    int buttonType = -1; //-1无按钮样式0待处理样式1已处理样式2查看详情样式
    List buttonInfos = [];
    List<NoticeButton>? buttonList = noticeMsg?.getDataP()?.buttonList();
    if (buttonList == null) return {'type': buttonType};
    if (noticeMsg?.getDataP()?.buttonList().isNotEmpty == true) {
      if (noticeMsg?.tempStatus == 0) {
        buttonType = 0;
        for (var i = 0; i < buttonList.length; i++) {
          NoticeButton button = buttonList[i];
          buttonInfos.add({
            'text': button.name,
            'color': backColor(button.colorType!),
            'button': button
          });
        }
      } else if (noticeMsg!.tempStatus! > 0) {
        buttonType = 1;
        for (var i = 0; i < buttonList.length; i++) {
          NoticeButton button = buttonList[i];
          if (noticeMsg!.tempStatus == button.buttonId) {
            buttonInfos.add({
              'text': button.resultTitle,
              'color': backColor(button.resultColorType!),
              'button': button
            });
            break;
          }
        }
      } else if (noticeMsg!.getDataP()!.functionType !=
              NoticeFunctonType.DDJumpFunctionTypeDefaultZero &&
          type != ConstantImMsgType.SSChatMessageTypeApprovalWithdrawRemind) {
        buttonType = 2;
        buttonInfos.add({'text': '查看详情', 'color': ColorConfig.mainTextColor});
      }
    } else {
      if (noticeMsg!.getDataP()!.functionType !=
              NoticeFunctonType.DDJumpFunctionTypeDefaultZero &&
          type != ConstantImMsgType.SSChatMessageTypeApprovalWithdrawRemind) {
        buttonType = 2;
        buttonInfos.add({'text': '查看详情', 'color': ColorConfig.mainTextColor});
      }
    }
    return {'type': buttonType, 'info': buttonInfos};
  }

  backColor(int colorType) {
    switch (colorType) {
      case 1:
        return ColorConfig.msgTextColor;
      case 2:
        return ColorConfig.desTextColor;
      case 3:
        return ColorConfig.deleteCorlor;
      case 4:
        return ColorConfig.themeCorlor;
      case 5:
        return ColorConfig.approveGreenColor;
      case 6:
        return ColorConfig.deleteCorlor;
      case 7:
        return ColorConfig.mainTextColor;
      default:
        return ColorConfig.mainTextColor;
    }
  }

  //将OfflineNoticeItemResp转换成Notice
  Notice getNoticeModel(uid) {
    Map<String, dynamic> respMap = toJson();
    Map<String, dynamic> noticeMsgMap = respMap['noticeMsg'];
    respMap.removeWhere((key, value) => key == 'noticeMsg');
    respMap.addAll(noticeMsgMap);
    NoticeData? noticeData = noticeMsg?.getDataP();
    Notice notice = Notice.fromJson(respMap);
    notice.uid = uid;
    notice.sessionId = NoticeTypeManager.noticeTypeMap[type];
    if (type == ConstantImMsgType.WorkMsgSessionType) {
      notice.sessionId = noticeData?.companyId;
    }
    if (type == ConstantImMsgType.TeamMsgSessionType) {
      notice.sessionId = ConstantTcpUtil.TCP_TEAM_SESSIONID;
    }
    notice.cmdId = noticeData!.cmdId;
    notice.msgType = noticeData.msgType;
    notice.companyId = noticeData.companyId;
    notice.classifyType = noticeData.classifyType;
    return notice;
  }
}
