

/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class ImOfflineSessionResp {
  String? sessionId;
  int? sessionType;
  int? msgTime;
  String? msgContent;
  int msgCount = 0;
  String? msgId;
  String? appUid;
  String? nickname;
  String? avatar;
  int? type;//消息类型

  ImOfflineSessionResp({this.sessionId, this.sessionType, this.msgTime, this.msgContent, this.msgCount =0, this.msgId, this.appUid, this.nickname, this.avatar, this.type});

  ImOfflineSessionResp.fromJson(Map<String, dynamic> json) {
    sessionId = json['sessionId'];
    sessionType = json['sessionType'];
    msgTime = json['msgTime'];
    msgContent = json['msgContent'];
    msgCount = json['msgCount'];
    msgId = json['msgId'];
    appUid = json['appUid'];
    nickname = json['nickname'];
    avatar = json['avatar'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['sessionId'] = sessionId;
    data['sessionType'] = sessionType;
    data['msgTime'] = msgTime;
    data['msgContent'] = msgContent;
    data['msgCount'] = msgCount;
    data['msgId'] = msgId;
    data['appUid'] = appUid;
    data['nickname'] = nickname;
    data['avatar'] = avatar;
    data['type'] = type;
    return data;
  }

  @override
  String toString() {
    return 'ImOfflineSessionResp{sessionId: $sessionId, sessionType: $sessionType, msgTime: $msgTime, msgContent: $msgContent, msgCount: $msgCount, msgId: $msgId, appUid: $appUid, nickname: $nickname, avatar: $avatar, msgType: $type}';
  }
}

//会话置顶合并后的model
class ImOfflineSessionV2Resp {
  List<ImOfflineSessionResp?>? conversation;
  List? single;
  List? group;
  List? notice;

  ImOfflineSessionV2Resp({this.conversation, this.single, this.group, this.notice});

  ImOfflineSessionV2Resp.fromJson(Map<String, dynamic> json) {
    if (json['conversation'] != null) {
      conversation = <ImOfflineSessionResp>[];
      json['conversation'].forEach((v) {
        conversation!.add(ImOfflineSessionResp.fromJson(v));
      });
    }
    single = json['single'] ?? [];
    group = json['group'] ?? [];
    notice = json['notice'] ?? [];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['conversation'] = conversation?.map((v) => v?.toJson()).toList();
    data['single'] = single;
    data['group'] = group;
    data['notice'] = notice;
    return data;
  }
}

