

class SixInOneReqBody {

  int client = 0;
  String imToken = ''; // 厂商 推送 id
  String netstat = '';

  SixInOneReqBody(this.client, this.imToken, this.netstat);

  SixInOneReqBody.fromJson(Map<String, dynamic> json) {
    client = json['client'];
    imToken = json['imToken'];
    netstat = json['netstat'];
  }

  Map<String, dynamic> toJson() => {
        'client': client,
        'imToken': imToken,
        'netstat': netstat,
      };
}