/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class ListItem {
  int? timeStart;
  int? type;
  int? count;
  String? tag;

  ListItem({this.timeStart, this.type, this.tag, this.count});

  ListItem.fromJson(Map<String, dynamic> json) {
    timeStart = json['timeStart'];
    type = json['type'];
    tag = json['tag'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['timeStart'] = timeStart;
    data['type'] = type;
    data['tag'] = tag;
    data['count'] = count;
    return data;
  }
}

class OfflineNoticeSessionReqBody {
  List<int>? disassembleTag;
  List<ListItem> list = [];

  OfflineNoticeSessionReqBody({this.disassembleTag, required this.list});

  OfflineNoticeSessionReqBody.fromJson(Map<String, dynamic> json) {
    if (json['disassembleTag'] != null) {
      disassembleTag = [];
      json['disassembleTag'].forEach((v) {
        disassembleTag!.add(v);
      });
    }
    if (json['list'] != null) {
      list = <ListItem>[];
      json['list'].forEach((v) {
        list.add(ListItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['disassembleTag'] =
        disassembleTag != null ? disassembleTag!.map((v) => v).toList() : null;
    data['list'] = list != null ? list!.map((v) => v.toJson()).toList() : null;
    return data;
  }
}
