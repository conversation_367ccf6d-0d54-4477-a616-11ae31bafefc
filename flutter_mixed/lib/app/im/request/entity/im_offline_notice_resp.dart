/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class ImOfflineNoticeResp {
  int? type;
  String? tag;
  int? count;
  String? data;
  String? msgContent;
  int? msgTime;

  ImOfflineNoticeResp({this.type, this.tag, this.count, this.data, this.msgContent, this.msgTime});

  ImOfflineNoticeResp.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    tag = json['tag'];
    count = json['count'];
    data = json['data'];
    msgContent = json['msgContent'];
    msgTime = json['msgTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> map = Map<String, dynamic>();
    map['type'] = type;
    map['tag'] = tag;
    map['count'] = count;
    map['data'] = data;
    map['msgContent'] = msgContent;
    map['msgTime'] = msgTime;
    return map;
  }
}

