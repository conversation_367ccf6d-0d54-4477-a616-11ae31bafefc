

class ImTokenResp {

  String appId="";
  String appUid="";
  String avatar="";
  String id="";
  String mobile="";
  String nickname="";
  String token="";

  ImTokenResp(
    this.appId,
    this.appUid,
    this.avatar,
    this.id,
    this.mobile,
    this.nickname,
    this.token,
  );

  ImTokenResp.fromJson(Map<String, dynamic> json) {
      appId = json['appId'];
      appUid = json['appUid'];
      avatar = json['avatar'];
      id = json['id'];
      mobile =  json['mobile'];
      nickname = json['nickname'];
      token = json['token'];
  }

  Map<String, dynamic> toJson() => {
        'appId': appId,
        'appUid': appUid,
        'avatar': avatar,
        'id': id,
        'mobile': mobile,
        'nickname': nickname,
        'token': token,
      };
}