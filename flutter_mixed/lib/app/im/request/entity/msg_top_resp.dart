/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class MsgTopResp {
  List<String>? single = [];
  List<String>? group = [];
  List<String>? notice = [];

  MsgTopResp({this.single, this.group , this.notice});

  MsgTopResp.fromJson(Map<String, dynamic> json) {
    if (json['single'] != null) {
      single = <String>[];
      json['single'].forEach((v) {
        single?.add(v);
      });
    }
    if (json['group'] != null) {
      group = <String>[];
      json['group'].forEach((v) {
        group?.add(v);
      });
    }

    if (json['notice'] != null) {
      notice = <String>[];
      json['notice'].forEach((v) {
        notice?.add(v);
      });
    }

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['single'] = single != null ? single!.map((v) => v).toList() : null;
    data['group'] = group != null ? group!.map((v) => v).toList() : null;
    data['notice'] = notice != null ? notice!.map((v) => v).toList() : null;
    return data;
  }
}

class MsgTopReq {
  int type = 0;
  String id = '';

  MsgTopReq(this.type, this.id);

  MsgTopReq.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'id': id,
      };
}
