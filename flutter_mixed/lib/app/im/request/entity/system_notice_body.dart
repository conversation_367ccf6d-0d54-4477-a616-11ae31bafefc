class SystemNoticeDataBean {
  int? classifyType = 0;
  String? companyId = "";
  String? companyLogo = "";
  String? companyName = "";
  String? cmdId = "";
  String? content = "";
  String? detail = "";
  String? iconUrl = "";
  int? msgType = 0;
  String? noticeName = "";
  String? noticeTitle = "";
  int? tempType = 0;
  String? buttons = "";
  String? resultTitle = "";
  int? resultColorType = 0;
  int? functionType = 0;

  SystemNoticeDataBean.fromJson(Map<String, dynamic> json) {
      classifyType = json['classifyType'];
      companyId = json['companyId'];
      companyLogo = json['companyLogo'];
      companyName = json['companyName'];
      cmdId = json['cmdId'];
      content = json['content'];
      detail = json['detail'];
      iconUrl = json['iconUrl'];
      msgType = json['msgType'];
      noticeName = json['noticeName'];
      noticeTitle = json['noticeTitle'];
      tempType = json['tempType'];
      buttons = json['buttons'];
      resultTitle = json['resultTitle'];
      resultColorType = json['resultColorType'];
      functionType = json['functionType'];
  }

  Map<String, dynamic> toJson() => {
        'classifyType': classifyType,
        'companyId': companyId,
        'companyLogo': companyLogo,
        'companyName': companyName,
        'cmdId': cmdId,
        'content': content,
        'detail': detail,
        'iconUrl': iconUrl,
        'msgType': msgType,
        'noticeName': noticeName,
        'noticeTitle': noticeTitle,
        'tempType': tempType,
        'buttons': buttons,
        'resultTitle': resultTitle,
        'resultColorType': resultColorType,
        'functionType': functionType,
      };
}
