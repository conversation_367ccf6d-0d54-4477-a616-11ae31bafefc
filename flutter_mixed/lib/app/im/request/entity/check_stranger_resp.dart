/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class CheckStrangerResp {
  int? strangerSwitch;   // 0 未开启陌生人设置； 1 已开启了陌生人设置
  int? logout;  // 1 对方已注销

  CheckStrangerResp({this.strangerSwitch, this.logout});

  CheckStrangerResp.fromJson(Map<String, dynamic> json) {
    strangerSwitch = json['strangerSwitch'];
    logout = json['logout'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['strangerSwitch'] = strangerSwitch;
    data['logout'] = logout;
    return data;
  }
}

