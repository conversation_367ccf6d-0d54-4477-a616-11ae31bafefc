import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/im/constant/im_cache_global.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/retrofit/entity/base_resp.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../main.dart';
import '../request/datasource/disturb_datasource.dart';
import '../request/datasource/offline_datasource.dart';

/// 初始化 ------
/// 离线会话
/// 六合一接口
class ImInitializer {

  static ImInitializer? _instance;

  ImInitializer._internal();

  static ImInitializer get instance => _instance ??= ImInitializer._internal();


  Future init() async {
    await fetchInitApis();
  }

  // 六合一接口， 获取单/群聊 ， 各种工单等 免打扰信息 , 重置session
  Future fetchInitApis() async {
    var datasource = DisturbDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);

    var resp = await Future.wait<BaseResp>([
      datasource.getSingleDisturbState()
      , datasource.getGroupDisturbState()
    ]);
    var singleResp = resp[0];
    var groupResp = resp[1];

    List<String> disturbs = [];

    if(singleResp.success() && singleResp.data is List<String>){
      disturbs.addAll(singleResp.data);
    }
    if(groupResp.success() && groupResp.data is List<String>) {
      disturbs.addAll(groupResp.data);
    }

    await ImCacheData.instance.updateSingleAndGroupChatDisturbList(disturbs);

  }

}