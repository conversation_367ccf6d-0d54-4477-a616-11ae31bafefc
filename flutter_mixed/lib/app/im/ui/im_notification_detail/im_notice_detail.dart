import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/ui/im_notification_detail/im_notice_detail_controller.dart';
import 'package:get/get.dart';

import '../../../common/config/config.dart';

class ImNotificationDetail extends StatelessWidget{
  const ImNotificationDetail({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ImNotificationDetailController>(builder: (controller){
      return ToolBar(
        title:'通知详情' ,
        body: Column(
          children: [
            SizedBox(height: 10,),
            Container(
              padding: EdgeInsets.only(left: 12,top: 20, right: 15,bottom: 20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: ColorConfig.lineColor,
                    width: 1
                  )
                )
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(1),
                    width: 58,
                    height: 58,
                    alignment: Alignment.centerLeft,
                    decoration: BoxDecoration(
                        image: DecorationImage(
                            image: SettingWidget.backImageProvider(controller.logoStr)),
                        borderRadius: BorderRadius.circular(29),
                        border: Border.all(
                            color: ColorConfig.lineColor,
                            width: 1)),
                  ),
                  SizedBox(width: 10,),
                  Expanded(child: Text(controller.descStr,
                    style: const TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  )),
                ],
              ),
            ),
            controller.dealName.isNotEmpty ?
            Container(
              padding: EdgeInsets.only(left: 20,top: 15, right: 20,bottom: 15),
              color: Colors.white,
              child: Row(
                children: [
                  Text(
                    "处理人",
                    style: const TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  ),
                  SizedBox(width: 10,),
                  ClipOval(
                    child: Image(
                      image: SettingWidget.backImageProvider(controller.dealLogoStr),
                      width: 25,
                    ),
                  ),
                  SizedBox(width: 8,),
                  Text(
                    controller.dealName,
                    style: const TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  ),
                ],
              ),
            ) : Container(),
            Container(
              padding: EdgeInsets.only(top: 48),
              child: Text(
                controller.timeStr,
                style: const TextStyle(
                    color: ColorConfig.mainTextColor, fontSize: 14),
              ),
            )
          ],
        ),
      );

    });
  }

}