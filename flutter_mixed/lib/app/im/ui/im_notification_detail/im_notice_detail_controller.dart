import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

class ImNotificationDetailController extends GetxController{
  String logoStr = '';
  String dealLogoStr = '';
  String dealName = '';
  String descStr = '';
  String timeStr = '';
    @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      logoStr = Get.arguments['logoStr'];
      dealLogoStr = Get.arguments['dealLogoStr'];
      dealName = Get.arguments['dealName'];
      descStr = Get.arguments['dataContent'];
      timeStr = Get.arguments['timeStr'];
      logger("==========logoStr $logoStr===========dealLogoStr $dealLogoStr====descStr $descStr====timeStr $timeStr==");
    }
  }
}