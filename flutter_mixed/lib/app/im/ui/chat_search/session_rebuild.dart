
import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';

class SessionRebuild extends Session {
  List<Message>? messages; // 搜索时该sessionId下的消息

  // Session(this.sessionId);

  SessionRebuild({required super.sessionId,
    super.uid,
    super.sessionTop,
    super.sessionHidden,
    super.msgType,
    super.sessionType,
    super.extend,
    super.name,
    super.chatStatus,
    super.cmdId,
    super.headerUrl,
    super.msgId,
    super.msgContent,
    super.notReadCount,
    super.msgTime,
    super.noDisturb,
    super.appChatId,
    this.messages,});

  @override
  String toString() {
    return 'SessionRebuild{sessionId: $sessionId, uid: $uid, sessionTop: $sessionTop, sessionHidden: $sessionHidden, msgType: $msgType, sessionType: $sessionType, extend: $extend, name: $name, chatStatus: $chatStatus, cmdId: $cmdId, headerUrl: $headerUrl, msgId: $msgId, msgContent: $msgContent, notReadCount: $notReadCount, msgTime: $msgTime, noDisturb: $noDisturb, appChatId: $appChatId, messages: $messages}';
  }
}