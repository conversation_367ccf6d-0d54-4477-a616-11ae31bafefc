import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/Session_rebuild.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/search_chat_controller.dart';
import 'package:flutter_mixed/app/im/utils/im_global_util.dart';
import 'package:flutter_mixed/app/modules/contact/model/friend/user_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import "package:collection/collection.dart";

import '../../chat/chat_binding.dart';
import '../../chat/chat_page.dart';
  
  class ChatSearchListPageController extends GetxController {
  RxList<Message> searchCharList = <Message>[].obs;
  RxString keyword = ''.obs;
  RxInt listType = 0.obs;
  RxString ownerId = ''.obs;

  RxList searchCharListRebuild = [].obs;
  RxList searchFriendList = [].obs;
    
  SearchChatController? searchChatController;

  @override
  void onInit() {
    super.onInit();
  }

  void updateKeyword(String newKeyword) {
    keyword.value = newKeyword;
    // 在这里可以触发其他更新方法
    _performUpdate();
  }

  void updateListType(int newListType) {
    listType.value = newListType;
    // 在这里可以触发其他更新方法
    _performUpdate();
  }

  void _performUpdate() {
    // 这里是更新逻辑
    logger('Keyword: $keyword, ListType: $listType');
    getListData();
  }

  onItemTap(dynamic item) async {
    logger("处理点击事件: $item");
    if (listType.value == 1) {
      RouteHelper.route(Routes.USER_INFO, arguments: {'userId': item.userId, 'type': 1});
    } else {
      if(item is SessionRebuild){
        if(item.messages?.isEmpty == true){
          var ownId = await UserHelper.getUid();
          var session = await DbHelper.getSessionByOwnerId2SessionId(ownId, item.sessionId); // 数据库
          await session?.reShowSession();
          RouteHelper.routeTotag(ChatPage(tag: session?.sessionId),Routes.IM_CHAGE_PAGE,arguments: session,binding: ChatBinding(tag: session?.sessionId));
        }else {
          Get.toNamed(Routes.SEARCH_CHAT_POINT_PERSION, arguments: item);
        }
      }else {
        Get.toNamed(Routes.SEARCH_CHAT_POINT_PERSION, arguments: item);
      }
    }
  }

  getListData() async {
    if (keyword.value.isEmpty) {
      return;
    }
    await loadUserId();
    List<Message> localMessageList = [];
    switch (listType.value) {
      case 0:
        // 消息
        localMessageList = await DbHelper.queryChatMessage(ownerId.value, "%${keyword}%");
        logger("localMessageList: $localMessageList");
        final result = await reorganizeMessages(localMessageList);
        searchCharListRebuild.value = result;
        update();
        break;
      case 1:
        // 好友
        await _getLocalData(keyword);
        break;
      case 2:
        // 群聊
        // localMessageList = await DbHelper.queryChatGroupMessage(ownerId.value, "%${keyword}%");
        var sessionList = await DbHelper.queryGroupChatList(ownerId.value, keyword.value ?? '');
        var gropChatRList = await reorganizeMessagesBySession(sessionList);
        // final result = await reorganizeMessages(localMessageList);

        logger("localMessageList: $localMessageList");

        var resultList = gropChatRList;
        searchCharListRebuild.value = resultList;
        update();
        break;
      default:
        logger("other");
    }
  }

  _getLocalData(text) async {
    // searchFriendList.clear();

    List<MemberModel> localFriendList = [];
    List friendList = await UserDefault.getData(Define.FRIENDLIST);
    logger("model text: ${text}");
    if (friendList.isNotEmpty) {
      for (var j = 0; j < friendList.length; j++) {
        UserModel model = UserModel.fromJson(friendList[j]);
        if (model.name.contains(text)) {
          MemberModel memberModel = MemberModel(model.userId);
          memberModel.headimg = model.avatar;
          memberModel.name = model.name;
          localFriendList.add(memberModel);
        }
      }
      searchFriendList.value = localFriendList; // 将 localFriendList 赋值
      logger("friendList: $searchFriendList");
      update();
    }
  }

  Future<List<SessionRebuild>> reorganizeMessages(List<Message> messages) async {
    final groupeds = groupBy(messages, (Message m) => m.sessionId.toString());
    final resultFutures  = groupeds.entries.map((entry) async {
      final sessionId = entry.key;
      var sessionItem = await DbHelper.getSessionByOwnerId2SessionId(ownerId.value, sessionId.toString());

      final msgs = entry.value;
      return SessionRebuild(
        sessionId: sessionId,
        headerUrl: sessionItem?.headerUrl ?? "",
        name: sessionItem?.name ?? "",
        messages: msgs,
      );
    }).toList();

    final result = await Future.wait(resultFutures);
    return result;
  }

  Future<List<SessionRebuild>> reorganizeMessagesBySession(List<Session> groupSessionList) async {
    var list = <SessionRebuild>[];
    groupSessionList.forEach((session){
      var sr = SessionRebuild(
        sessionId: session.sessionId,
        headerUrl: session?.headerUrl ?? "",
        name: session?.name ?? "",
        messages: [],
      );

      list.add(sr);
    });
    return list;
  }

  Future loadUserId() async {
    ownerId.value = await ImGlobalUtil.currentUserId();
  }

  String emptyTitle() {
    switch(listType.value) {
      case  0:
        return '相关消息';
      case 1:
        return '好友信息';
      case 2:
        return '群聊信息';
      default:
        return '相关信息';
    }
  }

  int getListLength () => listType.value == 1 ? searchFriendList.length : searchCharListRebuild.length;

  isShowText (String? text) => !(text != null && text.isNotEmpty);
}