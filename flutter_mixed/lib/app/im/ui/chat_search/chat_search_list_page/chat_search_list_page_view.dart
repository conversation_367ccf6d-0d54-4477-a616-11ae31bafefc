import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/page_status.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/Session_rebuild.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/chat_search_list_page/chat_search_list_page_controller.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';
import 'package:flutter/cupertino.dart';

import '../../../../modules/place_holder/common_empty.dart';

class ChatSearchListPageView extends StatefulWidget {
  final int listType;
  final String keyword;

  const ChatSearchListPageView({
    Key? key,
    required this.listType,
    required this.keyword,
  }) : super(key: key);


  @override
  State<ChatSearchListPageView> createState() => _ChatSearchListPageState();
}

class _ChatSearchListPageState extends State<ChatSearchListPageView> with PageLoadWidget {
  ChatSearchListPageController? controllerData;

  @override
  void initState() {
    super.initState();
    controllerData = ChatSearchListPageController();
    controllerData?.updateKeyword(widget.keyword);
    controllerData?.updateListType(widget.listType);
  }

  @override
  void didUpdateWidget(covariant ChatSearchListPageView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.keyword != widget.keyword) {
      controllerData?.updateKeyword(widget.keyword);
    }
    if (oldWidget.listType != widget.listType) {
      controllerData?.updateListType(widget.listType);
    }
  }
  
  @override
  Widget build(BuildContext context, ) {
    return GetBuilder<ChatSearchListPageController>(
      init: controllerData,
      global: false,
      builder: (controller) {
      return Scaffold(
        backgroundColor: ColorConfig.whiteColor,
        body: controller.getListLength() == 0 ? Container(
          alignment: Alignment.center,
          child: CommonEmpty('没有${controller.emptyTitle()}'),
        ) : ListView.builder(
            padding: const EdgeInsets.only(top: 10),
            itemCount: controller.getListLength(),
            addAutomaticKeepAlives: false,
            addRepaintBoundaries: false,
            itemBuilder: (context, index) {
              if (controller.listType.value == 1 && index < (controller.searchFriendList?.length ?? 0)) {
                final item = controller.searchFriendList[index];
                return InkWell(
                    onTap: () => controller.onItemTap(item),
                    child:  _buildItem(controller,item.name, item.headimg)
                );

              } else if (index < (controller.searchCharListRebuild?.length ?? 0 )) {
                SessionRebuild item = controller.searchCharListRebuild[index];
                var itemContent = (item.messages?.length ?? 0) == 0 ? '':
                "共${item.messages?.length ?? 0}条消息相关的聊天记录";
                return InkWell(
                    onTap: () => controller.onItemTap(item),
                    child: _buildItem(controller, item.name, item.headerUrl , itemContent)
                );

              } else {
                return Container();
              }
            }
        )
      );
    });
  }

  Widget _buildItem(controller, String? itemName, String? itemHeader, [String? itemText = ""]) {
    return Container(
    padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            ImageLoader(
              url: itemHeader,
              width: 44,
              height: 44,
              radius: 8,
            ),
            12.gap,
            Expanded(child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(child: Text(
                        "$itemName",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(fontSize: 16, color: Color(0xe6222222) ),
                      ),)
                    ],
                  ),
                ),

                if(!StringUtil.isEmpty(itemText))...[
                  Text(
                    "$itemText", // 根据需求可以添加更多内容
                    style: TextStyle(fontSize: 12, color: Color(0x66222222)),
                    overflow: TextOverflow.ellipsis, // 超出部分用省略号代替
                    maxLines: 1, // 最多显示一行
                  )
                ]
              ],
            ),)

          ],
        )
      ],
    ),
  );
  }
}