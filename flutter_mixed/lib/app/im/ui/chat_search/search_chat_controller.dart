import 'package:flutter/material.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:flutter/cupertino.dart';

  class SearchChatController extends GetxController {


  TextEditingController searchJobEditingController = TextEditingController();
  FocusNode searchJobFocusNode = FocusNode();

  RxString keyword = ''.obs;
  RxInt currentIndex = 0.obs;

  TabController? tabController;
  
  

  @override
  void onInit() {
    super.onInit();
  }

  search(String key) async {
    keyword.value = key;
    update();
    logger("输入框内容：$keyword");
  }
  clearInput() {
    keyword.value = '';
    searchJobEditingController.clear();
  }

  //处理按钮逻辑
  settingRightButton(int listType) {
    logger("listType: $listType");
    // currentIndex.value = listType;
  }
}