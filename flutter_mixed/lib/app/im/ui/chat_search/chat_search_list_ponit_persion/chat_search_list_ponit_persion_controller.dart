import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/Session_rebuild.dart';
import 'package:flutter_mixed/app/im/utils/im_global_util.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

class ChatSearchListPointPersionController extends GetxController {
  SessionRebuild? obj;
  RxList<Message> itemList = <Message>[].obs;
  RxString sendName = ''.obs;

  @override
  void onInit() {
    super.onInit();
    obj = Get.arguments as SessionRebuild;
    
    if (obj != null) {
      itemList.value = obj!.messages ?? [];
      sendName.value = obj!.name ?? "";
    }
    logger("ChatSearchListPointPersionController onInit: $obj");
  }

  void onItemTap(Message item, int index) async {
    var userId = await ImGlobalUtil.currentUserId();
    var sessionId = item.sessionId;
    var session = await DbHelper.getSessionByOwnerId2SessionId(userId, sessionId!); // 数据库
    await session?.reShowSession();
    if (session == null) return;
    RouteHelper.routeTotag(ChatPage(tag: session.sessionId),Routes.IM_CHAGE_PAGE,arguments: {
      "session": session,
      "message": item,
      },binding: ChatBinding(tag: session.sessionId));
  }

  isShowText (String? text) => !(text != null && text.isNotEmpty);
}