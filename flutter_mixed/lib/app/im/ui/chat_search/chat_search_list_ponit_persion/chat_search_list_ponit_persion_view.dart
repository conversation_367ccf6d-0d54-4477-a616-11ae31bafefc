

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/chat_search_list_ponit_persion/chat_search_list_ponit_persion_controller.dart';
import 'package:flutter_mixed/app/im/widget/some_widget.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

class ChatSearchListPointPersionView extends StatelessWidget {
   final ChatSearchListPointPersionController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return ToolBar(
      title: controller.sendName.value ??'',
      body: Container(
        child: Column(
          children: [
            10.gap,
            Expanded(
              child: ListView.builder(
                  itemCount: controller.itemList.length,
                  addAutomaticKeepAlives: false,
                  addRepaintBoundaries: false,
                  itemBuilder: (context, index) {
                    final item = controller.itemList[index];
                    logger("当前项$item");
                    return InkWell(
                        onTap: () => controller.onItemTap(item, index),
                        child:  _buildItem(item.sendName, item.sendHeader, item.text)
                    );
                  }
              ),
            )
          ],
        ),
      ) ,
    );
  }

  Widget _buildItem(String? itemName, String? itemHeader, String? itemText) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal:16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          6.gap,
          Row(
            children: [
              ImageLoader(
                url: itemHeader,
                width: 44,
                height: 44,
                radius: 8,
              ),
              12.gap,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "$itemName",
                      style: TextStyle(fontSize: 16, color: Color(0xe6222222)),
                    ),
                    Offstage(
                      offstage: controller.isShowText(itemText),
                      child: Text(
                        "$itemText", // 根据需求可以添加更多内容
                        style: TextStyle(fontSize: 12, color: Color(0x66222222)),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          5.gap,
          line,
        ],
      ),
    );
  }

}