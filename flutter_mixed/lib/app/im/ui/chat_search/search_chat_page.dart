import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/keep_alive_wrapper.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/chat_search_list_page/chat_search_list_page_view.dart';
import 'package:flutter_mixed/app/modules/group_member/icon_search_input_field.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:flutter_mixed/app/im/ui/chat_search/search_chat_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

class TabInfo {
  final int type;
  final String text;

  TabInfo({required this.type, required this.text});
}
class SearchChatPage extends GetView<SearchChatController> {
  SearchChatPage({Key? key,})
      : super(key: key);

  late TabController _tabController;
  final List<TabInfo> _tabs = <TabInfo>[
    TabInfo(type: 0, text: "消息"),
    TabInfo(type: 1, text: "好友"),
    TabInfo(type: 2, text: "群聊"),
  ];
  int currentIndex = 0;

  SystemUiOverlayStyle overlayStyle = const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
  );

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SearchChatController>(builder: (controller) {
      return AnnotatedRegion<SystemUiOverlayStyle>(
        value: overlayStyle,
        child: Container(
          color: ColorConfig.whiteColor,
          child: Column(
              children: [
                (DeviceUtils().top.value).gap,
                _buildSearchBar(context, controller),
                Expanded(
                    child: DefaultTabController(
                        initialIndex: currentIndex,
                        length: _tabs.length,
                        child: Builder(builder: (context) {
                          _tabController = DefaultTabController.of(context);
                          _tabController.addListener(() {
                            currentIndex = _tabController.index;
                            controller.update();
                          });
                          return Column(
                            children: [
                              Container(
                                color: ColorConfig.whiteColor,
                                height: 48,
                                child: TabBar(
                                  onTap: (value) {},
                                  indicatorColor: ColorConfig.themeCorlor,
                                  indicatorWeight: 1,
                                  tabs: _backTabs(),
                                  dividerHeight: 0,
                                ),
                              ),
                              Expanded(
                                  child: SizedBox(
                                      width: double.infinity,
                                      child: TabBarView(
                                        controller: _tabController,
                                        children: _tabs.map((tab) {
                                          return KeepAliveWrapper(
                                            child: ChatSearchListPageView(keyword: controller.keyword.value, listType: tab.type),
                                          );
                                        }).toList(),
                                      )
                                  )
                              ),
                            ],
                          );
                        })
                    )
                )
              ],
            
        ),
        )
      );
    }
    );
  }

  _backTabs(){
    logger('====sile');
    List<Widget> lists = [];
    for (var i = 0; i < _tabs.length; i++) {
      TabInfo info = _tabs[i];
      lists.add(
         Text(info.text,style: TextStyle(fontSize: 14,color: currentIndex==i?ColorConfig.themeCorlor:ColorConfig.mainTextColor),),
      );
    }
    return lists;
  }

  _buildSearchBar(BuildContext context, SearchChatController controller) {
    return Row(
      children: [
        Expanded(
          child: Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: SearchInputField(
              hintText: '请输入搜索内容',
              focusNode: controller.searchJobFocusNode,
              onChanged: (e) {
                controller.search(e);
              },
              textFieldContainer: controller.searchJobEditingController,
              clearInput: () {
                controller.clearInput();
              },
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            controller.clearInput();
            Navigator.pop(context);
          },
          child: Text(
            '取消',
            style: TextStyle(color: ColorConfig.themeCorlor),
          ),
        ),
      ],
    );
  }


}
