import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/click_card/click_card_jump.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/ext/im_time_ext.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/utils/file_util.dart';
import 'package:flutter_mixed/app/retrofit/entity/group/group_info.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../utils/string-util.dart';
import '../../../db/db_helper.dart';
import '../../../request/entity/chat_resp_ext.dart';
import '../../../request/entity/group_info_resp.dart';
import '../common/picture_manager.dart';


enum MessageSendStatus {
  success(1),
  sending(2),
  error(0);

  final int status;

  const MessageSendStatus(this.status);

  static MessageSendStatus getSendStatus(int type) {
    switch (type) {
      case 1:
        return MessageSendStatus.success;
      case 0:
        return MessageSendStatus.error;
      default:
        return MessageSendStatus.sending;
    }
  }
}

abstract class MessageItem {
  bool showImmediateTime = false;
  bool selected = false;
}


abstract class MessageItemData implements MessageItem {
  //信息source
  late final Message message;

  String messageId() => message.msgId;

  //姓名
  String? author;

  //头像
  String? avatar;
  String? body;

  bool isRecord = false;

  @override
  bool showImmediateTime = false;

  @override
  bool selected = false;

  bool isMyMessage = false;
  MessageSendStatus? sendStatus;
  String? dateCreated;

  String sendTime() => (message.sendTime ?? 0).convertDate();

  bool? _showSendName;

  bool get showSenderName{
    if(_showSendName != null) return _showSendName!;
    if(message.sessionType == 1) return false;
    if(message.sessionType == 2 && isMyMessage) return false;
    return true;
  }

  set showSenderName(bool value) {
    _showSendName = value;
  }

  bool get showReadStatus{
    if(message.sessionType == 2) return false;
    if(message.sessionType == 1 && !isMyMessage) return false;
    if (this is MessageAudioAndVideoCallData) return false;
    return true;
  }

  MessageItemData(this.message, this.author, this.avatar, this.body, this.isMyMessage,
      this.sendStatus, this.dateCreated);

  @override
  bool operator ==(Object other) =>
      (this is! MessageImageData) && (identical(this, other) ||
          other is MessageItemData && runtimeType == other.runtimeType && message == other.message);

  @override
  int get hashCode => message.hashCode;
}

class MessageTime implements MessageItem {
  String displayDate;

  MessageTime({required this.displayDate , required this.showImmediateTime
  , required this.selected
  });

  @override
  bool showImmediateTime;

  @override
  bool selected;

}


/// 策略模式扩展 ： 具体使用的消息类型，文本、图片等 ------- start
class MessageSysData extends MessageItemData {
  MessageSysData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageOfflineFault extends MessageItemData {
  MessageOfflineFault(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageTextData extends MessageItemData {

  List<GroupUser>? groupMembers = [];

  MessageTextData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageImageData extends MessageItemData {
  MessageImageData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);

  double? imgWidth;
  double? imgHeight;
  String? fileId;
  String? localUrl;
  double? progress = 0;

  // 仅发送时候使用
  List<String> imagePaths = [];
  bool original = false;
  // 仅发送时候使用
}

class TempLocationData {
  double? longitude;
  double? latitude;
  String? title;
  String? address;
  String? addressImgUrl;

  TempLocationData({this.longitude, this.latitude, this.title,
    this.address, this.addressImgUrl});

  factory TempLocationData.fromJson(Map<String, dynamic> json) {
    return TempLocationData(
      longitude: json['longitude'],
      latitude: json['latitude'],
      title: json['addressTitle'],
      address: json['addressDetail'],
      addressImgUrl: json['addressImgUrl'],
    );
  }

  Map<String, dynamic> toJson() => {
        'longitude': longitude,
        'latitude': latitude,
        'addressTitle': title,
        'addressDetail': address,
        'addressImgUrl': addressImgUrl,
      };
}

class MessageLocationData extends MessageItemData {
  double? longitude;
  double? latitude;
  String? addressTitle;
  String? addressDetail;
  String? addressImgUrl;

  TempLocationData? sendLocation;

  MessageLocationData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);

  @override
  String toString() {
    return 'MessageLocationData{longitude: $longitude, latitude: $latitude, addressTitle: $addressTitle, addressDetail: $addressDetail, addressImgUrl: $addressImgUrl}';
  }
}

// 文件类型
class MessageFileData extends MessageItemData {
  String? fileId;
  String? fileName;
  String? size;
  String? fileType;
  String? fileTypeIcon;
  String? localUrl;
  double? progress;

  String path = '';

  MessageFileData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageVideoData extends MessageItemData {
  String? fileName;
  String? videoImageId;
  double? coverWidth;
  double? coverHeight;
  String? fileId;
  String? cover;
  String? localUrl;
  double? progress;

  List<String> videoPaths = [];

  MessageVideoData(super.message ,super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageVoiceData extends MessageItemData {
  String? fileId; // voiceId
  String? localUrl;
  int? duration;

  String? path;
  Duration? sendDuration;

  MessageVoiceData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageAtSingleMemberData extends MessageItemData {
  MessageAtSingleMemberData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageAtGroupMemberData extends MessageItemData {
  MessageAtGroupMemberData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageUndoData extends MessageItemData {
  MessageUndoData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageGifData extends MessageItemData {
  MessageGifData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

class MessageTimeData extends MessageItemData {
  MessageTimeData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);

}

class MessageReplyData extends MessageItemData {
  MessageReplyData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

//音视频
class MessageAudioAndVideoCallData extends MessageItemData {
  MessageAudioAndVideoCallData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}


class MessageInBlackData extends MessageItemData {
  MessageInBlackData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}

// 邀请加入团队
class MessageInviteInTeamData extends MessageItemData {
  String? companyId;
  String? logo;
  String? companyName;
  String? inviteContent = '邀请成员加入企业';

  MessageInviteInTeamData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);
}


// 回复/引用类型，
class MessageReplyUiData extends MessageItemData{

  Message? quoteMsg;

  bool unFold = false;

  MessageReplyUiData(super.message , super.author, super.avatar, super.body, super.isMyMessage,
      super.sendStatus, super.dateCreated);

  // 暂时只考虑简单的body， 其他类型需要重新定义
  String get content {
    String showQuoteContent = message.alias();
    return '$author: $showQuoteContent';
  }
}

// 引用类型子类型的包装
class InnerMessageData {
  String? msgId;
  String? sendName;
  String? txt;
  Message? message;
}

// （转发的）聊天记录类型
class MessageRecordUiData extends MessageItemData{

  MsgUIRecord? uiRecord;

  MessageRecordUiData(super.message, super.author, super.avatar, super.body,
      super.isMyMessage, super.sendStatus, super.dateCreated);
}

// 机器人消息
class MessageBotUIData extends MessageItemData{

  MsgBotUIData? botUIData;

  void parseAndOpenLink() {
    if(!StringUtil.isEmpty(message.extendOne)){
      var data = message.extendOne!;
      var noticeMsg = ClientNoticeMsg(data: data);
      var notice = OfflineNoticeItemResp(noticeMsg: noticeMsg);
      ClickCardJump.clickCard(notice);
    }
  }

  MessageBotUIData(super.message, super.author, super.avatar, super.body, super.isMyMessage, super.sendStatus, super.dateCreated);
}


extension MessageBotUIDataExtension on MessageBotUIData {

  bool canCopy() {
    return botUIData?.canCopy ?? false;
  }

  // 针对复制机器人消息的时候
  String copy() {
    if(botUIData == null) return "";
    var copyText = "";
    copyText += '${botUIData?.noticeTitle ?? ''}\n';
    botUIData?.oldItems.forEach((e) {
      copyText += '${e.title ?? ''}${e.content ?? ''}\n';
    });
    return copyText;
  }

}

extension MessageIitemExt on double?{

  bool showProgressBar() {
    if(this == null) return false;
    if(this == 0 || this == 1) return false;
    return true;
  }

}


extension MessageQuoteDataExt on MessageReplyUiData {

  // 更新图片引用类型的url
  updateDBImage(String localPath) async {
    quoteMsg?.localUrl = localPath;
    message.extendOne = json.encode(quoteMsg);
    DbHelper.insertMsg(message);
  }

  // 更新引用类型  视频的cover url
  updateVideoCover(String cover) async {
    quoteMsg?.videoImagePath = cover;
    message.extendOne = json.encode(quoteMsg);
    DbHelper.insertMsg(message);
  }

  // 更新引用类型  视频的播放 url
  updateVideoPlayUrl(String playUrl) async {
    quoteMsg?.localUrl = playUrl;
    message.extendOne = json.encode(quoteMsg);
    DbHelper.insertMsg(message);
  }

  // 更新引用类型  语音的播放 url
  updateVoicePlayUrl(String playUrl) async {
    quoteMsg?.localUrl = playUrl;
    message.extendOne = json.encode(quoteMsg);
    DbHelper.insertMsg(message);
  }

  Future updateFileUrl(String url) async {
    quoteMsg?.localUrl = url;
    message.extendOne = json.encode(quoteMsg);
    DbHelper.insertMsg(message);
  }

}


extension MessageImageDataExt on MessageImageData {

  Future convertThumbPath(String? fileId) async {
    var localCoverExist = await FileUtil.isExist(this.message.localUrl);
    if(localCoverExist) return this.message.localUrl;

    if(fileId == null) return '';
    String? path = await FileThumbHelper.thumbPathByFileId(fileId);
    return path;
  }

  Future convertPreviewPath(String fileId) async {
    String dir = (await getApplicationDocumentsDirectory()).path;
    String path = '$dir/${IMAGE_PREVIEW_FOLDER}/${fileId}.png';
    return path;
  }

  Future downloadImage() async {
    var e = this;
    Completer<String> completer = Completer();

    var localUrlExist = await FileUtil.isExist(e.message.localUrl);
    if (localUrlExist) return e.message.localUrl;
    var exist = await FileThumbHelper.isThumbExist(e.message.fileId);
    if (exist) return await e.convertThumbPath(e.message.fileId);
    var downloadPath = await e.convertThumbPath(e.message.fileId);

    await CosDownLoadUtil.cacheThumb(e.message.fileId, (prop) async {
      if (prop < 1 && prop > 0) {
        e.progress = prop;
        // update();
      }
    },success: (){
        e.localUrl = downloadPath;
        e.message.localUrl = downloadPath;
        e.message.longitude = 1;
        DbHelper.insertMsg(e.message);
        completer.complete(downloadPath);
        // update();
        // 防止其他引用类型的图片有该 msgId的消息，直接更新
        // _queryQuoteTypeLocalUrl(e.message.msgId , downloadPath);
    });

    return completer.future;
  }
}

extension MessageVideoDataExt on MessageVideoData {

  Future convertCoverThumbUrl(String? fileId) async {
    var localCoverExist = await FileUtil.isExist(this.message.videoImagePath);
    if(localCoverExist)return this.message.videoImagePath;

    if(fileId == null) return '';
    String dir = (await getApplicationDocumentsDirectory()).path;
    String path = '$dir/${IMAGE_THUMB_FOLDER}/${fileId}.png';
    return path;
  }


  Future convertVideoLocalUrl(String filepath) async {
    String dir = (await getApplicationDocumentsDirectory()).path;
    Directory directory = Directory('$dir/${CosManager().VIDEO_COMPRESSED_FOLDER}');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    String path = '$dir/${CosManager().VIDEO_COMPRESSED_FOLDER}/$filepath';
    return path;
  }

  // 更新视频封面； 更新本地db
  Future updateDbVideoCover(String cover) async {
    message.videoImagePath = cover;
    this.cover = cover;
    message.longitude = 1;
    DbHelper.insertMsg(message);
  }

  // 下载完视频播放地址后 更新本地db
  Future updateDbVideoUrl(String url) async {
    logger('下载完视频后重新更新数据库1:  ${message.msgId}');
    var uid = await UserHelper.getUid();
    var list = await DbHelper.getMessageByUidAndMsgId(uid, message.msgId);
    if(list.isNotEmpty){
      var m = list.first;
      m.localUrl = url;
      localUrl = url;
      DbHelper.insertMsg(m);
    }
  }
}

extension MessageFileDataExt on MessageFileData {

  Future convertLocalUrl(String filepath) async {
    String dir = (await getApplicationDocumentsDirectory()).path;
    Directory directory = Directory('$dir/${CosManager().FILE_CACHE_FOLDER}');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    String path = '$dir/${CosManager().FILE_CACHE_FOLDER}/$filepath';
    return path;
  }

  Future updateDbFileUrl(String url) async {
    message.localUrl = url;
    localUrl = url;
    DbHelper.insertMsg(message);
  }

}

extension MessageVoiceDataExt on MessageVoiceData {

  Future convertLocalUrl(String filepath) async {
    String dir = (await getApplicationDocumentsDirectory()).path;
    Directory directory = Directory('$dir/${CosManager().VOICE_CACHE_FOLDER}');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    String path = '$dir/${CosManager().VOICE_CACHE_FOLDER}/$filepath';
    return path;
  }

  Future updatePlayUrl(String url) async {
    message.localUrl = url;
    localUrl = url;
    DbHelper.insertMsg(message);
  }
}


/// 策略模式扩展 ： 具体使用的消息类型，文本、图片等 ------- end

