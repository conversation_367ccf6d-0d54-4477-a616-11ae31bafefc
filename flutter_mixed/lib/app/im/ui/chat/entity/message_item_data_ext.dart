

import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../constant/ImMsgConstant.dart';

extension MessageItemDataExt on MessageItemData {
  
  bool get canRecall {
    if (!isMyMessage) return false;
    //  两分钟
    if (dateCreated != null) {
      if (DateTime.now().difference(DateTime.fromMillisecondsSinceEpoch(message?.sendTime ?? 0)) > Duration(seconds: 120)) {
        return false;
      } else {
        return true;
      }
    }
    return false;
  }
  
  bool get canCopy {
    return (message.msgType == ConstantImMsgType.SSChatMessageTypeText ||  
        message.msgType == ConstantImMsgType.SSChatMessageTypeQuote ||  
        message.msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice || 
        message.msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeRobot);
  }
  
  bool get canQuote {
     return  (message.msgType == ConstantImMsgType.SSChatMessageTypeText ||  // 文本
         message.msgType == ConstantImMsgType.SSChatMessageTypeQuote ||  // 引用
         message.msgType == ConstantImMsgType.SSChatMessageTypeInvite ||
         message.msgType == ConstantImMsgType.SSChatMessageTypeForwardRecord ||  // 引用
         message.msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice || // @类型
         message.msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice ||
         message.msgType == ConstantImMsgType.SSChatMessageTypeImage ||
         message.msgType == ConstantImMsgType.SSChatMessageTypeVoice ||
         message.msgType == ConstantImMsgType.SSChatMessageTypeFile ||
         message.msgType == ConstantImMsgType.SSChatMessageTypeVideo ||
         message.msgType == ConstantImMsgType.SSChatMessageTypeMap) ;
  }

  // 长按模式后，是否能展示 【多选】内容
  bool get canSelect {
    if(message.isSuccess != 1) return false;
    return (message.msgType == ConstantImMsgType.SSChatMessageTypeText ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeImage ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeFile ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeMap ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeVideo ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeQuote ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeRobot ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice ||
        message.msgType == ConstantImMsgType.SSChatMessageTypeForwardRecord);
  }


  String convertInputQuoteAlias() {
    String showQuoteContent = message.alias();
    return '$author: $showQuoteContent';
  }

  
}