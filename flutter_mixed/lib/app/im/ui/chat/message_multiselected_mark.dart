

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';

/// 进入多选模式后，底层弹窗
class MessageMultiSelectedMark extends StatelessWidget {

  final VoidCallback itemTranslate;
  final VoidCallback mergeTranslate;
  final VoidCallback delete;

  MessageMultiSelectedMark(this.itemTranslate, this.mergeTranslate, this.delete);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 110,
      padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
      color: Color(0xffF3F4F6),
      child: Row(
        children: [
          Expanded(child: _buildItemCard(AssetsRes.IM_BYITEM_TRANSLATE, '逐条转发', () { itemTranslate(); })),
          Expanded(child: _buildItemCard(AssetsRes.IM_MERGE_TRANSLATE, '合并转发', () { mergeTranslate(); })),
          Expanded(child: _buildItemCard(AssetsRes.IM_TRANS_DEL, '删除', () { delete(); })),
        ],
      ),
    );
  }

  _buildItemCard(String imagePath, String title , VoidCallback click) {
    return InkWell(
      onTap: click,
      child: Column(
        children: [
          Image.asset(imagePath, width: 40 ,height: 40,),
          8.gap,
          Text(title , style: TextStyle(fontSize: 12 , color: ColorConfig.msgTextColor))
        ],
      ),
    );
  }

}

