
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class HeroLargeImagePage extends StatelessWidget {
  final String? url;
  final String? cacheKey;

  final String heroTag;

  const HeroLargeImagePage({
    super.key,
    this.url,
    this.cacheKey,
    required this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    return
      GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: Container(
          color: Colors.black,
          width: double.infinity,
          height: double.infinity,
          child: Center(
            child: Hero(
              tag: heroTag, //唯一标记，前后两个路由页Hero的tag必须相同
              child: Stack(
                children: [
                  Center(
                    // child: MessageImage(url: url, cacheKey: cacheKey,),
                    child: _buildLargeScale(url),
                  ),
                  // PhotoBrowserOperation(url: url,),
                ],
              ),
            ),
          ),
        ),
      );
  }

  _buildLargeScale(String? url) {
    var _imgURL = url ?? '';
    ImageProvider? picture;
    if (_imgURL.startsWith('http')) {
      picture = NetworkImage(_imgURL);
    } else {
      picture = FileImage(File(_imgURL));
    }

    return PhotoView(
      imageProvider: picture,
      // heroAttributes: heroTag != null ? PhotoViewHeroAttributes(tag: heroTag) : null,
    );
  }
}