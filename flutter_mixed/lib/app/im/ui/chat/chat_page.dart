import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/smart_refresh.dart';
import 'package:flutter_mixed/app/im/route/route_extension.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/group_dissolve_view.dart';
import 'package:flutter_mixed/app/im/ui/chat/media_chat/media_chat_view.dart';
import 'package:flutter_mixed/app/im/ui/chat/message_input_bar.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../common/widgets/watermark.dart';
import '../../../routes/app_pages.dart';
import '../../widget/common_empty.dart';
import 'chat_title_bar.dart';
import 'common/expanded_viewport.dart';
import 'message_item_widget.dart';
import 'message_multiselected_mark.dart';

/// 【 单聊/群聊】 消息页面
/// 参数通过 get.arguments 获取
class ChatPage extends StatelessWidget {
  String sessionId = '';
  bool isGroup = false;
  final String? tag;
  String? toTargetMsgId = ''; // 搜索历史聊天内容的时候，传入的定位消息id，不常用

  ChatPage({Key? key, this.tag}) : super(key: key);

  final RefreshListener _refreshController = RefreshListener();

  var messageInputTextController = TextEditingController();

  _loadHistory(ChatController controller) {
    controller.getMessageAndOffline().then((hasData) {
      if (hasData) {
        logger('还有数据...');
        _refreshController.loadComplete();
      } else {
        logger('没有数据...');
        _refreshController.loadNoData();
      }
    });
  }

  // 暂时禁掉，改为init时就拉取
  _loadLeast(ChatController controller) {}

  @override
  Widget build(BuildContext context) {
    ChatController controller = Get.find<ChatController>(tag: tag);
    return GetBuilder<ChatController>(
        tag: tag,
        builder: (controller) {
          return ChatTitleBar(
            controller.getSessionName(),
            // '测试2',
            controller.isLoading,
            controller.groupCount(),
            controller.isGroup(),
            _buildBody(context, controller),
            onBackPressed: () {
              controller.chatGoBack();
            },
            showRightIcon: !controller.isGroup() || controller.isInCurrentGroup(),
            rightClick: () {
              if (controller.isGroup() && controller.isInCurrentGroup()) {
                // 群聊设置页面
                Routes.IM_GROUP_CHAT_INFO
                    .toPage(arguments: controller.createGroupSettingParam());
              } else {
                // 单聊设置页面
                Routes.IM_SINGLE_CHAT_INFO
                    .toPage(arguments: controller.createSingleSettingParam());
              }
            },
          );
        });
  }

  // 水印： 聊天列表 + 输入框（添加展示功能菜单）
  Widget _buildBody(BuildContext context, ChatController chatController) {
    return Stack(
      children: [
        Container(
          color: Colors.white,
        ),
        _buildWaterMarkWidget(chatController),
        Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            _buildWarningWidget(chatController),
            Expanded(child: _chatWidget(chatController)),
            _buildInputView(context, chatController)
          ],
        ),

        _buildCallTip(chatController),
        _buildUnreadDownWiget(chatController),
        _buildUnreadUpWidget(chatController),
      ],
    );
  }

  //敏感词悬浮提示
  _buildWarningWidget(ChatController chatController){
    if (StringUtil.isEmpty(chatController.warningMsg)) {
      return Container();
    }
    return Container(
      width: double.infinity,
      color: ColorConfig.backgroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child:Container(
            padding: const EdgeInsets.symmetric(vertical: 5),
            alignment: Alignment.centerLeft,
            child: Text(chatController.warningMsg ?? '',style: const TextStyle(fontSize: 13,color: ColorConfig.deleteCorlor),),
          )),
          InkWell(
            onTap: () {
              chatController.tapWarningClose();
            },
            child: Container(
              padding: const EdgeInsets.all(5),
              child: SizedBox(
                width: 24,
                height: 24,
                child: Image.asset(AssetsRes.REGIMES_CLOSE),
          ),
            ),
          )
        ],
      ),
    );
  }

  _buildUnreadDownWiget(ChatController chatController ) {
    if(chatController.showBottomBtn) {
      return Positioned(
        bottom: 80,
        right: 12,
        child: InkWell(
          onTap: () {
            chatController.scrollToBottomIfNeeded(animated: false, delay: 0);
          },
          child: Container(
            height: 29,
            width: 29,
            decoration: const BoxDecoration(
                boxShadow: [BoxShadow(
                  color: Color.fromARGB(76, 1, 106, 253),
                  offset: Offset(1, 2),
                  blurRadius: 5,
                )],
                color: ColorConfig.themeCorlor,
                borderRadius: BorderRadius.all(Radius.circular(100))
            ),
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                Text(
                  chatController.bottomBtnUnreadCount > 99 ? '99+' : chatController.bottomBtnUnreadCount.toString(),
                  style:  const TextStyle(fontSize: 14, color: ColorConfig.whiteColor, fontWeight: FontWeight.w500),
                ),
                Positioned(
                    bottom: -2,
                    child: Container(
                      height: 10,
                      width: 4,
                      decoration: const BoxDecoration(
                          color: ColorConfig.themeCorlor,
                          borderRadius: BorderRadius.all(Radius.circular(100))
                      ),
                    )
                )
              ],
            ),
          ),
        ),
      );
    }
    return Container();
  }

  _buildUnreadUpWidget(ChatController chatController) {
    if(chatController.unReadCount > 10) {
      return Positioned(
        top: 24,
        right: 0,
        child: InkWell(
          onTap: () {
            chatController.getUnreadCountMsgId();
          },
          child: Container(
            width: 130,
            height: 32  ,
            decoration: BoxDecoration(
                color: ColorConfig.whiteColor,
                borderRadius: const BorderRadius.only(topLeft: Radius.circular(100), bottomLeft: Radius.circular(100)),
                border: Border.all(color: ColorConfig.lineColor, width: 0.5)
            ),
            child: Row(
              children: [
                Padding(padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Transform.rotate(
                    angle: pi,
                    child: Image.asset('assets/images/3.0x/im_to_unread_msg.png', width: 20, height: 18),
                  ),
                ),
                Padding(padding: const EdgeInsets.only(right: 8),
                  child: Text(
                      '${chatController.unReadCount > 99 ? '99+' : chatController.unReadCount}条新消息',
                      style: const TextStyle(fontSize: 14, color: ColorConfig.themeColorSecondary)
                  ),
                )
              ],
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  _buildCallTip(ChatController chatController) {
    if (chatController.isShowJoinCall) {
      return MediaChatView(
        avatar: chatController.toJoinMeetingGroupList(),
        joinAction: () {
          // 点击加入会议
          chatController.joinuIMMeeting();
        },
      );
    }
    return Container();
  }

  _buildInputView(BuildContext context, ChatController chatController) {
    if (chatController.isDissolve() || chatController.isKicked()) {
      return GroupDissolveView(status: chatController.isDissolve() ? 1 : 2);
    }
    return _buildInputOrMultiMark(context, chatController);
  }

  _buildInputOrMultiMark(BuildContext context, ChatController chatController) {
    return Stack(
      children: [
        MessageInputBar(
          chatListViewModel: chatController,
          onSendPressed: (txt) {
            // 文本or引用发送
            chatController.sendTextOrQuote(txt);
          },
          onImageSendPressed: (list , original) {
            logger('返回的图片or 视频 路径为： $list');
            chatController.sendImageOrVideos(list , original: original);
          },
          onCameraSendPressed: (path) {
            logger('返回的camera 的为： $path');
            chatController.sendImageOrVideos([path]);
          },
          onFileSendPressed: (file) {
            logger('返回的文件路径为： ${file.path}');
            chatController.pickFile(file);
          },
          onAudioSendPressed: (path, duration) {
            logger('返回的录音路径为： $path');
            chatController.sendAudio(path, duration);
          },
          onLocationTap: () async {
            // 发送位置
            chatController.shareLocation();
          },
          onVoiceCallBack: () {
            // 语音通话
            chatController.didClickCall(1);
          },
          onVideoCallBack: () {
            // 视频通话
            chatController.didClickCall(2);
          },
          onFocus: () {},
          atContactPressed: () {
            // 监听到@输入
            logger('监听到@输入');
            chatController.route2SelectGroupMembers();
          },
          isGroup: chatController.isGroup(),
          messageInputTextController:
          chatController.messageInputTextController,
          quoteData: chatController.currentQuoteMessageData,
        ),
        if (chatController.isMultiSelected) ...[
          MessageMultiSelectedMark(
                  () => chatController.itemByTranslateClick(),
                  () => chatController.mergeTranslateClick(),
                  () => chatController.delete(context))
        ]
      ],
    );
  }

  _buildWaterMarkWidget(ChatController? chatController) {
    return IgnorePointer(
      child: WaterMark(
        painter: TextWaterMarkPainter(
          text: chatController?.waterTxt() ?? '',
          padding: const EdgeInsets.all(30),
          textStyle: const TextStyle(
            color: Color(0xffececee),
          ),
          rotate: -19,
        ),
      ),
    );
  }

  Widget _chatWidget(ChatController controller) {
    return RefreshContainer(
        enablePullDown: false,
        enablePullUp: true,
        controller: _refreshController,
        footer: smartRefreshFooter(),
        onLoading: () {
          _loadHistory(controller);
        },
        onRefresh: () {
          _loadLeast(controller);
        },
        child: Scrollable(
            controller: controller.scrollController,
            axisDirection: AxisDirection.up,
            viewportBuilder: (BuildContext context, ViewportOffset offset) {
              return ExpandedViewport(
                offset: offset,
                axisDirection: AxisDirection.up,
                // keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                slivers: [SliverExpanded(), _buildMessageList(controller)],
              );
            }));
  }

  Widget _buildMessageList(ChatController chatvm) {
    // logger('======_buildMessageList==${chatvm.chatMessages}');
    return GetBuilder<ChatController>(
        init: chatvm,
        id: 'chatList',
        builder: (cl){
          return SliverPadding(
            padding: const EdgeInsets.only(bottom: 20),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
                int realIndex = index;

                realIndex = chatvm.chatMessages.length - index - 1;
                var message =
                chatvm.chatMessages[chatvm.chatMessages.length - index - 1];
                // return _buildMessage(index, message, chatvm);
                // logger('======_buildMessageList==${chatvm.chatMessages}');
                return AutoScrollTag(
                    key: ValueKey((message as MessageItemData).messageId()),
                    controller: chatvm.scrollController,
                    index: realIndex,
                    highlightColor: const Color.fromARGB(99, 228, 228, 228),
                    child: _buildMessage(index, message, chatvm));
              }, childCount: chatvm.chatMessages.length
                  ,addAutomaticKeepAlives: false,
                  addRepaintBoundaries : true
              ),
            ),
          );
    });


  }

  Widget _buildMessage(
      int index, MessageItem message, ChatController chatController) {
    return MessageItemContainer(index, message, chatController: chatController);
  }
}
