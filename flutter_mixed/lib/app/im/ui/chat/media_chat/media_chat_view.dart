
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';

class MediaChatView extends StatefulWidget {

  bool? isVoice;

  List<String>? avatar = [];

  VoidCallback? joinAction;

  MediaChatView({
    this.isVoice,
    this.avatar,
    this.joinAction,
  });

  @override
  State<StatefulWidget> createState() => _MediaChatViewState();

}

class _MediaChatViewState extends State<MediaChatView> {

  bool clickJoinu = false;

  @override
  Widget build(BuildContext context) {
    return clickJoinu ? _confirmView() : _waitEnter();
  }

  Widget _confirmView() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            alignment: Alignment.center,
            child: const Text('是否加入音视频通话' , style: TextStyle(fontSize: 14 , color: Color(0xff323232))),
          ),
          10.gap,

          Container(
            margin: EdgeInsets.symmetric(horizontal: 30),
            height: 38,
            child: ListView.builder(
               scrollDirection: Axis.horizontal,
               itemCount: (widget.avatar ?? []).length,
                itemBuilder: (ctx ,index){
                 var avatar = (widget.avatar ?? [])[index];
                  return ImageLoader(url: avatar, width: 36, height: 36, radius: 14,) ;
            }),
          ),

          Container(
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                MaterialButton(
                  minWidth: 100,
                  elevation: 0.5,
                  color: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 7),
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(19)),
                  ),
                  onPressed: () {
                    setState(() {
                      clickJoinu = false;
                    });
                  },
                  child: const Text(
                    '取消',
                    style: TextStyle(fontSize: 14, color: Colors.black),
                  ),
                ),
                25.gap,
                MaterialButton(
                  minWidth: 100,
                  elevation: 0,
                  color: Colors.blue,
                  padding: EdgeInsets.symmetric(vertical: 7),
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(19)),
                  ),
                  onPressed: () {
                    widget.joinAction?.call();
                  },
                  child: Text(
                    '加入',
                    style: TextStyle(fontSize: 14, color: Colors.white),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _waitEnter() {
    return InkWell(
      onTap: (){
        setState(() {
          clickJoinu = !clickJoinu;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10),
        color: Colors.white,
        child: Row(
          children: [
            10.gap,
            Image.asset(AssetsRes.ICON_IM_VC_HINT , width: 15,),
            6.gap,
            const Text('有正在进行中的音视频通话,点击进入...' , style: TextStyle(fontSize: 13 , color: Color(0xff323232))),
          ],
        ),
      ),
    );
  }

}