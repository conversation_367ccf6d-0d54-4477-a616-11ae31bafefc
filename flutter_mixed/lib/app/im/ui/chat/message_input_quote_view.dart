

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data_ext.dart';
import 'package:flutter_mixed/res/assets_res.dart';

class MessageInputQuoteView extends StatelessWidget {

  VoidCallback? deleteQuoteView;

  MessageItemData? quoteMessage;

  MessageInputQuoteView({super.key , this.quoteMessage ,this.deleteQuoteView});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          // 发送者：内容
          Expanded(child: Text(quoteMessage?.convertInputQuoteAlias() ?? '' ,
            maxLines: 1,
            style: TextStyle(fontSize: 12 , color: Colors.grey),)),
          InkWell(
            onTap: (){
              deleteQuoteView?.call();
            },
            child: Container(
              padding: EdgeInsets.only(top: 2,bottom: 2 , left: 2),
              color: Colors.transparent,
              child: Image.asset(AssetsRes.MICRO_CLOSE , width: 15,),
            ),
          )

        ],
      ),
    );
  }

}