

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

// 群组已解散 提示 view 层
class GroupDissolveView extends StatelessWidget {
  final int status;

  const GroupDissolveView({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(bottom: 10),
      color: Color(0xf8f8f8f8),
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(vertical: 5),
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(20),
        alignment: Alignment.center,
        child: Text(status == 1 ? '当前群组已解散' : '您已被踢出群组', style: const TextStyle(fontSize: 16 , color: ColorConfig.mainTextColor),),
      ),
    );
  }

}