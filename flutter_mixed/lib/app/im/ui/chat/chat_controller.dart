import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:audio_waveforms/audio_waveforms.dart';
import "package:collection/collection.dart";
import 'package:flutter/cupertino.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/event/event.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/extension/file_extension.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/convert/in_time_im_convert_to_session.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/session_read.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/im_cache_task.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/im_send_msg.dart';
import 'package:flutter_mixed/app/im/im_send_queue_manager.dart';
import 'package:flutter_mixed/app/im/im_translate_helper.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/request/datasource/offline_datasource.dart';
import 'package:flutter_mixed/app/im/request/entity/chat_resp_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/action/message_click_aciton.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/friend_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/sender/msg_sender_executor.dart';
import 'package:flutter_mixed/app/im/ui/map_search/map_channel.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/search_org_members_model.dart';
import 'package:flutter_mixed/app/modules/workStand/common/focus_switch_widget.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/app/utils/cache_helper.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/widgets/widgets.dart';
import '../../../modules/contact/aboutOrg/userInfo/controllers/user_info_controller.dart';
import '../../../modules/helper/photo_manager.dart';
import '../../../permission/permission_collection_dialog.dart';
import '../../../routes/app_pages.dart';
import '../../../tianditu/tianditu_manage.dart';
import '../../../utils/http.dart';
import '../../db/entity/session.dart';
import '../../dispatch_parser/multi_sync_event_model.dart';
import '../../im_client_manager.dart';
import '../../request/datasource/callkit_datasource.dart';
import '../../request/datasource/im_datasource.dart';
import '../../request/datasource/relation_datasource.dart';
import '../../request/entity/check_im_cal_resp.dart';
import '../../request/entity/group_info_resp.dart';
import '../../route/route_helper.dart';
import '../../utils/file_util.dart';
import '../../web_link/web_link.dart';
import '../base/im_base_controller.dart';
import '../simple_group_memebers_page/at_somebody.dart';
import 'common/picture_manager.dart';
import 'dialog/chat_simple_dialog.dart';
import 'message/message_convert.dart';
import 'message_input_bar.dart';
import 'mixin_chat_ui_state.dart';

extension IterableExtension<T> on Iterable<T> {
  List<T> toSetBy<R>(R Function(T) keySelector) {
    final seen = <R>{};
    return where((element) => seen.add(keySelector(element))).toList();
  }
}

/// 单/群聊页面  viewModel
class ChatController extends BaseController with ChatUIState {
  StreamSubscription? _messageStreamSubscription;
  StreamSubscription? _groupStreamSubscription;
  StreamController<String> appLifeStream = StreamController<String>.broadcast();
  StreamSubscription? _eventAppLifeChange ;
  StreamSubscription? _lastReadStreamSubscription;
  late AutoScrollController scrollController;

  TextEditingController messageInputTextController = TextEditingController();

  late Session session;

  String toTargetMsgId = "";

  final maxSelectLimitSize = 50;

  int leastPage = 0;

  int pageSize = 30;
  int unReadCount = 0;

  List<MessageItem> chatMessages = [];

  int loadPool = -1;

  int groupMemberCount = 0;

  List<GroupUser> groupMembers = [];

  // 是否可以发送消息
  bool canSendMsg = true;
  bool cancelled = false;

  // 是否多选模式
  bool isMultiSelected = false;

  ValueNotifier<bool> autoScrollToBottom = ValueNotifier(false);

  GroupItemResp? groupInfo; //群组信息

  CheckImMeetingDetailResp? checkImMeetingDetailResp;

  bool isShowJoinCall = false;

  SearchMemberModel? otherModel; //单聊对方的model

  bool ignoreScrollEvents = false;

  bool isMyChatPage = false;

  bool _isListenerEnd = false; //消息表初始化回调是否完成 过滤第一次

  bool _onResume = false;

  bool _firstSession = false;

  MessageSenderExecutor sendExecutor = MessageSenderExecutor();

  // 添加一个 Set 来记录正在重发的消息 ID
  final Set<String> _resendingMessages = {};

  String? senderUrl = '';//单聊或群组头像
  String? senderName = '';//单聊或群组名称

  bool isOffline = false;//是否为离线跳转

  String? warningMsg = '';//敏感词提示

  int lastReadTime = 0;//最后的已读时间戳
  void showLoading() {
    isLoading = true;
    update();
  }

  void disMissLoading() {
    isLoading = false;
    update();
  }

  @override
  void onInit() {
    super.onInit();
    scrollController = AutoScrollController();
    var param = Get.arguments;
    if (param == null) {
      Get.back();
      return;
    }

    if (param is Map) {
      // 来自 消息->搜索
      if (param.containsKey("session") && Get.arguments["session"] != null) {
        session = param["session"];
        if (param["message"] != null) {
          toTargetMsgId = param["message"].msgId;
        }
        if (param['isOffline'] != null) {
          isOffline = param['isOffline'];
        }
        
      }
    } else if (param is Session) {
      // 来自会话列表
      session = param;
    } else if (param is SingleInfoChatEntity) {
      parseSingleArgument(param);
    } else if (param is GroupInfoChatEntity) {
      // 来自群组 todo
    }
    checkImCallDetail();

    scrollController.addListener(() {
      if (!ignoreScrollEvents) {
        _scrollListener();
        _scrollListenerBottom();
      }
    });

    eventBus.on<ResumeSender>().listen((x){
      _onResume = true;
    });

    eventBus.on<ImConnected>().listen((x){
      // _onResumeFileSend();
    });

    //listenSessionChanged();

    _eventAppLifeChange = eventBus.on<EventAppLifeChange>().listen((event){
      logger("===========applife change===$event=======");
      appLifeStream.sink.add(event.state);
    });
  }


  // 主要用于，后台返回后socket断联，重新拉取session后 当前消息页面也恢复查找未收到数据
  // listenSessionChanged() async {
  //   var uid = await UserHelper.getUid();
  //   DbHelper.listenSessionList(uid).listen((event){
  //     logger('=====监听来了=====');
  //     if(!_firstSession) {
  //       logger('=========过滤第一次监听');
  //       _firstSession = true;
  //       return;
  //     }

  //     var hasChanged =  event.where((s) => s.sessionId == session.sessionId
  //         && s.msgTime != session.msgTime).isNotEmpty;
  //     if(hasChanged){
  //       logger('==========发现变化了=====');
  //         _fetchMessageList((session.msgTime ?? 0)+1, Define.TIMEMAX);
  //       var s = event.where((s) => s.sessionId == session.sessionId).first;
  //       session.msgTime = s.msgTime;
  //     }
  //   });
  // }

  //处理在此页面im重新连接，会话拉取到新消息的情况
  dealReciveSessionChange(){
    int? lastTime = 0;
    if (chatMessages.isNotEmpty) {
      var maxItem = maxBy(chatMessages, (item) {
        if (item is MessageItemData) {
          return item.message.sendTime;
        }
      });
      if (maxItem is MessageItemData) {
        lastTime = maxItem.message.sendTime;
      }
    }else{
      lastTime = session.msgTime;
    }
    _fetchMessageList((lastTime ?? 0) + 1, Define.TIMEMAX,isMore: true);
    DbHelper.upDateSessionUnReadCount(ownerId, getSessionId(), 0);
  }

  //收到了敏感词提醒
  receiveWarning(String warning){
    warningMsg = warning;
    update();
  }
  //点击了敏感提示悬浮叉号
  tapWarningClose(){
    warningMsg = '';
    update();
  }

  //清空未读
  checkImCallDetail() async {
    if (!isGroup()) return;
    try {
      var datasource = CallKitDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await datasource.checkGroupCallDetail(session.sessionId ?? '');
      if (resp.success()) {
        checkImMeetingDetailResp = resp.data;
        isShowJoinCall = false;
        if (checkImMeetingDetailResp == null) return;
        if (checkImMeetingDetailResp!.meetingMember != null) {
          if (checkImMeetingDetailResp!.meetingMember!.isNotEmpty) {
            isShowJoinCall = true;
          }
        }
        update();
      }
    } catch (e) {
      isShowJoinCall = false;
    }
  }

  parseSingleArgument(SingleInfoChatEntity singleParam) {
    // 来自个人信息页面
    logger('来自个人信息页面');
  }

  String waterTxt() =>
      '${localUserInfo?.name ?? ''}  ${localUserInfo?.mobile?.hintMobile() ?? ''}';

  bool canScrollToBottom = false;
  bool canScrollToMessage = false;

  bool _firstAutoscrollExecuted = true;
  bool _shouldAutoscroll = false;

  bool showBottomBtn = false;
  int bottomBtnUnreadCount = 0;

  void _scrollListener() {
    _firstAutoscrollExecuted = true;

    if (scrollController.hasClients &&
        scrollController.position.pixels >=
            scrollController.position.maxScrollExtent - 20) {
      _shouldAutoscroll = true;
    } else {
      _shouldAutoscroll = false;
    }

    if (unReadCount > 0 && scrollController.hasClients) {
      var targetMsgUpIndex =
          unReadCount > 99 ? 99 : chatMessages.length - unReadCount;

      if (_isIndexVisible(targetMsgUpIndex)) {
        unReadCount = 0;
        update();
      }
    }
  }

  bool _isIndexVisible(int index) {
    if (!scrollController.hasClients ||
        chatMessages.isEmpty ||
        index < 0 ||
        index >= chatMessages.length) {
      return false;
    }

    // 获取当前滚动位置
    final double scrollOffset = scrollController.offset;
    // 获取视口高度
    final double viewportHeight = scrollController.position.viewportDimension;
    // 计算可视区域的起始和结束位置
    final double visibleStart = scrollOffset;
    final double visibleEnd = scrollOffset + viewportHeight;

    try {
      // 获取目标index对应的context
      final state = scrollController.tagMap[index];
      if (state == null) return false;

      final RenderObject? renderObject = state.context.findRenderObject();
      if (renderObject == null) return false;

      final RenderAbstractViewport viewport =
          RenderAbstractViewport.of(renderObject);
      final RevealedOffset? itemOffset =
          viewport.getOffsetToReveal(renderObject, 0.0);

      if (itemOffset == null) return false;

      return itemOffset.offset >= visibleStart &&
          itemOffset.offset <= visibleEnd;
    } catch (e) {
      return false;
    }
  }

  void _scrollListenerBottom() {
    if (!scrollController.hasClients) return;

    const double threshold = 20.0;

    final double currentPosition = scrollController.position.pixels;
    final double minScrollExtent = scrollController.position.minScrollExtent;

    bool nowAtBottom = (currentPosition - minScrollExtent).abs() <= threshold;

    if (nowAtBottom) {
      clearBottomCount();
    }
  }

  clearBottomCount() {
    showBottomBtn = false;
    bottomBtnUnreadCount = 0;
    update();
  }

  _updateBottomCount() {
    if (scrollController.hasClients && scrollController.position.pixels > 40) {
      showBottomBtn = true;
      bottomBtnUnreadCount += 1;
      update();
    }
  }

  @override
  Future childOnInit() async {
    unReadCount = session.notReadCount ?? 0;
    pageSize = getPullCount(unReadCount);

    //红点清零
    session.notReadCount = 0;
    var ownImId = await UserHelper.getOwnImId();
    isMyChatPage = session.sessionId == ownImId;
    var start = DateTime.now().millisecondsSinceEpoch;
    if (isGroup()) {
      _listenerGroupDb();
      if(SessionListController.atList.contains(session.sessionId) ||
        session.msgContent?.contains("<font color='#EC0A39'>[有人@你]</font>") == true
      ){
        SessionListController.atList.remove(session.sessionId);
        session.msgContent = session.msgContent
            ?.replaceAll("<font color='#EC0A39'>[有人@你]</font>", "");
        DbHelper.insertSession(session);
      }
    }

    autoScrollToBottom.addListener(() {
      logger('autoScrollToBottom.....');
      if (scrollController.hasClients && _shouldAutoscroll) {
        scrollToBottomIfNeeded();
      }

      if (!_firstAutoscrollExecuted && scrollController.hasClients) {
        scrollToBottomIfNeeded();
      }
      if (canScrollToBottom) {
        scrollToBottomIfNeeded(animated: true);
        canScrollToBottom = false;
      }
    });

    if (isGroup()) {
      fetchGroupInfo(updateDd: true);
    } else {
      getOtherUserInfo();
      _getLoaclLastTimeWithSessionId();
      _requestSessionReadLastTime();
      _reportSessionReadLastTime();
    }

    loadUserInfo().then((value) {
      update();
    });

    _checkTargetValid();
    _updateOwnerNameAndHeadUrl();
    DbHelper.upDateSessionUnReadCount(ownerId, getSessionId(), 0);
    if (isOffline) {

      offlineInitData();
    }else{

      initData();
    }
    
    _listenerMessageDb();
    _listenerSessionReadDb();

    // ImSendQueueManager.instance.checkSendingMsg(getSessionId());
  }

  //监听已读数据
  Future _listenerSessionReadDb() async {
    _lastReadStreamSubscription = DbHelper.listenSessionReadList(ownerId, getSessionId())
            .listen((sessionReads) async {
        for (var i = 0; i < sessionReads.length; i++) {
          SessionRead sessionRead = sessionReads[i];
          if (sessionRead.sessionId == session.sessionId) {
            if (sessionRead.lastReadTime != null) {
              lastReadTime = sessionRead.lastReadTime!;
              _updateChatMessagesWithLastReadTime();
            }
            break;
          }
        }

    });
  }

  _updateChatMessagesWithLastReadTime(){
    if (isGroup()) return;
    for (var element in chatMessages) {
      if (element is MessageItemData){
        element.message.isReaded = 0;
        if (element.message.sendTime != null) {
          if (lastReadTime >= element.message.sendTime!) {
            element.message.isReaded = 1;
          }
        }
      }
    }
    update();
  }

  //查询本地当前会话最后的已读时间
  _getLoaclLastTimeWithSessionId() async{
    if (isGroup()) return;
    SessionRead? sessionRead = await DbHelper.querySessionReadBySessionId(session.sessionId);
    if (sessionRead != null) {
      lastReadTime = sessionRead.lastReadTime ?? 0;
      logger('=====获取到了本地最后已读时间====$lastReadTime');
    }
  }
  //获取会话最后已读时间时间戳
  _requestSessionReadLastTime() async{
    if (isGroup()) return;
    if (StringUtil.isEmpty(session.appChatId)) return;
    try {
      var datasource = OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
      var resp = await datasource.getSessionReadTime(session.appChatId!);
      if (resp.success()) {
        if (resp.data != null) {
          lastReadTime = resp.data;
          logger('=====获取到了远程最后已读时间====$lastReadTime');
          _updateLocalLastReadTime();
          _updateChatMessagesWithLastReadTime();
        }
      }
    } catch (e) {
      
    }
  }

  //更新本地已读时间戳
  _updateLocalLastReadTime(){
    if (isGroup()) return;
    SessionRead sessionRead = SessionRead(uid: ownerId)
    ..sessionId = session.sessionId
    ..appChatId = session.appChatId
    ..lastReadTime = lastReadTime;
    DbHelper.insertSessionRead(sessionRead);
  }

  //上报当前已读时间戳
  _reportSessionReadLastTime() async{
    if (isGroup()) return;
    if (StringUtil.isEmpty(session.appChatId)) return;
    logger('======上报userId====${session.appChatId}');
    try {
      var datasource = OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
      var resp = await datasource.setSessionReadTime({'userId':session.appChatId!});
      logger('======上报时间接口=====${resp.data}');
    } catch (e) {
      logger('=====e===$e');
    }
  }


  //更新本地自己的头像和名称
  _updateOwnerNameAndHeadUrl() async {
    var name = await UserHelper.getOwnName();
    var headerUrl = await UserHelper.getOwnAvatar();
    DbHelper.updateSenderInfo(
        ownerId, session.sessionId, ownerId, name, headerUrl);
  }

  //更新单聊对方的头像和名称
  _updateOtherNameAndHeadUrl(
      String otherId, String name, String headerUrl) async {
    if (session.appChatId == null) return;
    DbHelper.updateSenderInfo(
        ownerId, session.sessionId, otherId, name, headerUrl);
  }

  //更新当前数组里的头像和名称
  _updateChatMessages(){
    if ((senderName == null || senderUrl == null) && !session.isGroupChat()) return;
    if (groupMembers.isNotEmpty && session.isGroupChat()) return;
    for (var element in chatMessages) {
      if (element is MessageItemData){
        if (session.isGroupChat()) {
          for (var i = 0; i < groupMembers.length; i++) {
            GroupUser groupUser = groupInfo!.users![i];
            if (groupUser.id == element.message.sendId) {
              element.message.sendHeader = groupUser.headimg;
              element.message.sendName = groupUser.name;
              break;
            }
          }
        }else{
          if (element.message.sendId == session.appChatId) {
            element.message.sendHeader = senderUrl;
            element.message.sendName = senderName;
          }
        }

      }
    }
    update();
  }

  _updateGroupMembersInfo() {
    for (var i = 0; i < groupMembers.length; i++) {
      GroupUser groupUser = groupInfo!.users![i];
      _updateOtherNameAndHeadUrl(
          groupUser.id ?? '', groupUser.name ?? '', groupUser.headimg ?? '');
    }
  }

  Future getUnreadCountMsgId() async {
    var targetMsgUpIndex =
        unReadCount > 99 ? 99 : chatMessages.length - unReadCount;
    await scrollController.scrollToIndex(
      targetMsgUpIndex,
      preferPosition: AutoScrollPosition.middle,
    );
    await Future.delayed(const Duration(milliseconds: 200));
    scrollController.highlight(targetMsgUpIndex);
    unReadCount = 0;
    update();
  }

  Future scrollToMessageAndHighlight() async {
    if (toTargetMsgId.isEmpty || chatMessages.isEmpty) return;
    int targetIndex = chatMessages.indexWhere((messageItem) {
      if (messageItem is MessageItemData) {
        return messageItem.messageId() == toTargetMsgId;
      } else {
        return false;
      }
    });
    logger("targetIndex :  $targetIndex");
    if (targetIndex == -1) {
      logger("未找到目标消息");
      return;
    }

    await scrollController.scrollToIndex(
      targetIndex,
      preferPosition: AutoScrollPosition.begin,
      duration: Duration(milliseconds: 10),
    );
    // 启动高亮动画
    await Future.delayed(Duration(milliseconds: 200)); // 确保滚动完成后再高亮
    scrollController.highlight(targetIndex);

    toTargetMsgId = "";
    logger("targetIndex :  $targetIndex");
  }

  // 监听数据变化,直接渲染
  Future _listenerMessageDb() async {
    _messageStreamSubscription = DbHelper.listenMessageList(ownerId, getSessionId())
            .listen((messages) async {
      // 原始Message数据，转换为 MessageData（UI类型实体）
      if (!_isListenerEnd) {
        logger('聊天页面监听数据库变化，初始化： ${messages.toString()}');
        _isListenerEnd = true;
        _dealSessionAndScroll();
        return;
      }

      logger('聊天页面监听数据库变化， 监听： ${messages.toString()}');

      // 过滤掉断层数据
      List<Message> tempList = messages
          .where((e) =>
              e.msgType != ConstantImMsgType.SSChatMessageTypeOfflineFault &&
              e.msgType != ConstantImMsgType.SSChatMessageTypeClearChatRecord)
          .toList();

      _fillNullSessionMsgContent(tempList);

      //获取本地最大更新时间
      int localUpdateTime = _backMaxUpdateTime();
      logger('localUpdateTime = ${localUpdateTime}');
      //过滤出需处理的数据

      tempList = tempList.where((e) => (e.upDateTime ?? 0 ) > localUpdateTime)
          .toList();
      if (tempList.isNotEmpty) {
        logger('有 tempList 么 ${tempList}');
        // 注意，过滤完再分组
        await _dealReceiveListenerData(tempList);
      }
    });
  }

  //更新会话session
  _updateDbSession() async{
    if (chatMessages.isEmpty) return;
    var maxItem = maxBy(chatMessages, (item) {
      if (item is MessageItemData) {
        return item.message.sendTime;
      }
    });
    if (maxItem is MessageItemData) {
      if ((session.msgTime ?? 0) < maxItem.message.sendTime!) {
        session.msgContent = maxItem.message.alias();
        session.msgTime = maxItem.message.sendTime;
        session.msgId = maxItem.message.msgId;
        DbHelper.insertSession(session);
      }
    }
  }

  // 从 好友或群组进入的时候 防止会话为空
  _fillNullSessionMsgContent(List<Message> tempList) async {
    var session = await DbHelper.getSessionByOwnerId2SessionId(ownerId, getSessionId());
    if(session == null) return;
    if(StringUtil.isEmpty(session.msgContent)){
      var filters = tempList.where((e) => e.msgType != ConstantImMsgType.SSChatMessageTypeDel).toList();
      if(filters.isEmpty) return;
      session.msgContent = filters.first.alias();
      session.msgTime = filters.first.sendTime;
      DbHelper.insertSession(session);
    }
  }

  _dealReceiveListenerData(List<Message> tempList) async{
    await Future.forEach(tempList, (e) async {
      var a = await e.toMessageItem();
      bool isHaveCmdId = false;
      for (var i = 0; i < chatMessages.length; i++) {
          var chat = chatMessages[i];
          if (chat is MessageItemData) {
            if (chat.message.msgId == e.msgId) {
              //需更新
              logger('需更新1。。msgId = ${chat.message.msgId}');
              isHaveCmdId = true;
              chatMessages[i] = a;

              // update();
              break;
            }else{
              if (!StringUtil.isEmpty(e.cmdId)) {
                if (chat.message.cmdId == e.cmdId) {
                //需更新
                logger('需更新1。。cmdId = ${chat.message.cmdId}');
                isHaveCmdId = true;
                chatMessages[i] = a;

              // update();
                break;
              }
              }
            }
          }
      }
      if (!isHaveCmdId) {
        //需新增
        chatMessages.add(a);
        if (e.sendId != ownerId) {
          _sendReadedMessage();
          _reportSessionReadLastTime();
        }
      }
    });
    chatMessages = isShowGapTimeForMessageItem(chatMessages);

    chatMessages = chatMessages
      .where((e) => e is MessageItemData && e.message.isDelete != 1)
      .toSetBy((e) => (e as MessageItemData).message.msgId)
    .toList();
    _updateChatMessagesWithLastReadTime();
     _dealSessionAndScroll();
     _updateDbSession();
  }

  _backMaxUpdateTime(){
    int maxUpdateTime = 0;
    if (chatMessages.isNotEmpty) {
        var maxItem = maxBy(chatMessages, (item) {
          if (item is MessageItemData) {
           return item.message.upDateTime;
          }
        });
      
        if (maxItem is MessageItemData) {
          maxUpdateTime = maxItem.message.upDateTime ?? 0;
        }
    }
    return maxUpdateTime;
  }

  _dealSessionAndScroll() async{
    update();
    var updatedSession = await DbHelper.getSessionByOwnerId2SessionId(ownerId, getSessionId());
    if (updatedSession != null) {
      if (updatedSession.notReadCount != null) {
          // 进入聊天窗口外部session会有偶现小红点 直接 🆑 试试呢 ？？？
        DbHelper.upDateSessionUnReadCount(ownerId, getSessionId(), 0);
        if (updatedSession.notReadCount! > 0) {
            _updateBottomCount();
        }
      }
    }
    scrollToMessageAndHighlight();
  }

  _sendReadedMessage() async{
    if (isGroup() || isMyChatPage) return;
    if (ImClientManager.instance.connectStatus == IMConnectStatus.connected) {
      session.sendReadedSocket();
    }else{
      ImMsg readMsg = await session.getReadedSocket();
      ImCacheTask.addTask(readMsg);
    }
    
  }

  dynamic createSingleSettingParam() => session.createSingleInfoRouteParam();

  MessageItemData? currentQuoteMessageData;

  clearCurrentQuote() {
    currentQuoteMessageData = null;
    update();
  }

  // 在MessageInput 下方展示一个简略的引用标记内容
  createQuoteIntMessage(MessageItemData messageItemData) async {
    this.currentQuoteMessageData = messageItemData;
    update();
  }

  dynamic createGroupSettingParam() => session.createGroupInfoRouteParam();

  String getSessionName() => session.name.spiltString(10);

  String getLogo() => session.headerUrl ?? '';

  String getSessionId() => session.sessionId ?? '';

  int getSessionType() => session.sessionType ?? 1;

  bool isGroup() => session.isGroupChat() == true;

  bool isCanVoice() => groupInfo!.isAdmin == 1 || groupInfo!.voice != 1;
  bool isCanAtEveryone() =>
      groupInfo!.isAdmin == 1 || groupInfo!.hintMember != 1;

  // 当前时间
  int getTimeEnd() => DateTime.now().millisecondsSinceEpoch;

  int getLeastTime() => 9999999999999;

  // 获取断层的时间
  Future<int> getStartTime() async {
    var faultMsg = await DbHelper.queryLastFaultMessageBySessionId(
        ownerId, getSessionId());
    if (faultMsg == null) return -1;
    return faultMsg.sendTime ?? -1;
  }

  int groupCount() => groupMemberCount;

  var _isDissolve = false;
  var _isKicked = false;

  bool isKicked() => _isKicked;
  bool isDissolve() => _isDissolve;

  bool isInCurrentGroup() => isGroup() && !isDissolve() && !isKicked();

  Future _listenerGroupDb() async {
    _groupStreamSubscription =
        DbHelper.listenGroupList(session.sessionId).listen((group) async {
      logger("group_table_change");
      await fetchGroupInfo();
    });
  }

  // 获取群组信息，显示群成员数量等
  fetchGroupInfo({bool updateDd = false}) async {
    try {
      var datasource = RelationDatasource(retrofitDio, baseUrl: Host.HOST);
      var resp = await datasource.getGroupInfo(session.sessionId ?? '');
      var groupSession = await DbHelper.getSessionByOwnerId2SessionId(ownerId, session.sessionId ?? '');
      if (resp.success()) {
        var groups = resp.data;
        if (groups.isEmpty) return;
        var group = groups.first;
        groupInfo = group;
        groupMemberCount = group.users?.length ?? 0;

        groupMembers = group.users ?? [];

        int isAdmin =
            groupMembers.any((item) => item.id == ownerId && item.identity != 0)
                ? 1
                : 0;
        groupInfo?.isAdmin = isAdmin;

        if (groupSession?.sessionHidden != 1 && group.isKicked(ownerId)) {
          _isKicked = true;
          _showCannotDisMissDialog('您不是本群组成员');
        }

        if (group.isDissolve()) {
          _isDissolve = true;
          // _showCannotDisMissDialog('群组已解散');
        }

        _upDatePageSession(group.name, group.logo);
        if (!StringUtil.isEmpty(groupInfo?.name) && !StringUtil.isEmpty(groupInfo?.logo)) {
          DbHelper.upDateSessionSessionNameAndLogo(
            session.sessionId, groupInfo!.name!, groupInfo!.logo!,ownerId);
        }
        _updateGroupMembersInfo();
        _updateChatMessages();
        if (updateDd) {
          _updateDbGroupInfo(groupInfo);
        }
        update();
    }
    } catch (e) {
      
    }

  }

  //更新session 发送群消息是通过session获取的头像和名称
  _upDatePageSession(String? name,String? logo){
      if (name != null) {
        session.name = name;
      }
      if (logo != null) {
        session.headerUrl = logo;
      }
  }

  _updateDbGroupInfo(GroupItemResp? groupInfo) async {
    if (groupInfo == null) return;
    var groupDbData = await DbHelper.queryGroupByGroupId(session.sessionId);
    if (groupDbData == null) return;
    int isAdmin =
        groupInfo.users!.any((item) => item.id == ownerId && item.identity != 0)
            ? 1
            : 0;
    groupDbData
      ..banned = groupInfo.banned ?? 0
      ..status = groupInfo.status ?? 0
      ..logo = groupInfo.logo ?? ''
      ..name = groupInfo.name ?? ''
      ..type = groupInfo.type ?? 0
      ..createTime = groupInfo.createTime ?? 0
      ..orgId = groupInfo.orgId ?? ''
      ..isAdmin = isAdmin;

    DbHelper.insertGroup(groupDbData);
  }

  getOtherUserInfo() async {
    if (session.appChatId == null) return;
    var isYourFriend = false;
    if (session.isSingleChat()) {
      isYourFriend = await FriendHelper.isFriend(session.appChatId ?? '');
    }
    try {
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource.getUserInfo(
          session.appChatId!, null, null, isYourFriend ? 3 : null);
      if (resp.success()) {
        if (resp.data == null) return;
        senderName = resp.data!.name;
        senderUrl = resp.data!.headimg;
        _updateOtherNameAndHeadUrl(
            resp.data!.userId, resp.data!.name, resp.data!.headimg);
        _upDatePageSession(resp.data!.name, resp.data!.headimg);
        DbHelper.upDateSessionSessionNameAndLogo(
            session.sessionId, resp.data!.name, resp.data!.headimg,ownerId);
        _updateChatMessages();
        if (resp.data!.type == 4 && session.isSingleChat()) {
          canSendMsg = false;
          _showCannotDisMissDialog('对方不是你的好友！');
        }
      }
    } catch (e) {}
  }

  String targetChatId() =>
      isGroup() ? session.sessionId ?? '' : session.appChatId ?? '';

  //正常进入
  initData() async {
    chatMessages.clear();
    showLoading();
    await getMessageAndOffline();
    disMissLoading();
    _sendReadedMessage();
  }
  //离线进入
  offlineInitData() async{
    chatMessages.clear();
    showLoading();
    await _fetchMessageList(0, Define.TIMEMAX);
    disMissLoading();
    _sendReadedMessage();
  }


  // 上拉加载历史数据
  loadMoreData() async {
    showLoading();
    await getMessageAndOffline();
    disMissLoading();
  }

  //拉取离线


  int getPullCount(int unReadCount) {
    return unReadCount.clamp(0, 99).clamp(30, 99);
  }

  //获取消息记录并拉取离线
  Future<bool> getMessageAndOffline({bool isOfflineTimeOut = false}) async {
    //isOfflineTimeOut 离线catch拉取本地

    int endTime = Define.TIMEMAX;
    if (chatMessages.isNotEmpty) {
      var firstItem = chatMessages.first;
      if (firstItem is MessageItemData) {
        if (firstItem.message.sendTime != null) {
          endTime = firstItem.message.sendTime! - 1;
        } else {
          return false;
        }
      }
    }
    //查询本地30条
    int offlineStartTime = 0;

    // var messageList = await DbHelper.getLocalMessageData(
    //     ownerId, session.sessionId, endTime, pageSize);
    // var deleteStatusMsg = await DbHelper.queryDeleteTypeMsg(session.sessionId);

    try{
      var ms = await Future.wait([
        DbHelper.getLocalMessageData(
            ownerId, session.sessionId, endTime, pageSize),
        DbHelper.queryDeleteTypeMsg(session.sessionId)
      ]);
      var messageList = ms[0] as List<Message>;
      var deleteStatusMsg = ms[1] as Message?;

      if (deleteStatusMsg != null) {
        offlineStartTime = (deleteStatusMsg).sendTime ?? 0;
      }
      logger('====messageList===$messageList');
      if (messageList.isNotEmpty) {
        bool isHave = false; //是否有断层
        for (var i = messageList.length - 1; i >= 0; i--) {
          Message message = messageList[i];
          if (message.msgType ==
              ConstantImMsgType.SSChatMessageTypeOfflineFault) {
            isHave = true;
            break;
          }
        }
        logger('====isHave duanceng===$isHave===$isOfflineTimeOut');
        //先处理为拉本地30条 有断层就拉最新覆盖 没有就加载本地
        if (isHave && !isOfflineTimeOut) {
          return await _fetchMessageList(offlineStartTime, endTime);
        } else {
          List<MessageItem> items = [];

          List<Message> tempList = isShowGapTime(messageList);

          await Future.forEach(tempList, (message) async {
            var item = await message.toMessageItem();
            items.add(item);
          });
          if (endTime == Define.TIMEMAX) {
            //第一次拉取
            chatMessages.clear();
          }
          chatMessages.insertAll(0, items);
          // logger('======chatMessage===$chatMessages');
          update();
          if (messageList.length == pageSize) {
            return true;
          } else {
            return false;
          }
        }
      } else {
        if (!isOfflineTimeOut) {
          return await _fetchMessageList(offlineStartTime, endTime);
        }else{
          return false;
        }
      }
    }catch(e){
      logger('?????$e');
    }
    return false;


  }

  _query() async {

  }

  _fetchMessageList(startTime, endTime,{bool isMore = false}) async {
    if (!isGroup()) {
      return await fetchSingleOfflineData(startTime, endTime, isMore: isMore);
    } else {
      return await fetchGroupOfflineData(startTime, endTime, isMore: isMore);
    }
  }

  // 拉取【单聊】离线 isMore im断线后重新拉取
  Future<bool> fetchSingleOfflineData(int startTime, int endTime,{bool isMore = false}) async {
    try {
      var datasource = ImDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
    var resp = await datasource.fetchOfflineSingleChatList(
        getSessionId(), 0, isMore?300:pageSize, -1, startTime, endTime);
    if (resp.success()) {
      if (resp.data.isEmpty) return false;
      var messageList = <Message>[];
      await Future.forEach(resp.data, (item) async {
        var msg = await offlineSingle2Message(
            item, getSessionId(), session.appChatId);
        if (senderName != null && senderUrl != null) {
          msg.sendHeader = senderUrl;
          msg.sendName = senderName;
        }
        messageList.add(msg);
      });
      _dealFaultMsg(messageList, endTime);
      await DbHelper.insertMessages(messageList);
      _updateOwnerNameAndHeadUrl();
      if (otherModel != null && session.appChatId != null) {
        _updateOtherNameAndHeadUrl(
            session.appChatId!, otherModel!.name, otherModel!.headimg);
        DbHelper.upDateSessionSessionNameAndLogo(
            session.appChatId!, otherModel!.name, otherModel!.headimg,ownerId);
      }
      if (isMore) {
        
        return true;
      }
      return messageList.length == pageSize;
      // 更新数据库后采用db监听的方式 刷新UI
    } else {
      // 远端单聊数据拉取错误时，处理 todo
      if (isMore) {
        return true;
      }
      return await getMessageAndOffline(isOfflineTimeOut: true);
    }
    } catch (e) {
      logger('=====离线catch===$e');
      if (isMore) {
        return true;
      }
      return await getMessageAndOffline(isOfflineTimeOut: true);
    }
  }

  // 拉取【群聊】离线
  Future<bool> fetchGroupOfflineData(int startTime, int endTime,{bool isMore = false}) async {
    try {
          var datasource = ImDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
    var resp = await datasource.fetchOfflineGroupChatList(
        getSessionId(), 0, isMore ? 300 : pageSize, -1, startTime, endTime);
    if (resp.success()) {
      // 1 存储db  2 插入节点（） 3自动刷新UI
      if (resp.data.isEmpty) return false;

      var messageList = <Message>[];
      await Future.forEach(resp.data, (item) async {
        var msg = await offlineGroup2Message(item, inChat: true);
        if (groupMembers.isNotEmpty) {
          for (var i = 0; i < groupMembers.length; i++) {
            GroupUser groupUser = groupInfo!.users![i];
            if (groupUser.id == msg.sendId) {
              msg.sendHeader = groupUser.headimg;
              msg.sendName = groupUser.name;
              break;
            }
          }
        }
        messageList.add(msg);
      });
      _dealFaultMsg(messageList, endTime);
      await DbHelper.insertMessages(messageList);
      _updateOwnerNameAndHeadUrl();
      _updateGroupMembersInfo();
      if (isMore) {
        return true;
      }
      return messageList.length == pageSize;
    } else {
      // 远端群聊数据拉取错误时，处理 todo
      if (isMore) {
        return true;
      }
      return await getMessageAndOffline(isOfflineTimeOut: true);
    }
    } catch (e) {
      if (isMore) {
        return true;
      }
      return await getMessageAndOffline(isOfflineTimeOut: true);
    }

  }

  _dealFaultMsg(List<Message> messageList, int endTime) async {

      var m = minBy(messageList, (p0) => p0.sendTime);
      if (m == null) return;
      if (m.sendTime != null) {
        DbHelper.deleteChatMessageWithMsgType(
            ownerId,
            getSessionId(),
            ConstantImMsgType.SSChatMessageTypeOfflineFault,
            m.sendTime!,
            endTime);
      }
      if (messageList.length == pageSize) {
        List<Message> msgIds = await DbHelper.getChatMessageWihtMsgId(
            ownerId, getSessionId(), m.msgId);
        if (msgIds.isEmpty) {
          //拉取到最大条目并且无最小时间的msgId记录断层
          DbHelper.insertFaultMsg(m?.sendTime ?? -1, getSessionId(), ownerId);
        }
      }
  }

  // 加载本地数据优先展示
  Future loadLocalMessageList() async {
    var localMessageList = await DbHelper.queryAllMessageBySessionId(
        ownerId, session.sessionId ?? '');
    logger('本地查询的消息长度：${localMessageList.toString()}');
    // logger('本地查询的消息： $localMessageList');

    chatMessages.clear();
    await Future.forEach(localMessageList, (e) async {
      var x = await e.toMessageItem();
      chatMessages.add(x);
    });
    logger('======chatMessage==loadLocalMessageList=${chatMessages.length}');
    // chatMessages = localMessageList.map((e) => e.toMessageItem()).toList();
    update();
  }

  // 校验对方账号是否有效
  _checkTargetValid() async {
    if (isGroup()) return;
    try {
      var datasource = ImDataSource(retrofitDio);
      var resp = await datasource.checkStranger(session.appChatId ?? '');
      if (resp.success()) {
        if (resp.data.logout == 1) {
          canSendMsg = false;
          cancelled = true;
          update();
          _showCannotDisMissDialog('对方已注销账号!');
        }
      } else {
        toast('检测对方账号状态：${resp.msg}');
      }
    } catch (e) {
      
    }
  }

  // 显示一个对方注销或解散的弹窗
  _showCannotDisMissDialog(String msg) {
    SmartDialog.show(
      builder: (context) {
        return ChatMsgCannotSendDialog(msg: msg);
      },
      keepSingle: true,
      debounce: false,
      backDismiss: false,
      useAnimation: false,
      clickMaskDismiss: false,
    );
  }

  _disMissSysTools() {
    try{
      // 使键盘/文字工具等消息
      FocusScopeNode currentFocus = FocusScope.of(Get.context!);
      if (!currentFocus.hasPrimaryFocus) {
        currentFocus.unfocus();
      }
    }catch(e){}
  }

  showImmediateTime(MessageItem messageItemData) {
    logger('${messageItemData.showImmediateTime}');

    _disMissSysTools();
    bool isShow = messageItemData.showImmediateTime;
    chatMessages
        .where((element) => element.showImmediateTime)
        .toList()
        .forEach((element) {
      element.showImmediateTime = false;
    });
    messageItemData.showImmediateTime = !isShow;
    update();
  }

  // 进入多选模式： 隐藏软键盘，隐藏功能输入面板等
  startMultiSelected(MessageItem messageItem, BuildContext context) async {
    isMultiSelected = true;
    messageItem.selected = true;
    hideKeyboard(context);
    eventBus.fire(EventDisMessageInputMenu());
    update();
  }

  saveImage(MessageItem messageItem) async {
    if (messageItem is MessageImageData) {
      // 缩略图保存格式已经修改
      CosDownLoadUtil.cacheThumb(messageItem.fileId , (progress) async{

      },success: ()async{
          var path = await messageItem.convertThumbPath(messageItem.fileId);
          var saveResult = await PhotoHelper.saveImageUrlToGallery(path);
          var toastText = saveResult ? '保存成功' : '保存失败';
          toast(toastText);
      });
    }

    if (messageItem is MessageVideoData) {
      var localexist = await FileUtil.isExist(messageItem.localUrl);
      if(localexist){
        var saveResult = await PhotoHelper.saveVideoToGallery(messageItem.localUrl);
        var toastText = saveResult ? '保存成功' : '保存失败';
        toast(toastText);
        return;
      }

      var path = await messageItem.convertVideoLocalUrl(messageItem.fileName ?? '');
      var exist = await FileUtil.isExist(path);
      if (!exist) {
        toast('请先下载视频');
        return;
      }
      var saveResult = await PhotoHelper.saveVideoToGallery(path);
      var toastText = saveResult ? '保存成功' : '保存失败';
      toast(toastText);
    }
  }

  openLinkPage(String url) async {
    link2Web(url);
  }

  // 退出多选模式
  endMultiSelected() {
    isMultiSelected = false;
    chatMessages
        .where((element) => element.selected)
        .toList()
        .forEach((element) {
      element.selected = false;
    });
    update();
  }

  // 单条转发
  transLate(Message message) async {
    var params = {
      'type': 2,
      'forwardType': 1, // 单条转发
      'forwardText': '',
    };
    var trans =
        await RouteHelper.route(Routes.GROUP_ADD_MEMBERS, arguments: params);
    logger('选择用户转发：${trans.toString()}');
    var model = TransLateModel.fromJson(trans);
    logger('model = $model');
    model.translate(message);

  }

  itemByTranslateClick() async {
    if (_checkEmptySelectedMsg()) {
      openSelectMembersPage(false);
    }
  }

  itemClick(MessageItem message, bool selected) {
    if (isMultiSelected == true) {
      if (selected) {
        var isSelectedCount =
            chatMessages.where((element) => element.selected).toList().length;
        if (isSelectedCount > (maxSelectLimitSize - 1)) {
          toast('最多选择$maxSelectLimitSize条');
          return;
        }
      }

      if(message is MessageItemData && message.canSelect){
        message.selected = selected;
      }
      update();
    } else {
      showImmediateTime(message);
    }
  }

  // 合并转发
  mergeTranslateClick() async {
    if (_checkEmptySelectedMsg()) {
      var checks = chatMessages.where((e) => e.selected).toList();
      var text = session.recordContentAlias();
      var record = MsgUIRecord(title: text)..messages = checks;
      objectStore.putIfAbsent('multiMessages', () => record);
      openSelectMembersPage(true);
    }
  }

  delete(BuildContext context) async {
    if (_checkEmptySelectedMsg()) {
      showSimpleDialog(context, title: "确定要删除所选消息么", confirmCallBack: () async{
        var selecteds =
            chatMessages.where((element) => element.selected).toList();
        var deleteTime = 0;
        var maxItem = maxBy(selecteds, (item) {
          if (item is MessageItemData) {
           return item.message.sendTime;
          }
        });
        if (maxItem is MessageItemData) {
          var maxTime = maxItem.message.sendTime ?? 0;//删除的最大时间
          var currentSession = await DbHelper.getSessionByOwnerId2SessionId(ownerId, session.sessionId);
          if (maxTime == (currentSession?.msgTime ?? 0)) {
            //删除了最后一条
            deleteTime = (currentSession?.msgTime ?? 0);
          }
        }

        Future.forEach(selecteds, (e) async {
          if (e is MessageItemData) {
            await DbHelper.deleteMsg(e.message);
            var lastMsg = await DbHelper.getLastMsgBySessionId(ownerId, session.sessionId );
            String sessionMsgContent = "";
            if (lastMsg != null) {
              sessionMsgContent = lastMsg.alias();
            } else {
              sessionMsgContent = '';
            }

            session..msgContent = sessionMsgContent
            ..msgType = lastMsg?.msgType ?? 0
            ..msgTime = lastMsg?.sendTime ?? 0;
            if (deleteTime > 0) {
              if ((session.deleteLastTime ?? 0) < deleteTime) {
                session.deleteLastTime = deleteTime;
              }
            }
            await DbHelper.insertSession(session);
          }
        });



      });
    }
  }

  _checkEmptySelectedMsg() {
    var checks = chatMessages.where((e) => e.selected).toList();
    if (checks.isEmpty) {
      toast('还未选择任何消息');
      return false;
    }
    return true;
  }

  openSelectMembersPage(bool isMerge) async {
    var params = {
      'type': 2,
      'forwardType': isMerge ? 2 : 1,
      'forwardText': session.recordContentAlias(),
    };
    var translateParam =
        await RouteHelper.route(Routes.GROUP_ADD_MEMBERS, arguments: params);

    if (translateParam == null) {
      logger('未选择用户');
      return;
    }
    logger('选择用户转发：${translateParam.toString()}');

    var translateMessages = chatMessages.where((e) => e.selected).toList();

    var messages =
        translateMessages.map((e) => (e as MessageItemData).message).toList();

    var model = TransLateModel.fromJson(translateParam);

    if (isMerge) {
      await model.transLateByMerge(
        messages,
        session.isGroupChat()
      );
    } else {
      await model.transLateItemByItem(messages);
    }
    endMultiSelected();
  }

  chatGoBack() async {
     if(isMultiSelected){
       endMultiSelected();
     }else{
       Get.back();
     }
  }

  void scrollToBottomIfNeeded({bool animated = true, int delay = 500}) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      try {
        Future.delayed(Duration(milliseconds: delay), () {
          if (animated) {
            logger('animated....yes');
            scrollController.animateTo(
              scrollController.position.minScrollExtent,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInQuad,
            );
          } else {
            logger('animated....no');

            scrollController.jumpTo(
              scrollController.position.minScrollExtent,
            );
          }
        });
      } catch (e) {
        // Do nothing if there are no items.
      }
    });
  }

  downloadFile(MessageFileData fileData, Function urlCallBack) async {
    var exist = await FileUtil.isExist(fileData.localUrl);
    if (exist) {
      urlCallBack(fileData.localUrl);
      return;
    }
    await CosManager().initPans();
    var fileLocalPath = await fileData
        .convertLocalUrl('${fileData.message.msgId}.${fileData.fileType}');
    logger('fileLocalPath =====> $fileLocalPath');
    var bucket = await CosManager().imBucket();
    if (bucket == null) return;
    await CosDownLoadUtil.downLoad(bucket, fileData.fileId ?? '', fileLocalPath,
        progress: (prop) {
      fileData.progress = prop;
      update();
    } , success: (complete){
          urlCallBack(fileLocalPath);
    });
  }

  PlayerController playerController = PlayerController();
  var waveformDataList = <double>[];

  downloadVoice(MessageVoiceData fileData, Function urlCallBack) async {
    var exist = await FileUtil.isExist(fileData.localUrl);
    if (exist) {
      urlCallBack(fileData.localUrl);
      return;
    }
    await CosManager().initPans();
    var fileLocalPath =
        await fileData.convertLocalUrl('${fileData.message.msgId}.m4a');
    logger('voiceLocalPath =====> $fileLocalPath');
    var bucket = await CosManager().imBucket();
    if (bucket == null) return;
    await CosDownLoadUtil.downLoad(bucket, fileData.fileId ?? '', fileLocalPath,
        progress: (prop) async {
     // todo
    } , success: ( r ){
      urlCallBack(fileLocalPath);
    });
  }

  // todo 此处请求公司接口是为了兼容之前传参不兼容问题，后期需要优化
  Future<OrgModel?> openCompanyInvite(String? companyId) async {
    var r = await DioUtil()
        .get('${ORGApi.GETCOMPANYINFO}/${companyId}', null, true, () {});
    if (r == null) return null;
    if (r['code'] == 1) {
      var org = OrgModel.fromJson(r['data']);
      org.companyId = org.organizationId ?? '';
      return org;
    } else {
      toast(r['msg']);
      return null;
    }
  }

  Future sendLocation(TempLocationData? map, {Message? oldMessage}) async {
    if(map == null) return;
    var content = MessageLocationData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..sendLocation = map;

    sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeMap,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  updateSession(Message? message) {
    _updateSession(message);
  }

  _updateSession(Message? message, {String? text}) async {
    if (message == null) return;
    var msgContent = message.alias();
    session.msgContent = msgContent;
    session.msgType = message.msgType;
    if (text != null) {
      session.msgContent = text;
    }
    session.msgTime = message.sendTime;
    logger('发送消息的时候 存储session = ${session}');
    DbHelper.insertSession(session);
  }

  // 点击发送按钮，会触发 发送文本or引用类型的事件
  sendTextOrQuote(String? text, {Message? oldMessage}) async {
    if (currentQuoteMessageData == null) {
      sendText(text, oldMessage: oldMessage);
    } else {
      sendQuote(text, currentQuoteMessageData!.message, oldMessage: oldMessage);
    }

    // 发送后清除草稿
    await clearDraft();
  }

  sendText(String? text, {Message? oldMessage}) async {
    var content = MessageTextData(Message(''), '', '', text, false, MessageSendStatus.sending, '')
        ..groupMembers = groupMembers;
    sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeText,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendQuote(String? text, Message quote, {Message? oldMessage}) async {
    if (text == null) return;
    late Message message;
    var ids = hasAtSb(groupMembers, text);
    if (oldMessage != null) {
      message = oldMessage;
    } else {
      message = await session.cacheLocalQuoteMessage(text, quote, ids);
    }

    await session.sendQuoteBySocket(message.cmdId ?? '', text, quote, ids);
    _updateSession(message, text: text);
    clearCurrentQuote();
  }

  sendImage(List<String> imagePaths, {Message? oldMessage , bool? original =false}) async {
    var content = MessageImageData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..imagePaths = imagePaths
      ..original = original ?? false
    ;

    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeImage,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendImageOrVideos(List<String> paths, {bool original = false}) async {
    if (paths == null || paths.isEmpty) return;
    await CosManager().initPans();
    await Future.forEach(paths, (path) async {
      File file = File(path);
      var list = <String>[];
      list.add(path);
      if (file.isVideo()) {
        await sendVideo(list);
      } else if(file.isImage()){
        await sendImage(list, original: original);
      }
    });
  }

  sendVideo(List<String> videoPaths, {Message? oldMessage}) async {
    var content = MessageVideoData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..videoPaths = videoPaths;

    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeVideo,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  pickFile(File file) async {
    var upper100M = await file.beyondUpperSize();
    if (upper100M) {
      toast('文件不能超过100M');
      return;
    }

    await CosManager().initPans();

    if (file.isImage()) {
      var images = <String>[];
      images.add(file.path);
      sendImage(images);
    } else if (file.isVideo()) {
      var videos = <String>[];
      videos.add(file.path);
      sendVideo(videos);
    } else {
      sendFile(file);
    }
  }

  sendFile(File file, {Message? oldMessage}) async {
    var content = MessageFileData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..path = file.path
    ;
    sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeFile,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendAudio(String path, Duration duration, {Message? oldMessage}) async {
    var content = MessageVoiceData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..path = path
      ..sendDuration = duration
    ;
    sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeVoice,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendWithDraw(MessageItemData messageItem) async {
    try{
      late Message message;
      late Message updatedMessage;
      message = messageItem.message;
      updatedMessage = await session.cacheLocalWithDrawMessage(
          message.msgId, message.cmdId ?? '');
      await session.sendWithDrawBySocket(message.cmdId ?? '', message);
      _updateSession(updatedMessage);
      message.resetWithDrawReplayMessage();
    }catch(e){
      print(e);
    }
  }

  // 重发消息，添加防抖机制
  reSendMsg(MessageItem messageItem) async {
    if (messageItem is! MessageItemData) return;
    
    // 如果消息 ID 已经在重发集合中，则直接返回
    String messageId = messageItem.message.msgId;
    if (_resendingMessages.contains(messageId)) {
      logger('消息正在重发中，忽略重复点击');
      return;
    }
    
    try {
      // 将消息添加到重发集合中
      _resendingMessages.add(messageId);
      
      // 执行重发逻辑
      await CosManager().initPans();

      if (messageItem is MessageVideoData) {
        // 重发视频
        var videoPath = messageItem.localUrl;
        if (!StringUtil.isEmpty(videoPath)) {
          await sendVideo([videoPath!], oldMessage: messageItem.message);
        }
      } else if (messageItem is MessageTextData) {
        // 重发文本
        await sendText(messageItem.body ?? '', oldMessage: messageItem.message);
      } else if (messageItem is MessageImageData) {
        // 重发图片
        var filePath = messageItem.localUrl;
        if (filePath == null || filePath == '') return;
        var list = [filePath];
        await sendImage(list, oldMessage: messageItem.message);
      } else if (messageItem is MessageFileData) {
        // 重发文件
        var filePath = messageItem.localUrl;
        if (filePath == null || filePath == '') return;
        await sendFile(File(filePath), oldMessage: messageItem.message);
      } else if (messageItem is MessageRecordUiData) {
        // 重发聊天记录
        await reSendRecord(messageItem);
      } else if (messageItem is MessageVoiceData) {
        // 重发语音
        await sendAudio('', Duration(seconds: 1), oldMessage: messageItem.message);
      } else if (messageItem is MessageLocationData) {
        // 重发地图
        await sendLocation(null, oldMessage: messageItem.message);
      }
    } finally {
      // 无论结果如何，都从重发集合中移除消息 ID
      _resendingMessages.remove(messageId);
    }
  }

  reSendRecord(MessageRecordUiData messageItem) async {
    var newMsg = messageItem.message;
    await session.sendExtBySocket(newMsg.cmdId ?? '', newMsg);
  }

  // 去选择@的成员
  route2SelectGroupMembers() async {
    if (groupMembers.isEmpty) return;
    // @的群成员 需要踢出掉自己
    groupMembers.removeWhere((u) => u.id == ownerId);

    var selectMember = await RouteHelper.route(Routes.SIMPL_GROUP_MEMBERS,
        arguments: {
          'groupMembers': groupMembers,
          'isCanAtEveryone': isCanAtEveryone()
        });
    logger('您选择@了哪些人: $selectMember ==== ${groupInfo.toString()}');
    if (selectMember is GroupUser) {
      var originInputBody = messageInputTextController.text.toString();
      var newInput = atSbInputResult(selectMember, originInputBody);
      messageInputTextController.text = newInput;
      hideKeyboard(Get.context!);
    }
  }

  //点击了语音通话
  didClickCall(type) {
    _sendChannel(type);
  }

  _sendChannel(type) {
    if (isGroup() && type == 1) {
      //群组音视频默认视频
      type = 2;
    }
    Map<String, dynamic> map = {
      'sessionType': session.sessionType, //会话类型
      'type': type, //int 1语音2视频3加入
      'userId': ownerId, //自己的userId,
      'appChatId': session.appChatId, //单聊为对方的userId,群组为groupId
      'targetName': getSessionName(),
      'targetLogo': getLogo(),
      'sessionId': getSessionId(),
    };
    if (type == 3) {
      if (checkImMeetingDetailResp != null) {
        if (checkImMeetingDetailResp!.meetingMember != null) {
          if (checkImMeetingDetailResp!.meetingMember!.length >= 30) {
            var typeStr = '语音';
            if (checkImMeetingDetailResp?.type == '2') {
              typeStr = '视频';
            }
            toast('$typeStr通话一次最多30人!');
            return;
          }
        }
        if (checkImMeetingDetailResp!.type != null) {
          map['type'] = int.parse(checkImMeetingDetailResp!.type!);
        }
      }

      map['meetingId'] = checkImMeetingDetailResp?.id;
      map['roomId'] = checkImMeetingDetailResp?.roomId;
    }
    if (isGroup()) {
      if (groupInfo == null) {
        toast('未获取到群组信息');
        return;
      } else {
        //此处判断群管理权限-是否可以发送
        if (!isCanVoice()) {
          toast('仅群主和管理员可开启语音通话');
          return;
        }

        map['groupInfo'] = groupInfo!.toJson();
      }
    } else {
      if (session.appChatId == ownerId) {
        return;
      }
    }
    Channel().invoke(Channel_Native_IM_Video, map);
  }

  /// 跳转 Flutter的webview，获取位子
  Future shareLocation() async {
    // 调用native 去选位置
    var model = await shareMapLocation();
    sendLocation(model);

    return;

    var locationPermission = await PermissionUtil.checkLocationPermission(Get.context!);
    if(!locationPermission) return;

    var param = await getLocation();

    var resultMap = await openWebView(param);
    logger('天地图返回的数据 $resultMap');

    if(resultMap == null) return;
    if(resultMap !is Map) return;

    var address = resultMap['address'];
    var title = resultMap['poi'];
    var latitude = resultMap['lat'];
    var longitude = resultMap['lon'];
    var url = createMapImage(longitude, latitude);

    var model1 = TempLocationData()
      ..address = address
      ..title = title
      ..longitude = longitude
      ..latitude = latitude
      ..addressImgUrl = url;
    sendLocation(model1);
  }

  bool hasUnJoinuedMeeting() => checkImMeetingDetailResp != null;

  List<String> toJoinMeetingGroupList() {
    if (checkImMeetingDetailResp == null) return [];
    var members = (checkImMeetingDetailResp!.meetingMember ?? [])
        .where((e) => e?.status == '1')
        .toList()
        .map((e) => e?.avatar ?? '')
        .toList();
    return members;
  }

  Future joinuIMMeeting() async {
    // todo 进入会议
    _sendChannel(3);
  }

  @override
  void onClose() {
    hideKeyboard(Get.context!);
    super.onClose();
    objectStore.remove('multiMessages');
    CacheHelper.clearImageLoadMemory();
    _messageStreamSubscription?.cancel();
    _groupStreamSubscription?.cancel();
    _eventAppLifeChange?.cancel();
    _lastReadStreamSubscription?.cancel();
    appLifeStream.close();
    playerController.release();
    scrollController.removeListener(_scrollListenerBottom);
    scrollController.removeListener(_scrollListener);
  }

  void jumpToBottomSilently() {
    if (!scrollController.hasClients) return;

    ignoreScrollEvents = true;
    clearBottomCount();
    try {
      scrollController.jumpTo(
        scrollController.position.minScrollExtent,
      );
    } catch (e) {
      logger('跳转底部失败 $e');
    } finally {
      Future.delayed(const Duration(milliseconds: 100), () {
        ignoreScrollEvents = false;
      });
    }
  }

  // 添加方法用于从草稿设置引用消息
  // 添加方法用于从草稿设置引用消息
  void setQuoteMessage(Message message) {
    if (message.isSoftDel()) return;

    // 根据消息类型创建适当的消息项
    MessageItemData? messageItem;

    switch (message.msgType) {
      case ConstantImMsgType.SSChatMessageTypeText:
        messageItem = MessageTextData(
          message,
          message.sendName ?? '',
          message.sendHeader ?? '',
          message.text ?? '',
          message.sendId == ownerId,
          MessageSendStatus.getSendStatus(message.isSuccess ?? 0),
          message.sendTime?.toString() ?? '',
        );
        break;
      case ConstantImMsgType.SSChatMessageTypeImage:
        messageItem = MessageImageData(
          message,
          message.sendName ?? '',
          message.sendHeader ?? '',
          message.text ?? '',
          message.sendId == ownerId,
          MessageSendStatus.getSendStatus(message.isSuccess ?? 0),
          message.sendTime?.toString() ?? '',
        );
        break;
      case ConstantImMsgType.SSChatMessageTypeVoice:
        messageItem = MessageVoiceData(
          message,
          message.sendName ?? '',
          message.sendHeader ?? '',
          message.text ?? '',
          message.sendId == ownerId,
          MessageSendStatus.getSendStatus(message.isSuccess ?? 0),
          message.sendTime?.toString() ?? '',
        );
        break;
      case ConstantImMsgType.SSChatMessageTypeVideo:
        messageItem = MessageVideoData(
          message,
          message.sendName ?? '',
          message.sendHeader ?? '',
          message.text ?? '',
          message.sendId == ownerId,
          MessageSendStatus.getSendStatus(message.isSuccess ?? 0),
          message.sendTime?.toString() ?? '',
        );
        break;
      case ConstantImMsgType.SSChatMessageTypeFile:
        messageItem = MessageFileData(
          message,
          message.sendName ?? '',
          message.sendHeader ?? '',
          message.text ?? '',
          message.sendId == ownerId,
          MessageSendStatus.getSendStatus(message.isSuccess ?? 0),
          message.sendTime?.toString() ?? '',
        );
        break;
      case ConstantImMsgType.SSChatMessageTypeQuote:
        messageItem = MessageReplyUiData(
          message,
          message.sendName ?? '',
          message.sendHeader ?? '',
          message.text ?? '',
          message.sendId == ownerId,
          MessageSendStatus.getSendStatus(message.isSuccess ?? 0),
          message.sendTime?.toString() ?? '',
        );
        break;
      default:
      // 默认使用文本类型
        messageItem = MessageTextData(
          message,
          message.sendName ?? '',
          message.sendHeader ?? '',
          message.text ?? '',
          message.sendId == ownerId,
          MessageSendStatus.getSendStatus(message.isSuccess ?? 0),
          message.sendTime?.toString() ?? '',
        );
        break;
    }

    if (messageItem != null) {
      currentQuoteMessageData = messageItem;
      update();
    }
  }

  // 在发送消息后清除草稿
  Future<void> clearDraft() async {
    final uid = await UserHelper.getUid();
    if (uid != null && session.sessionId != null) {
      await DbHelper.deleteDraft(session.sessionId!, uid);
    }
  }

}




downloadVideo(MessageVideoData video, Function urlCallBack , {Function? existUrlCallBack,  Function(double)? progressLoad}) async {
  var exist = await FileUtil.isExist(video.localUrl);
  if (exist) {
    existUrlCallBack?.call(video.localUrl);
    return;
  }

  var subVideoUrl = await video.convertVideoLocalUrl('${video.message.fileId}.mp4');
  if(await FileUtil.isExist(subVideoUrl)){
    existUrlCallBack?.call(subVideoUrl);
    return;
  }

  await CosManager().initPans();
  var videoLocalPath = await video.convertVideoLocalUrl('${video.message.fileId}.mp4');
  var bucket = await CosManager().imBucket();
  if (bucket == null) return;
  await CosDownLoadUtil.downLoad(bucket, video.fileId ?? '', videoLocalPath,
      progress: (prop) {
        progressLoad?.call(prop);
      } , success: (complete){
        urlCallBack(videoLocalPath);
  });
}


downloadFile(MessageFileData fileData, Function urlCallBack , {Function? existUrlCallBack, Function? progressLoad}) async {
  var exist = await FileUtil.isExist(fileData.localUrl);
  if (exist) {
    existUrlCallBack?.call(fileData.localUrl);
    return;
  }
  await CosManager().initPans();
  var fileLocalPath = await fileData
      .convertLocalUrl('${fileData.message.msgId}.${fileData.fileType}');
  logger('fileLocalPath =====> $fileLocalPath');
  var bucket = await CosManager().imBucket();
  if (bucket == null) return;
  await CosDownLoadUtil.downLoad(bucket, fileData.fileId ?? '', fileLocalPath,
      progress: (prop) {
        fileData.progress = prop;
        progressLoad?.call(prop);
      } , success: (downloadSuccess){
        urlCallBack(fileLocalPath);
      });
}