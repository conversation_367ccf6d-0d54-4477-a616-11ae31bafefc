import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/action/message_click_aciton.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/chat/item_cards/corporate_invitation.dart';
import 'package:flutter_mixed/app/im/ui/chat/item_cards/image_card.dart';
import 'package:flutter_mixed/app/im/ui/chat/item_cards/location_card.dart';
import 'package:flutter_mixed/app/im/ui/chat/item_cards/video_card.dart';
import 'package:flutter_mixed/app/im/ui/chat/message/custom_msg_model.dart';
import 'package:flutter_mixed/app/im/ui/chat/reply/reply_expold_text.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/app/modules/workStand/common/focus_switch_widget.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../modules/gallery/gallery.dart';
import '../../../../routes/app_pages.dart';
import '../../../route/route_helper.dart';
import '../../../utils/file_util.dart';
import '../common/picture_manager.dart';
import '../entity/message_item_data.dart';
import '../item_cards/file_card.dart';
import '../item_cards/voice_card.dart';

// 引用类型的UI，具体引用部分样式
class MessageQuoteView extends StatelessWidget {

  MessageReplyUiData? message;

  ChatController? chatController;

  MessageQuoteView({super.key, required this.message , this.chatController});

  @override
  Widget build(BuildContext context) {
    if(message == null){
      return const Text('未知引用类型');
    }

    return _buildContainer(
         () async {
          // 由于引用拆解了布局，replayView 都需要监听响应
          logger('点击了引用:${message!.quoteMsg?.alias()}');
          var messageItem = await message!.quoteMsg?.toMessageItem();
          hideKeyboard(context);
          if(messageItem is MessageFileData){
            fileClickAction(messageItem);
          }else if(messageItem is MessageLocationData) {
            mapClickAction(messageItem);
          }else if(messageItem is MessageInviteInTeamData){
            var r = await chatController?.openCompanyInvite(messageItem.companyId);
            if(r == null) return;
            RouteHelper.routePath(Routes.ORG_DETAIL , arguments: {
              'model': r
            });
          }else if(messageItem is MessageRecordUiData){
            // 会话记录点击
            RouteHelper.routePath(Routes.CHAT_RECORD,
                arguments: {'msg': messageItem}, preventDuplicates: false);
          }else if(messageItem is MessageImageData){
            _onImageTap(messageItem);
          }else if(messageItem is MessageVideoData) {

          }

        },
        Container(
          decoration: const BoxDecoration(
            color: Color(0xFFD9D9D9),
            borderRadius: BorderRadius.all(Radius.circular(2)),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 4 , vertical: 2),
          constraints: const BoxConstraints(maxWidth: 250, minWidth: 0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 宽度不定的单独处理
              Flexible(child: Text('${message!.quoteMsg?.sendName ?? ''}: ${_getContentWithQuote()}' , style: TextStyle(fontSize: 12),),),
              if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeImage
                  || message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeVideo
                  || message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeMap
                  || message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeFile)...[Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  2.gap,
                  Container(
                    width: 20,
                    height: 20,
                    child: _buildFutureConvert(),
                  )
                ],
              )],
              if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeVoice)...[
                // 语音单独处理
                 _buildFutureConvert(),
              ]
            ],
          ),
        )
    );
  }

  _buildContainer( Function()? onTapClick,Widget body) {
    var msgType =  message?.quoteMsg?.msgType ;
    if(msgType == ConstantImMsgType.SSChatMessageTypeVoice){
      return Container(child: body);
    }
    return GestureDetector(
      onTap: onTapClick,
      child: body,
    );
  }

  _getContentWithQuote(){
    if(message?.quoteMsg == null) return '';

    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeUndo){
      var ownId = message?.quoteMsg?.uid;
      var sendId = message?.quoteMsg?.sendId;
      var sender = "你";
      if(sendId != ownId){
        sender = message?.quoteMsg?.sendName ?? '';
      }
      return '$sender 撤回了一条消息';
    }
    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeImage){
      return ImPrefixMsg.imageCardString;
    }
    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeVideo){
      return ImPrefixMsg.videoCardString;
    }
    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeVoice){
      return '';
      return '${ImPrefixMsg.voiceCardString} ${message?.quoteMsg?.voiceTime ?? 0} s';
    }
    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeFile){
      return message?.quoteMsg?.fileName ?? '';
    }

    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeInvite){
      try {
        var ext1  = jsonDecode(message?.quoteMsg?.extendOne ??'');
        var custoMsg = CustomMsgModel.fromJson(ext1);
        var companyName = custoMsg.extendThree;
        return '[企业邀请] $companyName邀请你加入企业';
      }catch(e){
        logger('>>>> $e');
        return '引用的企业邀请内容错误';
      }
    }

    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeMap){
      return '${ImPrefixMsg.locationCardString} ${message?.quoteMsg?.addressTitle ?? ''}';
    }

    if(message?.quoteMsg?.msgType == ConstantImMsgType.SSChatMessageTypeForwardRecord){
      var extString = message!.quoteMsg?.extendOne ??'';
      var j = jsonDecode(extString);
      var title = j['title'];
      return title ?? '';
    }

    return message?.quoteMsg?.text ?? '';
  }

  _buildWithDrawItem(MessageItemData msgItem) {
    return FutureBuilder(future: _createWithDraw(msgItem), builder: (ctx ,snap){
      return Text('${snap.data}' ,style: TextStyle(fontSize: 12),);
    });
  }

  Future<String> _createWithDraw(MessageItemData msgItem) async {
     var ownerId = await UserHelper.getUid();

     var sendId = msgItem.message.sendId;
     var sender = "你";
     if(sendId != ownerId){
       sender = msgItem.author ?? '';
     }
     return '${sender}撤回了一条消息';
  }

  _buildItems(BuildContext context ,MessageItem? msgItem)  {

    if(msgItem == null) return Container();

    if(msgItem is MessageItemData){
      if(msgItem.message.msgType == ConstantImMsgType.SSChatMessageTypeUndo){
        return _buildWithDrawItem(msgItem);
      }
    }

    if(msgItem is MessageImageData) {
      return ImageCardItem(msgItem, () {
        _onImageTap(msgItem);

      } , width: 20 , height: 20, simple: true,);
    }

    if(msgItem is MessageVideoData){
      return VideoCard(msgItem , width: 20 ,height: 20, playIconSize: 10, chatController: chatController, simple: true,);
    }

    if(msgItem is MessageVoiceData){
      var body = msgItem;
      return VoiceCard(body , simple: true  ,chatViewModel: chatController,);
    }

    if(msgItem is MessageFileData){
      return FileCard((){
      } , msgItem , simple: true,);
    }

    if(msgItem is MessageInviteInTeamData){
      return CorporateInvitation(msgItem, () async {
      } , simple: true,);
    }

    if(msgItem is MessageLocationData){
      return LocationCard(msgItem, () {
      }, simple: true,);
    }

    if(msgItem is MessageRecordUiData) {
      try{
        var extString = message!.quoteMsg?.extendOne ??'';
        var j = jsonDecode(extString);
        var title = j['title'];
        return Text('[$title]',style: const TextStyle(fontSize: 12),);
      }catch(e){
        return Text('${msgItem.body}',style: const TextStyle(fontSize: 12),);
      }
    }
    return ReplyUnFoldText(message! ,message!.quoteMsg?.text ?? '');
  }

  _buildFutureConvert() {
    return FutureBuilder(future: message!.quoteMsg?.toMessageItem(), builder: (ctx , snap){
      return _buildItems(ctx ,snap.data);
    });
  }

  _onImageTap(MessageImageData? messageItem) async {
    if(messageItem == null) return;
    var body = messageItem;

    var galleryList = <GalleryItem>[];
    var previewPath = await FileThumbHelper.previewPathByFileId(messageItem.fileId ?? '');
    galleryList.add(GalleryItem(messageItem.messageId(), previewPath)..fileId = messageItem.fileId);

    Navigator.push(Get.context!,
        PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
          return FadeTransition(
              opacity: animation, child: GalleryPage(galleryList, 0));
        }));
  }
}


