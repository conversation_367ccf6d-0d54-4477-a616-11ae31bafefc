
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';

import '../entity/message_item_data.dart';

class ReplyUnFoldText extends StatefulWidget {

  final String txt;

  final MessageReplyUiData messageReplyUiData;

  const ReplyUnFoldText(this.messageReplyUiData ,this.txt);

  @override
  State<StatefulWidget> createState() => _ReplyTextState();
}

class _ReplyTextState extends State<ReplyUnFoldText> {

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: (){
        setState(() {
          widget.messageReplyUiData.unFold = !widget.messageReplyUiData.unFold;
        });
      },
      child: Text(_content() , style: TextStyle(fontSize: 12 ,) ,
        maxLines: widget.messageReplyUiData.unFold ? 10 : 2,
        overflow: widget.messageReplyUiData.unFold ? null: TextOverflow.ellipsis,),
    );
  }

  String _content() {
    if(widget.messageReplyUiData.unFold) return widget.txt;
    if (widget.txt.length > 50) {
      var splt = '${widget.txt.getRange(0, 50)}...';
      return splt;
    }
    return widget.txt;
  }
}