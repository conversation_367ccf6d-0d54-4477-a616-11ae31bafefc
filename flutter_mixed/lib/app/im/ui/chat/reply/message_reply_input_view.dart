
//回复的view
import 'package:flutter/material.dart';

import '../../../../../res/assets_res.dart';
import '../entity/message_item_data.dart';

class MessageReplyInputView extends StatelessWidget{
  MessageReplyUiData data;
  VoidCallback? onReplyClose;

  MessageReplyInputView({super.key, required this.data, this.onReplyClose});

  @override
  Widget build(BuildContext context) {

    return Container(
      padding: EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        //背景
          color: Color(0xFFEBEBEB),
          //设置四周圆角 角度
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        //设置四周边框
        // border: Border.all(width: 0.5, color: borderColor ?? const Color(0xFFECECEC)),
      ),
      child: Row(
        children: [
          Expanded(
            child:  Text(data.content, maxLines: 2, style: TextStyle(fontSize: 14, color: Color(0xff666666)),),
          ),
          GestureDetector(onTap:
          onReplyClose,child: Image.asset(AssetsRes.MICRO_CLOSE, width: 18, height: 18,),),
        ],
      ),

    );
  }
}


