import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../parse/linkify.dart';

typedef LinkCallback = void Function(LinkableElement link);

class MessageRichText extends StatelessWidget {

  final String text;

  final double fontSize;
  final double emojiFontSize;
  final Color textColor;
  final Color linkColor;
  final LinkCallback? onOpen;

  final useMouseRegion = true;
  final VoidCallback? tapWidgetCall;
  const MessageRichText(
      {super.key,
      required this.text,
      this.fontSize = 16,
      this.emojiFontSize = 25,
      this.textColor = Colors.black,
        this.linkColor= Colors.black,
      this.onOpen,
      this.tapWidgetCall
      });

  @override
  Widget build(BuildContext context) {
    //这个地方可以提前解析
    final elements = linkify(text, linkifiers: [
      EmojiLinkifier(),
      UrlLinkifier(),
    ]);
    final style = TextStyle(
      fontSize: fontSize,
      color: textColor,
    );

    final linkStyle = TextStyle(
      fontSize: fontSize,
      color: linkColor,
      decorationColor: Color(0xff106cf7),
      decoration: TextDecoration.underline,
    );

    return RichText(
        text: TextSpan(
            children: elements.map((element) {
      if (element is EmojiElement) {
        return _buildEmojiSpan(
          element.text,
        );
      } else if (element is LinkableElement) {
        return LinkableSpan(
          mouseCursor: SystemMouseCursors.click,
          inlineSpan: TextSpan(
            text: element.text,
            style: linkStyle,
            recognizer: TapGestureRecognizer()..onTap = () => onOpen?.call(element),
          ),
        );
      } else {
        return TextSpan(
          text: element.text,
          style: style,
        );
      }
    }).toList()));
    return SelectableText.rich(
      onTap: () {
        tapWidgetCall?.call();
      },
      TextSpan(
        children: elements.map((element) {
          if (element is EmojiElement) {
            return _buildEmojiSpan(
              element.text,
            );
          } else if (element is LinkableElement) {
            return LinkableSpan(
              mouseCursor: SystemMouseCursors.click,
              inlineSpan: TextSpan(
                text: element.text,
                style: linkStyle,
                recognizer: TapGestureRecognizer()..onTap = () => onOpen?.call(element),
              ),
            );
          } else {
            return TextSpan(
              text: element.text,
              style: style,
            );
          }
        }).toList()));
  }

  TextSpan _buildEmojiSpan(String text) {
    return TextSpan(
        text: text,
        style: TextStyle(
          fontSize: emojiFontSize,
          color: textColor,
        ));
  }
}

class LinkableSpan extends WidgetSpan {
  LinkableSpan({
    required MouseCursor mouseCursor,
    required InlineSpan inlineSpan,
  }) : super(
          child: MouseRegion(
            cursor: mouseCursor,
            child: Text.rich(
              inlineSpan,
            ),
          ),
        );
}
//
// class MyMaterialTextSelectionControls extends MaterialTextSelectionControls {
//   // Padding between the toolbar and the anchor.
//   static const double _kToolbarContentDistanceBelow = 10.0;
//   static const double _kToolbarContentDistance = 8.0;
//
//   /// Builder for material-style copy/paste text selection toolbar.
//   @override
//   Widget buildToolbar(
//       BuildContext context,
//       Rect globalEditableRegion,
//       double textLineHeight,
//       Offset selectionMidpoint,
//       List<TextSelectionPoint> endpoints,
//       TextSelectionDelegate delegate,
//       ClipboardStatusNotifier? clipboardStatus,
//       Offset? lastSecondaryTapDownPosition,
//       ) {
//     final TextSelectionPoint startTextSelectionPoint = endpoints[0];
//     final TextSelectionPoint endTextSelectionPoint =
//     endpoints.length > 1 ? endpoints[1] : endpoints[0];
//     final Offset anchorAbove = Offset(
//       globalEditableRegion.left + selectionMidpoint.dx,
//       globalEditableRegion.top +
//           startTextSelectionPoint.point.dy -
//           textLineHeight -
//           _kToolbarContentDistance,
//     );
//     final Offset anchorBelow = Offset(
//       globalEditableRegion.left + selectionMidpoint.dx,
//       globalEditableRegion.top +
//           endTextSelectionPoint.point.dy +
//           _kToolbarContentDistanceBelow,
//     );
//     final value = delegate.textEditingValue;
//     return MyTextSelectionToolbar(
//       anchorAbove: anchorAbove,
//       anchorBelow: anchorBelow,
//       clipboardStatus: clipboardStatus,
//       handleCustomButton: () {
//         print(value.selection.textInside(value.text));
//         delegate.hideToolbar();
//       },
//     );
//   }
// }
//
// class MyTextSelectionToolbar extends StatelessWidget {
//   const MyTextSelectionToolbar({
//     Key? key,
//     required this.anchorAbove,
//     required this.anchorBelow,
//     required this.clipboardStatus,
//     required this.handleCustomButton,
//   }) : super(key: key);
//
//   final Offset anchorAbove;
//   final Offset anchorBelow;
//   final ClipboardStatusNotifier? clipboardStatus;
//   final VoidCallback? handleCustomButton;
//
//   @override
//   Widget build(BuildContext context) {
//     assert(debugCheckHasMaterialLocalizations(context));
//
//     final List<_TextSelectionToolbarItemData> items =
//     [
//       _TextSelectionToolbarItemData(
//         onPressed: handleCustomButton ?? () {},
//         label: 'copy',
//       ),
//       _TextSelectionToolbarItemData(
//         onPressed: handleCustomButton ?? () {},
//         label: 'forward',
//       ),
//       _TextSelectionToolbarItemData(
//         onPressed: handleCustomButton ?? () {},
//         label: 'recall',
//       ),
//       _TextSelectionToolbarItemData(
//         onPressed: handleCustomButton ?? () {},
//         label: 'reply',
//       ),
//     ];
//
//     int childIndex = 0;
//     return TextSelectionToolbar(
//       anchorAbove: anchorAbove,
//       anchorBelow: anchorBelow,
//       toolbarBuilder: (BuildContext context, Widget child) =>
//           Container(color: Colors.pink, height: 100, child: child),
//       children: items
//           .map((itemData) =>
//           TextSelectionToolbarTextButton(
//             padding: TextSelectionToolbarTextButton.getPadding(
//                 childIndex++, items.length),
//             onPressed: itemData.onPressed,
//             child: Container(
//               color: Colors.transparent,
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   10.gap,
//                   Image.asset(
//                     AssetsRes.MSG_COPY_ICON,
//                     width: 13,
//                   ),
//                   Container(
//                     margin: EdgeInsets.only(top: 4),
//                     child: Text(
//                       itemData.label,
//                       style: TextStyle(color: Colors.white, fontSize: 14),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ))
//           .toList(),
//     );
//   }
// }
//
//
// class _TextSelectionToolbarItemData {
//   const _TextSelectionToolbarItemData({
//     required this.label,
//     required this.onPressed,
//   });
//
//   final String label;
//   final VoidCallback onPressed;
// }
