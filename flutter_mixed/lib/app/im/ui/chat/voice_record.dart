
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/ui/chat/record_conller.dart';
import 'package:flutter_mixed/app/im/ui/chat/voice/voice_record_mark_bg.dart';
import 'package:flutter_mixed/app/im/ui/chat/wave.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../res/assets_res.dart';

/**
 * 录音控件
 */
class VoiceWidget extends StatefulWidget{
  const VoiceWidget(
      {
        super.key,
        this.onSendVoice
      });

  final Function(String path,Duration duration)? onSendVoice;
  @override
  State<StatefulWidget> createState() => _VoiceWidgetState();

}

class _VoiceWidgetState extends State<VoiceWidget>{
  final _controller = RecordController();
  final _screenSize = Size(ScreenUtil().screenWidth,ScreenUtil().screenHeight);
  // 遮罩图层
  OverlayEntry? _entry;
  @override
  Widget build(BuildContext context) {
    return  Container(
        width: double.infinity,
        height: 210,
        decoration: const BoxDecoration(color: Colors.white),
        child:Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            UnconstrainedBox(
              child: SizedBox(
                width: 100,
                height: 100,
                child:GestureDetector(
                  onLongPress: ()async {
                    //权限
                    if(! await _controller.hasPermission()){
                      logger(' 没有权限...');
                      PermissionUtil.checkAudioPermission(context);
                      return;
                    }

                    _showRecordingView();
                    //录制
                    _controller.startRecord(
                        onStatusChanged: (state){
                          debugPrint('========  onStateChanged: $state ');
                          if(state == RecorderState.stopped){
                            _removeRecordView();
                          }
                        },
                        onDurationChanged: (duration) {
                          debugPrint('=========  onDurationChanged: $duration ');
                        },
                        onCompleted: (path, duration) {
                          debugPrint('=========  onCompleted: $duration ');
                          if(duration.inSeconds < 1){
                            _removeRecordView();
                            toast('录制时间过短');
                            return;
                          }
                          if(_controller.status.value == RecordStatus.recording){
                            debugPrint('========发送  path=${path}  duration=${duration.inSeconds}=======');
                            widget.onSendVoice?.call(path!,duration);
                            _removeRecordView();
                          }else{
                            _removeRecordView();
                          }

                        }
                    );
                  },
                  onLongPressMoveUpdate:(detail){
                    if(_controller.status.value == RecordStatus.none){
                      return;
                    }
                    final offset = detail.globalPosition;
                    if(_screenSize.height - offset.dy.abs() > 260){
                        _controller.updateStatus(RecordStatus.canceled);
                    }else{
                      _controller.updateStatus(RecordStatus.recording);
                    }

                  } ,
                  onLongPressEnd: (details) async{
                    _controller.stopRecord();
                  },
                  child: IconButton(
                      onPressed: (){ },
                      icon: Image.asset(AssetsRes.IC_VOICE_IMAGE , width: 130)
                  ) ,
                )
              ),
            ),
            Text(_controller.status.value.title)
          ],
        )
    );
  }
  _showRecordingView(){
    _entry = OverlayEntry(builder: (context){
        return RepaintBoundary(
          child:ValueListenableBuilder(
              valueListenable: _controller.status,
              builder: (context,value,child){
                return Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(color:Colors.black54 ),
                    Positioned(
                      width: 50,
                      height: 50,
                      bottom: 240,
                      child: ClipOval(
                        child: Container(
                          width: 50,
                          height: 50,
                          color: value == RecordStatus.canceled ? Colors.red : Colors.white,
                          child:  const Image(image: AssetImage(AssetsRes.IC_VOICE_RECORD_CLOSE)),
                        ),
                      ),
                    ),
                    Positioned(
                        height: 210,
                        width: _screenSize.width,
                        bottom:0,
                        child: VoiceRecordMarkBg(),
                    ),
                    Positioned(
                        bottom: 150,
                        child: CustomPaint(painter: WavePainter(_controller.amplitudeList),)
                    ),
                    Positioned(
                        bottom: 20,
                        child: Text(value.title)
                    )
                  ],
                );
              }
          )
        );
    });
    Overlay.of(context).insert(_entry!);
  }

  _removeRecordView(){
    if(_entry != null){
      _entry!.remove();
      _entry = null;
      _controller.updateStatus(RecordStatus.none);
    }
  }

}