


import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/draft_message.dart';

import '../../../../../logger/logger.dart';
import '../../../../utils/string-util.dart';
import '../../../db/entity/session.dart';

// 是否包含草稿
Future<bool> hasDraft(String sessionId) async {
  var uid = await UserHelper.getUid();
  var draft = await DbHelper.getDraft(sessionId, uid);
  return draft != null;
}

Future<DraftMessage?> getDraft(String sessionId , {String? muid}) async {
  var _uid = '';
  if(muid != null){
    _uid = muid;
  }else{
    _uid = await UserHelper.getUid();
  }
  var draft = await DbHelper.getDraft(sessionId, _uid);
  return draft;
}

Future keepDraftIfHas(Session session, {String? uid}) async {
  // logger('ddddd=====> ${session.msgContent} ======>');
  var draft = await getDraft(session.sessionId , muid: uid);
  if(draft != null && !StringUtil.isEmpty(draft.text)){
    // logger('有草稿么 =====> ${draft.text} ======>有草稿么');
    var draftTxt = draft.text ?? '';
    session.msgContent = '${draftPrefix()} $draftTxt';
    return session;
  }
}

String draftPrefix() {
  return "<font color='#F53F3F'>[草稿]</font>";
}