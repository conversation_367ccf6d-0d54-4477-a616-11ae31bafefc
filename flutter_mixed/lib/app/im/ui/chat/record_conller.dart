

import 'dart:async';
import 'dart:io';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

enum RecordStatus{
  // 默认状态 未交互/交互完成
  none,
  // 录制
  recording,
  // 取消录制
  canceled;
  String get title{
    switch(this){
      case none :
        return "按住说话";
      case recording :
        return "上滑取消发送";
      case canceled :
        return "松手即可取消";
    }
  }
}

class RecordController{
  RecordController();
  //音频地址
  final audioPath = ValueNotifier<String?>("");
  // 录音操作的状态
  final status = ValueNotifier(RecordStatus.none);
  // 录音操作时间内的音频振幅集合，最新值在前
  // [0.0 ~ 1.0]
  final amplitudeList = ValueNotifier<List<double>>([]);
  RecorderController? audioController;
  final duration = ValueNotifier<Duration>(Duration.zero);
  Timer? _timer;
  Function(String? path, Duration duration)? _onAllCompleted;

  //开始录制
  startRecord({
    ValueChanged<RecorderState>? onStatusChanged,
    ValueChanged<List<double>>? onAmplitudeChanged,
    ValueChanged<Duration>? onDurationChanged,
    required Function(String? path, Duration duration) onCompleted
  })async{
      try{
        reset();
        _onAllCompleted = onCompleted;
        audioController = RecorderController()
        ..androidEncoder = AndroidEncoder.aac
        ..androidOutputFormat = AndroidOutputFormat.mpeg4
        ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
        ..sampleRate = 44100;

        updateStatus(RecordStatus.recording);
        audioController?.onRecorderStateChanged.listen((state){
            onStatusChanged?.call(state);
        });
        audioController?.onCurrentDuration.listen((value){
           duration.value = value;
           if(value.inSeconds >= 60){
              stopRecord();
           }
           onDurationChanged?.call(value);
           amplitudeList.value = audioController!.waveData.reversed.toList();
        });
        await audioController!.record();

      }catch(e){
        debugPrint(e.toString());
      }
  }

  //停止录音
  stopRecord() async{
    if(audioController!.isRecording){
      audioPath.value = await audioController!.stop();
      if(audioPath.value?.isNotEmpty == true){
        debugPrint("=======录音结束   path：${audioPath.value}=======");
        _onAllCompleted?.call(audioPath.value,duration.value);
      }
    }else{
        _onAllCompleted?.call(null,Duration.zero);
    }
    reset();
  }
  // 重置
  reset() {
    _timer?.cancel();
    duration.value = Duration.zero;
    audioController?.dispose();
  }
  // 更新状态
  updateStatus(RecordStatus value) {
    status.value = value;
  }

  //权限
  Future<bool> hasPermission() async{
    if(Platform.isIOS) return true;
    final state = await Permission.microphone.status;
    return state == PermissionStatus.granted;
  }

}

