

// 优化，用策略接口拆分msg发送方法
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../../../../common/cos/cos_manager.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../utils/image_util.dart';
import '../../../../../logger/logger.dart';
import '../../../../utils/video_util.dart';
import '../../../db/db_helper.dart';
import '../../../db/entity/message.dart';
import '../../../db/entity/session.dart';
import '../../../im_client_manager.dart';
import '../../../utils/file_util.dart';
import '../../simple_group_memebers_page/at_somebody.dart';
import '../entity/message_item_data.dart';

abstract class MessageSender<T extends MessageItem> {

  Future<void> send(Session session ,
      { Message? oldMessage ,
         required T content,
      });
}


class TextMessageSender implements MessageSender<MessageTextData> {

  @override
  Future<void> send(Session session, {Message? oldMessage, required MessageTextData content}) async{
     var text = content.body;
     if (StringUtil.isEmpty(text)) return;
     var groupMembers = content.groupMembers ?? [];
     late Message message;
     if (oldMessage != null) {
       message = oldMessage;
     } else {
       message = await session.cacheLocalMessage(text);
     }

     var ids = hasAtSb(groupMembers ?? [], text!);
     await session.sendTxtBySocket(message.cmdId ?? '', text, ids);
     _updateSession(message, session);
  }
}

class ImageMessageSender implements MessageSender<MessageImageData> {

  @override
  Future<void> send(Session session, {Message? oldMessage, required MessageImageData content}) async{

    var imagePaths = content.imagePaths;
    var original = content.original;
    if (imagePaths == null) {
      toast('您还未选择图片');
      return;
    };

    var imagePath = imagePaths.first;

    if(original = false){
      var compressFile = await ImageUtil.compressImg(imagePath);
      if(compressFile == null) return;
      imagePath = compressFile.path;
    }

    late Message message;
    if (oldMessage != null) {
      message = await updateSendingStatus(oldMessage.cmdId);
    } else {
      message = await session.cacheLocalImageMessage(imagePath);
    }

    var fileId = await CosUploadHelper.getUploadFieldId();
    if (fileId == null) {
      await message.sendFail();
      return;
    }
    var imageInfo = await ImageUtil.getImageInfo(imagePath, fileId: fileId);
    imageInfo.path = imagePath;
    await message.fillImageInfo(imageInfo);
    await CosUploadHelper.upload(imagePath, fileId: fileId);
    logger('发送socket image 前检查数据： $imageInfo');
    await session.sendImageBySocket(message.cmdId ?? '', message);
    await _updateSession(message, session);
  }
}

class VideoMessageSender implements MessageSender<MessageVideoData> {

  @override
  Future<void> send(Session session, {Message? oldMessage, required MessageVideoData content}) async {
    var videoPaths = content.videoPaths ?? [];
    if (videoPaths.isEmpty) return;
    var videoPath = videoPaths.first;
    // 缓存到本地数据库（标记[发送中]状态）


    late Message message;
    if (oldMessage != null) {
      message = await updateSendingStatus(oldMessage.cmdId);
    } else {
      message = await session.cacheLocalVideoMessage(videoPath);
    }
    // 上传【封面】和【视频】到腾讯云(修改db发送状态)
    var coverFile = await VideoUtil.getVideoFirstFrame(videoPath);
    if (coverFile == null) {
      message.sendFail();
      return;
    };
    message.videoImagePath = coverFile.path;
    DbHelper.insertMsg(message);
    logger('第一帧图片：${coverFile.toString()}');
    // CosManager
    await CosManager().initPans();
    var coverFileId = await CosUploadHelper.upload(coverFile.path);
    var cover = await VideoUtil.getImageDimensions(coverFile);
    if (coverFileId != null) {
      var coverWidth = cover['width'];
      var coverHeight = cover['height'];

      // 压缩视频
      videoPath = await VideoUtil.compressVideo(videoPath);

      var videoFileId = await CosUploadHelper.upload(videoPath);
      if (videoFileId != null) {
        // 上传完成，补充Message信息，
        var videoInfo = await VideoUtil.getVideoInfo(videoPath);
        videoInfo.fileId = videoFileId;
        videoInfo.coverWidth = coverWidth!;
        videoInfo.coverHeight = coverHeight!;
        videoInfo.coverFileId = coverFileId;
        videoInfo.cover = coverFile.path;

        logger('发送socket video 前检查数据： $videoInfo');

        await message.fillVideoInfo(videoInfo);
        // 发送socket
        try{
          await session.sendVideoBySocket(message.cmdId ?? '', message);
          await _updateSession(message ,session);
          await Future.delayed(Duration(seconds: 2));
        }catch(e){
          message.sendFail();
        }
      } else {
        message.sendFail();
      }
    } else {
      message.sendFail();
    }
  }
}

class VoiceMessageSender implements MessageSender<MessageVoiceData> {

  @override
  Future<void> send(Session session, {Message? oldMessage, required MessageVoiceData content}) async {

    var path = content.path;
    var duration = content.sendDuration;
    if(path == null) return;

    late Message message;
    if (oldMessage != null) {
      message = await updateSendingStatus(oldMessage.cmdId);
      path = message.localUrl ?? '';
      duration = Duration(seconds: message.voiceTime ?? 0);
    } else {
      message = await session.cacheLocalAudioMessage(path , duration);
    }

    await CosManager().initPans();
    var fileId = await CosUploadHelper.upload(path);
    if (fileId != null) {
      await message.fileAudioInfo(fileId, duration?.inSeconds ?? 0);
      // 发送socket
      await session.sendAudioBySocket(message.cmdId ?? '', message);
      _updateSession(message,session);
    } else {
      message.sendFail();
    }
  }
}


Future<Message> updateSendingStatus(String? cmdId) async {
  var list = await DbHelper.queryMsgListByCmdId(cmdId ??'');
  var message = list[0];
  message.isSuccess = 2;
  await DbHelper.insertMsg(message);
  return message;
}

class FileMessageSender implements MessageSender<MessageFileData> {

  @override
  Future<void> send(Session session, {Message? oldMessage, required MessageFileData content}) async {

    late Message message;

    try{
      var path = content.path;

      if (oldMessage != null) {
        message = await updateSendingStatus(oldMessage.cmdId);
      } else {
        message = await session.cacheLocalFileMessage(path!);
      }

      // if(!ImClientManager.instance.isConnected()){
      //   message.sendFail();
      //   return;
      // }

      var fileId = await CosUploadHelper.upload(path);
      if (fileId != null) {
        var fileInfo = await FileUtil.getFileInfo(path!, fileId: fileId); //

        await message.fileFileInfoAndSaveDB(fileInfo);
        // 发送socket
        var sendResult = await session.sendFileBySocket(message.cmdId ?? '', message);
        if(sendResult){
          _updateSession(message , session);
        }else{
          logger('文件发送失败');
          toast('文件发送失败');
          message.sendFail();
        }
      } else {
        message.sendFail();
      }
    }catch(e){
      message.sendFail();
    }

  }
}

class LocationMessageSender implements MessageSender<MessageLocationData> {

  @override
  Future<void> send(Session session, {Message? oldMessage, required MessageLocationData content}) async{

    var location = content.sendLocation;

    late Message message;
    if (oldMessage != null) {
      message = oldMessage;
    } else {
      message = await session.cacheLocationMessage(location);
    }
    await session.sendLocationBySocket(message.cmdId ?? '', message);
    _updateSession(message , session);
  }

}

_updateSession(Message? message, Session session ,{String? text}) async {
  if (message == null) return;
  var msgContent = message.alias();
  session.msgContent = msgContent;
  session.msgType = message.msgType;
  if (text != null) {
    session.msgContent = text;
  }
  session.msgTime = message.sendTime;
  logger('发送消息的时候 存储session = ${session}');
  DbHelper.insertSession(session);
}
