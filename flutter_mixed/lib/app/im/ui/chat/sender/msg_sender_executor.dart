

import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';

import '../../../constant/ImMsgConstant.dart';
import '../../../db/entity/message.dart';
import '../../../db/entity/session.dart';
import 'message_sender.dart';

class MessageSenderExecutor {
  final Map<int, MessageSender<MessageItemData>> _senders;

  MessageSenderExecutor() : _senders = {

    ConstantImMsgType.SSChatMessageTypeText : TextMessageSender(),
    ConstantImMsgType.SSChatMessageTypeImage : ImageMessageSender(),
    ConstantImMsgType.SSChatMessageTypeVideo : VideoMessageSender(),
    ConstantImMsgType.SSChatMessageTypeVoice : VoiceMessageSender(),
    ConstantImMsgType.SSChatMessageTypeFile : FileMessageSender(),
    ConstantImMsgType.SSChatMessageTypeMap : LocationMessageSender(),

  };

  Future<void> sendMessage({
    required int msgType,
    required Session session,
    required MessageItemData content,
    Message? oldMessage,
  }) async {
    final sender = _senders[msgType];
    if (sender == null) throw UnsupportedError('Unsupported message type');
    return sender.send(
      session,
      oldMessage: oldMessage,
      content: content, // 类型安全传递
    );
  }
}