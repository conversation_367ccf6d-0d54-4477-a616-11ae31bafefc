import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../../main.dart';
import '../../../../common/api/LoginApi.dart';
import '../../../request/datasource/im_datasource.dart';

/// 图片加载、缓存管理

/// 缩略图、 预览图、 原图目录
const IMAGE_THUMB_FOLDER = 'cache/image_thumb_folder';
const IMAGE_PREVIEW_FOLDER = 'cache/image_preview_folder';
const IMAGE_ORIGIN_FOLDER = 'cache/image_origin_folder';

var thumbProportion = "imageMogr2/thumbnail/!50p";
var previewProportion = "imageMogr2/thumbnail/!100p";
var originProportion = ""; // 原比例

class FileThumbHelper {
  static Future clearImageThumbs() async {
    String rootDir = (await getApplicationDocumentsDirectory()).path;
    var path = '$rootDir/cache';
    var dir = Directory(path);
    if (await dir.exists()) {
      dir.delete(recursive: true);
    }
  }

  static Future<String> fetchImageSignRemoteUrl(String? fileId) async {
    if (StringUtil.isEmpty(fileId)) return "";
    var datasource = ImDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);
    var resp = await datasource.getFileUrl(fileId!);
    if (resp.success()) return resp.data;
    return "";
  }

  // 获取缩略图的远程路径
  static Future<String> fetchThumbRemoteUrl(String? fileId) async {
    if (StringUtil.isEmpty(fileId)) return "";
    var url = await fetchImageSignRemoteUrl(fileId);
    if(StringUtil.isEmpty(url)) return '';
    return '$url&$thumbProportion';
  }


  // 下载图片 ,传入指定
  static Future downloadThumbOnly(String? fileId, String? url, String folder,
      Function(double) proCall , {Function? success}) async {
    if (fileId == null || url == null) return;
    String dir = (await getApplicationDocumentsDirectory()).path;
    var pro = thumbProportion;
    if (folder == IMAGE_THUMB_FOLDER) {
      pro = thumbProportion;
    } else if (folder == IMAGE_PREVIEW_FOLDER) {
      pro = previewProportion;
    } else {
      // nothing
    }
    url = '$url&$pro';
    String localPath = '$dir/$folder/$fileId.png';
    logger('需要下载的 url = $url');
    logger('对应的本地 url = $localPath');
    Directory directory = Directory('$dir/$folder');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    try {
      await CosManager().initPans();
      var bucket = await CosManager().imBucket();
      CosDownLoadUtil.downLoad(bucket, fileId, localPath, progress: (pro) {
        proCall(pro);
      } , success: (r){
        success?.call();
      });
    } on DioException catch (e) {
      print('===== 下载错误详情 =====');
      print('URL: ${e.requestOptions.uri}');
      print('请求头: ${e.requestOptions.headers}');
      print('响应状态码: ${e.response?.statusCode}');
      print('响应体: ${e.response?.data}'); // 关键！服务器可能返回具体错误原因
      print('========================');
    }
    return localPath;
  }

  static Future<bool> isThumbExist(String? fileId) async {
    if (fileId == null || fileId == '') return false;
    String dir = (await getApplicationDocumentsDirectory()).path;
    var path = '$dir/$IMAGE_THUMB_FOLDER/$fileId.png';
    return File(path).exists();
  }

  static Future<String?> thumbPathByFileId(String? fileId) async {
    if (fileId == null || fileId == '') return null;
    String dir = (await getApplicationDocumentsDirectory()).path;
    return '$dir/${IMAGE_THUMB_FOLDER}/${fileId}.png';
  }

  static Future<String?> previewPathByFileId(String? fileId) async {
    if (fileId == null || fileId == '') return null;
    String dir = (await getApplicationDocumentsDirectory()).path;
    return '$dir/${IMAGE_PREVIEW_FOLDER}/${fileId}.png';
  }
}
