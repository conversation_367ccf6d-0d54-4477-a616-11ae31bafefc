
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ext/color_ext.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

import '../../../utils/string-util.dart';
import '../../request/entity/chat_resp_ext.dart';
import '../../widget/some_widget.dart';
import 'entity/message_item_data.dart';

class MessageBotCard extends StatelessWidget {

  final MessageBotUIData message;
  final Function()? onBotTap;

  final _screenW = ScreenUtil().screenWidth;

  MessageBotCard(this.message, this.onBotTap);

  @override
  Widget build(BuildContext context) {
    var botUiData = message.botUIData;
    if(botUiData == null) return const Text('机器人消息类型不支持');
    return InkWell(
      onTap: onBotTap,
      child: Container(
        width: _screenW - 100,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
                width: 0.5, color: Color(0xffE2E2E2)),
            borderRadius: BorderRadius.circular(11)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 主标题
            Container(
              width: double.infinity,
              color: botUiData.noticeNameBgColor?.toColor(),
              padding: EdgeInsets.only(left: 8, top: 10 , bottom: 10 , right: 8),
              child: Text(botUiData.noticeName?? '', maxLines:2 , overflow: TextOverflow.ellipsis,style: TextStyle(
                  fontSize: 14, fontWeight: FontWeight.bold,color: botUiData.noticeNameTextColor?.toColor()),),
            ),
            line,
            // 副标题
            if(!StringUtil.isEmpty(botUiData.noticeTitle))...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(left: 20, top: 10 , bottom: 10 , right: 15),
                child: Text(botUiData.noticeTitle ?? '', maxLines:null , overflow: TextOverflow.ellipsis,style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14, color: Color(0xff23252A)),),
              ),
            ],
            Container(
              width: double.infinity,
              margin: EdgeInsets.only(left: 20 ,right: 20),
              child: _buildBotItems(botUiData),
            ),
            if(botUiData.botBtnText != null)...[
              Container(
                  padding: EdgeInsets.all(10),
                  margin: const EdgeInsets.only(top: 10 , left: 20 ,right: 20 , bottom: 10),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                          width: 0.5, color: Color(0xffE2E2E2)),
                      borderRadius: BorderRadius.circular(11)),
                  alignment: Alignment.center,
                  child: Text(botUiData.botBtnText ?? '', style: const TextStyle(fontSize: 14 ,color: Colors.black),)
              ),
            ],
            10.gap,

          ],
        ),
      ),
    );
  }

  _buildBotItems(MsgBotUIData botUIData) {
    var items = botUIData.oldItems.map((e) => Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10),
      child: _buildBotListItem(e),
    )).toList();
    return Column(
      children: items,
    );
  }

  _buildBotListItem(BotOldItem item) {
    if(item.type == 1){
      return Text(item.title?? '' , maxLines: null, style: TextStyle(fontWeight: FontWeight.bold ,fontSize: 16 ,color: item.leftColor?.toColor(defaultColor: Colors.black)));
    }else if(item.type == 2){
      return Text(item.title?? '' , maxLines: null, style: TextStyle(fontSize: 14 ,color: item.leftColor?.toColor(defaultColor: Colors.black)));
    }else if(item.type == 0){
      return Container(
            width: double.infinity,
            child: RichText(
              maxLines: null,
              text: TextSpan(
                  children: [
                    TextSpan(text: item.title ?? '' , style: TextStyle(fontSize: 14 ,fontWeight: FontWeight.bold, color: item.leftColor.toColor(defaultColor: Colors.black))),
                    TextSpan(text: ' '),
                    TextSpan(text: item.content ?? '' , style: TextStyle(fontSize: 14 , color: item.rightColor.toColor(defaultColor: Colors.black))),
                  ]
              ),
            ),
          );
    }else {
      // type == 3
      return Container(
          padding: EdgeInsets.all(5),
          constraints: BoxConstraints(maxWidth: _screenW - 150),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                  width: 0.5, color: Color(0xffF2F3F5)),
              borderRadius: BorderRadius.circular(11)),
          child: HtmlWidget(item.content ?? '', textStyle: TextStyle(fontSize: 14),)
      );
    }
  }


}