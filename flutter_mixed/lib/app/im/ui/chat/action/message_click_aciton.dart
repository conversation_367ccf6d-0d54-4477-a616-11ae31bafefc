import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:get/get.dart';

import '../../../../permission/permission_util.dart';
import '../../../utils/file_util.dart';
import '../../map_search/map_channel.dart';
import '../chat_controller.dart';


//
Future mapClickAction(MessageLocationData location) async {
  // 调用native去显示地图

  var locationPermission = await PermissionUtil.checkLocationPermission(Get.context!);
  if(!locationPermission) return;

  openNativeMap(location.latitude, location.longitude
      , location.addressTitle, location.addressDetail);
}


Future fileClickAction(MessageFileData message , {Function? progressChanged}) async {
  downloadFile(message, (url) {
    message.updateDbFileUrl(url);
    FileUtil.openFile(url);
  } , existUrlCallBack: (url){
    FileUtil.openFile(url);

  },  progressLoad: (prop){
    // 在有必要的场景刷新UI
    progressChanged?.call(prop);
    // setState(() {
    //   message.progress = prop;
    // });
  } );
}