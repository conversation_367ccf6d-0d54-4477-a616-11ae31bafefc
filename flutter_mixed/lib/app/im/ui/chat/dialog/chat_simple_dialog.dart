
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../../../common/config/config.dart';

class ChatMsgCannotSendDialog extends StatelessWidget {

  final String msg;

  const ChatMsgCannotSendDialog({super.key , required this.msg});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: ColorConfig.whiteColor,
          borderRadius: BorderRadius.circular(8)),
      height: 150,
      width: 200,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          10.gap,
          Text(msg , style: TextStyle(fontSize: 15 ,color: ColorConfig.mainTextColor),),
          10.gap,
          const Text('无法发送消息' , style: TextStyle(fontSize: 15 ,color: ColorConfig.desTextColor,),),
          20.gap,
          Container(
            height: 0.5,
            color: ColorConfig.desTextColor,
          ),
          Container(
            alignment: Alignment.center,
            child: InkWell(
              onTap: (){
                SmartDialog.dismiss();
              },
              child: Container(
                color: Colors.transparent,
                alignment: Alignment.center,
                padding: EdgeInsets.only(top: 10),
                child: const Text('知道了' , style: TextStyle(fontSize: 15 ,color: ColorConfig.themeCorlor),),
              ),
            ),
          ),

        ],
      ),
    );

  }

}