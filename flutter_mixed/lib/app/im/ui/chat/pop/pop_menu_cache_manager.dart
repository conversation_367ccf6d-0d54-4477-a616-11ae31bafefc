
import 'custom_pop_up_menu.dart';

class PopMenuCacheManager {

  static PopMenuCacheManager? _instance;

  PopMenuCacheManager.__internal();

  factory PopMenuCacheManager.get() => _getInstance();

  static _getInstance() {
    _instance ??= PopMenuCacheManager.__internal();
    return _instance;
  }

  Map<String,CustomPopupMenuController> _map = Map();

  CustomPopupMenuController getController(String key) {
    if(_map.containsKey(key)) return _map[key]!;
    _map.putIfAbsent(key, () => CustomPopupMenuController());
    return _map[key]!;
  }

  clear(){
    _map.clear();
  }

}