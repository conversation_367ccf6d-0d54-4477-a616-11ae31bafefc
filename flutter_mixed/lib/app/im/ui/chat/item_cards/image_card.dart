

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../common/widgets/image_loader.dart';
import '../../../../utils/cache_helper.dart';
import '../common/picture_manager.dart';
import '../entity/message_item_data.dart';


/// 消息类型： 图片 ----  通用于单独的图片类型和引用图片类型

// class ImageCardItem2 extends StatefulWidget {
//
//   final double? width;
//   final double? height;
//
//   MessageImageData message;
//
//   final VoidCallback? onImageTap;
//
//   bool? simple;
//
//
//   ImageCardItem2(this.message , this.onImageTap, {super.key , this.width , this.height , this.simple});
//
//   @override
//   State<StatefulWidget> createState() => _ImageCardState();
// }
//
// class _ImageCardState extends State<ImageCardItem2> {
//
//   final _screenW = ScreenUtil().screenWidth;
//
//   var mUrl = '';
//
//   @override
//   void initState() {
//     super.initState();
//     _loadRemoteUrl(widget.message.fileId ?? '').then((r){
//       setState(() {
//          mUrl = r;
//       });
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return _buildImageMessage(widget.message);
//   }
//
//   Widget _buildImageMessage(MessageImageData message) {
//     return InkWell(
//         onTap: (){
//           widget.onImageTap?.call();
//         },
//         child: widget.simple == true ? _buildImage() : Hero(tag: message.messageId(), child: _buildImage())
//     );
//   }
//   Future<String> _loadRemoteUrl(String fileId) async {
//     if(await CacheHelper.existImageCache(fileId)){
//       return await CacheHelper.getImageByKey(fileId);
//     }
//
//     var path =  await FileThumbHelper.fetchImageSignRemoteUrl(fileId);
//     if(StringUtil.isEmpty(path)) return '';
//     CacheHelper.saveImageByKey(fileId, path);
//     return path;
//   }
//
//   Widget _buildImage() {
//     var w = (widget.message.imgWidth == null || widget.message.imgWidth == 0) ? 100.0 : widget.message.imgWidth!;
//     var h = (widget.message.imgHeight == null || widget.message.imgHeight == 0) ? 100.0 : widget.message.imgHeight!;
//
//     if(widget.width != null){
//       w = widget.width!;
//       h = widget.height!;
//     }else {
//       var prop = w/h;
//       if(w < h){
//         if(w > _screenW/3){
//           w = _screenW/3 ;
//         }
//       }else{
//         if(w > _screenW/2){
//           w = _screenW/2 ;
//         }
//       }
//       h = w / prop ;
//     }
//
//     if(StringUtil.isEmpty(widget.message.localUrl)){
//       w = h = 100;
//     }
//
//     return Container(
//         width: w,
//         height: h,
//         child: Stack(
//           alignment: Alignment.center,
//           children: [
//             ImageLoader(
//               url: mUrl,
//               width: w,
//               height: h,
//               palaceHolder: AssetsRes.IM_IMAGE_ERROR,
//               cacheWidth: w.toInt(),
//               cacheHeight: h.toInt(),
//             ),
//           ],
//         ),
//       );
//
//   }
// }


class ImageCardItem extends StatelessWidget {

  final double? width;
  final double? height;

  MessageImageData message;

  final VoidCallback? onImageTap;

  bool? simple;

  final _screenW = ScreenUtil().screenWidth;

  ImageCardItem(this.message , this.onImageTap, {super.key , this.width , this.height , this.simple});

  @override
  Widget build(BuildContext context) {
    return _buildImageMessage(message);
  }

  Widget _buildImageMessage(MessageImageData message) {
    return InkWell(
        onTap: (){
          onImageTap?.call();
        },
        child: simple == true ? _buildImage() : _buildImage()
    );
  }
  Future<String> _loadRemoteUrl(String fileId) async {
    if(await CacheHelper.existImageCache(fileId)){
      return await CacheHelper.getImageByKey(fileId);
    }

    var path =  await FileThumbHelper.fetchImageSignRemoteUrl(fileId);
    if(StringUtil.isEmpty(path)) return '';
    CacheHelper.saveImageByKey(fileId, path);
    return path;
  }

  Widget _buildImage() {
    var w = (message.imgWidth == null || message.imgWidth == 0) ? 100.0 : message.imgWidth!;
    var h = (message.imgHeight == null || message.imgHeight == 0) ? 100.0 : message.imgHeight!;

    if(width != null){
      w = width!;
      h = height!;
    }else {
      var prop = w/h;
      if(w < h){
        if(w > _screenW/3){
          w = _screenW/3 ;
        }
      }else{
        if(w > _screenW/2){
          w = _screenW/2 ;
        }
      }
      h = w / prop ;
    }

    if(StringUtil.isEmpty(message.localUrl)){
      w = h = 100;
    }

    return FutureBuilder(future: _loadRemoteUrl(message.fileId ?? ''), builder: (ctx ,snap){
      return Container(
        width: w,
        height: h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            ImageLoader(
              url: snap.data,
              width: w,
              height: h,
              radius: simple == true ? 0 : null,
              palaceHolder: AssetsRes.IM_IMAGE_ERROR,
              cacheWidth: w.toInt(),
              cacheHeight: h.toInt(),
              clearMemoryCacheWhenDispose: false,
            ),
          ],
        ),
      );
    });

  }
}




