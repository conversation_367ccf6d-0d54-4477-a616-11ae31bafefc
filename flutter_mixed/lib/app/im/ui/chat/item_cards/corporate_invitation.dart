

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../entity/message_item_data.dart';

// 企业邀请卡片
class CorporateInvitation extends StatelessWidget {

  final MessageInviteInTeamData messageInviteInTeamData;

  final VoidCallback? onInviteTap;

  bool? simple;

  final _screenW = ScreenUtil().screenWidth;

  CorporateInvitation(this.messageInviteInTeamData , this.onInviteTap, {this.simple});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: (){
        onInviteTap?.call();
      },
      child: _buildInviteWidget(),
    );
  }

  _buildInviteWidget() {
    if(simple == true){
      return Container(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(child: Text('[企业邀请]${messageInviteInTeamData.companyName}${messageInviteInTeamData.inviteContent}',
              style: TextStyle(fontSize: 12) ,maxLines: 1,overflow: TextOverflow.ellipsis,
            )),
            2.gap,
            ImageLoader(url: messageInviteInTeamData.logo , width: 14 ,height: 14, radius: 10, isCircle: false,),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 8),
      decoration: BoxDecoration(
        color: Color(0xffe1ecf7),
        borderRadius: BorderRadius.all(Radius.circular(5))
      ),
      constraints: BoxConstraints(maxWidth: _screenW - 150),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ImageLoader(url: messageInviteInTeamData.logo , width: 44 ,height: 44, radius: 10, isCircle: false,),
          6.gap,
          Flexible(child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('[企业邀请] ${messageInviteInTeamData.inviteContent}'
                , style: TextStyle(fontSize: 12 , color: Color(0xff323232)),),
              3.gap,
              Text('${messageInviteInTeamData.companyName}' , style: TextStyle(fontSize: 14 , color: Color(0xff2479ed)),),
            ],
          ))
        ],
      ),
    );
  }

}