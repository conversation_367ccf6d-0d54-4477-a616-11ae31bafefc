import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../common/config/config.dart';

class LocationCard extends StatelessWidget {

  final MessageLocationData locationData;

  final VoidCallback? onMapTap;

  bool? simple;

  LocationCard(this.locationData, this.onMapTap,{super.key, this.simple});

  final _screenW = ScreenUtil().screenWidth;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: (){
        onMapTap?.call();
      },
      child: _buildMap(),
    );
  }

  _buildMap() {
    if(simple == true){
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ImageLoader(url: locationData.addressImgUrl, width: 17, height: 17,radius: 0 ,
            palaceHolder: AssetsRes.IM_IMAGE_ERROR,
          )
        ],
      );
    }
    return Container(
      constraints: BoxConstraints(maxWidth: _screenW - 150),
      decoration:  BoxDecoration(
          color: Colors.white,
          border: Border.all(
              color: ColorConfig.lineColor, width: 0.5),
          borderRadius: const BorderRadius.all(Radius.circular(3))
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ImageLoader(url: locationData.addressImgUrl, height: 120, width:  _screenW - 150, radius: 3,
            boxFit: BoxFit.fitWidth,
            palaceHolder: AssetsRes.IM_IMAGE_ERROR,
          ),
          8.gap,
          Container(
            padding: const EdgeInsets.symmetric(vertical: 2 ,horizontal: 4),
            child: Text('${locationData.addressTitle}', style: const TextStyle(fontSize: 16, color: Color(0xff323232)),),
          ),
          2.gap,
          Container(
            padding: const EdgeInsets.symmetric(vertical: 1 ,horizontal: 4),
            child: Text('${locationData.addressDetail}', style: const TextStyle(fontSize: 12, color: Color(0x99999999)),),
          ),
          4.gap
        ],
      ),
    );
  }

}