
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/no_double_click_wiget.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/common/focus_switch_widget.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../res/assets_res.dart';
import '../../../../common/widgets/image_loader.dart';
import '../../../../routes/app_pages.dart';
import '../../../../utils/cache_helper.dart';
import '../../../../utils/string-util.dart';
import '../../../route/route_helper.dart';
import '../../../widget/im_item_progress_bar.dart';
import '../common/picture_manager.dart';
import '../entity/message_item_data.dart';


/// 消息类型： 视频 ----

class VideoCard extends StatefulWidget {


  final double? width;
  final double? height;
  final double? playIconSize;
  final bool? simple;

  final MessageVideoData message;

  final ChatController? chatController;

  VideoCard(this.message, {super.key , this.width , this.height , this.playIconSize , this.simple = true,
    this.chatController
  });


  @override
  State<StatefulWidget> createState() => _VideoCardState();
}

class _VideoCardState extends State<VideoCard> {

  final _screenW = ScreenUtil().screenWidth;

  @override
  Widget build(BuildContext context) {
    return _buildVideoMessage(widget.message);
  }

  Future<String> _loadRemoteUrl(String fileId) async {
    if(await CacheHelper.existImageCache(fileId)){
      return await CacheHelper.getImageByKey(fileId);
    }
    var path =  await FileThumbHelper.fetchImageSignRemoteUrl(fileId);
    if(StringUtil.isEmpty(path)) return '';
    CacheHelper.saveImageByKey(fileId, path);
    return path;
  }

  _buildVideoMessage(MessageVideoData message) {
    var w = (message.coverWidth == null || message.coverWidth == 0) ? 100.0 : message.coverWidth!;
    var h = (message.coverHeight == null || message.coverHeight == 0) ? 100.0 : message.coverHeight!;

    if(widget.width != null){
      w = widget.width!;
      h = widget.height!;
    }else {
      var prop = w/h;
      if(w > _screenW/3){
        w = _screenW/3 ;
      }
      h = w / prop ;
    }

    return NoDoubleClickGestureDetector(child: SizedBox(
      width: w ?? 100,
      height: h ?? 100,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FutureBuilder(future: _loadRemoteUrl(message.videoImageId ??''), builder: (ctx , snap){
            return ImageLoader(
              url: snap.data,
              width: w,
              height: h,
              palaceHolder: AssetsRes.IM_IMAGE_ERROR,
              cacheWidth: (w).toInt(),
              cacheHeight: (h).toInt(),
              radius: widget.simple == true ? 0 : 15,
              clearMemoryCacheWhenDispose: false,
            );
          }),

          Icon(Icons.play_circle_outline , color:const Color(0xff8a8a8a) ,
            size: widget.playIconSize ?? 40,),

          if(widget.simple == false && message.progress.showProgressBar())...[
            ProgressBar(message.progress ?? 0),
          ]
        ],
      ),
    ), tapAction: (){
      _tapVideo();
      hideKeyboard(context);

    }, tapDuration: 2);
  }

  Future _tapVideo() async {
    downloadVideo(widget.message, (url) {
      (widget.message).updateDbVideoUrl(url);

      RouteHelper.routePath(Routes.COMMON_VIDEO_PLAY,
          arguments: {
              'file': File(url),
              'msgId':widget.message.messageId()
          });
    } , existUrlCallBack: (url){

      RouteHelper.routePath(Routes.COMMON_VIDEO_PLAY,
          arguments: {
            'file': File(url),
            'msgId':widget.message.messageId()
          });

    },  progressLoad: (prop){
      if(mounted) {
        setState(() {
         widget.message.progress = prop;
       });
      }
    });
  }
}




