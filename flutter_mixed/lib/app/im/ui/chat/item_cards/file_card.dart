import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/no_double_click_wiget.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat/action/message_click_aciton.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../modules/workStand/common/focus_switch_widget.dart';
import '../../../utils/file_util.dart';
import '../../../widget/im_item_progress_bar.dart';
import '../entity/message_item_data.dart';

class FileCard extends StatefulWidget {

  final VoidCallback? onFileTap;

  final MessageFileData messageFileData;

  final bool simple;


  FileCard(this.onFileTap, this.messageFileData , {this.simple = false});

  @override
  State<StatefulWidget> createState() => _FileCardState();
}

class _FileCardState extends State<FileCard> {

  final _screenW = ScreenUtil().screenWidth;


  @override
  Widget build(BuildContext context) {
    if(widget.simple == true) return _buildSimple(widget.messageFileData);
    return _buildFileMessage(widget.messageFileData);
  }

  _buildSimple(MessageFileData message) {
    return NoDoubleClickGestureDetector(child: Container(
      constraints: BoxConstraints(maxWidth: _screenW/6),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(message.fileTypeIcon ??'' , width: 15, height: 15,),
        ],
      ),
    ),
        tapAction: (){
          widget.onFileTap?.call();
    }, tapDuration: 2);
  }

  _buildFileMessage(MessageFileData message) {
    return NoDoubleClickGestureDetector(tapAction: (){
      hideKeyboard(context);
      fileClickAction(message , progressChanged: (prop){
        setState(() {
          message.progress = prop;
        });
      });
    }, tapDuration: 2, child: Container(
      constraints: BoxConstraints(maxWidth: _screenW - 200),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                    width: 0.5, color: Colors.grey),
                borderRadius: BorderRadius.circular(3)),
            padding: const EdgeInsets.symmetric(vertical: 4 , horizontal: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(message.fileTypeIcon ??'' , width: 35, height: 35,),
                4.gap,
                Flexible(child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${message.fileName}', maxLines: 2, overflow: TextOverflow.ellipsis
                      , style: TextStyle(fontSize: 14 ,color: Color(0xff333333)),),
                    2.gap,
                    Text('${message.size}' , style: TextStyle(fontSize: 12 ,color: Color(0xff666666)),),
                  ],
                ))
              ],
            ),
          ),

          if(message.progress.showProgressBar())...[
            ProgressBar(
              message.progress ?? 0,
              radius: 15,
            ),
          ]
        ],
      ),
    ));
  }

}
