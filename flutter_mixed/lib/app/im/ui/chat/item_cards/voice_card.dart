import 'dart:async';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../../../main.dart';
import '../../../../../res/assets_res.dart';
import '../../../../common/event/event.dart';
import '../entity/message_item_data.dart';

class VoiceCard extends StatefulWidget {

  final MessageVoiceData message;

  final bool simple;

  ChatController? chatViewModel;

  VoiceCard(this.message, {super.key, this.simple = false , this.chatViewModel});

  @override
  State<StatefulWidget> createState() => _VoiceCardState();
}
class _VoiceCardState extends State<VoiceCard>  with WidgetsBindingObserver{

  PlayerController playerController = PlayerController();

  StreamSubscription? _withDrawSubscription;
  
  bool isPlaying = false;
  int  duration = 0;

  @override
  void initState() {
    super.initState();
    
    _withDrawSubscription = eventBus.on<EventWithDraw>().listen((withDrawData){
      if(widget.message.messageId() == withDrawData.message.msgId){
        try{
          playerController.stopPlayer();
        }catch(e){}
      }
    });
    WidgetsBinding.instance.addObserver(this);
    playerController.onCompletion.listen((_VoiceCardState){
        isPlaying = false;
        duration = 0;
    });
    
    playerController.onCurrentDurationChanged.listen((duration){
      this.duration = duration;
    });
    widget.chatViewModel?.appLifeStream.stream.listen((life){
      if(life == "onAppPaused" && isPlaying){
        playerController.pausePlayer();
        isPlaying = false;
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    logger("=========applife change====$state");
    // if(state == AppLifecycleState.paused && isPlaying){
    //   playerController.pausePlayer();
    //   isPlaying = false;
    // }
  }


  @override
  void deactivate() {
    logger("=========deactivate====");
  }

  @override
  Widget build(BuildContext context) {
    return _buildVoiceMessage(widget.message);
  }
  @override
  void dispose() {
    playerController.release();
    _withDrawSubscription?.cancel();
    super.dispose();
  }
  _buildVoiceMessage(MessageVoiceData message) {
    final color = message.isMyMessage ? ColorConfig.imMyBackColor : ColorConfig.imOtherBackColor;
    if(widget.chatViewModel == null) return Container();
    if(widget.simple) {
      return InkWell(
        onTap: (){
          _playAudio(widget.message);
        },
        child: Container(
          color: Colors.transparent,
          child: Text('${ImPrefixMsg.voiceCardString} ${message.duration}s' , style: TextStyle(fontSize: 12),),
        ),
      );
    }

    return InkWell(
      onTap: (){
        _playAudio(widget.message);
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 6, horizontal:10),
        color: color,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(AssetsRes.IC_VOICE_BLUE , width: 25, height: 25,),
            5.gap,
            Container(
              child: AudioFileWaveforms(size: Size(60, 20),
                waveformType: WaveformType.long,
                enableSeekGesture: false,
                playerController: playerController,
              ),
            ),
            2.gap,
            Text('${message.duration}s', style: TextStyle(fontSize: 12, color: Color(0xff666666)),)
          ],
        ),
      ),
    );
  }

  _playAudio(MessageVoiceData message){
    logger('voiceCard 播放');
    if(isPlaying){
      playerController.pausePlayer();
      isPlaying = false;
    }else{
      if(duration > 0){
        playerController.startPlayer();
        isPlaying = true;
      }else{
        widget.chatViewModel?.downloadVoice(message, (url) async {
          message.updatePlayUrl(url);
          playerController.stopPlayer();
          isPlaying = true;
          var waveformDataList =
          await playerController.extractWaveformData(path: url);
          File file = File(url);
          await playerController.preparePlayer(
              shouldExtractWaveform: true, path: file.path);
          playerController.setFinishMode(finishMode: FinishMode.stop);
          playerController.seekTo(duration);
          playerController.startPlayer();
        });
      }

    }

  }
}






