
import '../../../common/api/Define.dart';
import '../../../modules/contact/model/friend/user_model.dart';
import '../../../utils/storage.dart';

/// 好友管理
class FriendHelper {

    // 判断是否是好友
    static Future<bool> isFriend(String ownerId) async {
      var list = await getMyFriendList();
      return list.map((e) => e.userId).toList().contains(ownerId);
    }

    // 获取我的好友
    static Future<List<UserModel>> getMyFriendList() async {
      var friendList = await UserDefault.getData(Define.FRIENDLIST);
      if(friendList == null || friendList is! List) return [];
      var list = friendList.map((e) => UserModel.fromJson(e)).toList();
      return list;
    }

}