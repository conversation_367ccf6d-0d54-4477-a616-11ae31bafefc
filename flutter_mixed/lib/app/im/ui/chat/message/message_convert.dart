import 'dart:convert';

import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_group_chat_resp.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/message/bot_parse.dart';
import 'package:flutter_mixed/app/im/ui/chat/message/custom_msg_model.dart';
import 'package:flutter_mixed/app/im/utils/im_html_parser.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../../../constant/ImMsgConstant.dart';
import '../../../db/db_helper.dart';
import '../../../db/entity/message.dart';
import '../../../request/entity/chat_resp_ext.dart';
import '../../../request/entity/offline_single_chat_resp.dart';
import '../../../utils/im_global_util.dart';
import '../../session_list/session_list_controller.dart';

/// 转换为Message的其他model操作类

// 群消息接口model 转换为 Message
Future<Message> offlineGroup2Message(OfflineGroupChatResp offLineMsg,
    {String? imSessionId, bool? inChat = false}) async {
  var uid = await ImGlobalUtil.currentUserId();
  var msgFrom = (uid == offLineMsg.senderUserInfo?.userId) ? 1 : 2;
  int? isDelete;

  if (inChat == true) {
    var localMsg = await DbHelper.getChatMessageWihtMsgId(uid, offLineMsg.groupId ?? '', offLineMsg.msgId ?? '');
    if(localMsg.isNotEmpty){
      isDelete = localMsg.first.isDelete;
    }
  }

  var message = Message(offLineMsg.msgId ?? '')
    ..uid = uid
    ..cmdId = '' // 做什么用？
    ..sessionType = 2
    ..sendTime = offLineMsg.sendTime ?? 0
    ..isReaded = 0
    ..msgFrom = msgFrom
    ..isSuccess = 1 // 收到了默认即成功
    ..text = offLineMsg.msg ?? ''
    ..isDelete = isDelete;

  // 兼容群聊情况
  if (offLineMsg.senderUserInfo != null) {
    message
      ..sendId = offLineMsg.senderUserInfo?.userId
      ..sendName = offLineMsg.senderUserInfo?.nickname
      ..sendHeader = offLineMsg.senderUserInfo?.avatar ?? ''
      ..appChatId = offLineMsg.groupId ?? ''
      ..sessionId = offLineMsg.groupId ?? '';

    if (offLineMsg.senderUserInfo?.userId == uid) {
      message.msgFrom = 1;
    } else {
      message.msgFrom = 2;
    }
  }

  // 兼容单聊情况
  if (offLineMsg.userInfo != null) {
    if (offLineMsg.userInfo!.userId == uid) {
      message
        ..sessionId = imSessionId
        ..msgFrom = 1
        ..appChatId = imSessionId;
    } else {
      message
        ..sessionId = offLineMsg.userInfo?.imUserId
        ..msgFrom = 2
        ..appChatId = offLineMsg.userInfo?.userId;
    }

    message
      ..sendId = offLineMsg.userInfo?.userId
      ..sendName = offLineMsg.userInfo?.nickname
      ..sendHeader = offLineMsg.userInfo?.avatar ?? '';
  }

  var type = offLineMsg.type ?? 0;
  // 其他处理
  if (type == ConstantImMsgType.SSChatMessageTypeChatGroupNotice) {
    message.sessionType = type ?? 0;
    try {
      var notice = jsonDecode(offLineMsg.noticeMsg?.data ?? '');
      var groupNoticeDataBean = GroupNoticeDataBean.fromJson(notice);
      message.msgType = groupNoticeDataBean.msgType ?? 0;
    } catch (e) {}
  } else {
    message.sessionType = 2;
    message.msgType = type;
  }

  switch (type) {
    case ConstantImMsgType.SSChatMessageTypeInvite:
    case ConstantImMsgType.SSChatMessageTypeReportShare:
    case ConstantImMsgType.SSChatMessageTypePanShareFile:
      message.extendOne = offLineMsg.ext?.ext1 ?? '';
      var customMap = jsonDecode(offLineMsg.ext?.ext1 ?? '');
      var custom = CustomMsgModel.fromJson(customMap);
      message
        ..text = custom.text
        ..extendTwo = custom.extendTwo ?? ''
        ..extendThree = custom.extendThree ?? ''
        ..imgUrl = custom.imgUrl
        ..miniImgUrl = custom.miniImgUrl
        ..voiceUrl = custom.voiceUrl;

      break;
    case ConstantImMsgType.SSChatMessageTypeRobot:
      message.extendOne = parseGroupMessageExt1(offLineMsg);
      message.text = parseBotMsgContentFromGroupBean(offLineMsg);

      break;
    case ConstantImMsgType.SSChatMessageTypeQuote: // !! 引用消息
      try {
        var extendOne = offLineMsg.ext?.ext1 ?? ''; //保存的原始json
        message.extendOne = extendOne;
        var inner = jsonDecode(message.extendOne ?? '');
        var innerMsg = Message.fromJson(inner);
        var customMap = jsonDecode(offLineMsg.ext?.ext1 ?? '');
        var custom = CustomMsgModel.fromJson(customMap);
        message
          ..text = innerMsg.quoteText
          ..extendTwo = custom.extendTwo ?? ''
          ..extendThree = custom.extendThree ?? ''
          ..imgUrl = custom.imgUrl ?? ''
          ..miniImgUrl = custom.miniImgUrl
          ..voiceUrl = custom.voiceUrl;
      } catch (e) {
        logger('SSChatMessageTypeQuote error: $e');
      }

      break;
    case ConstantImMsgType.SSChatMessageTypeText:
      message.text = offLineMsg.msg;
      break;
    case ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice:
    case ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice:
      try{
        var customMap = jsonDecode(offLineMsg.ext?.ext1 ?? '');
        var custom = CustomMsgModel.fromJson(customMap);
        message
          ..text = custom.text
          ..extendTwo = custom.extendTwo ?? ''
          ..extendThree = custom.extendThree ?? '';
        if (inChat == true) break;
        var haveAtTag = true;
        if(type == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice){
          List ids = json.decode(custom.extendTwo??"[]", );
          if(!ids.contains(uid)){
            haveAtTag = false;
          }
        }
        if(haveAtTag && message.sessionId != null && message.sessionId!.isNotEmpty){
          if( !SessionListController.atList.contains(message.sessionId)){
            SessionListController.atList.add(message.sessionId!);
          }
          var session =
          await DbHelper.getSessionByOwnerId2SessionId(uid, message.sessionId!); // 数据库
          logger("============session111  $session===================");
          if(session != null && session.msgContent?.contains("有人@你") == false &&
            session.sessionType == ConstantImMsgType.SSChatConversationTypeGroupChat){
            session.msgContent = "<font color='#EC0A39'>[有人@你]</font>${session.msgContent}";
            DbHelper.insertSession(session);
          }
        }
      }catch(e){
        logger('群通知解析错误：$e');
      }

      break;
    case ConstantImMsgType.SSChatMessageTypeImage:
      message
        ..localUrl = ""
        ..fileId = offLineMsg.image?.imageId ?? ''
        ..imgUrl = offLineMsg.image?.url ?? ''
        ..miniImgUrl = offLineMsg.image?.thumbnailUrl
        ..imgWidth = offLineMsg.image?.width ?? 0.0
        ..imgHeight = offLineMsg.image?.height ?? 0.0
        ..cmdId = getUUid()
        ..longitude = 0;
      break;

    case ConstantImMsgType.SSChatMessageTypeVoice:
      message
        ..localUrl = ""
        ..fileId = offLineMsg.voice?.voiceId ?? ''
        ..voiceUrl = offLineMsg.voice?.url ?? ''
        ..voiceTime = offLineMsg.voice?.duration ?? 0;
      break;

    // (转发的)聊天记录
    case ConstantImMsgType.SSChatMessageTypeForwardRecord:
      message.extendOne = offLineMsg.ext?.ext1 ?? '';
      break;

    case ConstantImMsgType.SSChatMessageTypeMap:
      message
        ..longitude = offLineMsg.location?.longitude ?? 0
        ..latitude = offLineMsg.location?.latitude ?? 0
        ..addressTitle = offLineMsg.location?.title
        ..addressDetail = offLineMsg.location?.address
        ..addressImgUrl = offLineMsg.location?.uri;
      break;

    case ConstantImMsgType.SSChatMessageTypeVideo:
      message
        ..localUrl = ''
        ..imgUrl = offLineMsg.video?.url ?? ''
        ..fileName = '${offLineMsg.msgId ?? ''}.mp4'
        ..fileSize = offLineMsg.video?.fileSize ?? 0
        ..fileId = offLineMsg.video?.fileId ?? ''
        ..voiceTime = offLineMsg.video?.duration ?? 0
        ..videoImageId = offLineMsg.video?.cover?.imageId ?? ''
        ..videoImagePath = offLineMsg.video?.cover?.url
        ..miniImgUrl = offLineMsg.video?.cover?.thumbnailUrl
        ..imgWidth = offLineMsg.video?.cover?.width ?? 0
        ..imgHeight = offLineMsg.video?.cover?.height ?? 0
        ..longitude = 0.0;
      break;
    case ConstantImMsgType.SSChatMessageTypeGifImage:
      break;
    case ConstantImMsgType.SSChatMessageTypeFile:
      message
        ..localUrl = ""
        ..fileId = offLineMsg.file?.fileId ?? ''
        ..fileSize = offLineMsg.file?.fileSize ?? 0
        ..fileName = offLineMsg.file?.name ?? ''
        ..imgUrl = offLineMsg.file?.url ?? ''
        ..longitude = 0.0;
      break;
    case ConstantImMsgType.SSChatMessageTypeChatGroupNotice:
      // 解析具体群通知类型
      _parseGroupNotice(offLineMsg, message, uid);
      break;

    default:
      break;
  }
  return message;
}

_parseGroupNotice(
    OfflineGroupChatResp offLineMsg, Message message, String userId) {
  switch (message.msgType) {
    //建群或者邀请
    case ConstantImMsgType.SSChatMessageTypeChatGroupInvitation:
      var noticeData = jsonDecode(offLineMsg.noticeMsg?.data ?? '');
      var notice = GroupNoticeDataBean.fromJson(noticeData);
      //
      Iterable l = jsonDecode(notice.content ?? '');
      List<InviteGroupNoticeContentBean> noticeList =
          List<InviteGroupNoticeContentBean>.from(
              l.map((model) => InviteGroupNoticeContentBean.fromJson(model)));
      String sb = '';
      noticeList.forEach((element) {
        sb += '${element.name},';
      });
      if (StringUtil.isEmpty(sb)) return;
      var content = sb.getRange(0, sb.length - 1);
      if (notice.userId == userId) {
        message.text = "<font color='#4D91EF'>你</font>" +
            "邀请" +
            "<font color='#4D91EF'>$content</font>" +
            "加入群组";
      } else {
        message.text = "<font color='#4D91EF'>${notice.name}</font>" +
            "邀请" +
            "<font color='#4D91EF'>$content</font>" +
            "加入群组";
      }
      break;
    case ConstantImMsgType.SSChatMessageTypeChatGroupOwnerConfigInfo: // 群主配置信息
    case ConstantImMsgType.SSChatMessageTypeChatGroupNameChange: //修改群名称
    case ConstantImMsgType.SSChatMessageTypeChatGroupCreateChange:
      var noticeData = jsonDecode(offLineMsg.noticeMsg?.data ?? '');
      var notice = GroupNoticeDataBean.fromJson(noticeData);
      Iterable l = jsonDecode(notice.content ?? '');
      List<ReNameReCreaterContentBean> noticeList =
          List<ReNameReCreaterContentBean>.from(
              l.map((model) => ReNameReCreaterContentBean.fromJson(model)));
      if (noticeList != null && noticeList.isNotEmpty) {
        message.text = ImHtmlParser.dealNoticeContent(noticeList);
      } else {
        message.text = '';
      }

      break;
    case ConstantImMsgType.SSChatMessageTypeChatGroupOrganizationAdd: //团队群组加入
      message.text = offLineMsg.noticeMsg?.context ?? '';
      break;
    case ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo:
        var noticeData = jsonDecode(offLineMsg.noticeMsg?.data ?? '');
        var videoBean = GroupVoiceVideoReceiveMsgBean.fromJson(noticeData);
        message.sendId = videoBean.sendId ?? "";
        message.sendName = videoBean.content ?? "";
        message.callType = videoBean.callType ?? 0;
        message.callContent = videoBean.content ?? "";
        message.meetingId = videoBean.meetingId ?? "";
        message.msgType = int.parse((videoBean.msgType ?? '0'));
        message.extendTwo = videoBean.personList ?? "";
        message.extendOne = offLineMsg.noticeMsg?.data ?? '';
        var tempStr = '发起了音视频通话';
        message.voiceUrl = tempStr;
        if (message.sendId == userId) {
          message.text = '你$tempStr';
        } else {
          message.text = '${videoBean.content}$tempStr';
        }
        break;
    case ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd:
        var noticeData = jsonDecode(offLineMsg.noticeMsg?.data ?? '');
        var videoBean = GroupVoiceVideoReceiveMsgBean.fromJson(noticeData);
        message.callType = videoBean.callType ?? 0;
        message.callContent = videoBean.content ?? "";
        message.meetingId = videoBean.meetingId ?? "";
        message.msgType = int.parse((videoBean.msgType ?? '0'));
        message.text = videoBean.content ?? "";
        break;
    case ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate:
        var noticeData = jsonDecode(offLineMsg.noticeMsg?.data ?? '');
        var videoBean = GroupVoiceVideoReceiveMsgBean.fromJson(noticeData);
        message.sendId = videoBean.sendId ?? "";
        message.sendName = videoBean.content ?? "";
        message.callType = videoBean.callType ?? 0;
        message.callContent = videoBean.content ?? "";
        message.meetingId = videoBean.meetingId ?? "";
        message.msgType = int.parse((videoBean.msgType ?? '0'));
        message.extendTwo = videoBean.personList ?? "";
        message.extendOne = offLineMsg.noticeMsg?.data ?? '';
        var tempStr = '发起了音视频通话';
        message.voiceUrl = tempStr;
        if (message.sendId == userId) {
          message.text = '你$tempStr';
        } else {
          message.text = '${videoBean.content}$tempStr';
        }
      break;
    default:
      break;
  }
}

//----------------------------------------------------------------
// 单聊离线消息实体 转换为 Message
Future<Message> offlineSingle2Message(OfflineSingleChatResp offLineMsg,
    String imSessionId, String? targetId) async {
  var uid = await ImGlobalUtil.currentUserId();
  var msgFrom = (uid == offLineMsg.userInfo?.userId) ? 1 : 2;
  var sessionId = offLineMsg.userInfo?.userId == uid
      ? imSessionId
      : offLineMsg.userInfo?.imUserId;

  int? isDelete;
  var localMsg = await DbHelper.getChatMessageWihtMsgId(uid, sessionId ?? '', offLineMsg.msgId ?? '');
  if(localMsg.isNotEmpty){
    isDelete = localMsg.first.isDelete;
  }

  var appChatId = offLineMsg.userInfo?.userId == uid
      ? targetId
      : offLineMsg.userInfo?.userId;
  var message = Message(offLineMsg.msgId ?? '')
    ..uid = uid
    ..cmdId = '' // 做什么用？
    ..sessionType = 1
    ..sendId = offLineMsg.userInfo?.userId
    ..sendName = offLineMsg.userInfo?.nickname
    ..sendTime = offLineMsg.sendTime ?? 0
    ..sendHeader = offLineMsg.userInfo?.avatar ?? ''
    ..isReaded = 0
    ..msgFrom = msgFrom
    ..isSuccess = 1 // 收到了默认即成功
    ..text = offLineMsg.msg ?? ''
    ..isDelete = isDelete
    ..appChatId = appChatId ?? ''
    ..msgType = offLineMsg.type ?? 0
    ..sessionId = sessionId;

  var type = offLineMsg.type ?? 0;

  switch (type) {
    case ConstantImMsgType.SSChatMessageTypeInvite:
    case ConstantImMsgType.SSChatMessageTypeReportShare:
    case ConstantImMsgType.SSChatMessageTypePanShareFile:

      try{
        message.extendOne = offLineMsg.ext?.ext1 ?? '';
        logger('type : $type -----> ${offLineMsg.ext?.ext1 ?? ''}');

        var customMap = jsonDecode(offLineMsg.ext?.ext1 ?? '');
        var custom = CustomMsgModel.fromJson(customMap);
        message
          ..text = custom.text
          ..extendTwo = custom.extendTwo ?? ''
          ..extendThree = custom.extendThree ?? ''
          ..imgUrl = custom.imgUrl
          ..miniImgUrl = custom.miniImgUrl
          ..voiceUrl = custom.voiceUrl;
      }catch(e){

      }


      break;

    // (转发的)聊天记录
    case ConstantImMsgType.SSChatMessageTypeForwardRecord:
      message.extendOne = offLineMsg.ext?.ext1 ?? '';
      break;

    case ConstantImMsgType.SSChatMessageTypeRobot:
      message.extendOne = parseSingleMessageExt1(offLineMsg);
      message.text = parseBotMsgContentFromSingleBean(offLineMsg);

      break;

    case ConstantImMsgType.SSChatMessageTypeQuote: // !! 引用消息
      message.extendOne = offLineMsg.ext?.ext1 ?? ''; //保存的原始json
      logger('离线引用类型 ext1 ： ${message.extendOne}');
      var txt = '';
      CustomMsgModel? custom;

      if (!StringUtil.isEmpty(message.extendOne)) {
        var inner = jsonDecode(message.extendOne ?? '');
        var innerMsg = Message.fromJson(inner);
        txt = innerMsg.quoteText ?? '';

        var customMap = jsonDecode(offLineMsg.ext?.ext1 ?? '');
        custom = CustomMsgModel.fromJson(customMap);
      } else {
        logger('接收引用类型的数据时候，出现数据错误');
      }

      message
        ..text = txt
        ..extendTwo = custom?.extendTwo ?? ''
        ..extendThree = custom?.extendThree ?? ''
        ..imgUrl = custom?.imgUrl ?? ''
        ..miniImgUrl = custom?.miniImgUrl
        ..voiceUrl = custom?.voiceUrl;
      break;
    case ConstantImMsgType.SSChatMessageTypeText:
      message.text = offLineMsg.msg;
      break;

    case ConstantImMsgType.SSChatMessageTypeImage:
      message
        ..localUrl = ""
        ..fileId = offLineMsg.image?.imageId ?? ''
        ..imgUrl = offLineMsg.image?.url ?? ''
        ..miniImgUrl = offLineMsg.image?.thumbnailUrl
        ..imgWidth = offLineMsg.image?.width ?? 0.0
        ..imgHeight = offLineMsg.image?.height ?? 0.0
        ..cmdId = getUUid()
        ..longitude = 0.0;
      break;

    case ConstantImMsgType.SSChatMessageTypeVoice:
      message
        ..localUrl = ""
        ..fileId = offLineMsg.voice?.voiceId ?? ''
        ..voiceUrl = offLineMsg.voice?.url ?? ''
        ..voiceTime = offLineMsg.voice?.duration ?? 0;
      break;

    case ConstantImMsgType.SSChatMessageTypeMap:
      message
        ..longitude = offLineMsg.location?.longitude ?? 0
        ..latitude = offLineMsg.location?.latitude ?? 0
        ..addressTitle = offLineMsg.location?.title
        ..addressDetail = offLineMsg.location?.address
        ..addressImgUrl = offLineMsg.location?.uri;
      break;

    case ConstantImMsgType.SSChatMessageTypeVideo:
      message
        ..localUrl = ''
        ..imgUrl = offLineMsg.video?.url ?? ''
        ..fileName = '${offLineMsg.msgId ?? ''}.mp4'
        ..fileSize = offLineMsg.video?.fileSize ?? 0
        ..fileId = offLineMsg.video?.fileId ?? ''
        ..voiceTime = offLineMsg.video?.duration ?? 0
        ..videoImageId = offLineMsg.video?.cover?.imageId ?? ''
        ..videoImagePath = offLineMsg.video?.cover?.url
        ..miniImgUrl = offLineMsg.video?.cover?.thumbnailUrl
        ..imgWidth = offLineMsg.video?.cover?.width ?? 0
        ..imgHeight = offLineMsg.video?.cover?.height ?? 0
        ..longitude = 0.0;
      break;
    case ConstantImMsgType.SSChatMessageTypeGifImage:
      break;
    case ConstantImMsgType.SSChatMessageTypeFile:
      message
        ..localUrl = ""
        ..fileId = offLineMsg.file?.fileId ?? ''
        ..fileSize = offLineMsg.file?.fileSize ?? 0
        ..fileName = offLineMsg.file?.name ?? ''
        ..imgUrl = offLineMsg.file?.url ?? ''
        ..longitude = 0;
      break;

    case ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo:
      if (offLineMsg.call != null) {
        var nameStr = offLineMsg.call!.content;
        message.callType = offLineMsg.call!.type;
        message.callContent = offLineMsg.call!.content;
        message.meetingId = offLineMsg.call!.metingId;
        var typeStr = '语音';
        if (message.callType == 2) {
          typeStr = '视频';
        }
        var contentStr = '发起了$typeStr通话';
        if (message.sendId == uid) {
          nameStr = '你';
        }
        message.text = '$nameStr$contentStr';
        message.voiceUrl = contentStr;
      }
      break;
    case ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd:
      if (offLineMsg.call != null) {
        message.callType = offLineMsg.call!.type;
        message.callContent = offLineMsg.call!.content;
        message.meetingId = offLineMsg.call!.metingId;
        var typeStr = '语音';
        if (message.callType == 2) {
          typeStr = '视频';
        }
        message.text = '$typeStr${message.callContent}';
      }
      break;
    case ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure:
      if (offLineMsg.call != null) {
        message.callType = offLineMsg.call!.type;
        message.callContent = offLineMsg.call!.content;
        message.meetingId = offLineMsg.call!.metingId;
        var typeStr = '语音';
        if (message.callType == 2) {
          typeStr = '视频';
        }
        message.text = '$typeStr通话-${message.callContent}';
      }
      break;
    default:
      break;
  }
  return message;
}

List<Message> isShowGapTime(List<Message> sourceList) {
  // 按时间升序排列（确保上边时间戳小，下边时间戳大）
  sourceList.sort((a, b) => (a.sendTime ?? 0).compareTo(b.sendTime ?? 0));

  int lastTime = 0;
  // 遍历消息列表
  for (var i = 0; i < sourceList.length; i++) {
    Message message = sourceList[i];
    if (i == 0) {
      lastTime = message.sendTime ?? 0;
      sourceList[i].showGapTime = true;
    } else {
      final diff = (message.sendTime ?? 0) - lastTime;
      if (diff > 5 * 60 * 1000) {
        lastTime = message.sendTime ?? 0;
        message.showGapTime = true;
      }
    }
  }
  return sourceList;
}

List<MessageItem> isShowGapTimeForMessageItem(List<MessageItem> sourceList){
    sourceList.sort((a, b){
      if (a is MessageItemData && b is MessageItemData) {
         return (a.message.sendTime ?? 0).compareTo(b.message.sendTime ?? 0);
      }
      return 0;
    });
    int lastTime = 0;
    // 遍历消息列表
    for (var i = 0; i < sourceList.length; i++) {
      var message = sourceList[i];
      if (message is MessageItemData) {
        if (i == 0) {
          lastTime = message.message.sendTime ?? 0;
          message.message.showGapTime = true;
        } else {
          final diff = (message.message.sendTime ?? 0) - lastTime;
          if (diff > 5 * 60 * 1000) {
            lastTime = message.message.sendTime ?? 0;
            message.message.showGapTime = true;
          }
        }
      }
    }
    return sourceList;
}

