

class CustomMsgModel {
  String? text;
  String? extendOne;
  String? extendTwo;
  String? extendThree;
  String? imgUrl;
  String? miniImgUrl;
  String? voiceUrl;

  CustomMsgModel({this.text, this.extendOne, this.extendTwo, this.extendThree,
    this.imgUrl, this.miniImgUrl, this.voiceUrl});

  factory CustomMsgModel.fromJson(Map<String, dynamic> json) {
    return CustomMsgModel(
      text: json['text'],
      extendOne: json['extendOne'],
      extendTwo: json['extendTwo'],
      extendThree: json['extendThree'],
      imgUrl: json['imgUrl'],
      miniImgUrl: json['miniImgUrl'],
      voiceUrl: json['voiceUrl'],
    );
  }

  Map<String, dynamic> toJson() => {
        'text': text,
        'extendOne': extendOne,
        'extendTwo': extendTwo,
        'extendThree': extendThree,
        'imgUrl': imgUrl,
        'miniImgUrl': miniImgUrl,
        'voiceUrl': voiceUrl,
      };
}