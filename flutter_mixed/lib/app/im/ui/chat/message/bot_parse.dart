import 'dart:convert';

import 'package:flutter_mixed/app/im/request/entity/chat_resp_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_single_chat_resp.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../../../request/entity/offline_group_chat_resp.dart';

/// 机器人消息的解析

String parseSingleMessageExt1(OfflineSingleChatResp offLineMsg) {
  if (StringUtil.isEmpty(offLineMsg.ext?.ext1)) {
    return offLineMsg.noticeMsg?.data ?? "";
  }
  var ext1 = offLineMsg.ext?.ext1 ?? "";
  return ImBotExtEntity.fromJson(json.decode(ext1))?.data?? '';
}

String parseGroupMessageExt1(OfflineGroupChatResp offLineMsg) {
  if (StringUtil.isEmpty(offLineMsg.ext?.ext1)) {
    return offLineMsg.noticeMsg?.data ?? "";
  }
  var ext1 = offLineMsg.ext?.ext1 ?? "";
  return ImBotExtEntity.fromJson(json.decode(ext1))?.data?? '';
}

String parseBotMsgContentFromGroupBean(OfflineGroupChatResp offLineMsg) {
  if (StringUtil.isEmpty(offLineMsg.ext?.ext1)){
    return offLineMsg.noticeMsg?.context ?? "";
  }else {
  var ext1 = offLineMsg.ext?.ext1 ?? '';
  return _parseBotMsgContent(ext1);
  }
}

String parseBotMsgContentFromSingleBean(OfflineSingleChatResp offLineMsg) {
  if (StringUtil.isEmpty(offLineMsg.ext?.ext1)){
    return offLineMsg.noticeMsg?.context ?? "";
  }else {
    var ext1 = offLineMsg.ext?.ext1 ?? '';
    return _parseBotMsgContent(ext1);
  }
}

String _parseBotMsgContent(String ext1){
  try{
    return ImBotExtEntity.fromJson(json.decode(ext1))?.context ?? '';
  }catch(e){
    return ClientNoticeMsg.fromJson(json.decode(ext1))?.context ?? '';
  }
}

class ImBotExtEntity {

   String? data ;
   String? context;

  ImBotExtEntity({this.data, this.context});

  factory ImBotExtEntity.fromJson(Map<String, dynamic> json) {
    return ImBotExtEntity(data: json['data'], context: json['context'],);
  }

  Map<String, dynamic> toJson() => {'data': data, 'context': context,};

}