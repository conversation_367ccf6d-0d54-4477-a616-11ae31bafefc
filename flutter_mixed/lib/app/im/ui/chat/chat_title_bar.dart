

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../common/channel/channel.dart';
import '../../../common/config/config.dart';
import '../../../common/widgets/theme.dart';


class ChatTitleBar extends StatelessWidget {

  final String title;

  bool showLoading = false;

  int? groupMemberCount = 0;

  bool isGroup = false;

  Function? onBackPressed;

  VoidCallback? rightClick;

  Widget body;

  bool? showRightIcon;

  ChatTitleBar(this.title , this.showLoading , this.groupMemberCount , this.isGroup, this.body ,{super.key, this.onBackPressed , this.showRightIcon = true,
    this.rightClick,
  } );

  @override
  Widget build(BuildContext context) {
    SystemUiOverlayStyle overlayStyle =  const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    );

    return Scaffold(
        backgroundColor:  ColorConfig.backgroundColor,
      resizeToAvoidBottomInset: true,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(50),
            child: AppBar(
                title: _buildTitle(title , showLoading,  groupMemberCount: groupMemberCount),
                centerTitle: true,
                leading: IconButton(
                  onPressed: () {
                    if (onBackPressed == null) {
                      Get.back();
                    } else {
                      onBackPressed!();
                    }
                  },
                  icon: SizedBox(
                    width: 24,
                    height: 24,
                    child: Image.asset('assets/images/3.0x/pic_return.png'),
                  ),
                ),
                // backgroundColor: const Color(0xF1FFFFFF),
                backgroundColor: Colors.white,
                elevation: 0.5,
                systemOverlayStyle: overlayStyle,
                actions: [
                  _buildRightIcon()
                ])),
        body: SafeArea(child: body),
    );
  }

  _buildRightIcon() {
    if(showRightIcon == true){
      return IconButton(onPressed: (){
        rightClick?.call();
      }, icon: Image.asset(AssetsRes.IM_CHAT_MORE, width: 24,));
    }
    return const SizedBox();
  }

  Widget _buildTitle(String title, bool showLoading, {int? groupMemberCount = 0}) {
    return Container(
      // height: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(title ?? '' , style: TextStyle(fontSize: 16 ,color: Color(0xff323232), height: 1.0,fontWeight: FontWeight.w500), maxLines: 1, ),
          Text((groupMemberCount ?? 0) > 0 ? "(${groupMemberCount ?? 0})" : "" , style: TextStyle(fontSize: 16 ,color: Color(0xff323232),fontWeight: FontWeight.w500),),
          3.gap,
          showLoading ? const CupertinoActivityIndicator() : Container()
        ],
      ),
    );
  }


}
