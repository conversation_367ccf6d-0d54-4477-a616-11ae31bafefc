import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/action/message_click_aciton.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/chat/common/picture_manager.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data_ext.dart';
import 'package:flutter_mixed/app/im/utils/file_util.dart';
import 'package:flutter_mixed/app/modules/gallery/gallery.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

import '../../../common/widgets/widgets.dart';
import '../../../permission/permission_collection_dialog.dart';
import '../../../../logger/logger.dart';
import '../../db/db_helper.dart';
import '../../route/route_helper.dart';
import 'entity/message_item_data.dart';
import 'message_bubble.dart';

/// 消息条目 item
/// 区分普通消息（文本图片视频引用等常规类型）、系统消息、 撤回消息、 时间间隔线； 注意Ui区分
class MessageItemContainer extends StatelessWidget {
  int index;

  MessageItem message;

  ChatController? chatController;

  MessageItemContainer(this.index, this.message, {this.chatController});

  _buildItemCard(Widget child) {
    if (!(message as MessageItemData).message.showGapTime) {
      return child;
    }
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 20),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 1),
          decoration: BoxDecoration(
              color: ColorConfig.imOtherBackColor, borderRadius: BorderRadius.circular(8)),
          child: Text('${(message as MessageItemData).sendTime()}',
              style: const TextStyle(fontSize: 12, color: Color(0xff666666))),
        ),
        child,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (message is MessageOfflineFault) {
      return Container();
    }

    // 系统消息
    if (message is MessageSysData) {
      return _buildItemCard(Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 0),
        child: Center(
            child: Html(
              data: '''
              <div style="text-align: center;">
                ${(message as MessageSysData).body ?? ''}
              </div>
            ''',
            )),
      ));
    }

    // 撤回消息
    if (message is MessageUndoData) {
      return Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 20, bottom: 0),
        child: Center(
          child: Text((message as MessageUndoData).body ?? "",
              style: const TextStyle(fontSize: 12, color: Color(0x99000000))),
        ),
      );
    }

    // 时间
    if (message is MessageTimeData) {
      return Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 20, bottom: 0),
        child: Center(
          child: Text((message as MessageTimeData).sendTime(),
              style: const TextStyle(fontSize: 12, color: Color(0x99000000))),
        ),
      );
    }
    if (message is MessageItemData) {
      return Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 20, bottom: 0),
        child: MessageBubble(
            chatViewModel: chatController,
            message: message as MessageItemData,
            index: index,
            onImageTap: () {
              _onImageTap();
            },
            onVideoTap: () {
              // do nothing 视频状态内部管理
            },
            onFileTap: () {
              _onFileTap();
            },
            onVoiceTap: () {
              _onVoice();
            },
            onInviteTap: () {
              _onInviteTap();
            },
            onMapTap: () {
              _onMapTap();
            },
            onRecordTap: () {
              // 聊天记录点击
              RouteHelper.routePath(Routes.CHAT_RECORD,
                  arguments: {'msg': message}, preventDuplicates: false);
            },
            onBotTap: () {
              // 【机器人消息】点击
              logger('=======机器人消息===点击');
              if (message is MessageBotUIData) {
                logger('=======机器人消息===判断');
                var bot = (message as MessageBotUIData);
                logger('=======机器人消息===json==${bot.message.toJson()}');
                bot.parseAndOpenLink();
              }
            },
            onErrorTap: () {
              // 重发消息
              chatController?.reSendMsg(message);
            },
            onRecallTap: () {
              // 撤回
              var currentMsg = message as MessageItemData;
              if (currentMsg.canRecall) {
                chatController?.sendWithDraw(currentMsg);
              } else {
                toast('Time out');
              }
            },
            onReplyTap: () {
              // _chatListViewModel.addReplyData(message);
              // FocusScope.of(context).requestFocus(messageInputFocusNode);
              // messageInputFocusNode.requestFocus();
            },
            onAvatarTap: () {
              // 点击头像
              var model = message as MessageItemData;
              RouteHelper.route(Routes.USER_INFO, arguments: {
                'userId': model.message.sendId,
                'type': model.message.sessionType,
                'fromSessionId':model.message.sessionId
              }); //todo...type缺少企业群组的判断 
            },
            onLinkTap: (url) {
              // 点击链接
              chatController?.openLinkPage(url);
            },
            onMultiSelectTap: () async {
              // 点击了多选
              await chatController?.startMultiSelected(message , context);
            },
            onSaveImageTap: () async {
              // 长按菜单里保存图片
              await chatController?.saveImage(message);
            },
            onForwardTap: () {
              // 点击了转发(单条)
              chatController?.transLate((message as MessageItemData).message);
            },
            onReplyViewTap: () {
              // 引用类型中，引用内容的点击

            },
            onQuoteTap: () async {
              logger(
                  '点击了引用，所引用的Message为： ${(message as MessageItemData).messageId()} 类型为： ${(message as MessageItemData).message.msgType}');
              chatController?.createQuoteIntMessage(message as MessageItemData);
            },
            onDeleteTap: () {
              showSimpleDialog(context, title: "确定要删除所选消息么",
                  confirmCallBack: () async {
                if (message is MessageItemData) {
                  int deleTime = 0;
                  var currentSession = await DbHelper.getSessionByOwnerId2SessionId(chatController?.ownerId ?? '', (message as MessageItemData).message.sessionId ?? '');
                  if ((message as MessageItemData).message.sendTime == currentSession?.msgTime) {
                    deleTime = currentSession?.msgTime ?? 0;
                  }
                  await DbHelper.deleteMsg((message as MessageItemData).message);
                  var lastMsg = await DbHelper.getLastMsgBySessionId(chatController?.ownerId ?? '', (message as MessageItemData).message.sessionId ?? '' );
                  String sessionMsgContent = "";
                  if (lastMsg != null) {
                    sessionMsgContent = lastMsg.alias();
                  } else {
                    sessionMsgContent = '';
                  }
      
                  currentSession?.msgContent = sessionMsgContent;
                  currentSession?.msgType = lastMsg?.msgType ?? 0;
                  currentSession?.msgTime = lastMsg?.sendTime ?? 0;
                  if (deleTime > 0) {
                    if ((currentSession?.deleteLastTime ?? 0) < deleTime) {
                      currentSession?.deleteLastTime = deleTime;
                    }
                  }
                  if (currentSession != null) {
                    await DbHelper.insertSession(currentSession);
                  }
                }
              });
            },
            onToText: () {},
            onCallTap: () {
              //点击了音视频卡片
              chatController
                  ?.didClickCall((message as MessageItemData).message.callType);
            },
            itemClick: (selected) {
              chatController?.itemClick(message, selected);
            }),
      );
    }
    return const SizedBox();
  }

  // 点击去查看预览/原图
  _onImageTap() async {
    if (message is MessageImageData) {
      var body = (message as MessageImageData);
      var fileIdFileExist = await FileThumbHelper.isThumbExist(body.message.fileId);
      logger('当前点击图片:${body.fileId}');
      if (StringUtil.isEmpty(body.fileId)) {
        toast('图片找不到');
        return;
      }
      var imagelist = chatController?.chatMessages
              .where((e) => e is MessageImageData)
              .toList() ?? [];

      List<MessageImageData> imagelist1 = imagelist.map((e) => e as MessageImageData).toList();

      var galleryList = <GalleryItem>[];
      var index = 0;

      if (body.isRecord) {
        var m = (message as MessageImageData).message;
        var previewPath = await FileThumbHelper.previewPathByFileId(m.fileId ?? '');
        galleryList.add(GalleryItem(m.msgId, previewPath)..fileId = m.fileId);
      } else {
        for (int i = 0; i < imagelist1.length; i++) {
          if((message as MessageImageData).messageId() == imagelist1[i].messageId()){
             index = i;
             break;
          }
        }

        await Future.forEach(imagelist, (e) async {
          var m = (e as MessageImageData).message;
          var previewPath = await FileThumbHelper.previewPathByFileId(m.fileId ?? '');
          galleryList.add(GalleryItem(m.msgId, previewPath)..fileId = m.fileId);
        });
      }

      Navigator.push(Get.context!,
          PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
        return FadeTransition(
            opacity: animation, child: GalleryPage(galleryList, index));
      }));
    }
  }

  _onFileTap() {
    if (message is MessageFileData) {
      chatController?.downloadFile(message as MessageFileData, (url) {
        (message as MessageFileData).updateDbFileUrl(url);

        FileUtil.openFile(url);
      });
    }
  }

  _onVoice() {
    logger(
        '=====message is MessageVoiceData =====> ${message is MessageVoiceData}====chatController====$chatController');
    if (message is MessageVoiceData) {
      chatController?.downloadVoice(message as MessageVoiceData, (url) async {
        (message as MessageVoiceData).updatePlayUrl(url);
        chatController?.playerController.stopPlayer();
        var waveformDataList = await chatController?.playerController
            .extractWaveformData(path: url);

        File file = File(url);
        await chatController?.playerController
            .preparePlayer(shouldExtractWaveform: true, path: file.path);

        chatController?.playerController
            .setFinishMode(finishMode: FinishMode.pause);
        chatController?.playerController.seekTo(0);
        chatController?.playerController.startPlayer();
      });
    }
  }

  _onInviteTap() async {
    if (message is MessageInviteInTeamData) {
      var r = await chatController
          ?.openCompanyInvite((message as MessageInviteInTeamData).companyId);
      if (r == null) return;
      RouteHelper.routePath(Routes.ORG_DETAIL, arguments: {'model': r});
    }
  }

  _onMapTap() async {
    if (message is MessageLocationData) {
      var location = message as MessageLocationData;
      logger('点击地图 $location');

      mapClickAction(location);
    }
  }

}