import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/no_double_click_wiget.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/item_cards/video_card.dart';
import 'package:flutter_mixed/app/im/ui/chat/message_audio_video_call_view.dart';
import 'package:flutter_mixed/app/im/ui/chat/pop/custom_pop_up_menu.dart';
import 'package:flutter_mixed/app/im/ui/chat/pop/pop_menu_cache_manager.dart';
import 'package:flutter_mixed/app/im/ui/chat/reply/message_reply_view.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../common/config/config.dart';
import '../../../utils/string-util.dart';
import 'item_cards/corporate_invitation.dart';
import 'item_cards/file_card.dart';
import 'item_cards/image_card.dart';
import 'item_cards/location_card.dart';
import 'item_cards/voice_card.dart';
import 'message_bot_card.dart';
import 'message_image_preview.dart';
import 'message_longpress_menu.dart';
import 'message_rich_text.dart';

/// 左右 的聊天【气泡】
class MessageBubble extends StatelessWidget {
  final ChatController? chatViewModel;
  final MessageItemData message;
  final VoidCallback? onImageTap;
  final VoidCallback? onVideoTap;
  final VoidCallback? onFileTap;
  final VoidCallback? onVoiceTap;
  final VoidCallback? onInviteTap;
  final VoidCallback? onMapTap;

  final VoidCallback? onAvatarTap;
  final VoidCallback? onErrorTap;

  final VoidCallback? onRecallTap;
  final VoidCallback? onReplyTap;
  final ValueChanged<String>? onLinkTap;

  final VoidCallback? onForwardTap;
  final VoidCallback? onQuoteTap;
  final VoidCallback? onDeleteTap;
  final VoidCallback? onMultiSelectTap;
  final VoidCallback? onSaveImageTap;
  final VoidCallback? onToText;

  final Function(bool)? itemClick;

  final VoidCallback? onReplyViewTap;

  final VoidCallback? onRecordTap;
  final VoidCallback? onBotTap;
  final VoidCallback? onCallTap;

  final int index;

  MessageBubble({
    Key? key,
    required this.chatViewModel,
    required this.message,
    required this.index,
    this.onImageTap,
    this.onVideoTap,
    this.onFileTap,
    this.onVoiceTap,
    this.onInviteTap,
    this.onMapTap,
    this.onErrorTap,
    this.onRecallTap,
    this.onReplyTap,
    this.onAvatarTap,
    this.onLinkTap,
    this.onForwardTap,
    this.onReplyViewTap,
    this.onQuoteTap,
    this.onRecordTap,
    this.onBotTap,
    this.onDeleteTap,
    this.onMultiSelectTap,
    this.onSaveImageTap,
    this.onToText,
    this.itemClick,
    this.onCallTap
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isMyMessage = message.isMyMessage;
    bool showSenderName = message.showSenderName;
    if (isMyMessage) {
      // 自己发的 *************
      return _buildMultiSelectCard(
          _buildItemCard(_buildMessageContentClickShowTime(Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildNameAndMessageContent(
              context, this.message, isMyMessage, showSenderName),
          SizedBox(
            width: 10,
          ),
          _buildAvatar(message.avatar, cleanCache: false),
        ],
      ))));
    } else {
      // 别人发的 *****************
      return _buildMultiSelectCard(
          _buildItemCard(_buildMessageContentClickShowTime(Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAvatar(message.avatar, cleanCache: false),
          SizedBox(
            width: 10,
          ),
          _buildNameAndMessageContent(
              context, this.message, isMyMessage, showSenderName)
        ],
      ))));
    }
  }

  _buildItemCard(Widget child) {
    if (!message.message.showGapTime) {
      return child;
    }
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 10, bottom: 10),
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 1),
          decoration: BoxDecoration(
               borderRadius: BorderRadius.circular(8)),
          child: Text('${message.sendTime()}',
              style: const TextStyle(fontSize: 12, color: ColorConfig.desTextColor)),
        ),
        child,
      ],
    );
  }

  // 多选容器
  _buildMultiSelectCard(Widget child) {
    if (chatViewModel?.isMultiSelected == true && message.canSelect) {
      return InkWell(
        onTap: () {
          itemClick?.call(!message.selected);
        },
        child: Row(
          crossAxisAlignment: message.message.showGapTime ? CrossAxisAlignment.center
              : CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(top: message.message.showGapTime ? 20 : 0),
              padding: EdgeInsets.only(right: 10),
              width: 30,
              height: 30,
              child: Checkbox(
                  value: message.selected,
                  activeColor: ColorConfig.themeCorlor,
                  side: const BorderSide(color: ColorConfig.lineColor,width: 1),
                  onChanged: (selected) {
                    itemClick?.call(selected ?? false);
                  }),
            ),
            Expanded(
                child: AbsorbPointer(
              absorbing: true,
              child: child,
            ))
          ],
        ),
      );
    }
    return child;
  }

  // 在聊天记录形式展示的时候，需要显示时间
  Widget _buildSendTime(MessageItemData message) {
    if (!message.isRecord) {
      return SizedBox();
    }
    return Container(
      padding: EdgeInsets.only(bottom: 8),
      child: Text(
        '${message.sendTime()}',
        style: TextStyle(fontSize: 12, color: Color(0xff666666)),
      ),
    );
  }

  //创建头像
  Widget _buildAvatar(String? url, {bool cleanCache = true}) {
    return GestureDetector(
      onTap: onAvatarTap,
      child: ImageLoader(
        url: url,
        width: 40,
        height: 40,
        radius: 10,
        clearMemoryCacheWhenDispose: cleanCache,
      ),
    );
  }

  Widget _buildNameAndMessageContent(BuildContext context,
      MessageItemData message, bool isMyMessage, bool showSenderName) {
    String usrName = message.author ?? "";
    return Expanded(
        child: Column(
      crossAxisAlignment:
          isMyMessage ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        //如果不是自己发的消息， 显示name, 是自己发的显示me
        Row(
          children: [
            !showSenderName
                ? Container()
                : _buildNameWidget(!isMyMessage ? usrName : "我", isMyMessage),
            const Spacer(),
            _buildSendTime(message),
          ],
        ),

        //显示各种消息类型(根据场景是否有长按功能)
        _buildContainerLongPressMenu(context),
        _buildMessageReadStatus(message)
      ],
    ));
  }

  _buildContainerLongPressMenu(BuildContext context) {
    if (message.isRecord) {
      // 聊天记录模式没有长按功能
      return _buildMessageContent(context, message);
    }
    return buildCustomPopupMenu(
      message,
      child: _buildMessageContent(context, message),
    );
  }

  Widget _buildNameWidget(String usrName, bool isMyMessage) {
    return Container(
      padding: EdgeInsets.only(bottom: 8),
      alignment: !isMyMessage ? Alignment.centerLeft : Alignment.centerRight,
      child: Text(!isMyMessage ? usrName : "我",
          style: const TextStyle(fontSize: 14, color: Color(0xff666666))),
    );
  }

  Widget _buildError(MessageItemData message) {
    return NoDoubleClickGestureDetector(
        tapAction: (){
            onErrorTap?.call();
    }, tapDuration: 2,
        child: const Icon(
          Icons.refresh,
          color: Colors.red,
        ));

    return InkWell(
      onTap: onErrorTap,
      child: Icon(
        Icons.refresh,
        color: Colors.red,
      ),
    );
  }

  Widget _buildMessageReadStatus(MessageItemData message) {
    if (!message.showReadStatus || chatViewModel?.isMyChatPage == true) {
      return Container();
    }
    bool readeStatus = message.message.isReaded == 1;
    return Container(
      padding: const EdgeInsets.only(top: 8),
      child: Text(readeStatus ? "已读" : "未读",
          style: const TextStyle(fontSize: 12, color: Color(0x99000000))),
    );
  }

  Widget _buildMessageContent(BuildContext context, MessageItemData message) {
    // 文本
    if (message is MessageTextData) {
      return buildMessageStatusCard(
          child: _buildTextMessage(message.body, message.isMyMessage));
    }
    // 图片
    if (message is MessageImageData) {
      return buildMessageStatusCard(child: ImageCardItem(message, onImageTap));
    }
    // 音频
    if (message is MessageVoiceData) {
      return buildMessageStatusCard(child: VoiceCard(message , chatViewModel: chatViewModel,));
    }
    // 视频
    if (message is MessageVideoData) {
      return buildMessageStatusCard(child: VideoCard(message, simple: false, chatController: chatViewModel,));
    }
    // 位置
    if (message is MessageLocationData) {
      return buildMessageStatusCard(child: LocationCard(message, onMapTap));
    }

    // (转发的聊天记录)
    if (message is MessageRecordUiData) {
      return buildMessageStatusCard(child: _buildRecordMessage(message));
    }

    // 文件类型
    if (message is MessageFileData) {
      return buildMessageStatusCard(child: FileCard(onFileTap, message));
    }

    // 机器人消息
    if (message is MessageBotUIData) {
      return buildMessageStatusCard(child: _buildBotMessage(message));
    }

    // 引用类型消息
    if (message is MessageReplyUiData) {
      return buildMessageStatusCard(child: _buildQuoteMessage(message));
    }

    // 企业邀请
    if (message is MessageInviteInTeamData) {
      return buildMessageStatusCard(
          child: CorporateInvitation(message, onInviteTap));
    }

    //音视频call
    if (message is MessageAudioAndVideoCallData) {
      logger('MessageAudioAndVideoCallData===${message.body}===${message.message.callType}');
      return AudioVideoCallWidget(message, onCallTap);
      return buildMessageStatusCard(
          child: AudioVideoCallWidget(message, onCallTap));
    }
    return SizedBox();
  }

  // 发送的状态卡片，所有消息类型通用（撤回等除外），loading，success，failed 等状态
  //layout type 0: left or right, 1: center
  Widget buildMessageStatusCard({
    required Widget child,
    int layoutType = 0,
  }) {
    if (layoutType == 0) {
      switch (message.sendStatus) {
        case MessageSendStatus.sending:
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CupertinoActivityIndicator(),
              const SizedBox(width: 8),
              child
            ],
          );
        case MessageSendStatus.success:
          return (child);
        case MessageSendStatus.error:
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [_buildError(message), const SizedBox(width: 8), child],
          );
        default:
          return child;
      }
    } else {
      switch (message.sendStatus) {
        case MessageSendStatus.sending:
          return Stack(
            alignment: Alignment.center,
            children: [child, const CupertinoActivityIndicator()],
          );
        case MessageSendStatus.success:
          return child;
        case MessageSendStatus.error:
          return Stack(
            alignment: Alignment.center,
            children: [child, _buildError(message)],
          );
        default:
          return child;
      }
    }
  }

  _buildMessageContentClickShowTime(Widget child) {
    if (message.isRecord) return child;
    return InkWell(
      overlayColor: const WidgetStatePropertyAll(Colors.transparent),
      onTap: () {
        itemClick?.call(!message.selected);
      },
      child: Column(
        crossAxisAlignment: !message.isMyMessage
            ? CrossAxisAlignment.start
            : CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          child,
          if (message.showImmediateTime) ...[
            Container(
              height: 20,
              padding: EdgeInsets.only(
                  left: !message.isMyMessage ? 50 : 0,
                  right: message.isMyMessage ? 50 : 0),
              alignment: message.isMyMessage
                  ? Alignment.centerRight
                  : Alignment.centerLeft,
              child: Text(message.sendTime(),
                  style:
                      const TextStyle(fontSize: 12, color: Color(0xff666666))),
            ),
          ]
        ],
      ),
    );
  }

  // 引用类型
  Widget _buildQuoteMessage(MessageReplyUiData message) {
    return Column(
      crossAxisAlignment: message.isMyMessage
          ? CrossAxisAlignment.end
          : CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildTextMessage(message.body, message.isMyMessage),
        if (message.quoteMsg != null) ...[
          8.gap,
          MessageQuoteView(message: message ,chatController: chatViewModel,)
        ],
      ],
    );
  }

  Widget _buildTextMessage(String? msg, bool isMyMessage) {
    final color = isMyMessage ? ColorConfig.imMyBackColor : ColorConfig.imOtherBackColor;
    var radius = Radius.circular(8.0);
    final bRadius = isMyMessage
        ? BorderRadius.only(
            topLeft: radius, bottomLeft: radius, bottomRight: radius)
        : BorderRadius.only(
            topRight: radius, bottomLeft: radius, bottomRight: radius);

    var textColor = ColorConfig.mainTextColor;
    var linkColor =  ColorConfig.themeCorlor;

    double emojiFontSize = Platform.isIOS ? 25 : 20;

    final maxWidth = ScreenUtil().screenWidth - 110;

    return InkWell(
      overlayColor: const WidgetStatePropertyAll(Colors.transparent),
      onTap: () {
        if (!message.isRecord) {
          itemClick?.call(!message.selected);
        }
        
      },
      child: Container(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 12, bottom: 12),
      constraints: BoxConstraints(maxWidth: maxWidth, minHeight: 35),
      decoration: BoxDecoration(
          //背景
          color: color,
          //设置四周圆角 角度
          borderRadius: bRadius
          //设置四周边框
          ),
      child: MessageRichText(
        text: (msg ?? ''),
        fontSize: 16,
        emojiFontSize: emojiFontSize,
        textColor: textColor,
        linkColor: linkColor,
        onOpen: (e) {
          logger("tap: ${e.url}");
          this.onLinkTap?.call(e.url);
        },
        // tapWidgetCall: () {
        // if (!message.isRecord) {
        //   itemClick?.call(!message.selected);
        // }
        // },
      ),
    ),
    );
  }

  _buildRecordMessage(MessageRecordUiData message) {
    return InkWell(
      onTap: onRecordTap,
      child: Container(
        constraints: BoxConstraints(maxWidth: _screenW - 150),
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: ColorConfig.backgroundColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.uiRecord?.title ?? '',
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.bold),
            ),
            2.gap,
            _buildRecordItems(message.uiRecord?.showText ?? []),
          ],
        ),
      ),
    );
  }

  final _screenW = ScreenUtil().screenWidth;

  _buildRecordItems(List<String> items) {
    var itemList = items
        .map((e) => Container(
              constraints: BoxConstraints(maxWidth: _screenW - 160),
              margin: const EdgeInsets.only(top: 2),
              child: Text(e,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontSize: 12, color: Color(0xff949495))),
            ))
        .toList().take(3).toList();

    return Wrap(
      direction: Axis.vertical,
      children: itemList,
    );
  }

  _buildBotMessage(MessageBotUIData message) {
    return MessageBotCard(message, onBotTap);
  }

  // 撤回聊天
  _reCallMessage() {
    onRecallTap?.call();
  }

  Widget buildCustomPopupMenu(
    MessageItemData message, {
    required Widget child,
    bool showText = false,
  }) {
    // var menuKey = '${message.hashCode}_${index}';
    var menuKey = message.messageId();
    return CustomPopupMenu(
      pressType: PressType.longPress,
      barrierColor: Colors.transparent,
      enablePassEvent: true,
      controller: PopMenuCacheManager.get().getController(menuKey),
      menuBuilder: () {
        return MessageLongPressMenu(
          message,
          PopMenuCacheManager.get().getController(menuKey),
          showUndoButton: message.canRecall,
          showQuoteButton: message.canQuote,
          showCopyButton: message.canCopy,
          showForward: message.canSelect,
          showText: showText,
          forward: () {
            onForwardTap?.call();
          },
          reCall: () {
            _reCallMessage();
          },
          quote: () {
            onQuoteTap?.call();
          },
          onDelete: () {
            onDeleteTap?.call();
          },
          multiSelect: () {
            onMultiSelectTap?.call();
          },
          saveImage: () {
            onSaveImageTap?.call();
          },
        );
      },
      child: child,
      position: index == 0 ? PreferredPosition.top : null,
    );
  }

  // showImageList(BuildContext context, String url, String tag, String cacheKey) {
  //   Navigator.push(context,
  //       PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
  //     return FadeTransition(
  //       opacity: animation,
  //       child: HeroLargeImagePage(
  //         url: url,
  //         heroTag: tag,
  //         cacheKey: cacheKey,
  //       ),
  //     );
  //   }));
  // }
}
