import 'dart:async';
import 'dart:io';

import 'package:file_manager/file_manager.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/file_extension.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/emoji/emoji_panel.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/message_input_quote_view.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_photo_editor/flutter_photo_editor.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../../../res/assets_res.dart';
import '../../../common/user/user_helper.dart';
import '../../../utils/cos_helper.dart';
import '../../db/db_helper.dart';
import '../../emoji/emoji_source.dart';
import '../select_picture/common_select_manager.dart';
import 'chat_controller.dart';
import 'draft/draft.dart';
import 'entity/message_item_data.dart';
import 'voice_record.dart';

/*
* 输入框
* */
class MessageInputBar extends StatefulWidget {

  ChatController chatListViewModel;

  final Function(String) onSendPressed;
  final Function(List<String> , bool original) onImageSendPressed;
  final Function(String) onCameraSendPressed;
  final Function(File) onFileSendPressed;
  final Function(String,Duration) onAudioSendPressed;

  final Function() onFocus;

  final Function? atContactPressed;

  final bool isGroup;

  final TextEditingController? messageInputTextController;
  final FocusNode? focusNode;

  //回复的消息
  // MessageReplyUiData? replyData;
  MessageItemData? quoteData;

  VoidCallback? onReplyClose;
  VoidCallback? onLocationTap;
  VoidCallback? onVoiceCallBack;
  VoidCallback? onVideoCallBack;

  MessageInputBar(
      {super.key,
        required this.chatListViewModel,
        required this.onSendPressed,
        required this.onImageSendPressed,
        required this.onCameraSendPressed,
        required this.onFileSendPressed,
        required this.onAudioSendPressed,
        required this.onFocus ,
        this.isGroup = false,
        this.atContactPressed,
        this.messageInputTextController,
        this.focusNode,
        this.quoteData,
        this.onReplyClose,
        this.onLocationTap,
        this.onVoiceCallBack,
        this.onVideoCallBack,
        // this.onVoiceSendAction
      });

  @override
  State<StatefulWidget> createState() => _MessageInputBarState();
}

class _MessageInputBarState extends State<MessageInputBar> {
  late FocusNode _focusNode;

  String? _draftSessionId;
  String? _draftUid;
  bool _isDraftLoaded = false;

  var _isGalleryVisible = false;
  bool _sendButtonVisible = false;
  bool isAudioSendType = false;
  var _emojiVisible = false;
  StreamSubscription? menuStream;


  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _handleSendButtonVisibilityModeChange();

    widget.chatListViewModel.scrollController.addListener(_onScroll);

    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        setState(() {
          _isGalleryVisible = false;
          _emojiVisible = false;
          isAudioSendType = false;
        });
        widget.chatListViewModel.jumpToBottomSilently();
        widget.onFocus();
      }
    });

    _focusNode.unfocus();

    menuStream = eventBus.on<EventDisMessageInputMenu>().listen((e){
      // 收到监听隐藏菜单
      if(mounted){
        setState(() {
          _isGalleryVisible = false;
          _emojiVisible = false;
          isAudioSendType = false;
        });
      }

    });

    // 加载草稿
    _loadDraft();

  }

  // 加载草稿
  Future<void> _loadDraft() async {
    if (_isDraftLoaded) return;

    try {
      final uid = await UserHelper.getUid();
      final sessionId = widget.chatListViewModel.session.sessionId;

      if (uid != null && sessionId != null) {
        _draftUid = uid;
        _draftSessionId = sessionId;

        final draft = await DbHelper.getDraft(sessionId, uid);
        if (draft != null) {
          // 设置文本草稿（如果有）
          if (draft.text != null && draft.text!.isNotEmpty) {
            widget.messageInputTextController?.text = draft.text!;
            setState(() {
              _sendButtonVisible = true;
            });
          }

          // 如果有引用消息ID，恢复引用消息
          if (draft.quoteMessageId != null) {
            final messages = await DbHelper.getMessageByUidAndMsgId(uid, draft.quoteMessageId!);
            if (messages.isNotEmpty) {
              final quoteMessage = messages.first;
              // 设置引用消息
              widget.chatListViewModel.setQuoteMessage(quoteMessage);
            }
          }
        }

        _isDraftLoaded = true;
      }
    } catch (e) {
      logger('加载草稿失败: $e');
    }
  }

// 保存草稿
  Future<void> _saveDraft() async {
    try {
      if (_draftUid != null && _draftSessionId != null) {
        final text = widget.messageInputTextController?.text.trim();
        final quoteMessageId = widget.quoteData?.message.msgId;

        // 如果有文本内容或有引用消息，保存草稿
        if ((text != null && text.isNotEmpty) || quoteMessageId != null) {
          // 保存草稿到数据库
          await DbHelper.saveDraft(_draftSessionId!, _draftUid!, text, quoteMessageId);

          // 更新会话列表显示草稿提示
          final session = await DbHelper.getSessionByOwnerId2SessionId(_draftUid!, _draftSessionId!);
          if (session != null) {
            // 根据是否有引用消息显示不同的草稿提示
            String draftContent;
            if (quoteMessageId != null && (text == null || text.isEmpty)) {
              draftContent = "${draftPrefix()} [引用消息]";
            } else if (quoteMessageId != null) {
              draftContent = "${draftPrefix()} [引用消息] $text";
            } else {
              draftContent = "${draftPrefix()} $text";
            }

            session.msgContent = draftContent;
            session.msgType = ConstantImMsgType.SSChatMessageTypeDraft; // 使用特殊类型标记草稿
            await DbHelper.insertSession(session);
          }
        } else {
          // 没有内容和引用时删除草稿
          await DbHelper.deleteDraft(_draftSessionId!, _draftUid!);

          // 恢复会话列表显示
          final session = await DbHelper.getSessionByOwnerId2SessionId(_draftUid!, _draftSessionId!);
          if (session != null && session.msgType == ConstantImMsgType.SSChatMessageTypeDraft) {
            // 如果当前是草稿状态，恢复到最后一条消息
            final lastMsg = await DbHelper.getLastMsgBySessionId(_draftUid!, _draftSessionId!);
            if (lastMsg != null) {
              session.msgContent = await lastMsg.alias();
              session.msgType = lastMsg.msgType;
              await DbHelper.insertSession(session);
            }
          }
        }
      }
    } catch (e) {
      logger('保存草稿失败: $e');
    }
  }

  void _onScroll() {
    if (widget.chatListViewModel.ignoreScrollEvents) return;

    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    }

    setState(() {
      _isGalleryVisible = false;
      _emojiVisible = false;
      isAudioSendType = false;
    });
  }

  @override
  void dispose() {
    // 在页面关闭时保存草稿
    _saveDraft();

    widget.chatListViewModel.scrollController.removeListener(_onScroll);
    _focusNode.dispose();
    widget.messageInputTextController?.dispose();
    menuStream?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // _buildFunctionMenuLayout(),
        _buildMessageInputBar(),
        if (_isGalleryVisible) _buildMorePage(),
        _buildEmojiPanel(),
        if (isAudioSendType) _buildVoiceHoldButton()
      ],
    );
  }

  var emojiList = buildEmoji();

  _buildEmojiPanel() {
    if(_emojiVisible){
      return EmojiPanel((emoji){
        var oldTxt = widget.messageInputTextController?.text?.toString() ?? '';
        widget.messageInputTextController?.text = '$oldTxt$emoji';
      }, emojiList);
    }
    return Container();
  }

  void _handleSendButtonVisibilityModeChange() {
    widget.messageInputTextController?.removeListener(_handleTextControllerChange);
    _sendButtonVisible = widget.messageInputTextController?.text.trim() != '';
    widget.messageInputTextController?.addListener(_handleTextControllerChange);
  }

  void _handleTextControllerChange() {
    setState(() {
      _sendButtonVisible = widget.messageInputTextController?.text.trim() != '';
      if (_sendButtonVisible) {
        _isGalleryVisible = false;
      }



    });
  }

  void _handleSendPressed() {
    final trimmedText = widget.messageInputTextController?.text.trim();
    if (trimmedText != '') {
      widget.onSendPressed(trimmedText ?? '');
      widget.messageInputTextController?.clear();

      // 发送消息后删除草稿
      if (_draftUid != null && _draftSessionId != null) {
        DbHelper.deleteDraft(_draftSessionId!, _draftUid!);
      }
    }
    _clearDetected();
  }

  bool canSendMsg() => widget.chatListViewModel.canSendMsg == true;
  bool cancelled() => widget.chatListViewModel.cancelled == true;

  String buildInputHintText() {
    return canSendMsg() ? '请输入......' :  cancelled() ? '对方已注销账号' : '对方不是你的好友';
  }


  // 语音切换按钮 + 输入框 + 表情 + more 按钮
  Widget _buildMessageInputBar() {
    return Container(
      color: ColorConfig.backgroundColor,
      child: Padding(
        padding: const EdgeInsets.only(top: 6, left: 8, bottom: 4),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            _buildVoiceMessageButton(),
            Flexible(
                child:  textInputView()
            ),
            _buildEmojiButton(),
            // _buildMediaMessageButton(),
            _buildSendOrMoreButton(),
            5.gap
          ],
        ),
      ),
    );
  }

  Widget textInputView() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          clipBehavior: Clip.hardEdge,

          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.transparent),
              borderRadius: const BorderRadius.all(Radius.circular(8))),
          child: Padding(padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
            child: TextField(
              enabled: canSendMsg(),
              onTapOutside: (event){

              },
              controller: widget.messageInputTextController,
              focusNode: _focusNode,
              // textInputAction:TextInputAction.send,
              textCapitalization: TextCapitalization.sentences,
              style: TextStyle(fontSize: 14, color: Colors.black),
              minLines: 1,
              maxLines: 6,
              // onEditingComplete: (){
              //   _handleSendPressed();
              // },
              onChanged: _detectedAtInput,
              decoration: InputDecoration(
                isDense: true,
                fillColor: Colors.red,
                border: InputBorder.none,
                hintText: buildInputHintText(),
                hintStyle: TextStyle(fontSize: 14, color: Colors.black38),
                contentPadding: EdgeInsets.only(
                    left: 12.0, right: 12.0, bottom: 8, top: 8),
              ),
            ),
          ),
        ),
        _buildQuoteView()
      ],
    );
  }

  Widget _buildQuoteView() {
    if(widget.quoteData == null) return SizedBox(height: 0,);
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: MessageInputQuoteView(quoteMessage: widget.quoteData , deleteQuoteView: (){
        widget.chatListViewModel.clearCurrentQuote();
      }, ),
    );
  }

  Widget _buildVoiceHoldButton() {
    return VoiceWidget(
      onSendVoice: (path,dration){
        widget.onAudioSendPressed(path,dration);
      },
    );
  }

  // 发送按钮，输入框有内容的时候显示
  Widget _buildSendOrMoreButton() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      child: !_sendButtonVisible
          ? _buildMoreButton()
          : Container(
        width: 64,
        height: 32,
        child: MaterialButton(
          color: Colors.blue,
          onPressed: () {
            if(!canSendMsg()) return;
            _handleSendPressed();

          },
          child: const Text('发送', style: TextStyle(fontSize: 13,color: Colors.white),),
        ),
      ),
    );
  }

  Widget _buildEmojiButton() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      child: IconButton(
        icon: Image.asset(AssetsRes.IC_IM_EMOJI , width: 24),
        onPressed: (){
          if(!canSendMsg()) return;
          _focusNode.unfocus();
          widget.onFocus();
          setState(() {
            isAudioSendType = false;
            _isGalleryVisible = false;
            _emojiVisible = !_emojiVisible;
            if (_emojiVisible) {
              widget.chatListViewModel.jumpToBottomSilently();
            }
          });

        },
      ) ,
    );
  }

  _changeAudioType(bool show) {
    setState(() {
      _isGalleryVisible = false;
      _emojiVisible = false;
      isAudioSendType = show;

      if (show) {
        widget.chatListViewModel.jumpToBottomSilently();
      }
    });
  }

  Widget _buildVoiceMessageButton() {
    if(!isAudioSendType) {
      return IconButton(
        icon: Image.asset(
          AssetsRes.IC_IM_VOICE,
          width: 21,
        ),
        onPressed: () {
          if(!canSendMsg()) return;
          _changeAudioType(true);
          _focusNode.unfocus();
        },
      );
    }else {
      return IconButton(
        icon: Image.asset(
          AssetsRes.IC_IM_VOICE,
          width: 21,
        ),
        onPressed: () {
          _changeAudioType(false);
        },

      );

    }
  }

  Widget _buildMoreButton() {
    return AnimatedRotation(
      turns: _isGalleryVisible ? 0.125 : 0,
      duration: const Duration(milliseconds: 100),
      child: IconButton(
        icon: Image.asset(
          AssetsRes.IC_IM_MORE,
          width: 24,
        ),
        onPressed: () {
          if(!canSendMsg()) return;
          _focusNode.unfocus();
          widget.onFocus();
          setState(() {
            isAudioSendType = false;
            _isGalleryVisible = !_isGalleryVisible;
            _emojiVisible = false;

            if (_isGalleryVisible) {
              widget.chatListViewModel.jumpToBottomSilently();
            }
          });
        },
      ),
    );
  }

  Widget _buildMorePage() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      padding: const EdgeInsets.only(top: 10,left: 18 ,right: 18),
      color: Color(0xF8F8F8F8),
      height: 200,
      child: GridView.count(
        padding: EdgeInsets.zero,
        shrinkWrap:true,
        crossAxisCount: 4,
        mainAxisSpacing: 6,
        crossAxisSpacing: 6,
        childAspectRatio: 0.9,
        children: [

          _buildMenuButton(AssetsRes.IC_FUNC_PIC , '相册' ,onPressed:()=> _onGalleryPickerPressed(context)),

          _buildMenuButton(AssetsRes.IC_FUNC_SHOT , '拍摄' ,onPressed:()=> _checkCameraPermission(context)),

          _buildMenuButton(AssetsRes.IC_FUNC_LOCATION , '位置' ,onPressed:() {
            widget.onLocationTap?.call();
          } ),

          _buildMenuButton(AssetsRes.IC_FILE_FOLDER, '文件' , image: Icon(Icons.folder, size: 21,),onPressed:() {
            pickFile();
          }),

          _buildMenuButton(AssetsRes.ICON_IM_VOICE_CHAT , '语音通话' ,onPressed:()=> widget.onVoiceCallBack?.call() ),

          if(widget.isGroup == false)...[
            _buildMenuButton(AssetsRes.ICON_IM_VIDEO_CHAT , '视频通话' ,onPressed:() {
          
              widget.onVideoCallBack?.call();
            }),
          ]
        ],
      ),
    );
  }

  void _onGalleryPickerPressed(BuildContext context) async {
    AssetManagerHelper.instance.resetOriginStatus();
    var r = await PermissionUtil.checkStoragePermission(context);
    if(!r){
      toast('没有权限');
      return;
    }

    var result = await SelectPicManager().showBurstSelectDialog(context ,type: SelectPicType.GALLERY_ONLY
        , maxGallerySize: 9 , tip: '选取图片需要您授权读写权限');
    if(result.paths.isEmpty) return;
    var photos = result.paths;
    logger('相册选择图片视频结果.... $photos , 是否选择了原图: ${AssetManagerHelper.instance.original}');
    if(photos.length == 1){
      File file = File(photos.first);
      if(file.isVideo()){
        widget.onImageSendPressed(photos , AssetManagerHelper.instance.original);
      }else if(file.isImage()){
        final filePath  = await editImage(photos[0]);
        if(filePath != null) {
          widget.onImageSendPressed([filePath] ,AssetManagerHelper.instance.original);
        }
      }else {
        toast('未选择视频或图片');
      }
    }else{
      // 暂时不走
      widget.onImageSendPressed(photos , AssetManagerHelper.instance.original);
    }
  }

  _checkCameraPermission(BuildContext context) async{
    SelectPicManager manager = SelectPicManager();
    var r = await manager.showBurstSelectDialog(context ,type: SelectPicType.CAMERA_ONLY ,
        tip: '拍照/选取图片需要您授权读写及照相机权限');
    if(r.paths.isEmpty) return;

    var result = r.paths.first;
    logger('拍照返回的 视频 ： $result');
    File file = File(result);
    if(file.isVideo()){
      widget.onCameraSendPressed.call(result);
    }else{
      var editResult = await editImage(result);
      widget.onCameraSendPressed.call(editResult);
    }
  }

  // 需要处理的话需备份
  Future<String> editImage(String? originPath, {bool? needEditImage = true}) async {
    if(needEditImage == false){
      return originPath ?? '';
    }
    if(originPath == null) return '';
    File tempFile = await CosHelper.move2Img(File(originPath));
    bool editResult =  await FlutterPhotoEditor().editImage(tempFile.path);
    if(editResult){
      return tempFile.path;
    }else{
      return '';
    }
  }


  _buildMenuButton(String? icon, String text , {Widget? image ,Function? onPressed,} ) {
    return GestureDetector(
      child: Container(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: EdgeInsets.only(bottom: 6),
              width: 50,
              height: 50,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(width: 0 , color: Colors.transparent),
              ),
              child: image != null ? image :Image.asset(icon?? '', width: 45,),
            ),
            Text(text , style: const TextStyle(fontSize: 11 ,color: Colors.black45), textScaleFactor:1)
          ],
        ),
      ),
      onTap: (){
        onPressed?.call();
      },
    );
  }


  String lastText = '';
  int startIndexOfTag = 0;
  int endIndexOfTag = 0;
  _clearDetected() {

    lastText = '';

  }
  _detectedAtInput(String value) {
    // print('_detectedAtInput');
    if(lastText.length <= value.length) {
      if (value.endsWith('@')) {
        if(widget.isGroup) {
          widget.atContactPressed?.call();
        }
      }
    }
    lastText = value;
  }

  // file， 不限制类型
  pickFile() async {
    if(Platform.isIOS){
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
      );
      if (result != null && result.count > 0) {
        final file = result.files.first;
        if (file != null && file.path != null) {
          widget.onFileSendPressed(File(file.path!));
        }
      }
    }else {
      var r = await PermissionUtil.requestFullFileAccess(context);
      if(!r){
        return;
      }
      var path = await RouteHelper.routePath(Routes.FILE_PICKER);
      if(StringUtil.isEmpty(path)) return;
      widget.onFileSendPressed(File(path));
    }
  }
}

class EventDisMessageInputMenu {}