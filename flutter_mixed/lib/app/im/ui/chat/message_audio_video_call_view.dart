import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AudioVideoCallWidget extends StatelessWidget {
  final MessageAudioAndVideoCallData message;
  final Function()? onCallTap;

  final _screenW = ScreenUtil().screenWidth;

  AudioVideoCallWidget(this.message, this.onCallTap);

  @override
  Widget build(BuildContext context) {
    final color = message.isMyMessage ? ColorConfig.imMyBackColor : ColorConfig.imOtherBackColor;
    var radius = const Radius.circular(8.0);
    final bRadius = message.isMyMessage
        ? BorderRadius.only(
            topLeft: radius, bottomLeft: radius, bottomRight: radius)
        : BorderRadius.only(
            topRight: radius, bottomLeft: radius, bottomRight: radius);

    var textColor = message.isMyMessage ? Colors.white : Colors.black;
    String assetName = '';
    if (message.isMyMessage) {
      if (message.message.callType == 1) {
        assetName = AssetsRes.CHAT_ME_VOICECALL;
      } else {
        assetName = AssetsRes.CHAT_ME_VIDEOCALL;
      }
    } else {
      if (message.message.callType == 1) {
        assetName = AssetsRes.CHAT_OTHER_VOICE_CALL;
      } else {
        assetName = AssetsRes.CHAT_OTHER_VIDEO_CALL;
      }
    }
    // TODO: implement build
    return InkWell(
      onTap: onCallTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10),
        constraints: BoxConstraints(maxWidth: _screenW - 100),
        height: 50,
        decoration: BoxDecoration(color: color, borderRadius: bRadius),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 20,
              height: 20,
              child: Image.asset(assetName,width: 20,height: 20,),
            ),
            5.gap,
            Text(
              message.body ?? '',
              style: TextStyle(fontSize: 14, color: textColor),
            )
          ],
        ),
      ),
    );
  }
}
