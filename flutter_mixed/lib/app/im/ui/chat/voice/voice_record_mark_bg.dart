
import 'package:flutter/material.dart';

class VoiceRecordMarkBg extends StatelessWidget {

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: const Si<PERSON>(double.infinity, 400),
      painter: CurvedTopRectanglePainter(),
    );
  }
}

class CurvedTopRectanglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.white // 底色
      ..style = PaintingStyle.fill;

    Path path = Path();

    // 左下角开始
    path.moveTo(0, size.height);
    path.lineTo(0, size.height * 0.3);

    // 上弧
    path.quadraticBezierTo(size.width / 2, 0, size.width, size.height * 0.3);

    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}