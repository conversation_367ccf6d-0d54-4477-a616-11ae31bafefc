
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/pop/custom_pop_up_menu.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../../res/assets_res.dart';
import '../../../common/widgets/widgets.dart';
import '../../../utils/system_util.dart';
import 'entity/message_item_data.dart';


/// 消息长按弹出pop： 复制、转发、删除、引用、撤回
class MessageLongPressMenu extends StatelessWidget {

  final MessageItemData message;
  final CustomPopupMenuController customPopupMenuController;

  final bool showUndoButton;
  final bool showQuoteButton;
  final bool showCopyButton;
  final bool showReminderButton;
  final bool showForward;
  final bool showMultiSelectButton;
  final bool showText;

  final Function? copy;
  final Function? forward;
  final Function? reCall;
  final Function? quote;
  final Function? onDelete;
  final Function? multiSelect;
  final Function? saveImage;

  MessageLongPressMenu(this.message, this.customPopupMenuController,
      {
      this.showUndoButton = true,
      this.showQuoteButton = true,
      this.showCopyButton = true,
      this.showReminderButton = true,
        this.copy,
        this.showForward = true,
        this.showMultiSelectButton = true,
        this.showText = false,
        this.forward,
        this.reCall,
        this.quote,
        this.onDelete,
        this.multiSelect,
        this.saveImage,
      });

  List<ItemModel> menuItems = [
    ItemModel('复制', MessageLongType.COPY, icon: AssetsRes.IM_MENU_COPY),
    ItemModel('转发', MessageLongType.FORWARD, icon: AssetsRes.IM_MENU_FORWARD),
    ItemModel('撤回', MessageLongType.RE_CALL, icon: AssetsRes.IM_MENU_RECALL),
    ItemModel('删除', MessageLongType.DEL, icon: AssetsRes.IM_MENU_DELETE),
    ItemModel('引用', MessageLongType.QUOTE, icon: AssetsRes.IM_MENU_QUOTE),
    ItemModel('保存', MessageLongType.SAVE, icon: AssetsRes.IM_MENU_SAVE),
    ItemModel('多选', MessageLongType.MULTI_SELECT, icon: AssetsRes.IM_MENU_MULTISELECT),
  ];

  _menus() {
    if(message.message.isSuccess != 1){
      menuItems.removeWhere((element) => element.type != MessageLongType.DEL);
      return menuItems;
    }

    if (!showUndoButton) {
      menuItems.removeWhere((element) => element.type == MessageLongType.RE_CALL);
    }

    if(!showQuoteButton) {
      menuItems.removeWhere((element) => element.type == MessageLongType.QUOTE);
    }
    if(!showForward) {
      menuItems.removeWhere((element) => element.type == MessageLongType.FORWARD);
      menuItems.removeWhere((element) => element.type == MessageLongType.MULTI_SELECT);
    }
    if(!showCopyButton) {
      menuItems.removeWhere((element) => element.type == MessageLongType.COPY);
    }

    if(message is MessageBotUIData){
      if(!((message as MessageBotUIData).botUIData?.canCopy ?? false)){
        menuItems.removeWhere((element) => element.type == MessageLongType.COPY);
      }
    }
    if(message is! MessageImageData && message is! MessageVideoData){
      menuItems.removeWhere((element) => element.type == MessageLongType.SAVE);
    }

    return menuItems;
  }

  @override
  Widget build(BuildContext context) => _buildLongPressMenu();

  Widget _buildLongPressMenu() {
    List<ItemModel> menuItems = _menus();
    int crossAxisCount = menuItems.length > 4 ? 4 : menuItems.length;
    var itemWidth = 72.0;
    double width = itemWidth * crossAxisCount;

    return ClipRRect(
      borderRadius: BorderRadius.circular(5),
      child: Container(
        width: width,
        color:  ColorConfig.blackColor,
        child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 0,
              mainAxisSpacing: 1,
              childAspectRatio: 1.2,
            ),
            itemCount: menuItems.length,
            itemBuilder: (ctx, index) {
              var item = menuItems[index];
              return _buildPopItemWidget(item, index, menuItems.length, crossAxisCount);
            }),
      ),
    );
  }

  _buildPopItemWidget(ItemModel item, int index, int totalCount, int crossAxisCount) {
    // 判断是否在行尾（不需要显示竖线）
    bool isRowEnd = (index + 1) % crossAxisCount == 0 || index == totalCount - 1;
    int rowCount = ((index+1)/crossAxisCount).ceil();
    int totalRow = (totalCount/crossAxisCount).ceil();
    bool lastRow = rowCount == totalRow;
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _onPressed(item.type);
        print('customPopupMenuController');

        customPopupMenuController.hideMenu();
      },
      child: Container(
        alignment: Alignment.center,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 主要内容：上图下文布局
            Expanded(child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Spacer(),
                // 图片
                if (item.icon != null)
                  Image.asset(
                    item.icon!,
                    width: 16,
                    height: 16,
                    color: Colors.white,
                  ),
                4.gap,
                // 文字
                Text(
                  item.title,
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
                Spacer(),
                Offstage(
                  offstage: lastRow,
                  child: Container(
                    width: double.infinity,
                    height: 1,
                    color: const Color(0x1AF3F3F3),
                  ),
                )
              ],
            )),
            // 右侧竖线
            if (!isRowEnd)
              Container(
                    width: 1,
                    height: 8,
                    color: const Color(0x1AF3F3F3),
                  )
          ],
        ),
      ),
    );
  }

  _onPressed(MessageLongType type) {
    switch (type) {
      case MessageLongType.COPY:
        if(message is MessageBotUIData){
          // 复制机器人消息
          var copyText = (message as MessageBotUIData).copy();
          SystemUtil.copy2Clipboard(copyText);
        }else {
          SystemUtil.copy2Clipboard(message.body);
        }
        toast('已复制到剪贴板');
        break;
      case MessageLongType.FORWARD:
        forward?.call();
        break;
      case MessageLongType.RE_CALL:
        reCall?.call();
        break;
      case MessageLongType.DEL:
        onDelete?.call();
        break;
      case MessageLongType.QUOTE:
        quote?.call();
        break;
      case MessageLongType.MULTI_SELECT:
        multiSelect?.call();
        break;
      case MessageLongType.SAVE:
        saveImage?.call();
        break;
      default:
        break;
    }
  }
}

class ItemModel {
  String title;
  MessageLongType type;
  String? icon;

  ItemModel(this.title, this.type, {this.icon});
}

enum MessageLongType {
  COPY,
  FORWARD,
  DEL,
  QUOTE,
  SAVE,
  RE_CALL,
  LINE,
  MULTI_SELECT
}