
import '../linkify.dart';
import 'emoji_regex.dart';

// final regex_emoji = RegExp(
//     "(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])");


class EmojiLinkifier extends Linkifier {
  EmojiLinkifier();
  final regex = emojiRegexRGI();

  @override
  List<LinkifyElement> parse(elements, options) {
    final list = <LinkifyElement>[];


    elements.forEach((element) {
      if (element is TextElement) {

        final match = regex.firstMatch(element.text);
        // print('element: ${ regex_emoji.hasMatch('\'')}, ${element}');

        if (match == null) {
          list.add(element);
        } else {
          final text = element.text.replaceFirst(match.group(0)!, '');

          if (match.group(1)?.isNotEmpty == true) {
            list.add(TextElement(match.group(1)!));
          }

          if (match.group(2)?.isNotEmpty == true) {
            // Always humanize emails
            list.add(EmojiElement(
              match.group(2)!,
            ));
          }
          if (text.isNotEmpty) {
            list.addAll(parse([TextElement(text)], options));
          }
        }
      } else {
        list.add(element);
      }
    });

    return list;
  }
}

class EmojiElement extends TextElement {
  EmojiElement(super.text);



  @override
  String toString() {
    return "EmojiElement: ($text)";
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is EmojiElement && runtimeType == other.runtimeType;

  @override
  int get hashCode => 0;
}

