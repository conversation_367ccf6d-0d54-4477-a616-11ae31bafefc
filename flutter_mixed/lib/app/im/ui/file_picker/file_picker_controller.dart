import 'dart:io';

import 'package:file_manager/file_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/utils/file_util.dart';
import 'package:get/get.dart';

class FilePickerController extends GetxController {

  bool canPop = true;

  late FileManagerController controller;

  @override
  void onInit() {
    super.onInit();
    controller = FileManagerController();
  }

  @override
  onClose() {}

  Future onPop(bool canPop) async {
    if(canPop) return;
    popBack();
  }

  Future popBack() async {
    if(!await controller.isRootDirectory()){
      controller.goToParentDirectory();
    }else {
      Get.back();
    }
  }

  bool isRoot(FileSystemEntity entity) {
    var isRoot =  FileManager.isDirectory(entity);
    canPop = isRoot;
    return isRoot;
  }

  openDir(FileSystemEntity entity) async {
    controller.openDirectory(entity);
    canPop = await controller.isRootDirectory();
  }

  Widget getIcon(FileSystemEntity entity) {
    if(entity is File){
      var fileName = FileUtil.getFileName(entity.path);
      var assetres = FileUtil.getFileTypeIcon(fileName);
      return Image.asset(assetres ,width: 25,);
    }else {
      return const Icon(Icons.folder);
    }
  }
}