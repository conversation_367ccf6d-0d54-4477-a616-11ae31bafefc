import 'dart:io';

import 'package:file_manager/file_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/ui/file_picker/file_picker_controller.dart';
import 'package:get/get.dart';

class FilePickerPage extends StatelessWidget {


  @override
  Widget build(BuildContext context) {
    return GetBuilder<FilePickerController>(builder: (ctx ){
      return PopScope(
          canPop: false,
          onPopInvokedWithResult: (canPop ,result){
             ctx.onPop(canPop);
          },
          child: ToolBar(
          backFunction: () async {
            ctx.popBack();
          },
          title: '文件浏览',
          body: FileManager(
            controller: ctx.controller,
            builder: (context, snapshot) {
              final List<FileSystemEntity> entities = snapshot;
              return ListView.builder(
                itemCount: entities.length,
                itemBuilder: (context, index) {
                  FileSystemEntity entity = entities[index];
                  return Card(
                    child: ListTile(
                      leading: ctx.getIcon(entity),
                      title: Text(FileManager.basename(entity,showFileExtension: true)),
                      subtitle: subtitle(entity),
                      onTap: () async {
                        if (ctx.isRoot(entity)) {
                          try{
                            ctx.openDir(entity);   // open directory
                          }catch(e){
                            print(e);
                          }
                        } else {
                          if(! await entity.exists()){
                            toast('文件不存在');
                          }else {
                            var path = entity.path;
                            Get.back(result: path);
                          }
                        }
                      },
                    ),
                  );
                },
              );
            },
          )
      ));
    });
  }

  Widget subtitle(FileSystemEntity entity) {
    return FutureBuilder<FileStat>(
      future: entity.stat(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          if (entity is File) {
            int size = snapshot.data!.size;

            return Text(
              "${FileManager.formatBytes(size)}",
            );
          }
          return Text(
            "${snapshot.data!.modified}".substring(0, 10),
          );
        } else {
          return Text("");
        }
      },
    );
  }

}