import 'dart:convert';

import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/constant/im_cache_global.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/refresh_notice_model.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/im_send_msg.dart';
import 'package:flutter_mixed/app/im/request/datasource/disturb_datasource.dart';
import 'package:flutter_mixed/app/im/request/datasource/offline_datasource.dart';
import 'package:flutter_mixed/app/im/request/entity/msg_top_resp.dart';
import 'package:flutter_mixed/app/im/ui/base/im_data_mixin.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../../request/entity/distrub_request_resp.dart';

class NoticeSettingController extends GetxController {
  Session? session;
  RxBool isOpenedDisturb = false.obs;
  RxBool isOpenedSessionTop = false.obs;
  String myUserId = '';
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      if (Get.arguments['session'] != null) {
        session = Get.arguments['session'];
        isOpenedDisturb.value = session?.noDisturb == 1 ? true : false;
        isOpenedSessionTop.value = session?.sessionTop == 1 ? true : false;
      }
    }
  }

  @override
  void onReady() async {
    super.onReady();
    myUserId = await UserHelper.getUid();
  }

  @override
  void onClose() {
    super.onClose();
  }

  dicClickSwitch(int type, bool value) {
    if (type == 0) {
      _settingNotDisturb(value);
    }
    if (type == 1) {
      _settingSessionTop(value);
    }
  }

  //设置消息免打扰
  _settingNotDisturb(bool value) async {
    var datasource = DisturbDataSource(retrofitDio, baseUrl: Host.HOST);

    NoticeDisturbReq req = NoticeDisturbReq(ImCacheData.instance.companies());

    var reqSwitch = value ? 1 : 0;

    switch (session?.sessionType) {
      case ConstantImMsgType.SystemMsgSessionType:
        req.systemPushSwitch = reqSwitch;
        break;
      case ConstantImMsgType.ApprovalMsgSessionType:
        req.approvalPushSwitch = reqSwitch;
        break;
      case ConstantImMsgType.TicketMsgSessionType:
        req.ticketPushSwitch = reqSwitch;
        break;
      case ConstantImMsgType.TrainMsgSessionType:
        req.trainingSwitch = reqSwitch;
        break;
      case ConstantImMsgType.MatterMsgSessionType:
        req.inventorySwitch = reqSwitch;
        break;
      case ConstantImMsgType.KingdeeMsgSessionType:
        req.kingdeePushSwitch = reqSwitch;
        break;
      case ConstantImMsgType.GeneralManagementSessionType:
        req.managementSwitch = reqSwitch;
        break;
      case ConstantImMsgType.RangeParkMsgSessionType:
        req.rangeParkSwitch = reqSwitch;
        break;
      case ConstantImMsgType.IDCMsgSessionType:
        req.idcSwitch = reqSwitch;
        break;
      case ConstantImMsgType.WorkMsgSessionType:
        _settingWorkNoticeDisturb(reqSwitch);
        return;
    }

    var resp = await datasource.modifyNoticeDisturb(req);
    if (resp.success()) {
      isOpenedDisturb.value = value;
      if (session != null) {
        session!.noDisturb = reqSwitch;
        _updateSessionDisturbDb(session!.sessionId, reqSwitch);
        ImCacheData.instance.updateNotifyDisturbWithSessionType(session!.sessionType!, reqSwitch);
        _sendSynchronousNotDisturb(ConstantImMsgType.SSChatMessageHandleTypeThreeNoticeDisturb,reqSwitch);
      }
      update();
      _refreshNoticeUI();
    } else {
      toast(resp.msg);
    }
  }

  _settingWorkNoticeDisturb(int reqSwitch) async {
    var datasource = DisturbDataSource(retrofitDio, baseUrl: Host.HOST);
    var workNoticeList = await DbHelper.getMySessionList(myUserId);
    workNoticeList = workNoticeList.where((item) => item.msgType == ConstantImMsgType.WorkMsgSessionType).toList();
    var req = workNoticeList.map((item) => item.sessionId).toList();
    if (reqSwitch == 1) {
      req.add(session!.sessionId);
    } else {
      req.remove(session!.sessionId);
    }
    var resp = await datasource.modifyWorkNoticeDisturb(req);
    if (resp.success()) {
      isOpenedDisturb.value = reqSwitch == 1;
      if (session != null) {
        session!.noDisturb = reqSwitch;
        _updateSessionDisturbDb(session!.sessionId, reqSwitch);
        ImCacheData.instance.updateCompanyDisturbWithCompanyId(session!.sessionId, reqSwitch);
        _sendSynchronousNotDisturb(ConstantImMsgType.SSChatMessageHandleTypeThreeNoticeDisturb,reqSwitch);
      }
      update();
      _refreshNoticeUI();
    } else {
      toast(resp.msg);
    }
  }

  _updateSessionDisturbDb(String sessionId, int disturb) async {
    var _session =
        await DbHelper.getSessionByOwnerId2SessionId(myUserId, sessionId);
    if (_session != null) {
      _session.noDisturb = disturb;
      DbHelper.insertSession(_session);
    }
  }

  //置顶聊天
  _settingSessionTop(bool value) async {
    if (session == null) return;
    var datasource =
        OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);

    if (value) {
      // 设置置顶
      List<String> singles = [];
      List<String> groups = [];
      List<String> notices = [session!.sessionId];
      var req = MsgTopResp(single: singles, group: groups, notice: notices);
      var resp = await datasource.createMsgTops(req);
      if (resp.success()) {
        toast('设置成功');
        isOpenedSessionTop.value = value;
        session!.sessionTop = 1;
        update();
        _updateMsgTopDb(true, session!.sessionId);
        _sendSynchronousNotDisturb(ConstantImMsgType.SSChatMessageHandleTypeConversationTop,1);
      } else {
        toast(resp.msg);
      }
    } else {
      // 取消置顶
      var req = MsgTopReq(session!.topType(), session!.sessionId);
      var resp = await datasource.delMsgTops(req);
      if (resp.success()) {
        toast('已取消置顶');
        isOpenedSessionTop.value = value;
        session!.sessionTop = 0;
        update();
        _updateMsgTopDb(false, session!.sessionId);
        _refreshNoticeUI();
        _sendSynchronousNotDisturb(ConstantImMsgType.SSChatMessageHandleTypeConversationTop,0);
      } else {
        toast(resp.msg);
      }
    }
  }

  // 置顶状态更新db
  _updateMsgTopDb(bool isTop, sessionId) async {
    var _session =
        await DbHelper.getSessionByOwnerId2SessionId(myUserId, sessionId);
    if (_session != null) {
      _session.sessionTop = isTop ? 1 : 0;
      DbHelper.insertSession(_session);
    }
  }

  //刷新前一页面
  _refreshNoticeUI() {
    RefreshNoticeModel refreshNoticeModel = RefreshNoticeModel(type: 0);
    eventBus.fire(refreshNoticeModel);
  }

  //发送同步指令
  _sendSynchronousNotDisturb(int type,int switchStatus) async{
    Map<String, dynamic> extDic = {
      'handleType': type,
      'handleId': session?.sessionId,
      'switchStatus': switchStatus
    };
    String extendOne = jsonEncode(extDic);
    Message message = await ImSendMsg.getSynchronousMessage(extendOne);
    ImSendMsg.sendSingleMsgWithMessage(message);
  }

}
