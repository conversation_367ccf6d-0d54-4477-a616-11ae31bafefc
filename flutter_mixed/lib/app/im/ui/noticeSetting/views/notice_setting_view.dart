import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_item.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';

import '../controllers/notice_setting_controller.dart';

class NoticeSettingView extends GetView<NoticeSettingController> {
  NoticeSettingView({super.key});
  NoticeSettingController noticeSettingController = Get.find();
  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      global: false,
      init: noticeSettingController,
      builder: ((controller) {
        return ToolBar(
          backgroundColor: ColorConfig.backgroundColor,
          title: '设置',
          body: Column(
            children: [
              1.gap,
              _backHeaderAndName(),
              10.gap,
              _backFunctionSwitch(0),
              10.gap,
              _backFunctionSwitch(1)
            ],
          ),
        );
      }),
    );
  }

  _backHeaderAndName() {
    return Container(
      color: ColorConfig.whiteColor,
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(15, 20, 15, 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 40,
            height: 40,
            child: noticeSettingController.session == null
                ? Container()
                : SessionAvatar(noticeSettingController.session!),
          ),
          10.gap,
          Expanded(
              child: Container(
            alignment: Alignment.centerLeft,
            child: Text(
              _backName(),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ))
        ],
      ),
    );
  }

  _backName() {
    dynamic nameStr = noticeSettingController.session?.name;
    if (noticeSettingController.session?.sessionType ==
        ConstantImMsgType.WorkMsgSessionType) {
      nameStr = noticeSettingController.session?.extend ?? '';
    }
    return nameStr;
  }

  _backFunctionSwitch(int type) {
    //type 0消息免打扰 1置顶聊天
    String text = '消息免打扰';
    bool currentValue = noticeSettingController.isOpenedDisturb.value;
    if (type == 1) {
      text = '置顶聊天';
      currentValue = noticeSettingController.isOpenedSessionTop.value;
    }
    return Container(
      alignment: Alignment.centerLeft,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      color: ColorConfig.whiteColor,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              text,
              style: const TextStyle(
                  fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
          Container(
              width: 80,
              height: 54,
              alignment: Alignment.centerRight,
              child: CupertinoSwitch(
                  activeTrackColor: ColorConfig.themeCorlor,
                  inactiveTrackColor: ColorConfig.lineColor,
                  value: currentValue,
                  onChanged: (value) {
                    noticeSettingController.dicClickSwitch(type, value);
                  }))
        ],
      ),
    );
  }
}
