import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/route/route_extension.dart';
import 'package:flutter_mixed/app/im/ui/tip_off/tip_off_main_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

import '../../widget/some_widget.dart';



class TipOffMainPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<TipOffMainController>(builder: (controller){
       return ToolBar(
         title: '投诉',
         body: Column(
           children: [
               Container(
                 alignment: Alignment.centerLeft,
                 padding: const EdgeInsets.only(top: 10 , left: 20 , bottom: 10),
                 child: const Text('请选择投诉该账号的原因' , style: TextStyle(fontSize: 15 , color: Color(0xff323232)),),
               ),

               Expanded(child: ListView.builder(
                   itemCount: controller.uiList.length,
                   itemBuilder: (ctx , index) {
                     var item = controller.uiList[index];
                 return _buildTipOffItem(item.content ?? '' , () {

                   Routes.TIP_OFF_FEEDBACK.toPage(arguments: {
                     'sessionId': controller.sessionId,
                     'items' : item.tipOff ?? []
                   });
                 });

               })),

           ],
         ),
       );
    });
  }

  _buildTipOffItem(String label , VoidCallback click) {
    return InkWell(
      onTap: () => click.call(),
      child: Container(
        padding: const EdgeInsets.all(16),
        color: Colors.white,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(label , style: const TextStyle(fontSize: 14 , color: Color(0xff323232)),),
            const Spacer(),
            arrow,
          ],
        ),
      ),
    );
  }



}