

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:get/get.dart';

import '../../../../common/theme.dart';
import 'bottom_dialog_container.dart';

class CommonSelectPicDialog extends StatelessWidget {

  CommonSelectPicDialog( this.album ,this.camera, {super.key});

  VoidCallback? album;
  VoidCallback? camera;

  @override
  Widget build(BuildContext context) {
    return BottomDialogContainer(child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Container(
        //     padding: const EdgeInsets.only(top: 20, bottom: 20),
        //     child: Text('选择',
        //         style: const TextStyle(fontSize: 16, color: Colors.black))),
        //
        // divider,
        InkWell(
            onTap: (){
              Get.back();
              camera?.call();
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(topLeft: Radius.circular(8),topRight: Radius.circular(8))
              ),
              alignment: Alignment.center,
              padding: const EdgeInsets.all(16),
              child: Text('拍照' , style: const TextStyle(fontSize: 16 ,color: Colors.black),),
            )
        ),


        divider,

        InkWell(
            onTap: (){
              Get.back();
              album?.call();
            },
            child: Container(
              color: Colors.white,
              alignment: Alignment.center,
              padding: const EdgeInsets.all(16),
              child: Text('选择相册' , style: const TextStyle(fontSize: 16 ,color: Colors.black),),
            )
        ),

        Container(
          color: Color(0xfff3f4f6),
          height: 8,
        ),

        InkWell(
            onTap: (){
              Get.back();
            },
            child: Container(
              color: Colors.white,
              alignment: Alignment.center,
              padding: const EdgeInsets.all(16),
              child: Text('取消' , style: const TextStyle(fontSize: 16 ,color: Colors.black),),
            )
        ),
        divider,
        10.gap

      ],
    ));
  }

}