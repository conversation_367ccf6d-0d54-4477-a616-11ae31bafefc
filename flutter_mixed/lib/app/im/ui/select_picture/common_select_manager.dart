import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../../logger/logger.dart';
import '../../../common/widgets/widgets.dart';
import '../../../permission/permission_util.dart';
import '../../../utils/string-util.dart';
import 'common_select_pic_dialog.dart';
import 'image_selector.dart';


/// camera , storage 权限管理， 返回拍照、图库选择的图片
class SelectPicManager {

  final ImagePicker _picker = ImagePicker();

  // 系统相册， 加连拍
  Future<SelectPicResult> showBurstSelectDialog(BuildContext context ,{int? cameraLimitSize = 20 , SelectPicType? type = SelectPicType.ALL ,
    bool? dirSendImage = true , int? maxGallerySize , String? tip}) async {

    Completer<SelectPicResult> pleter = Completer();
    if(type == SelectPicType.ALL){
      var hasPermission = await checkPermission(context);
      if(hasPermission){
        Get.bottomSheet(CommonSelectPicDialog(() async {
          // album , 系统相册
          var r = SelectPicResult()..paths = await _pickGallery(context ,maxSize: maxGallerySize)
            ..isGallery = true;
          pleter.complete(r);
        } , () async{
          // camera , 连拍相机
          pleter.complete(await _pickerBurstCamera(context));
        }));
      }else{
        pleter.complete(SelectPicResult());
      }
    }else if(type == SelectPicType.CAMERA_ONLY){
      var hasPermission = await checkPermission(context , tip: tip);

      if(hasPermission){
        pleter.complete(await _pickerBurstCamera(context));
      }else{
        pleter.complete(SelectPicResult());
      }
    }else if(type == SelectPicType.GALLERY_ONLY){
      // wechat_assets_picker

      var hasPermission = await checkStoragePermission(context);
      if(hasPermission){
        var r = SelectPicResult()..paths = await _pickGallery(context ,maxSize: maxGallerySize)
          ..isGallery = true;
        pleter.complete(r);
      }else{
        pleter.complete(SelectPicResult());
      }
    }else if(type == SelectPicType.IMAGE_ONLY){
      // 图库+系统相机
      var hasPermission = await checkPermission(context);
      if(hasPermission){
        Get.bottomSheet(CommonSelectPicDialog(() async {
          // album , 系统相册
          var r = SelectPicResult()..paths = await _pickGallery(context ,maxSize: maxGallerySize,requestType: RequestType.image)
            ..isGallery = true;
          pleter.complete(r);
        } , () async{
          // camera ,
          var r = SelectPicResult()..paths = await _pickCamera()
            ..isGallery = false;
          pleter.complete(r);
        }));
      }else{
        toast('请从手机设置中打开相机权限');
        pleter.complete(SelectPicResult());
      }
    }else {
      // 图库+系统相机
      var hasPermission = await checkPermission(context);
      if(hasPermission){
        Get.bottomSheet(CommonSelectPicDialog(() async {
          // album , 系统相册
          var r = SelectPicResult()..paths = await _pickGallery(context ,maxSize: maxGallerySize)
            ..isGallery = true;
          pleter.complete(r);
        } , () async{
          // camera ,
          var r = SelectPicResult()..paths = await _pickCamera()
            ..isGallery = false;
          pleter.complete(r);
        }));
      }else{
        pleter.complete(SelectPicResult());
      }
    }
    return pleter.future;
  }

  Future<bool> checkStoragePermission(BuildContext context) async {
    Completer<bool> c = Completer();
    if(Platform.isAndroid){
      var value = await Future.wait([Permission.storage.status]);
      if(value[0] == PermissionStatus.denied) {
        PermissionUtil.showPhotoPrivacyDialog(context , callback: (){
          PermissionUtil.requestPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if(value.isDenied){
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      }else {
        c.complete(true);
      }
    }else{
      c.complete(true);
    }
    return c.future;
  }

  // 为方便将camera和photo权限一起申请
  Future<bool> checkPermission(BuildContext context , {String? tip}) async {
    Completer<bool> c = Completer();
    if(Platform.isAndroid){

      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo = await deviceInfoPlugin.androidInfo;

      bool isDenied = false;
      if((deviceInfo.version.sdkInt ?? 0) >= 33) {
        var value = await Future.wait([Permission.camera.status]);
        isDenied = (value[0] == PermissionStatus.denied);
      }else {
        var value = await Future.wait([Permission.camera.status,Permission.storage.status]);
        isDenied = (value[0] == PermissionStatus.denied || value[1] == PermissionStatus.denied);
      }

      if(isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(tip: tip ,context , callback: (){
          PermissionUtil.requestPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if(value.isDenied){
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      }else {
        c.complete(true);
      }
    }else{
      c.complete(true);
    }
    return c.future;
  }

  Future<List<String>> _pickGallery(BuildContext context , {int? maxSize,RequestType requestType = RequestType.common}) async {
    var images = await ImageSelector.imagePhotos(context, maxPhotosSize: maxSize,requestType: requestType);
    if(images == null) return [];
    var paths = <String>[];
    var f = images.map((e) => e.file).toList();
    var fileList = await Future.wait(f);
    fileList.forEach((e) {
      var path = e?.path;
      if(path != null){
        paths.add(path);
      }
    });
    return paths;
  }

  Future<SelectPicResult> _pickerBurstCamera(BuildContext context) async {
    final AssetEntity? entity = await CameraPicker.pickFromCamera(
      context,
      pickerConfig:  CameraPickerConfig(
          enableRecording: true,
          // enableTapRecording: true,
          enableScaledPreview: true
      ),
    );
    var path = await entity?.file;
    if(path == null) return SelectPicResult()..paths = [];
    return SelectPicResult()..paths = [
      path.path
    ];
  }

  Future<List<String>> _pickCamera() async {
    // var image = await _picker.pickImage(source: ImageSource.camera);
    // var path = image?.path;
    // if(StringUtil.isEmpty(path)){
    //   return [];
    // }
    // return [path!];

    try {
      // 尝试使用 CameraPicker 替代 ImagePicker
      final AssetEntity? entity = await CameraPicker.pickFromCamera(
        Get.context!,
        pickerConfig: const CameraPickerConfig(
          enableRecording: false,
          enableScaledPreview: true,
        ),
      );
      if (entity == null) return [];

      final file = await entity.file;
      if (file == null) return [];

      return [file.path];
    } catch (e) {
      logger("相机打开失败: $e");
      toast("相机打开失败，请稍后重试");
      return [];
    }
  }
}

class SelectPicResult {
  bool isGallery = false;
  List<String> paths = [];
  List<String> marks = [];
}

enum SelectPicType{
  GALLERY_ONLY,
  CAMERA_ONLY,
  ALL,
  SYSTEM_ALL,
  IMAGE_ONLY
}