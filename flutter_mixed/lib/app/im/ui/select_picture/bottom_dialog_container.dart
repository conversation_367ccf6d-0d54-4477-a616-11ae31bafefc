import 'package:flutter/material.dart';

/// bottom dialog
class BottomDialogContainer extends StatelessWidget {

  final Widget child;

  const BottomDialogContainer({Key? key , required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10))),

      child: <PERSON><PERSON><PERSON>(child: child),
    );
  }
}