import 'package:flutter/cupertino.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

/// image_picker: ^0.8.5
/// wechat_assets_picker: ^8.3.2


class ImageSelector {

  static Future<List<AssetEntity>?> imagePhotos(BuildContext context , {int? maxPhotosSize,RequestType requestType = RequestType.common}) async {
    return await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: maxPhotosSize ?? 9,
        pageSize: 24,
        requestType: requestType,
        gridThumbnailSize: const ThumbnailSize.square(50),
        showOriginOption: true,
        // previewThumbnailSize: const ThumbnailSize.square(150),
        // specialPickerType: SpecialPickerType.wechatMoment,
      ),
    );
  }

  static Future<List<AssetEntity>?> pickerVideo(BuildContext context , {int maxSize = 1}) async {
    return await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        maxAssets: maxSize,
        requestType: RequestType.video,
        pageSize: 24,
        gridThumbnailSize: const ThumbnailSize.square(60),
        // previewThumbnailSize: const ThumbnailSize.square(150),
        // specialPickerType: SpecialPickerType.wechatMoment,
      ),
    );
  }
}