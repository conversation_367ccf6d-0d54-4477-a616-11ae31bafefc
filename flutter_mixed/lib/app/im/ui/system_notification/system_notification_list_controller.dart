import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/app/common/dialog/msg-dialog.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/smart_refresh.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/refresh_notice_model.dart';
import 'package:flutter_mixed/app/im/ext/notice_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_session_reqbody.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../../main.dart';
import '../../../common/api/Define.dart';
import '../../../common/api/LoginApi.dart';
import '../../../modules/contact/model/org/org_model.dart';
import '../../../utils/storage.dart';
import '../../ext/company_ext.dart';
import '../../request/datasource/offline_datasource.dart';
import '../../request/entity/offline_notice_msg.dart';
import "package:collection/collection.dart";

/// IM 消息 系统通知列表
class SystemNotificationListController extends GetxController {
  late final Session mSession;

  int page = 0;

  int pageSize = 30;

  String companyId = '';

  String companyName = '';

  List<OfflineNoticeItemResp> uiList = [];

  List<OrgModel> allOrgs = [];

  String myUserId = '';

  RefreshListener? refreshController;
  late AutoScrollController scrollController;
  CancelToken? offlineCancelToken;

  int currentClasssifyType = 0; //当前工作通知类型
  String currentClassString = '全部类型'; //当前工作通知筛选名称

  StreamSubscription? _eventNoticeStreamSubscription;

  bool isNoTag = false; //不区分公司的类型

  bool isOffline = false;
  @override
  void onInit() async {
    super.onInit();
    refreshController = RefreshListener();
    scrollController = AutoScrollController();
    if (Get.arguments is Session) {
      mSession = Get.arguments;
      companyId = mSession.appChatId ?? '';
      companyName = mSession.extend ?? '';
    } else if(Get.arguments is Map){
      if (Get.arguments['session'] != null) {
        mSession = Get.arguments['session'];
        companyId = mSession.appChatId ?? '';
        companyName = mSession.extend ?? '';
      }
      if (Get.arguments['isOffline'] != null) {
        isOffline = Get.arguments['isOffline'];
      }
    }else {
      Get.back();
    }
    if (mSession.sessionType == ConstantImMsgType.SystemMsgSessionType || mSession.sessionType == ConstantImMsgType.RangeParkMsgSessionType || mSession.sessionType == ConstantImMsgType.IDCMsgSessionType) {
      //新增的sessionType 不区分公司的话可添加在此
      isNoTag = true;
    }

    _eventNoticeStreamSubscription =
        eventBus.on<RefreshNoticeModel>().listen((event) async {
      if (event.type == 0) {
        if (event.resp != null) {
          ClientNoticeMsg? noticeMsg = event.resp!.noticeMsg;
          if (noticeMsg == null) return;
          NoticeData? noticeData = noticeMsg.getDataP();
          for (var i = 0; i < uiList.length; i++) {
            OfflineNoticeItemResp resp = uiList[i];
            if (resp.noticeMsg?.getDataP()?.cmdId == noticeData?.cmdId) {
              resp.noticeMsg = noticeMsg;
              update();
              break;
            }
          }
        } else {
          update();
        }
      } else if (event.type == 1 && event.notice != null) {
        if (event.notice?.sessionId != mSession.sessionId) {
          return;
        }
        if ((!isNoTag &&
                event.notice?.companyId == companyId) ||
            isNoTag) {
          uiList.add(event.notice!.getOfflineNoticeItemResp());
          update();
        }
      }
    });

    _getCompanyInfo();
    myUserId = await UserHelper.getUid();
    if (isOffline) {
       _fetchNoticeList(0, Define.TIMEMAX);
    }else{
       _getLocalData();
    }
    
  }

  //处理在此页面im重新连接，会话拉取到新消息的情况
  dealReciveSessionChange() async{
    int? lastTime = 0;
    if (uiList.isNotEmpty) {
      var m = maxBy(uiList, (item) {
        return item.msgTime;
      });
      if (m != null) {
        lastTime = m.msgTime;
      }
    }else{
      lastTime = mSession.msgTime;
    }
    _fetchNoticeList((lastTime ?? 0) + 1, Define.TIMEMAX,isMore: true);
  }

  //拉取离线数据
  _fetchNoticeList(int statusInt, int timeEnd,{bool isMore = false}) async {
    try {
      offlineCancelToken = CancelToken();
      var datasource =
          OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
      String? tag = companyId;
      if (isNoTag) {
        tag = null;
      }
      var resp = await datasource.getOfflineNoticeMsg(page, isMore?300:pageSize, tag, -1,
          0, timeEnd, mSession.sessionType ?? 0, offlineCancelToken!);
      if (resp.success()) {
        _fillUIData(resp.data, statusInt, timeEnd,isMore: isMore);
      } else {
        if (!isMore) {
          _getLocalData(isOfflineTimeOut: true);
        } 
      }
    } catch (e) {
      if (!isMore) {
        _getLocalData(isOfflineTimeOut: true);
      }
    }
  }

  _fillUIData(
      List<OfflineNoticeItemResp> _sources, int statusInt, int timeEnd,{bool isMore = false}) async {
    uiList.insertAll(0, _sources);
    if (!isMore) {
      if (_sources.isNotEmpty) {
        OfflineNoticeItemResp first = _sources.first;
        if (first.msgTime != null) {
          if (first.msgId != null) {
            List<Notice> msgList = await DbHelper.getMessageWihtMsgId(
                myUserId, mSession.sessionId, first.msgId!);
            if (_sources.length == pageSize && msgList.isEmpty) {
              //本地无最早一条记录断层
              Notice notice = Notice(
                  msgId: getUUid(),
                  uid: myUserId,
                  sessionId: mSession.sessionId,
                  companyId: companyId,
                  msgType: ConstantImMsgType.SSChatMessageTypeOfflineFault,
                  msgTime: first.msgTime! - 1);
              DbHelper.insertNotice(notice);
            }
          }
          //删除此消息时间段内的断层
          if (isNoTag) {
            DbHelper.deleteSystemMessageWithMsgType(
                myUserId,
                mSession.sessionId,
                ConstantImMsgType.SSChatMessageTypeOfflineFault,
                first.msgTime!,
                timeEnd);
          } else {
            DbHelper.deleteMessageWithMsgType(
                myUserId,
                mSession.sessionId,
                companyId,
                ConstantImMsgType.SSChatMessageTypeOfflineFault,
                first.msgTime!,
                timeEnd);
          }
        }
      }
      if (_sources.length == pageSize) {
        refreshController?.loadComplete();
      } else {
        refreshController?.loadNoData();
      }
    }

    for (var i = 0; i < _sources.length; i++) {
      OfflineNoticeItemResp resp = _sources[i];
      Notice notice = resp.getNoticeModel(myUserId);
      await DbHelper.insertNotice(notice);
    }
    update();
  }

  //处理公司数据
  _getCompanyInfo() async {
    if (mSession.isNoticeType() &&
        mSession.sessionType != ConstantImMsgType.KingdeeMsgSessionType &&
        !isNoTag) {
      //除金蝶外的审批需要公司数据
      Map dataDic = await UserDefault.getData(Define.ORGLIST);
      CompanyEntity companyEntity = dataDic.getCompanyEntity();

      allOrgs = companyEntity.createdList +
          companyEntity.joinedList +
          companyEntity.externalList;
      for (var i = 0; i < allOrgs.length; i++) {
        OrgModel model = allOrgs[i];
        model.haveApprove = 0;
      }
      //未读数处理
      getOfflineUnreadData();

      update();
    } else {
      DbHelper.upDateSessionUnReadCount(
          mSession.uid ?? '', mSession.sessionId, 0);
    }
  }

  //获取本地数据
  _getLocalData({bool isOfflineTimeOut = false}) async {
    int endTime = Define.TIMEMAX;
    if (uiList.isNotEmpty) {
      OfflineNoticeItemResp firstResp = uiList.first;
      if (firstResp.msgTime != null) {
        endTime = firstResp.msgTime! - 1;
      } else {
        return;
      }
    }

    var noticeList = await DbHelper.getLocalNoticeData(
        myUserId, mSession.sessionId, companyId, endTime, pageSize);
    if (isNoTag) {
      noticeList = await DbHelper.getSystemmLocalNoticeData(
          myUserId, mSession.sessionId, endTime, pageSize);
    }
    if (noticeList.isNotEmpty) {
      bool isHave = false;
      for (var i = noticeList.length - 1; i >= 0; i--) {
        Notice notice = noticeList[i];
        if (notice.msgType == ConstantImMsgType.SSChatMessageTypeOfflineFault) {
          isHave = true;
          break;
        }
      }
      //粗暴处理 有就拉最新覆盖 没有就本地显示
      if (isHave && !isOfflineTimeOut) {
        _fetchNoticeList(0, endTime);
      } else {
        List<OfflineNoticeItemResp> respList = [];
        for (var i = 0; i < noticeList.length; i++) {
          Notice notice = noticeList[i];
          if (notice.msgType !=
              ConstantImMsgType.SSChatMessageTypeOfflineFault) {
            OfflineNoticeItemResp noticeItemResp =
                notice.getOfflineNoticeItemResp();
            respList.add(noticeItemResp);
            if (mSession.isNoticeType() &&
                !isNoTag) {
              if (notice.tempStatus == 0) {
                getSingleNoticeMsg(
                    notice.msgId, notice.cmdId, noticeItemResp, '');
              } else {
                if (notice.msgType ==
                        ConstantImMsgType
                            .SSChatMessageTypeApprovalKingDeeDetail ||
                    notice.msgType ==
                        ConstantImMsgType
                            .SSChatMessageTypeApprovalKingDeeList ||
                    notice.type == ConstantImMsgType.KingdeeMsgSessionType) {
                  List<NoticeButton>? buttonList =
                      noticeItemResp.noticeMsg?.getDataP()?.buttonList();
                  if (buttonList != null && notice.tempStatus != null) {
                    for (var j = 0; j < buttonList.length; j++) {
                      NoticeButton button = buttonList[j];
                      if (button.buttonId == notice.tempStatus) {
                        if (button.resultTitle == '未处理') {
                          getSingleNoticeMsg(notice.msgId, notice.cmdId,
                              noticeItemResp, button.resultTitle!);
                        }
                        break;
                      }
                    }
                  }
                }
              }
            }
          }
        }
        if (endTime == Define.TIMEMAX) {
          uiList.clear();
        }
        uiList.insertAll(0, respList);
        update();
        if (noticeList.length == pageSize) {
          refreshController?.loadComplete();
        } else {
          refreshController?.loadNoData();
        }
      }
    } else {
      if (!isOfflineTimeOut) {
        _fetchNoticeList(0, endTime);
      }
    }
  }

  //获取离线未读
  Future getOfflineUnreadData() async {
    try {
      var reqBody = await createApproveUnreadReqBody(companyId, mSession);
      if (reqBody.list.isEmpty) {
        _dealOrgChooseState();
        _updateSessionUnreadCount();
        update();
        return;
      }
      var datasource =
          OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
      var resp = await datasource.getOfflineApproveUnread(reqBody);
      if (resp.success() && resp.data.isNotEmpty) {
        List<ListItem> result = resp.data;
        for (var i = 0; i < result.length; i++) {
          ListItem item = result[i];
          for (var j = 0; j < allOrgs.length; j++) {
            OrgModel model = allOrgs[j];
            if (item.tag == model.companyId) {
              model.haveApprove = item.count ?? 0;
            }
          }
        }
        _dealOrgChooseState();
        _updateSessionUnreadCount();
        update();
      }
    } catch (e) {
    }
  }

  //获取单条通知消息
  getSingleNoticeMsg(String msgId, String? cmdId,
      OfflineNoticeItemResp noticeResp, String resultTitle) async {
    var datasource =
        OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
    var resp = await datasource.getSingleNoticeMsg(msgId);
    if (resp.success()) {
      if (resp.data.noticeMsg != null) {
        if (resp.data.noticeMsg!.tempStatus != null) {
          int tempStatus = resp.data.noticeMsg!.tempStatus!;
          if (tempStatus > 0) {
            if (cmdId != null) {
              bool isUpdate = resultTitle.isEmpty;
              if (!isUpdate) {
                List<NoticeButton>? buttonList =
                    resp.data.noticeMsg?.getDataP()?.buttonList();
                if (buttonList != null) {
                  NoticeButton button = buttonList.first;
                  if (button.resultTitle != resultTitle) {
                    isUpdate = true;
                  }
                }
              }
              if (isUpdate) {
                DbHelper.upDateNoticeTempStatus(myUserId, cmdId, tempStatus);
                if (resp.data.noticeMsg?.data != null) {
                  DbHelper.upDateNoticeTempData(
                      myUserId, cmdId, resp.data.noticeMsg!.data!);
                }
                noticeResp.noticeMsg?.tempStatus = tempStatus;
                noticeResp.noticeMsg?.data = resp.data.noticeMsg?.data;
                update();
              }
            }
          }
        }
      }
    }
  }

  //切换公司
  changeOrgNotice(OrgModel model) {
    if (model.companyId == companyId) return;
    companyId = model.companyId;
    companyName = model.name;
    _dealOrgChooseState();
    _updateSessionUnreadCount();
    uiList.clear();
    if (offlineCancelToken != null) {
      offlineCancelToken?.cancel();
    }
    onPullDown();
  }

  //处理选中状态
  _dealOrgChooseState() {
    for (var i = 0; i < allOrgs.length; i++) {
      OrgModel model = allOrgs[i];
      if (model.companyId == companyId) {
        model.chooseState = 1;
        model.haveApprove = 0;
      } else {
        model.chooseState = 0;
      }
    }
  }

  //更新会话红点
  _updateSessionUnreadCount() {
    int allCount = 0;
    for (var i = 0; i < allOrgs.length; i++) {
      OrgModel model = allOrgs[i];
      allCount += model.haveApprove;
    }
    logger('=======会话未读======$allCount');
    //更新数据库此会话红点个数
    DbHelper.upDateSessionUnReadCount(myUserId, mSession.sessionId, allCount);
  }

  //更新审批状态
  updateApproveState(cmdId, tempStatus) {
    DbHelper.upDateNoticeTempStatus(myUserId, cmdId, tempStatus);
  }

  //切换工作通知类型
  changeWorkNoticeClasssify(int classsifyType, String name) async {
    if (currentClasssifyType == classsifyType) return;
    if (offlineCancelToken != null) {
      offlineCancelToken!.cancel();
    }
    currentClasssifyType = classsifyType;
    currentClassString = name;
    uiList.clear();
    if (currentClasssifyType == 0) {
      _getLocalData();
    } else {
      List<Notice> workNoticeList =
          await DbHelper.getLocalWorkNoticeDataWithClassifyType(myUserId,
              mSession.sessionId, currentClasssifyType, Define.TIMEMAX);
      logger('---workNotice ---$workNoticeList');
      for (var i = 0; i < workNoticeList.length; i++) {
        Notice notice = workNoticeList[i];
        if (notice.msgType != ConstantImMsgType.SSChatMessageTypeOfflineFault) {
          uiList.add(notice.getOfflineNoticeItemResp());
        }
      }
    }
    update();
  }

  //下拉加载
  onPullDown() {
    if (currentClasssifyType > 0) {
      refreshController?.loadComplete();
      return;
    }
    _getLocalData();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    scrollController.dispose();
    if (_eventNoticeStreamSubscription != null) {
      _eventNoticeStreamSubscription?.cancel();
    }
    super.onClose();
  }
}
