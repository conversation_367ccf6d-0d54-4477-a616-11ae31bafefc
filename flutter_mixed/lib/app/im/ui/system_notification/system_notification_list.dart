import 'dart:math';

import 'package:floor/floor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/smart_refresh.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/click_card/classify_type.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/convert/notice_temp_type.dart';
import 'package:flutter_mixed/app/im/ext/notice_button_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/notice_detail_data.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/ui/chat/common/expanded_viewport.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_controller.dart';
import 'package:flutter_mixed/app/im/widget/idc_card.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';
import 'package:kumi_popup_window/kumi_popup_window.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../modules/contact/model/org/org_model.dart';
import '../../../modules/place_holder/common_empty.dart';
import '../../click_card/click_card_jump.dart';
import '../../click_card/method_type.dart';
import '../../widget/choose_org_component.dart';
import '../../widget/notice_card.dart';

/// IM 消息 【系统通知】列表
class SystemNotificationListPage extends StatelessWidget {
  final String? tag;
  SystemNotificationListPage({Key? key, this.tag}) : super(key: key);
  SystemNotificationListController?systemNotificationListController;
  @override
  Widget build(BuildContext context) {
    SystemNotificationListController tagController = Get.find<SystemNotificationListController>(tag: tag);
    systemNotificationListController = tagController;
    return GetBuilder(
        init: systemNotificationListController,
        global: false,
        tag: systemNotificationListController?.mSession.sessionId,
        builder: (_) {
          return ToolBar(
            backgroundColor: ColorConfig.whiteColor,
            title: '',
            titleWidget: _dealTilteData(),
            actions: _backActions(),
            body: Column(
              children: [
                _backWorkNoticeCurrentClasssifyWidget(),
                Expanded(
                    child: RefreshContainer(
                        enablePullDown: false,
                        enablePullUp: true,
                        footer: smartRefreshFooter(),
                        controller:
                            systemNotificationListController!.refreshController!,
                        onLoading: () async {
                          systemNotificationListController?.onPullDown();
                        },
                        child: Scrollable(
                            controller: systemNotificationListController!
                                .scrollController,
                            axisDirection: AxisDirection.up,
                            viewportBuilder:
                                (BuildContext context, ViewportOffset offset) {
                              return ExpandedViewport(
                                offset: offset,
                                axisDirection: AxisDirection.up,
                                // keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                                slivers: [SliverExpanded(), _buildBodyWidget()],
                              );
                            })))
              ],
            ),
          );
        });
  }

  _buildBodyWidget() {
    return SliverPadding(
      padding: const EdgeInsets.only(bottom: 20),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          var realIndex =
              systemNotificationListController!.uiList.length - index - 1;
          var itemBody = systemNotificationListController!.uiList[realIndex];
          return AutoScrollTag(
              key: ValueKey(itemBody),
              controller: systemNotificationListController!.scrollController,
              index: realIndex,
              child: _buildNotice(realIndex, itemBody));
        }, childCount: systemNotificationListController!.uiList.length),
      ),
    );
  }

  _buildNotice(int index, OfflineNoticeItemResp itemBody) {
    if (itemBody.isUseApproveCard()) {
      NoticeData? noticeData = itemBody?.noticeMsg?.getDataP();
      return ApproveStyleCard(
        itemBody: systemNotificationListController!.uiList[index],
        itemClick: () {
          //点击了卡片
          logger('============itemBody====${itemBody.toJson()}');
          ClickCardJump.clickCard(itemBody);
        },
        buttonClick: (NoticeButton button, bool isSign) async {
          //点击了按钮
          if (isSign) {
            //或者跳转签名 待定
            ClickCardJump.clickCard(itemBody);
          } else {
            if (itemBody.noticeMsg != null &&
                itemBody.noticeMsg!.tempStatus != null) {
              if (itemBody.noticeMsg!.tempStatus! > 0) {
                if (itemBody.noticeMsg!.data != null) {
                  ClickCardJump.clickCard(itemBody);
                }
              } else {
                var result = await button.dealNoticeButtonWith(
                    systemNotificationListController!.companyId);
                if (result == 1) {
                  bool isRequest = false;
                  if ((button.methodType ?? 0) <
                      NetWorkMethodType.DDNetworkMethodTypeWeb) {
                    isRequest = true;
                  }
                  if (isRequest) {
                    //无网络请求不用变更
                    logger('=======button====${button.toJson()}');
                    itemBody?.noticeMsg?.tempStatus = button.buttonId;
                    systemNotificationListController?.update();
                    systemNotificationListController?.updateApproveState(
                        noticeData?.cmdId, button.buttonId);
                  }
                }
              }
            }
          }
        },
      );
    } else {
      NoticeData? noticeData = itemBody?.noticeMsg?.getDataP();
      if (noticeData?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeSix) {
        return Container();//不显示日报类型
      }else if(noticeData?.tempType ==
          NoticeTempType.DDNotificationTemplateTypeNine){
        return IdcCard(itemBody: itemBody,urlCallBack: (url){
          ClickCardJump.webJump(url, '');
        },);
      }
      return Container(
        alignment: Alignment.center,
        child:  const Text('不支持的类型'),
      );
    }
  }

  _backActions() {
    List<Widget> lists = [];
    if (systemNotificationListController?.mSession.sessionType ==
        ConstantImMsgType.TeamMsgSessionType) {
      //无按钮
    } else if (
      systemNotificationListController?.mSession.sessionType ==
            ConstantImMsgType.KingdeeMsgSessionType ||
        systemNotificationListController?.isNoTag == true) {
      lists.add(_backMoreAction());
    } else {
      lists.add(_backScreenAction());
      lists.add(_backMoreAction());
    }
    return lists;
  }

  _backScreenAction() {
    return InkWell(
      overlayColor: const WidgetStatePropertyAll(Colors.transparent),
      onTap: () {
        logger('点击了筛选');
        _popComponent(Get.context);
      },
      child: Container(
        width: 40,
        height: 40,
        alignment: Alignment.center,
        child: Image.asset(
          AssetsRes.IM_NOTICE_SCREEN,
          width: 24,
        ),
      ),
    );
  }

  _backMoreAction() {
    return InkWell(
      onTap: () {
        Get.toNamed(Routes.NOTICE_SETTING,
            arguments: {'session': systemNotificationListController?.mSession},
            preventDuplicates: false);
      },
      child: Container(
        width: 40,
        height: 40,
        alignment: Alignment.center,
        child: Image.asset(
          AssetsRes.IM_CHAT_MORE,
          width: 24,
        ),
      ),
    );
  }

  _popComponent(context) async {
    int type = 0; //type 0 审批通知公司筛选 1工作通知类型筛选
    if (systemNotificationListController?.mSession.sessionType ==
        ConstantImMsgType.WorkMsgSessionType) {
      type = 1;
    }
    double cellHeight = 56.0;
    double maxHeight = DeviceUtils().height.value -
        DeviceUtils().top.value -
        44 -
        DeviceUtils().bottom.value;
    double height =
        maxHeight > cellHeight * systemNotificationListController!.allOrgs.length
            ? cellHeight * systemNotificationListController!.allOrgs.length
            : maxHeight;
    if (type == 1) {
      double cellHeight = 44.0;
      height = maxHeight > cellHeight * ClassifyType.classsifyList.length
          ? cellHeight * ClassifyType.classsifyList.length
          : maxHeight;
    }
    showPopupWindow(
      context,
      gravity: KumiPopupGravity.centerTop,
      bgColor: const Color(0x88000000),
      clickOutDismiss: true,
      clickBackDismiss: true,
      customAnimation: false,
      customPop: false,
      customPage: false,
      underStatusBar: false,
      underAppBar: false,
      offsetX: 0,
      offsetY: 100,
      duration: const Duration(milliseconds: 200),
      childFun: (pop) {
        return Container(
          width: DeviceUtils().width.value,
          height: height,
          padding: const EdgeInsets.only(left: 15, right: 15),
          key: GlobalKey(),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 7,
                offset: const Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              children: type == 0
                  ? _backOrgWidget(context)
                  : _backClasssifyWidget(context),
            ),
          ),
        );
      },
    );
  }

  //公司筛选cell
  _backOrgWidget(context) {
    List<Widget> lists = [];

    for (var i = 0; i < systemNotificationListController!.allOrgs.length; i++) {
      OrgModel model = systemNotificationListController!.allOrgs[i];
      lists.add(InkWell(
        onTap: () async {
          Navigator.of(context).pop('');
          systemNotificationListController?.changeOrgNotice(model);
        },
        child: ChooseOrgComponentWidget(model),
      ));
    }
    return lists;
  }

  //类型筛选cell
  _backClasssifyWidget(context) {
    List<Widget> lists = [];

    for (var i = 0; i < ClassifyType.classsifyList.length; i++) {
      Map<String, dynamic> map = ClassifyType.classsifyList[i];
      int classifyType = map['classsifyType'];
      String name = map['name'];
      lists.add(InkWell(
        onTap: () async {
          Navigator.of(context).pop('');
          systemNotificationListController?.changeWorkNoticeClasssify(
              classifyType, name);
        },
        child: Column(
          children: [
            Container(
              height: 43,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.center,
              child: Text(
                name,
                style: TextStyle(
                    fontSize: 15,
                    color:
                        systemNotificationListController?.currentClasssifyType ==
                                classifyType
                            ? ColorConfig.themeCorlor
                            : ColorConfig.mainTextColor),
              ),
            ),
            Divider(
              height: 1,
              color: i == ClassifyType.classsifyList.length - 1
                  ? Colors.transparent
                  : ColorConfig.lineColor,
            )
          ],
        ),
      ));
    }
    return lists;
  }

  //工作通知当前筛选项
  _backWorkNoticeCurrentClasssifyWidget() {

    int classsifyType = systemNotificationListController!.currentClasssifyType;
    if (classsifyType == 0) {
      return Container();
    } else {
      return Container(
        margin: const EdgeInsets.fromLTRB(16, 10, 16, 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Expanded(
                child: Container(
              alignment: Alignment.centerRight,
              child: Text(
                systemNotificationListController!.currentClassString,
                style: const TextStyle(
                    fontSize: 14, color: ColorConfig.themeCorlor),
              ),
            )),
            InkWell(
              onTap: () {
                systemNotificationListController?.changeWorkNoticeClasssify(
                    0, '全部类型');
              },
              child: Container(
                width: 40,
                height: 40,
                alignment: Alignment.center,
                child: SizedBox(
                  width: 16,
                  height: 16,
                  child:
                      Image.asset('assets/images/3.0x/contact_dept_close.png'),
                ),
              ),
            )
          ],
        ),
      );
    }
  }

  //titleWidget
  _dealTilteData() {
    dynamic nameStr = systemNotificationListController?.mSession.name;
    if (systemNotificationListController?.mSession.sessionType ==
        ConstantImMsgType.WorkMsgSessionType) {
      nameStr = systemNotificationListController?.companyName;
    }
    if (
      systemNotificationListController?.mSession.sessionType ==
            ConstantImMsgType.WorkMsgSessionType ||
        systemNotificationListController?.mSession.sessionType ==
            ConstantImMsgType.TeamMsgSessionType ||
        systemNotificationListController?.isNoTag == true) {
      return _backTitleWidget(nameStr, '');
    } else {
      return _backTitleWidget(
          nameStr, systemNotificationListController!.companyName);
    }
  }

  _backTitleWidget(dynamic nameStr, String companyName) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
                child: Container(
              child: Text(nameStr ?? '',
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                      fontSize: companyName.isEmpty ? 18 : 15,
                      color: ColorConfig.mainTextColor)),
            )),
            if (systemNotificationListController!.mSession
                .isOpenedDisturb()) ...[
              Image.asset(
                AssetsRes.ICON_NOTICE_UNREMIND,
                width: 17,
                height: 17,
              )
            ]
          ],
        ),
        if (companyName.isNotEmpty) ...[
          Container(
            alignment: Alignment.center,
            child: Text(companyName,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: const TextStyle(
                    fontSize: 12, color: ColorConfig.desTextColor)),
          )
        ]
      ],
    );
  }
}
