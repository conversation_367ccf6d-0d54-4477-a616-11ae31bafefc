

import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_controller.dart';
import 'package:get/get.dart';

/// IM 消息 系统通知列表
class SystemNotificationListBinding extends Bindings {
  final String? tag;
  SystemNotificationListBinding({this.tag});
  @override
  void dependencies() {
    Get.lazyPut<SystemNotificationListController>(() => SystemNotificationListController(), tag: tag);
  }
}