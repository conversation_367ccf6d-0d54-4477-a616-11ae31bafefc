import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/ui/msg_record/msg_record_vm.dart';
import 'package:get/get.dart';
import '../chat/message_item_widget.dart';

/// 聊天记录 页面
class MsgRecordPage extends StatelessWidget {

  const MsgRecordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MsgRecordVm>(builder: (vm) {
      return ToolBar(
        backgroundColor: Colors.white,
        title: vm.getTitle(),
        body: Container(
          child: _buildPage(vm),
          padding: const EdgeInsets.only(bottom: 10),
        ),
      );
    });
  }

  _buildPage(MsgRecordVm chatvm) {
    if(chatvm.loading){
      return Container(
        alignment: Alignment.center,
        child: const CupertinoActivityIndicator(),
      );
    }
    return ListView.builder(
        itemCount: chatvm.chatMessages.length,
        itemBuilder: (ctx ,index){
        var message = chatvm.chatMessages[chatvm.chatMessages.length - index - 1];
        return Container(
          key: ValueKey(message),
          child: MessageItemContainer(index, message , chatController: chatvm.chatController,),
        );
    });
  }



}