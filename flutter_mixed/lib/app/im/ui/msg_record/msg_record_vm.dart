import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/forward_record_resp.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/widgets/widgets.dart';
import '../../db/entity/message.dart';
import '../../request/datasource/offline_datasource.dart';
import '../../request/entity/chat_resp_ext.dart';
import '../base/im_base_controller.dart';
import '../chat/entity/message_item_data.dart';
import '../chat/message/message_convert.dart';

class MsgRecordVm extends BaseController {

  bool loading = true;

  MessageItemData? message;
  MsgUIRecord? record;

  List<MessageItem> chatMessages = [];

  ChatController? chatController;

  @override
  void onInit() {
    super.onInit();

    _initMessages().then((r){
       if(!r){
         message = Get.arguments['msg'];
         if(message == null){
           Get.back();
           return;
         }

         try{
           chatController = Get.find<ChatController>(tag: message?.message.sessionId);
         }catch(e){}

         record = (message as MessageRecordUiData).uiRecord;
         if(record == null){
           Get.back();
           return;
         }
         update();
         _fetchRecords();
       }
    });
  }

  Future _initMessages() async {
    var _record = Get.arguments['msgList'];
    if(_record == null) return false;
    record = _record;
    if(record is! MsgUIRecord) return;
    await Future.forEach(record!.messages, (item) async {
      if(item is MessageItemData){
        item.isMyMessage = false;
        item.isRecord = true;
        item.author = item.message.sendName;
        item.showSenderName = true;
      }
      chatMessages.add(item);
    });

    loading = false;
    update();
    return true;
  }


  String getTitle() => record?.title ?? '';

  _fetchRecords() async {
    var datasource = OfflineDataSource(retrofitDio , baseUrl: Host.IM_OFFLINE_HOST);
    var req = FetchForwardRecord(type: record?.sessionType, ids: record?.msgIds);
    logger('获取会话记录列表：请求参数$req');
    var resp = await datasource.fetchForwardMsgList(req);
    if(resp.success()){
      // scrollController.scrollToIndex(0);

      var messageList = <Message>[];
      await Future.forEach(resp.data, (item) async {
        var msg =  await offlineGroup2Message(item , imSessionId: message?.message.sessionId );
        messageList.add(msg);
      });

      await Future.forEach(messageList, (item) async {
        var i = await item.toMessageItem(isRecordStyle: true);
        await _syncRecordStyle(i);
        chatMessages.add(i);
      });

      chatMessages = chatMessages.reversed.toList();

      loading = false;
      update();

    }else {
      toast(resp.msg);
    }
  }

  // 针对聊天记录的模式的修改
  // 1 都显示在左边
  // 2 撤回的样式改为 txt 形式
  // 3 都改为记录标记
  _syncRecordStyle(MessageItem i) async {
    if(i is MessageItemData){
      i.isMyMessage = false;
      i.isRecord = true;
    }
  }
}