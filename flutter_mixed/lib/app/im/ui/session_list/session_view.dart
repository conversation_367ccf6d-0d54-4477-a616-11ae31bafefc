import 'package:buttons_tabbar/buttons_tabbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/keep_alive_wrapper.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_controller.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_view.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_top_list_view.dart';
import 'package:flutter_mixed/app/im/ui/session_list/sesssion_tab_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';
import 'package:kumi_popup_window/kumi_popup_window.dart';

import '../../../common/config/config.dart';
import '../../../common/widgets/ai/single_ai_entrance.dart';
import '../../../permission/permission_util.dart';
import '../../../routes/app_pages.dart';
import '../../im_client_manager.dart';

class SessionView extends StatelessWidget {

  SessionView({super.key});

  var r = Get.put<SessionController>(SessionController());

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SessionController>(builder: (controller){
      return Container(
        color: ColorConfig.whiteColor,
        child: Stack(
          children: [
            Column(
              children: [
                (DeviceUtils().top.value).gap,
                10.gap,
                Row(
                  children: [
                    const Spacer(),
                    Expanded(child: Center(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text('消息',style: TextStyle(fontSize: 16, color: ColorConfig.mainTextColor,fontWeight: FontWeight.w500)),

                            StreamBuilder<IMConnectStatus>(
                                stream: ImClientManager.instance.connectListener.stream,
                                initialData: ImClientManager.instance.connectStatus,
                                builder: (ctx , snapshot){
                                  logger('刷新UI： ${snapshot.data}');

                                  if(snapshot.data == IMConnectStatus.connecting) {
                                    return Container(
                                      padding: const EdgeInsets.only(left: 15),
                                      width: 6,
                                      height: 6,
                                      child: const CupertinoActivityIndicator(),
                                    );
                                  } else if(snapshot.data == IMConnectStatus.connected) {
                                    return Container();
                                  }else if(snapshot.data == IMConnectStatus.failed ){

                                    return Container(
                                      color: Colors.transparent,
                                      margin: EdgeInsets.only(top: 2),
                                      padding: const EdgeInsets.only(left: 5),
                                      child: InkWell(
                                        onTap: (){
                                          // todo 下版本尝试用状态标记连接状态，去除lock
                                          ImClientManager.instance.reConnect();
                                        },
                                        child: Image.asset(AssetsRes.RETRY_CONNECT , width: 20),
                                      ),
                                    );
                                  }else {
                                    return Container();
                                  }
                                }),
                          ],
                        ))),

                    Expanded(
                        child: Container(
                          padding: EdgeInsets.only(right: 16),
                          alignment: Alignment.centerRight,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Offstage(
                                offstage: !controller.aiEntryList.isNotEmpty,
                                child: GestureDetector(
                                  onTap: () {
                                    controller.clickAiAction();
                                  },
                                  child: Container(
                                    padding: EdgeInsets.only(right: 16),
                                    child: Image.asset(AssetsRes.NEWS_AI,width: 24,),
                                  ),
                                )),
                              GestureDetector(
                                onTap: (){
                                  RouteHelper.route(Routes.SEARCH_CHAT);
                                },
                                child: Image.asset('assets/images/3.0x/im_newsHome_search.png',width: 24,),
                              ),

                              16.gap,

                              GestureDetector(
                                onTap: (){
                                  _popComponent(DeviceUtils().top.value+44);
                                },
                                child: Image.asset('assets/images/3.0x/external_add.png',width: 24,),
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
                10.gap,

                Expanded(
                  child: NestedScrollView(
                      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                        return <Widget>[

                          SliverToBoxAdapter(
                            child: DecoratedBox(
                              decoration: const BoxDecoration(color: Colors.white),
                              child: Padding(
                                padding: const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
                                child: AnimatedContainer(duration: Duration(milliseconds: 200),
                                  child: MediaQuery.removePadding(removeTop: true,context: context, child: SessionTopList(sessions: controller.msgTimeSortList)),
                                ),
                              ),
                            ),
                          ),
                        ];
                      },
                      body: NotificationListener<ScrollNotification>(
                          onNotification: (notification) {
                            controller.resetTopState(notification.metrics.pixels > 10);
                            return false;
                          },
                          child: MediaQuery.removePadding(removeTop: true,context: context, child:  SessionTabBar()))
                  ),
                )
              ],
            ),

            // Offstage(
            //     offstage: !controller.aiEntryList.isNotEmpty,
            //     child: DraggablePage(aiEntryList: controller.aiEntryList,)),

          ],
        ),
      );
    });
  }

  _popComponent(offsetY) {
    double viewW = 116;
    double cellH = 54;
    double viewH = cellH * 3;
    showPopupWindow(
      Get.context!,
      gravity: KumiPopupGravity.rightTop,
      bgColor: Colors.transparent,
      clickOutDismiss: true,
      clickBackDismiss: true,
      customAnimation: false,
      customPop: false,
      customPage: false,
      underStatusBar: false,
      underAppBar: false,
      offsetX: 10,
      offsetY: offsetY,
      duration: const Duration(milliseconds: 200),
      childFun: (pop) {
        return Container(
          width: viewW,
          height: viewH,
          key: GlobalKey(),
          decoration: BoxDecoration(
            color: ColorConfig.whiteColor,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 7,
                offset: Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(Get.context!);
                  RouteHelper.route(Routes.SEARCH_ORG, arguments: {'type': 2});
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 16),
                  height: cellH,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: ColorConfig.backgroundColor,
                        width: 1, // 设置下边框宽度为 2
                        style: BorderStyle.solid, // 设置下边框样式为实线
                      ),
                    ),
                  ),
                  child:  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(AssetsRes.NEWS_ADD_FRIEND, width: 20),
                      8.gap,
                      const Text(
                        "添加好友",
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      )
                    ],
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(Get.context!);
                  Get.toNamed(Routes.GROUP_ADD_MEMBERS , arguments: {});
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 16),
                  height: cellH,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: ColorConfig.backgroundColor,
                        width: 1,
                        style: BorderStyle.solid, // 设置下边框样式为实线
                      ),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(AssetsRes.NEWS_CREATE_GROUP, width: 20),
                      8.gap,
                      const Text(
                        "创建群组",
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      )
                    ],
                  ),
                ),
              ),
              InkWell(
                onTap: () async {
                  Navigator.pop(Get.context!);
                  var r = await PermissionUtil.checkCameraPermission(Get.context! , tip: scanPermissionTip );
                  if (!r) return;
                  RouteHelper.route(Routes.QR_CODE,arguments: {'type':1});
                  // if (Platform.isIOS) {
                  //   Channel().invoke(Channel_call_iOS_qrCode, {});
                  // } else if (Platform.isAndroid) {

                  //   var r = await PermissionUtil.checkCameraPermission(Get.context! , tip: scanPermissionTip );
                  //   if (!r) return;
                  //   RouteHelper.route(Routes.QR_CODE);

                  // }
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 16),
                  height: cellH,
                  alignment: Alignment.center,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(AssetsRes.NEWS_QR_CODE, width: 20),
                      8.gap,
                      const Text(
                        "扫一扫",
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

class TabInfo {
  final SessionListType type;
  final String text;

  TabInfo({required this.type, required this.text});
}

class SessionTabBar extends StatefulWidget {
  const SessionTabBar({super.key});
  @override
  _SessionTabBarState createState() => _SessionTabBarState();
}

class _SessionTabBarState extends State<SessionTabBar>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int currentIndex = 0;
  final List<TabInfo> _tabs = <TabInfo>[
    TabInfo(type: SessionListType.all, text: "消息"),
    TabInfo(type: SessionListType.unread, text: "未读"),
    TabInfo(type: SessionListType.singleChat, text: "单聊"),
    TabInfo(type: SessionListType.groupChat, text: "群聊"),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child:DefaultTabController(
        initialIndex: currentIndex,
        length: _tabs.length, child:  Builder(builder: (context){
          return Column(
        children: [
          DecoratedBox(
            decoration: const BoxDecoration(color: ColorConfig.whiteColor),
            child: Padding(
              padding: const EdgeInsets.only(left: 12.0, right: 12.0, top: 8.0, bottom: 8.0),
              child: Row(
                children: [
                  DecoratedBox(
                    decoration: const BoxDecoration(
                      color: Color(0xFFF3F4F6),
                      borderRadius: BorderRadius.all(Radius.circular(100))
                    ),
                    child: SizedBox(
                        height: 36.0,
                        child: TabBar(
                            onTap: (value) {
                              currentIndex = value;
                              setState(() {
                                
                              });
                            },
                            indicator: BoxDecoration(),
                            indicatorWeight: 0,
                            tabAlignment: TabAlignment.start,
                            dividerHeight: 0,
                            labelPadding: EdgeInsets.all(0),
                            tabs: _buildTabs(),
                            isScrollable: true,
                          ),
                      ),
                  )
                ], 
              ), 
            )
          ),
          Expanded(
            child: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              //controller: _tabController,
              children: _tabs.map((e) {
                return KeepAliveWrapper(
                  child: SessionListView(sessionListType: e.type,),
                );
              }).toList(),
            ),
          ),
        ],
      );
        })),
    );
  }

  _buildTabs(){
    List<Widget> lists = [];
    for (var i = 0; i < _tabs.length; i++) {
      bool isCurrent = currentIndex == i;
      TabInfo info = _tabs[i];
      lists.add(Tab(
        child: Container(
          padding: EdgeInsets.all(4),
          width: (DeviceUtils().width.value - 24) / 4,
          height: double.infinity,
          child: Container(
            height: double.infinity,
            decoration: isCurrent? BoxDecoration(
              borderRadius: BorderRadius.circular(18),
              color: isCurrent
                  ? ColorConfig.whiteColor
                  : Colors.transparent,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  spreadRadius: 0,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ):BoxDecoration(),
            child: Center(
              child: Text(
                info.text,
                style: TextStyle(
                  fontSize: 12,
                  color: isCurrent?ColorConfig.themeCorlor:ColorConfig.msgTextColor,
                  fontWeight: isCurrent?FontWeight.w500:FontWeight.w400
                ),
              ),
            ),
          ),
        ),
      ));
    }
    return lists;
  }
  
  @override
  void dispose() {
    // 释放资源
    _tabController.dispose();
    super.dispose();
  }



}


