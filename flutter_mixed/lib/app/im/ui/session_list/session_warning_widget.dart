

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/res/assets_res.dart';


enum SessionWarning {
  SOCKET_CONNECTING,
  SOCKET_CONNECTED,
  NET_UNAVAILABLE,
  KICK_OFF,
  IDLE,
}

class SessionWarningBar extends StatelessWidget {

  final SessionWarning sessionWarning;

  const SessionWarningBar({super.key ,required this.sessionWarning});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        ImClientManager.instance.connectIm();
      },
      child: Container(
        color: Color(0xffE2E2E2),
        padding: EdgeInsets.all(10),
        alignment: Alignment.center,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(AssetsRes.IM_WARNING_ICON ,width: 12,),
            6.gap,
            Text(_showTip() , style: TextStyle(fontSize: 13),)
          ],
        ),
      ),
    );
  }

  _showTip() {
    switch(sessionWarning){
      case SessionWarning.SOCKET_CONNECTING:
        return '连接中...';
      case SessionWarning.KICK_OFF:
        return '您的帐号已在其他地方登录，请重新登录';
      case SessionWarning.NET_UNAVAILABLE:
        return '当前网络不可用，请检查网络设置';
      default:
        return '';
    }
  }

}