import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/im/ext/list_extension.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/request/datasource/relation_datasource.dart';
import 'package:flutter_mixed/app/im/request/entity/group_brief_item_resp.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/modules/workStand/approveFunction/controllers/approve_function_controller.dart';
import 'package:flutter_mixed/app/retrofit/entity/ai/ai_power_resp.dart';
import 'package:flutter_mixed/app/utils/login_util.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../../../../main.dart';
import '../../../common/api/Define.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/user/user_helper.dart';
import '../../../common/widgets/circle_menu/ai_enter.dart';
import '../../../modules/contact/model/org/org_model.dart';
import '../../../../logger/logger.dart';
import '../../../utils/storage.dart';
import '../../../utils/string-util.dart';
import '../../constant/ImMsgConstant.dart';
import '../../constant/im_cache_global.dart';
import '../../convert/in_time_im_convert_to_session.dart';
import '../../db/db_helper.dart';
import '../../db/entity/session.dart';
import '../../ext/company_ext.dart';
import '../../manager/im_initializer.dart';
import '../../model/im_session_info.dart';
import '../../request/datasource/offline_datasource.dart';
import '../../request/datasource/relation_datasource.dart';
import '../../request/entity/group_brief_item_resp.dart';
import '../../request/entity/im_offline_notice_resp.dart';
import '../../request/entity/im_offline_session_resp.dart';
import '../../request/entity/offline_session_reqbody.dart';
import '../../utils/im_global_util.dart';
import '../chat/draft/draft.dart';

class SessionController extends SessionListController {

  bool tabIsTop = true;

  CancelToken? noticeOfflineCancelToken;

  List<AIPowerItem> aiEntryList = [];

  List<Session> topSessionList = []; 
  @override
  void onInit() {
    super.onInit();
    loadSession();
  }

  loadSession() async {
    var isLogin = await LoginUtil.isLogin();
    if(!isLogin) return;
    ownerId = await ImGlobalUtil.currentUserId();
    await loading();
    var localList = await _loadLocalSession();
    await _loadOfflineNoticeSession(localList);
    await _loadOfflineSession(localList);
    loadSuccess();
    loadAiEntryList();
  }



  // 加载本地db数据，同时过滤掉隐藏数据， 并增加缺省数据
  Future<List<Session>> _loadLocalSession() async {
    var dbList = await DbHelper.getSessionNotHiddenByOwnerId(ownerId);
    var unHiddenList =
    dbList.where((element) => element.sessionHidden == 0).toList();

    unHiddenList.sort((l, r) => (r.msgTime ?? 0).compareTo(l.msgTime ?? 0));

    list..clear()..addAll(unHiddenList);

    return list;
  }

  Future _loadOfflineNoticeSession(List<Session> dbSessionList) async {
    try {
          var reqBody = await createOfflineNoticeReqBody(dbSessionList);
    var datasource =
    OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
    if (noticeOfflineCancelToken != null) {
      noticeOfflineCancelToken!.cancel();
    }
    noticeOfflineCancelToken = CancelToken();
    var resp = await datasource.getOfflineNoticeList(reqBody,noticeOfflineCancelToken!);
    if (resp.success() && resp.data.isNotEmpty) {
      List<ImOfflineNoticeResp> result = resp.data;
      Map disturbMap = dbSessionList.asMap().map((key, session) {
        return MapEntry(session.sessionId, session.noDisturb);
      });

      Map dataDic = await UserDefault.getData(Define.ORGLIST);
      CompanyEntity companyEntity = dataDic.getCompanyEntity();

      List<OrgModel> allOrgs =
          companyEntity.createdList + companyEntity.joinedList;
      List<OrgModel> exOrgs = companyEntity.externalList;
      await Future.forEach(result, (e) async {
        // logger('=====lixian==e==${e.toJson()}');
        await e.createSessionFromOfflineNoticeSession(ownerId, disturbMap, allOrgs, exOrgs);
      });
      await _loadLocalSession();
     //await _fetchImTops();
      await synchronizationNoticeDisturb();
    }
    } catch (e) {
      
    }

  }

  Future _loadOfflineSession(List<Session> localList) async {
    // 本地会话数据生成 req
    try {
      List<Single> singles = [];
      List<Group> groups = [];
      localList.forEach((session) {
        int? lastTime = session.msgTime;
        if ((session.deleteLastTime ?? 0) > (session.msgTime ?? 0)) {
          //删除了最后一条的情况
          lastTime = session.deleteLastTime;
        }
        if (session.sessionType == 1) {
          // 单聊
          var single = Single(id: session.sessionId, time: lastTime);
          singles.add(single);
        } else if (session.sessionType == 2 ||
            session.sessionType ==
                ConstantImMsgType.SSChatMessageTypeChatGroupNotice) {
          // 群聊，群聊通知
          var group = Group(id: session.sessionId, time: lastTime);
          groups.add(group);
        }
      });

      var datasource = OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);
      var req = OfflineSessionReq(groups, singles);
      var resp = await datasource.getOfflineSessionListV2(req);
      var dbList = await DbHelper.getSessionNotHiddenByOwnerId(ownerId);
      var map = dbList.asMap().map((key, session) {
        return MapEntry(session.sessionId, session.noDisturb ?? 0);
      });
      if (resp.success()) {
        if (resp.data?.conversation?.isNotEmpty == true) {
          var tempSessionList = <Session>[];
          await Future.forEach(resp.data!.conversation!, (e) async {
            if (e != null) {
              var tempSession = await e.saveOfflineSession(ownerId, e.sessionId ?? '', map);
              if(tempSession != null){
                tempSessionList.add(tempSession);
              }
            }
          });

          await _fetchEmptyGroupAvatarName(tempSessionList);
          await checkLocalSessionDraft(tempSessionList);
          await DbHelper.insertSessions(tempSessionList);

          // 未读消息总数刷新 todo
          // sessionList 刷新 todo

          // 存储过后 重新查询本地
          await _loadLocalSession();
          await _fetchImTops(resp.data!);
          await _synchronizationChatDisturb();
        }
      }
    } catch (e) {
      logger(e);
    }
  }

  Future checkLocalSessionDraft(List<Session> tempSessionList) async {
    var ownerId = await UserHelper.getUid();
    var drafts = await DbHelper.getAllDraft(ownerId);
    if(drafts.isEmpty) return;
    // var tempSessionList = await DbHelper.getSessionNotHiddenByOwnerId(ownerId);
    var chats = tempSessionList.where((s) => s.isSingleChat() || s.isGroupChat()).toList();
    if(chats.isEmpty) return;
    var sessionIds = chats.map((s) => s.sessionId).toList();
    await Future.forEach(drafts, (draft) async {
      if(sessionIds.contains(draft.sessionId)){
        var s = chats.where((c) => c.sessionId == draft.sessionId).toList().firstOrNull;
        if(s != null){
          keepDraftIfHas(s ,uid: ownerId);
        }
      }
    });
  }

  // 额外处理会话中空头像和名字的数据
  _fetchEmptyGroupAvatarName(List<Session> sessions) async {
    var emptyGroups = sessions
        .where((e) => e.isGroupChat())
        .toList()
        .where((e) =>
    StringUtil.isEmpty(e.headerUrl) || StringUtil.isEmpty(e.name))
        .toList();
    if (emptyGroups.isEmpty) return;
    var datasource = RelationDatasource(retrofitDio, baseUrl: Host.HOST);
    var req = emptyGroups.map((e) => e.sessionId ?? '').toList();
    logger('请求的简略group信息参数 $req');
    var resp = await datasource.getGroupBriefs(req);
    if (resp.success()) {
      var groups = resp.data ?? [];
      groups.forEach((g){
        sessions.forEach((s){
          if(g.groupId ==  s.sessionId){
            s.headerUrl = g.logo;
            s.name = g.name;
          }
        });
      });
    }
  }

  Future loadAiEntryList() async {
    var result = await fetchAiEnterPermission();
    if(result.isEmpty) return;
    aiEntryList = result;
    update();
  }

  _fetchImTops(ImOfflineSessionV2Resp resp) async {
    var singles = resp.single ?? [];
    var groups = resp.group ?? [];
    var notices = resp.notice ?? [];
    var teams = singles + groups;

    //处理单群聊数据
    if(teams.isEmpty){
      // 重置session 数据里所有的会话不置顶
      var sessions = await DbHelper.getMySessionList(ownerId);
      if (sessions.isNotEmpty) {
        sessions.forEach((s) {
          if (s.isGroupChat() || s.isSingleChat()) {
            s.sessionTop = 0;
          }
        });
        DbHelper.insertSessions(sessions);
      }

    }else {
      // 遍历session里的数据，
      var idList = teams.map((e) => e).toList();
      var sessions = await DbHelper.getMySessionList(ownerId);
      if(sessions.isNotEmpty) {
        sessions.forEach((e) {
          if (e.isGroupChat() || e.isSingleChat()) {
            if(idList.contains(e.sessionId)){
              e.sessionTop = 1;
            }else {
            e.sessionTop = 0;
            }
          }
          idList.remove(e.sessionId);
        });
        DbHelper.insertSessions(sessions);
      }
      if (idList.isNotEmpty) {
        //无置顶数据
        List<String> sessionIds = [];
        idList.forEach((e){
          if(e is String){
            String sessionId = e;
            sessionIds.add(sessionId);
          }
        }); 

        //获取单群头像名称
        List<ImSessionInfo?>? sessionInfoList = await BaseInfo.getUserInfoWithImIds(sessionIds);
        if (sessionInfoList != null) {
          if (sessionInfoList.isNotEmpty) {
            for (var i = 0; i < sessionInfoList.length; i++) {
              ImSessionInfo? info = sessionInfoList[i];
              if (info == null) return;
              String sessionId = sessionIds[i];
              Session session = await createNewSession(sessionId, info.avatar ?? '', info.name ?? '', singles.contains(sessionId)?1:2, 1, '', '', 0, 0, 0, false, info.appChatId ?? '', 1);
              session.sessionHidden = -1;
              DbHelper.insertSession(session);
            }
          }
        }
      }
    }

    //处理通知
    var noticeList = notices.map((e) => e).toList();
    var sessions = await DbHelper.getMySessionList(ownerId);
    if(sessions.isNotEmpty) {
      sessions.forEach((e) {
        if (e.isNotice()) {
          if(noticeList.contains(e.sessionId)){
            e.sessionTop = 1;
          }else {
            e.sessionTop = 0;
          }
          noticeList.remove(e.sessionId);
        }

      });
      DbHelper.insertSessions(sessions);
    }
    if (noticeList.isNotEmpty) {
      var orgList = await UserDefault.getData(Define.ORGLIST);
       List<OrgModel> allOrgs = [];
      if (orgList is Map) {
        Map dataDic = orgList;
        CompanyEntity companyEntity = dataDic.getCompanyEntity();
        allOrgs = companyEntity.createdList + companyEntity.joinedList;
      }

      noticeList.forEach((e) async{
        await Future.forEach(allOrgs, (model) async{
          if (e == model.companyId) {
            Session session = await createNewSession(model.companyId, model.logo ?? '', model.name ?? '', ConstantImMsgType.WorkMsgSessionType, ConstantImMsgType.WorkMsgSessionType,'', '', 0, 0, 0, false, model.companyId, 1);
            session.sessionHidden = -1;
            DbHelper.insertSession(session);
          }
        });
        await Future.forEach(NoticeTypeManager.noticeTypeMap.keys, (key) async{
          if (e == NoticeTypeManager.noticeSwitchMap[key]) {
            String avatar = NoticeTypeManager.buildSessionAvatar(key);
            String name = NoticeTypeManager.noticeNameMap[key] ?? '';
            Session session = await createNewSession('$e', avatar, name ?? '', key, key,'', '', 0, 0, 0, false, '', 1);
            session.sessionHidden = -1;
            DbHelper.insertSession(session);
          }
        });
      });
    }
    _loadLocalSession();
  }

  //单群聊免打扰
  _synchronizationChatDisturb() async{
    var sessions = await DbHelper.getMySessionList(ownerId);
    var singleGroupIds = await ImCacheData.instance.getSingleGroupDisturbIds();
    var singleGroupMap = singleGroupIds.asmap();
    for (var session in sessions) {
      if (session.isSingleChat() || session.isGroupChat()) {
        _setNoDisturb(singleGroupMap, session);
      }
    }
    DbHelper.insertSessions(sessions);
    _loadLocalSession();
  }

  //通知免打扰
  synchronizationNoticeDisturb() async{
    var sessions = await DbHelper.getMySessionList(ownerId);
    var notify = await ImCacheData.instance.getNotifyDisturb();
    var companyDisturbIds = await ImCacheData.instance.getWorkNoticeDisturbIds();
    var companyDisturbMap = companyDisturbIds.asmap();
    for (var session in sessions) {
      if (session.isNoticeType()) {
        session.resetDisturbState(notify);
      }else if(session.sessionType == ConstantImMsgType.WorkMsgSessionType){
        _setNoDisturb(companyDisturbMap, session);
      }else if(session.sessionType == ConstantImMsgType.TeamMsgSessionType){
        //合作团队无消息免打扰
      }
    }
    DbHelper.insertSessions(sessions);
    _loadLocalSession();
  }

  _setNoDisturb(Map<String, dynamic> disturbMap, Session session) {
    logger('=======company=123===$disturbMap===$session');
    session.noDisturb = disturbMap.containsKey(session.sessionId) ? 1 : 0;
  }
  
  resetTopState(bool top) {
    if(top == false) return;
    tabIsTop = top;

    if(tabIsTop){
      if(ImCacheData.instance.expanded){
        ImCacheData.instance.expanded = !ImCacheData.instance.expanded;
      }
    }
  }

  clickAiAction() async {
    var url = await _createUrl('chat');
    logger('url = $url');
    openWebView({
      'url': url,
      'title': '',
      'isWebNavigation': 1,
    });
  }

  _createUrl(String powerId) async {
    var enterItem = aiEntryList.firstWhere((element) => element.powerId == powerId);
    var url = "${enterItem.webUrl}?agent=${enterItem.agent}";
    //获取platform
    int platform = Platform.isIOS ? 2 : 1;
    //获取appVersion
    String appVersion = await BaseInfo().getAppVersion();
    if (url.contains('?')) {
      url = '$url&platform=$platform&appVersion=$appVersion';
    } else {
      url = '$url?platform=$platform&appVersion=$appVersion';
    }
    return url;
  }

}