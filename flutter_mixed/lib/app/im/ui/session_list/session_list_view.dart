import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_item.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/im/ui/session_list/sesssion_tab_controller.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_binding.dart';
import 'package:flutter_mixed/app/im/widget/page_status.dart';
import 'package:flutter_mixed/app/permission/permission_collection_dialog.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../../modules/place_holder/common_empty.dart';

class SessionListView extends StatefulWidget {
  final SessionListType sessionListType;

  const SessionListView(
    {Key? key, required this.sessionListType }) : super(key: key);

  @override
  State<SessionListView> createState() => _SessionListViewState();
}

class _SessionListViewState extends State<SessionListView> with PageLoadWidget {
  SessionTabController? controllerData;

  @override
  void initState() {
    super.initState();
    controllerData = SessionTabController(sessionListType: widget.sessionListType);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SessionTabController>(
      init: controllerData,
      global: false,
      builder: (controller) {
      if (controller.screenList.isEmpty) {
        return Container(
          alignment: Alignment.center,
          child: const CommonEmpty('暂无消息'),
        );
      }
      return loadPage(controller.loadStatus, ListView.builder(
        itemCount: controller.screenList.length,
        itemBuilder: (ctx, index) {
          return SessionItem(
            mSession: controller.screenList[index],
            delete: (session) {
              showSimpleDialog(context, title: "确定要删除这条消息么", confirmCallBack: () {
                controller.deleteSession(session);
              });
            },
            top: (session) {
              controller.topSession(session);
            },
            disturb: (session) {
              controller.disturbSession(session);
            },
            itemClick: (session) {
              logger(session.toString());
              if (session.isSingleChat() || session.isGroupChat()) {
                 RouteHelper.routeTotag(ChatPage(tag: session.sessionId),Routes.IM_CHAGE_PAGE,arguments: session,binding: ChatBinding(tag: session.sessionId));
              } else if (session.isNoticeType() || session.sessionType == ConstantImMsgType.WorkMsgSessionType || session.sessionType == ConstantImMsgType.TeamMsgSessionType) {
                RouteHelper.routeTotag(
                  SystemNotificationListPage(
                    tag: session.sessionId,
                  ),
                  Routes.IM_SYSTEM_NOTIFICATION_LIST,
                  arguments: session,
                  binding: SystemNotificationListBinding(tag: session.sessionId));
              } else {
                // others do nothing
              }
            },
          );
        },
      ), (){});
    });
  }
}