import 'dart:async';

import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/refresh_notice_model.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/im/widget/page_status.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class SessionTabController extends GetxController with PageStatus {
  SessionListType sessionListType;

  SessionTabController({this.sessionListType = SessionListType.all});
  bool isLoading() => loadStatus == LoadState.loading;

  List<Session> dataList = [];
  List<Session> screenList = [];

  SessionListController sessionListController =
      Get.put(SessionListController());
  StreamSubscription? _refreshPageSubscroption;
  @override
  void onInit() {
    super.onInit();
    _getDataForSessionController();
    _refreshPageSubscroption = eventBus.on<RefreshPage>().listen((event) async {
      if (event.page == 'session_tab') {
        _getDataForSessionController();
      }
    });
  }

  _getDataForSessionController() {
    loadSuccess();
    dataList = sessionListController.list;
    switch (sessionListType) {
      case SessionListType.all:
        screenList = dataList;
        break;
      case SessionListType.unread:
        screenList = dataList
            .where((item) => item.notReadCount != 0 && !item.isOpenedDisturb())
            .toList();
        break;
      case SessionListType.groupChat:
        screenList = dataList.where((item) => item.isGroupChat()).toList();
        break;
      case SessionListType.singleChat:
        screenList = dataList.where((item) => item.isSingleChat()).toList();
        break;
      default:
    }
    update();
  }

  deleteSession(session) {
    sessionListController.deleteSession(session);
  }

  topSession(session) {
    sessionListController.topSession(session);
  }

  disturbSession(session) {
    sessionListController.disturbSession(session);
  }
}
