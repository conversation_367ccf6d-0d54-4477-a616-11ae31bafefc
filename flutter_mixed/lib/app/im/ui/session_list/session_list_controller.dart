import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:math';

import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/refresh_notice_model.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/im_send_msg.dart';
import 'package:flutter_mixed/app/im/request/entity/group_brief_item_resp.dart';
import 'package:flutter_mixed/app/im/widget/page_status.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/widgets/widgets.dart';
import '../../constant/ImMsgConstant.dart';
import '../../constant/im_cache_global.dart';
import '../../dispatch_parser/multi_sync_event_model.dart';
import '../../im_client_manager.dart';
import '../../request/datasource/disturb_datasource.dart';
import '../../request/datasource/offline_datasource.dart';
import '../../request/datasource/relation_datasource.dart';
import '../../request/entity/distrub_request_resp.dart';
import '../../request/entity/msg_top_resp.dart';
import '../../ui/base/im_base_controller.dart';

class SessionListController extends BaseController with PageStatus {
  SessionListType sessionListType;
  List<Session> list = [];
  List<Session> msgTimeSortList = [];
  List<Session> filterList = [];
  static HashSet<String> atList = HashSet();
  int filterListLength = 0;
  SessionListType filterListType = SessionListType.all;

  SessionListController({this.sessionListType = SessionListType.all});

  StreamSubscription? _sessionStreamSubscription;
  StreamSubscription? _netStreamSubscription;

  StreamSubscription? _eventMsgTopStreamSubscription;
  StreamSubscription? _eventDisturbStreamSubscription;

  bool isLoading() => loadStatus == LoadState.loading;

  @override
  void onInit() {
    super.onInit();
    logger('======当前sessionlist页面======code--$hashCode');
  }

  @override
  Future childOnInit() async {
    _eventMsgTopStreamSubscription =
        eventBus.on<EventSessionMsgTop>().listen((event) async {
      logger('session_list_controller: ${event.toString()}');
      var sessionId = event.sessionId;
      bool sessionIsTop = event.isTop;
      var session =
          await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
      if (session != null) {
        session.sessionTop = sessionIsTop ? 1 : 0;
        DbHelper.insertSession(session);
        _loadLocalSession();
      }
    });

    _eventDisturbStreamSubscription =
        eventBus.on<EventSessionMsgDisturb>().listen((event) async {
      var session = await DbHelper.getSessionByOwnerId2SessionId(
          ownerId, event.sessionId);
      if (session != null) {
        session.noDisturb = event.disturbOpen ? 1 : 0;
        DbHelper.insertSession(session);
        _loadLocalSession();
        if (session.isNoticeType()) {
          ImCacheData.instance.updateNotifyDisturbWithSessionType(session.sessionType ?? 0, session.noDisturb!);
        }else if(session.sessionType == ConstantImMsgType.WorkMsgSessionType){
          ImCacheData.instance.updateCompanyDisturbWithCompanyId(session.sessionId, session.noDisturb!);
        }
      }
    });

    // _listenConnectChanged();

    _sessionStreamSubscription =
        DbHelper.listenSessionList(ownerId).listen((event) {
      _loadLocalSession();
    });
  }

  // 加载本地db数据，同时过滤掉隐藏数据， 并增加缺省数据
  Future<List<Session>> _loadLocalSession() async {
    var dbList = await DbHelper.getSessionNotHiddenByOwnerId(ownerId);
    msgTimeSortList = await DbHelper.getMySessionList(ownerId);
    var unHiddenList =
        dbList.where((element) => element.sessionHidden == 0).toList();

    unHiddenList.sort((l, r) => (r.msgTime ?? 0).compareTo(l.msgTime ?? 0));

    list
      ..clear()
      ..addAll(unHiddenList);
    filterSessionList(sessionListType);
    _refreshPage();
    _whetherShowEmptyPage();

    _updateUnreadCount(dbList);
    return list;
  }

  //变更底部红点
  _updateUnreadCount(List<Session> list){
    var noOpenDisturbList = list.where((e) => !e.isOpenedDisturb()).toList();
    var unReadCountList = noOpenDisturbList
              .map((e) => e.notReadCount ?? 0);

    var updateCount = unReadCountList.length;
    if(unReadCountList.isNotEmpty){
      updateCount = unReadCountList.reduce((a, b) => a + b) ?? 0;
    }
    try {
      HomeController homeController = Get.find<HomeController>();
      homeController.updateMsgUnread(updateCount);
    } catch (e) {
      
    }

  }

  _whetherShowEmptyPage() {
    list.isNotEmpty ? loadSuccess() : loadEmpty();
    _refreshPage();
  }


  filterSessionList(SessionListType sessionListType) {
    switch (sessionListType) {
      case SessionListType.all:
        filterList = list;
        break;
      case SessionListType.unread:
        filterList = list
            .where((item) => item.notReadCount != 0 && !item.isOpenedDisturb())
            .toList();
        break;
      case SessionListType.groupChat:
        filterList = list.where((item) => item.isGroupChat()).toList();
        break;
      case SessionListType.singleChat:
        filterList = list.where((item) => item.isSingleChat()).toList();
        break;
      default:
        break;
    }
    filterListType = sessionListType;
    filterListLength = filterList.length;
  }


  int sessionCount() => filterList.length;

  /// 免打扰
  disturbSession(Session session) async {
    var datasource = DisturbDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);
    var isOpened = session.isOpenedDisturb();
    if (session.isSingleChat()) {
      // 单聊
      if (isOpened) {
        var resp = await datasource.closeSingleDisturb(session.sessionId);
        if (resp.success()) {
          toast('已关闭免打扰');
          session.noDisturb = 0;
          ImCacheData.instance
              .removeSingleAndGroupChatSessionId(session.sessionId);
          _updateSessionDisturbDb(session.sessionId, 0);
        } else {
          toast(resp.msg);
        }
      } else {
        OpenSingleDisturbReq req = OpenSingleDisturbReq(session.sessionId);
        var resp = await datasource.openSingleDisturb(req);
        if (resp.success()) {
          toast('已开启免打扰');
          session.noDisturb = 1;
          ImCacheData.instance
              .addSingleAndGroupChatDisturbSessionId(session.sessionId);

          _updateSessionDisturbDb(session.sessionId, 1);

          _refreshPage();
        } else {
          toast(resp.msg);
        }
      }
    } else if (session.isGroupChat()) {
      // 群聊
      if (isOpened) {
        var resp = await datasource.closeGroupDisturb(session.sessionId);
        if (resp.success()) {
          session.noDisturb = 0;
          ImCacheData.instance
              .removeSingleAndGroupChatSessionId(session.sessionId);

          _updateSessionDisturbDb(session.sessionId, 0);

          _refreshPage();
        } else {
          toast(resp.msg);
        }
      } else {
        var req = OpenSingleDisturbReq(session.sessionId);
        var resp = await datasource.openGroupDisturb(req);
        if (resp.success()) {
          session.noDisturb = 1;
          ImCacheData.instance
              .addSingleAndGroupChatDisturbSessionId(session.sessionId);
          _updateSessionDisturbDb(session.sessionId, 1);
          _refreshPage();
        } else {
          toast(resp.msg);
        }
      }
    } else {
      // 通知
      var datasource = DisturbDataSource(retrofitDio, baseUrl: Host.HOST);
      NoticeDisturbReq req = NoticeDisturbReq(ImCacheData.instance.companies());

      var reqSwitch = isOpened ? 0 : 1;

      switch (session.sessionType) {
        case ConstantImMsgType.SystemMsgSessionType:
          req.systemPushSwitch = reqSwitch;
          break;
        case ConstantImMsgType.ApprovalMsgSessionType:
          req.approvalPushSwitch = reqSwitch;
          break;
        case ConstantImMsgType.TicketMsgSessionType:
          req.ticketPushSwitch = reqSwitch;
          break;
        case ConstantImMsgType.TrainMsgSessionType:
          req.trainingSwitch = reqSwitch;
          break;
        case ConstantImMsgType.MatterMsgSessionType:
          req.inventorySwitch = reqSwitch;
          break;
        case ConstantImMsgType.KingdeeMsgSessionType:
          req.kingdeePushSwitch = reqSwitch;
          break;
        case ConstantImMsgType.GeneralManagementSessionType:
          req.managementSwitch = reqSwitch;
          break;
        case ConstantImMsgType.RangeParkMsgSessionType:
          req.rangeParkSwitch = reqSwitch;
          break;
        case ConstantImMsgType.IDCMsgSessionType:
          req.idcSwitch = reqSwitch;
          break;
        case ConstantImMsgType.WorkMsgSessionType:
          _settingWorkNoticeDisturb(session, reqSwitch);
          return;
      }

      var resp = await datasource.modifyNoticeDisturb(req);
      if (resp.success()) {
        _updateSessionDisturbDb(session.sessionId, reqSwitch);
         ImCacheData.instance.updateNotifyDisturbWithSessionType(session.sessionType!, reqSwitch);
        _refreshPage();
        _sendSynchronous(session.sessionId,ConstantImMsgType.SSChatMessageHandleTypeThreeNoticeDisturb,reqSwitch);
      } else {
        toast(resp.msg);
      }
    }
  }

    _settingWorkNoticeDisturb(Session session, int reqSwitch) async {
      var datasource = DisturbDataSource(retrofitDio, baseUrl: Host.HOST);
      var workNoticeList = await DbHelper.getMySessionList(ownerId);
      workNoticeList = workNoticeList.where((item) => item.sessionType == ConstantImMsgType.WorkMsgSessionType && item.noDisturb == 1).toList();
      var req = workNoticeList.map((item) => item.sessionId).toList();
      if (reqSwitch == 1) {
        if (!req.contains(session.sessionId)) {
          req.add(session.sessionId);
        }
        
      } else {
        if (req.contains(session.sessionId)) {
          req.remove(session.sessionId);
        }
      }
      var resp = await datasource.modifyWorkNoticeDisturb(req);
      if (resp.success()) {
        _updateSessionDisturbDb(session.sessionId, reqSwitch);
        ImCacheData.instance.updateCompanyDisturbWithCompanyId(session.sessionId,reqSwitch);
        _refreshPage();
        update();
        _sendSynchronous(session.sessionId,ConstantImMsgType.SSChatMessageHandleTypeThreeNoticeDisturb,reqSwitch);
      } else {
        toast(resp.msg);
      }
    }

  _updateSessionDisturbDb(String sessionId, int disturb) async {
    var _session =
        await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if (_session != null) {
      _session.noDisturb = disturb;
      logger('======_updateSessionDisturbDb====${_session.toString()}');
      DbHelper.insertSession(_session);
    }
  }

  /// 置顶会话
  topSession(Session session) async {
    var datasource =
        OfflineDataSource(retrofitDio, baseUrl: Host.IM_OFFLINE_HOST);

    if (!session.isTop()) {
      // 设置置顶
      List<String> singles = session.isSingleChat() ? [session.sessionId] : [];
      List<String> groups = session.isGroupChat() ? [session.sessionId] : [];
      List<String> notices = session.isNotice() ? [session.sessionId] : [];
      var req = MsgTopResp(single: singles, group: groups, notice: notices);
      var resp = await datasource.createMsgTops(req);
      if (resp.success()) {
        toast('设置成功');
        session.sessionTop = 1;

        _refreshPage();
        _updateMsgTopDb(true, session.sessionId);
        _sendSynchronous(session.sessionId,ConstantImMsgType.SSChatMessageHandleTypeConversationTop,1);
      } else {
        toast(resp.msg);
      }
    } else {
      // 取消置顶
      var req = MsgTopReq(session.topType(), session.sessionId);
      var resp = await datasource.delMsgTops(req);
      if (resp.success()) {
        toast('已取消置顶');
        session.sessionTop = 0;
        _refreshPage();
        _updateMsgTopDb(false, session.sessionId);
        _sendSynchronous(session.sessionId,ConstantImMsgType.SSChatMessageHandleTypeConversationTop,0);
      } else {
        toast(resp.msg);
      }
    }
  }

    //发送同步指令
  _sendSynchronous(String sessionId,int type,int switchStatus) async{
    Map<String, dynamic> extDic = {
      'handleType': type,
      'handleId': sessionId,
      'switchStatus': switchStatus
    };
    String extendOne = jsonEncode(extDic);
    Message message = await ImSendMsg.getSynchronousMessage(extendOne);
    ImSendMsg.sendSingleMsgWithMessage(message);
  }

  // 置顶状态更新db
  _updateMsgTopDb(bool isTop, sessionId) async {
    var session =
        await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if (session != null) {
      session.sessionTop = isTop ? 1 : 0;
      logger('======_updateMsgTopDb====${session.toString()}');
      DbHelper.insertSession(session);
    }
  }

  /// todo 删除消息；包括（session和 message）考虑软删
  /// todo 判断页面是否有数据，是否显示empty page
  deleteSession(Session session) async {
    session.sessionHidden = 1;
    session.notReadCount = 0;
    DbHelper.insertSession(session);
    // TODO 删除 message 表对应数据

    _whetherShowEmptyPage();

    _refreshPage();
  }

  _refreshPage() {
    RefreshPage refreshPage = RefreshPage()..page = 'session_tab';
    eventBus.fire(refreshPage);
    update();
  }

  @override
  void onClose() {
    super.onClose();
    _netStreamSubscription?.cancel();
    _sessionStreamSubscription?.cancel();
    _eventMsgTopStreamSubscription?.cancel();
    _eventDisturbStreamSubscription?.cancel();
    logger('=========sessionList======close');
  }
}

enum SessionListType { all, unread, singleChat, groupChat }
