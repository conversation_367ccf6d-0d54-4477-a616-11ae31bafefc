import 'dart:io';

import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badge;
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/constant/im_cache_global.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_controller.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_item.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_binding.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import '../../db/entity/session.dart';
import 'dart:math';


class SessionTopList extends StatefulWidget{
  final List<Session> sessions;

  const SessionTopList({super.key, required this.sessions,});

  @override
  State<SessionTopList> createState() => _SessionTopListState();
}

class _SessionTopListState extends State<SessionTopList> with BuildUnreadCount  {

  @override
  void initState() {
    super.initState();
    sessionController = Get.find<SessionController>();
  }

  SessionController? sessionController ;

  @override
  void didUpdateWidget(covariant SessionTopList oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final filteredSessions = widget.sessions.where((session) => session.sessionTop == 1).toList();
    final showItems = ImCacheData.instance.expanded ? filteredSessions : filteredSessions.take(6).toList();
    final screenWidth = MediaQuery.of(context).size.width;
  
    final itemWidth = screenWidth / 6;
    final spacing = itemWidth * 0.2; // 间距设为item宽度的20%

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: 0.755,
      ),
      itemCount: showItems.length > 6 ? showItems.length + 1 : showItems.length,
      itemBuilder: (_, index) {
        if (index == 5 && filteredSessions.length > 6) {
          return GestureDetector(
              onTap: () {
                setState(() {
                  ImCacheData.instance.expanded = !ImCacheData.instance.expanded;
                });

              },
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Stack(
                      clipBehavior: Clip.none,
                      alignment:AlignmentDirectional.center,
                      children: [
                        Container(
                          width: 40.0,
                          height: 40.0,
                          decoration: BoxDecoration(
                              color: const Color(0xFFF3F4F6),
                              borderRadius: BorderRadius.circular(10.0)
                          ),
                          margin: const EdgeInsets.only(bottom: 4),
                          alignment: Alignment.center, // 确保图片居中对齐
                          child: Transform.rotate(
                            angle: ImCacheData.instance.expanded ? pi : 0.0, // 当 ImCacheData.instance.expanded 为 true 时旋转 180 度
                            child: Image.asset(
                              'assets/images/3.0x/im_session_top_expand.png',
                              width: 20.0,
                              height: 20.0,
                            ),
                          ),
                        ),
                        ImCacheData.instance.expanded ? Container() : _buildExpandUnReadCount(filteredSessions),
                      ],
                    ),
                    Text(
                      ImCacheData.instance.expanded ? '收起' : '展开',
                      style: const TextStyle(fontSize: 10, height: 1.4, color: ColorConfig.desTextColor),
                    )
                  ]
              )
          );
        }

        final session = showItems[ filteredSessions.length <= 6 ? index : index >= 5 ? index - 1 : index];
        return ChatSessionItem(session: session);
      },
    );
  }

}

class ChatSessionItem extends StatelessWidget with BuildUnreadCount {
  final Session session;

  const ChatSessionItem({Key? key, required this.session}) : super(key: key);

  Future<void> _onTap(BuildContext context) async {
    Session currentSession = session;
    if (session.isSingleChat()) {
      Map dataDic = {
        'userId': session.appChatId,
        'imId': session.sessionId,
        'name': session.name,
        'headimg': session.headerUrl
      };
      currentSession = await dataDic.createSinglePageParam();
    } else if(session.isGroupChat()){
      var map = {'group': {
        'groupId': session.sessionId,
        'logo': session.headerUrl,
        'name': session.name
      }};
      currentSession = await map.createGroupSessionAndEnter(enter: false);
    }
    if (session.isSingleChat() || session.isGroupChat()) {
      RouteHelper.routeTotag(ChatPage(tag: session.sessionId),Routes.IM_CHAGE_PAGE,arguments: currentSession,binding: ChatBinding(tag: session.sessionId));
    } else if (session.isNoticeType() || session.sessionType == ConstantImMsgType.WorkMsgSessionType || session.sessionType == ConstantImMsgType.TeamMsgSessionType) {
      
        RouteHelper.routeTotag(
          SystemNotificationListPage(
          tag: currentSession.sessionId,
          ),
          Routes.IM_SYSTEM_NOTIFICATION_LIST,
          arguments: currentSession,
          binding: SystemNotificationListBinding(tag: currentSession.sessionId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _onTap(context),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            clipBehavior: Clip.none,
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 4),
                child: SessionAvatar(session),
              ),
              _buildUnReadMsg(session),
            ],
          ),
          Text(
            session.name ?? "",
            style: const TextStyle(fontSize: 10, height: 1.35, color: Color.fromRGBO(34, 34, 34, 0.4)),
            overflow: TextOverflow.ellipsis, // 设置溢出时显示省略号
            maxLines: 1,
          )
        ],
      ),
    );
  }
}

mixin BuildUnreadCount {
  static const double badgeSize = 10.0;

  _badgeWidget(int count, Widget child) {
  var value = '';
  var shape = badge.BadgeShape.circle;
  if (count <= 0) {
    value = '';
  } else if (count > 99) {
    value = '99+';
    shape = badge.BadgeShape.square;
  } else {
    value = '$count';
    shape = badge.BadgeShape.circle;
  }
  double fontSize = 10;
  return Padding(
      padding: const EdgeInsets.all(0),
      child: badge.Badge(
        // shape: BadgeShape.circle,
        ignorePointer: false,
        badgeStyle: badge.BadgeStyle(
          shape: shape,
          badgeColor: ColorConfig.deleteCorlor,
          padding: const EdgeInsets.all(1),
          borderRadius: BorderRadius.circular(12),
          // borderSide: BorderSide(color: ColorConfig.deleteCorlor, width: 1),
          elevation: 0,
        ),
        badgeContent: Container(
          padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
          constraints: const BoxConstraints(minWidth: 22),
          child: Center(
            child: Text(
              value,
              style: TextStyle(fontSize: fontSize, color: Colors.white),
            ),
          ),
        ),
        showBadge: count > 0,
        position: badge.BadgePosition.topEnd(top: -8, end: -4),
        child: child,
      ));
}

  _buildUnReadMsg(Session session) {
    int unReadCount = session.notReadCount ?? 0;
    if (unReadCount == 0) return Container();

    return _buildBadge(session.isOpenedDisturb(), unReadCount);
  }

  _buildExpandUnReadCount(List<Session> list) {
    List<Session> expandSessionList = _getExpandSessions(list);
    int totalUnReadCount = expandSessionList.fold(0, (sum, session) => sum + (session.notReadCount ?? 0));
    bool hasNoDisturb = expandSessionList.any((session) => session.isOpenedDisturb() && session.notReadCount != 0);

    return _buildBadge(hasNoDisturb, totalUnReadCount);
  }

  _buildBadge(bool isDisturb, int count) {
    if (isDisturb && count != 0) {
      return Positioned(
        right: -2,
        top: -2,
        child: Container(
          width: badgeSize,
          height: badgeSize,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: ColorConfig.deleteCorlor,
            borderRadius: BorderRadius.circular(100.0),
            border: Border.all(
              color: Colors.white,
              width: 1,
            ),
          ),
        ),
      );
    } else if (count > 0) {
      return Positioned(
        right: -8,
        top: 5,
        child: _badgeWidget(count, Container()),
      );
    }
    return Container();
  }

  List<Session> _getExpandSessions(List<Session> list) {
    if (list.length > 6) {
      return list.sublist(5);
    } else {
      return [];
    }
  }
}

class TopExpandState{
  bool expand = false;
}