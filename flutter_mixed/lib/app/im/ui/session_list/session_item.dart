import 'dart:io';

import 'package:badges/badges.dart' as badge;
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ext/im_time_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:flutter_swipe_action_cell/core/cell.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

import '../../../common/config/config.dart';
import '../../constant/ImMsgConstant.dart';
import '../../db/entity/session.dart';

class SessionItem extends StatelessWidget {
  final Session mSession;

  final ValueChanged<Session>? delete;
  final ValueChanged<Session>? disturb;
  final ValueChanged<Session>? top;
  final ValueChanged<Session>? itemClick;

  const SessionItem(
      {super.key,
      required this.mSession,
      this.delete,
      this.disturb,
      this.top,
      this.itemClick});

  @override
  Widget build(BuildContext context) {
    return Container(
        color: ColorConfig.whiteColor,
        padding: EdgeInsets.symmetric(horizontal: 8),
        child: SwipeActionCell(
          backgroundColor: ColorConfig.whiteColor,
          key: ValueKey(mSession.sessionId),
          trailingActions: [
            if (!mSession.isNoticeType()) ...[
              //通知不能删除
              SwipeAction(
                style: const TextStyle(fontSize: 13, color: Colors.white),
                onTap: (handler) async {
                  handler(false);
                  delete?.call(mSession);
                },
                color: ColorConfig.deleteCorlor,
                // icon: Icons.delete,
                title: '删除',
              ),
            ],
            SwipeAction(
              style: const TextStyle(fontSize: 13, color: Colors.white),
              onTap: (handler) async {
                handler(false);
                top?.call(mSession);
              },
              color: Colors.blue,
              title: mSession.isTop() ? '取消置顶' : '置顶',
            ),
            if (mSession.sessionType !=
                ConstantImMsgType.TeamMsgSessionType) ...[
              //合作企业无免打扰
              SwipeAction(
                style: const TextStyle(fontSize: 13, color: Colors.white),
                onTap: (handler) async {
                  handler(false);
                  disturb?.call(mSession);
                },
                color: Colors.black26,
                title: mSession.isOpenedDisturb() ? '取消免打扰' : '消息免打扰',
              ),
            ]
          ],
          child: InkWell(
            onTap: () {
              // toast('当前的： ${mSession.sessionId}');
              itemClick?.call(mSession);
            },
            child: Container(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  8.gap,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      8.gap,
                      // 头像
                      Container(
                        //padding: const EdgeInsets.symmetric(vertical: 3),
                        child: SessionAvatar(mSession,sessionHeadSize: 44,),
                      ),

                      12.gap,

                      Expanded(
                          child: Container(
                        //height: 44,
                        child: Column(
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              height: 24,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                      child: Text(mSession.name ?? '',
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                          style: const TextStyle(
                                              fontSize: 16,
                                              color:
                                                  ColorConfig.mainTextColor))),
                                  Text(
                                    (mSession.msgTime ?? 0).convertDate(),
                                    style: const TextStyle(
                                        fontSize: 12,
                                        color: ColorConfig.desTextColor),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              height: 20,
                              alignment: Alignment.centerLeft,
                              child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                // 会话内容
                                // Expanded(child: Text(mSession.msgContent ?? '', style: const TextStyle(fontSize: 12,color: Color(0xff999999)),)),
                                Expanded(
                                    child: _buildRichText(
                                  mSession.msgContent ?? '',
                                )),
                                Container(
                                  width: 46,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      _buildUnReadMsg(mSession),
                                      2.gap,
                                      _buildDisturb(mSession)
                                    ],
                                  ),
                                )
                              ],
                            ),
                            )
                            
                          ],
                        ),
                      )),

                      8.gap
                    ],
                  ),
                  8.gap
                ],
              ),
            ),
          ),
        ));
  }

  // 免打扰
  _buildDisturb(Session session) {
    if (session.isOpenedDisturb()) {
      return Image.asset(
        AssetsRes.ICON_NOTICE_UNREMIND,
        width: 16,
        height: 16,
      );
    }
    return Container();
  }

  // 未读消息提醒
  _buildUnReadMsg(Session session) {
    int unReadCount = session.notReadCount ?? 0;
    if (unReadCount == 0) return Container();
    if (session.isOpenedDisturb()) {
      return Container(
        width: 5,
        height: 5,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: ColorConfig.deleteCorlor,
            borderRadius: BorderRadius.circular(100.0)),
      );
    }
    return _badge(unReadCount, Container());
  }

  _buildRichText(String data) {
    if (data.contains('<font')) {
      return Container(
          child: HtmlWidget(
        '<div style="max-lines: 1; text-overflow: ellipsis">$data</div>',
        textStyle:
            const TextStyle(fontSize: 13, color: ColorConfig.msgTextColor),
      ));
    }
    return Container(
      child: Text(
        data,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style:
            const TextStyle(fontSize: 14, color: ColorConfig.subTitleTextColor),
      ),
    );
  }
}

_badge(int count, Widget child) {
  var value = '';
  var shape = badge.BadgeShape.circle;
  if (count <= 0) {
    value = '';
  } else if (count > 99) {
    value = '99+';
    shape = badge.BadgeShape.square;
  } else {
    value = '$count';
    shape = badge.BadgeShape.circle;
  }
  double fontSize = 10;
  return Padding(
      padding: const EdgeInsets.all(0),
      child: badge.Badge(
        // shape: BadgeShape.circle,
        ignorePointer: false,
        badgeStyle: badge.BadgeStyle(
          shape: shape,
          badgeColor: ColorConfig.deleteCorlor,
          padding: const EdgeInsets.all(1),
          borderRadius: BorderRadius.circular(12),
          // borderSide: BorderSide(color: ColorConfig.deleteCorlor, width: 1),
          elevation: 0,
        ),
        badgeContent: Container(
          padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
          constraints: const BoxConstraints(minWidth: 22),
          child: Center(
            child: Text(
              value,
              style: TextStyle(fontSize: fontSize, color: Colors.white),
            ),
          ),
        ),
        showBadge: count > 0,
        position: badge.BadgePosition.topEnd(top: 2, end: -4),
        child: child,
      ));
}

class SessionAvatar extends StatelessWidget {
  final Session session;
  double? sessionHeadSize;
  SessionAvatar(this.session, {super.key,this.sessionHeadSize = 40});

  @override
  Widget build(BuildContext context) {
    return _buildCircleAvatar(session);
  }

  _buildCircleAvatar(Session mSession) {
    // logger('sessionAdapter: name= ${mSession.name} , avatar = ${mSession.headerUrl}');
    return Container(
      width: sessionHeadSize,
      height: sessionHeadSize,
      decoration: BoxDecoration(
        //border: Border.all(width: 0.5, color: ColorConfig.btnGrayColor),
        borderRadius: BorderRadius.circular(8),
      ),
      // decoration: ,
      child: ImageLoader(
        url: _buildSessionAvatar(mSession),
        width: sessionHeadSize,
        height: sessionHeadSize,
        radius: 8,
      ),
    );
  }

  // 方法形式的组件命名： buildXX
  String _buildSessionAvatar(Session mSession) {
    String avatar = NoticeTypeManager.buildSessionAvatar(mSession.sessionType ?? 0);
    if (avatar.isEmpty) {
      avatar = mSession.headerUrl ?? '';
    }
    return avatar;
  }
}
