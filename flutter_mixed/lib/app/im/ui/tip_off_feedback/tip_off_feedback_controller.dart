import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/request/entity/apply_tip_off_req.dart';
import 'package:flutter_mixed/app/im/ui/base/im_base_controller.dart';
import 'package:get/get.dart';

import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../request/datasource/relation_datasource.dart';
import '../../request/entity/tip_off_resp.dart';

class TipOffFeedBackController extends BaseController {

  int contentInputLength = 0;

  TextEditingController textEditingController = TextEditingController();

  String sessionId = '';

  List<TipOff> items = [];

  @override
  void onInit() {
    super.onInit();
    sessionId = Get.arguments['sessionId'] ?? '';
    items = Get.arguments['items'] ?? [];

    if(items.isNotEmpty){
      update();
    }
  }

  bool isContentFeedback() => items.isEmpty;

  void onContentChanged(String comments) async {
    contentInputLength = comments.length;
    update();
  }

  void updateChecked(TipOff tipOff , bool? v) {
     tipOff.selected = v ?? false;
     update();
     if(!tipOff.selected) return;
     items.forEach((e) {
       if(e.id != tipOff.id){
         e.selected = false;
       }
     });
     update();
  }

  void apply() async {
     if(isContentFeedback()){
       _feedbackContent();
     }else {
       _feedbackItem();
     }
  }

  _feedbackContent() async {
    if(textEditingController.text.isEmpty){
      toast('请输入投诉内容');
      return;
    }

    var session = await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if(session == null){
      toast('找不到当前会话');
      return;
    }

    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var req = TipOffCommitReq(session.sessionType, textEditingController.text, sessionId, []);
    Get.loading();
    var resp = await datasource.submitComplain(req);
    await Get.dismiss();
    if(resp.success()){
      toast('提交成功');
      Get.back();
    }else {
      toast(resp.msg);
    }
  }

  _feedbackItem() async {
    // 提交某项
    var checkeds = items.where((element) => element.selected).toList();
    if(checkeds.isEmpty){
      toast('请选择一项');
      return;
    }
    var session = await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if(session == null){
      toast('找不到当前会话');
      return;
    }
    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var req = TipOffCommitReq(session.sessionType, '', sessionId, checkeds.map((e) => e.id ?? '').toList());
    Get.loading();
    var resp = await datasource.submitComplain(req);
    await Get.dismiss();
    if(resp.success()){
      toast('提交成功');
      Get.back();
    }else {
      toast(resp.msg);
    }
  }

  @override
  void onClose() {
    super.onClose();
    textEditingController.clear();
    textEditingController.dispose();
  }

}