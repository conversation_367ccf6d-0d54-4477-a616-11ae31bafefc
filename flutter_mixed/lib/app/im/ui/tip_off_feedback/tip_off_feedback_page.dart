import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/ui/tip_off_feedback/tip_off_feedback_controller.dart';
import 'package:get/get.dart';


class TipOffFeedbackPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<TipOffFeedBackController>(builder: (ctr){
       return ToolBar(
         title: '投诉',
         body: Column(
           mainAxisSize: MainAxisSize.max,
           children: [

             _buildTip(ctr),

              ctr.isContentFeedback() ?
              _buildInput(ctr) : _buildFeedbackList(ctr),

               10.gap,
               Row(
                 children: [
                   16.gap,
                   Expanded(child: MaterialButton(
                     padding: const EdgeInsets.symmetric(vertical: 10),
                     color: const Color(0xff0A6DFF),
                       child: const Text('提交' , style: TextStyle(color: Colors.white),),
                       onPressed: () => ctr.apply())),
                   16.gap,

                 ],
               ),

           ],
         ),
       );
    });
  }

  _buildTip(TipOffFeedBackController ctr) {
    return ctr.isContentFeedback() ? Container():

    Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(top: 10 , left: 20 , bottom: 10),
      child: const Text('请选择投诉行为' , style: TextStyle(fontSize: 15 , color: Color(0xff323232)),),
    );

  }

  _buildFeedbackList(TipOffFeedBackController ctr) {
    return Expanded(child: ListView.builder(
        itemCount: ctr.items.length,
        itemBuilder: (ctx , index) {
          var body = ctr.items[index];
          return Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 8 ,horizontal: 16),
            child: Row(
              children: [
                Expanded(child: Text(body.content ?? '' , style: TextStyle(fontSize: 14 ,color: Color(0xff323232)))),
                Checkbox(value: body.selected, onChanged: (b){
                  ctr.updateChecked(body , b);
                }),
              ],
            ),
          );
        }));
  }

  _buildInput(TipOffFeedBackController ctr ) {
     return Column(
       children: [
         Container(
           color: Colors.white,
           child: TextField(
               keyboardType: TextInputType.multiline,
               maxLines: 10,
               style: const TextStyle(fontSize: 13, height: 1.5),
               controller: ctr.textEditingController,
               inputFormatters: <TextInputFormatter>[
                 LengthLimitingTextInputFormatter(200)
               ],
               decoration: const InputDecoration(
                   hintText: "投诉内容（必填）",
                   hintStyle: TextStyle(fontSize: 14 , color: Color(0xff86909C)),
                   border: InputBorder.none,
                   contentPadding: EdgeInsets.all(10)
               ),
               onChanged: (value) {
                 ctr.onContentChanged(value);
               }
           ),
         ),
         Container(
           padding: const EdgeInsets.all(10),
           alignment: Alignment.centerRight,
           color: Colors.white,
           child: Text('${ctr.contentInputLength}/200' , style: const TextStyle(fontSize: 15 , color: Color(0xffD9D9D9))),
         ),
       ],
     );
  }


}