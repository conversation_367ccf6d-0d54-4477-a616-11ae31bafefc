


// im 打开地图去定位，返回位置并发送
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/tianditu/tianditu_manage.dart';

import '../../../../logger/logger.dart';
import '../chat/entity/message_item_data.dart';

void openNativeMap(double? lat, double? lon, String? address, String? detail) async {
  var param = {
    'lat': lat,
    'lon': lon,
    'poiName': address,
    'poiAddress': detail
  };
  Channel().invoke(CHANNEL_OPEN_MAP, param);
  return;

  var params = await toNavigate(lat, lon, address, detail);
  openWebView(params);
  
}


Future<TempLocationData?> shareMapLocation() async {
  Map<dynamic,dynamic>? resultMap = await Channel().invokeMap(
    Channel_Native_Approve_Postion,);
  logger('Channel_Native_Approve_Postion 返回数据 ${resultMap}');

  if(resultMap == null) return null;
  var address = resultMap['address'];
  var title = resultMap['positioning'];
  var latitude = resultMap['latitude'];
  var longitude = resultMap['longitude'];
  var url = resultMap['uri'];

  var model = TempLocationData()
    ..address = address
    ..title = title
    ..longitude = longitude
    ..latitude = latitude
    ..addressImgUrl = url
  ;
  return model;
}