//
// import 'package:flutter/material.dart';
// import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
// import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
// import 'package:flutter_baidu_mapapi_utils/flutter_baidu_mapapi_utils.dart';
// import 'package:flutter_bmflocation/flutter_bmflocation.dart';
// import 'package:flutter_mixed/app/extension/get_extension.dart';
// import 'package:flutter_mixed/app/im/utils/location_helper.dart';
// import 'package:flutter_mixed/app/modules/workStand/common/focus_switch_widget.dart';
// import 'package:flutter_mixed/app/utils/logger.dart';
// import 'package:get/get.dart';
// import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';
//
// import '../../../../res/assets_res.dart';
// import '../../../common/widgets/widgets.dart';
//
// class MapSearchController extends GetxController {
//
//   double? lat;
//   double? lon;
//   String? address;
//   String? addressDetail;
//
//   double? mylat;
//   double? mylon;
//   String? city; // 定位的时候获取city信息，用于poi检索
//
//   String? currentKey;
//
//   final List<SearchResultUIData> suggestResultList = [];
//   final List<SearchResultUIData> searchResultList = [];
//
//   BMFMapController? myMapController;
//
//   final LocationFlutterPlugin myLocPlugin = LocationFlutterPlugin();
//
//   BMFSuggestionSearch suggestionSearch = BMFSuggestionSearch();
//   BMFPoiCitySearch citySearch = BMFPoiCitySearch();
//
//   FocusNode searchJobFocusNode = FocusNode();
//
//   TextEditingController searchJobEditingController = TextEditingController();
//
//   BMFMapOptions mapOptions = BMFMapOptions(
//       center: BMFCoordinate(39.917215, 116.380341),
//       zoomLevel: 12,
//       mapPadding: BMFEdgeInsets(left: 30, top: 0, right: 30, bottom: 0));
//
//   @override
//   void onInit() {
//     super.onInit();
//
//     LocationHelper().initLocation();
//
//     //定位回调
//     myLocPlugin.seriesLocationCallback(callback: _locationResult);
//     // 检索回调
//     suggestionSearch.onGetSuggestSearchResult(callback: _onSuggestResult);
//     // 搜索回调
//     citySearch.onGetPoiCitySearchResult(callback: _onGetPoiCitySearchResult);
//   }
//
//   // 添加marker并居中显示
//   addMarker(String title,String subTitle,double lat,double lon) {
//     List<BMFMarker> markers = [];
//     BMFMarker marker = BMFMarker.icon(
//       position: BMFCoordinate(lat, lon),
//       icon: AssetsRes.ICON_LOCATION_CENTER,
//       title: title,  // key
//       subtitle: subTitle, // address
//     );
//     markers.add(marker);
//     myMapController?.cleanAllMarkers();
//     myMapController?.addMarkers(markers);
//     myMapController?.setCenterCoordinate(markers.first.position, true);
//   }
//
//   /// 城市内搜索  ----  回调
//   void _onGetPoiCitySearchResult(
//       BMFPoiSearchResult result, BMFSearchErrorCode errorCode) {
//     if (errorCode != BMFSearchErrorCode.NO_ERROR) {
//       Get.dismiss();
//       var error = "检索失败" + "errorCode:${errorCode.toString()}";
//       toast(error);
//       logger(error);
//       return;
//     }
//     Get.dismiss();
//
//     var r = result.poiInfoList;
//     if(r == null || r.isEmpty){
//       return;
//     }
//
//     var l = r.map((e) => SearchResultUIData(e.name, e.address, e.pt?.latitude, e.pt?.longitude)).toList();
//     searchResultList.clear();
//     searchResultList.addAll(l);
//     update();
//   }
//
//   testMockSuggestResult() {
//     suggestResultList.clear();
//     suggestResultList.add(SearchResultUIData('suggestkey1', 'district', 222.444, 444.4));
//     suggestResultList.add(SearchResultUIData('suggestkey2', 'district', 222.444, 444.4));
//     suggestResultList.add(SearchResultUIData('suggestkey3', 'district', 222.444, 444.4));
//     suggestResultList.add(SearchResultUIData('suggestkey4', 'district', 222.444, 444.4));
//     update();
//   }
//
//   testMockSearchResult() {
//     searchResultList.clear();
//     searchResultList.add(SearchResultUIData('key1', 'district', 222.444, 444.4 , selected: true));
//     searchResultList.add(SearchResultUIData('key2', 'district', 222.444, 444.4));
//     searchResultList.add(SearchResultUIData('key3', 'district', 222.444, 444.4));
//     searchResultList.add(SearchResultUIData('key4', 'districtdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrictdistrict', 222.444, 444.4));
//     update();
//   }
//
//   void _onSuggestResult(BMFSuggestionSearchResult result, BMFSearchErrorCode errorCode) {
//     if((result.suggestionList ?? []).isEmpty) return;
//     var l = result.suggestionList!.map((e) => SearchResultUIData(
//       e.key, e.district , e.location?.latitude , e.location?.longitude ,
//     )).toList();
//
//     searchResultList.clear();
//     searchResultList.addAll(l);
//     update();
//   }
//
//   void _locationResult(BaiduLocation result) {
//     mylat = result.latitude;
//     mylon = result.longitude;
//     city = result.city;
//     logger('我的位置：$city, $mylat , $mylon');
//   }
//
//   navigateNativeMap(String? address ,double? lat, double? lon) async {
//     // 我的位置
//     BMFCoordinate coordinate1 = BMFCoordinate(39.998691, 116.508936);
//     String startName = "我的位置";
//     // 百度大厦坐标
//     String endName = "${address ??''}";
//     BMFCoordinate coordinate2 = BMFCoordinate(lat ?? 39.998691, lon ?? 116.508936);
//
//     List<BMFWayPointInfo> viaPoints = [];
//     viaPoints.add(BMFWayPointInfo(
//         pt: BMFCoordinate(lat ?? 39.998691, lon ?? 116.508936), name: address));
//     BMFOpenNaviOption naviOption = BMFOpenNaviOption(
//         startCoord: coordinate1,
//         endCoord: coordinate2,
//         startName: startName,
//         endName: endName,
//         naviType: BMFNaviType.DriveNavi,
//         appScheme: 'baidumapsdk://mapsdk.baidu.com',
//         // 指定返回自定义scheme
//         appName: 'baidumap',
//         // 应用名称
//         isSupportWeb: false,
//         viaPoints: viaPoints); // 调起百度地图客户端驾车导航失败后（步行、骑行导航设置该参数无效），是否支持调起web地图，默认：true
//
//     BMFOpenErrorCode? flag = await BMFOpenMapUtils.openBaiduMapNavi(naviOption);
//   }
//
//   // 检索
//   suggest(String keyWord) async  {
//     currentKey = keyWord;
//     BMFSuggestionSearchOption suggestionSearchOption =
//     BMFSuggestionSearchOption(keyword: keyWord, cityname: city);
//     bool flag = await suggestionSearch.suggestionSearch(suggestionSearchOption);
//
//     // testMockSuggestResult();
//   }
//
//   search(String key) async {
//
//     // clearInput();
//     // testMockSearchResult();
//     // return;
//
//
//     clearInput();
//
//     Get.loading();
//     /// 检索参数
//     BMFPoiCitySearchOption citySearchOption = BMFPoiCitySearchOption(
//         city: city,
//         keyword: key,
//         isCityLimit: false,
//         pageSize: 1,
//         scope: BMFPoiSearchScopeType.DETAIL_INFORMATION);
//     bool result = await citySearch.poiCitySearch(citySearchOption);
//   }
//
//   clearInput() {
//     hideKeyboard(Get.context!);
//     currentKey = '';
//     searchJobEditingController.clear();
//     searchResultList.clear();
//     update();
//   }
//
//   // 选择该结果后，选择状态更新, 同时在地图上marker
//   checkItem(SearchResultUIData data) async {
//     for (var element in searchResultList) {
//       if(element.key != data.key){
//         element.selected = false;
//       }else{
//         element.selected = true;
//       }
//     }
//     addMarker(data.key ??'', data.district ??'' , data.lat ?? 0 , data.lon ?? 0);
//     update();
//   }
//
//   sendLocation() async {
//     if(searchResultList.isEmpty) return;
//     var selected = searchResultList.where((element) => element.selected == true).toList();
//     if(selected.isEmpty) return;
//     var checkItem = selected.first;
//     // todo 发送位置信息给IM
//     var uri = "http://api.map.baidu.com" +
//         "/staticimage?width=450&height=300&" +
//         "center=${checkItem.lon},${checkItem.lat}" +
//         "&zoom=17&markers=${checkItem.lon},${checkItem.lat}" +
//         "&markerStyles=m,A";
//     logger('发送位置： ${uri}');
//     Get.back(result: {
//       'uri':uri
//     });
//   }
//
// }
//
// class SearchResultUIData {
//   String? key;
//   String? district;
//   double? lat;
//   double? lon;
//   bool? selected = false;
//
//   SearchResultUIData(this.key, this.district, this.lat, this.lon ,{this.selected = false});
//
// }