// import 'package:flutter/material.dart';
// import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
// import 'package:flutter_bmflocation/flutter_bmflocation.dart';
// import 'package:flutter_mixed/app/extension/number_size_ex.dart';
// import 'package:flutter_mixed/app/im/widget/some_widget.dart';
// import 'package:flutter_mixed/app/utils/string-util.dart';
// import 'package:flutter_mixed/res/assets_res.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get_state_manager/src/simple/get_state.dart';
// import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
//
// import '../../../common/widgets/title_bar.dart';
// import '../../../modules/group_member/icon_search_input_field.dart';
// import 'map_search_controller.dart';
//
// /// 地图位置POI检索页面
// /// 入口：1 考勤；  2 im 发送位置
// /// 包含检索列表； 搜索列表
// class MapSearchPage extends StatelessWidget {
//
//   MapSearchPage({super.key});
//
//   final _screenW = ScreenUtil().screenWidth;
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<MapSearchController>(builder: (controller) {
//       return ToolBar(
//         title: '请选择地点',
//         actions: [
//           TextButton(onPressed: (){
//             // 发送选择的位置
//             controller.sendLocation();
//           }, child: const Text('发送',style: TextStyle(fontSize: 15 ,color: Colors.blue),))
//         ],
//         body: Container(
//           color: Colors.white,
//           child: Stack(
//             children: [
//               Column(
//                 children: [
//                   Container(
//                     padding: const EdgeInsets.symmetric(horizontal: 10),
//                     child: SearchInputField(
//                       hintText: '搜索地点',
//                       focusNode: controller.searchJobFocusNode,
//                       onChanged: (e) {
//                         controller.suggest(e);
//                       },
//                       textFieldContainer: controller.searchJobEditingController,
//                       clearInput: () {
//                         controller.clearInput();
//                       },
//                     ),
//                   ),
//                   Expanded(child: Stack(
//                     children: [
//                       // map ，search,
//                       Column(
//                         children: [
//                           Container(
//                             height: _screenW - 50,
//                             child: BMFMapWidget(onBMFMapCreated: (mapController){
//                               onBMFMapCreated(mapController , controller);
//                             },mapOptions: BMFMapOptions(
//                                 center: BMFCoordinate(controller.lat ?? 39.917215, controller.lon ?? 116.380341),
//                                 zoomLevel: 12,
//                                 mapPadding: BMFEdgeInsets(left: 30, top: 0, right: 30, bottom: 0))),
//                           ),
//
//                           Expanded(child: Container(
//                             child: ListView.builder(
//                                 itemCount: controller.searchResultList.length,
//                                 itemBuilder: (ctx , index){
//                                   return _buildSearchItem(controller.searchResultList[index] ,controller);
//                                 }),
//                           ),),
//                         ],
//                       ),
//
//                       // suggest list
//                       _buildSuggestPage(controller),
//                     ],
//                   ),),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       );
//     });
//   }
//
//   _buildSuggestPage(MapSearchController controller) {
//     if(StringUtil.isEmpty(controller.currentKey)) return Container();
//     return Container(
//       color: Colors.white,
//       child: Column(
//         children: [
//           Expanded(child: ListView.builder(
//               itemCount: controller.suggestResultList.length,
//               itemBuilder: (ctx , index){
//                 return _buildSuggestItem(controller.suggestResultList[index] , controller);
//               }))
//         ],
//       ),
//     );
//   }
//
//   // 检索列表 item
//   _buildSuggestItem(SearchResultUIData item , MapSearchController controller) {
//     return InkWell(
//       onTap: (){
//         controller.search(item.key ?? '');
//       },
//       child: Container(
//         padding: const EdgeInsets.symmetric(vertical: 4 ,horizontal: 14),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Expanded(child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisSize: MainAxisSize.max,
//               children: [
//                 Text(item.key ?? '', maxLines:2 ,style: TextStyle(fontSize: 16 , fontWeight: FontWeight.bold, color: Color(0xff323232)),),
//                 5.gap,
//                 Text(item.district ?? '', maxLines:2 ,style: TextStyle(fontSize: 13 ,color: Color(0xff808080)),),
//                 7.gap,
//                 line,
//               ],
//             )),
//           ],
//         ),
//       ),
//     );
//   }
//
//   // 搜索列表 item
//   _buildSearchItem(SearchResultUIData item , MapSearchController controller) {
//     return InkWell(
//       onTap: (){
//         controller.checkItem(item);
//       },
//       child: Container(
//         padding: const EdgeInsets.symmetric(vertical: 4 ,horizontal: 14),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Expanded(child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisSize: MainAxisSize.max,
//               children: [
//                 Text(item.key ?? '', maxLines:2 ,style: TextStyle(fontSize: 16 , fontWeight: FontWeight.bold,color: Color(0xff323232)),),
//                 5.gap,
//                 Text(item.district ?? '', maxLines:2 ,style: TextStyle(fontSize: 13 ,color: Color(0xff808080)),),
//                 7.gap,
//                 line,
//               ],
//             )),
//             if(item.selected == true)...[
//               Image.asset(AssetsRes.APPROVE_SELECTED , width: 17,height: 17,)
//             ]
//           ],
//         ),
//       ),
//     );
//   }
//
//
//
//   /// 定位模式
//   BMFUserTrackingMode _userTrackingMode = BMFUserTrackingMode.None;
//
//   /// 创建完成回调
//   void onBMFMapCreated(BMFMapController controller , MapSearchController getxController) {
//     getxController.myMapController = controller;
//
//     /// 地图加载回调
//     getxController.myMapController?.setMapDidLoadCallback(callback: () {
//       print('mapDidLoad-地图加载完成');
//
//       ///设置定位参数
//       locationAction(getxController);
//
//       ///启动定位
//       getxController.myLocPlugin.startLocation();
//       if (true) {
//         // 显示自己的位置
//         getxController.myMapController?.showUserLocation(true);
//         getxController.myMapController?.setUserTrackingMode(_userTrackingMode);
//       }
//     });
//
//     getxController.myMapController?.setMapClickedMarkerCallback(callback: (BMFMarker marker) {
//       print('marker点击----${marker.id}');
//     });
//
//     getxController.myMapController?.setMapDragMarkerCallback(callback: (BMFMarker marker,
//         BMFMarkerDragState newState, BMFMarkerDragState oldState) {
//       print('marker推拽----${marker.id}');
//     });
//
//     getxController.addMarker(getxController.address ??'', getxController.addressDetail ?? '',
//         getxController.lat ?? 0, getxController.lon ??0);
//   }
//
//   void locationAction(MapSearchController getxController) async {
//     /// 设置android端和ios端定位参数
//     /// android 端设置定位参数
//     /// ios 端设置定位参数
//     Map iosMap = _initIOSOptions().getMap();
//     Map androidMap = _initAndroidOptions().getMap();
//
//     await getxController.myLocPlugin.prepareLoc(androidMap, iosMap);
//   }
//
//   /// 设置地图参数
//   BaiduLocationAndroidOption _initAndroidOptions() {
//     BaiduLocationAndroidOption options = BaiduLocationAndroidOption(
//         locationMode: BMFLocationMode.hightAccuracy,
//         isNeedAddress: true,
//         isNeedAltitude: true,
//         isNeedLocationPoiList: true,
//         isNeedNewVersionRgc: true,
//         isNeedLocationDescribe: true,
//         openGps: true,
//         scanspan: 4000,
//         coordType: BMFLocationCoordType.bd09ll);
//     return options;
//   }
//
//   BaiduLocationIOSOption _initIOSOptions() {
//     BaiduLocationIOSOption options = BaiduLocationIOSOption(
//         coordType: BMFLocationCoordType.bd09ll,
//         desiredAccuracy: BMFDesiredAccuracy.best,
//         allowsBackgroundLocationUpdates: true,
//         pausesLocationUpdatesAutomatically: false);
//     return options;
//   }
//
//
// }