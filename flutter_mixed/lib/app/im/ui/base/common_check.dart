import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_controller.dart';
import 'package:get/get.dart';

class CommonCheck {
  //是否在聊天页面
  static checkIsOnTheChatPage(String tag){
    try {
      bool isRegistered = Get.isRegistered<ChatController>(tag: tag);
      return isRegistered;
    } catch (e) {
      return false;
    }
  }

  //是否在通知页(系统通知、工作通知、金蝶HR,其他类型有公司切换不能直接消红点)
  static checkIsOnTheNoticePage(String tag){
    try {
      bool isRegistered = Get.isRegistered<SystemNotificationListController>(tag: tag);
      return isRegistered;
    } catch (e) {
      return false;
    }
  }

  //通知页面查询红点
  static noticeRequestUnread(String tag){
    try {
      bool isRegistered = Get.isRegistered<SystemNotificationListController>(tag: tag);
      if (isRegistered) {
        SystemNotificationListController? systemNotificationListController = Get.find<SystemNotificationListController>(tag: tag);
        systemNotificationListController.getOfflineUnreadData();

      }
    } catch (e) {
      
    }
  }

  //通知页面查询红点
  static noticeFetchMessage(String sessionId){
    try {
      bool isRegistered = Get.isRegistered<SystemNotificationListController>(tag: sessionId);
      if (isRegistered) {
        SystemNotificationListController? systemNotificationListController = Get.find<SystemNotificationListController>(tag: sessionId);
        systemNotificationListController.dealReciveSessionChange();

      }
    } catch (e) {
      
    }
  }

  //消息页面向下拉取消息
  static chatControllerFetchMessage(String sessionId){
    bool isRegistered = Get.isRegistered<ChatController>(tag: sessionId);
    if (isRegistered) {
      ChatController chatController = Get.find<ChatController>(tag: sessionId);
      chatController.dealReciveSessionChange();
    }
  }
}