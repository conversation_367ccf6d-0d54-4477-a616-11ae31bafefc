import 'dart:async';

import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/ext/list_extension.dart';
import 'package:flutter_mixed/app/im/ui/base/im_base_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../common/widgets/widgets.dart';
import '../../constant/im_cache_global.dart';
import '../../db/db_helper.dart';
import '../../dispatch_parser/multi_sync_event_model.dart';
import '../../im_client_manager.dart';
import '../../request/datasource/disturb_datasource.dart';
import '../../request/datasource/offline_datasource.dart';
import '../../request/entity/distrub_request_resp.dart';
import '../../request/entity/msg_top_resp.dart';

/// 单群聊设置中通用部分: 免打扰； 置顶
abstract class BaseChatInfoVm extends BaseController {

  String sessionId = '';

  ChatSettingUIData settingUIData = ChatSettingUIData(false, false);

  StreamSubscription? eventMsgTopStreamSubscription;
  StreamSubscription? eventMsgDisturbStreamSubscription;

  @override
  void onInit() async {
    super.onInit();
    var arguments = Get.arguments;
    if(arguments == null) {
      Get.back();
      return;
    }
    await loadUserInfo();

    sessionId = arguments['sessionId'];

    eventMsgTopStreamSubscription = eventBus.on<EventChatInfoMsgTop>().listen((event) async {
        if(event.sessionId == sessionId){
          settingUIData.msgIsTop = event.isTop;
          update();
        }
    });

    eventMsgDisturbStreamSubscription = eventBus.on<EventSessionMsgDisturb>().listen((event) {
       if(sessionId == event.sessionId){
         settingUIData.isDisturbOpen = event.disturbOpen;
         update();
       }
    });

    DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId).then((session) {
      settingUIData.msgIsTop = session?.sessionTop == 1;
      settingUIData.isDisturbOpen = session?.noDisturb == 1;

      update();
    });
  }


  // 设置置顶信息， 1 调用接口，2 发送im 消息，3 存储进db ，4 更新UI
  updateMsgTop(bool v) async {
    var datasource = OfflineDataSource(retrofitDio , baseUrl: Host.IM_OFFLINE_HOST);
    var ownImId = await UserHelper.getOwnImId();
    if(v){
      // 设置置顶
      List<String> singles = (!isGroup()) ? [sessionId] : [];
      List<String> groups = (isGroup()) ? [sessionId] : [];
      var req = MsgTopResp(single: singles , group: groups, notice: []);
      var resp = await datasource.createMsgTops(req);
      if(resp.success()){
        toast('置顶设置成功');
        settingUIData.msgIsTop = v;
        update();
        ImClientManager.instance.sendTopMsg(ownerId, ownImId, sessionId, true);
        _updateMsgTopDb(true);
      }else {
        toast(resp.msg);
      }
    }else {
      // 取消置顶
      var req = MsgTopReq(isGroup() ? 2: 1 , sessionId);
      var resp = await datasource.delMsgTops(req);
      if(resp.success()){
        toast('已取消置顶');
        settingUIData.msgIsTop = v;
        update();
        ImClientManager.instance.sendTopMsg(ownerId, ownImId, sessionId, false);
        _updateMsgTopDb(false);
      }else {
        toast(resp.msg);
      }
    }
  }

  updateDisturb(bool v) async {
    var datasource = DisturbDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);
    OpenSingleDisturbReq req = OpenSingleDisturbReq(sessionId);
    if(v){
      var resp = isGroup() ? await datasource.openGroupDisturb(req): await datasource.openSingleDisturb(req);
      if(resp.success()){
        toast('已开启免打扰');
        settingUIData.isDisturbOpen = v;
        update();
        _updateMsgDisturbDb(true);
        _updateMsgDisturbCache(true);
      }else{
        toast(resp.msg);
      }
    }else{
      var resp = isGroup() ? await datasource.closeGroupDisturb(sessionId) : await datasource.closeSingleDisturb(sessionId);
      if(resp.success()){
        toast('已关闭免打扰');
        settingUIData.isDisturbOpen = v;
        update();
        _updateMsgDisturbDb(false);
        _updateMsgDisturbCache(false);
      }else{
        toast(resp.msg);
      }
    }
  }

  // 更新【免打扰】db
  _updateMsgDisturbDb(bool disturbOpen) async {
    var session = await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if(session != null){
      session.noDisturb = disturbOpen ? 1: 0;
      DbHelper.insertSession(session);
    }
  }

  // 更新【免打扰】 文件缓存
  _updateMsgDisturbCache(bool disturbOpen) async {
    var list = await ImCacheData.instance.getSingleGroupDisturbIds();
    var map = list.asmap();
    if(disturbOpen){
      ImCacheData.instance.addSingleAndGroupChatDisturbSessionId(sessionId);
    }else {
      ImCacheData.instance.removeSingleAndGroupChatSessionId(sessionId);
    }
  }

  // 置顶状态更新db
  _updateMsgTopDb(bool isTop) async {
    var session = await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if(session != null){
      session.sessionTop = isTop ? 1: 0;
      DbHelper.insertSession(session);
    }
  }

  bool isGroup();

  @override
  void onClose() {
    super.onClose();
    eventMsgTopStreamSubscription?.cancel();
    eventMsgDisturbStreamSubscription?.cancel();
  }

}

class ChatSettingUIData {
  bool isDisturbOpen = false;
  bool msgIsTop = false;
  ChatSettingUIData(this.isDisturbOpen, this.msgIsTop);
}