

import 'dart:convert';

import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/request/entity/group_info_resp.dart';
import 'package:flutter_mixed/logger/logger.dart';


/// 判断输入文本框内容是否有@某人
/// 如果返回 null 为 没有@ 任何人
/// 如果返回 List<-1> 为 @ 全体人员
/// 如果返回 List<String> 为 @sb
List<String?> hasAtSb(List<GroupUser> userList , String text) {
  if(text.contains('@全体成员')) return ['-1'];

  RegExp mentionPattern = RegExp(r"@(\S+)");
  Iterable<RegExpMatch> matches = mentionPattern.allMatches(text);

  List<GroupUser> detectedUsers = [];

  for (var match in matches) {
    String mentionName = match.group(1)!; // 提取 @后面的名字
    GroupUser? matchedUser = userList.firstWhere(
          (user) => mentionName.contains(user.name ?? ''),
      orElse: () => GroupUser(), // 防止找不到崩溃
    );
    if (matchedUser.id != null) {
      detectedUsers.add(matchedUser);
    }
  }
  if(detectedUsers.isEmpty) return [];
  return detectedUsers.map((e) => e.id).toList();
}

String atSbInputResult(GroupUser selectMember , String originInputBody ) {
  var newInput = '';
  if(selectMember.id == null){
    // 全体人员
    if(originInputBody.endsWith('@')){
      newInput = '${originInputBody}全体成员';
    }else{
      newInput = '${originInputBody} @全体成员';
    }
  }else {
    // 某人
    if(originInputBody.endsWith('@')){
      newInput = '${originInputBody}${selectMember.name}';
    }else{
      newInput = '${originInputBody} @${selectMember.name}';
    }
  }
  return newInput;
}

int atSbExtStringMsgType(List<String?> ids){
  logger('atSbExtStringMsgType ==> $ids');
  if(ids.isEmpty) return ConstantImMsgType.SSChatMessageTypeText;
  if(ids.length == 1){
    if(ids.first == '-1'){
     return ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice;
    }else {
      return ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice;
    }
  }else{
    return ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice;
  }
}

ExtMsg? atSbExtString(List<String?> ids , String text, {String? quoteString}){
  if(ids.isEmpty) return null;
  var map;
  if(ids.length == 1){
    if(ids.first == '-1'){
      map = {
        'text' : text,
        if(quoteString != null) 'extendThree': quoteString
      };
    }else {
      if(ids.first != null){
        map = {
          'text' : text,
          'extendTwo': json.encode(ids),
          if(quoteString != null) 'extendThree': quoteString
        };
      }

    }
  }else{
    map = {
      'text' : text,
      'extendTwo': json.encode(ids),
      if(quoteString != null) 'extendThree': quoteString
    };
  }

  if(map == null) return null;

  var str = json.encode(map);
  var ext = ExtMsg()..ext1 = str;
  return ext;
}