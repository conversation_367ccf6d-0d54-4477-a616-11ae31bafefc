

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/request/entity/group_info_resp.dart';
import 'package:flutter_mixed/app/im/ui/simple_group_memebers_page/simple_group_member_ctl.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';

import '../../../common/config/config.dart';
import '../../../common/widgets/title_bar.dart';
import '../../../modules/group_member/icon_search_input_field.dart';

/// 群组 @ 某人 成员选择列表
class SimpleMembersPage extends StatelessWidget {

  SimpleMembersPage();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SimpleGroupMemberController>(builder: (controller) {
      return ToolBar(
        title: "请选择群组成员",
        backgroundColor: Colors.white,
        body: Column(
          children: [
            _buildSearchBar(context , controller),
            10.gap,
            Expanded(child: ListView.builder(
                itemCount: controller.uiList.length,
                itemBuilder: (context, index) {
                  GroupUser memberModel = controller.uiList[index];
                  return _buildItem(memberModel , controller);
                })),
          ],
        ),
      );
    });
  }

  _buildItem(GroupUser mGroupMember , SimpleGroupMemberController controller) {
    if(mGroupMember.id == null){
      return InkWell(
        onTap: (){
          controller.onTapMember(mGroupMember);
        },
        child: controller.isCanAtEveryone() ? Container(alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(bottom: 10),
          padding: const EdgeInsets.only(top: 10,left: 15),
          height: 30,
          child: Row(
            children: [
              Image.asset(AssetsRes.APPROVE_UNSELECTED ,width: 20,),
              5.gap,
              Text(
                '全体成员',
                style: const TextStyle(
                    fontSize: 14,
                    color: ColorConfig.themeCorlor),
              )
            ],
          ),
        ) : Container(),
      );
    }

    return InkWell(
      onTap: (){
        controller.onTapMember(mGroupMember);
      },
      child: Container(
        child: Row(
          children: [
            Container(
              margin: const EdgeInsets.only(left: 16 ,top: 8,bottom: 8),
              padding: const EdgeInsets.all(1),
              width: 40,
              height: 40,
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: SettingWidget.backImageProvider(mGroupMember.headimg,cache: true), fit: BoxFit.contain),
                  borderRadius:
                  BorderRadius.circular(8),
                  border: Border.all(
                      color: ColorConfig.lineColor, width: 0.5)),
            ),
            10.gap,
            Text(mGroupMember.name ?? '' , style: TextStyle(fontSize: 14 ,color: Color(0xff1D2129)),),
          ],
        ),
      ),
    );
  }

  _buildSearchBar(BuildContext context, SimpleGroupMemberController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SearchInputField(
        hintText: '请输入要搜索的用户名',
        focusNode: controller.searchJobFocusNode,
        onChanged: (e) {
          controller.searchMembers(e);

        },
        textFieldContainer: controller.searchJobEditingController,
        clearInput: () {
          controller.clearInput();
        },
      ),
    );
  }

}