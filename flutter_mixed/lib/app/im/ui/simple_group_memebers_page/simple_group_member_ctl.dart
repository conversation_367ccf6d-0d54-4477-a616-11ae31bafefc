import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../utils/string-util.dart';
import '../../request/entity/group_info_resp.dart';
import '../base/im_base_controller.dart';

class SimpleGroupMemberController extends BaseController {

  TextEditingController searchJobEditingController = TextEditingController();

  FocusNode searchJobFocusNode = FocusNode();

  String currentKey = '';

  List<GroupUser> groupMemberList = [];
  List<GroupUser> uiList = [];

  bool isCanAtEveryone() => Get.arguments['isCanAtEveryone'] ?? true;

  @override
  void onInit() {
    super.onInit();
    var sources = Get.arguments['groupMembers'];

    if(sources is List<GroupUser>){
      groupMemberList.clear();
      groupMemberList.addAll(sources);

      _buildOriginUIList();
    }
  }

  onTapMember(GroupUser user) {
    Get.back(result: user);
  }

  showMembers() {
    if(StringUtil.isEmpty(currentKey)){
      searchJobEditingController.clear();
      searchJobFocusNode.unfocus();
      _buildOriginUIList();
    }else {
      var dulist = groupMemberList.map((e) => e.id).toSet();
      var copyList = groupMemberList.map((e) => e).toList();
      copyList.retainWhere((element) => dulist.remove(element.id));
      var result = copyList.where((element) => (element.name?? '').contains(currentKey)).toList();
      uiList.clear();
      uiList.addAll(result);
    }
    update();
  }

  _buildOriginUIList() {
    uiList.clear();
    uiList.add(GroupUser());
    uiList.addAll(groupMemberList);
  }

  searchMembers(String key) {
    currentKey = key;
    showMembers();
  }

  clearInput() {
    currentKey = '';
    searchJobEditingController.clear();
  }

  @override
  void onClose() {
    super.onClose();
    searchJobEditingController.dispose();
  }

}