//
// import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
// import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
// import 'package:flutter_baidu_mapapi_utils/flutter_baidu_mapapi_utils.dart';
// import 'package:flutter_bmflocation/flutter_bmflocation.dart';
// import 'package:flutter_mixed/app/im/utils/location_helper.dart';
// import 'package:flutter_mixed/app/utils/logger.dart';
// import 'package:get/get.dart';
//
// class MapTargetController extends GetxController {
//
//   double? lat;
//   double? lon;
//   String? address;
//   String? addressDetail;
//
//   double? mylat;
//   double? mylon;
//   String? city; // 定位的时候获取city信息，用于poi检索
//
//   final LocationFlutterPlugin myLocPlugin = LocationFlutterPlugin();
//
//   BMFMapOptions mapOptions = BMFMapOptions(
//       center: BMFCoordinate(39.917215, 116.380341),
//       zoomLevel: 12,
//       mapPadding: BMFEdgeInsets(left: 30, top: 0, right: 30, bottom: 0));
//
//   @override
//   void onInit() {
//     super.onInit();
//     var argument = Get.arguments;
//     if(argument == null) {
//       Get.back();
//     }
//
//     LocationHelper().initLocation();
//
//     lat = argument['lat'];
//     lon = argument['lon'];
//     address = argument['address'];
//     addressDetail = argument['detail'];
//     update();
//
//
//     //定位回调
//     myLocPlugin.seriesLocationCallback(callback: (BaiduLocation result) {
//       mylat = result.latitude;
//       mylon = result.longitude;
//       city = result.city;
//       logger('我的位置：$city, $mylat , $mylon');
//     });
//
//   }
//
//   navigateNativeMap(String? address ,double? lat, double? lon) async {
//     // 我的位置
//     BMFCoordinate coordinate1 = BMFCoordinate(39.998691, 116.508936);
//     String startName = "我的位置";
//     // 百度大厦坐标
//     String endName = "${address ??''}";
//     BMFCoordinate coordinate2 = BMFCoordinate(lat ?? 39.998691, lon ?? 116.508936);
//
//     List<BMFWayPointInfo> viaPoints = [];
//     viaPoints.add(BMFWayPointInfo(
//         pt: BMFCoordinate(lat ?? 39.998691, lon ?? 116.508936), name: address));
//     BMFOpenNaviOption naviOption = BMFOpenNaviOption(
//         startCoord: coordinate1,
//         endCoord: coordinate2,
//         startName: startName,
//         endName: endName,
//         naviType: BMFNaviType.DriveNavi,
//         appScheme: 'baidumapsdk://mapsdk.baidu.com',
//         // 指定返回自定义scheme
//         appName: 'baidumap',
//         // 应用名称
//         isSupportWeb: false,
//         viaPoints: viaPoints); // 调起百度地图客户端驾车导航失败后（步行、骑行导航设置该参数无效），是否支持调起web地图，默认：true
//
//     BMFOpenErrorCode? flag = await BMFOpenMapUtils.openBaiduMapNavi(naviOption);
//     print('open - navi - errorCode = $flag');
//   }
//
//
//
//
// }