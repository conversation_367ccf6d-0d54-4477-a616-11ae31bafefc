//
// import 'package:flutter/material.dart';
// import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
// import 'package:flutter_bmflocation/flutter_bmflocation.dart';
// import 'package:flutter_mixed/app/extension/number_size_ex.dart';
// import 'package:flutter_mixed/app/im/ui/map_target/map_target_controller.dart';
// import 'package:flutter_mixed/res/assets_res.dart';
// import 'package:get/get_state_manager/src/simple/get_state.dart';
// import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
//
// import '../../../common/widgets/title_bar.dart';
//
// /// 地图消息点击进入的标记定位页面
// class MapTargetPage extends StatelessWidget {
//
//   MapTargetPage({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<MapTargetController>(builder: (controller) {
//       return ToolBar(
//         title: '位置信息',
//         body: Container(
//           color: Colors.white,
//           child: Column(
//             children: [
//               Expanded(child: Container(
//                 child: BMFMapWidget(onBMFMapCreated: (mapController){
//                   onBMFMapCreated(mapController , controller);
//                 },mapOptions: BMFMapOptions(
//                     center: BMFCoordinate(controller.lat ?? 39.917215, controller.lon ?? 116.380341),
//                     zoomLevel: 20,
//                     mapPadding: BMFEdgeInsets(left: 30, top: 0, right: 30, bottom: 0))),
//               )),
//               Container(
//                 padding: EdgeInsets.all(16),
//                 child: Row(
//                   children: [
//                     Expanded(child:
//                     Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(controller.address ?? '', maxLines:1 , overflow:TextOverflow.ellipsis,style: const TextStyle(fontSize: 15, color: Color(0xff333333)),),
//                         7.gap,
//                         Text(controller.addressDetail ?? '', maxLines:1 , overflow:TextOverflow.ellipsis, style: const TextStyle(fontSize: 12, color: Color(0x99999999)),),
//                       ],
//                     )),
//                     10.gap,
//                     ElevatedButton(
//                         onPressed: (){
//                             controller.navigateNativeMap(controller.address,
//                               controller.lat, controller.lon
//                             );
//                         }, child: Text('导航到这里' , style: TextStyle(fontSize: 15,color: Colors.white),))
//
//                   ],
//                 ),
//               ),
//               15.gap,
//             ],
//           ),
//         ),
//       );
//
//     });
//   }
//
//   BMFMapController? myMapController;
//
//
//   /// 定位模式
//   BMFUserTrackingMode _userTrackingMode = BMFUserTrackingMode.None;
//
//   /// 创建完成回调
//   void onBMFMapCreated(BMFMapController controller , MapTargetController getxController) {
//     myMapController = controller;
//
//     /// 地图加载回调
//     myMapController?.setMapDidLoadCallback(callback: () {
//       print('mapDidLoad-地图加载完成');
//
//       ///设置定位参数
//       locationAction(getxController);
//
//       ///启动定位
//       getxController.myLocPlugin.startLocation();
//       if (true) {
//         // 显示自己的位置
//         myMapController?.showUserLocation(true);
//         myMapController?.setUserTrackingMode(_userTrackingMode);
//       }
//
//     });
//
//     myMapController?.setMapClickedMarkerCallback(callback: (BMFMarker marker) {
//       print('marker点击----${marker.id}');
//     });
//
//     myMapController?.setMapDragMarkerCallback(callback: (BMFMarker marker,
//         BMFMarkerDragState newState, BMFMarkerDragState oldState) {
//       print('marker推拽----${marker.id}');
//     });
//
//     addMarker(getxController);
//   }
//
//   late BMFMarker _marker;
//
//   void addMarker(MapTargetController getxController) {
//     _marker = BMFMarker.icon(
//         position: BMFCoordinate(getxController.lat ?? 39.917215, getxController.lon ?? 116.380341),
//         icon: AssetsRes.ICON_LOCATION_CENTER,
//         title: getxController.address ??'',
//         subtitle: getxController.addressDetail ?? '',
//         identifier: 'flutter_marker',
//         enabled: true,
//         draggable: false);
//     myMapController?.addMarker(_marker);
//   }
//
//   void locationAction(MapTargetController getxController) async {
//     /// 设置android端和ios端定位参数
//     /// android 端设置定位参数
//     /// ios 端设置定位参数
//     Map iosMap = _initIOSOptions().getMap();
//     Map androidMap = _initAndroidOptions().getMap();
//
//     await getxController.myLocPlugin.prepareLoc(androidMap, iosMap);
//   }
//
//   /// 设置地图参数
//   BaiduLocationAndroidOption _initAndroidOptions() {
//     BaiduLocationAndroidOption options = BaiduLocationAndroidOption(
//         locationMode: BMFLocationMode.hightAccuracy,
//         isNeedAddress: true,
//         isNeedAltitude: true,
//         isNeedLocationPoiList: true,
//         isNeedNewVersionRgc: true,
//         isNeedLocationDescribe: true,
//         openGps: true,
//         scanspan: 4000,
//         coordType: BMFLocationCoordType.bd09ll);
//     return options;
//   }
//
//   BaiduLocationIOSOption _initIOSOptions() {
//     BaiduLocationIOSOption options = BaiduLocationIOSOption(
//         coordType: BMFLocationCoordType.bd09ll,
//         desiredAccuracy: BMFDesiredAccuracy.best,
//         allowsBackgroundLocationUpdates: true,
//         pausesLocationUpdatesAutomatically: false);
//     return options;
//   }
//
//
// }