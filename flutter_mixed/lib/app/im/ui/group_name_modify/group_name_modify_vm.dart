

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/ui/base/im_base_controller.dart';
import 'package:flutter_mixed/app/im/ui/single_chat_info/single_chat_info_vm.dart';
import 'package:get/get.dart';

import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../request/datasource/relation_datasource.dart';

class GroupNameModifyVM extends BaseController {

  String? groupName;

  String? groupId;

  TextEditingController textEditingController = TextEditingController();

  @override
  Future childOnInit() async {
    var para = Get.arguments;
    if(para == null){
      Get.back();
      return;
    }
    groupId = para['groupId'];
    groupName = para['groupName'];

    textEditingController.text = groupName ?? '';
  }

  Future<bool> modifyGroupName() async {
    if(textEditingController.text.isEmpty){
      toast('不能为空');
      return false;
    }

    Get.loading();
    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var req = {
      'groupId': groupId,
      'name': textEditingController.text,
    };
    var resp = await datasource.updateGroup(req);
    await Get.dismiss();
    if(resp.success()){
      // 修改session 名称
      var session = await DbHelper.getSessionByOwnerId2SessionId(ownerId, groupId?? '');
      if(session != null){
        DbHelper.insertSession(session..name = textEditingController.text);
      }
      return true;
    }else{
      toast(resp.msg);
      return false;
    }
  }

}

class UpdateGroupNameGetModel{}