

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/im/ui/group_name_modify/group_name_modify_vm.dart';
import 'package:get/get.dart';

import '../../../common/config/config.dart';

// 修啊群组名称
class GroupNameModifyPage extends StatelessWidget {

  const GroupNameModifyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GroupNameModifyVM>(builder: (controller){
      return ToolBar(
        title: '修改群组名称',
        backgroundColor: Colors.white,
        body: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              child: TextField(

                onChanged: (value) {
                  controller.groupName =
                      controller.textEditingController.text;
                },
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.search,
                controller: controller.textEditingController,
                style: const TextStyle(
                  color: Color(0xff323232),
                  fontSize: 15,
                ),
                inputFormatters: <TextInputFormatter>[
                  LengthLimitingTextInputFormatter(30) //限制长度
                ],
                decoration: InputDecoration(
                    contentPadding: const EdgeInsets.only(
                        top: 0, bottom: 0),

                    hintText: '请输入群名称',
                    hintStyle: const TextStyle(
                      color: ColorConfig.desTextColor,
                      fontSize: 15,
                    )),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () async {
              var r = await controller.modifyGroupName();
              if(r){
                Get.back(result: UpdateGroupNameGetModel());
              }
          }, child: Text('完成', style: TextStyle(fontSize: 13 ,color: Colors.blue),))
        ],
      );

    });
  }

}