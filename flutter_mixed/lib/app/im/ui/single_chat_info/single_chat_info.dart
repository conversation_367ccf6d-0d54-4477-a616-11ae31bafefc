import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/route/route_extension.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/single_chat_info/single_chat_info_vm.dart';
import 'package:flutter_mixed/app/im/widget/some_widget.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../common/config/config.dart';
import '../../../common/dialog/msg-dialog.dart';

/// 单聊，个人设置（信息）页面
class SingleChatInfo extends StatelessWidget {

  const SingleChatInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SingleChatInfoVm>(builder: (_) {
      return ToolBar(
        body: Column(
          children: [
            // 个人信息详情区
              Container(
                padding: EdgeInsets.symmetric(vertical: 12),
                color: Colors.white,
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {
                        RouteHelper.route(Routes.USER_INFO, arguments: {
                          'userId': _.imChatId,
                          'type': 1
                        }); 
                      },
                      child: Row(
                        children: [
                          19.gap,
                          ImageLoader(
                            url: _.entity.avatar, width: 43, height: 43,
                          ),
                          10.gap,
                          Text(_.entity.name ??'' , style: TextStyle(fontSize: 14 ,color: Color(0xff000000)),)
                        ],
                      ),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: (){
                        // 发起群聊
                        RouteHelper.routePath(Routes.GROUP_ADD_MEMBERS , arguments: _.createGroupParam());
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                           Image.asset(AssetsRes.ICON_SEND_GROUP_MSG , width: 25, height: 25,),
                          10.gap,
                          Text('发起群聊',style: TextStyle(fontSize: 13 ,color: Color(0xff2479ed)),)
                        ],
                      ),
                    ),

                    10.gap,
                  ],
                ),
              ),

             10.gap,
             //

            _buildRowItem('清空聊天记录' , itemClick: (){
              MsgDiaLog('提示', '确定删除聊天记录吗？', '取消', '确定', () {
                Navigator.of(context).pop();
              }, () async {
                Navigator.of(context).pop();
                await _.clearChatRecord();
                Get.back();
              }, rightColor: ColorConfig.deleteCorlor).show();
            }),

            line,
            _buildRowItem('消息免打扰' , checked: _.settingUIData.isDisturbOpen
                ,checkF: (v) => _.updateDisturb(v)),

            line,

            _buildRowItem('置顶聊天' , checked: _.settingUIData.msgIsTop
                , checkF: (v) => _.updateMsgTop(v)),

            line,

            _buildRowItem('举报电话：18301476078' , showArrow: true, itemClick: (){
                 // toast('11');
                if(_.title){
                  Get.dialog(
                      const AlertDialog(
                        backgroundColor: Colors.white,
                        elevation: 1,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(8),
                          ),
                        ),
                        content: Text('经审核，您投诉的用户存在违规行为，系统已封禁其账号。'),
                      ),
                  );
                }else {
                  Routes.TIP_OFF_MAIN.toPage(arguments: {
                    'sessionId': _.sessionId,
                  });
                }
            }),
          ],
        ),
      );
    });
  }

  _buildRowItem(String label , {bool? showArrow = false , bool? checked , Function? checkF , VoidCallback? itemClick}) {
    return InkWell(
      onTap: (){
        itemClick?.call();
      },
      child: Container(
        color: Colors.white,
        height: 50,
        child: Row(
          children: [
            14.gap,
            Expanded(child: Text(label , style: const TextStyle(fontSize: 13 , color: Color(0xff323232)),),),
            if(showArrow == true)...[
              arrow,
            ],

            if(checked != null)...[
              CupertinoSwitch(
                  activeColor: ColorConfig.themeCorlor,
                  trackColor: ColorConfig.lineColor,
                  value: checked,
                  onChanged: (value) {
                    checkF?.call(value);
                  })
            ],

            14.gap
          ],
        ),
      )
    );
  }
}