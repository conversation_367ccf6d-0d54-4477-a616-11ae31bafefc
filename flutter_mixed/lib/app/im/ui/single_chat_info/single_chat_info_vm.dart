import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../db/db_helper.dart';
import '../base/chat_info_vm.dart';

class SingleChatInfoVm extends BaseChatInfoVm {

  String name = '';

  String avatar = '';

  String imChatId = '';

  bool title = false; // 是否被封号了

  SingleChatInfoUIData entity = SingleChatInfoUIData('','',);

  @override
  Future childOnInit() async {
    var arguments = Get.arguments;

    name = arguments['name'];
    avatar = arguments['avatar'];
    imChatId = arguments['imChatId'];
    title = arguments['complainState'] ?? false;

    entity = SingleChatInfoUIData(
        avatar ,name
    );
    update();
  }

  // 清空聊天记录
  clearChatRecord() async {
    var currentSession = await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if (currentSession == null) return false;
    currentSession.msgContent = "";
    await DbHelper.insertSession(currentSession);
    Message cleanMessage = Message(getUUid());
    cleanMessage..msgType = ConstantImMsgType.SSChatMessageTypeClearChatRecord
      ..sendTime = DateTime.now().millisecondsSinceEpoch
      ..sessionId = sessionId
      ..uid = ownerId
    ;
    await DbHelper.insertMsg(cleanMessage);
    await DbHelper.updateDeleteStatus(sessionId);
    return true;
  }

   createGroupParam()  {
     var params = {
       'type': 0,
       'groupId' : imChatId,
       'userList': [{'name': name}],
       'selectList': [imChatId],
       'index':0
     };
     return params;
   }

  @override
  bool isGroup() => false;
}


class SingleChatInfoUIData {
    String? avatar;
    String? name;

    SingleChatInfoUIData(
      this.avatar, this.name);
}