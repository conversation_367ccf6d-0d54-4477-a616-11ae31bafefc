import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/modules/group_member/group_member_operate_ctl.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/request/entity/group_info_resp.dart';
import 'package:flutter_mixed/app/im/route/route_extension.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/group_chat_info/group_chat_info_vm.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';

import '../../../common/config/config.dart';
import '../../../common/widgets/title_bar.dart';
import '../../../modules/gallery/gallery.dart';
import '../../../routes/route_util.dart';
import '../../../utils/string-util.dart';
import '../../widget/some_widget.dart';
import '../group_name_modify/group_name_modify_vm.dart';

/// 群聊，设置（信息）页面
class GroupChatInfoPage extends StatelessWidget {

  const GroupChatInfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GroupChatInfoVm>(builder: (controller) {
      return ToolBar(
        title: '群组信息',
        body: SingleChildScrollView(
          child: Center(
            child: Column(
              children: [
                16.gap,
                _buildGroupMemberWidget(controller),
                16.gap,
                _buildGroupManageWidget(controller),
                16.gap,
                _buildRecordWidget(controller, context),
                16.gap,
                _buildQuitGroupWidget(context ,controller),
                10.gap,
                _buildTransferGroupOwner(controller),
                10.gap
              ],
            ),
          ),
        ),
      );
    });
  }

  // 群成员
  _buildGroupMemberWidget(GroupChatInfoVm controller) {
     return Container(
       margin: const EdgeInsets.symmetric(horizontal: 16),
       decoration: BoxDecoration(
           borderRadius: BorderRadius.circular(8),
           color: Colors.white),
       child: Column(
         children: [
           _buildRowItem(
             controller.uiData.getGroupImage(),
             des: controller.uiData.getGroupName(),
             hasImage: true,
             showArrow: controller.isCreator() || !controller.isCompanyGroup(),
             desFontBold: false,
             desColor: ColorConfig.mainTextColor,
             imageClick: () {
               _tapAvatar(controller.uiData.getGroupImage());
             },
             itemClick: (){
                if(controller.isCompanyGroup()){
                  return;
                }
                if (!controller.isCreator()) {
                  toast("只有创建者才可以修改群名称和群头像");
                  return;
                }
                RouteHelper.route(Routes.GROUP_CHANGE_INFO, arguments: {
                    'groupId': controller.groupId,
                    'groupLogo': controller.uiData.getGroupImage(),
                    'groupName': controller.uiData.getGroupName(),
                    'colour': controller.uiData.getGroupLogoColor(),
                    'logoText': controller.uiData.getGroupLogoText(),
                });
             }
           ),
           5.gap,
           Container(
             margin: const EdgeInsets.symmetric(horizontal: 16),
             height: 0.5,
             color: ColorConfig.backgroundColor,
           ),
           Container(
             padding: const EdgeInsets.all(16),
             height: controller.getGridHeight(),
             child: GridView.builder(
                 gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                   crossAxisCount: 5,
                   crossAxisSpacing: 1,
                   mainAxisSpacing: 10,
                   childAspectRatio: 0.9,
                 ),
                 itemCount: controller.members.length,
                 itemBuilder: (ctx ,index){
                   var u = controller.members[index];
                   return _buildMemberItem(controller ,u);
                 }),
           ),
           5.gap,
           Container(
             margin: const EdgeInsets.symmetric(horizontal: 16),
             height: 0.5,
             color: ColorConfig.backgroundColor,
           ),
           _buildRowItem('群成员', des: '查看更多群成员',showArrow: true, itemClick: (){
               RouteHelper.route(Routes.GROUP_MEMBER_PAGE , arguments: {
                 'groupId': controller.groupId
               });
           }),
         ],
       ),
     );
  }
  _tapAvatar(String? url){
    if (StringUtil.isEmpty(url)) return;
    var galleryList = <GalleryItem>[];
    galleryList.add(GalleryItem('',url));
    Navigator.push(Get.context!,
        PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
          return FadeTransition(
              opacity: animation, child: GalleryPage(galleryList, 0));
        }));
  }

  _buildMemberItem(GroupChatInfoVm controller ,GroupUser? member) {
    if(member == null) return SizedBox();
    if(member.isMember()){
      return InkWell(
        onTap: (){
          // 进入个人信息页面
          RouteHelper.routePath(Routes.USER_INFO , arguments: {'userId': member.id, 'type': 2});//todo...type缺少企业群组的判断 
        },
        child: Container(
          // width: 44,
          height: 100,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ImageLoader(url: member.headimg, width: 44 ,height: 44, radius: 10.0, ),
              const Spacer(),
              Text(member.name ?? '' ,maxLines: 1, style: const TextStyle(overflow: TextOverflow.ellipsis , fontSize: 12 ,color: ColorConfig.mainTextColor)),
            ],
          ),
        ),
      );
    }else {
      if (controller.isCompanyGroup()) {
        return Container();
      } else {
         if(member.isAddButton()){
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                controller.isCanAddMember() ? InkWell(
                  onTap: () async {
                    // 群组添加成员
                    var argument = controller.createAddGroupMemberArgument();
                    await RouteHelper.route(Routes.GROUP_ADD_MEMBERS, arguments: argument);
                    controller.fetchGroupInfo();
                  },
                  child: Image.asset(AssetsRes.ICON_GROUP_MEMBER_ADD , width: 44 , height: 44,),
                ) : Container(),
                const Text('')
              ],
            );

        }else {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                onTap: (){
                  Routes.GROUP_MANAGE_OPERATE.toPage(arguments: {
                    'groupId': controller.sessionId,
                    'pageType': EnumGroupMemberOperateType.removeMember
                  });
                  // todo 进入群管理页面 , 群组删除成员 ,

                },
                child: Image.asset(AssetsRes.ICON_GROUP_MEMBER_REMOVE , width: 44 , height: 44,),
              ),
              const Text('')
            ],
          );
        }
      }
    }
  }

  _buildGroupQrCode(GroupChatInfoVm controller) {
     if(controller.isCanAddMember() && !controller.isCompanyGroup()){
       return Column(
         children: [
           _buildRowItem('群二维码' ,showArrow: true, itemClick: (){
             controller.route2Qrcode();
           }),
           marginLine,
         ],
       );
     }
     return Container();
  }

  // 群管理
  _buildGroupManageWidget(GroupChatInfoVm controller) {
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            marginLine,
            _buildGroupQrCode(controller),

            if(controller.isCreator() && !controller.isCompanyGroup())...[
              _buildRowItem('群管理',showArrow: true, itemClick: (){
                Routes.GROUP_MANAGE.toPage(arguments: {
                  'sessionId': controller.sessionId,
                });
              }),
              marginLine,
            ],

            _buildRowItem('消息免打扰', checked: controller.settingUIData.isDisturbOpen
                , checkF: (v) => controller.updateDisturb(v)),
            marginLine,
            _buildRowItem('置顶聊天', checked: controller.settingUIData.msgIsTop
                , checkF: (v) => controller.updateMsgTop(v)),
          ],
        )
    );
  }

  // 清空聊天记录，投诉
  _buildRecordWidget(GroupChatInfoVm controller, BuildContext context) {
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildRowItem('清空聊天记录' ,showArrow: true, itemClick: (){
              showDialog(context: context, builder: (ctx){
                return CupertinoAlertDialog(
                  content: Container(
                    padding: EdgeInsets.symmetric(vertical: 10),
                    child: Text("确定删除${controller.uiData.getGroupName()}的群组信息吗？", style: TextStyle(fontSize: 14),),),
                  actions: <Widget>[
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text("取消" , style: TextStyle(fontSize: 15, color: ColorConfig.mainTextColor)),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        controller.handleMessageClean().then((value) {
                          if(value){
                             Get.back(result: DissolveGetBackModel());
                          }
                        });
                      },
                      child: const Text("确认", style: TextStyle(fontSize: 15 , color: ColorConfig.themeCorlor),),
                    ),
                  ],
                );
              });
            }),
            marginLine,
            _buildRowItem('投诉',showArrow: true, itemClick: (){
              Routes.TIP_OFF_MAIN.toPage(arguments: {
                'sessionId': controller.sessionId,
              });
            }),
          ],
        )
    );
  }

  // 退出解散群组
  _buildQuitGroupWidget(BuildContext context ,GroupChatInfoVm controller) {
    if(controller.isCompanyGroup()){
      return Container();
    }
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildRowItem('退出群组' , textColor: ColorConfig.deleteCorlor,itemClick: (){
             
              showDialog(context: context, builder: (ctx){

                var canQuitGroup = controller.canQuitGroup();
                if(canQuitGroup) {
                  return SimpleDialog(
                    title: Container(
                      alignment: Alignment.center,
                      child: Column(
                        children: [
                          const Text('您是群组创建者', style: TextStyle(fontSize: 13 , color: ColorConfig.mainTextColor),),
                          1.gap,
                          const Text('请谨慎执行退出群组操作', style: TextStyle(fontSize: 14 , color: ColorConfig.mainTextColor),)
                        ],
                      ),
                    ),
                    children: [
                      15.gap,
                      line,
                      10.gap,
                      Container(
                        alignment: Alignment.center,
                        child: InkWell(
                          onTap: (){
                            Navigator.of(context).pop();
                            // todo 跳转群管理页面
                            Routes.GROUP_MANAGE_OPERATE.toPage(arguments: {
                              'groupId': controller.sessionId,
                              'pageType': EnumGroupMemberOperateType.transferAndQuit
                            });

                          },
                          child: Container(
                            color: Colors.transparent,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: const Text('转让创建者身份并退出' , style: TextStyle(fontSize: 13 , color: ColorConfig.themeCorlor),),
                          ),
                        ),
                      ),
                      10.gap,
                      line,
                      10.gap,
                      Container(
                        alignment: Alignment.center,
                        child: InkWell(
                          onTap: () => Navigator.of(context).pop(),
                          child: Container(
                            color: Colors.transparent,
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: const Text('取消' , style: TextStyle(fontSize: 15 , color: ColorConfig.mainTextColor),),
                          ),
                        ),
                      ),

                    ],
                  );
                }else {
                  return CupertinoAlertDialog(
                    content: Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: Text("退出后不会通知群组中其他成员\n且不会再接收此群组消息", style: TextStyle(fontSize: 14),),),
                    actions: <Widget>[
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text("取消" , style: TextStyle(fontSize: 15, color: ColorConfig.themeCorlor),),
                      ),
                      TextButton(
                        onPressed: () async {
                          Navigator.of(context).pop();
                          var result = await controller.quitGroup();
                          if(result){
                            Get.until((route){
                              if(route.settings.name == Routes.MY_GROUP){
                                return true;
                              }
                              return route.settings.name == Routes.HOME;
                            });
                          }
                        },
                        child: const Text("退出", style: TextStyle(fontSize: 15 , color: ColorConfig.deleteCorlor),),
                      ),
                    ],
                  );
                }
              });

            }),
            if (controller.isCreator())...[
              marginLine,
              _buildRowItem('解散群组' , textColor: ColorConfig.deleteCorlor ,itemClick: (){
                showDialog(context: context, builder: (ctx){
                  return CupertinoAlertDialog(
                    title: const Text("您确认要解散当前群组吗", style: TextStyle(fontSize: 14),),
                    content: const Text("解散群组，聊天的消息将会被清除,无法找回!" , style: TextStyle(fontSize: 12 ,color: ColorConfig.msgTextColor),),
                    actions: <Widget>[
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text("取消" , style: TextStyle(fontSize: 15, color: ColorConfig.themeCorlor),),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();

                          controller.dissolve().then((value) {
                            if(value){
                               Get.back(result: DissolveGetBackModel());
                            }
                          });
                        },
                        child: const Text("解散", style: TextStyle(fontSize: 15 , color: ColorConfig.deleteCorlor),),
                      ),
                    ],
                  );
                });
              }),
            ]
          ],
        )
    );
  }

  _buildTransferGroupOwner(GroupChatInfoVm controller) {
    if(controller.isCreator()) {
      return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.white),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              marginLine,
              _buildRowItem('转让群组创建者' ,showArrow: true, itemClick: (){
                Routes.GROUP_MANAGE_OPERATE.toPage(arguments: {
                  'groupId': controller.sessionId,
                  'pageType': EnumGroupMemberOperateType.transferLeader
                });
              }),
            ],
          )
      );
    }
    return Container();
  }

  _buildRowItem(String label , {
    Color? textColor, 
    Color? desColor,
    String? des,
    bool? showArrow = false,
    bool? checked,
    bool? hasImage,
    bool desFontBold = false,
    Function? checkF,
    VoidCallback? itemClick,
    VoidCallback? imageClick

  }) {
    return InkWell(
        onTap: (){
          itemClick?.call();
        },
        child: SizedBox(
          height: hasImage != null ? 56 : 50,
          child: Row(
            children: [
              14.gap,
              hasImage != null ? 
               Container(margin: const EdgeInsets.only(right: 12), child: InkWell(
                onTap: () {
                  imageClick?.call();
                },
                child: ImageLoader(url: label, width: 44 ,height: 44, radius: 10.0),
               )):
               Expanded(child: Text(label , style: TextStyle(fontSize: 15 , color: textColor ?? ColorConfig.mainTextColor),),),

              if(des != null)...[
                Expanded(child: Container(
                  alignment: Alignment.centerRight,
                  child: Text(
                    des,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: desFontBold ? FontWeight.bold : FontWeight.normal,
                      color: desColor ?? ColorConfig.msgTextColor
                    ),
                  ),
                )),
                8.gap
              ],

              if(showArrow == true)...[
                arrow,
              ],

              if(checked != null)...[
                CupertinoSwitch(
                    activeColor: ColorConfig.themeCorlor,
                    trackColor: ColorConfig.lineColor,
                    value: checked,
                    onChanged: (value) {
                      checkF?.call(value);
                    })
              ],

              14.gap
            ],
          ),
        )
    );
  }


}