import 'dart:async';

import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/ui/base/chat_info_vm.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../../modules/home/<USER>/home_controller.dart';
import '../../../routes/app_pages.dart';
import '../../request/datasource/relation_datasource.dart';
import '../../request/entity/group_info_resp.dart';
import '../../route/route_helper.dart';



class GroupChatInfoVm extends BaseChatInfoVm {

  String? groupId;

  String? groupName;

  GroupChatInfoUIData uiData = GroupChatInfoUIData();

  StreamSubscription? _groupStreamSubscription;

  List<GroupUser> members = [];

  List<GroupUser> allmembers = [];

  @override
  void onInit() {
    super.onInit();
    var arguments = Get.arguments;
    if(arguments == null) {
      Get.back();
      return;
    }

    groupId = arguments['sessionId'];
    groupName = arguments['groupName'];
  }

  @override
  Future childOnInit() async {
    fetchGroupInfo();
    _listenerGroupDb();
  }

  Future _listenerGroupDb() async {
    _groupStreamSubscription =
        DbHelper.listenGroupList(groupId ?? '').listen((group) async {
          await fetchGroupInfo();
    });
  }

  getGridHeight() {
    if(members.length > 5) return 200.0;
    return 110.0;
  }

  fetchGroupInfo() async {
    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var resp = await datasource.getGroupInfo(groupId ?? '');
    if(resp.success()){
      uiData.group = resp.data.first;

      if(uiData.group == null) return;

      uiData.group?.users?.forEach((element) {
         element.type = 0;
      });

      if(uiData.group!.isDissolve()){
        toast('群组已解散');
      }

      var _users = <GroupUser>[];

      uiData.group?.users?.forEach((element) {
        _users.add(element);
      });

      allmembers..clear()
                ..addAll(_users);

      members.clear();

      // fix 显示的成员数根据是否有添加/修改成员按钮决定 ，默认都有添加按钮，删除按钮根据权限判断
      var totalSize = 10;
      var diff = 1;
      if(canDelGroupMember()){
        diff += 1;
      }

      if(_users.length > (totalSize - diff)){
        members.addAll(_users.sublist(0,(totalSize - diff)));
      }else {
        members.addAll(_users);
      }

      members.add(GroupUser()..type = 1);
      if(canDelGroupMember()){
        members.add(GroupUser()..type = 2);
      }

      update();

    }else {
      logger('fetchGroupInfo error: ${resp.msg}');
      toast(resp.msg);
    }
  }

  createAddGroupMemberArgument() {
    var selectList = allmembers.where((element) => element.type == 0).map((e) => e.id ?? '').toList();
    var argument = {
      "type": 1,
      "groupId": groupId,
      "selectList": selectList,
    };
    return argument;
  }

  @override
  bool isGroup() => true;

  // 是否是创建者, 群内超过一个人
  bool canQuitGroup() {
    return uiData.isCreator(ownerId) && members.where((e) => e.type == 0).toList().length > 1;
  }

  bool isCompanyGroup() => uiData.isCompanyGroup();

  bool isCreator() {
    return uiData.isCreator(ownerId);
  }

  bool isAdmin() {
    return uiData.isAdmin(ownerId);
  }

  bool isCanAddMember() {
    return uiData.isCanAddMember() || isAdmin();
  }

  bool canDelGroupMember() {
    if((uiData.group?.users ?? []).isEmpty) return false;
    var mySelf = uiData.getMySelf(ownerId);
    if(mySelf == null) return false;
    return mySelf.identity == 1 || mySelf.identity == 2;
  }

  Future route2Qrcode() async {
    var param = {
      'type':1,
      'index':1,
      'groupId': groupId
    };
    await RouteHelper.route(Routes.GROUP_ADD_MEMBERS, arguments: param);
  }

  // 清空聊天记录
  Future<bool> handleMessageClean() async {
    var currentSession = await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
    if (currentSession == null) return false;
    currentSession.msgContent = "";
    await DbHelper.insertSession(currentSession);
    Message cleanMessage = Message(getUUid());
    cleanMessage..msgType = ConstantImMsgType.SSChatMessageTypeClearChatRecord
      ..sendTime = DateTime.now().millisecondsSinceEpoch
      ..sessionId = sessionId
      ..uid = ownerId
    ;
    await DbHelper.insertMsg(cleanMessage);
    await DbHelper.updateDeleteStatus(sessionId);
    return true;
  }

  // 退出群组
  Future<bool> quitGroup() async {
    Get.loading();
    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var resp = await datasource.quitGroup(groupId ?? '');
    if(resp.success()){
      await Get.dismiss();
      // 退群后数据操作: 更新session ； 关掉当前页面；  关掉群聊页面
      var session = await DbHelper.getSessionByOwnerId2SessionId(ownerId, sessionId);
      if(session != null){
        session.sessionHidden = 1;
        await DbHelper.insertSession(session);
      }
      return true;
    }else {
      await Get.dismiss();
      toast(resp.msg);
      return false;
    }
  }



  // 解散群组
  Future<bool> dissolve() async {
    Get.loading();
    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var resp = await datasource.dissolveGroup(groupId ?? '');
    if(resp.success()){
      // db删除群组信息
      try{
        HomeController homeController = Get.find();
        await homeController.getAllGroup();
      }catch(e){}
      await Get.dismiss();
      return true;
    }else {
      await Get.dismiss();
      toast(resp.msg);
      return false;
    }
  }

  @override
  void onClose() {
    super.onClose();
    _groupStreamSubscription?.cancel();
  }
}

// 解散群组
class DissolveGetBackModel {}

// 退出群组
class QuitGetBackModel {}


class GroupChatInfoUIData {
  GroupItemResp? group;
  GroupChatInfoUIData();

  String getGroupName() => group?.name ?? '';

  String getGroupImage() => group?.logo ?? '';

  String getGroupLogoColor() => group?.colour ?? '';
  String getGroupLogoText() => group?.logoText ?? '';

  bool isCreator(String? myUid) {
    var list = group?.users?.where((u) => u.id == myUid).toList() ?? [];
    var myU = list.firstOrNull;
    if(myU == null) return false;
    return myU.identity == 1;
  }

  bool isCompanyGroup() => group?.orgId != '';

  bool isAdmin(String? myUid) {
    var list = group?.users?.where((u) => u.id == myUid).toList() ?? [];
    return list.any((item) => item.id == myUid && item.identity != 0);
  }

  bool isCanAddMember() => group?.addMember != 1;

  GroupUser? getMySelf(String? myUid) => group?.users
      ?.where((u) => u.id == myUid).toList().firstOrNull;

}

