import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/ui/group_chat_info/group_chat_manage_controller.dart';
import '../../../common/config/config.dart';
import 'package:get/get.dart';

class GroupChatManageInfo extends StatelessWidget {
  const GroupChatManageInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<GroupChatManageController>(builder: (controller) {
      return ToolBar(
        title: '群管理',
        body: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                margin: const EdgeInsets.only(bottom: 16),
                child: _buildRowItem(
                  '仅群主和管理员可以添加群成员', 
                  des: "关闭后所有人均可添加群成员", 
                  checked: controller.settingUIData.authAddMember,
                  checkF: controller.updateAddMember,
                  height: 56
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  children: [
                    _buildRowItem('仅群主和管理员可以@所有人', 
                      checked: controller.settingUIData.authAtMember,
                      checkF: controller.updateAtAuth),
                    _buildRowItem('仅群主和管理员可以开启语音通话', 
                      checked: controller.settingUIData.authVoice,
                      checkF: controller.updateVoiceAuth, 
                      useborder: true),
                  ],
                ),
              ),
            ],
          ),
        )
      );
    });
  }

  _buildRowItem(String label, {String? des, bool? checked, Function(bool)? checkF, bool useborder = false,double height = 44}) {
    return InkWell(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(color: useborder ? ColorConfig.backgroundColor : Colors.transparent, width: useborder ? 0.5 : 0)
            )
          ),
          height: height,
          child:  Row(
              children: [
                Expanded(child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label, 
                      style: const TextStyle(fontSize: 15, color: ColorConfig.mainTextColor)
                    ),
                    if(des != null)...[
                      Container(
                        margin: const EdgeInsets.only(top: 2),
                        child: Text(des, style: const TextStyle(fontSize: 12, color: ColorConfig.desTextColor)),
                      ),
                    ],
                  ],
                )),
                if(checked != null)...[
                  CupertinoSwitch(
                    activeTrackColor: ColorConfig.themeCorlor,
                    inactiveTrackColor: ColorConfig.lineColor,
                    value: checked,
                    onChanged: checkF,
                  )
                ],
              ],
            )
        ),  
      )
    );
  }
}