
import 'package:get/get.dart';

import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/widgets/base_get_controller.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/request/datasource/relation_datasource.dart';
import 'package:flutter_mixed/app/im/request/entity/group_brief_item_resp.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import '../../../../../main.dart';

class GroupChatManageController extends BaseGetXController {

  String groupId = '';

  ChatSettingUIData settingUIData = ChatSettingUIData(false, false, false);

  @override
  void onInit() {
    super.onInit();
    groupId = Get.arguments['sessionId'] ?? '';
    _fetchGroupManageInfo();
  }

  _fetchGroupManageInfo() async {
    var datasource = RelationDatasource(retrofitDio, baseUrl: Host.HOST);
    var resp = await datasource.getGroupInfo(groupId);

    if (resp.success()) {
      var groups = resp.data;
      if (groups.isEmpty) return;
      var group = groups.first;
      settingUIData.authAddMember = group.addMember == 1;
      settingUIData.authAtMember = group.hintMember == 1;
      settingUIData.authVoice = group.voice == 1;
    }
    update();
  }

  void updateAddMember(bool v) => _updateSetting(EnumManageMentType.addMember, v, 'authAddMember');
  void updateAtAuth(bool v) => _updateSetting(EnumManageMentType.hintMember, v, 'authAtMember');
  void updateVoiceAuth(bool v) => _updateSetting(EnumManageMentType.voice, v, 'authVoice');

  Future<void> _updateSetting(EnumManageMentType type, bool value, String settingField) async {
    var datasource = RelationDatasource(retrofitDio, baseUrl: Host.HOST);
    var req = GroupManageItem(groupId: groupId, operation: value ? 1 : 0, type: type.index);
    Get.loading();
    var resp = await datasource.updateGroupManage(req);
    await Get.dismiss();
    if (resp.success()) {
      switch (settingField) {
        case 'authAddMember':
          settingUIData.authAddMember = value;
          break;
        case 'authAtMember':
          settingUIData.authAtMember = value;
          break;
        case 'authVoice':
          settingUIData.authVoice = value;
          break;
      }
      update();
    } else {
      toast(resp.msg);
    }
  }
}

class ChatSettingUIData {
  bool authAddMember = false;
  bool authAtMember = false;
  bool authVoice = false;
  ChatSettingUIData(this.authAddMember, this.authAtMember, this.authVoice);
}

enum EnumManageMentType {
  normal,
  addMember,
  hintMember,
  voice,
}