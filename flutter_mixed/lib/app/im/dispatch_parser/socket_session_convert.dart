


import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/ui/base/common_check.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../db/entity/session.dart';
import '../ui/chat/draft/draft.dart';
import '../utils/im_global_util.dart';

Future<Session?> covertGroupSession(String msgId, String? cmdId ,String groupId , String groupName, String groupLogo,
    int msgType,
    int msgTime,
    String? senderName,
    String? senderId,
    String? msgContent,
    {bool haveAt = false}) async {

  var userId = await ImGlobalUtil.currentUserId();
  bool isSelfSend = senderId == userId;

  var existSession = await DbHelper.getSessionByOwnerId2SessionId(userId, groupId);
  int? sessionHidden = 0;
  int? sessionTop = 0;
  int? unReadCount = 1;
  int? isDisturb = 0;

  var sendNameTxt = '';
  if(!StringUtil.isEmpty(senderName) && senderId != userId){
    sendNameTxt = '${senderName}:';
  }

  logger("come on baby !!!");

  if(existSession != null){
    sessionHidden = 0;//来新消息显示之前隐藏的会话
    sessionTop = existSession.sessionTop;
    isDisturb = existSession.noDisturb;
    unReadCount = isSelfSend ? (existSession.notReadCount ?? 0) : (existSession.notReadCount ?? 0) + 1;
  }else{
    _createFaultMessage(userId, groupId, msgTime, msgContent);
  }
  if (CommonCheck.checkIsOnTheChatPage(groupId)) {
    //在当前页面
    unReadCount = 0;
  }

  var session = Session(sessionId: groupId ?? '')
    ..uid = userId
    ..sessionTop = sessionTop
    ..sessionHidden = sessionHidden
    ..msgType = msgType
    ..sessionType = 2
    ..extend = ''
    ..name = groupName
    ..chatStatus = 0
    ..cmdId = cmdId
    ..headerUrl = groupLogo
    ..msgId = msgId
    ..msgContent = haveAt? "<font color='#EC0A39'>[有人@你]</font>${sendNameTxt}${msgContent}" : "${sendNameTxt}${msgContent}"
    ..notReadCount = unReadCount
    ..msgTime = msgTime ?? 0
    ..noDisturb = isDisturb
    ..appChatId = groupId;

  await keepDraftIfHas(session);

  DbHelper.insertSession(session);
  return session;
}
//无session 强行创建断层，场景：长时间不聊天，换设备登录，在线收到消息 无断层会拉不到之前的消息
_createFaultMessage(String userId,String sessionId,int msgTime,String? msgContent){
    DbHelper.updateLineFaultAgeMessage(userId, sessionId,
            msgTime-1, 1, msgContent ?? '');
  }