import 'dart:convert';

import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/session_read.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/multi_sync_dispatch_parser.dart';
import 'package:flutter_mixed/app/im/ext/c2cmsg_ext.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_helper.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_model.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_payLoad_model.dart';
import 'package:flutter_mixed/app/im/utils/im_global_util.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';

import '../../../logger/logger.dart';
import '../constant/ImMsgConstant.dart';
import '../ext/c2c_group_ext.dart';
import '../ext/im_msg_ext.dart';
import '../proto_model/Msg.pb.dart';

/// Socket Resp 中 解析单聊数据 ，入库
class SingleChatParser {
  final ImCommonData imCommonData;
  ImMsg imMsg;
  final C2CMsg receiveData;
  Message? message;
  SingleChatParser(this.imCommonData, this.imMsg, this.receiveData);

  Future parse() async {
    if (imCommonData.senderInfo.imUserId == receiveData.receiver) {
      //自己给自己发
      if (!receiveData.isWithDraw() && imCommonData.type !=
          ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange) {
        var uid = await UserHelper.getUid();
        List<Message> messageList = await DbHelper.getMessageByUidAndMsgId(uid, imCommonData.msgId);
        if (messageList.isNotEmpty) {
          return;
        }
        Message? cmdMessage = await DbHelper.queryMsgByCmdId(imCommonData.cmdId);
        if (cmdMessage != null) {
          return;
        }
      }
    }

    if (receiveData.isText()) {
      var txt = receiveData.msg;
      logger('收到文本：$txt');
      imCommonData.msgContent = txt;
      message = await imCommonData.createTxtMessage(txt, receiver: receiveData.receiver);
    }

    if (receiveData.isImg()) {
      var img = receiveData.image;
      logger('收到图片：${img.toString()}');
      imCommonData
        ..imgMsg = img
        ..extMsg = receiveData.ext;
      message = await imCommonData.createImgMessage(receiver: receiveData.receiver);
    }

    if (receiveData.isFile()) {
      var file = receiveData.file;
      imCommonData.fileMsg = file;
      logger('收到文件：${file.toString()}');
      message = await imCommonData.createFileMessage(receiver: receiveData.receiver);
    }

    if (receiveData.isVideo()) {
      var video = receiveData.video;
      imCommonData.videoMsg = video;
      logger('收到视频：${video.toString()}');
      message = await imCommonData.createVideoMessage(receiver: receiveData.receiver);
    }

    if (receiveData.isVoice()) {
      var voice = receiveData.voice;
      imCommonData.voiceMsg = voice;
      logger('收到语音：${voice.toString()}');
      message = await imCommonData.createVoiceMessage(receiver: receiveData.receiver);
    }

    if (receiveData.isLocation()) {
      var location = receiveData.location;
      imCommonData.locationMsg = location;
      logger('收到location：${location.toString()}');
      message = await imCommonData.createLocationMessage(receiver: receiveData.receiver);
    }

    if (receiveData.isWithDraw()) {
      var withdraw = receiveData.withdraw;
      message = await imCommonData.updateMessageToWithDraw(
          receiver: receiveData.receiver, msgId: withdraw.msgId);
      if (imCommonData.senderInfo.imUserId != receiveData.receiver) {
        message!.text = '${imCommonData.senderInfo.nickname}撤回了一条消息';
      } else {
        message!.text = '你撤回了一条消息';
      }
    }

    if (receiveData.isRead()) {
      var targetSession = receiveData.from;
      var ownImId = await UserHelper.getOwnImId();
      if (ownImId == targetSession) {
        //忽略来自于自己的已读 PC发送已读时
        return;
      }
      _updateLocalLastReadTime(receiveData.from, imCommonData.msgTime);

    }

    if (receiveData.isCall()) {
      message = await imMsg.dealSignleCall(imCommonData,!imMsg.recordIgnore);
    }

    /// 扩展类型，包括 ==>
    /// 黑名单变化，
    /// 引用
    /// 机器人
    /// 转发记录
    /// 团队邀请
    if (receiveData.isExt()) {
      var ext = receiveData.ext;
      imCommonData.extMsg = ext;
      if (imCommonData.type ==
          ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange) {
        MultiSyncParser(receiveData.ext.ext1, true, imCommonData.type)
            .parseData();
      } else {
        if (!imMsg.recordIgnore) {
          message = await imCommonData.createExtMessage(ext, receiver: receiveData.receiver);
        }
      }
    }

    ///  if (receiveData.msg == "我们已经是好友了,快来和我一起聊天吧!") {
    //                                     //发送刷新好友列表的通知
    //                                     EventBusUtils.sendEvent(
    //                                         EventBusEvent(
    //                                             ConstantEventCodeUtil.TCP_REFRESH_FRIEND_LIST,
    //                                             ""
    //                                         )
    //                                     )
    //                                 }

    bool isNeedSend = await imMsg.isNeedSendLocalNotification();

    if (isNeedSend) {
      var settingModel = await LocalNotiHelper.isCanSendAndIsNotOnthepage(
          Routes.IM_CHAGE_PAGE, imCommonData.senderInfo.imUserId);
      if (settingModel != null && !imMsg.recordIgnore) {
        //生成本地通知model
        if (message != null) {
          settingModel.body = message?.text;
        }
        LocalNotiModel notiModel = imMsg.createNotificationModel(settingModel);
        logger('单聊，开启本地通知 $notiModel');
        LocalNotification().showNotification(notiModel);
      }
    }
    // 单聊： 统一接收到消息创建会话
    if (!imMsg.recordIgnore) {
      if (message != null) {
        receiveData.createSession(imCommonData,message!.text);
      }
    }
  }

    //更新本地已读时间戳
  _updateLocalLastReadTime(String sessionId,int sendTime) async{
    var uid = await UserHelper.getUid();
    SessionRead sessionRead = SessionRead(uid: uid)
    ..sessionId = sessionId
    ..appChatId = ''
    ..lastReadTime = sendTime;
    DbHelper.insertSessionRead(sessionRead);
  }
}
