import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';

import '../../utils/image_util.dart';
import '../db/db_helper.dart';
import '../ext/im_msg_ext.dart';
import '../proto_model/Msg.pb.dart';

/// Socket Resp 中 解析 群聊反馈
class GroupChatFeedBackParser {

  final GroupMsgResponse receiveData;
  final ImCommonData imCommonData;

  GroupChatFeedBackParser(this.imCommonData ,this.receiveData);

  Future parse(bool result) async {
    var success = receiveData.success;
    var failType = receiveData.failType.value;

    var msgId = imCommonData.msgId;

    var cmdId = imCommonData.cmdId;
    var msg = await DbHelper.queryMsgByCmdId(cmdId);
    if(msg == null || msg.isSoftDel()) return;
    var sendTime = imCommonData.msgTime;

    if(result) {
      if (imCommonData.type == ConstantImMsgType.SSChatMessageTypeUndo) {
        DbHelper.updateMsgSendStatusMsgId(msg.msgId, msg.msgId , 1 , sendTime);
      }else{
        DbHelper.updateMsgSendStatusMsgId(msgId, msg.msgId , 1 , sendTime);
      }

    }else{
      if (imCommonData.type == ConstantImMsgType.SSChatMessageTypeUndo) {
        DbHelper.updateMsgSendStatusMsgId(msg.msgId, msg.msgId , 0 , sendTime);
      }else{
        DbHelper.updateMsgSendStatusMsgId(msgId, msg.msgId , 0 , sendTime);
      }
    }

    // 当发现cmdid 仍有多条的时候 删除旧数据
    await ImageUtil.removeDupImageTempImg(cmdId, msg.msgId);

    // 更新对应 session 的 msgTime
    var session = await DbHelper.getSessionByOwnerId2SessionId(msg.uid ?? '', msg.sessionId ?? '');
    if(session != null) {
      session.msgTime = sendTime;
      session.msgId = msgId;
      await DbHelper.insertSession(session);
    }
  }
}