import 'dart:convert';

import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/socket_session_convert.dart';
import 'package:flutter_mixed/app/im/ext/groupmsg_ext.dart';
import 'package:flutter_mixed/app/im/ext/notice_msg_ext.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_helper.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_model.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

import '../../../logger/logger.dart';
import '../constant/ImMsgConstant.dart';
import '../ext/c2c_group_ext.dart';
import '../ext/im_msg_ext.dart';
import '../proto_model/Msg.pb.dart';
import '../ui/chat/message/custom_msg_model.dart';

class GroupChatParser {
  GroupMsg receiveData;
  ImMsg imMsg;
  ImCommonData imCommonData;
  Message? message;
  GroupChatParser(this.imCommonData, this.imMsg, this.receiveData);

  Future parse() async {
    var groupId = receiveData.groupId;
    var groupName = receiveData.groupName;
    var groupLogo = receiveData.groupLogo;
    var msgId = imMsg.msgId;
    var cmdId = imMsg.cmdId;
    var msgType = imMsg.type;
    var msgTime = imMsg.msgTime.toInt();
    var senderName = imMsg.senderInfo.nickname;
    var senderId = imMsg.senderInfo.userId;

    var txt = receiveData.isText() ? receiveData.msg : imMsg.msgContent;

    logger(
        'group => : groupId =$groupId , groupName =$groupName , groupLogo =$groupLogo , msgType = $msgType');

    // 群聊： 统一接收到消息创建会话
    //receiveData.canCreateSession()
    if (receiveData.isNotice()) {
      String dataStr = imMsg.groupMsg.noticeMsg.data;
      Map<String, dynamic> dataMap = json.decode(dataStr);
      msgType = parseGroupNoticeMsgType(dataMap);
      try {
        ChatController? chatController =
            Get.find<ChatController>(tag: imMsg.groupMsg.groupId);
        if (chatController != null) {
          chatController.fetchGroupInfo();
        }
      } catch (e) {}
    }

    if (msgType == ConstantImMsgType.SSChatMessageTypeChatGroupMemberChange) {
      DbHelper.updateSessionGroupLogo(groupId, groupLogo);
    } else {
      if (!imMsg.recordIgnore && msgType != ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate) {
        var haveAtTag = false;
        if(receiveData.isExt() && LocalNotiHelper.isNotOnThePage(Routes.IM_CHAGE_PAGE, groupId)){
            if(msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice){
              //@部分
              CustomMsgModel? customMsg = CustomMsgModel.fromJson(jsonDecode(receiveData.ext.ext1));
              List ids = json.decode(customMsg.extendTwo??"[]", );
              logger("=====解析@人员=========$ids===========");
              var ownId = await UserHelper.getUid();
              if(ids.contains(ownId)){
                haveAtTag = true;
              }
            }else if(msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice){
              haveAtTag = true;
            }
        }
        if(haveAtTag && !SessionListController.atList.contains(groupId)){
          SessionListController.atList.add(groupId);
        }

        covertGroupSession(msgId, cmdId, groupId, groupName, groupLogo, msgType,
            msgTime, senderName, senderId, txt,haveAt: SessionListController.atList.contains(groupId));
      }
    }

    if (receiveData.isText()) {
      logger('收到文本：$txt');
      message = await imCommonData.createTxtMessage(txt, groupId: groupId);
    }

    if (receiveData.isImg()) {
      var img = receiveData.image;
      imCommonData
        ..imgMsg = img
        ..extMsg = receiveData.ext;
      logger('收到图片：${img.toString()}');
      message = await imCommonData.createImgMessage(groupId: groupId);
    }

    if (receiveData.isFile()) {
      var f = receiveData.file;
      imCommonData.fileMsg = f;
      message = await imCommonData.createFileMessage(groupId: groupId);
    }

    if (receiveData.isVideo()) {
      var video = receiveData.video;
      imCommonData.videoMsg = video;
      logger('收到视频：${video.toString()}');
      message = await imCommonData.createVideoMessage(groupId: groupId);
    }

    if (receiveData.isVoice()) {
      var voice = receiveData.voice;
      imCommonData.voiceMsg = voice;
      logger('收到语音：${voice.toString()}');
      message = await imCommonData.createVoiceMessage(groupId: groupId);
    }

    if (receiveData.isLocation()) {
      var location = receiveData.location;
      imCommonData.locationMsg = location;
      logger('收到location：${location.toString()}');
      message = await imCommonData.createLocationMessage(groupId: groupId);
    }

    if (receiveData.isWithDraw()) {
      var withDraw = receiveData.withdraw;
      imCommonData.updateMessageToWithDraw(
          groupId: groupId, msgId: withDraw.msgId);
    }

    if (receiveData.isCall()) {
      var call = receiveData.call;
    }

    if (receiveData.isNotice()) {
      var notice = receiveData.getNotice();
      logger("updateNotice ${notice.toJson()}");
      if (msgType != ConstantImMsgType.SSChatMessageTypeChatGroupMemberChange) {
        message = await imCommonData.createGroupNoticeMessage(
            groupId: groupId,
            notice: notice,
            noticeSessionType: imMsg.type,
            text: txt ??'',
            isNeedInsert: !imMsg.recordIgnore);
        if (message != null) {
          _dealGroupMsg(message!);
        }
      }
      imCommonData.updateGroupDbData(groupId, notice);
    }

    if (receiveData.isExt()) {
      var ext = receiveData.ext;
      imCommonData.extMsg = ext;
      if (!imMsg.recordIgnore) {
        message = await imCommonData.createExtMessage(ext, groupId: groupId);
      }
      logger('扩展消息： ${message?.text}');

    }
    logger('======daozheleme');
    bool isNeedSend = await imMsg.isNeedSendLocalNotification();
    if (isNeedSend) {
      var settingModel = await LocalNotiHelper.isCanSendAndIsNotOnthepage(
          Routes.IM_CHAGE_PAGE, groupId);
      if (settingModel != null && !imMsg.recordIgnore && msgType != ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate) {
        //生成本地通知model
        logger('可以弹通知么..${isNeedSend}');
        if (message != null) {
          var name = '';
          if (imMsg.senderInfo.nickname.isNotEmpty) {
            name = '${imMsg.senderInfo.nickname}:';
          }
          
          // 清理文本中的 font 标签
          var cleanText = _cleanFontTags(message!.text ?? '');
          logger('====name====$name===cleantext==$cleanText');
          settingModel.body = '$name$cleanText';
        }
        LocalNotiModel notiModel = imMsg.createNotificationModel(settingModel);
        LocalNotification().showNotification(notiModel);
      }
    }
  }

  _dealGroupMsg(Message groupMessage) {
    if (groupMessage.msgType ==
            ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd ||
        groupMessage.msgType ==
            ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo ||
        groupMessage.msgType ==
            ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate) {
      //通知groupChat隐藏上方的加入群组音视频控件
      bool isHaveChat = Get.isRegistered<ChatController>(tag: groupMessage.sessionId);
      if (isHaveChat) {
        ChatController? chatController =
            Get.find<ChatController>(tag: groupMessage.sessionId);
        if (chatController != null) {
          chatController.checkImCallDetail();
        }
      }
    }
    if (groupMessage.msgType ==
            ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo ||
        groupMessage.msgType ==
            ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate) {
      if (groupMessage.msgFrom == 2) {
        if (groupMessage.extendTwo != null && groupMessage.uid != null) {
          if (groupMessage.extendTwo!.contains(groupMessage.uid!)) {
            Map<String, dynamic> messageJson = groupMessage.toJson();
            messageJson['groupName'] = receiveData.groupName;
            messageJson['groupLogo'] = receiveData.groupLogo;
            messageJson['sessionType'] = 2;
            Channel().invoke(
              Channel_Receive_Call_Msg,
              messageJson, //群聊添加groupName,groupLogo字段
            );
          }
        }
      }
    }
  }

  String _cleanFontTags(String text) {
    RegExp fontTagRegex = RegExp(r'<font[^>]*>(.*?)</font>');
    String cleanedText = text.replaceAllMapped(fontTagRegex, (match) {
      return match.group(1) ?? '';
    });
    return cleanedText;
  }
}
