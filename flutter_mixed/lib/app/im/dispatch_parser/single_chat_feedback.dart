import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/utils/image_util.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../ext/im_msg_ext.dart';
import '../proto_model/Msg.pb.dart';

/// Socket Resp 中 解析 单聊反馈： 是否发送成功（不成功显示 红色警告等提示）
class SingleChatFeedBackParser {

  final C2CMsgResponse receiveData;

  final ImCommonData imCommonData;
  
  SingleChatFeedBackParser(this.imCommonData , this.receiveData);

  Future parse() async {

    var failType = receiveData.failType.value;
    var success = receiveData.success;

    var cmdId = imCommonData.cmdId;

    logger('收到单聊响应....failType = $failType');

    // 查询出这条消息（通过cmdid）
    var msg = await DbHelper.queryMsgByCmdId(cmdId);
    if(msg == null || msg.isSoftDel()) return;
    var sendTime = imCommonData.msgTime;
    var msgId = imCommonData.msgId;

    if(failType == 0 || failType == 2){
       // 发送成功
      // 0 对方在线，  2 对方不在线
      if (imCommonData.type == ConstantImMsgType.SSChatMessageTypeUndo) {
        await DbHelper.updateMsgSendStatusMsgId(msg.msgId, msg.msgId , 1 , sendTime);
      }else{
        await DbHelper.updateMsgSendStatusMsgId(msgId, msg.msgId , 1 , sendTime);
      }
      
    }else {
      // 响应失败，1, 3: //在对方的黑名单中或者消息存储失败
      if (imCommonData.type == ConstantImMsgType.SSChatMessageTypeUndo) {
        await DbHelper.updateMsgSendStatusMsgId(msg.msgId, msg.msgId , 0 , sendTime);
      }else{
        await DbHelper.updateMsgSendStatusMsgId(msgId, msg.msgId , 0 , sendTime);
      }
      
    }

    // 当发现cmdid 仍有多条的时候 删除旧数据
    await ImageUtil.removeDupImageTempImg(cmdId, msg.msgId);

    // 更新对应 session 的 msgTime
    var session = await DbHelper.getSessionByOwnerId2SessionId(msg.uid ?? '', msg.sessionId ?? '');
    if(session != null) {
      session.msgTime = sendTime;
      session.msgId = msgId;
      await DbHelper.insertSession(session);
    }

  }
}