import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/refresh_notice_model.dart';
import 'package:flutter_mixed/app/im/ext/notice_msg_ext.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/refresh_pages_data/refresh_pages_data.dart';
import 'package:flutter_mixed/app/im/utils/im_global_util.dart';
import 'package:flutter_mixed/main.dart';

import '../request/entity/offline_notice_msg.dart';

class ServerMsgParser {
  final ServerMsg receiveData;

  ServerMsgParser(this.receiveData);

  Future parse() async {
    var userId = await ImGlobalUtil.currentUserId();
    ClientNoticeMsg noticeMsg = ClientNoticeMsg.fromJson(
        receiveData.noticeMsgUpdated.noticeMsg.toJson());
    NoticeData? noticeData = noticeMsg.getDataP();
    dynamic cmdId = noticeData?.cmdId;
    if (cmdId != null) {
      int tempStatus = receiveData.noticeMsgUpdated.noticeMsg.tempStatus;
      DbHelper.upDateNoticeTempStatus(userId, cmdId, tempStatus);
      if (noticeMsg.data != null) {
        DbHelper.upDateNoticeTempData(userId, cmdId, noticeMsg.data!);
      }
      eventBus.fire(RefreshNoticeModel(
          type: 0, resp: OfflineNoticeItemResp(noticeMsg: noticeMsg)));
    }
    if (receiveData.noticeMsgUpdated.type == ConstantImMsgType.ApprovalMsgSessionType) {
      RefreshPagesData.refreshApproveUnreadData({});
    }
  }
}
