/// 用于 event 事件的消息实体，确定业务类型

class EventChatInfoMsgTop {
  String sessionId;
  bool isTop;
  EventChatInfoMsgTop(this.sessionId, this.isTop);

  @override
  String toString() {
    return 'EventChatInfoMsgTop{sessionId: $sessionId, isTop: $isTop}';
  }
}


class EventSessionMsgTop {
  String sessionId;
  bool isTop;
  EventSessionMsgTop(this.sessionId, this.isTop);

  @override
  String toString() {
    return 'EventSessionMsgTop{sessionId: $sessionId, isTop: $isTop}';
  }
}

class EventSessionMsgDisturb {
  String sessionId;
  bool disturbOpen;
  String handleId;  // 消息类型： 单群聊， 审批、工作通知等等
  EventSessionMsgDisturb(
      this.sessionId, this.disturbOpen, this.handleId);

  @override
  String toString() {
    return 'EventSessionMsgDisturb{sessionId: $sessionId, disturbOpen: $disturbOpen, handleId: $handleId, }';
  }
}

class EventAppLifeChange {
  String state;
  EventAppLifeChange(this.state);

  @override
  String toString() {
    return 'EventAppLifeChange{state: $state}';
  }
}