import 'dart:convert';
import 'dart:math';

import 'package:flutter_mixed/app/im/click_card/classify_type.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/refresh_notice_model.dart';
import 'package:flutter_mixed/app/im/ext/im_msg_ext.dart';
import 'package:flutter_mixed/app/im/ext/notice_msg_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_ext.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_helper.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_model.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/refresh_pages_data/refresh_pages_data.dart';
import 'package:flutter_mixed/app/im/request/entity/im_offline_notice_resp.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/ui/base/common_check.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_controller.dart';
import 'package:flutter_mixed/app/im/utils/im_global_util.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class NoticeMsgParser {
  final ImCommonData imCommonData;
  ImMsg imMsg;
  final NoticeMsg receiveData;

  NoticeMsgParser(this.imCommonData, this.imMsg, this.receiveData);

  Future parse() async {
    var userId = await ImGlobalUtil.currentUserId();
    ClientNoticeMsg noticeMsg = ClientNoticeMsg.fromJson(receiveData.toJson());
    OfflineNoticeItemResp resp = OfflineNoticeItemResp(
        noticeMsg: noticeMsg,
        appId: '',
        receiver: '',
        type: imCommonData.type,
        msgId: imCommonData.msgId,
        msgTime: imCommonData.msgTime);
    Notice notice = resp.getNoticeModel(userId);
    var type = notice.type;
    var msgType = notice.msgType ?? 0;
    if (type == ConstantImMsgType.ApprovalMsgSessionType) {
      //刷新审批refreshApproveUnreadData params:{}
      RefreshPagesData.refreshApproveUnreadData({});
    }
    if (msgType < ConstantImMsgType.SystemMsgSessionType) {
      //刷新指令不计离线
      if (msgType == ConstantImMsgType.SSChatMessageTypeRefreshAllOrgList) {
        //刷新全部公司
        RefreshPagesData.refreshOrgData();
      }
      if (msgType == ConstantImMsgType.SSChatMessageTypeRefreshWorkRedPoint) {
        //刷新工作台红点 params:{"orgId":"companyId"}
        RefreshPagesData.refreshWorkRedPoint({"orgId": notice.companyId ?? ''});
      }
      return;
    }
    if (msgType == ConstantImMsgType.SystemMsgFriendChange) {
      //刷新好友
      RefreshPagesData.refreshFriendData();
      return;
    }
    if (msgType == ConstantImMsgType.SSChatMessageTypeUserLogoutAccount) {
      //注销账号 给单聊页面发送通知 提示当前账号已注销userId:noticeMsg.context todo...
      return;
    }
    if (msgType ==
        ConstantImMsgType.SSChatMessageTypeExternalCollaborationDelete) {
      //被企业删除外部协作人身份
      RefreshPagesData.refreshOrgData();
      return;
    }
    if (type == ConstantImMsgType.SystemMsgSessionType) {
      //刷新待办refreshContactPendingData
      RefreshPagesData.refreshContactPendingData();
      if (msgType == ConstantImMsgType.SystemMsgCompanyRemove ||
          msgType == ConstantImMsgType.SystemMsgCompanyDissolution ||
          msgType == ConstantImMsgType.SSChatMessageTypeCompanyJoinedTeam) {
        //刷新公司数据
        RefreshPagesData.refreshOrgData();
      }
      if (msgType == ConstantImMsgType.SSChatMessageTypeChatExitGroup) {
        //主动退出群组 刷新群组 //通知群聊页面-处于此群组则变化UI不能发送消息
        RefreshPagesData.refreshGroupData();
        return;
      }
      if (msgType == ConstantImMsgType.SSChatMessageTypeChatGroupKicked) {
        //被踢出群组 //通知群聊页面-处于此群组则变化UI不能发送消息
        RefreshPagesData.refreshGroupData();
      }
      if (msgType == ConstantImMsgType.SSChatMessageTypeChatGroupDissolution) {
        //群组解散 //通知群聊页面-处于此群组则变化UI不能发送消息
        RefreshPagesData.refreshGroupData();
      }
    } else if (type == ConstantImMsgType.WorkMsgSessionType) {
      var classifyType = notice.classifyType;
      if (classifyType == ClassifyType.SSChatWorkNoticeClassifyAnnouncement) {
        //刷新企业
        RefreshPagesData.refreshOrgData();
      }
      if (classifyType ==
          ClassifyType.SSChatWorkNoticeClassifyPermissionsChange) {
        //刷新企业
        RefreshPagesData.refreshOrgData();
      }
    }
    //刷新会话列表
    ImOfflineNoticeResp noticeResp = ImOfflineNoticeResp(type: type)
      ..tag = notice.sessionId
      ..data = notice.data
      ..msgTime = notice.msgTime
      ..msgContent = notice.context;

    var session =
        noticeResp.createDbSessionFromOfflineNoticeSession(notice.data, userId);
    Session? listSession = await DbHelper.getSessionByOwnerId2SessionId(
        userId, notice.sessionId ?? '');

    if (session != null) {
      int notReadCount = (listSession?.notReadCount ?? 0) + 1;
      session.noDisturb = listSession?.noDisturb ?? 0;
      session.sessionTop = listSession?.sessionTop ?? 0;
      if (CommonCheck.checkIsOnTheNoticePage(session.sessionId)) {
        SystemNotificationListController systemNotificationListController = Get.find<SystemNotificationListController>(tag: session.sessionId);
        if (systemNotificationListController.isNoTag) {
          notReadCount = 0;
        }else{
          if (systemNotificationListController.companyId == notice.companyId) {
            notReadCount = 0;
          }
        }
      }
      session.notReadCount = notReadCount;
      DbHelper.insertSession(session);
    }

    DbHelper.insertNotice(notice);
    eventBus.fire(RefreshNoticeModel(type: 1, notice: notice));
    if (session?.noDisturb == 0) {
          var settingModel = await LocalNotiHelper.isCanSendAndIsNotOnthepage(
        Routes.IM_SYSTEM_NOTIFICATION_LIST, session?.sessionId);
      if (settingModel != null) {
        //生成本地通知model
        LocalNotiModel notiModel = imMsg.createNotificationModel(settingModel);
        LocalNotification().showNotification(notiModel);
      }
    }

  }
}
