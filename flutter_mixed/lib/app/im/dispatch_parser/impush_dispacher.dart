import 'package:flutter_mixed/logger/logger.dart';

import '../ext/im_msg_ext.dart';
import '../proto_model/Msg.pb.dart';
import 'multi_sync_dispatch_parser.dart';

// im push数据解析 : 单群聊免打扰开闭， 黑名单增删等
class ImPushDispatchParser {
  final ImPushMsg receiveData;

  ImPushDispatchParser(this.receiveData);

  Future parse() async {
    if (receiveData.isSingleChatMuteAdd()) {
      var uid = receiveData.singleChatIdIfMuteAdd();
      var disturbOpen = true;
      sendSessionMsgDisturb(uid, disturbOpen, uid);
    } else if (receiveData.isSingleChatMuteRemoved()) {
      var uid = receiveData.singleChatIdIfMuteRemoved();
      var disturbOpen = false;
      sendSessionMsgDisturb(uid, disturbOpen, uid);
    } else if (receiveData.isGroupChatMuteAdd()) {
      var groupId = receiveData.groupIdIfMuteAdd();
      var disturbOpen = true;
      sendSessionMsgDisturb(groupId, disturbOpen, groupId);
    } else if (receiveData.isGroupChatMuteRemoved()) {
      var groupId = receiveData.groupIdIfMuteRemoved();
      var disturbOpen = false;
      sendSessionMsgDisturb(groupId, disturbOpen, groupId);
    } else if (receiveData.isAddedBlackList()) {
      //已无黑名单功能
    } else if (receiveData.isRemovedBlackList()) {
      //已无黑名单功能
    } else if(receiveData.isWarning()){
      sendMsgForChat(receiveData.warnMessage.sessionId,receiveData.warnMessage.msg);
    }
  }
}
