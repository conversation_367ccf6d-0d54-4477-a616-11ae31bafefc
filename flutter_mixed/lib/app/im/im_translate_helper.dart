
import 'dart:convert';

import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/im_send_msg.dart';
import 'package:flutter_mixed/app/im/request/entity/chat_resp_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../common/user/user_helper.dart';
import '../modules/contact/model/org/dept_members_model.dart';
import '../routes/app_pages.dart';
import 'constant/ImMsgConstant.dart';
import 'convert/in_time_im_convert_to_session.dart';
import 'db/db_helper.dart';
import 'db/entity/message.dart';
import 'db/entity/session.dart';
import 'im_send_helper.dart';
import 'package:uuid/uuid.dart';


extension TransLateExt on TransLateModel {

  // 转发的时候更新session
  updateTransLateSession(Message? message, String targetName, String targetLogo ,{String? text}) async {
    if (message == null) return;
    var userId = await UserHelper.getUid();
    var session = await DbHelper.getSessionByOwnerId2SessionId(userId, message.sessionId ?? '');
    if(session == null){
      session = await createNewSession(message.sessionId ??'',
          message.sendHeader ?? '', message.sendName ?? '', message.sessionType ?? 1, message.msgType ?? 1,
          message.text ??'', message.msgId, message.sendTime ?? 0, 0, 0, false, message.appChatId ??'', 0);
    }else{
      var msgContent = message.alias();
      session.msgContent = msgContent;
      session.msgType = message.msgType;
      if (text != null) {
        session.msgContent = text;
      }
      session.msgTime = message.sendTime;
    }

    session.msgContent = message.alias();
    session.name = targetName;
    session.headerUrl = targetLogo;
    session.sessionHidden = 0;
    await DbHelper.insertSession(session);
  }

  // 单条转发
  Future translate(Message message) async {
    if(list == null) return;
    if(message.isText() || message.isQuote()){  // 文本或引用
      var text = '';
      if(message.isText()){
        text = message.text ?? '';
      }else{
        text = message.text ?? '';
        // var quote = Message.fromJson(jsonDecode(message.extendOne ?? ''));
        // text = quote.quoteText ?? '';
      }

      await Future.forEach(list!, (user) async {
        Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(message.uid!, user.imId);
        targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
        var targetSessionType = user.sessionType;
        var targetSessionId = user.imId;
        var targetName = user.name;
        var targetLogo = user.headimg;
        var newMsg = await targetSession.cacheLocalMessage(text , targetSessionId: targetSessionId , targetSessionType: targetSessionType);
        var uuid = newMsg.cmdId ?? '';
        await targetSession.sendTxtBySocket(uuid, text ,[] ,sessionType: targetSessionType,  targetSessionId: targetSessionId, targetName:targetName );
        await updateTransLateSession(newMsg , targetName , targetLogo);
      });

    } else if(message.isImage()){  // 图片

      await Future.forEach(list!, (user) async {
        Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(message.uid!, user.imId);
        targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
        var targetSessionType = user.sessionType;
        var targetSessionId = user.imId;
        var targetName = user.name;
        var targetLogo = user.headimg;
        var newMsg = await message.transImageMessage(targetSessionId ,_getTargetAppChatId(user), targetSessionType);
        var uuid = newMsg.cmdId ?? '';
        targetSession.setTargetLogo(targetLogo);
        await targetSession.sendImageBySocket(uuid, newMsg ,sessionType: targetSessionType,  targetSessionId: targetSessionId , targetName:targetName);
        await updateTransLateSession(newMsg , targetName , targetLogo);
      });
    } else if(message.isFile()){
      await Future.forEach(list!, (user) async {
        Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(message.uid!, user.imId);
        targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
        var targetSessionType = user.sessionType;
        var targetSessionId = user.imId;
        var targetName = user.name;
        var targetLogo = user.headimg;
        var newMsg = await message.transFileMessage(targetSessionId ,_getTargetAppChatId(user), targetSessionType);
        var uuid = newMsg.cmdId ?? '';
        targetSession.setTargetLogo(targetLogo);
        await targetSession.sendFileBySocket(uuid, newMsg ,sessionType: targetSessionType,  targetSessionId: targetSessionId ,targetName:targetName);
        await updateTransLateSession(newMsg , targetName , targetLogo);
      });
    } else if(message.isVideo()) {

      await Future.forEach(list!, (user) async {
        Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(message.uid!, user.imId);
        targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
        var targetSessionType = user.sessionType;
        var targetSessionId = user.imId;
        var targetName = user.name;
        var targetLogo = user.headimg;
        var newMsg = await message.transVideoMessage(targetSessionId ,_getTargetAppChatId(user), targetSessionType);
        var uuid = newMsg.cmdId ?? '';
        targetSession.setTargetLogo(targetLogo);
        await targetSession.sendVideoBySocket(uuid, newMsg ,sessionType: targetSessionType,
            targetSessionId: targetSessionId , targetName:targetName);
        await updateTransLateSession(newMsg , targetName , targetLogo);
      });

    }else if(message.isVoice()) {
      //  语音无需转发

    } else if(message.isMsgRecord()) {
      await Future.forEach(list!, (user) async {
        Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(message.uid!, user.imId);
        targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
        var targetSessionType = user.sessionType;
        var targetSessionId = user.imId;
        var targetName = user.name;
        var targetLogo = user.headimg;
        var newMsg = await message.transRecordMessage(targetSessionId ,_getTargetAppChatId(user), targetSessionType);
        var uuid = newMsg.cmdId ?? '';
        targetSession.setTargetLogo(targetLogo);
        await targetSession.sendExtBySocket(uuid, newMsg ,sessionType: targetSessionType,
            targetSessionId: targetSessionId , targetName:targetName);
        await updateTransLateSession(newMsg , targetName , targetLogo);
      });

    } else if(message.isBot()){
      // 转发机器人消息
      await Future.forEach(list!, (user) async {
        Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(message.uid!, user.imId);
        targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
        var targetSessionType = user.sessionType;
        var targetSessionId = user.imId;
        var targetName = user.name;
        var targetLogo = user.headimg;
        var newMsg = await message.transBotMessage(targetSessionId ,_getTargetAppChatId(user), targetSessionType);
        var uuid = newMsg.cmdId ?? '';
        targetSession.setTargetLogo(targetLogo);
        await targetSession.sendExtBySocket(uuid, newMsg ,sessionType: targetSessionType,
            targetSessionId: targetSessionId , targetName:targetName);
        await updateTransLateSession(newMsg , targetName , targetLogo);
      });
    } else if(message.isLocation()) {
      // 位置转发
      await Future.forEach(list!, (user) async {
        Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(message.uid!, user.imId);
        targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
        var targetSessionType = user.sessionType;
        var targetSessionId = user.imId;
        var targetName = user.name;
        var targetLogo = user.headimg;
        var newMsg = await message.transLocationMessage(targetSessionId ,_getTargetAppChatId(user), targetSessionType);
        var uuid = newMsg.cmdId ?? '';
        targetSession.setTargetLogo(targetLogo);
        await targetSession.sendLocationBySocket(uuid, newMsg ,sessionType: targetSessionType,
            targetSessionId: targetSessionId , targetName:targetName);
        await updateTransLateSession(newMsg , targetName , targetLogo);
      });

    }
  }


  // 【合并转发】
  // 1 缓存本地； 2 发送byteBuffer
  // 3 发送多端同步指令，防止没有这条会话
  Future transLateByMerge(List<Message> messageList,bool isGroup) async {
    if(list== null || list!.isEmpty) return;
    var uid = await UserHelper.getUid();
    var msgType = ConstantImMsgType.SSChatMessageTypeForwardRecord;

    var leaveMsg = this.text ?? '';
    logger('留言 ===> $leaveMsg');

    var transMembers = list ?? [];
    if (messageList.isEmpty) return;
    Message singleMessage = messageList.first;
    await Future.forEach(transMembers, (user) async {
      Session? targetSession = await DbHelper.getSessionByOwnerId2SessionId(singleMessage.uid!, user.imId);
      targetSession ??= Session(sessionId: user.imId,name: user.name,headerUrl: user.headimg);
      var targetSessionType = user.sessionType ?? 1;
      var targetSessionId = user.imId;
      var targetName = user.name;
      var targetLogo = user.headimg;

      var msgIds = messageList.map((m) => m.msgId).toList();

      var showTexts = messageList.map((m) => "${m.sendName ??''} : ${m.alias().spiltString(50)}").toList();
      logger('多条转发的时候 showText = $showTexts');
      var title = targetSession.recordContentAlias();
      var record = MsgUIRecord(
        title: title,
        msgIds: msgIds,
        showText: showTexts,
        sessionType: isGroup?2:1
      );

      var extendOne = jsonEncode(record.toJson());

      var msgContent = targetSession.recordSessionAlias();

      var m = Message('')
        ..uid = uid
        ..msgType = msgType
        ..sessionType = targetSessionType
        ..appChatId = _getTargetAppChatId(user)
        ..extendOne = extendOne
        ..sessionId = targetSessionId
        ..text = msgContent;

      var mergeMsg = await m.transRecordMessage(targetSessionId, _getTargetAppChatId(user), targetSessionType);
      var uuid = mergeMsg.cmdId ?? '';
      await targetSession.sendExtBySocket(uuid, mergeMsg ,sessionType: targetSessionType,
          targetSessionId: targetSessionId , targetName:targetName ,targetLogo:targetLogo);
      await updateTransLateSession(mergeMsg , targetName , targetLogo);
      // 补: 发送留言
      await sendLeaveMsgText(targetSession, leaveMsg, targetName ,targetSessionId
          , targetSessionType);
    });
  }

  sendLeaveMsgText(Session session ,String? text , String targetName
      , String targetSessionId , int targetSessionType) async {
    if (StringUtil.isEmpty(text)) return;
    Message message = await session.cacheLocalMessage(text , targetSessionId: targetSessionId,targetSessionType: targetSessionType);
    await session.sendTxtBySocket(message.cmdId ?? '', text, [] ,
        sessionType: targetSessionType,
        targetName: targetName, targetSessionId: targetSessionId);
    _updateSession(message);
  }

  _updateSession(Message? message) async {
    if (message == null) return;
    var userId = await UserHelper.getUid();
    var session = await DbHelper.getSessionByOwnerId2SessionId(userId, message.sessionId ?? '');
    if(session == null) return;
    var msgContent = message.alias();
    session.msgContent = msgContent;
    session.msgType = message.msgType;
    session.msgTime = message.sendTime;
    logger('发送留言的时候 存储session = ${session}');
    await DbHelper.insertSession(session);
  }


  // 【逐条转发】
  // 1 缓存本地； 2 发送byteBuffer
  Future transLateItemByItem(List<Message> messageList) async {
    await Future.forEach(messageList, (m) async {
      await translate(m);
    });
  }

  _getTargetAppChatId(MemberModel member) => member.sessionType == ConstantImMsgType.SSChatConversationTypeGroupChat ? member.imId : member.userId;

}

extension MessageTransLate on Message {

  Future<Message> transImageMessage(String targetSessionId , String targetAppChatId, int sessionType) async {

    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType =  sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeImage
      ..fileId = this.fileId
      ..fileSize = this.fileSize
      ..imgUrl = ''
      ..videoImageId = ''
      ..localUrl = this.localUrl
      ..imgWidth = this.imgWidth
      ..imgHeight = this.imgHeight
      ..text = ImPrefixMsg.imageCardString;

    message..sendId = ownId
      ..sendName = this.sendName
      ..sendHeader = this.sendHeader
      ..appChatId = targetAppChatId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  Future<Message> transFileMessage(String targetSessionId , String targetAppChatId, int sessionType) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeFile
      ..fileId = this.fileId
      ..fileSize = this.fileSize
      ..fileName = this.fileName
      ..localUrl = this.localUrl
      ..voiceTime = 0
      ..text = ImPrefixMsg.fileCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = targetAppChatId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  Future<Message> transVideoMessage(String targetSessionId , String targetAppChatId, int sessionType) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeVideo
    // 因为是预缓存video msg ，以下都为空 --------
      ..fileId = this.fileId
      ..fileSize = this.fileSize
      ..imgUrl = this.imgUrl
      ..videoImageId = this.videoImageId
      ..localUrl = this.localUrl
      ..videoImagePath = this.videoImagePath
      ..imgWidth = this.imgWidth
      ..imgHeight = this.imgHeight
      ..voiceTime = this.voiceTime
    // 因为是预缓存video msg ，以上都为空 --------
      ..text = ImPrefixMsg.videoCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = targetAppChatId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  Future<Message> transVoiceMessage(String targetSessionId , String targetAppChatId, int sessionType) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeVoice
    // 因为是预缓存video msg ，以下都为空 --------
      ..fileId = this.fileId
      ..fileSize = this.fileSize
      ..imgUrl = this.imgUrl
      ..videoImageId = this.videoImageId
      ..localUrl = this.localUrl
      ..videoImagePath = this.videoImagePath
      ..imgWidth = this.imgWidth
      ..imgHeight = this.imgHeight
      ..voiceTime = this.voiceTime
    // 因为是预缓存video msg ，以上都为空 --------
      ..text = ImPrefixMsg.voiceCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = targetAppChatId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  Future<Message> transBotMessage(String targetSessionId , String targetAppChatId, int sessionType) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeRobot
      ..extendOne = this.extendOne
      ..text = this.text;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = targetAppChatId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  Future<Message> transRecordMessage(String targetSessionId , String targetAppChatId, int sessionType) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeForwardRecord
      ..extendOne = this.extendOne
      ..text = this.text;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = targetAppChatId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  Future<Message> transLocationMessage(String targetSessionId , String targetAppChatId, int sessionType) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeMap
      ..addressTitle = addressTitle
      ..addressDetail = addressDetail
      ..addressImgUrl = addressImgUrl
      ..latitude = latitude
      ..longitude = longitude
      ..extendOne = this.extendOne

      ..text = ImPrefixMsg.locationCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = targetAppChatId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }


}


class TransLateModel {
  int? forwardType;
  String? fromSessionId;
  String? text;
  List<MemberModel>? list = [];

  TransLateModel({this.forwardType, this.fromSessionId, this.text, this.list});

  factory TransLateModel.fromJson(Map<String, dynamic> json) {
    return TransLateModel(
      forwardType: json['forwardType'],
      fromSessionId: json['fromSessionId'],
      text: json['text'],
      list: json['list'] == null
          ? List<MemberModel>.from([])
          : List<MemberModel>.from(
          json['list'].map((x) => MemberModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
    'forwardType': forwardType,
    'fromSessionId': fromSessionId,
    'text': text,
    'list': list?.map((e) => e.toJson()).toList(),
  };

  @override
  String toString() {
    return 'TransLateModel{forwardType: $forwardType, fromSessionId: $fromSessionId, text: $text, list: $list}';
  }
}
