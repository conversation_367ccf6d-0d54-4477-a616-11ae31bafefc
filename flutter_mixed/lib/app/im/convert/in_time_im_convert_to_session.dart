import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/utils/im_global_util.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../db/entity/session.dart';
import '../ui/chat/draft/draft.dart';

/// 实时im 消息转换为 session ,并存储
///

// 创建新session
Future<Session> createNewSession(String sessionId ,String logo,String name,
    int sessionType, int msgType, String msgContent , String msgId , int msgTime,
    int notReadCount , int isdistrub , bool sbAtU , String appChatId, int sessionTop) async{

  var uid = await ImGlobalUtil.currentUserId();
  var session = Session(sessionId: sessionId)
    ..sessionHidden = 0
    ..uid = uid
    ..msgType = msgType
    ..sessionType = sessionType
    ..name = name
    ..sessionId = sessionId
    ..headerUrl = logo
    ..msgId = msgId
    ..msgContent = msgContent
    ..notReadCount = notReadCount
    ..msgTime = msgTime
    ..noDisturb = isdistrub
    ..appChatId = appChatId
    ..sessionTop = sessionTop;

  // 创建新会话的时候如果这条session msgcontent 为空
  if(StringUtil.isEmpty(msgContent)){
    var uid = await UserHelper.getUid();
    var msg = await DbHelper.getLastAvailableMsgBySessionId(uid, sessionId);
    if(msg != null){
       msgContent = msg.alias();
       session.msgContent = msgContent;
    }
  }

  await keepDraftIfHas(session);
  DbHelper.insertSession(session);
  return session;
}
