
import 'dart:convert';

/// c2CMsgRequest 实体
class MultiSynchronizeMsgBean {

  MultiSynchronizeMsgBean({this.handleType, this.handleId, this.appChatId, this.name, this.headerUrl, this.switchStatus,this.sessionType});

  int? handleType = 0;
  String? handleId = "";
  String? appChatId = "";
  String? name = "";
  String? headerUrl = "";
  int? switchStatus = 0;
  int? sessionType = 1;

  factory MultiSynchronizeMsgBean.fromJson1(Map<String, dynamic> json) => MultiSynchronizeMsgBean(
      handleType: json['handleType'] ?? 0,
      handleId: json['handleId'] ?? '',
      appChatId: json['appChatId'] ?? '',
      name: json['name'] ?? '',
      headerUrl: json['headerUrl'] ?? '',
      switchStatus: json['switchStatus'] ?? 0,
      sessionType:json['sessionType']
    );

  Map<String, dynamic> toJson() => {
        'handleType': handleType,
        'handleId': handleId,
        'appChatId': appChatId,
        'name': name,
        'headerUrl': headerUrl,
        'switchStatus': switchStatus,
        'sessionType':sessionType
      };
}

MultiSynchronizeMsgBean convert2MultiSynchronizeMsgBean(String ext1) {
    var map = jsonDecode(ext1);
    if(map is Map<String,dynamic>){
      return MultiSynchronizeMsgBean.fromJson1(map);
    }
    throw Exception('');
}
