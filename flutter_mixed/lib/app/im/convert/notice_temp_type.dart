class NoticeTempType{
  static const int DDNotificationTemplateTypeOne   = 1; // 1系统通知的样式
  static const int DDNotificationTemplateTypeTwo   = 2; // 2考勤打卡的大图样式
  static const int DDNotificationTemplateTypeThree = 3; // 3工作通知通用样式(已弃用)
  static const int DDNotificationTemplateTypeFour  = 4; // 4审批通知的样式
  static const int DDNotificationTemplateTypeFive  = 5; // 5纯提示无详情(工作通知通用样式去掉底部查看详情)
  static const int DDNotificationTemplateTypeSix   = 6; // 6内容特殊(邀请提交汇报20301)(已弃用)
  static const int DDNotificationTemplateTypeSeven   = 7; // 7机器人消息20240604样式
  static const int DDNotificationTemplateTypeEight   = 8; // 8审批新样式短控件左右长控件最多展示2行
  static const int DDNotificationTemplateTypeNine   = 9; // 9idc知识库-新闻样式
}