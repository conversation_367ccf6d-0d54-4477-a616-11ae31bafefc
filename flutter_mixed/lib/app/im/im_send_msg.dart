//发送消息 非会话类
import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';

class ImSendMsg {
  static sendSingleMsgWithMessage(Message message) {
    if (message.sendId == null || message.sessionId == null) return;

    var senderInfo = UserInfo()
      ..userId = message.sendId!
      ..nickname = message.sendName ?? ''
      ..avatar = message.sendHeader ?? '';

    var sendMsg = ImMsg()
      ..cmdId = message.cmdId ?? ''
      ..senderInfo = senderInfo
      ..type = message.msgType ?? 1;

    if (message.text != null) {
      String msgContent = message.text!;
      if (msgContent.characters.length > 50) {
        var text = '${msgContent.getRange(0, 50)}';
        msgContent = text;
      }
      sendMsg.msgContent = msgContent;
    }

    var c2CMsgRequest = C2CMsgRequest()..receiver = message.sessionId!;
    switch (message.msgType) {
      case ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange:
        var extMsg = ExtMsg()..ext1 = message.extendOne ?? '';
        c2CMsgRequest.ext = extMsg;
        sendMsg.selfMsg = true;
        sendMsg.recordIgnore = true;
        break;
      default:
    }
    sendMsg.c2cMsgRequest = c2CMsgRequest;
    ImClientManager.instance.sendBuffer(sendMsg , needFeedback: false);
  }

  //同步指令获取Message
  static Future<Message> getSynchronousMessage(String extendOne) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();
    var ownImId = await UserHelper.getOwnImId();

    Message message = Message('')
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..sessionId = ownImId
      ..sessionType = ConstantImMsgType.SSChatConversationTypeChat
      ..msgType = ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange
      ..extendOne = extendOne
      ..cmdId = getUUid();
    return message;
  }
}
