import 'package:flutter_mixed/app/modules/contact/contact_home/controllers/contact_controller.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class RefreshPagesData {
  //刷新审批红点
  static refreshApproveUnreadData(arguments) {
    HomeController homeController = Get.find();
    homeController.getAllUnread();

    bool isWorkFlow = Get.isRegistered<WorkFlowController>();
    if (isWorkFlow) {
      WorkFlowController workFlowController = Get.find();
      workFlowController.getAllCompanyUntreated();
    }

    eventBus.fire({'listPage_refreshScreenData': 1});
    eventBus.fire({'refreshUnreadCount': 1});
    if (arguments['handleId'] != null) {
      eventBus.fire({'refresh_list_red_point': arguments['handleId']});
    }
  }

  //刷新全部公司
  static refreshOrgData() {
    HomeController homeController = Get.find();
    homeController.getAllCompanies();
  }

  //刷新工作台模块红点与所有公司红点
  static refreshWorkRedPoint(arguments) {
    bool isWorkFlow = Get.isRegistered<WorkFlowController>();
    if (isWorkFlow) {
      WorkFlowController workFlowController = Get.find();
      workFlowController.receiveRefreshNotice(arguments['orgId']);
    }
  }

  //刷新好友数据
  static refreshFriendData() {
    HomeController homeController = Get.find();
    homeController.getFriendList();
  }

  //刷新群组数据
  static refreshGroupData() {
    HomeController homeController = Get.find();
    homeController.getAllGroup();
  }

  //刷新通讯录红点
  static refreshContactPendingData() {
    bool isHaveContact = Get.isRegistered<ContactController>();
    if (isHaveContact) {
      ContactController contactController = Get.find();
      contactController.getPendingListCount();
    }
  }
}
