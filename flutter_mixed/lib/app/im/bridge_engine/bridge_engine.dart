



import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/utils/json_parse.dart';
import 'package:flutter_mixed/app/modules/invite_by_session/invite_by_session_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

import '../../common/channel/channel.dart';
import '../../common/cos/cos_manager.dart';
import '../../utils/image_util.dart';
import '../db/db_helper.dart';
import '../db/entity/message.dart';
import '../db/entity/session.dart';

Future invokeMainEngine(dynamic params) async {
  logger('invokeMainEngine==> $params');
  if(params is Map){
    var action = params['action'];
    switch(action){
      case 'shareImage':
        var bodyJson = params['body'];
        var j = jsonDecode(bodyJson);
        var shareEntity = ShareImageBody.fromJson(j);

        shareImage(shareEntity);

        break;
      
      default:
        break;
    }
  }
}


shareImage(ShareImageBody entity) async {
  // 压缩图片
  var imagePath = entity.path;
  var selects = entity.selecters ?? [];

  var compressFile = await ImageUtil.compressImg(imagePath!);
  if(compressFile == null) return;
  imagePath = compressFile.path;

  var fileId = await CosUploadHelper.getUploadFieldId();
  if (fileId == null) {
    return;
  }

  await CosManager().initPans();

  await CosUploadHelper.upload(imagePath, fileId: fileId);

  await Future.forEach(selects, (sessionUI) async {
    var targetSessionId = sessionUI.session.sessionId;
    var targetSessionType = sessionUI.session.sessionType ?? 1;
    var targetSessionName = sessionUI.session.name ?? '';

    var msg = await sessionUI.session.cacheLocalImageMessage(
      imagePath!,
      targetSessionId:targetSessionId,
      targetSessionType: targetSessionType,
    );

    var imageInfo = await ImageUtil.getImageInfo(imagePath!, fileId: fileId);
    imageInfo.path = imagePath;
    await msg.fillImageInfo(imageInfo);

    await DbHelper.insertMsg(msg);
    await _updateSession(sessionUI.session , msg);
    await sessionUI.session.sendImageBySocket(msg.cmdId ?? '', msg,
        targetSessionId: targetSessionId,
        sessionType: targetSessionType,
        targetName: targetSessionName
    );
  });
}

_updateSession(Session session ,Message? message, {String? text}) async {
  if (message == null) return;
  var msgContent = await message.alias();
  session.msgContent = msgContent;
  session.msgType = message.msgType;
  if (text != null) {
    session.msgContent = text;
  }
  session.msgTime = message.sendTime;
  logger('发送消息的时候 存储session = ${session}');
  DbHelper.insertSession(session);
}