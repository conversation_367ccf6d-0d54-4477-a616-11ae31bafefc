var LOGINRESULT = resp.getField(5);  // 收到， 登录结果
ImClientManagervar C2CMSG = resp.getField(6); // 单聊消息（收到）
ImClientManagervar C2CMSG_RESPONSE = resp.getField(8); // 单聊响应（收到）
ImClientManagervar GROUP_MSG = resp.getField(9); // 群聊
ImClientManagervar GROUP_MSG_RESPONSE = resp.getField(11); // 群聊 响应
ImClientManagervar PUSH_MSG = resp.getField(12); // 推送
ImClientManagervar SERVER_MSG = resp.getField(13); // 审批通知状态改变
ImClientManagervar NOTICE_MSG = resp.getField(14); // 收到 【系统通知】
ImClientManagervar EXCEPTION_MSG = resp.getField(3); // 收到 异常
ImClientManagervar KICK_MSG = resp.getField(23); // 被踢出
ImClientManagervar IMPUSH_MSG = resp.getField(24); // 接收pc端或者移动端进行了黑名单或者免打扰的设置---接收投诉提醒的提醒消息--
ImClientManagervar C2CMSGREQUEST = resp.getField(7); // 多端同步时用于接收另一端给自己设备发送的消息