import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:fixnum/src/int64.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/int64_ext.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/impush_dispacher.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/notice_msg_parser.dart';
import 'package:flutter_mixed/app/im/dispatch_parser/server_msg_parser.dart';
import 'package:flutter_mixed/app/im/request/datasource/im_datasource.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_controller.dart';
import 'package:flutter_mixed/app/im/utils/im_global_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/login_util.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'package:web_socket_channel/src/channel.dart';

import '../../main.dart';
import '../common/api/Define.dart';
import '../common/dialog/reset_login_dialog.dart';
import '../lock/lock.dart';
import '../modules/networkListen/controllers/network_listen_controller.dart';
import '../utils/storage.dart';
import 'constant/ImMsgConstant.dart';
import 'constant/im_share.dart';
import 'dispatch_parser/group_chat.dart';
import 'dispatch_parser/group_chat_feedback.dart';
import 'dispatch_parser/im_login_result.dart';
import 'dispatch_parser/multi_sync_dispatch_parser.dart';
import 'dispatch_parser/single_chat.dart';
import 'dispatch_parser/single_chat_feedback.dart';
import 'ext/im_msg_ext.dart';
import 'im_send_queue_manager.dart';
import 'manager/im_initializer.dart';
import 'proto_model/Msg.pb.dart';

/// 备份！！！！！
/// im Socket 连接管理
class FlutterImClientManager {
  static FlutterImClientManager? _instance;

  FlutterImClientManager._internal();

  static FlutterImClientManager get instance =>
      _instance ??= FlutterImClientManager._internal();

  int loginConnectTime = 0;

  IMConnectStatus connectStatus = IMConnectStatus.idle;

  WebSocketChannel? channel;

  bool isDebug = false;

  Timer? heartBeatTimer;

  StreamSubscription? _streamsubscription;

  // 使用自定义锁
  final CustomLock _customLock = CustomLock();

  StreamController<IMConnectStatus> connectListener =
  StreamController<IMConnectStatus>.broadcast();

  bool isConnected() => connectStatus == IMConnectStatus.connected;

  bool isConnecting() => connectStatus == IMConnectStatus.connecting;

  Future connectIm({ValueChanged<bool>? callBack}) async {
    try {
      await _customLock.acquire();

      if(!await couldReconnect()) {
        return;
      };

      await disConnect();
      await Future.delayed(const Duration(milliseconds: 1200)); // 预留资源释放时间

      logger('开始进行连接......');
      await _connecting();

      var imToken = await ImShareValues.getImtoken();
      if (StringUtil.isEmpty(imToken)) {
        imToken = await fetchImToken();
        ImShareValues.saveImtoken(imToken);
      }
      logger('===========获取到imToken====$imToken');
      if (StringUtil.isEmpty(imToken)) {
        logger('获取imtoken 失败');
        _connectFailed();
        callBack?.call(false);
        return;
      } else {
        callBack?.call(true);
      }
      var path = await fetchSocketUrl(imToken);
      if (StringUtil.isEmpty(path)) {
        logger('获取socketUrl 失败');
        _connectFailed();
        callBack?.call(false);
        return;
      }
      channel = WebSocketChannel.connect(Uri.parse(path));
      await channel?.ready;
      String localUserId = await UserHelper.getUid();

      _loginSocket(imToken ,localUserId);
      _checkImHearBeat(channel);
      _streamsubscription = channel?.stream.listen((message) {
        // logger('收到原始格式： $message');
        _handleMessage(message);
      }, onError: (e) {
        _connectFailed();
        disConnect();
        logger('连接报错了...$e');
      }, onDone: () async {
        _connectFailed();
        logger('连接关闭了...');
        callBack?.call(false);

        reConnect();
      });
    } catch(e) {
      logger('connectIm error = $e');
      _connectFailed();
      // disConnect();
    }finally {
      logger('======finally=====');
      _customLock.release();
    }
  }

  _handleMessage(dynamic message) async {
    if (message is Uint8List) {
      try {
        var resp = ImMsg.fromBuffer(message);
        logger("resp = $resp");

        var msgId = resp.msgId;
        var cmdId = resp.cmdId;
        var msgTime = resp.msgTime;
        var msgType = resp.type;
        var selfMsg = resp.selfMsg;
        var senderInfo = resp.senderInfo;
        var msgContent = resp.msgContent;

        var nickName = senderInfo.nickname;
        var imCommonData = resp.createImCommonReceiverData();

        logger('msgId ：$msgId,  cmdId: $cmdId , time: $msgTime , msgType: $msgType , msgContent :$msgContent');

        if (resp.isIMLoginResp())  {
          logger('收到im登录');
          ImLoginResult();
          _connected();
          eventBus.fire(ImConnected());

          ImSendQueueManager.instance.validateMsgSendStatus();
          loginConnectTime = DateTime.now().millisecondsSinceEpoch;
          await ImInitializer.instance.init();
          _reRequestSessionData();
        }

        if (resp.isIMSingleChat()) {
          logger('收到单聊');
          var receiveData = resp.singleChatData();
          SingleChatParser(imCommonData, resp, receiveData).parse();
        }

        if (resp.isIMSingleResp()) {
          logger('单聊响应');
          SingleChatFeedBackParser(imCommonData, resp.getSingleRespData())
              .parse();
        }

        if (resp.isIMGroup()) {
          logger('收到群聊');
          var receiveData = resp.getGroupData();
          GroupChatParser(imCommonData, resp, receiveData).parse();
        }

        if (resp.isIMGroupResp()) {
          var r = resp.getGroupRespData();
          var sendResult = r.isSuccess();
          logger('群聊响应: ${sendResult}');
          GroupChatFeedBackParser(imCommonData, resp.getGroupRespData())
              .parse(sendResult);
        }

        if (resp.isNotice()) {
          // 收到通知
          NoticeMsgParser(imCommonData, resp, resp.systemNoticeData())
              .parse();
        }

        if (resp.isImApproveChanged()) {
          //收到了更新推送
          ServerMsgParser(resp.imApproveChangedData()).parse();
        }

        if (resp.isPush()) {
          // 推送 do nothing
        }

        if (resp.isImException()) {
          // 异常， do nothing
        }

        if (resp.isKickOff()) {
          if (imCommonData.msgContent == 'RECONNECT') {
            reConnect();
          }else{
            showReLoginDialog('当前账号在其它设备登录!');
            LoginUtil.reLogin(invokeNative: true);
            disConnect();
          }
        }

        if (resp.isImPush()) {
          var receiveData = resp.getImPushData();
          logger('im push : ${receiveData}');
          ImPushDispatchParser(receiveData).parse();
        }

        if (resp.isImMultiSelf()) {
          logger('多端同步 $resp ， $msgType , $selfMsg');
          var c2cRequest = resp.imMultiSelf();
          MultiSyncParser(c2cRequest.ext.ext1, selfMsg, msgType)
              .parseData();
        }
      } catch (e) {
        logger('error ==> $e');
      }
    } else {
      logger('非Unit8List类型');
    }
  }

  _loginSocket(String imToken ,String localUserId) async {
    var loginReq = await createLoginRequestBody(imToken, localUserId);
    channel?.sink.add(loginReq.writeToBuffer());
  }

  releaseLock() async {
    _customLock.release();
  }

  _connecting() {
    connectStatus = IMConnectStatus.connecting;
    connectListener?.sink.add(connectStatus);
  }

  _connected() {
    connectStatus = IMConnectStatus.connected;
    connectListener?.sink.add(connectStatus);
  }

  _connectFailed() {
    connectStatus = IMConnectStatus.failed;
    connectListener?.sink.add(connectStatus);
  }

  //  relation/im/users/v1/min
  Future<String> fetchImToken() async {
    try {
      var datasource = await ImDataSource(
        retrofitDio,
      );
      var resp = await datasource.fetchImToken();
      if (resp.success()) {
        return resp.data.token;
      }
      return '';
    } catch (e) {
      return '';
    }
  }

  Future<String> fetchSocketUrl(String imToken) async {
    try{
      var datasource = ImDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);
      var resp = await datasource.fetchSocketLink();
      logger('fetchSocketUrl = > $resp');
      return resp.success() ? resp.data.url ?? '': "";
    }catch(e){
      logger('fetchSocketUrl = > $e');
      return '';
    }
  }

  /// 心跳检测
  void _checkImHearBeat(WebSocketChannel? channel) async {
    logger('----开启定时器');
    if (heartBeatTimer != null) {
      return;
    }
    heartBeatTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if(channel == null ) return;
      if(channel.closeCode  == null){
        channel.sink.add('ping');
        logger('------发送心跳---------');
      }else {
        timer.cancel();
      }
    });
  }

  Future<ImMsg> createLoginRequestBody(String imToken, String userId) async {
    var uuid = const Uuid().v1().replaceAll(RegExp('-'), '');

    var deviceId = await DeviceUtils.getUDIDStr();
    var tokeTarget = '';
    if (StringUtil.isEmpty(deviceId)) {
      tokeTarget = imToken;
    } else {
      tokeTarget = '$imToken::$deviceId';
    }
    if (Platform.isIOS) {
      tokeTarget = imToken;
    }
    var loginReq = LoginRequest()
      ..token = tokeTarget
      ..clientType = Platform.isAndroid
          ? LoginRequest_ClientTypeEnum.ANDROID
          : LoginRequest_ClientTypeEnum.IOS;

    var sendMsg = ImMsg()
      ..cmdId = uuid
    // ..senderInfo = senderInfo
      ..msgTime = Int64(DateTime.now().millisecondsSinceEpoch)
      ..type = ConstantImMsgType.SSChatMessageTypeText
      ..loginRequest = loginReq
      ..recordIgnore = false;

    return sendMsg;
  }

  Future sendTopMsg(String ownerId, String targetSessionId, String handleId,
      bool isTop) async {
    createMsgTopRequest(ownerId, targetSessionId, handleId, isTop);
  }

  // 发送同步置顶消息
  Future<ImMsg> createMsgTopRequest(String ownerId, String targetSessionId,
      String handleId, bool isTop) async {
    var msg = {
      'handleType': 6,
      'handleId': handleId,
      'switchStatus': isTop ? 1 : 0
    };
    return sendSynchronous(ownerId, targetSessionId, msg);
  }

  Future<ImMsg> sendSynchronous(
      String ownerId, String targetSessionId, msg) async {
    var userInfo = await ImGlobalUtil.currentUserInfo();
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var senderInfo = UserInfo()
      ..userId = ownerId
      ..nickname = userInfo?.name ?? ''
      ..avatar = userInfo?.avatar ?? '';
    var jsonData = json.encode(msg);

    var extMsg = ExtMsg()..ext1 = jsonData;

    var c2CMsgRequest = C2CMsgRequest()
      ..receiver = userInfo?.imId ?? ''
      ..ext = extMsg;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = DateTime.now().millisecondsSinceEpoch.toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange // 扩展
      ..msgContent = ''
      ..c2cMsgRequest = c2CMsgRequest
      ..selfMsg = true
      ..recordIgnore = true;
    channel?.sink.add(sendMsg.writeToBuffer());
    return sendMsg;
  }

  /// 主动断开连接
  Future disConnect() async {
    logger('========主动断开连接--------');
    try {
      if (heartBeatTimer != null) {
        logger('-------关闭定时器--------');
        heartBeatTimer!.cancel();
        heartBeatTimer = null;
      }
      if (_streamsubscription != null) {
        _streamsubscription?.cancel();
        _streamsubscription = null;
      }
      // 添加 1000 状态码：正常关闭
      await channel?.sink.close(1000 , 'Normal closure');
      channel = null;
      connectStatus = IMConnectStatus.idle;
    } catch (e) {}
  }

  Future _notLogined() async {
    var userInfo = await UserDefault.getData(Define.TOKENKEY);
    return userInfo == null;
  }

  Future testReConnect() async {
    // var list = [];
    // for(int i = 0;i< 50; i++)  {
    //   list.add(i);
    // }
    // Future.forEach(list, (x) async {
    //   await reConnect();
    // });

    startTaskRunner(reConnect);

  }

  void startTaskRunner(VoidCallback customTask) {
    const Duration interval = Duration(seconds: 2);
    const int maxRunsPerGroup = 3;
    const Duration totalDuration = Duration(minutes: 5);

    DateTime endTime = DateTime.now().add(totalDuration);

    void runGroup() async {
      for (int i = 0; i < maxRunsPerGroup; i++) {
        if (DateTime.now().isAfter(endTime)) {
          print('超过 5 分钟，停止任务');
          return;
        }

        customTask();
        print('执行任务 第 ${i + 1} 次（组） at ${DateTime.now()}');
        await Future.delayed(interval);
      }

      // 下一组（递归调用直到超时）
      runGroup();
    }

    runGroup();
  }


  Future<bool> couldReconnect() async {
    var hasNet = await netConnected();
    if(!hasNet) {
      logger('无网络无法重连im');
      _connectFailed();
      return false;
    }

    if(await _notLogined()) {
      _connectFailed();
      return false;
    };
    if(isConnected() || isConnecting()){
      logger('连接中，无须重连');
      _connected();
      return false;
    }
    return true;
  }

  Future reConnect() async {
    try {
      // await Future.delayed(const Duration(seconds: 1));
      connectIm();
      _reRequestSessionData();
    } catch (e) {}
  }

  // 重连后 需要重新加载 会话数据
  _reRequestSessionData() async {
    bool isHave = Get.isRegistered<SessionController>();
    if (isHave) {
      SessionController sessionController = Get.find();
      sessionController.loadSession();
    }
  }

  Future sendBuffer(ImMsg imMsg, {bool? needFeedback = true}) async {
    if (needFeedback == true) {
      logger('sendBuffer.....${imMsg.cmdId}');
    }
    channel?.sink.add(imMsg.writeToBuffer());
  }
}

enum IMConnectStatus {
  idle,
  connecting,
  connected,
  failed,
}

// 监听回到前台，做一些恢复性的操作，比如从新拉取session，如果当前是chat页面，检查发送中的数据，重新发送等。
class ResumeSender {}

class ImConnected {}
