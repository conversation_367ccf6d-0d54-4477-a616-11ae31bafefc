import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

class NotificationBackIm{
  //tabbar回到消息 清空栈
  static clearVcAndBackIm() async{
    await Channel().invoke(CHANNEL_CLEAR_NATIVE_VC, {});
    Get.until((route) => route.settings.name == Routes.HOME);
    try {
      HomeController homeController = Get.find();
      homeController.currentIndex.value = 0;
      homeController.update();
    } catch (e) {
      
    }
  }
}