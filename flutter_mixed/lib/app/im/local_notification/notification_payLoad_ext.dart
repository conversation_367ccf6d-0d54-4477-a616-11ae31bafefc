import 'dart:convert';

import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/constant/constant_tcp_util.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_helper.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_payLoad_model.dart';
import 'package:flutter_mixed/app/im/local_notification/notification_back_im.dart';
import 'package:flutter_mixed/app/im/request/entity/notice_detail_data.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_binding.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../click_card/click_card_jump.dart';
import '../click_card/notice_funtion_type.dart';

extension NotificationPayloadExt on LocalNotiPayLoadModel {
  Future didClickNotification({bool isNeedClear = true}) async {
    //isNeedClear 是否需要清栈 通过点击离线推送启动app不需要清栈
    var myUserId = await UserHelper.getUid();
    if (myUserId.isEmpty) return;
    if (isNeedClear) {
      await NotificationBackIm.clearVcAndBackIm();
    }
    if (type == ConstantImMsgType.SSChatConversationTypeChat) {
      //单聊
      if (!_judgeIsNotOnThePage(Routes.IM_CHAGE_PAGE, imUserId)) {
        return;
      }

      if (imUserId != null) {
        if (StringUtil.isEmpty(imUserId)) return;
        var session =
            await DbHelper.getSessionByOwnerId2SessionId(myUserId, imUserId!);
        session ??= Session(sessionId: imUserId ?? '')
          ..sessionType = ConstantImMsgType.SSChatConversationTypeChat
          ..headerUrl = avatar
          ..name = nickname
          ..appChatId = userId
          ..uid = myUserId;
        session.sessionHidden = 0;
        RouteHelper.routeTotag(
              ChatPage(tag: session.sessionId), Routes.IM_CHAGE_PAGE,
              arguments: {'session':session,'isOffline':true}, binding: ChatBinding(tag: session.sessionId));
      }
    } else if (type == ConstantImMsgType.SSChatConversationTypeGroupChat) {
      //群聊
      if (!_judgeIsNotOnThePage(Routes.IM_CHAGE_PAGE, groupId)) {
        return;
      }
      if (groupId != null) {
        if (StringUtil.isEmpty(groupId)) return;
        var session =
            await DbHelper.getSessionByOwnerId2SessionId(myUserId, groupId!);
        session ??= Session(sessionId: groupId ?? '')
          ..sessionType = ConstantImMsgType.SSChatConversationTypeGroupChat
          ..headerUrl = groupLogo
          ..name = groupName
          ..appChatId = groupId
          ..uid = myUserId;
        session.sessionHidden = 0;
        RouteHelper.routeTotag(
              ChatPage(tag: session.sessionId), Routes.IM_CHAGE_PAGE,
              arguments: {'session':session,'isOffline':true}, binding: ChatBinding(tag: session.sessionId));
      }
    } else if (type == ConstantImMsgType.WorkMsgSessionType) {
      //工作通知

      if (data != null) {
        NoticeData noticeData = NoticeData.fromJson(json.decode(data!));
        if (noticeData.companyId != null) {
          if (!_judgeIsNotOnThePage(
              Routes.IM_SYSTEM_NOTIFICATION_LIST, noticeData.companyId!)) {
            return;
          }
          var session = await DbHelper.getSessionByOwnerId2SessionId(
              myUserId, noticeData.companyId!);
          if (session != null) {
            RouteHelper.routeTotag(
                SystemNotificationListPage(
                  tag: session.sessionId,
                ),
                Routes.IM_SYSTEM_NOTIFICATION_LIST,
                arguments: {'session':session,'isOffline':true},
                binding: SystemNotificationListBinding(tag: session.sessionId));
          }
        }
      }
    } else if (type == ConstantImMsgType.TeamMsgSessionType) {
      //合作企业通知
      if (!_judgeIsNotOnThePage(Routes.IM_SYSTEM_NOTIFICATION_LIST,
          ConstantTcpUtil.TCP_TEAM_SESSIONID)) {
        return;
      }
      var session = await DbHelper.getSessionByOwnerId2SessionId(
          myUserId, ConstantTcpUtil.TCP_TEAM_SESSIONID);
      if (session != null) {
            RouteHelper.routeTotag(
                SystemNotificationListPage(
                  tag: session.sessionId,
                ),
                Routes.IM_SYSTEM_NOTIFICATION_LIST,
                arguments: {'session':session,'isOffline':true},
                binding: SystemNotificationListBinding(tag: session.sessionId));
      }
    } else if (NoticeTypeManager.noticeTypeMap.keys.contains(type)) {
      //其他通知
      if (NoticeTypeManager.noticeTypeMap[type] != null) {
        if (!_judgeIsNotOnThePage(Routes.IM_SYSTEM_NOTIFICATION_LIST,
            NoticeTypeManager.noticeTypeMap[type])) {
          return;
        }
        if (type == ConstantImMsgType.SystemMsgSessionType) {
          //系统通知
          var session = await DbHelper.getSessionByOwnerId2SessionId(
              myUserId, NoticeTypeManager.noticeTypeMap[type]!);
          if (session != null) {
            RouteHelper.routeTotag(
                SystemNotificationListPage(
                  tag: session.sessionId,
                ),
                Routes.IM_SYSTEM_NOTIFICATION_LIST,
                arguments: {'session':session,'isOffline':true},
                binding: SystemNotificationListBinding(tag: session.sessionId));
          }
        } else {
          //各种审批通知
          if (data != null) {
            NoticeData noticeData = NoticeData.fromJson(json.decode(data!));
            NoticeDataDetail? detailData = noticeData.getDetail();
            switch (noticeData.functionType) {
              case NoticeFunctonType.DDJumpFunctionTypeWeb:
                if (detailData?.link == null) return;
                ClickCardJump.webJump(
                    detailData!.link!, noticeData?.companyId ?? '');
                break;
              case NoticeFunctonType.DDJumpFunctionTypeWebWithNavigationBar:
                if (detailData?.link == null) return;
                ClickCardJump.webJump(
                    detailData!.link!, noticeData?.companyId ?? '',
                    isWebNavigation: 0);
                break;
              case NoticeFunctonType.DDJumpFunctionTypeWebWithParams:
                if (detailData?.paramType != null) {
                  if (detailData?.paramType == 0) {
                    //拼接金蝶token
                    ClickCardJump.getKingDeeToken(detailData?.link);
                  }else{
                    ClickCardJump.webJump(
                      detailData!.link!, noticeData?.companyId ?? '',
                      isWebNavigation: 0);
                  }
                }
                break;
              case NoticeFunctonType.DDJumpFunctionTypeFlutter:
                if (detailData?.routeName != null) {
                  RouteHelper.route(detailData!.routeName!,
                      arguments: detailData.argument);
                }

                break;
              case NoticeFunctonType.DDJumpFunctionTypeVisitorWebList:
                Map<String, dynamic> headerMap =
                    await UserDefault.getHttpHeader();
                String url =
                    '${Host.WEBHOST}/visitor/index.html#/applyList?height=${DeviceUtils().top.value}&version=${headerMap['appVersion']}';
                ClickCardJump.webJump(url, noticeData?.companyId ?? '');
                break;
              default:
                var session = await DbHelper.getSessionByOwnerId2SessionId(
                    myUserId, NoticeTypeManager.noticeTypeMap[type]!);
                if (session != null) {
                  RouteHelper.routeTotag(
                  SystemNotificationListPage(
                    tag: session.sessionId,
                  ),
                  Routes.IM_SYSTEM_NOTIFICATION_LIST,
                  arguments: {'session':session,'isOffline':true},
                  binding: SystemNotificationListBinding(tag: session.sessionId));
                }
            }
          }
        }
      }
    }
  }

  _judgeIsNotOnThePage(String routeName, String? sessionId) {
    return LocalNotiHelper.isNotOnThePage(routeName, sessionId);
  }
}
