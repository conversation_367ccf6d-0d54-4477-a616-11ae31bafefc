import 'dart:io';

import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_ext.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_model.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/system_notification/system_notification_list_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';

class LocalNotiHelper {
  //获取本地通知配置开关状态
  static Future<Map<String, dynamic>?> getLocalNotificationSetting() async {
    var localNoticeSetting;
    try {
      localNoticeSetting =
          await Channel().invokeMap(Channel_getLocalNotificationSetting, {});
    } catch (e) {}

    Map<String, dynamic>? map;
    if (localNoticeSetting != null) {
      map = localNoticeSetting.cast<String, dynamic>();
    }
    return map;
  }

  static assignmentAndroidDefaultNotificationSetting(
      Map<String, dynamic>? localMap) async {
    if (Platform.isAndroid) {
      Map<String, dynamic> map = {'isNotActive': false};
      return map;
    }
    return localMap;
  }

  //是否能够发送并且不在当前页面
  static isCanSendAndIsNotOnthepage(String routeName, String? sessionId) async {
    var localMap = await LocalNotiHelper.getLocalNotificationSetting();
    // TODO 暂时给默认值
    localMap = await assignmentAndroidDefaultNotificationSetting(localMap);

    bool isCanSend = true; //是否能发送
    bool isNotOnPage = true; //是否不在当前页面
    LocalNotiModel settingModel = LocalNotiModel();
    if (localMap != null) {
      settingModel = LocalNotiModel.fromJson(localMap);
      isCanSend = settingModel.judgeIsCanSendNotification();
      isNotOnPage = LocalNotiHelper.isNotOnThePage(routeName, sessionId);
    }
    if (settingModel.isNotActive != null) {
      if (settingModel.isNotActive!) {
        return settingModel;
      }
    }
    if (isCanSend && isNotOnPage) {
      return settingModel;
    }
    return null;
  }

  static isNotOnThePage(String routeName, String? sessionId) {
    if (routeName == Routes.IM_SYSTEM_NOTIFICATION_LIST) {
      //通知页面
      try {
        bool isRegistered =
            Get.isRegistered<SystemNotificationListController>(tag: sessionId);
        if (isRegistered) {
          return false;
        }
      } catch (e) {
        logger('===isNotOnThePage==catch===$e');
      }
    }
    if (routeName == Routes.IM_CHAGE_PAGE) {
      //聊天页面
      try {
        bool isRegistered = Get.isRegistered<ChatController>(tag: sessionId);
        if (isRegistered) {
          return false;
        }
      } catch (e) {
        logger('===isNotOnThePage==catch===$e');
      }
    }
    return true;
  }
}
