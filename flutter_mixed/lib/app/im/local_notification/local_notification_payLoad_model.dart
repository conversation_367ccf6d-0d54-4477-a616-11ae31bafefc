class LocalNotiPayLoadModel {
  int? type = 1; //会话类型
  String? data = ''; //通知类型携带的数据

  //目前远程通知字段
  //单聊
  String? imUserId = '';
  String? userId = '';
  String? nickname = '';
  String? avatar = '';
  //群聊
  String? groupId = '';
  String? groupName = '';
  String? groupLogo = '';

  LocalNotiPayLoadModel({this.type, this.data});
  LocalNotiPayLoadModel.fromJson(Map<String, dynamic> json) {
    type = (json['type'] ?? 0) as int;
    data = (json['data'] ?? '') as String;

    imUserId = (json['imUserId'] ?? '') as String;
    userId = (json['userId'] ?? '') as String;
    nickname = (json['nickname'] ?? '') as String;
    avatar = (json['avatar'] ?? '') as String;

    groupId = (json['groupId'] ?? '') as String;
    groupName = (json['groupName'] ?? '') as String;
    groupLogo = (json['groupLogo'] ?? '') as String;
  }

  Map<String, dynamic> toJson() => {
        'type': type,
        'data': data,
        'imUserId': imUserId,
        'userId': userId,
        'nickname': nickname,
        'avatar': avatar,
        'groupId': groupId,
        'groupName': groupName,
        'groupLogo': groupLogo,
      };
}
