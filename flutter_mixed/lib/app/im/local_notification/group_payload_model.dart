class GroupPayloadModel {
  String? groupId;
  String? groupName;
  String? groupLogo;

  GroupPayloadModel({this.groupId, this.groupName, this.groupLogo});

  GroupPayloadModel.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'] ?? '';
    groupName = json['groupName'] ?? '';
    groupLogo = json['groupLogo'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = Map<String, dynamic>();
    json['groupId'] = groupId;
    json['groupName'] = groupName;
    json['groupLogo'] = groupLogo;
    return json;
  }
}
