class LocalNotiModel {
  //通知数据
  int? noticeId = 0; //通知id
  String? title = ''; //标题
  String? body = ''; //内容
  String? payload = ''; //携带数据
  int? badgeCount = 0; //角标 待定,暂不配置,收到通知更新会话列表时会更新应用角标
  bool? sound = true; //声音
  bool? vibrate = true; //震动(iOS通知无震动属性,需单独实现)

  //自定义字段 
  bool? isNotActive = false; //app是否处于非活跃状态 处于非活跃状态时不在当前页面也需要推送
  bool? isReceive = true;//是否开启接收新消息功能

  LocalNotiModel(
      {this.noticeId,
      this.title,
      this.body,
      this.payload,
      this.badgeCount,
      this.sound,
      this.vibrate});
  LocalNotiModel.fromJson(Map<String, dynamic> json) {
    noticeId = (json['noticeId'] ?? 0) as int;
    title = (json['title'] ?? '') as String;
    body = (json['body'] ?? '') as String;
    payload = (json['payload'] ?? '') as String;
    badgeCount = (json['badgeCount'] ?? 0) as int;
    sound = (json['sound'] ?? true) as bool;
    vibrate = (json['vibrate'] ?? true) as bool;
    isNotActive = (json['isNotActive'] ?? false) as bool;
    isReceive = (json['isReceive'] ?? true) as bool;
  }

  Map<String, dynamic> toJson() => {
        'noticeId': noticeId,
        'title': title,
        'body': body,
        'payload': payload,
        'badgeCount': badgeCount,
        'sound': sound,
        'vibrate': vibrate,
        'isNotActive':isNotActive,
        'isReceive':isReceive
      };

  @override
  String toString() {
    return 'LocalNotiModel{title: $title, body: $body, badgeCount: $badgeCount, isNotActive: $isNotActive}';
  }
}
