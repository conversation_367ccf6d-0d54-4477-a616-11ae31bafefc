import 'dart:async';

import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_payLoad_model.dart';
import 'package:flutter_mixed/app/im/local_notification/notification_payLoad_ext.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';

class NotificationTask{
  static LocalNotiPayLoadModel? cachePayLoadModel;
  static String sign = 'notificationTask';
  static StreamSubscription? sessionSubscription;
  static StreamSubscription? approveSubscription;

  static didReciveNotification(LocalNotiPayLoadModel? notiPayLoadModel){
    
    // if (notiPayLoadModel == null) return;
    // List<Future<dynamic>> tasks = [approveTask()];
    // if (!NotificationApproveHelper.isNeedWait) {
    //   //app IM掉线收到了离线推送
    //   notiPayLoadModel.didClickNotification(isNeedClear: NotificationApproveHelper.isNeedWait);
    // }else{
    //   Future.wait(tasks).then((value){
    //     notiPayLoadModel.didClickNotification(isNeedClear: NotificationApproveHelper.isNeedWait);
    //   });
    // }

  }

  static Future sessionTask(){
    Completer completer = Completer();
    sessionSubscription = eventBus.on<NotificationTaskModel>().listen((event) {
      if (event.sign == sign && event.type == 1) {
        completer.complete();
        sessionSubscription?.cancel();
        sessionSubscription = null;
      }
    });
    return completer.future;
  }

  static Future approveTask(){
    Completer completer = Completer();
    approveSubscription = eventBus.on<NotificationTaskModel>().listen((event) {
      if (event.sign == sign && event.type == 2) {
        completer.complete();
        approveSubscription?.cancel();
        approveSubscription = null;
      }
    });
    return completer.future;
  }

}

class NotificationTaskModel{
  String? sign;
  int? type = 0;//1session完成 2审批中心任务完成
  NotificationTaskModel({this.sign,this.type});
}

class NotificationApproveHelper{
  //记录是否需要等待审批中心弹窗
  static bool isNeedWait = true;
  static dealApproveWaitInfo(){
    isNeedWait = false;
    if (NotificationTask.approveSubscription != null) {
      eventBus.fire(NotificationTaskModel(sign: NotificationTask.sign,type: 2));
    }
  }
}