import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_model.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_payLoad_model.dart';
import 'package:flutter_mixed/app/im/local_notification/notification_payLoad_ext.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';

class LocalNotification {
  int id = 0;
  static final LocalNotification _instance = LocalNotification._init();
  static late FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;

  factory LocalNotification() {
    return _instance;
  }

  getFlutterLocalNotificationsPlugin() => _flutterLocalNotificationsPlugin;

  LocalNotification._init() {
    _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  }

  requestNotificationPermission() {
    if (Platform.isAndroid)
      _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
  }

  Future initLocalNotification() async {
    await _flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings("@mipmap/ic_launcher");
    const DarwinInitializationSettings initializationSettingsDarwin =
        DarwinInitializationSettings();
    InitializationSettings initializationSettings =
        const InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin,
    );

    await _flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
        onDidReceiveNotificationResponse:
            (NotificationResponse notificationResponse) async {
      switch (notificationResponse.notificationResponseType) {
        case NotificationResponseType.selectedNotification:
          logger(
              '被点击了---selectedNotification---${notificationResponse.payload}');
          if (notificationResponse.payload != null) {
            String payLoad = notificationResponse.payload!;
            LocalNotiPayLoadModel payLoadModel =
                LocalNotiPayLoadModel.fromJson(json.decode(payLoad));
            await payLoadModel.didClickNotification();
          }
          break;
        case NotificationResponseType.selectedNotificationAction:
          logger(
              '被点击了---selectedNotificationAction---${notificationResponse.payload}');
          break;
      }
    });

    // android 创建通知通道
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'localNotification_dd_channelId', // 通道ID
      '担当本地通知', // 通道名称
      importance: Importance.max, // 设置重要性为最大
    );
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future showNotification(LocalNotiModel notiModel) async {
    AndroidNotificationDetails androidNotificationDetails =
        const AndroidNotificationDetails(
            'localNotification_dd_channelId ',
            '担当本地通知',
            // channelDescription: 'your channel description',
            importance: Importance.max,
            priority: Priority.high,
            ticker: 'ticker',
            fullScreenIntent: false);
    const DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails(presentSound: false //iOS声音和震动是一个整体，无法分别进行设置
            );
    NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails, iOS: darwinNotificationDetails);

    String? title = notiModel.title;
    title = title ?? '';
    if (title.characters.length > 50) {
      var text = title.getRange(0, 50);
      title = text;
    }

    String? msgContent = notiModel.body;
    msgContent = msgContent ?? '';
    if (msgContent.characters.length > 50) {
      var text = msgContent.getRange(0, 50);
      msgContent = text;
    }
    await _flutterLocalNotificationsPlugin.show(
        id++, title, msgContent, notificationDetails,
        payload: notiModel.payload);
    if (Platform.isIOS) {
      //与原生交互进行系统声音与震动的分别实现
      Channel().invoke(Channel_Notification_Sound_shock, {
        'sound': notiModel.sound ?? true,
        'vibrate': notiModel.vibrate ?? true
      });
    }
  }

  /// 隐藏/取消通知
  /// [notificationId] 通知的ID，如果为null则取消所有通知
  Future<void> hideNotification({int? notificationId}) async {
    if (notificationId != null) {
      await _flutterLocalNotificationsPlugin.cancel(notificationId);
    } else {
      await _flutterLocalNotificationsPlugin.cancelAll();
    }
  }
}
