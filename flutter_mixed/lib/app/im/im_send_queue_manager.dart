

import 'dart:collection';
import 'dart:io';

import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/sender/msg_sender_executor.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../common/cos/cos_manager.dart';
import '../utils/string-util.dart';
import 'constant/ImMsgConstant.dart';
import 'db/entity/session.dart';


// 消息发送管理器，主要处理 socket 超时 发送的问题
class ImSendQueueManager {

  static ImSendQueueManager? _instance;

  ImSendQueueManager._internal();

  static ImSendQueueManager get instance =>
      _instance ??= ImSendQueueManager._internal();


  // 开始发送消息的时候统一将消息入队列
  final msgMap = HashMap<String,Message>();

  void putMessage2Queue(String? cmdId) async{
    if(cmdId == null) return;
    var msg = await DbHelper.queryMsgByCmdId(cmdId);
    if(msg == null) return;
    msgMap.putIfAbsent(cmdId, () => msg);
  }

  validateMsgSendStatus() async {
    var uid = await UserHelper.getUid();
    var unSuccessList = await DbHelper.getMyUnSuccessMsgList(uid);
    // logger('历史未成功的消息 : ${unSuccessList.length}');
    if(unSuccessList.isEmpty) return;
    var nowTime = DateTime.now().millisecondsSinceEpoch;
    var recentList = unSuccessList.where((e) => ((nowTime - (e.sendTime ?? 0)) < 1000 * 60 * 60 *24)).toList();
    // logger('一天内的未成功的消息 : ${recentList.length}');
    await Future.forEach(recentList, (m) async {
      await ResendManager().reSendMsgWhenNetResumed(m);
    });
  }
}


class ResendManager {

  MessageSenderExecutor sendExecutor = MessageSenderExecutor();

  reSendMsgWhenNetResumed(Message message) async {
    try {
      // 执行重发逻辑
      await CosManager().initPans();

      if (message.isVideo()) {
        // 重发视频
        var videoPath = message.localUrl;
        if (!StringUtil.isEmpty(videoPath)) {
          await sendVideo([videoPath!], oldMessage: message);
        }
      } else if (message.isText()) {
        // 重发文本
        await sendText(message.text ?? '', oldMessage: message);
      } else if (message.isImage()) {
        // 重发图片
        var filePath = message.localUrl;
        if (filePath == null || filePath == '') return;
        var list = [filePath];
        await sendImage(list, oldMessage: message);
      } else if (message.isFile()) {
        // 重发文件
        var filePath = message.localUrl;
        if (filePath == null || filePath == '') return;
        await sendFile(File(filePath), oldMessage: message);
      } else if (message.isMsgRecord()) {
        // 重发聊天记录
        var newMsg = message;
        var session = await getSession(newMsg);
        if(session == null) return;
        await session.sendExtBySocket(newMsg.cmdId ?? '', newMsg);

      } else if (message.isVoice()) {
        // 重发语音
        await sendAudio('', Duration(seconds: 1), oldMessage: message);
      } else if (message.isLocation()) {
        // 重发地图
        await sendLocation(null, oldMessage: message);
      }
    } finally {
    }
  }

  Future sendLocation(TempLocationData? map, {Message? oldMessage}) async {
    var session = await getSession(oldMessage);
    if(session == null) return;
    var content = MessageLocationData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..sendLocation = map;
    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeMap,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendVideo(List<String> videoPaths, {Message? oldMessage}) async {
    var content = MessageVideoData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..videoPaths = videoPaths;

    var session = await getSession(oldMessage);
    if(session == null) return;
    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeVideo,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendText(String? text, {Message? oldMessage}) async {
    var session = await getSession(oldMessage);
    if(session == null) return;
    var content = MessageTextData(Message(''), '', '', text, false, MessageSendStatus.sending, '');
    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeText,
        oldMessage: oldMessage,
        session: session, content: content);
  }


  sendImage(List<String> imagePaths, {Message? oldMessage , bool? original =false}) async {
    var session = await getSession(oldMessage);
    if(session == null) return;
    var content = MessageImageData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..imagePaths = imagePaths
      ..original = original ?? false
    ;
    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeImage,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendFile(File file, {Message? oldMessage}) async {
    var session = await getSession(oldMessage);
    if(session == null) return;
    var content = MessageFileData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..path = file.path
    ;
    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeFile,
        oldMessage: oldMessage,
        session: session, content: content);
  }

  sendAudio(String path, Duration duration, {Message? oldMessage}) async {
    var session = await getSession(oldMessage);
    if(session == null) return;
    var content = MessageVoiceData(Message(''), '', '', '', false, MessageSendStatus.sending, '')
      ..path = path
      ..sendDuration = duration
    ;
    await sendExecutor.sendMessage(msgType: ConstantImMsgType.SSChatMessageTypeVoice,
        oldMessage: oldMessage,
        session: session, content: content);
  }


  Future<Session?> getSession(Message? message) async {
    var userId = await UserHelper.getUid();
    var sessionList = await DbHelper.getSessionListByOwnerId(userId);
    var _sessionList = sessionList.where((s) => s.sessionId == message?.sessionId).toList();
    if(_sessionList.isEmpty) return null;
    var s =  _sessionList.first;
    return s;
  }
}