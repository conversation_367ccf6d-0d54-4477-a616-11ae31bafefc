import 'dart:convert';
import 'dart:io';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/ext/im_time_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../../common/base_info/info.dart';
import '../../common/channel/channel.dart';
import '../request/entity/notice_detail_data.dart';
import 'notice_funtion_type.dart';

class ClickCardJump {
  static clickCard(OfflineNoticeItemResp itemBody) async {
    ClientNoticeMsg? noticeMsg = itemBody.noticeMsg;
    NoticeData? noticeData = noticeMsg?.getDataP();
    NoticeDataDetail? detailData = noticeData?.getDetail();
    int isHaveAuth = 0;
    if (itemBody.type == ConstantImMsgType.WorkMsgSessionType) {
      isHaveAuth =
          await ClickCardJump.isHaveOrgPower(noticeData?.companyId ?? '');
      if (isHaveAuth == -1) {
        toast('你已不是当前公司的员工，无法查看详情!');
        return;
      }
    }
    logger('===========clickcard==${itemBody.toJson()}');
    switch (noticeData?.functionType) {
      case NoticeFunctonType.DDJumpFunctionTypeLocalImDetail:
        Get.toNamed(
          Routes.SYSTEM_NOTICE,
          arguments: {
            'dataContent': noticeMsg?.context ?? '',
            'logoStr': noticeData?.companyLogo == ''
                ? noticeData?.iconUrl
                : noticeData?.companyLogo,
            'companyName': noticeData?.companyName,
            'dealLogoStr': detailData?.handlerAvatar ?? '',
            'dealName': detailData?.handlerName ?? '',
            'timeStr': itemBody.msgTime?.convertDate(),
          },
        );
        break;
      case NoticeFunctonType.DDJumpFunctionTypeFriendApply:
        if (detailData == null) return;
        Get.toNamed(
          Routes.FRIEND_APPLY_DETAIL,
          arguments: {'recordId': detailData.routeId, 'type': 0},
        );
        break;
      case NoticeFunctonType.DDJumpFunctionTypeTeamApply:
        if (detailData == null) return;
        Get.toNamed(
          Routes.FRIEND_APPLY_DETAIL,
          arguments: {
            'recordId': detailData.routeId,
            'type': 2,
            'companyId': noticeData?.companyId
          },
        );
        break;
      case NoticeFunctonType.DDJumpFunctionTypeExternalPerson:
        if (detailData == null) return;
        Get.toNamed(
          Routes.FRIEND_APPLY_DETAIL,
          arguments: {'recordId': detailData.routeId, 'type': 1},
        );
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAprovalDetail:
        if (detailData == null) return;
        Get.toNamed(Routes.APPROVE_DETAIL, arguments: {
          'approveId': detailData.routeId,
          'orgId': noticeData?.companyId
        });
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAprovalList:
        Get.toNamed(Routes.APPROVE_LIST,
            arguments: {'listType': 2, 'orgId': noticeData?.companyId});
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAttendanceClock:
        if (detailData == null) return;
        int needClock = detailData.needClock ?? 0;
        if (needClock < 1608566400000) {
          needClock = itemBody.msgTime ?? 0;
        }
        if (needClock == 0) return;
        Channel().invoke(Channel_Native_attendance, {
          'companyId': noticeData?.companyId,
          'companyName': noticeData?.companyName,
          'needClock': needClock
        });
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAttendanceRelu:
        if (detailData == null) return;
        Channel().invoke(
            Channel_Native_attendance_rule, {'routeId': detailData.routeId});
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAttendanceSpecialAdjust:
        if (detailData == null) return;
        Channel().invoke(Channel_Native_attendance_adjust, {
          'routeId': detailData.routeId,
          'companyId': noticeData?.companyId
        });
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAttendanceLegworkApprove:
        if (detailData == null) return;
        int needClock = detailData.needClock ?? 0;
        if (needClock < 1608566400000) {
          needClock = itemBody.msgTime ?? 0;
        }
        if (needClock == 0) return;
        Channel().invoke(Channel_Native_attendance, {
          'companyId': noticeData?.companyId,
          'companyName': noticeData?.companyName,
          'needClock': needClock,
          'jumpType': 2
        });
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAnnouncementDetail:
        if (detailData == null) return;
        Channel().invoke(Channel_Native_announce, {
          'type': 2,
          'isHaveAuth': isHaveAuth,
          'orgId': noticeData?.companyId,
          'noticeId': detailData.routeId
        });
        break;
      case NoticeFunctonType.DDJumpFunctionTypeAwardedPermissions:
        if (detailData == null) return;
        RouteHelper.route(Routes.ORG_PERMISSION_INFO,
            arguments: {'noticeMsg': noticeMsg});
        break;
      case NoticeFunctonType.DDJumpFunctionTypeWeb:
        if (detailData == null) return;
        if (detailData.link == null) return;
        webJump(detailData.link!, noticeData?.companyId ?? '');
        break;
      case NoticeFunctonType.DDJumpFunctionTypeWebWithNavigationBar:
        if (detailData == null) return;
        if (detailData.link == null) return;
        webJump(detailData.link!, noticeData?.companyId ?? '',
            isWebNavigation: 0);
        break;
      case NoticeFunctonType.DDJumpFunctionTypeWebWithParams:
        if (detailData == null) return;
        if (detailData.paramType != null && detailData.link != null) {
          if (detailData.paramType == 0) {
            //拼接金蝶token
            getKingDeeToken(detailData.link!);
          } else {
            webJump(detailData.link!, noticeData?.companyId ?? '',
                  isWebNavigation: 0);
          }
        }
        break;
      case NoticeFunctonType.DDJumpFunctionTypeFlutter:
        if (detailData == null) return;
        if (detailData.routeName != null) {
          Get.toNamed(detailData.routeName!, arguments: detailData.argument);
        }

        break;
      case NoticeFunctonType.DDJumpFunctionTypeKingdeeWebList:
        getKingDeeToken(noticeMsg?.ext);
        break;
      case NoticeFunctonType.DDJumpFunctionTypeKingdeeWebDetail:
        getKingDeeToken(noticeMsg?.ext);
        break;
      case NoticeFunctonType.DDJumpFunctionTypeVisitorWebList:
        Map<String, dynamic> headerMap = await UserDefault.getHttpHeader();
        String url =
            '${Host.WEBHOST}/visitor/index.html#/applyList?height=${DeviceUtils().top.value}&version=${headerMap['appVersion']}';
        webJump(url, noticeData?.companyId ?? '');
        break;
      case NoticeFunctonType.DDJumpFunctionTypeVisitorWebDetail:
        Map<String, dynamic> headerMap = await UserDefault.getHttpHeader();
        int status = 0;
        if (noticeMsg != null) {
          if (noticeMsg.tempStatus != null) {
            status = (noticeMsg.tempStatus! > 0 ? 1 : 0);
            String url =
                '${Host.WEBHOST}/visitor/index.html#/applyPage?height=${DeviceUtils().top.value}&version=${headerMap['appVersion']}&status=$status&visitorId=${detailData?.routeId}';
            webJump(url, noticeData?.companyId ?? '');
          }
        }

        break;
      default:
    }
  }

  static isHaveOrgPower(companyId) async {
    //-1 不在公司 0无组织权限 1有组织权限
    dynamic dataDic = await UserDefault.getData(Define.ORGLIST);
    if (dataDic == null) {
      return -1;
    }

    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }

    List allOrModelList = [];

    if (companies.isNotEmpty) {
      bool isHave = false;
      allOrModelList =
          companies.map((item) => OrgModel.fromJson(item)).toList();
      for (var i = 0; i < allOrModelList.length; i++) {
        OrgModel model = allOrModelList[i];
        if (model.companyId == companyId) {
          if (model.deptId == '0' ||
              model.power.contains('-1') ||
              model.power.contains('1')) {
            return 1;
          } else {
            return 0;
          }
        }
      }
      if (!isHave) {
        return -1;
      }
    } else {
      return -1;
    }
  }

  static getKingDeeToken(String? webUrl) async {
    if (webUrl == null) return;
    Get.loading();
    var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
    var resp = await wrokDatasource.getKingDeeToken();
    Get.dismiss();
    if (resp.success()) {
      if (resp.data == null) return;
      logger('======kingdee===${resp.data}');
      webJump(webUrl + resp.data, '', isWebNavigation: 0);
    } else {
      toast(resp.msg);
    }
  }

  static webJump(String url, String companyId,
      {int isWebNavigation = 1, title = '',int isCloseFront = 0}) async {
    //获取platform
    int platform = 1;
    if (Platform.isIOS) {
      platform = 2;
    }
    //获取appVersion
    String appVersion = await BaseInfo().getAppVersion();

    if (url.contains('?')) {
      url = '$url&platform=$platform&appVersion=$appVersion';
    } else {
      url = '$url?platform=$platform&appVersion=$appVersion';
    }

    var args =  {
      'url': url,
      'title': title,
      'isWebNavigation': isWebNavigation,
      'orgId': companyId,
      'isCloseFront':isCloseFront
    };

    openWebView(args);

  }
}
