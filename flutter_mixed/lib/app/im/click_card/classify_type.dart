class ClassifyType {
  static const int SSChatWorkNoticeClassifyAllWork = 0; // 全部类型
  static const int SSChatWorkNoticeClassifyAttendance =
      1; // 考勤提醒    20001 - 20099
  static const int SSChatWorkNoticeClassifyProject = 2; // 项目通知    20100 - 20199
  static const int SSChatWorkNoticeClassifyTask = 3; // 任务通知    20200 - 20299
  static const int SSChatWorkNoticeClassifyWorkReport =
      4; // 工作汇报    20300 - 20399
  static const int SSChatWorkNoticeClassifyAudioVideoMeeting =
      5; // 音/视频会议 20400 - 20499
  static const int SSChatWorkNoticeClassifyAnnouncement =
      6; // 公告提醒    20500 - 20599
  static const int SSChatWorkNoticeClassifyPermissionsChange =
      7; // 权限变更    20600 - 20699
  static const int SSChatWorkNoticeClassifyDigitalReport =
      8; // 企业数字报告 20700 - 20799
  static const int SSChatWorkNoticeClassifyCommentRemind =
      9; // 评论提醒     20800 - 20899
  static const int SSChatWorkNoticeClassifyCooperationMessage =
      10; // 协作消息    20900 - 20999
  // static const int SSChatWorkNoticeClassifyClassesRemind = 11; // 班次提醒
  static const int SSChatWorkNoticeClassifyRegimesnMessage =
      12;// 规章制度

  static List<Map<String, dynamic>> classsifyList = [
    {'classsifyType': SSChatWorkNoticeClassifyAllWork, 'name': '全部类型'},
    {'classsifyType': SSChatWorkNoticeClassifyAttendance, 'name': '考勤提醒'},
    {'classsifyType': SSChatWorkNoticeClassifyProject, 'name': '项目通知'},
    {'classsifyType': SSChatWorkNoticeClassifyTask, 'name': '任务通知'},
    {'classsifyType': SSChatWorkNoticeClassifyWorkReport, 'name': '工作汇报'},
    {
      'classsifyType': SSChatWorkNoticeClassifyAudioVideoMeeting,
      'name': '音/视频会议'
    },
    {'classsifyType': SSChatWorkNoticeClassifyAnnouncement, 'name': '公告提醒'},
    {
      'classsifyType': SSChatWorkNoticeClassifyPermissionsChange,
      'name': '权限变更'
    },
    {'classsifyType': SSChatWorkNoticeClassifyDigitalReport, 'name': '企业数字报告'},
    {'classsifyType': SSChatWorkNoticeClassifyCommentRemind, 'name': '评论提醒'},
    {
      'classsifyType': SSChatWorkNoticeClassifyCooperationMessage,
      'name': '协作消息'
    },
    {
      'classsifyType': SSChatWorkNoticeClassifyRegimesnMessage,
      'name': '规章制度'
    }
  ];
}
