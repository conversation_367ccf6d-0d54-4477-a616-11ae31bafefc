class NoticeFunctonType{
    static const int DDJumpFunctionTypeDefaultZero             = 0; // 默认0没有功能
    static const int DDJumpFunctionTypeFriendApply             = 1; // 1好友申请
    static const int DDJumpFunctionTypeTeamApply               = 2; // 2团队申请
    static const int DDJumpFunctionTypeExternalPerson          = 3; // 3外部协作人
    static const int DDJumpFunctionTypeExternalFolder          = 4; // 4外部协作文件夹WEB
    static const int DDJumpFunctionTypeLocalImDetail           = 5; // 5本地IM消息数据详情

      // 审批通知 100审批详情，101审批列表，
    static const int DDJumpFunctionTypeAprovalDetail           = 100; // 审批详情
    static const int DDJumpFunctionTypeAprovalList             = 101; // 我审批的列表
    
    // 工作通知 --考勤相关 200考勤打卡，201考勤规则，202特殊工作日调整
    static const int DDJumpFunctionTypeAttendanceClock         = 200;// 考勤打卡
    static const int DDJumpFunctionTypeAttendanceRelu          = 201; // 考勤规则
    static const int DDJumpFunctionTypeAttendanceSpecialAdjust = 202; // 特殊工作日调整
    static const int DDJumpFunctionTypeAttendanceLegworkApprove = 203; // 外勤审核
    static const int DDJumpFunctionTypeAnnouncementDetail = 204; // 公告详情
    

    /*已弃用*/
    // 项目相关
    static const int DDJumpFunctionTypeProjectAllTaskList      = 300; // 项目的全部任务列表
    
    // 任务相关
    static const int DDJumpFunctionTypeTaskDetail              = 400; // 任务的详情
    
    // 工作汇报 500有人提交了汇报-去汇报详情，501邀请提交汇报-去新建，502汇报提醒及汇报统计-去统计，503汇报评论
    static const int DDJumpFunctionTypeWorkSubmitReport         = 500; // 有人提交了汇报-去汇报详情
    static const int DDJumpFunctionTypeWorkInviteSubmitReport   = 501; // 邀请提交汇报-去新建
    static const int DDJumpFunctionTypeWorkReportRemindStatistics = 502; // 汇报提醒及汇报统计-去统计
    static const int DDJumpFunctionTypeWorkReportComment        = 503; // 汇报评论 去看评论
    /*已弃用*/



    
    // 权限变更
    static const int  DDJumpFunctionTypeAwardedPermissions      = 600; // 被授予权限-去看详情
    
    

    /*已弃用*/
    // 协作消息
    static const int DDJumpFunctionTypeCooperationMessage     = 700; // D1 邀请加入协作文件夹
    
    // 企业数字报告
    static const int DDJumpFunctionTypeEnterpriseNumberReport = 800; // 企业数字报告WEB
    /*已弃用*/



    
    //跳转指定web页面
    static const int DDJumpFunctionTypeWeb                    = 1000; //跳转web (跳转的url为detail字段里的link字段)
    static const int DDJumpFunctionTypeWebWithNavigationBar   = 1001; //跳转web (原生navigarionBar;跳转的url为detail字段里的link字段)
    static const int DDJumpFunctionTypeWebWithParams          = 1002; //跳转web 拼接参数detail字段paramType 0金蝶token1手机号
    static const int DDJumpFunctionTypeFlutter                = 1100; //跳转flutter
    // 金蝶审批
    static const int DDJumpFunctionTypeKingdeeWebList         = 3100; // 金蝶审批WEB列表
    static const int DDJumpFunctionTypeKingdeeWebDetail       = 3101; // 金蝶审批WEB详情
    static const int DDJumpFunctionTypeVisitorWebList         = 2100; // 访客审批WEB列表
    static const int DDJumpFunctionTypeVisitorWebDetail       = 2101; // 访客审批WEB详情
}