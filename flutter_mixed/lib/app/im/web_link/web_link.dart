

import 'dart:convert';
import 'dart:io';

import 'package:flutter_mixed/app/common/event/event.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/app/modules/webview/web_view_binding.dart';
import 'package:flutter_mixed/app/modules/webview/web_view_ctl.dart';
import 'package:flutter_mixed/app/modules/webview/web_view_page.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:mmkv/mmkv.dart';

import '../../common/channel/channel.dart';

Future cacheWhiteListData(String? warn , List<String?>? whiteList) async {
   var link  = WebLinkInfo(warn: warn, whiteList: whiteList);
   var j = jsonEncode(link.toJson());
   UserDefault.setData('whiteList', j);
}

Future<WebLinkInfo?> hasLimitWhiteList() async {
  try{
    var json = await UserDefault.getData('whiteList');
    if(StringUtil.isEmpty(json)) return null;
    var j = jsonDecode(json);
    return WebLinkInfo.fromJson(j);
  }catch(e){
    logger(e);
    return null;
  }
}


Future link2Web(String webUrl) async {
   var info = await hasLimitWhiteList();
   if(info == null || (info.whiteList ?? []).isEmpty) {
     direct2Web(webUrl);
     return;
   }

   bool validate = false;
   (info.whiteList ??[]).forEach((white){
     if(webUrl.contains(white ??'')){
       validate = true;
       return;
     }
   });

   if(validate) {
     direct2Web(webUrl);
     return;
   }

   RouteHelper.routePath(Routes.WEB_TIP_LINK , arguments: {
     'warn': info.warn ??'',
     'url' : webUrl
   });

}

Future direct2Web(String webUrl , {bool? disposeLat = false}) async {
  openWebView({
    'url': webUrl,
    'title': '',
    'isWebNavigation': 0,
  } , disposeLast: disposeLat);
}

var kingdee = 'kingdee.ddbes.com';
var kdeascloud = 'mbos.kdeascloud.com';

Future openWebView(Map? param , {bool? disposeLast = false}) async {
  if(param == null) return;
  logger('openWebView ==> $param');
  if(disposeLast == true){
    return await RouteHelper.offRoutePath(Routes.WEB_VIEW , arguments: param,preventDuplicates: false);
  }

  if(param['url'].contains(kingdee) || param['url'].contains(kdeascloud)){
    Channel.instance.invoke(Channel_openWebView , param);
    return;
  }
  return await RouteHelper.routePath(Routes.WEB_VIEW , arguments: param,preventDuplicates: false);
}



class WebLinkInfo {

  String? warn = '';
  List<String?>? whiteList = [];

  WebLinkInfo({this.warn, this.whiteList});

  factory WebLinkInfo.fromJson(Map<String, dynamic> json) {
    return WebLinkInfo(
      warn: json['warn'],
      whiteList: json['whiteList'] != null
          ? List<String?>.from(json['whiteList'].map((e) => e?.toString()))
          : null,);
  }

  Map<String, dynamic> toJson() => {'warn': warn, 'whiteList': whiteList,};

  @override
  String toString() {
    return 'WebLinkInfo{warn: $warn, whiteList: $whiteList}';
  }
}