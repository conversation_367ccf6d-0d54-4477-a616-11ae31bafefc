import 'dart:io';

import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:get/get.dart';

class WebTipLinkController extends GetxController {

  String? warn;
  String? url;

  @override
  void onInit() async {
    super.onInit();
    var argument = Get.arguments;
    warn = argument['warn'];
    url = argument['url'];
  }

  route2Web() {
    direct2Web(url ??'' , disposeLat: true);
  }

  @override
  void onClose() {
    super.onClose();
  }

}
