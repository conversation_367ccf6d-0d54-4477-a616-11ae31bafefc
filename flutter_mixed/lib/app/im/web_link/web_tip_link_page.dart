

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/web_link/web_tip_link_controller.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:get/get.dart';


class WebTipLinkPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<WebTipLinkController>(builder: (controller){
      return ToolBar(
        title: '',
        body: Container(
          
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              10.gap,
              Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    color: Colors.white,
                    child: Column(
                      children: [
                        70.gap,
                        Image.asset(AssetsRes.IC_TANHAO , width: 60 ),
                        30.gap,
                        Text('提示', style: TextStyle(fontSize: 18 , color: ColorConfig.mainTextColor),),
                        20.gap,
                        Text('${controller.warn}', style: TextStyle(fontSize: 14 , color: ColorConfig.subTitleTextColor),),
                      ],
                    ),
                    
                  )),

              Expanded(child: Container(
                color: Color(0xfff5f6f7),
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    30.gap,
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(child: MaterialButton(
                          onPressed: (){
                              controller.route2Web();
                        },
                          padding: EdgeInsets.symmetric(vertical: 10),
                        color: Color(0xff094AEC),
                          child: Text('继续访问' , style: TextStyle(fontSize: 16 ,color: Colors.white),),)
                        ),
                      ],
                    ),
                    12.gap,
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(child: MaterialButton(
                          onPressed: (){
                                Get.back();
                          },
                          padding: EdgeInsets.symmetric(vertical: 10),
                          child: Text('返回' , style: TextStyle(fontSize: 16 ,color: ColorConfig.mainTextColor),),
                          color: Colors.white,)
                        ),
                      ],
                    )
                  ],
                ),
              ))

            ],
          ),
        ),
      );
    });
  }

}