//
//  Generated code. Do not modify.
//  source: example/Msg.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'any.pb.dart' as $0;
import 'Msg.pbenum.dart';

export 'Msg.pbenum.dart';

enum ImMsg_Content {
  exception, 
  loginRequest, 
  loginResult, 
  c2cMsg, 
  c2cMsgRequest, 
  c2cMsgResponse, 
  groupMsg, 
  groupMsgRequest, 
  groupMsgResponse, 
  pushMsg, 
  serverMsg, 
  noticeMsg, 
  ext, 
  offline, 
  offlineMsgRequest, 
  kick, 
  imPushMsg, 
  notSet
}

class ImMsg extends $pb.GeneratedMessage {
  factory ImMsg({
    $core.String? msgId,
    $core.String? cmdId,
    Exception? exception,
    LoginRequest? loginRequest,
    LoginResult? loginResult,
    C2CMsg? c2cMsg,
    C2CMsgRequest? c2cMsgRequest,
    C2CMsgResponse? c2cMsgResponse,
    GroupMsg? groupMsg,
    GroupMsgRequest? groupMsgRequest,
    GroupMsgResponse? groupMsgResponse,
    PushMsg? pushMsg,
    ServerMsg? serverMsg,
    NoticeMsg? noticeMsg,
    ExtMsg? ext,
    $core.int? type,
    UserInfo? senderInfo,
    $fixnum.Int64? msgTime,
    MultipleMsg? offline,
    OfflineMsgRequest? offlineMsgRequest,
    $core.bool? recordIgnore,
    $core.String? msgContent,
    KickMsg? kick,
    ImPushMsg? imPushMsg,
    $core.bool? selfMsg,
    $core.String? customMessage,
  }) {
    final $result = create();
    if (msgId != null) {
      $result.msgId = msgId;
    }
    if (cmdId != null) {
      $result.cmdId = cmdId;
    }
    if (exception != null) {
      $result.exception = exception;
    }
    if (loginRequest != null) {
      $result.loginRequest = loginRequest;
    }
    if (loginResult != null) {
      $result.loginResult = loginResult;
    }
    if (c2cMsg != null) {
      $result.c2cMsg = c2cMsg;
    }
    if (c2cMsgRequest != null) {
      $result.c2cMsgRequest = c2cMsgRequest;
    }
    if (c2cMsgResponse != null) {
      $result.c2cMsgResponse = c2cMsgResponse;
    }
    if (groupMsg != null) {
      $result.groupMsg = groupMsg;
    }
    if (groupMsgRequest != null) {
      $result.groupMsgRequest = groupMsgRequest;
    }
    if (groupMsgResponse != null) {
      $result.groupMsgResponse = groupMsgResponse;
    }
    if (pushMsg != null) {
      $result.pushMsg = pushMsg;
    }
    if (serverMsg != null) {
      $result.serverMsg = serverMsg;
    }
    if (noticeMsg != null) {
      $result.noticeMsg = noticeMsg;
    }
    if (ext != null) {
      $result.ext = ext;
    }
    if (type != null) {
      $result.type = type;
    }
    if (senderInfo != null) {
      $result.senderInfo = senderInfo;
    }
    if (msgTime != null) {
      $result.msgTime = msgTime;
    }
    if (offline != null) {
      $result.offline = offline;
    }
    if (offlineMsgRequest != null) {
      $result.offlineMsgRequest = offlineMsgRequest;
    }
    if (recordIgnore != null) {
      $result.recordIgnore = recordIgnore;
    }
    if (msgContent != null) {
      $result.msgContent = msgContent;
    }
    if (kick != null) {
      $result.kick = kick;
    }
    if (imPushMsg != null) {
      $result.imPushMsg = imPushMsg;
    }
    if (selfMsg != null) {
      $result.selfMsg = selfMsg;
    }
    if (customMessage != null) {
      $result.customMessage = customMessage;
    }
    return $result;
  }
  ImMsg._() : super();
  factory ImMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ImMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, ImMsg_Content> _ImMsg_ContentByTag = {
    3 : ImMsg_Content.exception,
    4 : ImMsg_Content.loginRequest,
    5 : ImMsg_Content.loginResult,
    6 : ImMsg_Content.c2cMsg,
    7 : ImMsg_Content.c2cMsgRequest,
    8 : ImMsg_Content.c2cMsgResponse,
    9 : ImMsg_Content.groupMsg,
    10 : ImMsg_Content.groupMsgRequest,
    11 : ImMsg_Content.groupMsgResponse,
    12 : ImMsg_Content.pushMsg,
    13 : ImMsg_Content.serverMsg,
    14 : ImMsg_Content.noticeMsg,
    15 : ImMsg_Content.ext,
    19 : ImMsg_Content.offline,
    20 : ImMsg_Content.offlineMsgRequest,
    23 : ImMsg_Content.kick,
    24 : ImMsg_Content.imPushMsg,
    0 : ImMsg_Content.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ImMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 19, 20, 23, 24])
    ..aOS(1, _omitFieldNames ? '' : 'msgId', protoName: 'msgId')
    ..aOS(2, _omitFieldNames ? '' : 'cmdId', protoName: 'cmdId')
    ..aOM<Exception>(3, _omitFieldNames ? '' : 'exception', subBuilder: Exception.create)
    ..aOM<LoginRequest>(4, _omitFieldNames ? '' : 'loginRequest', protoName: 'loginRequest', subBuilder: LoginRequest.create)
    ..aOM<LoginResult>(5, _omitFieldNames ? '' : 'loginResult', protoName: 'loginResult', subBuilder: LoginResult.create)
    ..aOM<C2CMsg>(6, _omitFieldNames ? '' : 'c2cMsg', protoName: 'c2cMsg', subBuilder: C2CMsg.create)
    ..aOM<C2CMsgRequest>(7, _omitFieldNames ? '' : 'c2cMsgRequest', protoName: 'c2cMsgRequest', subBuilder: C2CMsgRequest.create)
    ..aOM<C2CMsgResponse>(8, _omitFieldNames ? '' : 'c2cMsgResponse', protoName: 'c2cMsgResponse', subBuilder: C2CMsgResponse.create)
    ..aOM<GroupMsg>(9, _omitFieldNames ? '' : 'groupMsg', protoName: 'groupMsg', subBuilder: GroupMsg.create)
    ..aOM<GroupMsgRequest>(10, _omitFieldNames ? '' : 'groupMsgRequest', protoName: 'groupMsgRequest', subBuilder: GroupMsgRequest.create)
    ..aOM<GroupMsgResponse>(11, _omitFieldNames ? '' : 'groupMsgResponse', protoName: 'groupMsgResponse', subBuilder: GroupMsgResponse.create)
    ..aOM<PushMsg>(12, _omitFieldNames ? '' : 'pushMsg', protoName: 'pushMsg', subBuilder: PushMsg.create)
    ..aOM<ServerMsg>(13, _omitFieldNames ? '' : 'serverMsg', protoName: 'serverMsg', subBuilder: ServerMsg.create)
    ..aOM<NoticeMsg>(14, _omitFieldNames ? '' : 'noticeMsg', protoName: 'noticeMsg', subBuilder: NoticeMsg.create)
    ..aOM<ExtMsg>(15, _omitFieldNames ? '' : 'ext', subBuilder: ExtMsg.create)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'type', $pb.PbFieldType.O3)
    ..aOM<UserInfo>(17, _omitFieldNames ? '' : 'senderInfo', protoName: 'senderInfo', subBuilder: UserInfo.create)
    ..aInt64(18, _omitFieldNames ? '' : 'msgTime', protoName: 'msgTime')
    ..aOM<MultipleMsg>(19, _omitFieldNames ? '' : 'offline', subBuilder: MultipleMsg.create)
    ..aOM<OfflineMsgRequest>(20, _omitFieldNames ? '' : 'offlineMsgRequest', protoName: 'offlineMsgRequest', subBuilder: OfflineMsgRequest.create)
    ..aOB(21, _omitFieldNames ? '' : 'recordIgnore', protoName: 'recordIgnore')
    ..aOS(22, _omitFieldNames ? '' : 'msgContent', protoName: 'msgContent')
    ..aOM<KickMsg>(23, _omitFieldNames ? '' : 'kick', subBuilder: KickMsg.create)
    ..aOM<ImPushMsg>(24, _omitFieldNames ? '' : 'imPushMsg', protoName: 'imPushMsg', subBuilder: ImPushMsg.create)
    ..aOB(25, _omitFieldNames ? '' : 'selfMsg', protoName: 'selfMsg')
    ..aOS(26, _omitFieldNames ? '' : 'customMessage', protoName: 'customMessage')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ImMsg clone() => ImMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ImMsg copyWith(void Function(ImMsg) updates) => super.copyWith((message) => updates(message as ImMsg)) as ImMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ImMsg create() => ImMsg._();
  ImMsg createEmptyInstance() => create();
  static $pb.PbList<ImMsg> createRepeated() => $pb.PbList<ImMsg>();
  @$core.pragma('dart2js:noInline')
  static ImMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ImMsg>(create);
  static ImMsg? _defaultInstance;

  ImMsg_Content whichContent() => _ImMsg_ContentByTag[$_whichOneof(0)]!;
  void clearContent() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get msgId => $_getSZ(0);
  @$pb.TagNumber(1)
  set msgId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsgId() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsgId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get cmdId => $_getSZ(1);
  @$pb.TagNumber(2)
  set cmdId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCmdId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCmdId() => clearField(2);

  @$pb.TagNumber(3)
  Exception get exception => $_getN(2);
  @$pb.TagNumber(3)
  set exception(Exception v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasException() => $_has(2);
  @$pb.TagNumber(3)
  void clearException() => clearField(3);
  @$pb.TagNumber(3)
  Exception ensureException() => $_ensure(2);

  @$pb.TagNumber(4)
  LoginRequest get loginRequest => $_getN(3);
  @$pb.TagNumber(4)
  set loginRequest(LoginRequest v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasLoginRequest() => $_has(3);
  @$pb.TagNumber(4)
  void clearLoginRequest() => clearField(4);
  @$pb.TagNumber(4)
  LoginRequest ensureLoginRequest() => $_ensure(3);

  @$pb.TagNumber(5)
  LoginResult get loginResult => $_getN(4);
  @$pb.TagNumber(5)
  set loginResult(LoginResult v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasLoginResult() => $_has(4);
  @$pb.TagNumber(5)
  void clearLoginResult() => clearField(5);
  @$pb.TagNumber(5)
  LoginResult ensureLoginResult() => $_ensure(4);

  @$pb.TagNumber(6)
  C2CMsg get c2cMsg => $_getN(5);
  @$pb.TagNumber(6)
  set c2cMsg(C2CMsg v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasC2cMsg() => $_has(5);
  @$pb.TagNumber(6)
  void clearC2cMsg() => clearField(6);
  @$pb.TagNumber(6)
  C2CMsg ensureC2cMsg() => $_ensure(5);

  @$pb.TagNumber(7)
  C2CMsgRequest get c2cMsgRequest => $_getN(6);
  @$pb.TagNumber(7)
  set c2cMsgRequest(C2CMsgRequest v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasC2cMsgRequest() => $_has(6);
  @$pb.TagNumber(7)
  void clearC2cMsgRequest() => clearField(7);
  @$pb.TagNumber(7)
  C2CMsgRequest ensureC2cMsgRequest() => $_ensure(6);

  @$pb.TagNumber(8)
  C2CMsgResponse get c2cMsgResponse => $_getN(7);
  @$pb.TagNumber(8)
  set c2cMsgResponse(C2CMsgResponse v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasC2cMsgResponse() => $_has(7);
  @$pb.TagNumber(8)
  void clearC2cMsgResponse() => clearField(8);
  @$pb.TagNumber(8)
  C2CMsgResponse ensureC2cMsgResponse() => $_ensure(7);

  @$pb.TagNumber(9)
  GroupMsg get groupMsg => $_getN(8);
  @$pb.TagNumber(9)
  set groupMsg(GroupMsg v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasGroupMsg() => $_has(8);
  @$pb.TagNumber(9)
  void clearGroupMsg() => clearField(9);
  @$pb.TagNumber(9)
  GroupMsg ensureGroupMsg() => $_ensure(8);

  @$pb.TagNumber(10)
  GroupMsgRequest get groupMsgRequest => $_getN(9);
  @$pb.TagNumber(10)
  set groupMsgRequest(GroupMsgRequest v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasGroupMsgRequest() => $_has(9);
  @$pb.TagNumber(10)
  void clearGroupMsgRequest() => clearField(10);
  @$pb.TagNumber(10)
  GroupMsgRequest ensureGroupMsgRequest() => $_ensure(9);

  @$pb.TagNumber(11)
  GroupMsgResponse get groupMsgResponse => $_getN(10);
  @$pb.TagNumber(11)
  set groupMsgResponse(GroupMsgResponse v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasGroupMsgResponse() => $_has(10);
  @$pb.TagNumber(11)
  void clearGroupMsgResponse() => clearField(11);
  @$pb.TagNumber(11)
  GroupMsgResponse ensureGroupMsgResponse() => $_ensure(10);

  @$pb.TagNumber(12)
  PushMsg get pushMsg => $_getN(11);
  @$pb.TagNumber(12)
  set pushMsg(PushMsg v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasPushMsg() => $_has(11);
  @$pb.TagNumber(12)
  void clearPushMsg() => clearField(12);
  @$pb.TagNumber(12)
  PushMsg ensurePushMsg() => $_ensure(11);

  @$pb.TagNumber(13)
  ServerMsg get serverMsg => $_getN(12);
  @$pb.TagNumber(13)
  set serverMsg(ServerMsg v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasServerMsg() => $_has(12);
  @$pb.TagNumber(13)
  void clearServerMsg() => clearField(13);
  @$pb.TagNumber(13)
  ServerMsg ensureServerMsg() => $_ensure(12);

  @$pb.TagNumber(14)
  NoticeMsg get noticeMsg => $_getN(13);
  @$pb.TagNumber(14)
  set noticeMsg(NoticeMsg v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasNoticeMsg() => $_has(13);
  @$pb.TagNumber(14)
  void clearNoticeMsg() => clearField(14);
  @$pb.TagNumber(14)
  NoticeMsg ensureNoticeMsg() => $_ensure(13);

  @$pb.TagNumber(15)
  ExtMsg get ext => $_getN(14);
  @$pb.TagNumber(15)
  set ext(ExtMsg v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasExt() => $_has(14);
  @$pb.TagNumber(15)
  void clearExt() => clearField(15);
  @$pb.TagNumber(15)
  ExtMsg ensureExt() => $_ensure(14);

  @$pb.TagNumber(16)
  $core.int get type => $_getIZ(15);
  @$pb.TagNumber(16)
  set type($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasType() => $_has(15);
  @$pb.TagNumber(16)
  void clearType() => clearField(16);

  @$pb.TagNumber(17)
  UserInfo get senderInfo => $_getN(16);
  @$pb.TagNumber(17)
  set senderInfo(UserInfo v) { setField(17, v); }
  @$pb.TagNumber(17)
  $core.bool hasSenderInfo() => $_has(16);
  @$pb.TagNumber(17)
  void clearSenderInfo() => clearField(17);
  @$pb.TagNumber(17)
  UserInfo ensureSenderInfo() => $_ensure(16);

  @$pb.TagNumber(18)
  $fixnum.Int64 get msgTime => $_getI64(17);
  @$pb.TagNumber(18)
  set msgTime($fixnum.Int64 v) { $_setInt64(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasMsgTime() => $_has(17);
  @$pb.TagNumber(18)
  void clearMsgTime() => clearField(18);

  @$pb.TagNumber(19)
  MultipleMsg get offline => $_getN(18);
  @$pb.TagNumber(19)
  set offline(MultipleMsg v) { setField(19, v); }
  @$pb.TagNumber(19)
  $core.bool hasOffline() => $_has(18);
  @$pb.TagNumber(19)
  void clearOffline() => clearField(19);
  @$pb.TagNumber(19)
  MultipleMsg ensureOffline() => $_ensure(18);

  @$pb.TagNumber(20)
  OfflineMsgRequest get offlineMsgRequest => $_getN(19);
  @$pb.TagNumber(20)
  set offlineMsgRequest(OfflineMsgRequest v) { setField(20, v); }
  @$pb.TagNumber(20)
  $core.bool hasOfflineMsgRequest() => $_has(19);
  @$pb.TagNumber(20)
  void clearOfflineMsgRequest() => clearField(20);
  @$pb.TagNumber(20)
  OfflineMsgRequest ensureOfflineMsgRequest() => $_ensure(19);

  @$pb.TagNumber(21)
  $core.bool get recordIgnore => $_getBF(20);
  @$pb.TagNumber(21)
  set recordIgnore($core.bool v) { $_setBool(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasRecordIgnore() => $_has(20);
  @$pb.TagNumber(21)
  void clearRecordIgnore() => clearField(21);

  @$pb.TagNumber(22)
  $core.String get msgContent => $_getSZ(21);
  @$pb.TagNumber(22)
  set msgContent($core.String v) { $_setString(21, v); }
  @$pb.TagNumber(22)
  $core.bool hasMsgContent() => $_has(21);
  @$pb.TagNumber(22)
  void clearMsgContent() => clearField(22);

  @$pb.TagNumber(23)
  KickMsg get kick => $_getN(22);
  @$pb.TagNumber(23)
  set kick(KickMsg v) { setField(23, v); }
  @$pb.TagNumber(23)
  $core.bool hasKick() => $_has(22);
  @$pb.TagNumber(23)
  void clearKick() => clearField(23);
  @$pb.TagNumber(23)
  KickMsg ensureKick() => $_ensure(22);

  @$pb.TagNumber(24)
  ImPushMsg get imPushMsg => $_getN(23);
  @$pb.TagNumber(24)
  set imPushMsg(ImPushMsg v) { setField(24, v); }
  @$pb.TagNumber(24)
  $core.bool hasImPushMsg() => $_has(23);
  @$pb.TagNumber(24)
  void clearImPushMsg() => clearField(24);
  @$pb.TagNumber(24)
  ImPushMsg ensureImPushMsg() => $_ensure(23);

  @$pb.TagNumber(25)
  $core.bool get selfMsg => $_getBF(24);
  @$pb.TagNumber(25)
  set selfMsg($core.bool v) { $_setBool(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasSelfMsg() => $_has(24);
  @$pb.TagNumber(25)
  void clearSelfMsg() => clearField(25);

  /// 自定义消息
  @$pb.TagNumber(26)
  $core.String get customMessage => $_getSZ(25);
  @$pb.TagNumber(26)
  set customMessage($core.String v) { $_setString(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasCustomMessage() => $_has(25);
  @$pb.TagNumber(26)
  void clearCustomMessage() => clearField(26);
}

enum ImPushMsg_Content {
  blacklistAdd, 
  blacklistRemove, 
  singleMuteListAdd, 
  singleMuteListRemove, 
  groupMuteListAdd, 
  groupMuteListRemove, 
  warnMessage, 
  notSet
}

class ImPushMsg extends $pb.GeneratedMessage {
  factory ImPushMsg({
    $core.String? blacklistAdd,
    $core.String? blacklistRemove,
    $core.String? singleMuteListAdd,
    $core.String? singleMuteListRemove,
    $core.String? groupMuteListAdd,
    $core.String? groupMuteListRemove,
    WarnMessage? warnMessage,
  }) {
    final $result = create();
    if (blacklistAdd != null) {
      $result.blacklistAdd = blacklistAdd;
    }
    if (blacklistRemove != null) {
      $result.blacklistRemove = blacklistRemove;
    }
    if (singleMuteListAdd != null) {
      $result.singleMuteListAdd = singleMuteListAdd;
    }
    if (singleMuteListRemove != null) {
      $result.singleMuteListRemove = singleMuteListRemove;
    }
    if (groupMuteListAdd != null) {
      $result.groupMuteListAdd = groupMuteListAdd;
    }
    if (groupMuteListRemove != null) {
      $result.groupMuteListRemove = groupMuteListRemove;
    }
    if (warnMessage != null) {
      $result.warnMessage = warnMessage;
    }
    return $result;
  }
  ImPushMsg._() : super();
  factory ImPushMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ImPushMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, ImPushMsg_Content> _ImPushMsg_ContentByTag = {
    1 : ImPushMsg_Content.blacklistAdd,
    2 : ImPushMsg_Content.blacklistRemove,
    3 : ImPushMsg_Content.singleMuteListAdd,
    4 : ImPushMsg_Content.singleMuteListRemove,
    5 : ImPushMsg_Content.groupMuteListAdd,
    6 : ImPushMsg_Content.groupMuteListRemove,
    7 : ImPushMsg_Content.warnMessage,
    0 : ImPushMsg_Content.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ImPushMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3, 4, 5, 6, 7])
    ..aOS(1, _omitFieldNames ? '' : 'blacklistAdd', protoName: 'blacklistAdd')
    ..aOS(2, _omitFieldNames ? '' : 'blacklistRemove', protoName: 'blacklistRemove')
    ..aOS(3, _omitFieldNames ? '' : 'singleMuteListAdd', protoName: 'singleMuteListAdd')
    ..aOS(4, _omitFieldNames ? '' : 'singleMuteListRemove', protoName: 'singleMuteListRemove')
    ..aOS(5, _omitFieldNames ? '' : 'groupMuteListAdd', protoName: 'groupMuteListAdd')
    ..aOS(6, _omitFieldNames ? '' : 'groupMuteListRemove', protoName: 'groupMuteListRemove')
    ..aOM<WarnMessage>(7, _omitFieldNames ? '' : 'warnMessage', protoName: 'warnMessage', subBuilder: WarnMessage.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ImPushMsg clone() => ImPushMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ImPushMsg copyWith(void Function(ImPushMsg) updates) => super.copyWith((message) => updates(message as ImPushMsg)) as ImPushMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ImPushMsg create() => ImPushMsg._();
  ImPushMsg createEmptyInstance() => create();
  static $pb.PbList<ImPushMsg> createRepeated() => $pb.PbList<ImPushMsg>();
  @$core.pragma('dart2js:noInline')
  static ImPushMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ImPushMsg>(create);
  static ImPushMsg? _defaultInstance;

  ImPushMsg_Content whichContent() => _ImPushMsg_ContentByTag[$_whichOneof(0)]!;
  void clearContent() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get blacklistAdd => $_getSZ(0);
  @$pb.TagNumber(1)
  set blacklistAdd($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBlacklistAdd() => $_has(0);
  @$pb.TagNumber(1)
  void clearBlacklistAdd() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get blacklistRemove => $_getSZ(1);
  @$pb.TagNumber(2)
  set blacklistRemove($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBlacklistRemove() => $_has(1);
  @$pb.TagNumber(2)
  void clearBlacklistRemove() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get singleMuteListAdd => $_getSZ(2);
  @$pb.TagNumber(3)
  set singleMuteListAdd($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSingleMuteListAdd() => $_has(2);
  @$pb.TagNumber(3)
  void clearSingleMuteListAdd() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get singleMuteListRemove => $_getSZ(3);
  @$pb.TagNumber(4)
  set singleMuteListRemove($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSingleMuteListRemove() => $_has(3);
  @$pb.TagNumber(4)
  void clearSingleMuteListRemove() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get groupMuteListAdd => $_getSZ(4);
  @$pb.TagNumber(5)
  set groupMuteListAdd($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGroupMuteListAdd() => $_has(4);
  @$pb.TagNumber(5)
  void clearGroupMuteListAdd() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get groupMuteListRemove => $_getSZ(5);
  @$pb.TagNumber(6)
  set groupMuteListRemove($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasGroupMuteListRemove() => $_has(5);
  @$pb.TagNumber(6)
  void clearGroupMuteListRemove() => clearField(6);

  @$pb.TagNumber(7)
  WarnMessage get warnMessage => $_getN(6);
  @$pb.TagNumber(7)
  set warnMessage(WarnMessage v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasWarnMessage() => $_has(6);
  @$pb.TagNumber(7)
  void clearWarnMessage() => clearField(7);
  @$pb.TagNumber(7)
  WarnMessage ensureWarnMessage() => $_ensure(6);
}

class WarnMessage extends $pb.GeneratedMessage {
  factory WarnMessage({
    $core.String? msg,
    $core.String? sessionId,
    WarnMessage_SessionType? sessionType,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (sessionId != null) {
      $result.sessionId = sessionId;
    }
    if (sessionType != null) {
      $result.sessionType = sessionType;
    }
    return $result;
  }
  WarnMessage._() : super();
  factory WarnMessage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WarnMessage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'WarnMessage', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'msg')
    ..aOS(2, _omitFieldNames ? '' : 'sessionId', protoName: 'sessionId')
    ..e<WarnMessage_SessionType>(3, _omitFieldNames ? '' : 'sessionType', $pb.PbFieldType.OE, protoName: 'sessionType', defaultOrMaker: WarnMessage_SessionType.NULL, valueOf: WarnMessage_SessionType.valueOf, enumValues: WarnMessage_SessionType.values)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WarnMessage clone() => WarnMessage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WarnMessage copyWith(void Function(WarnMessage) updates) => super.copyWith((message) => updates(message as WarnMessage)) as WarnMessage;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static WarnMessage create() => WarnMessage._();
  WarnMessage createEmptyInstance() => create();
  static $pb.PbList<WarnMessage> createRepeated() => $pb.PbList<WarnMessage>();
  @$core.pragma('dart2js:noInline')
  static WarnMessage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WarnMessage>(create);
  static WarnMessage? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get msg => $_getSZ(0);
  @$pb.TagNumber(1)
  set msg($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get sessionId => $_getSZ(1);
  @$pb.TagNumber(2)
  set sessionId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSessionId() => $_has(1);
  @$pb.TagNumber(2)
  void clearSessionId() => clearField(2);

  @$pb.TagNumber(3)
  WarnMessage_SessionType get sessionType => $_getN(2);
  @$pb.TagNumber(3)
  set sessionType(WarnMessage_SessionType v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasSessionType() => $_has(2);
  @$pb.TagNumber(3)
  void clearSessionType() => clearField(3);
}

class KickMsg extends $pb.GeneratedMessage {
  factory KickMsg({
    KickMsg_KickType? kickType,
  }) {
    final $result = create();
    if (kickType != null) {
      $result.kickType = kickType;
    }
    return $result;
  }
  KickMsg._() : super();
  factory KickMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory KickMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'KickMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..e<KickMsg_KickType>(1, _omitFieldNames ? '' : 'kickType', $pb.PbFieldType.OE, protoName: 'kickType', defaultOrMaker: KickMsg_KickType.NULL, valueOf: KickMsg_KickType.valueOf, enumValues: KickMsg_KickType.values)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  KickMsg clone() => KickMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  KickMsg copyWith(void Function(KickMsg) updates) => super.copyWith((message) => updates(message as KickMsg)) as KickMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static KickMsg create() => KickMsg._();
  KickMsg createEmptyInstance() => create();
  static $pb.PbList<KickMsg> createRepeated() => $pb.PbList<KickMsg>();
  @$core.pragma('dart2js:noInline')
  static KickMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<KickMsg>(create);
  static KickMsg? _defaultInstance;

  @$pb.TagNumber(1)
  KickMsg_KickType get kickType => $_getN(0);
  @$pb.TagNumber(1)
  set kickType(KickMsg_KickType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasKickType() => $_has(0);
  @$pb.TagNumber(1)
  void clearKickType() => clearField(1);
}

class OfflineMsgRequest extends $pb.GeneratedMessage {
  factory OfflineMsgRequest({
    $fixnum.Int64? offlineTime,
    $core.Iterable<$core.String>? targetUserId,
    $core.Iterable<$core.String>? groupId,
  }) {
    final $result = create();
    if (offlineTime != null) {
      $result.offlineTime = offlineTime;
    }
    if (targetUserId != null) {
      $result.targetUserId.addAll(targetUserId);
    }
    if (groupId != null) {
      $result.groupId.addAll(groupId);
    }
    return $result;
  }
  OfflineMsgRequest._() : super();
  factory OfflineMsgRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory OfflineMsgRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'OfflineMsgRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'offlineTime', protoName: 'offlineTime')
    ..pPS(2, _omitFieldNames ? '' : 'targetUserId', protoName: 'targetUserId')
    ..pPS(3, _omitFieldNames ? '' : 'groupId', protoName: 'groupId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  OfflineMsgRequest clone() => OfflineMsgRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  OfflineMsgRequest copyWith(void Function(OfflineMsgRequest) updates) => super.copyWith((message) => updates(message as OfflineMsgRequest)) as OfflineMsgRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static OfflineMsgRequest create() => OfflineMsgRequest._();
  OfflineMsgRequest createEmptyInstance() => create();
  static $pb.PbList<OfflineMsgRequest> createRepeated() => $pb.PbList<OfflineMsgRequest>();
  @$core.pragma('dart2js:noInline')
  static OfflineMsgRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<OfflineMsgRequest>(create);
  static OfflineMsgRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get offlineTime => $_getI64(0);
  @$pb.TagNumber(1)
  set offlineTime($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasOfflineTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearOfflineTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get targetUserId => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<$core.String> get groupId => $_getList(2);
}

class MultipleMsg extends $pb.GeneratedMessage {
  factory MultipleMsg({
    $core.Iterable<ImMsg>? msg,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg.addAll(msg);
    }
    return $result;
  }
  MultipleMsg._() : super();
  factory MultipleMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MultipleMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'MultipleMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..pc<ImMsg>(1, _omitFieldNames ? '' : 'msg', $pb.PbFieldType.PM, subBuilder: ImMsg.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MultipleMsg clone() => MultipleMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MultipleMsg copyWith(void Function(MultipleMsg) updates) => super.copyWith((message) => updates(message as MultipleMsg)) as MultipleMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static MultipleMsg create() => MultipleMsg._();
  MultipleMsg createEmptyInstance() => create();
  static $pb.PbList<MultipleMsg> createRepeated() => $pb.PbList<MultipleMsg>();
  @$core.pragma('dart2js:noInline')
  static MultipleMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MultipleMsg>(create);
  static MultipleMsg? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ImMsg> get msg => $_getList(0);
}

/// 通知消息
class NoticeMsg extends $pb.GeneratedMessage {
  factory NoticeMsg({
    $core.String? title,
    $core.String? subtitle,
    $core.String? context,
    $core.String? data,
    $core.String? ext,
    $core.int? tempStatus,
  }) {
    final $result = create();
    if (title != null) {
      $result.title = title;
    }
    if (subtitle != null) {
      $result.subtitle = subtitle;
    }
    if (context != null) {
      $result.context = context;
    }
    if (data != null) {
      $result.data = data;
    }
    if (ext != null) {
      $result.ext = ext;
    }
    if (tempStatus != null) {
      $result.tempStatus = tempStatus;
    }
    return $result;
  }
  NoticeMsg._() : super();
  factory NoticeMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory NoticeMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'NoticeMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'title')
    ..aOS(2, _omitFieldNames ? '' : 'subtitle')
    ..aOS(3, _omitFieldNames ? '' : 'context')
    ..aOS(4, _omitFieldNames ? '' : 'data')
    ..aOS(5, _omitFieldNames ? '' : 'ext')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'tempStatus', $pb.PbFieldType.O3, protoName: 'tempStatus')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  NoticeMsg clone() => NoticeMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  NoticeMsg copyWith(void Function(NoticeMsg) updates) => super.copyWith((message) => updates(message as NoticeMsg)) as NoticeMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static NoticeMsg create() => NoticeMsg._();
  NoticeMsg createEmptyInstance() => create();
  static $pb.PbList<NoticeMsg> createRepeated() => $pb.PbList<NoticeMsg>();
  @$core.pragma('dart2js:noInline')
  static NoticeMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<NoticeMsg>(create);
  static NoticeMsg? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get title => $_getSZ(0);
  @$pb.TagNumber(1)
  set title($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTitle() => $_has(0);
  @$pb.TagNumber(1)
  void clearTitle() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get subtitle => $_getSZ(1);
  @$pb.TagNumber(2)
  set subtitle($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSubtitle() => $_has(1);
  @$pb.TagNumber(2)
  void clearSubtitle() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get context => $_getSZ(2);
  @$pb.TagNumber(3)
  set context($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasContext() => $_has(2);
  @$pb.TagNumber(3)
  void clearContext() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get data => $_getSZ(3);
  @$pb.TagNumber(4)
  set data($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasData() => $_has(3);
  @$pb.TagNumber(4)
  void clearData() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get ext => $_getSZ(4);
  @$pb.TagNumber(5)
  set ext($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasExt() => $_has(4);
  @$pb.TagNumber(5)
  void clearExt() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get tempStatus => $_getIZ(5);
  @$pb.TagNumber(6)
  set tempStatus($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasTempStatus() => $_has(5);
  @$pb.TagNumber(6)
  void clearTempStatus() => clearField(6);
}

class ServerMsg extends $pb.GeneratedMessage {
  factory ServerMsg({
    ImMsg? noticeMsgUpdated,
  }) {
    final $result = create();
    if (noticeMsgUpdated != null) {
      $result.noticeMsgUpdated = noticeMsgUpdated;
    }
    return $result;
  }
  ServerMsg._() : super();
  factory ServerMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ServerMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ServerMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOM<ImMsg>(1, _omitFieldNames ? '' : 'noticeMsgUpdated', protoName: 'noticeMsgUpdated', subBuilder: ImMsg.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ServerMsg clone() => ServerMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ServerMsg copyWith(void Function(ServerMsg) updates) => super.copyWith((message) => updates(message as ServerMsg)) as ServerMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ServerMsg create() => ServerMsg._();
  ServerMsg createEmptyInstance() => create();
  static $pb.PbList<ServerMsg> createRepeated() => $pb.PbList<ServerMsg>();
  @$core.pragma('dart2js:noInline')
  static ServerMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ServerMsg>(create);
  static ServerMsg? _defaultInstance;

  @$pb.TagNumber(1)
  ImMsg get noticeMsgUpdated => $_getN(0);
  @$pb.TagNumber(1)
  set noticeMsgUpdated(ImMsg v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasNoticeMsgUpdated() => $_has(0);
  @$pb.TagNumber(1)
  void clearNoticeMsgUpdated() => clearField(1);
  @$pb.TagNumber(1)
  ImMsg ensureNoticeMsgUpdated() => $_ensure(0);
}

class Exception extends $pb.GeneratedMessage {
  factory Exception({
    $core.String? msg,
    $core.int? requestName,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (requestName != null) {
      $result.requestName = requestName;
    }
    return $result;
  }
  Exception._() : super();
  factory Exception.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Exception.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Exception', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'msg')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'requestName', $pb.PbFieldType.O3, protoName: 'requestName')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Exception clone() => Exception()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Exception copyWith(void Function(Exception) updates) => super.copyWith((message) => updates(message as Exception)) as Exception;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Exception create() => Exception._();
  Exception createEmptyInstance() => create();
  static $pb.PbList<Exception> createRepeated() => $pb.PbList<Exception>();
  @$core.pragma('dart2js:noInline')
  static Exception getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Exception>(create);
  static Exception? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get msg => $_getSZ(0);
  @$pb.TagNumber(1)
  set msg($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get requestName => $_getIZ(1);
  @$pb.TagNumber(2)
  set requestName($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRequestName() => $_has(1);
  @$pb.TagNumber(2)
  void clearRequestName() => clearField(2);
}

class LoginRequest extends $pb.GeneratedMessage {
  factory LoginRequest({
    $core.String? token,
    LoginRequest_ClientTypeEnum? clientType,
  }) {
    final $result = create();
    if (token != null) {
      $result.token = token;
    }
    if (clientType != null) {
      $result.clientType = clientType;
    }
    return $result;
  }
  LoginRequest._() : super();
  factory LoginRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoginRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'LoginRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'token')
    ..e<LoginRequest_ClientTypeEnum>(2, _omitFieldNames ? '' : 'clientType', $pb.PbFieldType.OE, protoName: 'clientType', defaultOrMaker: LoginRequest_ClientTypeEnum.NULL, valueOf: LoginRequest_ClientTypeEnum.valueOf, enumValues: LoginRequest_ClientTypeEnum.values)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoginRequest clone() => LoginRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoginRequest copyWith(void Function(LoginRequest) updates) => super.copyWith((message) => updates(message as LoginRequest)) as LoginRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static LoginRequest create() => LoginRequest._();
  LoginRequest createEmptyInstance() => create();
  static $pb.PbList<LoginRequest> createRepeated() => $pb.PbList<LoginRequest>();
  @$core.pragma('dart2js:noInline')
  static LoginRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoginRequest>(create);
  static LoginRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get token => $_getSZ(0);
  @$pb.TagNumber(1)
  set token($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearToken() => clearField(1);

  @$pb.TagNumber(2)
  LoginRequest_ClientTypeEnum get clientType => $_getN(1);
  @$pb.TagNumber(2)
  set clientType(LoginRequest_ClientTypeEnum v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasClientType() => $_has(1);
  @$pb.TagNumber(2)
  void clearClientType() => clearField(2);
}

class LoginResult extends $pb.GeneratedMessage {
  factory LoginResult({
    $core.bool? success,
    $core.String? imUserId,
  }) {
    final $result = create();
    if (success != null) {
      $result.success = success;
    }
    if (imUserId != null) {
      $result.imUserId = imUserId;
    }
    return $result;
  }
  LoginResult._() : super();
  factory LoginResult.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoginResult.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'LoginResult', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOB(1, _omitFieldNames ? '' : 'success')
    ..aOS(2, _omitFieldNames ? '' : 'imUserId', protoName: 'imUserId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoginResult clone() => LoginResult()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoginResult copyWith(void Function(LoginResult) updates) => super.copyWith((message) => updates(message as LoginResult)) as LoginResult;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static LoginResult create() => LoginResult._();
  LoginResult createEmptyInstance() => create();
  static $pb.PbList<LoginResult> createRepeated() => $pb.PbList<LoginResult>();
  @$core.pragma('dart2js:noInline')
  static LoginResult getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoginResult>(create);
  static LoginResult? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get success => $_getBF(0);
  @$pb.TagNumber(1)
  set success($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSuccess() => $_has(0);
  @$pb.TagNumber(1)
  void clearSuccess() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get imUserId => $_getSZ(1);
  @$pb.TagNumber(2)
  set imUserId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasImUserId() => $_has(1);
  @$pb.TagNumber(2)
  void clearImUserId() => clearField(2);
}

enum ImgMsg_Image {
  url, 
  imageId, 
  notSet
}

class ImgMsg extends $pb.GeneratedMessage {
  factory ImgMsg({
    $core.List<$core.int>? thumbnail,
    $core.String? url,
    $core.String? imageId,
    $core.String? thumbnailUrl,
    $core.double? width,
    $core.double? height,
    $core.String? attach,
  }) {
    final $result = create();
    if (thumbnail != null) {
      $result.thumbnail = thumbnail;
    }
    if (url != null) {
      $result.url = url;
    }
    if (imageId != null) {
      $result.imageId = imageId;
    }
    if (thumbnailUrl != null) {
      $result.thumbnailUrl = thumbnailUrl;
    }
    if (width != null) {
      $result.width = width;
    }
    if (height != null) {
      $result.height = height;
    }
    if (attach != null) {
      $result.attach = attach;
    }
    return $result;
  }
  ImgMsg._() : super();
  factory ImgMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ImgMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, ImgMsg_Image> _ImgMsg_ImageByTag = {
    2 : ImgMsg_Image.url,
    3 : ImgMsg_Image.imageId,
    0 : ImgMsg_Image.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ImgMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [2, 3])
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'thumbnail', $pb.PbFieldType.OY)
    ..aOS(2, _omitFieldNames ? '' : 'url')
    ..aOS(3, _omitFieldNames ? '' : 'imageId', protoName: 'imageId')
    ..aOS(4, _omitFieldNames ? '' : 'thumbnailUrl', protoName: 'thumbnailUrl')
    ..a<$core.double>(5, _omitFieldNames ? '' : 'width', $pb.PbFieldType.OF)
    ..a<$core.double>(6, _omitFieldNames ? '' : 'height', $pb.PbFieldType.OF)
    ..aOS(7, _omitFieldNames ? '' : 'attach')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ImgMsg clone() => ImgMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ImgMsg copyWith(void Function(ImgMsg) updates) => super.copyWith((message) => updates(message as ImgMsg)) as ImgMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ImgMsg create() => ImgMsg._();
  ImgMsg createEmptyInstance() => create();
  static $pb.PbList<ImgMsg> createRepeated() => $pb.PbList<ImgMsg>();
  @$core.pragma('dart2js:noInline')
  static ImgMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ImgMsg>(create);
  static ImgMsg? _defaultInstance;

  ImgMsg_Image whichImage() => _ImgMsg_ImageByTag[$_whichOneof(0)]!;
  void clearImage() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.List<$core.int> get thumbnail => $_getN(0);
  @$pb.TagNumber(1)
  set thumbnail($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasThumbnail() => $_has(0);
  @$pb.TagNumber(1)
  void clearThumbnail() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get url => $_getSZ(1);
  @$pb.TagNumber(2)
  set url($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUrl() => $_has(1);
  @$pb.TagNumber(2)
  void clearUrl() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get imageId => $_getSZ(2);
  @$pb.TagNumber(3)
  set imageId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasImageId() => $_has(2);
  @$pb.TagNumber(3)
  void clearImageId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get thumbnailUrl => $_getSZ(3);
  @$pb.TagNumber(4)
  set thumbnailUrl($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasThumbnailUrl() => $_has(3);
  @$pb.TagNumber(4)
  void clearThumbnailUrl() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get width => $_getN(4);
  @$pb.TagNumber(5)
  set width($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasWidth() => $_has(4);
  @$pb.TagNumber(5)
  void clearWidth() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get height => $_getN(5);
  @$pb.TagNumber(6)
  set height($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasHeight() => $_has(5);
  @$pb.TagNumber(6)
  void clearHeight() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get attach => $_getSZ(6);
  @$pb.TagNumber(7)
  set attach($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasAttach() => $_has(6);
  @$pb.TagNumber(7)
  void clearAttach() => clearField(7);
}

enum Voice_Voice {
  url, 
  voiceId, 
  notSet
}

/// 语音消息
class Voice extends $pb.GeneratedMessage {
  factory Voice({
    $core.String? url,
    $core.int? duration,
    $core.String? voiceId,
    $core.String? attach,
  }) {
    final $result = create();
    if (url != null) {
      $result.url = url;
    }
    if (duration != null) {
      $result.duration = duration;
    }
    if (voiceId != null) {
      $result.voiceId = voiceId;
    }
    if (attach != null) {
      $result.attach = attach;
    }
    return $result;
  }
  Voice._() : super();
  factory Voice.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Voice.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, Voice_Voice> _Voice_VoiceByTag = {
    1 : Voice_Voice.url,
    3 : Voice_Voice.voiceId,
    0 : Voice_Voice.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Voice', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [1, 3])
    ..aOS(1, _omitFieldNames ? '' : 'url')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'duration', $pb.PbFieldType.OU3)
    ..aOS(3, _omitFieldNames ? '' : 'voiceId', protoName: 'voiceId')
    ..aOS(4, _omitFieldNames ? '' : 'attach')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Voice clone() => Voice()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Voice copyWith(void Function(Voice) updates) => super.copyWith((message) => updates(message as Voice)) as Voice;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Voice create() => Voice._();
  Voice createEmptyInstance() => create();
  static $pb.PbList<Voice> createRepeated() => $pb.PbList<Voice>();
  @$core.pragma('dart2js:noInline')
  static Voice getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Voice>(create);
  static Voice? _defaultInstance;

  Voice_Voice whichVoice() => _Voice_VoiceByTag[$_whichOneof(0)]!;
  void clearVoice() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get url => $_getSZ(0);
  @$pb.TagNumber(1)
  set url($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUrl() => $_has(0);
  @$pb.TagNumber(1)
  void clearUrl() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get duration => $_getIZ(1);
  @$pb.TagNumber(2)
  set duration($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDuration() => $_has(1);
  @$pb.TagNumber(2)
  void clearDuration() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get voiceId => $_getSZ(2);
  @$pb.TagNumber(3)
  set voiceId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasVoiceId() => $_has(2);
  @$pb.TagNumber(3)
  void clearVoiceId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get attach => $_getSZ(3);
  @$pb.TagNumber(4)
  set attach($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasAttach() => $_has(3);
  @$pb.TagNumber(4)
  void clearAttach() => clearField(4);
}

class Location extends $pb.GeneratedMessage {
  factory Location({
    $core.String? title,
    $core.String? address,
    $core.double? latitude,
    $core.double? longitude,
    $core.String? uri,
    $core.String? attach,
  }) {
    final $result = create();
    if (title != null) {
      $result.title = title;
    }
    if (address != null) {
      $result.address = address;
    }
    if (latitude != null) {
      $result.latitude = latitude;
    }
    if (longitude != null) {
      $result.longitude = longitude;
    }
    if (uri != null) {
      $result.uri = uri;
    }
    if (attach != null) {
      $result.attach = attach;
    }
    return $result;
  }
  Location._() : super();
  factory Location.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Location.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Location', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'title')
    ..aOS(2, _omitFieldNames ? '' : 'address')
    ..a<$core.double>(3, _omitFieldNames ? '' : 'latitude', $pb.PbFieldType.OD)
    ..a<$core.double>(4, _omitFieldNames ? '' : 'longitude', $pb.PbFieldType.OD)
    ..aOS(5, _omitFieldNames ? '' : 'uri')
    ..aOS(6, _omitFieldNames ? '' : 'attach')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Location clone() => Location()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Location copyWith(void Function(Location) updates) => super.copyWith((message) => updates(message as Location)) as Location;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Location create() => Location._();
  Location createEmptyInstance() => create();
  static $pb.PbList<Location> createRepeated() => $pb.PbList<Location>();
  @$core.pragma('dart2js:noInline')
  static Location getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Location>(create);
  static Location? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get title => $_getSZ(0);
  @$pb.TagNumber(1)
  set title($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTitle() => $_has(0);
  @$pb.TagNumber(1)
  void clearTitle() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get address => $_getSZ(1);
  @$pb.TagNumber(2)
  set address($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAddress() => $_has(1);
  @$pb.TagNumber(2)
  void clearAddress() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get latitude => $_getN(2);
  @$pb.TagNumber(3)
  set latitude($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLatitude() => $_has(2);
  @$pb.TagNumber(3)
  void clearLatitude() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get longitude => $_getN(3);
  @$pb.TagNumber(4)
  set longitude($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLongitude() => $_has(3);
  @$pb.TagNumber(4)
  void clearLongitude() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get uri => $_getSZ(4);
  @$pb.TagNumber(5)
  set uri($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasUri() => $_has(4);
  @$pb.TagNumber(5)
  void clearUri() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get attach => $_getSZ(5);
  @$pb.TagNumber(6)
  set attach($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAttach() => $_has(5);
  @$pb.TagNumber(6)
  void clearAttach() => clearField(6);
}

enum VideoMsg_File {
  url, 
  fileId, 
  notSet
}

class VideoMsg extends $pb.GeneratedMessage {
  factory VideoMsg({
    ImgMsg? cover,
    $core.String? url,
    $core.String? fileId,
    $fixnum.Int64? fileSize,
    $core.int? duration,
    $core.String? attach,
  }) {
    final $result = create();
    if (cover != null) {
      $result.cover = cover;
    }
    if (url != null) {
      $result.url = url;
    }
    if (fileId != null) {
      $result.fileId = fileId;
    }
    if (fileSize != null) {
      $result.fileSize = fileSize;
    }
    if (duration != null) {
      $result.duration = duration;
    }
    if (attach != null) {
      $result.attach = attach;
    }
    return $result;
  }
  VideoMsg._() : super();
  factory VideoMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory VideoMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, VideoMsg_File> _VideoMsg_FileByTag = {
    2 : VideoMsg_File.url,
    3 : VideoMsg_File.fileId,
    0 : VideoMsg_File.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'VideoMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [2, 3])
    ..aOM<ImgMsg>(1, _omitFieldNames ? '' : 'cover', subBuilder: ImgMsg.create)
    ..aOS(2, _omitFieldNames ? '' : 'url')
    ..aOS(3, _omitFieldNames ? '' : 'fileId', protoName: 'fileId')
    ..a<$fixnum.Int64>(4, _omitFieldNames ? '' : 'fileSize', $pb.PbFieldType.OU6, protoName: 'fileSize', defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'duration', $pb.PbFieldType.OU3)
    ..aOS(6, _omitFieldNames ? '' : 'attach')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  VideoMsg clone() => VideoMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  VideoMsg copyWith(void Function(VideoMsg) updates) => super.copyWith((message) => updates(message as VideoMsg)) as VideoMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static VideoMsg create() => VideoMsg._();
  VideoMsg createEmptyInstance() => create();
  static $pb.PbList<VideoMsg> createRepeated() => $pb.PbList<VideoMsg>();
  @$core.pragma('dart2js:noInline')
  static VideoMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<VideoMsg>(create);
  static VideoMsg? _defaultInstance;

  VideoMsg_File whichFile() => _VideoMsg_FileByTag[$_whichOneof(0)]!;
  void clearFile() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  ImgMsg get cover => $_getN(0);
  @$pb.TagNumber(1)
  set cover(ImgMsg v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCover() => $_has(0);
  @$pb.TagNumber(1)
  void clearCover() => clearField(1);
  @$pb.TagNumber(1)
  ImgMsg ensureCover() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get url => $_getSZ(1);
  @$pb.TagNumber(2)
  set url($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUrl() => $_has(1);
  @$pb.TagNumber(2)
  void clearUrl() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get fileId => $_getSZ(2);
  @$pb.TagNumber(3)
  set fileId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasFileId() => $_has(2);
  @$pb.TagNumber(3)
  void clearFileId() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get fileSize => $_getI64(3);
  @$pb.TagNumber(4)
  set fileSize($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasFileSize() => $_has(3);
  @$pb.TagNumber(4)
  void clearFileSize() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get duration => $_getIZ(4);
  @$pb.TagNumber(5)
  set duration($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDuration() => $_has(4);
  @$pb.TagNumber(5)
  void clearDuration() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get attach => $_getSZ(5);
  @$pb.TagNumber(6)
  set attach($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAttach() => $_has(5);
  @$pb.TagNumber(6)
  void clearAttach() => clearField(6);
}

enum FileMsg_File {
  url, 
  fileId, 
  notSet
}

class FileMsg extends $pb.GeneratedMessage {
  factory FileMsg({
    $core.String? name,
    $core.String? url,
    $core.String? fileId,
    $fixnum.Int64? fileSize,
    $core.String? attach,
  }) {
    final $result = create();
    if (name != null) {
      $result.name = name;
    }
    if (url != null) {
      $result.url = url;
    }
    if (fileId != null) {
      $result.fileId = fileId;
    }
    if (fileSize != null) {
      $result.fileSize = fileSize;
    }
    if (attach != null) {
      $result.attach = attach;
    }
    return $result;
  }
  FileMsg._() : super();
  factory FileMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FileMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, FileMsg_File> _FileMsg_FileByTag = {
    2 : FileMsg_File.url,
    3 : FileMsg_File.fileId,
    0 : FileMsg_File.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'FileMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [2, 3])
    ..aOS(1, _omitFieldNames ? '' : 'name')
    ..aOS(2, _omitFieldNames ? '' : 'url')
    ..aOS(3, _omitFieldNames ? '' : 'fileId', protoName: 'fileId')
    ..a<$fixnum.Int64>(4, _omitFieldNames ? '' : 'fileSize', $pb.PbFieldType.OU6, protoName: 'fileSize', defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(5, _omitFieldNames ? '' : 'attach')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FileMsg clone() => FileMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FileMsg copyWith(void Function(FileMsg) updates) => super.copyWith((message) => updates(message as FileMsg)) as FileMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileMsg create() => FileMsg._();
  FileMsg createEmptyInstance() => create();
  static $pb.PbList<FileMsg> createRepeated() => $pb.PbList<FileMsg>();
  @$core.pragma('dart2js:noInline')
  static FileMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileMsg>(create);
  static FileMsg? _defaultInstance;

  FileMsg_File whichFile() => _FileMsg_FileByTag[$_whichOneof(0)]!;
  void clearFile() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get url => $_getSZ(1);
  @$pb.TagNumber(2)
  set url($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUrl() => $_has(1);
  @$pb.TagNumber(2)
  void clearUrl() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get fileId => $_getSZ(2);
  @$pb.TagNumber(3)
  set fileId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasFileId() => $_has(2);
  @$pb.TagNumber(3)
  void clearFileId() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get fileSize => $_getI64(3);
  @$pb.TagNumber(4)
  set fileSize($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasFileSize() => $_has(3);
  @$pb.TagNumber(4)
  void clearFileSize() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get attach => $_getSZ(4);
  @$pb.TagNumber(5)
  set attach($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasAttach() => $_has(4);
  @$pb.TagNumber(5)
  void clearAttach() => clearField(5);
}

class UserInfo extends $pb.GeneratedMessage {
  factory UserInfo({
    $core.String? nickname,
    $core.String? userId,
    $core.String? avatar,
    $core.String? imUserId,
  }) {
    final $result = create();
    if (nickname != null) {
      $result.nickname = nickname;
    }
    if (userId != null) {
      $result.userId = userId;
    }
    if (avatar != null) {
      $result.avatar = avatar;
    }
    if (imUserId != null) {
      $result.imUserId = imUserId;
    }
    return $result;
  }
  UserInfo._() : super();
  factory UserInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UserInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'UserInfo', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'nickname')
    ..aOS(2, _omitFieldNames ? '' : 'userId', protoName: 'userId')
    ..aOS(3, _omitFieldNames ? '' : 'avatar')
    ..aOS(4, _omitFieldNames ? '' : 'imUserId', protoName: 'imUserId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UserInfo clone() => UserInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UserInfo copyWith(void Function(UserInfo) updates) => super.copyWith((message) => updates(message as UserInfo)) as UserInfo;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static UserInfo create() => UserInfo._();
  UserInfo createEmptyInstance() => create();
  static $pb.PbList<UserInfo> createRepeated() => $pb.PbList<UserInfo>();
  @$core.pragma('dart2js:noInline')
  static UserInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UserInfo>(create);
  static UserInfo? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get nickname => $_getSZ(0);
  @$pb.TagNumber(1)
  set nickname($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNickname() => $_has(0);
  @$pb.TagNumber(1)
  void clearNickname() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get userId => $_getSZ(1);
  @$pb.TagNumber(2)
  set userId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUserId() => $_has(1);
  @$pb.TagNumber(2)
  void clearUserId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get avatar => $_getSZ(2);
  @$pb.TagNumber(3)
  set avatar($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAvatar() => $_has(2);
  @$pb.TagNumber(3)
  void clearAvatar() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get imUserId => $_getSZ(3);
  @$pb.TagNumber(4)
  set imUserId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasImUserId() => $_has(3);
  @$pb.TagNumber(4)
  void clearImUserId() => clearField(4);
}

class AudioAndVideoCall extends $pb.GeneratedMessage {
  factory AudioAndVideoCall({
    AudioAndVideoCall_DurationType? durationType,
    $core.String? content,
    $core.String? metingId,
    $core.String? attach,
  }) {
    final $result = create();
    if (durationType != null) {
      $result.durationType = durationType;
    }
    if (content != null) {
      $result.content = content;
    }
    if (metingId != null) {
      $result.metingId = metingId;
    }
    if (attach != null) {
      $result.attach = attach;
    }
    return $result;
  }
  AudioAndVideoCall._() : super();
  factory AudioAndVideoCall.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AudioAndVideoCall.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'AudioAndVideoCall', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..e<AudioAndVideoCall_DurationType>(1, _omitFieldNames ? '' : 'durationType', $pb.PbFieldType.OE, protoName: 'durationType', defaultOrMaker: AudioAndVideoCall_DurationType.NULL, valueOf: AudioAndVideoCall_DurationType.valueOf, enumValues: AudioAndVideoCall_DurationType.values)
    ..aOS(2, _omitFieldNames ? '' : 'content')
    ..aOS(3, _omitFieldNames ? '' : 'metingId', protoName: 'metingId')
    ..aOS(4, _omitFieldNames ? '' : 'attach')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AudioAndVideoCall clone() => AudioAndVideoCall()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AudioAndVideoCall copyWith(void Function(AudioAndVideoCall) updates) => super.copyWith((message) => updates(message as AudioAndVideoCall)) as AudioAndVideoCall;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static AudioAndVideoCall create() => AudioAndVideoCall._();
  AudioAndVideoCall createEmptyInstance() => create();
  static $pb.PbList<AudioAndVideoCall> createRepeated() => $pb.PbList<AudioAndVideoCall>();
  @$core.pragma('dart2js:noInline')
  static AudioAndVideoCall getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AudioAndVideoCall>(create);
  static AudioAndVideoCall? _defaultInstance;

  @$pb.TagNumber(1)
  AudioAndVideoCall_DurationType get durationType => $_getN(0);
  @$pb.TagNumber(1)
  set durationType(AudioAndVideoCall_DurationType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDurationType() => $_has(0);
  @$pb.TagNumber(1)
  void clearDurationType() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get content => $_getSZ(1);
  @$pb.TagNumber(2)
  set content($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasContent() => $_has(1);
  @$pb.TagNumber(2)
  void clearContent() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get metingId => $_getSZ(2);
  @$pb.TagNumber(3)
  set metingId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMetingId() => $_has(2);
  @$pb.TagNumber(3)
  void clearMetingId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get attach => $_getSZ(3);
  @$pb.TagNumber(4)
  set attach($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasAttach() => $_has(3);
  @$pb.TagNumber(4)
  void clearAttach() => clearField(4);
}

/// *
///  撤回消息
class Withdraw extends $pb.GeneratedMessage {
  factory Withdraw({
    $core.String? msgId,
  }) {
    final $result = create();
    if (msgId != null) {
      $result.msgId = msgId;
    }
    return $result;
  }
  Withdraw._() : super();
  factory Withdraw.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Withdraw.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Withdraw', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'msgId', protoName: 'msgId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Withdraw clone() => Withdraw()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Withdraw copyWith(void Function(Withdraw) updates) => super.copyWith((message) => updates(message as Withdraw)) as Withdraw;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Withdraw create() => Withdraw._();
  Withdraw createEmptyInstance() => create();
  static $pb.PbList<Withdraw> createRepeated() => $pb.PbList<Withdraw>();
  @$core.pragma('dart2js:noInline')
  static Withdraw getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Withdraw>(create);
  static Withdraw? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get msgId => $_getSZ(0);
  @$pb.TagNumber(1)
  set msgId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsgId() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsgId() => clearField(1);
}

class ReadMsg extends $pb.GeneratedMessage {
  factory ReadMsg({
    $core.Iterable<$core.String>? msgId,
  }) {
    final $result = create();
    if (msgId != null) {
      $result.msgId.addAll(msgId);
    }
    return $result;
  }
  ReadMsg._() : super();
  factory ReadMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ReadMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ReadMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..pPS(1, _omitFieldNames ? '' : 'msgId', protoName: 'msgId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ReadMsg clone() => ReadMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ReadMsg copyWith(void Function(ReadMsg) updates) => super.copyWith((message) => updates(message as ReadMsg)) as ReadMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ReadMsg create() => ReadMsg._();
  ReadMsg createEmptyInstance() => create();
  static $pb.PbList<ReadMsg> createRepeated() => $pb.PbList<ReadMsg>();
  @$core.pragma('dart2js:noInline')
  static ReadMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ReadMsg>(create);
  static ReadMsg? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get msgId => $_getList(0);
}

enum C2CMsg_Context {
  msg, 
  image, 
  voice, 
  location, 
  video, 
  file, 
  withdraw, 
  read, 
  ext, 
  call, 
  notSet
}

/// 接收用户到用户的单聊消息
class C2CMsg extends $pb.GeneratedMessage {
  factory C2CMsg({
    $core.String? from,
    $core.int? conversationId,
    $core.String? msg,
    ImgMsg? image,
    Voice? voice,
    Location? location,
    VideoMsg? video,
    FileMsg? file,
    Withdraw? withdraw,
    ReadMsg? read,
    ExtMsg? ext,
    AudioAndVideoCall? call,
    $core.String? receiver,
  }) {
    final $result = create();
    if (from != null) {
      $result.from = from;
    }
    if (conversationId != null) {
      $result.conversationId = conversationId;
    }
    if (msg != null) {
      $result.msg = msg;
    }
    if (image != null) {
      $result.image = image;
    }
    if (voice != null) {
      $result.voice = voice;
    }
    if (location != null) {
      $result.location = location;
    }
    if (video != null) {
      $result.video = video;
    }
    if (file != null) {
      $result.file = file;
    }
    if (withdraw != null) {
      $result.withdraw = withdraw;
    }
    if (read != null) {
      $result.read = read;
    }
    if (ext != null) {
      $result.ext = ext;
    }
    if (call != null) {
      $result.call = call;
    }
    if (receiver != null) {
      $result.receiver = receiver;
    }
    return $result;
  }
  C2CMsg._() : super();
  factory C2CMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory C2CMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, C2CMsg_Context> _C2CMsg_ContextByTag = {
    6 : C2CMsg_Context.msg,
    7 : C2CMsg_Context.image,
    8 : C2CMsg_Context.voice,
    9 : C2CMsg_Context.location,
    10 : C2CMsg_Context.video,
    11 : C2CMsg_Context.file,
    12 : C2CMsg_Context.withdraw,
    13 : C2CMsg_Context.read,
    14 : C2CMsg_Context.ext,
    15 : C2CMsg_Context.call,
    0 : C2CMsg_Context.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'C2CMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [6, 7, 8, 9, 10, 11, 12, 13, 14, 15])
    ..aOS(1, _omitFieldNames ? '' : 'from')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'conversationId', $pb.PbFieldType.OU3, protoName: 'conversationId')
    ..aOS(6, _omitFieldNames ? '' : 'msg')
    ..aOM<ImgMsg>(7, _omitFieldNames ? '' : 'image', subBuilder: ImgMsg.create)
    ..aOM<Voice>(8, _omitFieldNames ? '' : 'voice', subBuilder: Voice.create)
    ..aOM<Location>(9, _omitFieldNames ? '' : 'location', subBuilder: Location.create)
    ..aOM<VideoMsg>(10, _omitFieldNames ? '' : 'video', subBuilder: VideoMsg.create)
    ..aOM<FileMsg>(11, _omitFieldNames ? '' : 'file', subBuilder: FileMsg.create)
    ..aOM<Withdraw>(12, _omitFieldNames ? '' : 'withdraw', subBuilder: Withdraw.create)
    ..aOM<ReadMsg>(13, _omitFieldNames ? '' : 'read', subBuilder: ReadMsg.create)
    ..aOM<ExtMsg>(14, _omitFieldNames ? '' : 'ext', subBuilder: ExtMsg.create)
    ..aOM<AudioAndVideoCall>(15, _omitFieldNames ? '' : 'call', subBuilder: AudioAndVideoCall.create)
    ..aOS(16, _omitFieldNames ? '' : 'receiver')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  C2CMsg clone() => C2CMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  C2CMsg copyWith(void Function(C2CMsg) updates) => super.copyWith((message) => updates(message as C2CMsg)) as C2CMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static C2CMsg create() => C2CMsg._();
  C2CMsg createEmptyInstance() => create();
  static $pb.PbList<C2CMsg> createRepeated() => $pb.PbList<C2CMsg>();
  @$core.pragma('dart2js:noInline')
  static C2CMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<C2CMsg>(create);
  static C2CMsg? _defaultInstance;

  C2CMsg_Context whichContext() => _C2CMsg_ContextByTag[$_whichOneof(0)]!;
  void clearContext() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get from => $_getSZ(0);
  @$pb.TagNumber(1)
  set from($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasFrom() => $_has(0);
  @$pb.TagNumber(1)
  void clearFrom() => clearField(1);

  @$pb.TagNumber(4)
  $core.int get conversationId => $_getIZ(1);
  @$pb.TagNumber(4)
  set conversationId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(4)
  $core.bool hasConversationId() => $_has(1);
  @$pb.TagNumber(4)
  void clearConversationId() => clearField(4);

  @$pb.TagNumber(6)
  $core.String get msg => $_getSZ(2);
  @$pb.TagNumber(6)
  set msg($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(6)
  $core.bool hasMsg() => $_has(2);
  @$pb.TagNumber(6)
  void clearMsg() => clearField(6);

  @$pb.TagNumber(7)
  ImgMsg get image => $_getN(3);
  @$pb.TagNumber(7)
  set image(ImgMsg v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasImage() => $_has(3);
  @$pb.TagNumber(7)
  void clearImage() => clearField(7);
  @$pb.TagNumber(7)
  ImgMsg ensureImage() => $_ensure(3);

  @$pb.TagNumber(8)
  Voice get voice => $_getN(4);
  @$pb.TagNumber(8)
  set voice(Voice v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasVoice() => $_has(4);
  @$pb.TagNumber(8)
  void clearVoice() => clearField(8);
  @$pb.TagNumber(8)
  Voice ensureVoice() => $_ensure(4);

  @$pb.TagNumber(9)
  Location get location => $_getN(5);
  @$pb.TagNumber(9)
  set location(Location v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasLocation() => $_has(5);
  @$pb.TagNumber(9)
  void clearLocation() => clearField(9);
  @$pb.TagNumber(9)
  Location ensureLocation() => $_ensure(5);

  @$pb.TagNumber(10)
  VideoMsg get video => $_getN(6);
  @$pb.TagNumber(10)
  set video(VideoMsg v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasVideo() => $_has(6);
  @$pb.TagNumber(10)
  void clearVideo() => clearField(10);
  @$pb.TagNumber(10)
  VideoMsg ensureVideo() => $_ensure(6);

  @$pb.TagNumber(11)
  FileMsg get file => $_getN(7);
  @$pb.TagNumber(11)
  set file(FileMsg v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasFile() => $_has(7);
  @$pb.TagNumber(11)
  void clearFile() => clearField(11);
  @$pb.TagNumber(11)
  FileMsg ensureFile() => $_ensure(7);

  @$pb.TagNumber(12)
  Withdraw get withdraw => $_getN(8);
  @$pb.TagNumber(12)
  set withdraw(Withdraw v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasWithdraw() => $_has(8);
  @$pb.TagNumber(12)
  void clearWithdraw() => clearField(12);
  @$pb.TagNumber(12)
  Withdraw ensureWithdraw() => $_ensure(8);

  @$pb.TagNumber(13)
  ReadMsg get read => $_getN(9);
  @$pb.TagNumber(13)
  set read(ReadMsg v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasRead() => $_has(9);
  @$pb.TagNumber(13)
  void clearRead() => clearField(13);
  @$pb.TagNumber(13)
  ReadMsg ensureRead() => $_ensure(9);

  @$pb.TagNumber(14)
  ExtMsg get ext => $_getN(10);
  @$pb.TagNumber(14)
  set ext(ExtMsg v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasExt() => $_has(10);
  @$pb.TagNumber(14)
  void clearExt() => clearField(14);
  @$pb.TagNumber(14)
  ExtMsg ensureExt() => $_ensure(10);

  @$pb.TagNumber(15)
  AudioAndVideoCall get call => $_getN(11);
  @$pb.TagNumber(15)
  set call(AudioAndVideoCall v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasCall() => $_has(11);
  @$pb.TagNumber(15)
  void clearCall() => clearField(15);
  @$pb.TagNumber(15)
  AudioAndVideoCall ensureCall() => $_ensure(11);

  @$pb.TagNumber(16)
  $core.String get receiver => $_getSZ(12);
  @$pb.TagNumber(16)
  set receiver($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(16)
  $core.bool hasReceiver() => $_has(12);
  @$pb.TagNumber(16)
  void clearReceiver() => clearField(16);
}

enum C2CMsgRequest_Context {
  msg, 
  image, 
  voice, 
  location, 
  withdraw, 
  read, 
  ext, 
  video, 
  file, 
  call, 
  notSet
}

class C2CMsgRequest extends $pb.GeneratedMessage {
  factory C2CMsgRequest({
    $core.String? receiver,
    $core.String? msg,
    ImgMsg? image,
    Voice? voice,
    Location? location,
    Withdraw? withdraw,
    ReadMsg? read,
    ExtMsg? ext,
    VideoMsg? video,
    FileMsg? file,
    AudioAndVideoCall? call,
  }) {
    final $result = create();
    if (receiver != null) {
      $result.receiver = receiver;
    }
    if (msg != null) {
      $result.msg = msg;
    }
    if (image != null) {
      $result.image = image;
    }
    if (voice != null) {
      $result.voice = voice;
    }
    if (location != null) {
      $result.location = location;
    }
    if (withdraw != null) {
      $result.withdraw = withdraw;
    }
    if (read != null) {
      $result.read = read;
    }
    if (ext != null) {
      $result.ext = ext;
    }
    if (video != null) {
      $result.video = video;
    }
    if (file != null) {
      $result.file = file;
    }
    if (call != null) {
      $result.call = call;
    }
    return $result;
  }
  C2CMsgRequest._() : super();
  factory C2CMsgRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory C2CMsgRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, C2CMsgRequest_Context> _C2CMsgRequest_ContextByTag = {
    3 : C2CMsgRequest_Context.msg,
    4 : C2CMsgRequest_Context.image,
    5 : C2CMsgRequest_Context.voice,
    6 : C2CMsgRequest_Context.location,
    7 : C2CMsgRequest_Context.withdraw,
    8 : C2CMsgRequest_Context.read,
    9 : C2CMsgRequest_Context.ext,
    10 : C2CMsgRequest_Context.video,
    11 : C2CMsgRequest_Context.file,
    12 : C2CMsgRequest_Context.call,
    0 : C2CMsgRequest_Context.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'C2CMsgRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [3, 4, 5, 6, 7, 8, 9, 10, 11, 12])
    ..aOS(1, _omitFieldNames ? '' : 'receiver')
    ..aOS(3, _omitFieldNames ? '' : 'msg')
    ..aOM<ImgMsg>(4, _omitFieldNames ? '' : 'image', subBuilder: ImgMsg.create)
    ..aOM<Voice>(5, _omitFieldNames ? '' : 'voice', subBuilder: Voice.create)
    ..aOM<Location>(6, _omitFieldNames ? '' : 'location', subBuilder: Location.create)
    ..aOM<Withdraw>(7, _omitFieldNames ? '' : 'withdraw', subBuilder: Withdraw.create)
    ..aOM<ReadMsg>(8, _omitFieldNames ? '' : 'read', subBuilder: ReadMsg.create)
    ..aOM<ExtMsg>(9, _omitFieldNames ? '' : 'ext', subBuilder: ExtMsg.create)
    ..aOM<VideoMsg>(10, _omitFieldNames ? '' : 'video', subBuilder: VideoMsg.create)
    ..aOM<FileMsg>(11, _omitFieldNames ? '' : 'file', subBuilder: FileMsg.create)
    ..aOM<AudioAndVideoCall>(12, _omitFieldNames ? '' : 'call', subBuilder: AudioAndVideoCall.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  C2CMsgRequest clone() => C2CMsgRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  C2CMsgRequest copyWith(void Function(C2CMsgRequest) updates) => super.copyWith((message) => updates(message as C2CMsgRequest)) as C2CMsgRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static C2CMsgRequest create() => C2CMsgRequest._();
  C2CMsgRequest createEmptyInstance() => create();
  static $pb.PbList<C2CMsgRequest> createRepeated() => $pb.PbList<C2CMsgRequest>();
  @$core.pragma('dart2js:noInline')
  static C2CMsgRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<C2CMsgRequest>(create);
  static C2CMsgRequest? _defaultInstance;

  C2CMsgRequest_Context whichContext() => _C2CMsgRequest_ContextByTag[$_whichOneof(0)]!;
  void clearContext() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get receiver => $_getSZ(0);
  @$pb.TagNumber(1)
  set receiver($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasReceiver() => $_has(0);
  @$pb.TagNumber(1)
  void clearReceiver() => clearField(1);

  @$pb.TagNumber(3)
  $core.String get msg => $_getSZ(1);
  @$pb.TagNumber(3)
  set msg($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasMsg() => $_has(1);
  @$pb.TagNumber(3)
  void clearMsg() => clearField(3);

  @$pb.TagNumber(4)
  ImgMsg get image => $_getN(2);
  @$pb.TagNumber(4)
  set image(ImgMsg v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasImage() => $_has(2);
  @$pb.TagNumber(4)
  void clearImage() => clearField(4);
  @$pb.TagNumber(4)
  ImgMsg ensureImage() => $_ensure(2);

  @$pb.TagNumber(5)
  Voice get voice => $_getN(3);
  @$pb.TagNumber(5)
  set voice(Voice v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasVoice() => $_has(3);
  @$pb.TagNumber(5)
  void clearVoice() => clearField(5);
  @$pb.TagNumber(5)
  Voice ensureVoice() => $_ensure(3);

  @$pb.TagNumber(6)
  Location get location => $_getN(4);
  @$pb.TagNumber(6)
  set location(Location v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasLocation() => $_has(4);
  @$pb.TagNumber(6)
  void clearLocation() => clearField(6);
  @$pb.TagNumber(6)
  Location ensureLocation() => $_ensure(4);

  @$pb.TagNumber(7)
  Withdraw get withdraw => $_getN(5);
  @$pb.TagNumber(7)
  set withdraw(Withdraw v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasWithdraw() => $_has(5);
  @$pb.TagNumber(7)
  void clearWithdraw() => clearField(7);
  @$pb.TagNumber(7)
  Withdraw ensureWithdraw() => $_ensure(5);

  @$pb.TagNumber(8)
  ReadMsg get read => $_getN(6);
  @$pb.TagNumber(8)
  set read(ReadMsg v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasRead() => $_has(6);
  @$pb.TagNumber(8)
  void clearRead() => clearField(8);
  @$pb.TagNumber(8)
  ReadMsg ensureRead() => $_ensure(6);

  @$pb.TagNumber(9)
  ExtMsg get ext => $_getN(7);
  @$pb.TagNumber(9)
  set ext(ExtMsg v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasExt() => $_has(7);
  @$pb.TagNumber(9)
  void clearExt() => clearField(9);
  @$pb.TagNumber(9)
  ExtMsg ensureExt() => $_ensure(7);

  @$pb.TagNumber(10)
  VideoMsg get video => $_getN(8);
  @$pb.TagNumber(10)
  set video(VideoMsg v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasVideo() => $_has(8);
  @$pb.TagNumber(10)
  void clearVideo() => clearField(10);
  @$pb.TagNumber(10)
  VideoMsg ensureVideo() => $_ensure(8);

  @$pb.TagNumber(11)
  FileMsg get file => $_getN(9);
  @$pb.TagNumber(11)
  set file(FileMsg v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasFile() => $_has(9);
  @$pb.TagNumber(11)
  void clearFile() => clearField(11);
  @$pb.TagNumber(11)
  FileMsg ensureFile() => $_ensure(9);

  @$pb.TagNumber(12)
  AudioAndVideoCall get call => $_getN(10);
  @$pb.TagNumber(12)
  set call(AudioAndVideoCall v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasCall() => $_has(10);
  @$pb.TagNumber(12)
  void clearCall() => clearField(12);
  @$pb.TagNumber(12)
  AudioAndVideoCall ensureCall() => $_ensure(10);
}

class C2CMsgResponse extends $pb.GeneratedMessage {
  factory C2CMsgResponse({
    $core.String? receiver,
    $core.bool? success,
    FailType? failType,
    $core.String? failMessage,
  }) {
    final $result = create();
    if (receiver != null) {
      $result.receiver = receiver;
    }
    if (success != null) {
      $result.success = success;
    }
    if (failType != null) {
      $result.failType = failType;
    }
    if (failMessage != null) {
      $result.failMessage = failMessage;
    }
    return $result;
  }
  C2CMsgResponse._() : super();
  factory C2CMsgResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory C2CMsgResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'C2CMsgResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'receiver')
    ..aOB(2, _omitFieldNames ? '' : 'success')
    ..e<FailType>(4, _omitFieldNames ? '' : 'failType', $pb.PbFieldType.OE, protoName: 'failType', defaultOrMaker: FailType.UNKNOWN, valueOf: FailType.valueOf, enumValues: FailType.values)
    ..aOS(5, _omitFieldNames ? '' : 'failMessage', protoName: 'failMessage')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  C2CMsgResponse clone() => C2CMsgResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  C2CMsgResponse copyWith(void Function(C2CMsgResponse) updates) => super.copyWith((message) => updates(message as C2CMsgResponse)) as C2CMsgResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static C2CMsgResponse create() => C2CMsgResponse._();
  C2CMsgResponse createEmptyInstance() => create();
  static $pb.PbList<C2CMsgResponse> createRepeated() => $pb.PbList<C2CMsgResponse>();
  @$core.pragma('dart2js:noInline')
  static C2CMsgResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<C2CMsgResponse>(create);
  static C2CMsgResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get receiver => $_getSZ(0);
  @$pb.TagNumber(1)
  set receiver($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasReceiver() => $_has(0);
  @$pb.TagNumber(1)
  void clearReceiver() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get success => $_getBF(1);
  @$pb.TagNumber(2)
  set success($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSuccess() => $_has(1);
  @$pb.TagNumber(2)
  void clearSuccess() => clearField(2);

  @$pb.TagNumber(4)
  FailType get failType => $_getN(2);
  @$pb.TagNumber(4)
  set failType(FailType v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasFailType() => $_has(2);
  @$pb.TagNumber(4)
  void clearFailType() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get failMessage => $_getSZ(3);
  @$pb.TagNumber(5)
  set failMessage($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasFailMessage() => $_has(3);
  @$pb.TagNumber(5)
  void clearFailMessage() => clearField(5);
}

class PushMsg extends $pb.GeneratedMessage {
  factory PushMsg({
    $core.String? msg,
    $core.int? type,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (type != null) {
      $result.type = type;
    }
    return $result;
  }
  PushMsg._() : super();
  factory PushMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PushMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PushMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'msg')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'type', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PushMsg clone() => PushMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PushMsg copyWith(void Function(PushMsg) updates) => super.copyWith((message) => updates(message as PushMsg)) as PushMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PushMsg create() => PushMsg._();
  PushMsg createEmptyInstance() => create();
  static $pb.PbList<PushMsg> createRepeated() => $pb.PbList<PushMsg>();
  @$core.pragma('dart2js:noInline')
  static PushMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PushMsg>(create);
  static PushMsg? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get msg => $_getSZ(0);
  @$pb.TagNumber(1)
  set msg($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get type => $_getIZ(1);
  @$pb.TagNumber(2)
  set type($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasType() => $_has(1);
  @$pb.TagNumber(2)
  void clearType() => clearField(2);
}

enum GroupMsg_Context {
  msg, 
  image, 
  voice, 
  location, 
  ext, 
  withdraw, 
  video, 
  file, 
  call, 
  noticeMsg, 
  notSet
}

class GroupMsg extends $pb.GeneratedMessage {
  factory GroupMsg({
    $core.String? msg,
    ImgMsg? image,
    Voice? voice,
    Location? location,
    $core.String? groupId,
    ExtMsg? ext,
    Withdraw? withdraw,
    VideoMsg? video,
    FileMsg? file,
    AudioAndVideoCall? call,
    NoticeMsg? noticeMsg,
    $core.String? groupName,
    $core.String? groupLogo,
    $core.String? appId,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (image != null) {
      $result.image = image;
    }
    if (voice != null) {
      $result.voice = voice;
    }
    if (location != null) {
      $result.location = location;
    }
    if (groupId != null) {
      $result.groupId = groupId;
    }
    if (ext != null) {
      $result.ext = ext;
    }
    if (withdraw != null) {
      $result.withdraw = withdraw;
    }
    if (video != null) {
      $result.video = video;
    }
    if (file != null) {
      $result.file = file;
    }
    if (call != null) {
      $result.call = call;
    }
    if (noticeMsg != null) {
      $result.noticeMsg = noticeMsg;
    }
    if (groupName != null) {
      $result.groupName = groupName;
    }
    if (groupLogo != null) {
      $result.groupLogo = groupLogo;
    }
    if (appId != null) {
      $result.appId = appId;
    }
    return $result;
  }
  GroupMsg._() : super();
  factory GroupMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GroupMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, GroupMsg_Context> _GroupMsg_ContextByTag = {
    1 : GroupMsg_Context.msg,
    2 : GroupMsg_Context.image,
    3 : GroupMsg_Context.voice,
    4 : GroupMsg_Context.location,
    6 : GroupMsg_Context.ext,
    7 : GroupMsg_Context.withdraw,
    8 : GroupMsg_Context.video,
    9 : GroupMsg_Context.file,
    10 : GroupMsg_Context.call,
    11 : GroupMsg_Context.noticeMsg,
    0 : GroupMsg_Context.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GroupMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3, 4, 6, 7, 8, 9, 10, 11])
    ..aOS(1, _omitFieldNames ? '' : 'msg')
    ..aOM<ImgMsg>(2, _omitFieldNames ? '' : 'image', subBuilder: ImgMsg.create)
    ..aOM<Voice>(3, _omitFieldNames ? '' : 'voice', subBuilder: Voice.create)
    ..aOM<Location>(4, _omitFieldNames ? '' : 'location', subBuilder: Location.create)
    ..aOS(5, _omitFieldNames ? '' : 'groupId', protoName: 'groupId')
    ..aOM<ExtMsg>(6, _omitFieldNames ? '' : 'ext', subBuilder: ExtMsg.create)
    ..aOM<Withdraw>(7, _omitFieldNames ? '' : 'withdraw', subBuilder: Withdraw.create)
    ..aOM<VideoMsg>(8, _omitFieldNames ? '' : 'video', subBuilder: VideoMsg.create)
    ..aOM<FileMsg>(9, _omitFieldNames ? '' : 'file', subBuilder: FileMsg.create)
    ..aOM<AudioAndVideoCall>(10, _omitFieldNames ? '' : 'call', subBuilder: AudioAndVideoCall.create)
    ..aOM<NoticeMsg>(11, _omitFieldNames ? '' : 'noticeMsg', protoName: 'noticeMsg', subBuilder: NoticeMsg.create)
    ..aOS(12, _omitFieldNames ? '' : 'groupName', protoName: 'groupName')
    ..aOS(13, _omitFieldNames ? '' : 'groupLogo', protoName: 'groupLogo')
    ..aOS(14, _omitFieldNames ? '' : 'appId', protoName: 'appId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GroupMsg clone() => GroupMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GroupMsg copyWith(void Function(GroupMsg) updates) => super.copyWith((message) => updates(message as GroupMsg)) as GroupMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GroupMsg create() => GroupMsg._();
  GroupMsg createEmptyInstance() => create();
  static $pb.PbList<GroupMsg> createRepeated() => $pb.PbList<GroupMsg>();
  @$core.pragma('dart2js:noInline')
  static GroupMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GroupMsg>(create);
  static GroupMsg? _defaultInstance;

  GroupMsg_Context whichContext() => _GroupMsg_ContextByTag[$_whichOneof(0)]!;
  void clearContext() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get msg => $_getSZ(0);
  @$pb.TagNumber(1)
  set msg($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(2)
  ImgMsg get image => $_getN(1);
  @$pb.TagNumber(2)
  set image(ImgMsg v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasImage() => $_has(1);
  @$pb.TagNumber(2)
  void clearImage() => clearField(2);
  @$pb.TagNumber(2)
  ImgMsg ensureImage() => $_ensure(1);

  @$pb.TagNumber(3)
  Voice get voice => $_getN(2);
  @$pb.TagNumber(3)
  set voice(Voice v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasVoice() => $_has(2);
  @$pb.TagNumber(3)
  void clearVoice() => clearField(3);
  @$pb.TagNumber(3)
  Voice ensureVoice() => $_ensure(2);

  @$pb.TagNumber(4)
  Location get location => $_getN(3);
  @$pb.TagNumber(4)
  set location(Location v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasLocation() => $_has(3);
  @$pb.TagNumber(4)
  void clearLocation() => clearField(4);
  @$pb.TagNumber(4)
  Location ensureLocation() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.String get groupId => $_getSZ(4);
  @$pb.TagNumber(5)
  set groupId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGroupId() => $_has(4);
  @$pb.TagNumber(5)
  void clearGroupId() => clearField(5);

  @$pb.TagNumber(6)
  ExtMsg get ext => $_getN(5);
  @$pb.TagNumber(6)
  set ext(ExtMsg v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasExt() => $_has(5);
  @$pb.TagNumber(6)
  void clearExt() => clearField(6);
  @$pb.TagNumber(6)
  ExtMsg ensureExt() => $_ensure(5);

  @$pb.TagNumber(7)
  Withdraw get withdraw => $_getN(6);
  @$pb.TagNumber(7)
  set withdraw(Withdraw v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasWithdraw() => $_has(6);
  @$pb.TagNumber(7)
  void clearWithdraw() => clearField(7);
  @$pb.TagNumber(7)
  Withdraw ensureWithdraw() => $_ensure(6);

  @$pb.TagNumber(8)
  VideoMsg get video => $_getN(7);
  @$pb.TagNumber(8)
  set video(VideoMsg v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasVideo() => $_has(7);
  @$pb.TagNumber(8)
  void clearVideo() => clearField(8);
  @$pb.TagNumber(8)
  VideoMsg ensureVideo() => $_ensure(7);

  @$pb.TagNumber(9)
  FileMsg get file => $_getN(8);
  @$pb.TagNumber(9)
  set file(FileMsg v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasFile() => $_has(8);
  @$pb.TagNumber(9)
  void clearFile() => clearField(9);
  @$pb.TagNumber(9)
  FileMsg ensureFile() => $_ensure(8);

  @$pb.TagNumber(10)
  AudioAndVideoCall get call => $_getN(9);
  @$pb.TagNumber(10)
  set call(AudioAndVideoCall v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasCall() => $_has(9);
  @$pb.TagNumber(10)
  void clearCall() => clearField(10);
  @$pb.TagNumber(10)
  AudioAndVideoCall ensureCall() => $_ensure(9);

  @$pb.TagNumber(11)
  NoticeMsg get noticeMsg => $_getN(10);
  @$pb.TagNumber(11)
  set noticeMsg(NoticeMsg v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasNoticeMsg() => $_has(10);
  @$pb.TagNumber(11)
  void clearNoticeMsg() => clearField(11);
  @$pb.TagNumber(11)
  NoticeMsg ensureNoticeMsg() => $_ensure(10);

  @$pb.TagNumber(12)
  $core.String get groupName => $_getSZ(11);
  @$pb.TagNumber(12)
  set groupName($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasGroupName() => $_has(11);
  @$pb.TagNumber(12)
  void clearGroupName() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get groupLogo => $_getSZ(12);
  @$pb.TagNumber(13)
  set groupLogo($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasGroupLogo() => $_has(12);
  @$pb.TagNumber(13)
  void clearGroupLogo() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get appId => $_getSZ(13);
  @$pb.TagNumber(14)
  set appId($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasAppId() => $_has(13);
  @$pb.TagNumber(14)
  void clearAppId() => clearField(14);
}

class ExtMsg extends $pb.GeneratedMessage {
  factory ExtMsg({
    $0.Any? ext,
    $core.String? ext1,
    $core.String? ext2,
    $core.String? ext3,
  }) {
    final $result = create();
    if (ext != null) {
      $result.ext = ext;
    }
    if (ext1 != null) {
      $result.ext1 = ext1;
    }
    if (ext2 != null) {
      $result.ext2 = ext2;
    }
    if (ext3 != null) {
      $result.ext3 = ext3;
    }
    return $result;
  }
  ExtMsg._() : super();
  factory ExtMsg.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ExtMsg.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ExtMsg', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOM<$0.Any>(1, _omitFieldNames ? '' : 'ext', subBuilder: $0.Any.create)
    ..aOS(2, _omitFieldNames ? '' : 'ext1')
    ..aOS(3, _omitFieldNames ? '' : 'ext2')
    ..aOS(4, _omitFieldNames ? '' : 'ext3')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ExtMsg clone() => ExtMsg()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ExtMsg copyWith(void Function(ExtMsg) updates) => super.copyWith((message) => updates(message as ExtMsg)) as ExtMsg;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ExtMsg create() => ExtMsg._();
  ExtMsg createEmptyInstance() => create();
  static $pb.PbList<ExtMsg> createRepeated() => $pb.PbList<ExtMsg>();
  @$core.pragma('dart2js:noInline')
  static ExtMsg getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ExtMsg>(create);
  static ExtMsg? _defaultInstance;

  @$pb.TagNumber(1)
  $0.Any get ext => $_getN(0);
  @$pb.TagNumber(1)
  set ext($0.Any v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasExt() => $_has(0);
  @$pb.TagNumber(1)
  void clearExt() => clearField(1);
  @$pb.TagNumber(1)
  $0.Any ensureExt() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get ext1 => $_getSZ(1);
  @$pb.TagNumber(2)
  set ext1($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasExt1() => $_has(1);
  @$pb.TagNumber(2)
  void clearExt1() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get ext2 => $_getSZ(2);
  @$pb.TagNumber(3)
  set ext2($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasExt2() => $_has(2);
  @$pb.TagNumber(3)
  void clearExt2() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get ext3 => $_getSZ(3);
  @$pb.TagNumber(4)
  set ext3($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasExt3() => $_has(3);
  @$pb.TagNumber(4)
  void clearExt3() => clearField(4);
}

enum GroupMsgRequest_Context {
  msg, 
  image, 
  voice, 
  location, 
  ext, 
  withdraw, 
  video, 
  file, 
  call, 
  notSet
}

class GroupMsgRequest extends $pb.GeneratedMessage {
  factory GroupMsgRequest({
    $core.String? msg,
    ImgMsg? image,
    Voice? voice,
    Location? location,
    $core.String? groupId,
    ExtMsg? ext,
    Withdraw? withdraw,
    VideoMsg? video,
    FileMsg? file,
    AudioAndVideoCall? call,
    $core.String? groupName,
    $core.String? groupLogo,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (image != null) {
      $result.image = image;
    }
    if (voice != null) {
      $result.voice = voice;
    }
    if (location != null) {
      $result.location = location;
    }
    if (groupId != null) {
      $result.groupId = groupId;
    }
    if (ext != null) {
      $result.ext = ext;
    }
    if (withdraw != null) {
      $result.withdraw = withdraw;
    }
    if (video != null) {
      $result.video = video;
    }
    if (file != null) {
      $result.file = file;
    }
    if (call != null) {
      $result.call = call;
    }
    if (groupName != null) {
      $result.groupName = groupName;
    }
    if (groupLogo != null) {
      $result.groupLogo = groupLogo;
    }
    return $result;
  }
  GroupMsgRequest._() : super();
  factory GroupMsgRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GroupMsgRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, GroupMsgRequest_Context> _GroupMsgRequest_ContextByTag = {
    1 : GroupMsgRequest_Context.msg,
    2 : GroupMsgRequest_Context.image,
    3 : GroupMsgRequest_Context.voice,
    4 : GroupMsgRequest_Context.location,
    6 : GroupMsgRequest_Context.ext,
    7 : GroupMsgRequest_Context.withdraw,
    8 : GroupMsgRequest_Context.video,
    9 : GroupMsgRequest_Context.file,
    10 : GroupMsgRequest_Context.call,
    0 : GroupMsgRequest_Context.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GroupMsgRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3, 4, 6, 7, 8, 9, 10])
    ..aOS(1, _omitFieldNames ? '' : 'msg')
    ..aOM<ImgMsg>(2, _omitFieldNames ? '' : 'image', subBuilder: ImgMsg.create)
    ..aOM<Voice>(3, _omitFieldNames ? '' : 'voice', subBuilder: Voice.create)
    ..aOM<Location>(4, _omitFieldNames ? '' : 'location', subBuilder: Location.create)
    ..aOS(5, _omitFieldNames ? '' : 'groupId', protoName: 'groupId')
    ..aOM<ExtMsg>(6, _omitFieldNames ? '' : 'ext', subBuilder: ExtMsg.create)
    ..aOM<Withdraw>(7, _omitFieldNames ? '' : 'withdraw', subBuilder: Withdraw.create)
    ..aOM<VideoMsg>(8, _omitFieldNames ? '' : 'video', subBuilder: VideoMsg.create)
    ..aOM<FileMsg>(9, _omitFieldNames ? '' : 'file', subBuilder: FileMsg.create)
    ..aOM<AudioAndVideoCall>(10, _omitFieldNames ? '' : 'call', subBuilder: AudioAndVideoCall.create)
    ..aOS(11, _omitFieldNames ? '' : 'groupName', protoName: 'groupName')
    ..aOS(12, _omitFieldNames ? '' : 'groupLogo', protoName: 'groupLogo')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GroupMsgRequest clone() => GroupMsgRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GroupMsgRequest copyWith(void Function(GroupMsgRequest) updates) => super.copyWith((message) => updates(message as GroupMsgRequest)) as GroupMsgRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GroupMsgRequest create() => GroupMsgRequest._();
  GroupMsgRequest createEmptyInstance() => create();
  static $pb.PbList<GroupMsgRequest> createRepeated() => $pb.PbList<GroupMsgRequest>();
  @$core.pragma('dart2js:noInline')
  static GroupMsgRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GroupMsgRequest>(create);
  static GroupMsgRequest? _defaultInstance;

  GroupMsgRequest_Context whichContext() => _GroupMsgRequest_ContextByTag[$_whichOneof(0)]!;
  void clearContext() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get msg => $_getSZ(0);
  @$pb.TagNumber(1)
  set msg($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(2)
  ImgMsg get image => $_getN(1);
  @$pb.TagNumber(2)
  set image(ImgMsg v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasImage() => $_has(1);
  @$pb.TagNumber(2)
  void clearImage() => clearField(2);
  @$pb.TagNumber(2)
  ImgMsg ensureImage() => $_ensure(1);

  @$pb.TagNumber(3)
  Voice get voice => $_getN(2);
  @$pb.TagNumber(3)
  set voice(Voice v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasVoice() => $_has(2);
  @$pb.TagNumber(3)
  void clearVoice() => clearField(3);
  @$pb.TagNumber(3)
  Voice ensureVoice() => $_ensure(2);

  @$pb.TagNumber(4)
  Location get location => $_getN(3);
  @$pb.TagNumber(4)
  set location(Location v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasLocation() => $_has(3);
  @$pb.TagNumber(4)
  void clearLocation() => clearField(4);
  @$pb.TagNumber(4)
  Location ensureLocation() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.String get groupId => $_getSZ(4);
  @$pb.TagNumber(5)
  set groupId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGroupId() => $_has(4);
  @$pb.TagNumber(5)
  void clearGroupId() => clearField(5);

  @$pb.TagNumber(6)
  ExtMsg get ext => $_getN(5);
  @$pb.TagNumber(6)
  set ext(ExtMsg v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasExt() => $_has(5);
  @$pb.TagNumber(6)
  void clearExt() => clearField(6);
  @$pb.TagNumber(6)
  ExtMsg ensureExt() => $_ensure(5);

  @$pb.TagNumber(7)
  Withdraw get withdraw => $_getN(6);
  @$pb.TagNumber(7)
  set withdraw(Withdraw v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasWithdraw() => $_has(6);
  @$pb.TagNumber(7)
  void clearWithdraw() => clearField(7);
  @$pb.TagNumber(7)
  Withdraw ensureWithdraw() => $_ensure(6);

  @$pb.TagNumber(8)
  VideoMsg get video => $_getN(7);
  @$pb.TagNumber(8)
  set video(VideoMsg v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasVideo() => $_has(7);
  @$pb.TagNumber(8)
  void clearVideo() => clearField(8);
  @$pb.TagNumber(8)
  VideoMsg ensureVideo() => $_ensure(7);

  @$pb.TagNumber(9)
  FileMsg get file => $_getN(8);
  @$pb.TagNumber(9)
  set file(FileMsg v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasFile() => $_has(8);
  @$pb.TagNumber(9)
  void clearFile() => clearField(9);
  @$pb.TagNumber(9)
  FileMsg ensureFile() => $_ensure(8);

  @$pb.TagNumber(10)
  AudioAndVideoCall get call => $_getN(9);
  @$pb.TagNumber(10)
  set call(AudioAndVideoCall v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasCall() => $_has(9);
  @$pb.TagNumber(10)
  void clearCall() => clearField(10);
  @$pb.TagNumber(10)
  AudioAndVideoCall ensureCall() => $_ensure(9);

  @$pb.TagNumber(11)
  $core.String get groupName => $_getSZ(10);
  @$pb.TagNumber(11)
  set groupName($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasGroupName() => $_has(10);
  @$pb.TagNumber(11)
  void clearGroupName() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get groupLogo => $_getSZ(11);
  @$pb.TagNumber(12)
  set groupLogo($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasGroupLogo() => $_has(11);
  @$pb.TagNumber(12)
  void clearGroupLogo() => clearField(12);
}

class GroupMsgResponse extends $pb.GeneratedMessage {
  factory GroupMsgResponse({
    $core.String? groupId,
    $core.bool? success,
    FailType? failType,
    $core.String? failMessage,
  }) {
    final $result = create();
    if (groupId != null) {
      $result.groupId = groupId;
    }
    if (success != null) {
      $result.success = success;
    }
    if (failType != null) {
      $result.failType = failType;
    }
    if (failMessage != null) {
      $result.failMessage = failMessage;
    }
    return $result;
  }
  GroupMsgResponse._() : super();
  factory GroupMsgResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GroupMsgResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GroupMsgResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.joinu.im.protobuf'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'groupId', protoName: 'groupId')
    ..aOB(2, _omitFieldNames ? '' : 'success')
    ..e<FailType>(4, _omitFieldNames ? '' : 'failType', $pb.PbFieldType.OE, protoName: 'failType', defaultOrMaker: FailType.UNKNOWN, valueOf: FailType.valueOf, enumValues: FailType.values)
    ..aOS(5, _omitFieldNames ? '' : 'failMessage', protoName: 'failMessage')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GroupMsgResponse clone() => GroupMsgResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GroupMsgResponse copyWith(void Function(GroupMsgResponse) updates) => super.copyWith((message) => updates(message as GroupMsgResponse)) as GroupMsgResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GroupMsgResponse create() => GroupMsgResponse._();
  GroupMsgResponse createEmptyInstance() => create();
  static $pb.PbList<GroupMsgResponse> createRepeated() => $pb.PbList<GroupMsgResponse>();
  @$core.pragma('dart2js:noInline')
  static GroupMsgResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GroupMsgResponse>(create);
  static GroupMsgResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get groupId => $_getSZ(0);
  @$pb.TagNumber(1)
  set groupId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasGroupId() => $_has(0);
  @$pb.TagNumber(1)
  void clearGroupId() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get success => $_getBF(1);
  @$pb.TagNumber(2)
  set success($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSuccess() => $_has(1);
  @$pb.TagNumber(2)
  void clearSuccess() => clearField(2);

  @$pb.TagNumber(4)
  FailType get failType => $_getN(2);
  @$pb.TagNumber(4)
  set failType(FailType v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasFailType() => $_has(2);
  @$pb.TagNumber(4)
  void clearFailType() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get failMessage => $_getSZ(3);
  @$pb.TagNumber(5)
  set failMessage($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasFailMessage() => $_has(3);
  @$pb.TagNumber(5)
  void clearFailMessage() => clearField(5);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
