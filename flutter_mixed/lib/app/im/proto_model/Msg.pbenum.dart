//
//  Generated code. Do not modify.
//  source: example/Msg.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

/// *
///  消息发送失败的原因列表
class FailType extends $pb.ProtobufEnum {
  static const FailType UNKNOWN = FailType._(0, _omitEnumNames ? '' : 'UNKNOWN');
  static const FailType IN_BLACKLIST = FailType._(1, _omitEnumNames ? '' : 'IN_BLACKLIST');
  static const FailType TARGET_NOT_LOGIN = FailType._(2, _omitEnumNames ? '' : 'TARGET_NOT_LOGIN');
  static const FailType MSG_SAVE_FAILURE = FailType._(3, _omitEnumNames ? '' : 'MSG_SAVE_FAILURE');
  static const FailType TARGET_IS_EMPTY = FailType._(4, _omitEnumNames ? '' : 'TARGET_IS_EMPTY');
  static const FailType GROUP_NOT_EXIST = FailType._(5, _omitEnumNames ? '' : 'GROUP_NOT_EXIST');
  static const FailType GROUP_MUTED = FailType._(6, _omitEnumNames ? '' : 'GROUP_MUTED');
  static const FailType GROUP_USER_MUTED = FailType._(7, _omitEnumNames ? '' : 'GROUP_USER_MUTED');
  static const FailType USER_NOT_IN_GROUP = FailType._(8, _omitEnumNames ? '' : 'USER_NOT_IN_GROUP');

  static const $core.List<FailType> values = <FailType> [
    UNKNOWN,
    IN_BLACKLIST,
    TARGET_NOT_LOGIN,
    MSG_SAVE_FAILURE,
    TARGET_IS_EMPTY,
    GROUP_NOT_EXIST,
    GROUP_MUTED,
    GROUP_USER_MUTED,
    USER_NOT_IN_GROUP,
  ];

  static final $core.Map<$core.int, FailType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static FailType? valueOf($core.int value) => _byValue[value];

  const FailType._($core.int v, $core.String n) : super(v, n);
}

class WarnMessage_SessionType extends $pb.ProtobufEnum {
  static const WarnMessage_SessionType NULL = WarnMessage_SessionType._(0, _omitEnumNames ? '' : 'NULL');
  static const WarnMessage_SessionType C2C = WarnMessage_SessionType._(1, _omitEnumNames ? '' : 'C2C');
  static const WarnMessage_SessionType GROUP = WarnMessage_SessionType._(2, _omitEnumNames ? '' : 'GROUP');

  static const $core.List<WarnMessage_SessionType> values = <WarnMessage_SessionType> [
    NULL,
    C2C,
    GROUP,
  ];

  static final $core.Map<$core.int, WarnMessage_SessionType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static WarnMessage_SessionType? valueOf($core.int value) => _byValue[value];

  const WarnMessage_SessionType._($core.int v, $core.String n) : super(v, n);
}

class KickMsg_KickType extends $pb.ProtobufEnum {
  static const KickMsg_KickType NULL = KickMsg_KickType._(0, _omitEnumNames ? '' : 'NULL');
  static const KickMsg_KickType OtherDeviceLogin = KickMsg_KickType._(1, _omitEnumNames ? '' : 'OtherDeviceLogin');

  static const $core.List<KickMsg_KickType> values = <KickMsg_KickType> [
    NULL,
    OtherDeviceLogin,
  ];

  static final $core.Map<$core.int, KickMsg_KickType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static KickMsg_KickType? valueOf($core.int value) => _byValue[value];

  const KickMsg_KickType._($core.int v, $core.String n) : super(v, n);
}

class LoginRequest_ClientTypeEnum extends $pb.ProtobufEnum {
  static const LoginRequest_ClientTypeEnum NULL = LoginRequest_ClientTypeEnum._(0, _omitEnumNames ? '' : 'NULL');
  static const LoginRequest_ClientTypeEnum IOS = LoginRequest_ClientTypeEnum._(1, _omitEnumNames ? '' : 'IOS');
  static const LoginRequest_ClientTypeEnum ANDROID = LoginRequest_ClientTypeEnum._(2, _omitEnumNames ? '' : 'ANDROID');
  static const LoginRequest_ClientTypeEnum WEB = LoginRequest_ClientTypeEnum._(3, _omitEnumNames ? '' : 'WEB');
  static const LoginRequest_ClientTypeEnum PC = LoginRequest_ClientTypeEnum._(4, _omitEnumNames ? '' : 'PC');
  static const LoginRequest_ClientTypeEnum OTHER = LoginRequest_ClientTypeEnum._(5, _omitEnumNames ? '' : 'OTHER');
  static const LoginRequest_ClientTypeEnum MOBILE = LoginRequest_ClientTypeEnum._(6, _omitEnumNames ? '' : 'MOBILE');

  static const $core.List<LoginRequest_ClientTypeEnum> values = <LoginRequest_ClientTypeEnum> [
    NULL,
    IOS,
    ANDROID,
    WEB,
    PC,
    OTHER,
    MOBILE,
  ];

  static final $core.Map<$core.int, LoginRequest_ClientTypeEnum> _byValue = $pb.ProtobufEnum.initByValue(values);
  static LoginRequest_ClientTypeEnum? valueOf($core.int value) => _byValue[value];

  const LoginRequest_ClientTypeEnum._($core.int v, $core.String n) : super(v, n);
}

class AudioAndVideoCall_DurationType extends $pb.ProtobufEnum {
  static const AudioAndVideoCall_DurationType NULL = AudioAndVideoCall_DurationType._(0, _omitEnumNames ? '' : 'NULL');
  static const AudioAndVideoCall_DurationType VOICE = AudioAndVideoCall_DurationType._(1, _omitEnumNames ? '' : 'VOICE');
  static const AudioAndVideoCall_DurationType VIDEO = AudioAndVideoCall_DurationType._(2, _omitEnumNames ? '' : 'VIDEO');

  static const $core.List<AudioAndVideoCall_DurationType> values = <AudioAndVideoCall_DurationType> [
    NULL,
    VOICE,
    VIDEO,
  ];

  static final $core.Map<$core.int, AudioAndVideoCall_DurationType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static AudioAndVideoCall_DurationType? valueOf($core.int value) => _byValue[value];

  const AudioAndVideoCall_DurationType._($core.int v, $core.String n) : super(v, n);
}


const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
