//
//  Generated code. Do not modify.
//  source: example/Msg.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use failTypeDescriptor instead')
const FailType$json = {
  '1': 'FailType',
  '2': [
    {'1': 'UNKNOWN', '2': 0},
    {'1': 'IN_BLACKLIST', '2': 1},
    {'1': 'TARGET_NOT_LOGIN', '2': 2},
    {'1': 'MSG_SAVE_FAILURE', '2': 3},
    {'1': 'TARGET_IS_EMPTY', '2': 4},
    {'1': 'GROUP_NOT_EXIST', '2': 5},
    {'1': 'GROUP_MUTED', '2': 6},
    {'1': 'GROUP_USER_MUTED', '2': 7},
    {'1': 'USER_NOT_IN_GROUP', '2': 8},
  ],
};

/// Descriptor for `FailType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List failTypeDescriptor = $convert.base64Decode(
    'CghGYWlsVHlwZRILCgdVTktOT1dOEAASEAoMSU5fQkxBQ0tMSVNUEAESFAoQVEFSR0VUX05PVF'
    '9MT0dJThACEhQKEE1TR19TQVZFX0ZBSUxVUkUQAxITCg9UQVJHRVRfSVNfRU1QVFkQBBITCg9H'
    'Uk9VUF9OT1RfRVhJU1QQBRIPCgtHUk9VUF9NVVRFRBAGEhQKEEdST1VQX1VTRVJfTVVURUQQBx'
    'IVChFVU0VSX05PVF9JTl9HUk9VUBAI');

@$core.Deprecated('Use imMsgDescriptor instead')
const ImMsg$json = {
  '1': 'ImMsg',
  '2': [
    {'1': 'msgId', '3': 1, '4': 1, '5': 9, '10': 'msgId'},
    {'1': 'cmdId', '3': 2, '4': 1, '5': 9, '10': 'cmdId'},
    {'1': 'type', '3': 16, '4': 1, '5': 5, '10': 'type'},
    {'1': 'senderInfo', '3': 17, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.UserInfo', '10': 'senderInfo'},
    {'1': 'msgTime', '3': 18, '4': 1, '5': 3, '10': 'msgTime'},
    {'1': 'recordIgnore', '3': 21, '4': 1, '5': 8, '10': 'recordIgnore'},
    {'1': 'msgContent', '3': 22, '4': 1, '5': 9, '10': 'msgContent'},
    {'1': 'selfMsg', '3': 25, '4': 1, '5': 8, '10': 'selfMsg'},
    {'1': 'customMessage', '3': 26, '4': 1, '5': 9, '10': 'customMessage'},
    {'1': 'exception', '3': 3, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Exception', '9': 0, '10': 'exception'},
    {'1': 'loginRequest', '3': 4, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.LoginRequest', '9': 0, '10': 'loginRequest'},
    {'1': 'loginResult', '3': 5, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.LoginResult', '9': 0, '10': 'loginResult'},
    {'1': 'c2cMsg', '3': 6, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.C2CMsg', '9': 0, '10': 'c2cMsg'},
    {'1': 'c2cMsgRequest', '3': 7, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.C2CMsgRequest', '9': 0, '10': 'c2cMsgRequest'},
    {'1': 'c2cMsgResponse', '3': 8, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.C2CMsgResponse', '9': 0, '10': 'c2cMsgResponse'},
    {'1': 'groupMsg', '3': 9, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.GroupMsg', '9': 0, '10': 'groupMsg'},
    {'1': 'groupMsgRequest', '3': 10, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.GroupMsgRequest', '9': 0, '10': 'groupMsgRequest'},
    {'1': 'groupMsgResponse', '3': 11, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.GroupMsgResponse', '9': 0, '10': 'groupMsgResponse'},
    {'1': 'pushMsg', '3': 12, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.PushMsg', '9': 0, '10': 'pushMsg'},
    {'1': 'serverMsg', '3': 13, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ServerMsg', '9': 0, '10': 'serverMsg'},
    {'1': 'noticeMsg', '3': 14, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.NoticeMsg', '9': 0, '10': 'noticeMsg'},
    {'1': 'ext', '3': 15, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ExtMsg', '9': 0, '10': 'ext'},
    {'1': 'offline', '3': 19, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.MultipleMsg', '9': 0, '10': 'offline'},
    {'1': 'offlineMsgRequest', '3': 20, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.OfflineMsgRequest', '9': 0, '10': 'offlineMsgRequest'},
    {'1': 'kick', '3': 23, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.KickMsg', '9': 0, '10': 'kick'},
    {'1': 'imPushMsg', '3': 24, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ImPushMsg', '9': 0, '10': 'imPushMsg'},
  ],
  '8': [
    {'1': 'content'},
  ],
};

/// Descriptor for `ImMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List imMsgDescriptor = $convert.base64Decode(
    'CgVJbU1zZxIUCgVtc2dJZBgBIAEoCVIFbXNnSWQSFAoFY21kSWQYAiABKAlSBWNtZElkEhIKBH'
    'R5cGUYECABKAVSBHR5cGUSPwoKc2VuZGVySW5mbxgRIAEoCzIfLmNvbS5qb2ludS5pbS5wcm90'
    'b2J1Zi5Vc2VySW5mb1IKc2VuZGVySW5mbxIYCgdtc2dUaW1lGBIgASgDUgdtc2dUaW1lEiIKDH'
    'JlY29yZElnbm9yZRgVIAEoCFIMcmVjb3JkSWdub3JlEh4KCm1zZ0NvbnRlbnQYFiABKAlSCm1z'
    'Z0NvbnRlbnQSGAoHc2VsZk1zZxgZIAEoCFIHc2VsZk1zZxIkCg1jdXN0b21NZXNzYWdlGBogAS'
    'gJUg1jdXN0b21NZXNzYWdlEkAKCWV4Y2VwdGlvbhgDIAEoCzIgLmNvbS5qb2ludS5pbS5wcm90'
    'b2J1Zi5FeGNlcHRpb25IAFIJZXhjZXB0aW9uEkkKDGxvZ2luUmVxdWVzdBgEIAEoCzIjLmNvbS'
    '5qb2ludS5pbS5wcm90b2J1Zi5Mb2dpblJlcXVlc3RIAFIMbG9naW5SZXF1ZXN0EkYKC2xvZ2lu'
    'UmVzdWx0GAUgASgLMiIuY29tLmpvaW51LmltLnByb3RvYnVmLkxvZ2luUmVzdWx0SABSC2xvZ2'
    'luUmVzdWx0EjcKBmMyY01zZxgGIAEoCzIdLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5DMkNNc2dI'
    'AFIGYzJjTXNnEkwKDWMyY01zZ1JlcXVlc3QYByABKAsyJC5jb20uam9pbnUuaW0ucHJvdG9idW'
    'YuQzJDTXNnUmVxdWVzdEgAUg1jMmNNc2dSZXF1ZXN0Ek8KDmMyY01zZ1Jlc3BvbnNlGAggASgL'
    'MiUuY29tLmpvaW51LmltLnByb3RvYnVmLkMyQ01zZ1Jlc3BvbnNlSABSDmMyY01zZ1Jlc3Bvbn'
    'NlEj0KCGdyb3VwTXNnGAkgASgLMh8uY29tLmpvaW51LmltLnByb3RvYnVmLkdyb3VwTXNnSABS'
    'CGdyb3VwTXNnElIKD2dyb3VwTXNnUmVxdWVzdBgKIAEoCzImLmNvbS5qb2ludS5pbS5wcm90b2'
    'J1Zi5Hcm91cE1zZ1JlcXVlc3RIAFIPZ3JvdXBNc2dSZXF1ZXN0ElUKEGdyb3VwTXNnUmVzcG9u'
    'c2UYCyABKAsyJy5jb20uam9pbnUuaW0ucHJvdG9idWYuR3JvdXBNc2dSZXNwb25zZUgAUhBncm'
    '91cE1zZ1Jlc3BvbnNlEjoKB3B1c2hNc2cYDCABKAsyHi5jb20uam9pbnUuaW0ucHJvdG9idWYu'
    'UHVzaE1zZ0gAUgdwdXNoTXNnEkAKCXNlcnZlck1zZxgNIAEoCzIgLmNvbS5qb2ludS5pbS5wcm'
    '90b2J1Zi5TZXJ2ZXJNc2dIAFIJc2VydmVyTXNnEkAKCW5vdGljZU1zZxgOIAEoCzIgLmNvbS5q'
    'b2ludS5pbS5wcm90b2J1Zi5Ob3RpY2VNc2dIAFIJbm90aWNlTXNnEjEKA2V4dBgPIAEoCzIdLm'
    'NvbS5qb2ludS5pbS5wcm90b2J1Zi5FeHRNc2dIAFIDZXh0Ej4KB29mZmxpbmUYEyABKAsyIi5j'
    'b20uam9pbnUuaW0ucHJvdG9idWYuTXVsdGlwbGVNc2dIAFIHb2ZmbGluZRJYChFvZmZsaW5lTX'
    'NnUmVxdWVzdBgUIAEoCzIoLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5PZmZsaW5lTXNnUmVxdWVz'
    'dEgAUhFvZmZsaW5lTXNnUmVxdWVzdBI0CgRraWNrGBcgASgLMh4uY29tLmpvaW51LmltLnByb3'
    'RvYnVmLktpY2tNc2dIAFIEa2ljaxJACglpbVB1c2hNc2cYGCABKAsyIC5jb20uam9pbnUuaW0u'
    'cHJvdG9idWYuSW1QdXNoTXNnSABSCWltUHVzaE1zZ0IJCgdjb250ZW50');

@$core.Deprecated('Use imPushMsgDescriptor instead')
const ImPushMsg$json = {
  '1': 'ImPushMsg',
  '2': [
    {'1': 'blacklistAdd', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'blacklistAdd'},
    {'1': 'blacklistRemove', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'blacklistRemove'},
    {'1': 'singleMuteListAdd', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'singleMuteListAdd'},
    {'1': 'singleMuteListRemove', '3': 4, '4': 1, '5': 9, '9': 0, '10': 'singleMuteListRemove'},
    {'1': 'groupMuteListAdd', '3': 5, '4': 1, '5': 9, '9': 0, '10': 'groupMuteListAdd'},
    {'1': 'groupMuteListRemove', '3': 6, '4': 1, '5': 9, '9': 0, '10': 'groupMuteListRemove'},
    {'1': 'warnMessage', '3': 7, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.WarnMessage', '9': 0, '10': 'warnMessage'},
  ],
  '8': [
    {'1': 'content'},
  ],
};

/// Descriptor for `ImPushMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List imPushMsgDescriptor = $convert.base64Decode(
    'CglJbVB1c2hNc2cSJAoMYmxhY2tsaXN0QWRkGAEgASgJSABSDGJsYWNrbGlzdEFkZBIqCg9ibG'
    'Fja2xpc3RSZW1vdmUYAiABKAlIAFIPYmxhY2tsaXN0UmVtb3ZlEi4KEXNpbmdsZU11dGVMaXN0'
    'QWRkGAMgASgJSABSEXNpbmdsZU11dGVMaXN0QWRkEjQKFHNpbmdsZU11dGVMaXN0UmVtb3ZlGA'
    'QgASgJSABSFHNpbmdsZU11dGVMaXN0UmVtb3ZlEiwKEGdyb3VwTXV0ZUxpc3RBZGQYBSABKAlI'
    'AFIQZ3JvdXBNdXRlTGlzdEFkZBIyChNncm91cE11dGVMaXN0UmVtb3ZlGAYgASgJSABSE2dyb3'
    'VwTXV0ZUxpc3RSZW1vdmUSRgoLd2Fybk1lc3NhZ2UYByABKAsyIi5jb20uam9pbnUuaW0ucHJv'
    'dG9idWYuV2Fybk1lc3NhZ2VIAFILd2Fybk1lc3NhZ2VCCQoHY29udGVudA==');

@$core.Deprecated('Use warnMessageDescriptor instead')
const WarnMessage$json = {
  '1': 'WarnMessage',
  '2': [
    {'1': 'msg', '3': 1, '4': 1, '5': 9, '10': 'msg'},
    {'1': 'sessionId', '3': 2, '4': 1, '5': 9, '10': 'sessionId'},
    {'1': 'sessionType', '3': 3, '4': 1, '5': 14, '6': '.com.joinu.im.protobuf.WarnMessage.SessionType', '10': 'sessionType'},
  ],
  '4': [WarnMessage_SessionType$json],
};

@$core.Deprecated('Use warnMessageDescriptor instead')
const WarnMessage_SessionType$json = {
  '1': 'SessionType',
  '2': [
    {'1': 'NULL', '2': 0},
    {'1': 'C2C', '2': 1},
    {'1': 'GROUP', '2': 2},
  ],
};

/// Descriptor for `WarnMessage`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List warnMessageDescriptor = $convert.base64Decode(
    'CgtXYXJuTWVzc2FnZRIQCgNtc2cYASABKAlSA21zZxIcCglzZXNzaW9uSWQYAiABKAlSCXNlc3'
    'Npb25JZBJQCgtzZXNzaW9uVHlwZRgDIAEoDjIuLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5XYXJu'
    'TWVzc2FnZS5TZXNzaW9uVHlwZVILc2Vzc2lvblR5cGUiKwoLU2Vzc2lvblR5cGUSCAoETlVMTB'
    'AAEgcKA0MyQxABEgkKBUdST1VQEAI=');

@$core.Deprecated('Use kickMsgDescriptor instead')
const KickMsg$json = {
  '1': 'KickMsg',
  '2': [
    {'1': 'kickType', '3': 1, '4': 1, '5': 14, '6': '.com.joinu.im.protobuf.KickMsg.KickType', '10': 'kickType'},
  ],
  '4': [KickMsg_KickType$json],
};

@$core.Deprecated('Use kickMsgDescriptor instead')
const KickMsg_KickType$json = {
  '1': 'KickType',
  '2': [
    {'1': 'NULL', '2': 0},
    {'1': 'OtherDeviceLogin', '2': 1},
  ],
};

/// Descriptor for `KickMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List kickMsgDescriptor = $convert.base64Decode(
    'CgdLaWNrTXNnEkMKCGtpY2tUeXBlGAEgASgOMicuY29tLmpvaW51LmltLnByb3RvYnVmLktpY2'
    'tNc2cuS2lja1R5cGVSCGtpY2tUeXBlIioKCEtpY2tUeXBlEggKBE5VTEwQABIUChBPdGhlckRl'
    'dmljZUxvZ2luEAE=');

@$core.Deprecated('Use offlineMsgRequestDescriptor instead')
const OfflineMsgRequest$json = {
  '1': 'OfflineMsgRequest',
  '2': [
    {'1': 'offlineTime', '3': 1, '4': 1, '5': 3, '10': 'offlineTime'},
    {'1': 'targetUserId', '3': 2, '4': 3, '5': 9, '10': 'targetUserId'},
    {'1': 'groupId', '3': 3, '4': 3, '5': 9, '10': 'groupId'},
  ],
};

/// Descriptor for `OfflineMsgRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List offlineMsgRequestDescriptor = $convert.base64Decode(
    'ChFPZmZsaW5lTXNnUmVxdWVzdBIgCgtvZmZsaW5lVGltZRgBIAEoA1ILb2ZmbGluZVRpbWUSIg'
    'oMdGFyZ2V0VXNlcklkGAIgAygJUgx0YXJnZXRVc2VySWQSGAoHZ3JvdXBJZBgDIAMoCVIHZ3Jv'
    'dXBJZA==');

@$core.Deprecated('Use multipleMsgDescriptor instead')
const MultipleMsg$json = {
  '1': 'MultipleMsg',
  '2': [
    {'1': 'msg', '3': 1, '4': 3, '5': 11, '6': '.com.joinu.im.protobuf.ImMsg', '10': 'msg'},
  ],
};

/// Descriptor for `MultipleMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List multipleMsgDescriptor = $convert.base64Decode(
    'CgtNdWx0aXBsZU1zZxIuCgNtc2cYASADKAsyHC5jb20uam9pbnUuaW0ucHJvdG9idWYuSW1Nc2'
    'dSA21zZw==');

@$core.Deprecated('Use noticeMsgDescriptor instead')
const NoticeMsg$json = {
  '1': 'NoticeMsg',
  '2': [
    {'1': 'title', '3': 1, '4': 1, '5': 9, '10': 'title'},
    {'1': 'subtitle', '3': 2, '4': 1, '5': 9, '10': 'subtitle'},
    {'1': 'context', '3': 3, '4': 1, '5': 9, '10': 'context'},
    {'1': 'data', '3': 4, '4': 1, '5': 9, '10': 'data'},
    {'1': 'ext', '3': 5, '4': 1, '5': 9, '10': 'ext'},
    {'1': 'tempStatus', '3': 6, '4': 1, '5': 5, '10': 'tempStatus'},
  ],
};

/// Descriptor for `NoticeMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List noticeMsgDescriptor = $convert.base64Decode(
    'CglOb3RpY2VNc2cSFAoFdGl0bGUYASABKAlSBXRpdGxlEhoKCHN1YnRpdGxlGAIgASgJUghzdW'
    'J0aXRsZRIYCgdjb250ZXh0GAMgASgJUgdjb250ZXh0EhIKBGRhdGEYBCABKAlSBGRhdGESEAoD'
    'ZXh0GAUgASgJUgNleHQSHgoKdGVtcFN0YXR1cxgGIAEoBVIKdGVtcFN0YXR1cw==');

@$core.Deprecated('Use serverMsgDescriptor instead')
const ServerMsg$json = {
  '1': 'ServerMsg',
  '2': [
    {'1': 'noticeMsgUpdated', '3': 1, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ImMsg', '10': 'noticeMsgUpdated'},
  ],
};

/// Descriptor for `ServerMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List serverMsgDescriptor = $convert.base64Decode(
    'CglTZXJ2ZXJNc2cSSAoQbm90aWNlTXNnVXBkYXRlZBgBIAEoCzIcLmNvbS5qb2ludS5pbS5wcm'
    '90b2J1Zi5JbU1zZ1IQbm90aWNlTXNnVXBkYXRlZA==');

@$core.Deprecated('Use exceptionDescriptor instead')
const Exception$json = {
  '1': 'Exception',
  '2': [
    {'1': 'msg', '3': 1, '4': 1, '5': 9, '10': 'msg'},
    {'1': 'requestName', '3': 2, '4': 1, '5': 5, '10': 'requestName'},
  ],
};

/// Descriptor for `Exception`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List exceptionDescriptor = $convert.base64Decode(
    'CglFeGNlcHRpb24SEAoDbXNnGAEgASgJUgNtc2cSIAoLcmVxdWVzdE5hbWUYAiABKAVSC3JlcX'
    'Vlc3ROYW1l');

@$core.Deprecated('Use loginRequestDescriptor instead')
const LoginRequest$json = {
  '1': 'LoginRequest',
  '2': [
    {'1': 'token', '3': 1, '4': 1, '5': 9, '10': 'token'},
    {'1': 'clientType', '3': 2, '4': 1, '5': 14, '6': '.com.joinu.im.protobuf.LoginRequest.ClientTypeEnum', '10': 'clientType'},
  ],
  '4': [LoginRequest_ClientTypeEnum$json],
};

@$core.Deprecated('Use loginRequestDescriptor instead')
const LoginRequest_ClientTypeEnum$json = {
  '1': 'ClientTypeEnum',
  '2': [
    {'1': 'NULL', '2': 0},
    {'1': 'IOS', '2': 1},
    {'1': 'ANDROID', '2': 2},
    {'1': 'WEB', '2': 3},
    {'1': 'PC', '2': 4},
    {'1': 'OTHER', '2': 5},
    {'1': 'MOBILE', '2': 6},
  ],
};

/// Descriptor for `LoginRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List loginRequestDescriptor = $convert.base64Decode(
    'CgxMb2dpblJlcXVlc3QSFAoFdG9rZW4YASABKAlSBXRva2VuElIKCmNsaWVudFR5cGUYAiABKA'
    '4yMi5jb20uam9pbnUuaW0ucHJvdG9idWYuTG9naW5SZXF1ZXN0LkNsaWVudFR5cGVFbnVtUgpj'
    'bGllbnRUeXBlIlgKDkNsaWVudFR5cGVFbnVtEggKBE5VTEwQABIHCgNJT1MQARILCgdBTkRST0'
    'lEEAISBwoDV0VCEAMSBgoCUEMQBBIJCgVPVEhFUhAFEgoKBk1PQklMRRAG');

@$core.Deprecated('Use loginResultDescriptor instead')
const LoginResult$json = {
  '1': 'LoginResult',
  '2': [
    {'1': 'success', '3': 1, '4': 1, '5': 8, '10': 'success'},
    {'1': 'imUserId', '3': 2, '4': 1, '5': 9, '10': 'imUserId'},
  ],
};

/// Descriptor for `LoginResult`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List loginResultDescriptor = $convert.base64Decode(
    'CgtMb2dpblJlc3VsdBIYCgdzdWNjZXNzGAEgASgIUgdzdWNjZXNzEhoKCGltVXNlcklkGAIgAS'
    'gJUghpbVVzZXJJZA==');

@$core.Deprecated('Use imgMsgDescriptor instead')
const ImgMsg$json = {
  '1': 'ImgMsg',
  '2': [
    {'1': 'thumbnail', '3': 1, '4': 1, '5': 12, '10': 'thumbnail'},
    {'1': 'url', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'url'},
    {'1': 'imageId', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'imageId'},
    {'1': 'thumbnailUrl', '3': 4, '4': 1, '5': 9, '10': 'thumbnailUrl'},
    {'1': 'width', '3': 5, '4': 1, '5': 2, '10': 'width'},
    {'1': 'height', '3': 6, '4': 1, '5': 2, '10': 'height'},
    {'1': 'attach', '3': 7, '4': 1, '5': 9, '10': 'attach'},
  ],
  '8': [
    {'1': 'image'},
  ],
};

/// Descriptor for `ImgMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List imgMsgDescriptor = $convert.base64Decode(
    'CgZJbWdNc2cSHAoJdGh1bWJuYWlsGAEgASgMUgl0aHVtYm5haWwSEgoDdXJsGAIgASgJSABSA3'
    'VybBIaCgdpbWFnZUlkGAMgASgJSABSB2ltYWdlSWQSIgoMdGh1bWJuYWlsVXJsGAQgASgJUgx0'
    'aHVtYm5haWxVcmwSFAoFd2lkdGgYBSABKAJSBXdpZHRoEhYKBmhlaWdodBgGIAEoAlIGaGVpZ2'
    'h0EhYKBmF0dGFjaBgHIAEoCVIGYXR0YWNoQgcKBWltYWdl');

@$core.Deprecated('Use voiceDescriptor instead')
const Voice$json = {
  '1': 'Voice',
  '2': [
    {'1': 'url', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'url'},
    {'1': 'voiceId', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'voiceId'},
    {'1': 'duration', '3': 2, '4': 1, '5': 13, '10': 'duration'},
    {'1': 'attach', '3': 4, '4': 1, '5': 9, '10': 'attach'},
  ],
  '8': [
    {'1': 'voice'},
  ],
};

/// Descriptor for `Voice`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List voiceDescriptor = $convert.base64Decode(
    'CgVWb2ljZRISCgN1cmwYASABKAlIAFIDdXJsEhoKB3ZvaWNlSWQYAyABKAlIAFIHdm9pY2VJZB'
    'IaCghkdXJhdGlvbhgCIAEoDVIIZHVyYXRpb24SFgoGYXR0YWNoGAQgASgJUgZhdHRhY2hCBwoF'
    'dm9pY2U=');

@$core.Deprecated('Use locationDescriptor instead')
const Location$json = {
  '1': 'Location',
  '2': [
    {'1': 'title', '3': 1, '4': 1, '5': 9, '10': 'title'},
    {'1': 'address', '3': 2, '4': 1, '5': 9, '10': 'address'},
    {'1': 'latitude', '3': 3, '4': 1, '5': 1, '10': 'latitude'},
    {'1': 'longitude', '3': 4, '4': 1, '5': 1, '10': 'longitude'},
    {'1': 'uri', '3': 5, '4': 1, '5': 9, '10': 'uri'},
    {'1': 'attach', '3': 6, '4': 1, '5': 9, '10': 'attach'},
  ],
};

/// Descriptor for `Location`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List locationDescriptor = $convert.base64Decode(
    'CghMb2NhdGlvbhIUCgV0aXRsZRgBIAEoCVIFdGl0bGUSGAoHYWRkcmVzcxgCIAEoCVIHYWRkcm'
    'VzcxIaCghsYXRpdHVkZRgDIAEoAVIIbGF0aXR1ZGUSHAoJbG9uZ2l0dWRlGAQgASgBUglsb25n'
    'aXR1ZGUSEAoDdXJpGAUgASgJUgN1cmkSFgoGYXR0YWNoGAYgASgJUgZhdHRhY2g=');

@$core.Deprecated('Use videoMsgDescriptor instead')
const VideoMsg$json = {
  '1': 'VideoMsg',
  '2': [
    {'1': 'cover', '3': 1, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ImgMsg', '10': 'cover'},
    {'1': 'fileSize', '3': 4, '4': 1, '5': 4, '10': 'fileSize'},
    {'1': 'duration', '3': 5, '4': 1, '5': 13, '10': 'duration'},
    {'1': 'attach', '3': 6, '4': 1, '5': 9, '10': 'attach'},
    {'1': 'url', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'url'},
    {'1': 'fileId', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'fileId'},
  ],
  '8': [
    {'1': 'file'},
  ],
};

/// Descriptor for `VideoMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List videoMsgDescriptor = $convert.base64Decode(
    'CghWaWRlb01zZxIzCgVjb3ZlchgBIAEoCzIdLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5JbWdNc2'
    'dSBWNvdmVyEhoKCGZpbGVTaXplGAQgASgEUghmaWxlU2l6ZRIaCghkdXJhdGlvbhgFIAEoDVII'
    'ZHVyYXRpb24SFgoGYXR0YWNoGAYgASgJUgZhdHRhY2gSEgoDdXJsGAIgASgJSABSA3VybBIYCg'
    'ZmaWxlSWQYAyABKAlIAFIGZmlsZUlkQgYKBGZpbGU=');

@$core.Deprecated('Use fileMsgDescriptor instead')
const FileMsg$json = {
  '1': 'FileMsg',
  '2': [
    {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    {'1': 'fileSize', '3': 4, '4': 1, '5': 4, '10': 'fileSize'},
    {'1': 'url', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'url'},
    {'1': 'fileId', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'fileId'},
    {'1': 'attach', '3': 5, '4': 1, '5': 9, '10': 'attach'},
  ],
  '8': [
    {'1': 'file'},
  ],
};

/// Descriptor for `FileMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileMsgDescriptor = $convert.base64Decode(
    'CgdGaWxlTXNnEhIKBG5hbWUYASABKAlSBG5hbWUSGgoIZmlsZVNpemUYBCABKARSCGZpbGVTaX'
    'plEhIKA3VybBgCIAEoCUgAUgN1cmwSGAoGZmlsZUlkGAMgASgJSABSBmZpbGVJZBIWCgZhdHRh'
    'Y2gYBSABKAlSBmF0dGFjaEIGCgRmaWxl');

@$core.Deprecated('Use userInfoDescriptor instead')
const UserInfo$json = {
  '1': 'UserInfo',
  '2': [
    {'1': 'nickname', '3': 1, '4': 1, '5': 9, '10': 'nickname'},
    {'1': 'userId', '3': 2, '4': 1, '5': 9, '10': 'userId'},
    {'1': 'avatar', '3': 3, '4': 1, '5': 9, '10': 'avatar'},
    {'1': 'imUserId', '3': 4, '4': 1, '5': 9, '10': 'imUserId'},
  ],
};

/// Descriptor for `UserInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List userInfoDescriptor = $convert.base64Decode(
    'CghVc2VySW5mbxIaCghuaWNrbmFtZRgBIAEoCVIIbmlja25hbWUSFgoGdXNlcklkGAIgASgJUg'
    'Z1c2VySWQSFgoGYXZhdGFyGAMgASgJUgZhdmF0YXISGgoIaW1Vc2VySWQYBCABKAlSCGltVXNl'
    'cklk');

@$core.Deprecated('Use audioAndVideoCallDescriptor instead')
const AudioAndVideoCall$json = {
  '1': 'AudioAndVideoCall',
  '2': [
    {'1': 'durationType', '3': 1, '4': 1, '5': 14, '6': '.com.joinu.im.protobuf.AudioAndVideoCall.DurationType', '10': 'durationType'},
    {'1': 'content', '3': 2, '4': 1, '5': 9, '10': 'content'},
    {'1': 'metingId', '3': 3, '4': 1, '5': 9, '10': 'metingId'},
    {'1': 'attach', '3': 4, '4': 1, '5': 9, '10': 'attach'},
  ],
  '4': [AudioAndVideoCall_DurationType$json],
};

@$core.Deprecated('Use audioAndVideoCallDescriptor instead')
const AudioAndVideoCall_DurationType$json = {
  '1': 'DurationType',
  '2': [
    {'1': 'NULL', '2': 0},
    {'1': 'VOICE', '2': 1},
    {'1': 'VIDEO', '2': 2},
  ],
};

/// Descriptor for `AudioAndVideoCall`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List audioAndVideoCallDescriptor = $convert.base64Decode(
    'ChFBdWRpb0FuZFZpZGVvQ2FsbBJZCgxkdXJhdGlvblR5cGUYASABKA4yNS5jb20uam9pbnUuaW'
    '0ucHJvdG9idWYuQXVkaW9BbmRWaWRlb0NhbGwuRHVyYXRpb25UeXBlUgxkdXJhdGlvblR5cGUS'
    'GAoHY29udGVudBgCIAEoCVIHY29udGVudBIaCghtZXRpbmdJZBgDIAEoCVIIbWV0aW5nSWQSFg'
    'oGYXR0YWNoGAQgASgJUgZhdHRhY2giLgoMRHVyYXRpb25UeXBlEggKBE5VTEwQABIJCgVWT0lD'
    'RRABEgkKBVZJREVPEAI=');

@$core.Deprecated('Use withdrawDescriptor instead')
const Withdraw$json = {
  '1': 'Withdraw',
  '2': [
    {'1': 'msgId', '3': 1, '4': 1, '5': 9, '10': 'msgId'},
  ],
};

/// Descriptor for `Withdraw`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List withdrawDescriptor = $convert.base64Decode(
    'CghXaXRoZHJhdxIUCgVtc2dJZBgBIAEoCVIFbXNnSWQ=');

@$core.Deprecated('Use readMsgDescriptor instead')
const ReadMsg$json = {
  '1': 'ReadMsg',
  '2': [
    {'1': 'msgId', '3': 1, '4': 3, '5': 9, '10': 'msgId'},
  ],
};

/// Descriptor for `ReadMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List readMsgDescriptor = $convert.base64Decode(
    'CgdSZWFkTXNnEhQKBW1zZ0lkGAEgAygJUgVtc2dJZA==');

@$core.Deprecated('Use c2CMsgDescriptor instead')
const C2CMsg$json = {
  '1': 'C2CMsg',
  '2': [
    {'1': 'from', '3': 1, '4': 1, '5': 9, '10': 'from'},
    {'1': 'conversationId', '3': 4, '4': 1, '5': 13, '10': 'conversationId'},
    {'1': 'receiver', '3': 16, '4': 1, '5': 9, '10': 'receiver'},
    {'1': 'msg', '3': 6, '4': 1, '5': 9, '9': 0, '10': 'msg'},
    {'1': 'image', '3': 7, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ImgMsg', '9': 0, '10': 'image'},
    {'1': 'voice', '3': 8, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Voice', '9': 0, '10': 'voice'},
    {'1': 'location', '3': 9, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Location', '9': 0, '10': 'location'},
    {'1': 'video', '3': 10, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.VideoMsg', '9': 0, '10': 'video'},
    {'1': 'file', '3': 11, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.FileMsg', '9': 0, '10': 'file'},
    {'1': 'withdraw', '3': 12, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Withdraw', '9': 0, '10': 'withdraw'},
    {'1': 'read', '3': 13, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ReadMsg', '9': 0, '10': 'read'},
    {'1': 'ext', '3': 14, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ExtMsg', '9': 0, '10': 'ext'},
    {'1': 'call', '3': 15, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.AudioAndVideoCall', '9': 0, '10': 'call'},
  ],
  '8': [
    {'1': 'Context'},
  ],
};

/// Descriptor for `C2CMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List c2CMsgDescriptor = $convert.base64Decode(
    'CgZDMkNNc2cSEgoEZnJvbRgBIAEoCVIEZnJvbRImCg5jb252ZXJzYXRpb25JZBgEIAEoDVIOY2'
    '9udmVyc2F0aW9uSWQSGgoIcmVjZWl2ZXIYECABKAlSCHJlY2VpdmVyEhIKA21zZxgGIAEoCUgA'
    'UgNtc2cSNQoFaW1hZ2UYByABKAsyHS5jb20uam9pbnUuaW0ucHJvdG9idWYuSW1nTXNnSABSBW'
    'ltYWdlEjQKBXZvaWNlGAggASgLMhwuY29tLmpvaW51LmltLnByb3RvYnVmLlZvaWNlSABSBXZv'
    'aWNlEj0KCGxvY2F0aW9uGAkgASgLMh8uY29tLmpvaW51LmltLnByb3RvYnVmLkxvY2F0aW9uSA'
    'BSCGxvY2F0aW9uEjcKBXZpZGVvGAogASgLMh8uY29tLmpvaW51LmltLnByb3RvYnVmLlZpZGVv'
    'TXNnSABSBXZpZGVvEjQKBGZpbGUYCyABKAsyHi5jb20uam9pbnUuaW0ucHJvdG9idWYuRmlsZU'
    '1zZ0gAUgRmaWxlEj0KCHdpdGhkcmF3GAwgASgLMh8uY29tLmpvaW51LmltLnByb3RvYnVmLldp'
    'dGhkcmF3SABSCHdpdGhkcmF3EjQKBHJlYWQYDSABKAsyHi5jb20uam9pbnUuaW0ucHJvdG9idW'
    'YuUmVhZE1zZ0gAUgRyZWFkEjEKA2V4dBgOIAEoCzIdLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5F'
    'eHRNc2dIAFIDZXh0Ej4KBGNhbGwYDyABKAsyKC5jb20uam9pbnUuaW0ucHJvdG9idWYuQXVkaW'
    '9BbmRWaWRlb0NhbGxIAFIEY2FsbEIJCgdDb250ZXh0');

@$core.Deprecated('Use c2CMsgRequestDescriptor instead')
const C2CMsgRequest$json = {
  '1': 'C2CMsgRequest',
  '2': [
    {'1': 'receiver', '3': 1, '4': 1, '5': 9, '10': 'receiver'},
    {'1': 'msg', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'msg'},
    {'1': 'image', '3': 4, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ImgMsg', '9': 0, '10': 'image'},
    {'1': 'voice', '3': 5, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Voice', '9': 0, '10': 'voice'},
    {'1': 'location', '3': 6, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Location', '9': 0, '10': 'location'},
    {'1': 'withdraw', '3': 7, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Withdraw', '9': 0, '10': 'withdraw'},
    {'1': 'read', '3': 8, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ReadMsg', '9': 0, '10': 'read'},
    {'1': 'ext', '3': 9, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ExtMsg', '9': 0, '10': 'ext'},
    {'1': 'video', '3': 10, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.VideoMsg', '9': 0, '10': 'video'},
    {'1': 'file', '3': 11, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.FileMsg', '9': 0, '10': 'file'},
    {'1': 'call', '3': 12, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.AudioAndVideoCall', '9': 0, '10': 'call'},
  ],
  '8': [
    {'1': 'Context'},
  ],
};

/// Descriptor for `C2CMsgRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List c2CMsgRequestDescriptor = $convert.base64Decode(
    'Cg1DMkNNc2dSZXF1ZXN0EhoKCHJlY2VpdmVyGAEgASgJUghyZWNlaXZlchISCgNtc2cYAyABKA'
    'lIAFIDbXNnEjUKBWltYWdlGAQgASgLMh0uY29tLmpvaW51LmltLnByb3RvYnVmLkltZ01zZ0gA'
    'UgVpbWFnZRI0CgV2b2ljZRgFIAEoCzIcLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5Wb2ljZUgAUg'
    'V2b2ljZRI9Cghsb2NhdGlvbhgGIAEoCzIfLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5Mb2NhdGlv'
    'bkgAUghsb2NhdGlvbhI9Cgh3aXRoZHJhdxgHIAEoCzIfLmNvbS5qb2ludS5pbS5wcm90b2J1Zi'
    '5XaXRoZHJhd0gAUgh3aXRoZHJhdxI0CgRyZWFkGAggASgLMh4uY29tLmpvaW51LmltLnByb3Rv'
    'YnVmLlJlYWRNc2dIAFIEcmVhZBIxCgNleHQYCSABKAsyHS5jb20uam9pbnUuaW0ucHJvdG9idW'
    'YuRXh0TXNnSABSA2V4dBI3CgV2aWRlbxgKIAEoCzIfLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5W'
    'aWRlb01zZ0gAUgV2aWRlbxI0CgRmaWxlGAsgASgLMh4uY29tLmpvaW51LmltLnByb3RvYnVmLk'
    'ZpbGVNc2dIAFIEZmlsZRI+CgRjYWxsGAwgASgLMiguY29tLmpvaW51LmltLnByb3RvYnVmLkF1'
    'ZGlvQW5kVmlkZW9DYWxsSABSBGNhbGxCCQoHQ29udGV4dA==');

@$core.Deprecated('Use c2CMsgResponseDescriptor instead')
const C2CMsgResponse$json = {
  '1': 'C2CMsgResponse',
  '2': [
    {'1': 'receiver', '3': 1, '4': 1, '5': 9, '10': 'receiver'},
    {'1': 'success', '3': 2, '4': 1, '5': 8, '10': 'success'},
    {'1': 'failType', '3': 4, '4': 1, '5': 14, '6': '.com.joinu.im.protobuf.FailType', '10': 'failType'},
    {'1': 'failMessage', '3': 5, '4': 1, '5': 9, '10': 'failMessage'},
  ],
};

/// Descriptor for `C2CMsgResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List c2CMsgResponseDescriptor = $convert.base64Decode(
    'Cg5DMkNNc2dSZXNwb25zZRIaCghyZWNlaXZlchgBIAEoCVIIcmVjZWl2ZXISGAoHc3VjY2Vzcx'
    'gCIAEoCFIHc3VjY2VzcxI7CghmYWlsVHlwZRgEIAEoDjIfLmNvbS5qb2ludS5pbS5wcm90b2J1'
    'Zi5GYWlsVHlwZVIIZmFpbFR5cGUSIAoLZmFpbE1lc3NhZ2UYBSABKAlSC2ZhaWxNZXNzYWdl');

@$core.Deprecated('Use pushMsgDescriptor instead')
const PushMsg$json = {
  '1': 'PushMsg',
  '2': [
    {'1': 'msg', '3': 1, '4': 1, '5': 9, '10': 'msg'},
    {'1': 'type', '3': 2, '4': 1, '5': 5, '10': 'type'},
  ],
};

/// Descriptor for `PushMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pushMsgDescriptor = $convert.base64Decode(
    'CgdQdXNoTXNnEhAKA21zZxgBIAEoCVIDbXNnEhIKBHR5cGUYAiABKAVSBHR5cGU=');

@$core.Deprecated('Use groupMsgDescriptor instead')
const GroupMsg$json = {
  '1': 'GroupMsg',
  '2': [
    {'1': 'appId', '3': 14, '4': 1, '5': 9, '10': 'appId'},
    {'1': 'groupId', '3': 5, '4': 1, '5': 9, '10': 'groupId'},
    {'1': 'groupName', '3': 12, '4': 1, '5': 9, '10': 'groupName'},
    {'1': 'groupLogo', '3': 13, '4': 1, '5': 9, '10': 'groupLogo'},
    {'1': 'msg', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'msg'},
    {'1': 'image', '3': 2, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ImgMsg', '9': 0, '10': 'image'},
    {'1': 'voice', '3': 3, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Voice', '9': 0, '10': 'voice'},
    {'1': 'location', '3': 4, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Location', '9': 0, '10': 'location'},
    {'1': 'ext', '3': 6, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ExtMsg', '9': 0, '10': 'ext'},
    {'1': 'withdraw', '3': 7, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Withdraw', '9': 0, '10': 'withdraw'},
    {'1': 'video', '3': 8, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.VideoMsg', '9': 0, '10': 'video'},
    {'1': 'file', '3': 9, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.FileMsg', '9': 0, '10': 'file'},
    {'1': 'call', '3': 10, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.AudioAndVideoCall', '9': 0, '10': 'call'},
    {'1': 'noticeMsg', '3': 11, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.NoticeMsg', '9': 0, '10': 'noticeMsg'},
  ],
  '8': [
    {'1': 'Context'},
  ],
};

/// Descriptor for `GroupMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List groupMsgDescriptor = $convert.base64Decode(
    'CghHcm91cE1zZxIUCgVhcHBJZBgOIAEoCVIFYXBwSWQSGAoHZ3JvdXBJZBgFIAEoCVIHZ3JvdX'
    'BJZBIcCglncm91cE5hbWUYDCABKAlSCWdyb3VwTmFtZRIcCglncm91cExvZ28YDSABKAlSCWdy'
    'b3VwTG9nbxISCgNtc2cYASABKAlIAFIDbXNnEjUKBWltYWdlGAIgASgLMh0uY29tLmpvaW51Lm'
    'ltLnByb3RvYnVmLkltZ01zZ0gAUgVpbWFnZRI0CgV2b2ljZRgDIAEoCzIcLmNvbS5qb2ludS5p'
    'bS5wcm90b2J1Zi5Wb2ljZUgAUgV2b2ljZRI9Cghsb2NhdGlvbhgEIAEoCzIfLmNvbS5qb2ludS'
    '5pbS5wcm90b2J1Zi5Mb2NhdGlvbkgAUghsb2NhdGlvbhIxCgNleHQYBiABKAsyHS5jb20uam9p'
    'bnUuaW0ucHJvdG9idWYuRXh0TXNnSABSA2V4dBI9Cgh3aXRoZHJhdxgHIAEoCzIfLmNvbS5qb2'
    'ludS5pbS5wcm90b2J1Zi5XaXRoZHJhd0gAUgh3aXRoZHJhdxI3CgV2aWRlbxgIIAEoCzIfLmNv'
    'bS5qb2ludS5pbS5wcm90b2J1Zi5WaWRlb01zZ0gAUgV2aWRlbxI0CgRmaWxlGAkgASgLMh4uY2'
    '9tLmpvaW51LmltLnByb3RvYnVmLkZpbGVNc2dIAFIEZmlsZRI+CgRjYWxsGAogASgLMiguY29t'
    'LmpvaW51LmltLnByb3RvYnVmLkF1ZGlvQW5kVmlkZW9DYWxsSABSBGNhbGwSQAoJbm90aWNlTX'
    'NnGAsgASgLMiAuY29tLmpvaW51LmltLnByb3RvYnVmLk5vdGljZU1zZ0gAUglub3RpY2VNc2dC'
    'CQoHQ29udGV4dA==');

@$core.Deprecated('Use extMsgDescriptor instead')
const ExtMsg$json = {
  '1': 'ExtMsg',
  '2': [
    {'1': 'ext', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.Any', '10': 'ext'},
    {'1': 'ext1', '3': 2, '4': 1, '5': 9, '10': 'ext1'},
    {'1': 'ext2', '3': 3, '4': 1, '5': 9, '10': 'ext2'},
    {'1': 'ext3', '3': 4, '4': 1, '5': 9, '10': 'ext3'},
  ],
};

/// Descriptor for `ExtMsg`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List extMsgDescriptor = $convert.base64Decode(
    'CgZFeHRNc2cSJgoDZXh0GAEgASgLMhQuZ29vZ2xlLnByb3RvYnVmLkFueVIDZXh0EhIKBGV4dD'
    'EYAiABKAlSBGV4dDESEgoEZXh0MhgDIAEoCVIEZXh0MhISCgRleHQzGAQgASgJUgRleHQz');

@$core.Deprecated('Use groupMsgRequestDescriptor instead')
const GroupMsgRequest$json = {
  '1': 'GroupMsgRequest',
  '2': [
    {'1': 'groupId', '3': 5, '4': 1, '5': 9, '10': 'groupId'},
    {'1': 'groupName', '3': 11, '4': 1, '5': 9, '10': 'groupName'},
    {'1': 'groupLogo', '3': 12, '4': 1, '5': 9, '10': 'groupLogo'},
    {'1': 'msg', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'msg'},
    {'1': 'image', '3': 2, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ImgMsg', '9': 0, '10': 'image'},
    {'1': 'voice', '3': 3, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Voice', '9': 0, '10': 'voice'},
    {'1': 'location', '3': 4, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Location', '9': 0, '10': 'location'},
    {'1': 'ext', '3': 6, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.ExtMsg', '9': 0, '10': 'ext'},
    {'1': 'withdraw', '3': 7, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.Withdraw', '9': 0, '10': 'withdraw'},
    {'1': 'video', '3': 8, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.VideoMsg', '9': 0, '10': 'video'},
    {'1': 'file', '3': 9, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.FileMsg', '9': 0, '10': 'file'},
    {'1': 'call', '3': 10, '4': 1, '5': 11, '6': '.com.joinu.im.protobuf.AudioAndVideoCall', '9': 0, '10': 'call'},
  ],
  '8': [
    {'1': 'Context'},
  ],
};

/// Descriptor for `GroupMsgRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List groupMsgRequestDescriptor = $convert.base64Decode(
    'Cg9Hcm91cE1zZ1JlcXVlc3QSGAoHZ3JvdXBJZBgFIAEoCVIHZ3JvdXBJZBIcCglncm91cE5hbW'
    'UYCyABKAlSCWdyb3VwTmFtZRIcCglncm91cExvZ28YDCABKAlSCWdyb3VwTG9nbxISCgNtc2cY'
    'ASABKAlIAFIDbXNnEjUKBWltYWdlGAIgASgLMh0uY29tLmpvaW51LmltLnByb3RvYnVmLkltZ0'
    '1zZ0gAUgVpbWFnZRI0CgV2b2ljZRgDIAEoCzIcLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5Wb2lj'
    'ZUgAUgV2b2ljZRI9Cghsb2NhdGlvbhgEIAEoCzIfLmNvbS5qb2ludS5pbS5wcm90b2J1Zi5Mb2'
    'NhdGlvbkgAUghsb2NhdGlvbhIxCgNleHQYBiABKAsyHS5jb20uam9pbnUuaW0ucHJvdG9idWYu'
    'RXh0TXNnSABSA2V4dBI9Cgh3aXRoZHJhdxgHIAEoCzIfLmNvbS5qb2ludS5pbS5wcm90b2J1Zi'
    '5XaXRoZHJhd0gAUgh3aXRoZHJhdxI3CgV2aWRlbxgIIAEoCzIfLmNvbS5qb2ludS5pbS5wcm90'
    'b2J1Zi5WaWRlb01zZ0gAUgV2aWRlbxI0CgRmaWxlGAkgASgLMh4uY29tLmpvaW51LmltLnByb3'
    'RvYnVmLkZpbGVNc2dIAFIEZmlsZRI+CgRjYWxsGAogASgLMiguY29tLmpvaW51LmltLnByb3Rv'
    'YnVmLkF1ZGlvQW5kVmlkZW9DYWxsSABSBGNhbGxCCQoHQ29udGV4dA==');

@$core.Deprecated('Use groupMsgResponseDescriptor instead')
const GroupMsgResponse$json = {
  '1': 'GroupMsgResponse',
  '2': [
    {'1': 'groupId', '3': 1, '4': 1, '5': 9, '10': 'groupId'},
    {'1': 'success', '3': 2, '4': 1, '5': 8, '10': 'success'},
    {'1': 'failType', '3': 4, '4': 1, '5': 14, '6': '.com.joinu.im.protobuf.FailType', '10': 'failType'},
    {'1': 'failMessage', '3': 5, '4': 1, '5': 9, '10': 'failMessage'},
  ],
};

/// Descriptor for `GroupMsgResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List groupMsgResponseDescriptor = $convert.base64Decode(
    'ChBHcm91cE1zZ1Jlc3BvbnNlEhgKB2dyb3VwSWQYASABKAlSB2dyb3VwSWQSGAoHc3VjY2Vzcx'
    'gCIAEoCFIHc3VjY2VzcxI7CghmYWlsVHlwZRgEIAEoDjIfLmNvbS5qb2ludS5pbS5wcm90b2J1'
    'Zi5GYWlsVHlwZVIIZmFpbFR5cGUSIAoLZmFpbE1lc3NhZ2UYBSABKAlSC2ZhaWxNZXNzYWdl');

