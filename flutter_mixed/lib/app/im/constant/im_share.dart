
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

class ImShareValues {

  static String _imToken = "";

  static saveImtoken(String imtoken) async {
    await UserDefault.setData('imToken', imtoken);
    _imToken = imtoken;
  }

  static getImtoken() async {
    if(!StringUtil.isEmpty(_imToken)){
      return _imToken;
    }
    var imtoken = await UserDefault.getData('imToken');
    return imtoken;
  }

  static clearImToken() async {
    await UserDefault.setData('imToken', '');
    _imToken = '';
  }

}