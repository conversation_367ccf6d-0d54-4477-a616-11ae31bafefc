import 'dart:collection';

import '../../../res/assets_res.dart';
import 'constant_tcp_util.dart';

class ConstantImMsgType {
  static const int OffLineSessionMsgType =
      999991; // 注：个人单独添加的类型，为离线拉取会话列表后，每条会话中的消息类型；
  static const int OffLineNoticeSessionMsgType =
      999992; // 注：个人单独添加的类型，为离线拉取会话列表后，每条会话中的消息类型；
  static const int SSChatMessageTypeBlack =
      999993; // 注：个人单独添加的类型，为我发送消息时，发现对方把我添加进黑名单列表了，此时会在本地显示一个灰条消息；
  //----------------------------------------------------工作通知的classType----------------------------------------------------------------
  static const int SSChatWorkNoticeClassifyAttendance =
      1; // 考勤提醒    20001 - 20099
  static const int SSChatWorkNoticeClassifyProject = 2; // 项目通知    20100 - 20199
  static const int SSChatWorkNoticeClassifyTask = 3; // 任务通知    20200 - 20299
  static const int SSChatWorkNoticeClassifyWorkReport =
      4; // 工作汇报    20300 - 20399
  static const int SSChatWorkNoticeClassifyAudioVideoMeeting =
      5; // 音/视频会议 20400 - 20499
  static const int SSChatWorkNoticeClassifyAnnouncement =
      6; // 公告提醒    20500 - 20599
  static const int SSChatWorkNoticeClassifyPermissionsChange =
      7; // 权限变更    20600 - 20699
  static const int SSChatWorkNoticeClassifyDigitalReport =
      8; // 企业数字报告 20700 - 20799
  static const int SSChatWorkNoticeClassifyCommentRemind =
      9; // 评论提醒     20800 - 20899
  static const int SSChatWorkNoticeClassifyCooperationMessage =
      10; // 协作消息    20900 - 20999

  //-----------------------------------同步指令类型------------------------------------------------------------
  static const int SSChatMessageHandleTypeAnswer = 1; // 音视频通话-主动接听
  static const int SSChatMessageHandleTypeHangup = 2; // 音视频通话-主动挂断
  static const int SSChatMessageHandleTypeSessionUnread = 3; // 单聊群组会话-未读数
  static const int SSChatMessageHandleTypeSingleSynchronization = 4; // 单聊会话同步指令
  static const int SSChatMessageHandleTypeThreeNoticeDisturb =
      5; // 系统，工作，审批通知的，消息免打扰设置同步指令
  static const int SSChatMessageHandleTypeConversationTop = 6; //置顶聊天设置同步指令
  static const int SSChatMessageHandleTypeApproveRedPoint = 7; //审批红点同步指令

  //-------------------------------------------以下类型与iOS端同步---------------------------------------------------------
  static const int SSChatMessageTypeServerError = 0; // 服务器的错误消息
  static const int SSChatMessageTypeText = 1; // 发送文本消息
  static const int SSChatMessageTypeVoice = 2; // 发送语音消息
  static const int SSChatMessageTypeImage = 3; // 发送图片消息
  static const int SSChatMessageTypeGifImage = 4; // 发gif动图
  static const int SSChatMessageTypeVideo = 5; // 发送小视频
  static const int SSChatMessageTypeFile = 6; // 发文件 word,zip之类
  static const int SSChatMessageTypeMap = 7; // 发送地图定位
  static const int SSChatMessageTypeOfflineFault = 8; // 离线消息断层--本地使用
  static const int SSChatMessageTypeUndo = 9; // 撤销的消息--指令
  static const int SSChatMessageTypeReadOthers = 10; // 对方已读消息--指令=不计离线
  static const int SSChatMessageTypeInitiateVoiceVideo = 11; // 发起音视频通话
  static const int SSChatMessageTypeVoiceVideoEnd = 12; // 音视频通话 正常结束
  static const int SSChatMessageTypeVoiceVideoCallFailure = 13; // 音视频通话失败 未接听
  static const int SSChatMessageTypeVoiceVideoRefused = 14; // callContent=1已在通话会议中拒绝，2接收者主动挂断--指令=不计离线
  static const int SSChatMessageTypeVoiceVideoTwoInitiate = 15; // 群组2次邀请通话人员--指令=不计离线
  static const int SSChatMessageTypeGroupMoreMemberNotice = 16; // 群组@普通消息1-10人(实质是文本消息)
  static const int SSChatMessageTypeGroupAllMemberNotice = 17; // 群组管理员@全体成员(实质是文本消息)
  static const int SSChatMessageTypeValidation = 18; // 单聊、群组验证的-本地使用
  static const int SSChatMessageTypeReachTop = 19; // 消息到达顶-本地使用
  static const int SSChatMessageTypeBlacklistChange = 20; // 黑名单改变--指令=不计离线
  static const int SSChatMessageTypeMoreLoginNoticeChange =
      21; // 多端登录通知自己设备的变化--单聊指令=不计离线(handleType=1音视频通话-主动接听/2音视频通话-主动挂断/3单聊群组会话-未读数/4单聊会话同步指令/5系统、工作、审批通知的消息免打扰设置, handleId, appChatId, name, headerUrl, switchStatus)

  // modified by ljt
  static const int SSChatMessageTypeDel = 22; // 删除消息（仅本地？）
  static const int SSChatMessageTypeQuote = 23; // 引用类型
  static const int SSChatMessageTypeClearChatRecord = 24; // 清空聊天记录的断层标记，遇到此标记，不再拉取离线数据

  // 会议类型
  static const int SSChatMessageTypeMeeting = 25; // （单群聊）会议类型
  static const int SSChatMessageTypeForwardRecord = 26; //  消息转发记录

  /**
   * ----------IM自定义消息类型 Start----------
   **/
  static const int SSChatMessageTypeInvite = 100; // 邀请加入公司的消息
  static const int SSChatMessageTypeReportShare = 101; // 汇报分享
  static const int SSChatMessageTypePanShareFile = 102; // 云文档分享好友文件
  static const int SSChatMessageTypeRobot = 999; // 群聊机器人
  static const int SSChatMessageTypeDraft = -999; // 草稿
  /**
   * ----------IM自定义消息类型 End----------
   **/

  static const int SSChatConversationTypeChat = 1; //单聊
  static const int SSChatConversationTypeGroupChat = 2; //群聊

  // =======================================系统通知
  /**
   * 系统消息
   */

  static const int SSChatMessageTypeRefreshWorkRedPoint =
      8001; //刷新工作台红点与工作台各个公司消息总数红点
  static const int SSChatMessageTypeRefreshAllOrgList = 8100; //刷新全部公司

  static const int SystemMsgSessionType = 10000; //用作sessionType
  static const int SystemMsgFriendAdd = 11001; // 对方添加好友-已完成
  static const int SystemMsgFriendChange = 11002; // 好友变化--指令=不计离线-已完成

  static const int SystemMsgCompanyApply = 11003; // 公司加入申请-已完成
  static const int SystemMsgCompanyRemove = 11004; // 被公司移除-已完成
  static const int SystemMsgCompanyDissolution = 11005; // 公司解散
  static const int SSChatMessageTypeCompanyMobileDepartment =
      11006; // 被公司移动部门-已完成
  static const int SSChatMessageTypeCompanyJoinedTeam = 11007; // 已加入团队-已完成
  static const int SSChatMessageTypeCompanyeExitTeam = 11008; // 主动退出团队
  static const int SSChatMessageTypeCompanyeMemberExitTeam =
      11009; // 管理员收到成员退出团队
  static const int SSChatMessageTypeCompanyeDeptResponsible =
      11010; // 被任命为部门负责人
  static const int SSChatMessageTypeCompanyeDeptWithdrawResponsible =
      11011; // 被撤销部门负责人

  static const int SSChatMessageTypeExternalCollaboration = 11020; // 外部协作人员申请
  static const int SSChatMessageTypeExternalCollaborationDelete =
      11021; // 被团队删除外部协作人的身份--指令=不记离线

  static const int SSChatMessageTypeChatGroupKicked = 11030; // 被踢出群组(系统通知)-已完成
  static const int SSChatMessageTypeChatGroupDissolution =
      11031; // 群组解散(系统通知)-已完成
  static const int SSChatMessageTypeChatExitGroup =
      11032; // 主动退出群组(系统通知)-不计离线-已完成

  static const int SSChatMessageTypeUserLogoutAccount =
      11040; // 用户注销账号--指令--不计离线

  static const int SSChatMessageTypeAudioVideoMeeting = 11100; // 音视频会议--指令=不计离线

  static const int SSChatMessageTypePendingList = 11101; // 通讯录-待处理条数--指令=不计离线

  static const int SSChatMessageTypeSafetyPrompt = 12000; //安全提示

  // ====================================工作通知
  /**
   * 工作通知
   */
  static const int WorkMsgSessionType = 20000;

  // 考勤提醒     20001 - 20099
  static const int SSChatMessageTypeWorkAttendanceRuleChange =
      20001; // 考勤规则变更-已完成
  static const int SSChatMessageTypeWorkAttendanceClockInRemind =
      20002; // 考勤打卡提醒-已完成
  static const int SSChatMessageTypeWorkAttendanceLackRemind =
      20003; // 缺卡提醒-已完成
  static const int SSChatMessageTypeWorkAttendanceSpecialWorkday =
      20004; // 特殊工作日调整-已完成

  // 项目通知    20100 - 20199
  static const int SSChatMessageTypeWorkJoinTheProject = 20100; // P0 已加入项目-已完成
  static const int SSChatMessageTypeWorkRemovedProject = 20101; // P1 已被移出项目-已完成
  static const int SSChatMessageTypeWorkSettingProjectLeader =
      20102; // P2 被设置成为项目负责人-已完成
  static const int SSChatMessageTypeWorkUndoProjectLeader =
      20103; // P3 撤销项目负责人身份-已完成
  static const int SSChatMessageTypeWorkProjectDelete = 20104; // P4 项目被删除
  static const int SSChatMessageTypeWorkProjectRecover = 20105; // P5 项目已恢复
  static const int SSChatMessageTypeWorkProjectCreator = 20106; // P6 成为项目创建者

  // 任务通知    20200 - 20299
  static const int SSChatMessageTypeWorkJoinTheTask =
      20200; // (T0抛弃) T10加入任务-已完成
  static const int SSChatMessageTypeWorkRemovedTask = 20201; // T1 移出任务
  static const int SSChatMessageTypeWorkSettingTaskLeader = 20202; // T2 设为负责人
  static const int SSChatMessageTypeWorkUndoTaskLeader = 20203; // T3 负责人身份撤销
  static const int SSChatMessageTypeWorkTaskDelete = 20204; // T4 任务被删除
  static const int SSChatMessageTypeWorkTaskRecover = 20205; // T5 任务已恢复
  static const int SSChatMessageTypeWorkTaskChange = 20206; // T6 修改任务内容
  static const int SSChatMessageTypeWorkTaskThoroughlyDelete =
      20207; // T7 任务被 彻底删除--指令=不计离线

  // 工作汇报 20300 - 20399
  static const int SSChatMessageTypeWorkSubmitReport =
      20300; // R0 有人提交了汇报 去详情-已完成
  static const int SSChatMessageTypeWorkInviteSubmitReport =
      20301; // R1 邀请提交汇报 去新建-已完成
  static const int SSChatMessageTypeWorkReportRemindStatistics =
      20302; // R2 员工收到汇报提醒
  static const int SSChatMessageTypeWorkReportComment =
      20303; // R3 汇报评论 去看评论-已完成
  static const int SSChatMessageTypeWorkReportRemindBoss =
      20304; // R2 汇报编辑人收到汇报提醒======================自己加的====

  // 音/视频会议 20400 - 20499
  static const int SSChatMessageTypeWorkRemindJoinMeeting =
      20400; // A0 预约邀请参加会议
  static const int SSChatMessageTypeWorkInviteJoinMeeting =
      20401; // A1 实时邀请参加会议
  static const int SSChatMessageTypeWorkRemoveMeeting = 20402; // A2 被移出会议
  static const int SSChatMessageTypeWorkMeetingCancel = 20403; // A3 发起人取消了会议

  // 公告提醒    20500 - 20599
  static const int SSChatMessageTypeWorkHaveNewAnnouncement = 20500; // 有新公告
  static const int SSChatMessageTypeWorkDeleteAnnouncement = 20501; // 公告被删除
  static const int SSChatMessageTypeWorkEditAnnouncement = 20502; // 公告被编辑

  // 权限变更    20600 - 20699
  static const int SSChatMessageTypeWorkPermissionsChange = 20600; // 权限变更
  static const int SSChatMessageTypeWorkCancelPermissions =
      20601; // 被取消权限--指令=不计离线

  // 企业数字报告 20700 - 20799
  static const int SSChatMessageTypeWorkDigitalDayReport = 20700; // C0 日报
  static const int SSChatMessageTypeWorkDigitalWeekReport = 20701; // C1 公司周报
  static const int SSChatMessageTypeWorkDigitalMonthReport = 20702; // C2 月报

  // 评论提醒    20800 - 20899
  static const int SSChatMessageTypeWorkTaskCommentRemind =
      20800; // M0 任务评论-已完成
  static const int SSChatMessageTypeWorkAprovalCommentRemind =
      20801; // M1 审批评论-已完成

  // 协作消息    20900 - 20999
  static const int SSChatMessageTypeWorkAddCollaborationFolder =
      20900; // D1 加入协作文件夹
  static const int SSChatMessageTypeWorkRemoveCollaborationFolder =
      20901; // D2 移出协作文件夹

  // ==============================审批通知
  /**
   * 审批通知
   */
  static const int ApprovalMsgSessionType = 30000;
  static const int SSChatMessageTypePendingApprovalRemind =
      30001; // 我审批的-待处理审批提醒
  static const int SSChatMessageTypeApprovalSucceedRemind =
      30002; // 我发起的-审批成功提醒
  static const int SSChatMessageTypeApprovalFailRemind = 30003; // 我发起的-审批失败提醒
  static const int SSChatMessageTypeApprovalWithdrawRemind =
      30004; // 我审批的-审批撤回提醒
  static const int SSChatMessageTypeApprovalSendBack = 30005; // 我审批的和我发起的-审批退回
  static const int SSChatMessageTypeApprovalPressRemind = 30006; // 我审批的-审批催办
  static const int SSChatMessageTypeApprovalCopySend = 30007; // 抄送我的-审批抄送
  static const int SSChatMessageTypeApprovalEverydayRemind =
      30008; // 我审批的-待审批每天提醒
  static const int SSChatMessageTypeApprovalKingDeeList = 31000; // 金蝶审批列表
  static const int SSChatMessageTypeApprovalKingDeeDetail = 31001; // 金蝶审批详情

  static const int ApproveIMRefreshRedDot = 9000;

  // ====================================合作团队通知
  /**
   * 合作团队通知
   */
  static const int TeamMsgSessionType = 40000;
  static const int SSChatMessageTypeTeamworkeAprovalComment =
      40001; // 合作团队审批评论通知

  static const int SSChatMessageTypeChatGroupNotice = 50000; // 群组通知
  static const int SSChatMessageTypeChatGroupInvitation = 50001; // 邀请入群-新创建也是邀请
  static const int SSChatMessageTypeChatGroupCreateChange = 50002; // 创建者更改
  static const int SSChatMessageTypeChatGroupOrganizationAdd = 50003; // 团队群组 加入
  static const int SSChatMessageTypeChatGroupSayHello = 50004; // 团队群组 打招呼
  static const int SSChatMessageTypeChatGroupNameChange = 50005; // 群组名称修改
  static const int SSChatMessageTypeChatGroupMemberChange =
      50006; // 群组成员变化--指令=不计离线
  static const int SSChatMessageTypeChatGroupOwnerConfigInfo =
      50007; // 群组消息-群主配置相关提示消息

  static const int TicketMsgSessionType = 60000; //工单通知
  static const int TicketMsgWithDraw = 60004; //工单审批撤回

  static const int MatterMsgSessionType = 70000; //库存通知
  static const int KingdeeMsgSessionType = 80000; //金蝶审批通知 ,HR 助手

  static const int TrainMsgSessionType = 90000; //培训通知
  static const int GeneralManagementSessionType = 110000; //综合管理平台

  static const int RangeParkMsgSessionType = 120000; //园区通知

  static const int IDCMsgSessionType = 130000; //IDC知识库
}

// 通知类型管理器， 防止遗忘类型
class NoticeTypeManager {
  static Map<int, String> noticeTypeMap = {
    ConstantImMsgType.SystemMsgSessionType:
        ConstantTcpUtil.TCP_SYSTEM_SESSIONID,
    ConstantImMsgType.ApprovalMsgSessionType:
        ConstantTcpUtil.TCP_APPROVAL_SESSIONID,
    ConstantImMsgType.KingdeeMsgSessionType:
        ConstantTcpUtil.TCP_KINGDEE_SESSIONID,
    ConstantImMsgType.TicketMsgSessionType:
        ConstantTcpUtil.TCP_TICKET_SESSIONID,
    ConstantImMsgType.TrainMsgSessionType: ConstantTcpUtil.TCP_TRAIN_SESSIONID,
    ConstantImMsgType.MatterMsgSessionType:
        ConstantTcpUtil.TCP_MATTER_SESSIONID,
    ConstantImMsgType.GeneralManagementSessionType:
        ConstantTcpUtil.TCP_GENER_MANAGEMENT_SESSIONID,
    ConstantImMsgType.RangeParkMsgSessionType:
        ConstantTcpUtil.TCP_RANGE_PARK_SESSIONID,
    ConstantImMsgType.IDCMsgSessionType:
        ConstantTcpUtil.TCP_IDC_SESSIONID,
  };
  static Map<int, String> noticeSwitchMap = {
    ConstantImMsgType.SystemMsgSessionType:
        'systemPushSwitch',
    ConstantImMsgType.ApprovalMsgSessionType:
        'approvalPushSwitch',
    ConstantImMsgType.KingdeeMsgSessionType:
        'kingdeePushSwitch',
    ConstantImMsgType.TicketMsgSessionType:
        'ticketPushSwitch',
    ConstantImMsgType.TrainMsgSessionType: 'trainingSwitch',
    ConstantImMsgType.MatterMsgSessionType:
        'inventorySwitch',
    ConstantImMsgType.GeneralManagementSessionType:
        'managementSwitch',
    ConstantImMsgType.RangeParkMsgSessionType:
        'rangeParkSwitch',
    ConstantImMsgType.IDCMsgSessionType:
        'idcSwitch',
  };

  static  String buildSessionAvatar(int sessionType) {
    switch (sessionType) {
      case ConstantImMsgType.SystemMsgSessionType:
        return AssetsRes.SYSTEM_NOTIFICATION_ICON;
      case ConstantImMsgType.ApprovalMsgSessionType:
        return AssetsRes.APPROVAL_SESSION_LOGO;
      case ConstantImMsgType.KingdeeMsgSessionType:
        return AssetsRes.IC_KINGDEE;
      case ConstantImMsgType.TicketMsgSessionType:
        return AssetsRes.IC_TICKET;
      case ConstantImMsgType.TrainMsgSessionType:
        return AssetsRes.IC_TRAIN;
      case ConstantImMsgType.MatterMsgSessionType:
        return AssetsRes.IC_MATTER;
      case ConstantImMsgType.TeamMsgSessionType:
        return AssetsRes.ICON_MSG_COOPERATION_COMPANY;
      case ConstantImMsgType.GeneralManagementSessionType:
        return AssetsRes.IC_MANAGERMENT;
      case ConstantImMsgType.RangeParkMsgSessionType:
        return AssetsRes.IC_RANGE_PARK;
      case ConstantImMsgType.IDCMsgSessionType:
        return AssetsRes.IC_IDC;
      default:
        return '';
    }
  }

  static Map<int, String> noticeNameMap = {
    ConstantImMsgType.SystemMsgSessionType:
        '系统通知',
    ConstantImMsgType.ApprovalMsgSessionType:
        '审批',
    ConstantImMsgType.KingdeeMsgSessionType:
        'HR消息助手',
    ConstantImMsgType.TicketMsgSessionType:
        '工单',
    ConstantImMsgType.TrainMsgSessionType: '培训提醒',
    ConstantImMsgType.MatterMsgSessionType:
        '库存',
    ConstantImMsgType.GeneralManagementSessionType:
        '综合管理平台',
    ConstantImMsgType.RangeParkMsgSessionType:
        '园区通知',
  };
}
