
class ConstantTcpUtil {
    //判断文件类型时用到的
    static final String VIDEO_TYPE = "video_type";
    static final String FILE_TYPE = "file_type";
    static final String PICTURE_TYPE = "picture_type";
    static final String VOICE_TYPE = "voice_type";
    //视频第一帧存放的文件夹
    static final String VIDEO_SHOT_SCREEN_FOLDER = "video_shot_screen_folder";
    //视频压缩后存放的文件夹
    static final String VIDEO_COMPRESSED_FOLDER = "video_compressed_folder";
    //收到图片消息下载后存放的文件夹
    static final String IMAGE_CACHE_FOLDER = "image_cache_folder";
    //选择图片，压缩后存放的文件夹
    static final String IMAGE_COMPRESSED_FOLDER = "image_compressed_folder";
    //语音消息下载后存放的文件夹
    static final String VOICE_CACHE_FOLDER = "voice_cache_folder";
    //文件消息下载后存放的文件夹
    static final String FILE_CACHE_FOLDER = "file_cache_folder";


    //系统通知的表名，也是系统通知的sessionID
    static final String TCP_SYSTEM_SESSIONID = "systemNotification";
    //工作通知的表名，工作通知的sessionID是公司id
    static final String TCP_WORK_TABLE_NAME = "workNotification";
    //审批通知的表名，也是审批通知的sessionID
    static final String TCP_APPROVAL_SESSIONID = "approvalNotification";
    //工单通知的表名，也是工单通知的sessionID
    static final String TCP_TICKET_SESSIONID = "ticketNotification";

    static final String TCP_TRAIN_SESSIONID = "trainNotification";
    //金蝶审批通知的sessionid
    static final String TCP_KINGDEE_SESSIONID = "kingdeeNotification";
    //库存通知的表名，也是库存通知的sessionID
    static final String TCP_MATTER_SESSIONID = "matterNotification";
    //合作团队通知的表名，也是合作团队通知的sessionID
    static final String TCP_TEAM_SESSIONID = "collaborateTeamNotification";
    // 综合管理平台
    static final String TCP_GENER_MANAGEMENT_SESSIONID = "managementPlatformNotification";
    // 园区通知
    static final String TCP_RANGE_PARK_SESSIONID = "rangeParkNotification";
    // IDC知识库
    static final String TCP_IDC_SESSIONID = "idcNotification";

//    //系统通知和审批通知开启免打扰后，存到缓存集合中的字符串id
//    static final String TCP_SYSTEM_PUSH_ID = "tcp_system_push_id";
//    static final String TCP_APPROVAL_PUSH_ID = "tcp_approval_push_id";

    //单聊群聊通知也的标记
    static final String TCP_SINGLE_CHAT_PAGE = "tcp_single_chat_page";
    static final String TCP_GROUP_CHAT_PAGE = "tcp_group_chat_page";
    static final String TCP_NOTICE_CHAT_PAGE = "tcp_notice_chat_page";

    //消息被拒绝后的标记
    static final String TCP_MSG_IS_REJECTED = "tcp_msg_is_rejected";



}


class ConsKeys {

     static final PHONE_CLIENT_ANDROID = 2;
     static final PHONE_CLIENT_IOS = 1;

}