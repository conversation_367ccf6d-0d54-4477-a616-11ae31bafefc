import 'dart:convert';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import '../../common/api/Define.dart';

const singleDisturbKey = "SINGLEDISTURBKEY";
const notifyDisturbKey = "NOTIFYDISTURBKEY";
const companyDisturbKey = "COMPANYDISTURBKEY";

/// 全局性临时缓存数据：
/// 群组数据
class ImCacheData {
  static ImCacheData? _instance;

  ImCacheData._internal();

  static ImCacheData get instance => _instance ??= ImCacheData._internal();


  bool expanded = false;  // session top 默认 不展开

  // 免打扰的userId list
  final List<String> _singleGroupDisturbIds = [];

  final List<String> _companyDisturbIds = [];
  NotifyItemDisturb? _notifyItemDisturb;

  List<String> companies() => _companyDisturbIds;

  updateCompanyId(String companyId) {
    if (_companyDisturbIds.contains(companyId)) {
      _companyDisturbIds.remove(companyId);
    } else {
      _companyDisturbIds.add(companyId);
    }
  }

  updateCompanyIds(List<String> list) {
    _companyDisturbIds.clear();
    _companyDisturbIds.addAll(list);
    String v = jsonEncode(DisturbEntity(list).toJson());
    UserDefault.setData(companyDisturbKey, v);
  }

  Future updateSingleAndGroupChatDisturbList(List<String> list) async{
    _singleGroupDisturbIds.clear();
    _singleGroupDisturbIds.addAll(list);
    String v = jsonEncode(DisturbEntity(list).toJson());
    await UserDefault.setData(singleDisturbKey, v);
  }

  addSingleAndGroupChatDisturbSessionId(String sessionId) async {
    var list = await getSingleGroupDisturbIds();
    if (list.contains(sessionId)) {
      return;
    }
    list.add(sessionId);
    updateSingleAndGroupChatDisturbList(list);
  }

  removeSingleAndGroupChatSessionId(String sessionId) async {
    var list = await getSingleGroupDisturbIds();
    if (!list.contains(sessionId)) {
      return;
    }
    list.remove(sessionId);
    updateSingleAndGroupChatDisturbList(list);
  }

  updateNotifyDisturb(NotifyItemDisturb item) {
    _notifyItemDisturb = item;
    String v = jsonEncode(_notifyItemDisturb?.toJson());
    UserDefault.setData(notifyDisturbKey, v);
  }

  Future<NotifyItemDisturb?> getNotifyDisturb() async {
    if (_notifyItemDisturb != null) return _notifyItemDisturb;
    var r = await UserDefault.getData(notifyDisturbKey);
    if (r == null) return null;
    var d = json.decode(r);
    return NotifyItemDisturb.fromJson(d);
  }

  Future<List<String>> getSingleGroupDisturbIds() async {
    if (_singleGroupDisturbIds.isNotEmpty) return List.from(_singleGroupDisturbIds);
    String datas = await UserDefault.getData(singleDisturbKey) ?? '';
    if (datas.isNotBlank()) {
      final entity = json.decode(datas);
      var d = DisturbEntity.fromJson(entity);
      List<String> l = d.userIds;
      updateSingleAndGroupChatDisturbList(l);
      return l;
    }
    return [];
  }

  Future<List<String>> getWorkNoticeDisturbIds() async {
    if (_companyDisturbIds.isNotEmpty) return _companyDisturbIds;
    String datas = await UserDefault.getData(companyDisturbKey) ?? '';
    if (datas.isNotBlank()) {
      final entity = json.decode(datas);
      var d = DisturbEntity.fromJson(entity);
      List<String> l = d.userIds;
      updateCompanyIds(l);
      return l;
    }
    return [];
  }

  // 更新本地的群组信息数据
  Future getLocalGroupInfo() async {
    var list = await UserDefault.getData(Define.GROUPLIST);
    if (list == null) return [];
    return list;
  }

  Future updateNotifyDisturbWithSessionType(
      int sessionType, int noDisturb) async {
    NotifyItemDisturb? disturbItem = await getNotifyDisturb();
    if (disturbItem == null) return;
    Map<String, dynamic> disturbMap = disturbItem.toJson();
    String? switchKey = NoticeTypeManager.noticeSwitchMap[sessionType];
    if (switchKey != null) {
      disturbMap[switchKey] = noDisturb;
    }
    UserDefault.setData(notifyDisturbKey, disturbMap);
    NotifyItemDisturb currrentItem = NotifyItemDisturb.fromJson(disturbMap);
    _notifyItemDisturb = currrentItem;
  }

  updateCompanyDisturbWithCompanyId(String companyId, int noDisturb) {
    if (_companyDisturbIds.contains(companyId)) {
      if (noDisturb == 0) {
        _companyDisturbIds.remove(companyId);
      }
    } else {
      if (noDisturb == 1) {
        _companyDisturbIds.add(companyId);
      }
    }
    String v = jsonEncode(DisturbEntity(_companyDisturbIds).toJson());
    UserDefault.setData(companyDisturbKey, v);
  }

  void clearCaches() {
    _singleGroupDisturbIds.clear();
    _companyDisturbIds.clear();
    _notifyItemDisturb = NotifyItemDisturb();
    UserDefault.setData(singleDisturbKey, '');
    UserDefault.setData(companyDisturbKey, '');
    UserDefault.setData(notifyDisturbKey, '');
  }
}

class DisturbEntity {
  List<String> userIds = [];

  DisturbEntity.fromJson(Map<String, dynamic> json) {
    List<dynamic> jsonUsers = json['userIds'];

    userIds = json['userIds'] == null ? [] : jsonUsers.cast<String>();
  }

  Map<String, dynamic> toJson() => {
        'userIds': userIds,
      };

  DisturbEntity(this.userIds);
}

class NotifyItemDisturb {
  int? systemPushSwitch = 0;
  int? approvalPushSwitch = 0;
  int? ticketPushSwitch = 0;
  int? strangerSwitch = 0;
  int? inventorySwitch = 0;
  int? trainingSwitch = 0;
  int? kingdeePushSwitch = 0;
  int? managementSwitch = 0;
  int? rangeParkSwitch = 0;
  int? idcSwitch = 0;

  NotifyItemDisturb();

  NotifyItemDisturb.fromJson(Map<String, dynamic> json) {
    systemPushSwitch = json['systemPushSwitch'];
    approvalPushSwitch = json['approvalPushSwitch'];
    ticketPushSwitch = json['ticketPushSwitch'];
    strangerSwitch = json['strangerSwitch'];
    inventorySwitch = json['inventorySwitch'];
    trainingSwitch = json['trainingSwitch'];
    kingdeePushSwitch = json['kingdeePushSwitch'];
    managementSwitch = json['managementSwitch'];
    rangeParkSwitch = json['rangeParkSwitch'];
    idcSwitch = json['idcSwitch'];
  }

  Map<String, dynamic> toJson() => {
        'systemPushSwitch': systemPushSwitch,
        'approvalPushSwitch': approvalPushSwitch,
        'ticketPushSwitch': ticketPushSwitch,
        'strangerSwitch': strangerSwitch,
        'inventorySwitch': inventorySwitch,
        'trainingSwitch': trainingSwitch,
        'kingdeePushSwitch': kingdeePushSwitch,
        'managementSwitch': managementSwitch,
        'rangeParkSwitch': rangeParkSwitch,
        'idcSwitch':idcSwitch
      };
}
