class IdcModel {
  String? customTitle = "";//标题
  String? coverImageId = "";//图片url
  String? customAuthor = "";//作者
  String? mobileUrl = "";//跳转链接

  IdcModel({this.customTitle, this.coverImageId, this.customAuthor,this.mobileUrl});

  IdcModel.fromJson(Map<String, dynamic> json) {
    customTitle = json['customTitle'] ?? '';
    coverImageId = json['coverImageId'] ?? '';
    customAuthor = json['customAuthor'] ?? '';
    mobileUrl = json['mobileUrl'] ?? '';
  }

  Map<String, dynamic> toJson() => {
        'customTitle': customTitle,
        'coverImageId': coverImageId,
        'customAuthor': customAuthor,
        'mobileUrl': mobileUrl,
      };
}