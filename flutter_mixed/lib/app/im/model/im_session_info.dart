class ImSessionInfo {
  String? appChatId = "";
  String? avatar = "";
  String? name = "";

  ImSessionInfo(this.appChatId, this.avatar, this.name);

  ImSessionInfo.fromJson(Map<String, dynamic> json) {
    appChatId = json['id'] ?? '';
    avatar = json['avatar'] ?? '';
    name = json['name'] ?? '';
  }

  Map<String, dynamic> toJson() => {
        'id': appChatId,
        'avatar': avatar,
        'name': name,
      };
}
