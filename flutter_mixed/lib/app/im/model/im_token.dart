

class ImToken {

  String appId = "";
  String appUid = "";
  String avatar = "";
  String id = "";
  String mobile = "";
  String nickname = "";
  String token = "";

  ImToken(this.appId, this.appUid, this.avatar, this.id, this.mobile,
      this.nickname, this.token);

  ImToken.fromJson(Map<String, dynamic> json) {
      appId = (json['appId'] ?? '') as String;
      appUid = (json['appUid'] ?? '') as String;
      avatar = (json['avatar'] ?? '') as String;
      id = (json['id'] ?? '') as String;
      mobile = (json['mobile'] ?? '') as String;
      nickname = (json['nickname'] ?? '') as String;
      token = (json['token'] ?? '') as String;
  }

  Map<String, dynamic> toJson() => {
        'appId': appId,
        'appUid': appUid,
        'avatar': avatar,
        'id': id,
        'mobile': mobile,
        'nickname': nickname,
        'token': token,
      };
}
