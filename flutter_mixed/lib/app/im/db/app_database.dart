

import 'dart:async';
import 'package:flutter_mixed/app/im/db/dao/message_dao.dart';
import 'package:flutter_mixed/app/im/db/dao/notice_dao.dart';
import 'package:flutter_mixed/app/im/db/dao/session_read_dao.dart';
import 'package:flutter_mixed/app/im/db/entity/group.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/app/im/db/entity/session_read.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/im/db/dao/session_dao.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/db/dao/group_dao.dart';
import 'package:flutter_mixed/app/im/db/dao/draft_message_dao.dart';

import 'entity/draft_message.dart';
part 'app_database.g.dart';

@Database(version: 4, entities: [Session , Message , Notice, Group
  ,SessionRead, DraftMessage ])
abstract class AppDataBase extends FloorDatabase {

  SessionDao get getSessionDao;
  MessageDao get getMessageDao;
  NoticeDao get getNoticeDao;
  GroupDao get getGroupDao;
  SessionReadDao get getSessionReadDao;
  DraftMessageDao get draftMessageDao;

}