// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// **************************************************************************
// FloorGenerator
// **************************************************************************

abstract class $AppDataBaseBuilderContract {
  /// Adds migrations to the builder.
  $AppDataBaseBuilderContract addMigrations(List<Migration> migrations);

  /// Adds a database [Callback] to the builder.
  $AppDataBaseBuilderContract addCallback(Callback callback);

  /// Creates the database and initializes it.
  Future<AppDataBase> build();
}

// ignore: avoid_classes_with_only_static_members
class $FloorAppDataBase {
  /// Creates a database builder for a persistent database.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDataBaseBuilderContract databaseBuilder(String name) =>
      _$AppDataBaseBuilder(name);

  /// Creates a database builder for an in memory database.
  /// Information stored in an in memory database disappears when the process is killed.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDataBaseBuilderContract inMemoryDatabaseBuilder() =>
      _$AppDataBaseBuilder(null);
}

class _$AppDataBaseBuilder implements $AppDataBaseBuilderContract {
  _$AppDataBaseBuilder(this.name);

  final String? name;

  final List<Migration> _migrations = [];

  Callback? _callback;

  @override
  $AppDataBaseBuilderContract addMigrations(List<Migration> migrations) {
    _migrations.addAll(migrations);
    return this;
  }

  @override
  $AppDataBaseBuilderContract addCallback(Callback callback) {
    _callback = callback;
    return this;
  }

  @override
  Future<AppDataBase> build() async {
    final path = name != null
        ? await sqfliteDatabaseFactory.getDatabasePath(name!)
        : ':memory:';
    final database = _$AppDataBase();
    database.database = await database.open(
      path,
      _migrations,
      _callback,
    );
    return database;
  }
}

class _$AppDataBase extends AppDataBase {
  _$AppDataBase([StreamController<String>? listener]) {
    changeListener = listener ?? StreamController<String>.broadcast();
  }

  SessionDao? _getSessionDaoInstance;

  MessageDao? _getMessageDaoInstance;

  NoticeDao? _getNoticeDaoInstance;

  GroupDao? _getGroupDaoInstance;

  SessionReadDao? _getSessionReadDaoInstance;

  DraftMessageDao? _draftMessageDaoInstance;

  Future<sqflite.Database> open(
    String path,
    List<Migration> migrations, [
    Callback? callback,
  ]) async {
    final databaseOptions = sqflite.OpenDatabaseOptions(
      version: 4,
      onConfigure: (database) async {
        await database.execute('PRAGMA foreign_keys = ON');
        await callback?.onConfigure?.call(database);
      },
      onOpen: (database) async {
        await callback?.onOpen?.call(database);
      },
      onUpgrade: (database, startVersion, endVersion) async {
        await MigrationAdapter.runMigrations(
            database, startVersion, endVersion, migrations);

        await callback?.onUpgrade?.call(database, startVersion, endVersion);
      },
      onCreate: (database, version) async {
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `Session` (`sessionId` TEXT NOT NULL, `uid` TEXT, `sessionTop` INTEGER, `sessionHidden` INTEGER, `msgType` INTEGER, `sessionType` INTEGER, `extend` TEXT, `name` TEXT, `chatStatus` INTEGER, `cmdId` TEXT, `headerUrl` TEXT, `msgId` TEXT, `msgContent` TEXT, `notReadCount` INTEGER, `msgTime` INTEGER, `deleteLastTime` INTEGER, `noDisturb` INTEGER, `appChatId` TEXT, PRIMARY KEY (`sessionId`, `uid`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `Message` (`msgId` TEXT NOT NULL, `uid` TEXT, `sessionId` TEXT, `msgType` INTEGER, `sessionType` INTEGER, `sendId` TEXT, `sendName` TEXT, `sendHeader` TEXT, `sendTime` INTEGER, `cmdId` TEXT, `msgFrom` INTEGER, `extendOne` TEXT, `extendTwo` TEXT, `extendThree` TEXT, `showTime` INTEGER, `isReaded` INTEGER, `fileId` TEXT, `text` TEXT, `localUrl` TEXT, `imgUrl` TEXT, `miniImgUrl` TEXT, `imgWidth` REAL, `imgHeight` REAL, `voiceUrl` TEXT, `fileSize` INTEGER, `voiceTime` INTEGER, `longitude` REAL, `latitude` REAL, `addressTitle` TEXT, `addressDetail` TEXT, `addressImgUrl` TEXT, `callType` INTEGER, `callContent` TEXT, `meetingId` TEXT, `isSuccess` INTEGER, `appChatId` TEXT, `fileName` TEXT, `videoImageId` TEXT, `videoImagePath` TEXT, `isDelete` INTEGER, `upDateTime` INTEGER, PRIMARY KEY (`msgId`, `uid`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `Notice` (`msgId` TEXT NOT NULL, `uid` TEXT, `sessionId` TEXT, `msgTime` INTEGER, `title` TEXT, `subtitle` TEXT, `context` TEXT, `ext` TEXT, `data` TEXT, `type` INTEGER, `companyId` TEXT, `msgType` INTEGER, `cmdId` TEXT, `tempStatus` INTEGER, `classifyType` INTEGER, PRIMARY KEY (`msgId`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `group_chat` (`groupId` TEXT NOT NULL, `uid` TEXT, `name` TEXT, `logo` TEXT, `createUserId` TEXT, `createTime` INTEGER, `type` INTEGER, `orgId` TEXT, `receiveMode` INTEGER, `status` INTEGER, `banned` INTEGER, `logoText` TEXT, `colour` TEXT, `isAdmin` INTEGER, `updateTime` INTEGER, PRIMARY KEY (`groupId`, `uid`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `SessionRead` (`uid` TEXT, `sessionId` TEXT, `appChatId` TEXT, `lastReadTime` INTEGER, PRIMARY KEY (`uid`, `sessionId`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `DraftMessage` (`sessionId` TEXT NOT NULL, `uid` TEXT NOT NULL, `text` TEXT, `quoteMessageId` TEXT, `createTime` INTEGER NOT NULL, `updateTime` INTEGER NOT NULL, PRIMARY KEY (`sessionId`, `uid`))');

        await callback?.onCreate?.call(database, version);
      },
    );
    return sqfliteDatabaseFactory.openDatabase(path, options: databaseOptions);
  }

  @override
  SessionDao get getSessionDao {
    return _getSessionDaoInstance ??= _$SessionDao(database, changeListener);
  }

  @override
  MessageDao get getMessageDao {
    return _getMessageDaoInstance ??= _$MessageDao(database, changeListener);
  }

  @override
  NoticeDao get getNoticeDao {
    return _getNoticeDaoInstance ??= _$NoticeDao(database, changeListener);
  }

  @override
  GroupDao get getGroupDao {
    return _getGroupDaoInstance ??= _$GroupDao(database, changeListener);
  }

  @override
  SessionReadDao get getSessionReadDao {
    return _getSessionReadDaoInstance ??=
        _$SessionReadDao(database, changeListener);
  }

  @override
  DraftMessageDao get draftMessageDao {
    return _draftMessageDaoInstance ??=
        _$DraftMessageDao(database, changeListener);
  }
}

class _$SessionDao extends SessionDao {
  _$SessionDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _sessionInsertionAdapter = InsertionAdapter(
            database,
            'Session',
            (Session item) => <String, Object?>{
                  'sessionId': item.sessionId,
                  'uid': item.uid,
                  'sessionTop': item.sessionTop,
                  'sessionHidden': item.sessionHidden,
                  'msgType': item.msgType,
                  'sessionType': item.sessionType,
                  'extend': item.extend,
                  'name': item.name,
                  'chatStatus': item.chatStatus,
                  'cmdId': item.cmdId,
                  'headerUrl': item.headerUrl,
                  'msgId': item.msgId,
                  'msgContent': item.msgContent,
                  'notReadCount': item.notReadCount,
                  'msgTime': item.msgTime,
                  'deleteLastTime': item.deleteLastTime,
                  'noDisturb': item.noDisturb,
                  'appChatId': item.appChatId
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<Session> _sessionInsertionAdapter;

  @override
  Future<List<Session>> findAllSessionsByOwnerId(String userId) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Session WHERE uid = ?1 order by msgTime desc',
        mapper: (Map<String, Object?> row) => Session(
            sessionId: row['sessionId'] as String,
            uid: row['uid'] as String?,
            sessionTop: row['sessionTop'] as int?,
            sessionHidden: row['sessionHidden'] as int?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            extend: row['extend'] as String?,
            name: row['name'] as String?,
            chatStatus: row['chatStatus'] as int?,
            cmdId: row['cmdId'] as String?,
            headerUrl: row['headerUrl'] as String?,
            msgId: row['msgId'] as String?,
            msgContent: row['msgContent'] as String?,
            notReadCount: row['notReadCount'] as int?,
            msgTime: row['msgTime'] as int?,
            noDisturb: row['noDisturb'] as int?,
            appChatId: row['appChatId'] as String?,
            deleteLastTime: row['deleteLastTime'] as int?),
        arguments: [userId]);
  }

  @override
  Future<List<Session>> findAllSessionsNotHiddenByOwnerId(String userId) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Session WHERE uid = ?1 AND sessionHidden = 0 order by msgTime desc',
        mapper: (Map<String, Object?> row) => Session(sessionId: row['sessionId'] as String, uid: row['uid'] as String?, sessionTop: row['sessionTop'] as int?, sessionHidden: row['sessionHidden'] as int?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, extend: row['extend'] as String?, name: row['name'] as String?, chatStatus: row['chatStatus'] as int?, cmdId: row['cmdId'] as String?, headerUrl: row['headerUrl'] as String?, msgId: row['msgId'] as String?, msgContent: row['msgContent'] as String?, notReadCount: row['notReadCount'] as int?, msgTime: row['msgTime'] as int?, noDisturb: row['noDisturb'] as int?, appChatId: row['appChatId'] as String?, deleteLastTime: row['deleteLastTime'] as int?),
        arguments: [userId]);
  }

  @override
  Future<void> clearSession() async {
    await _queryAdapter.queryNoReturn('DELETE FROM Session');
  }

  @override
  Future<void> delSessionBySessionId(
    String appChatId,
    String userId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM Session WHERE appChatId = ?1 AND uid = ?2',
        arguments: [appChatId, userId]);
  }

  @override
  Future<List<Session>> getSessionByOwnerId_sessionId(
    String userId,
    String sessionId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Session WHERE uid = ?1 AND sessionId = ?2',
        mapper: (Map<String, Object?> row) => Session(
            sessionId: row['sessionId'] as String,
            uid: row['uid'] as String?,
            sessionTop: row['sessionTop'] as int?,
            sessionHidden: row['sessionHidden'] as int?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            extend: row['extend'] as String?,
            name: row['name'] as String?,
            chatStatus: row['chatStatus'] as int?,
            cmdId: row['cmdId'] as String?,
            headerUrl: row['headerUrl'] as String?,
            msgId: row['msgId'] as String?,
            msgContent: row['msgContent'] as String?,
            notReadCount: row['notReadCount'] as int?,
            msgTime: row['msgTime'] as int?,
            noDisturb: row['noDisturb'] as int?,
            appChatId: row['appChatId'] as String?,
            deleteLastTime: row['deleteLastTime'] as int?),
        arguments: [userId, sessionId]);
  }

  @override
  Stream<List<Session>> listenSessionList(String userId) {
    return _queryAdapter.queryListStream('SELECT * FROM Session WHERE uid = ?1',
        mapper: (Map<String, Object?> row) => Session(
            sessionId: row['sessionId'] as String,
            uid: row['uid'] as String?,
            sessionTop: row['sessionTop'] as int?,
            sessionHidden: row['sessionHidden'] as int?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            extend: row['extend'] as String?,
            name: row['name'] as String?,
            chatStatus: row['chatStatus'] as int?,
            cmdId: row['cmdId'] as String?,
            headerUrl: row['headerUrl'] as String?,
            msgId: row['msgId'] as String?,
            msgContent: row['msgContent'] as String?,
            notReadCount: row['notReadCount'] as int?,
            msgTime: row['msgTime'] as int?,
            noDisturb: row['noDisturb'] as int?,
            appChatId: row['appChatId'] as String?,
            deleteLastTime: row['deleteLastTime'] as int?),
        arguments: [userId],
        queryableName: 'Session',
        isView: false);
  }

  @override
  Future<void> upDateSessionUnReadCount(
    String userId,
    String sessionId,
    int notReadCount,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Session SET notReadCount = ?3 WHERE uid = ?1 AND sessionId = ?2',
        arguments: [userId, sessionId, notReadCount]);
  }

  @override
  Future<void> upDateSessionGroupLogo(
    String userId,
    String sessionId,
    String headerUrl,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Session SET headerUrl = ?3 WHERE uid = ?1 AND sessionId = ?2',
        arguments: [userId, sessionId, headerUrl]);
  }

  @override
  Future<void> upDateSessionSessionNameAndLogo(
    String userId,
    String sessionId,
    String name,
    String headerUrl,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Session SET name = ?3,headerUrl = ?4 WHERE uid = ?1 AND sessionId = ?2',
        arguments: [userId, sessionId, name, headerUrl]);
  }

  @override
  Future<void> upDateSessionWithContent(
    int sessionType,
    String appChatId,
    int msgTime,
    String msgContent,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Session SET msgContent = ?4 WHERE sessionType = ?1 AND appChatId = ?2 AND msgTime = ?3',
        arguments: [sessionType, appChatId, msgTime, msgContent]);
  }

  @override
  Future<void> insertList(List<Session> sessionList) async {
    await _sessionInsertionAdapter.insertList(
        sessionList, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertSession(Session session) async {
    await _sessionInsertionAdapter.insert(session, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertAllSessionsInTransaction(List<Session> sessions) async {
    if (database is sqflite.Transaction) {
      await super.insertAllSessionsInTransaction(sessions);
    } else {
      await (database as sqflite.Database)
          .transaction<void>((transaction) async {
        final transactionDatabase = _$AppDataBase(changeListener)
          ..database = transaction;
        await transactionDatabase.getSessionDao
            .insertAllSessionsInTransaction(sessions);
      });
    }
  }
}

class _$MessageDao extends MessageDao {
  _$MessageDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _messageInsertionAdapter = InsertionAdapter(
            database,
            'Message',
            (Message item) => <String, Object?>{
                  'msgId': item.msgId,
                  'uid': item.uid,
                  'sessionId': item.sessionId,
                  'msgType': item.msgType,
                  'sessionType': item.sessionType,
                  'sendId': item.sendId,
                  'sendName': item.sendName,
                  'sendHeader': item.sendHeader,
                  'sendTime': item.sendTime,
                  'cmdId': item.cmdId,
                  'msgFrom': item.msgFrom,
                  'extendOne': item.extendOne,
                  'extendTwo': item.extendTwo,
                  'extendThree': item.extendThree,
                  'showTime': item.showTime,
                  'isReaded': item.isReaded,
                  'fileId': item.fileId,
                  'text': item.text,
                  'localUrl': item.localUrl,
                  'imgUrl': item.imgUrl,
                  'miniImgUrl': item.miniImgUrl,
                  'imgWidth': item.imgWidth,
                  'imgHeight': item.imgHeight,
                  'voiceUrl': item.voiceUrl,
                  'fileSize': item.fileSize,
                  'voiceTime': item.voiceTime,
                  'longitude': item.longitude,
                  'latitude': item.latitude,
                  'addressTitle': item.addressTitle,
                  'addressDetail': item.addressDetail,
                  'addressImgUrl': item.addressImgUrl,
                  'callType': item.callType,
                  'callContent': item.callContent,
                  'meetingId': item.meetingId,
                  'isSuccess': item.isSuccess,
                  'appChatId': item.appChatId,
                  'fileName': item.fileName,
                  'videoImageId': item.videoImageId,
                  'videoImagePath': item.videoImagePath,
                  'isDelete': item.isDelete,
                  'upDateTime': item.upDateTime
                },
            changeListener),
        _messageDeletionAdapter = DeletionAdapter(
            database,
            'Message',
            ['msgId', 'uid'],
            (Message item) => <String, Object?>{
                  'msgId': item.msgId,
                  'uid': item.uid,
                  'sessionId': item.sessionId,
                  'msgType': item.msgType,
                  'sessionType': item.sessionType,
                  'sendId': item.sendId,
                  'sendName': item.sendName,
                  'sendHeader': item.sendHeader,
                  'sendTime': item.sendTime,
                  'cmdId': item.cmdId,
                  'msgFrom': item.msgFrom,
                  'extendOne': item.extendOne,
                  'extendTwo': item.extendTwo,
                  'extendThree': item.extendThree,
                  'showTime': item.showTime,
                  'isReaded': item.isReaded,
                  'fileId': item.fileId,
                  'text': item.text,
                  'localUrl': item.localUrl,
                  'imgUrl': item.imgUrl,
                  'miniImgUrl': item.miniImgUrl,
                  'imgWidth': item.imgWidth,
                  'imgHeight': item.imgHeight,
                  'voiceUrl': item.voiceUrl,
                  'fileSize': item.fileSize,
                  'voiceTime': item.voiceTime,
                  'longitude': item.longitude,
                  'latitude': item.latitude,
                  'addressTitle': item.addressTitle,
                  'addressDetail': item.addressDetail,
                  'addressImgUrl': item.addressImgUrl,
                  'callType': item.callType,
                  'callContent': item.callContent,
                  'meetingId': item.meetingId,
                  'isSuccess': item.isSuccess,
                  'appChatId': item.appChatId,
                  'fileName': item.fileName,
                  'videoImageId': item.videoImageId,
                  'videoImagePath': item.videoImagePath,
                  'isDelete': item.isDelete,
                  'upDateTime': item.upDateTime
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<Message> _messageInsertionAdapter;

  final DeletionAdapter<Message> _messageDeletionAdapter;

  @override
  Future<List<Message>> findAllMessagesByOwnerId(String userId) async {
    return _queryAdapter.queryList('SELECT * FROM Message WHERE uid = ?1',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String,
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            sendId: row['sendId'] as String?,
            sendName: row['sendName'] as String?,
            sendHeader: row['sendHeader'] as String?,
            sendTime: row['sendTime'] as int?,
            cmdId: row['cmdId'] as String?,
            msgFrom: row['msgFrom'] as int?,
            extendOne: row['extendOne'] as String?,
            extendTwo: row['extendTwo'] as String?,
            extendThree: row['extendThree'] as String?,
            showTime: row['showTime'] as int?,
            isReaded: row['isReaded'] as int?,
            fileId: row['fileId'] as String?,
            text: row['text'] as String?,
            localUrl: row['localUrl'] as String?,
            imgUrl: row['imgUrl'] as String?,
            miniImgUrl: row['miniImgUrl'] as String?,
            imgWidth: row['imgWidth'] as double?,
            imgHeight: row['imgHeight'] as double?,
            voiceUrl: row['voiceUrl'] as String?,
            fileSize: row['fileSize'] as int?,
            voiceTime: row['voiceTime'] as int?,
            longitude: row['longitude'] as double?,
            latitude: row['latitude'] as double?,
            addressTitle: row['addressTitle'] as String?,
            addressDetail: row['addressDetail'] as String?,
            addressImgUrl: row['addressImgUrl'] as String?,
            callType: row['callType'] as int?,
            callContent: row['callContent'] as String?,
            meetingId: row['meetingId'] as String?,
            isSuccess: row['isSuccess'] as int?,
            appChatId: row['appChatId'] as String?,
            fileName: row['fileName'] as String?,
            videoImageId: row['videoImageId'] as String?,
            videoImagePath: row['videoImagePath'] as String?,
            isDelete: row['isDelete'] as int?,
            upDateTime: row['upDateTime'] as int?),
        arguments: [userId]);
  }

  @override
  Future<void> delMessageByMsgId(String msgId) async {
    await _queryAdapter.queryNoReturn('DELETE FROM Message WHERE msgId = ?1',
        arguments: [msgId]);
  }

  @override
  Future<void> updateMessageSendStatus(
    String newId,
    String oldId,
    int isSuccess,
    int sendTime,
    int upDateTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Message SET msgId = ?1 , isSuccess = ?3 , sendTime = ?4 , upDateTime = ?5 WHERE msgId = ?2',
        arguments: [newId, oldId, isSuccess, sendTime, upDateTime]);
  }

  @override
  Future<void> clearMessage() async {
    await _queryAdapter.queryNoReturn('DELETE FROM Message');
  }

  @override
  Future<List<Message>> getMessagesByOwnerId_chatId(
    String userId,
    String appChatId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND appChatId = ?2',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String,
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            sendId: row['sendId'] as String?,
            sendName: row['sendName'] as String?,
            sendHeader: row['sendHeader'] as String?,
            sendTime: row['sendTime'] as int?,
            cmdId: row['cmdId'] as String?,
            msgFrom: row['msgFrom'] as int?,
            extendOne: row['extendOne'] as String?,
            extendTwo: row['extendTwo'] as String?,
            extendThree: row['extendThree'] as String?,
            showTime: row['showTime'] as int?,
            isReaded: row['isReaded'] as int?,
            fileId: row['fileId'] as String?,
            text: row['text'] as String?,
            localUrl: row['localUrl'] as String?,
            imgUrl: row['imgUrl'] as String?,
            miniImgUrl: row['miniImgUrl'] as String?,
            imgWidth: row['imgWidth'] as double?,
            imgHeight: row['imgHeight'] as double?,
            voiceUrl: row['voiceUrl'] as String?,
            fileSize: row['fileSize'] as int?,
            voiceTime: row['voiceTime'] as int?,
            longitude: row['longitude'] as double?,
            latitude: row['latitude'] as double?,
            addressTitle: row['addressTitle'] as String?,
            addressDetail: row['addressDetail'] as String?,
            addressImgUrl: row['addressImgUrl'] as String?,
            callType: row['callType'] as int?,
            callContent: row['callContent'] as String?,
            meetingId: row['meetingId'] as String?,
            isSuccess: row['isSuccess'] as int?,
            appChatId: row['appChatId'] as String?,
            fileName: row['fileName'] as String?,
            videoImageId: row['videoImageId'] as String?,
            videoImagePath: row['videoImagePath'] as String?,
            isDelete: row['isDelete'] as int?,
            upDateTime: row['upDateTime'] as int?),
        arguments: [userId, appChatId]);
  }

  @override
  Future<List<Message>> queryOffLineFaultMessage(
    String userId,
    String sessionId,
    int sendTime,
    int msgFrom,
    int msgType,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 AND sendTime = ?3 AND msgFrom = ?4 AND msgType = ?5',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId, sendTime, msgFrom, msgType]);
  }

  @override
  Future<List<Message>> queryLastFaultMessage(
    String userId,
    String sessionId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 order by sendTime desc LIMIT 35 OFFSET 0',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId]);
  }

  @override
  Future<List<Message>> queryAllMessageBySessionId(
    String userId,
    String sessionId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 order by sendTime desc',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId]);
  }

  @override
  Future<List<Message>> queryMsgByUid_sessionId_sendTime_msgType(
    String userId,
    String sessionId,
    int sendTime,
    int msgType,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 AND sendTime = ?3 AND msgType = ?4 AND (isDelete <> 1 OR isDelete IS NULL)',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId, sendTime, msgType]);
  }

  @override
  Stream<List<Message>> listenMessageList(
    String userId,
    String sessionId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 order by sendTime desc',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String,
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            sendId: row['sendId'] as String?,
            sendName: row['sendName'] as String?,
            sendHeader: row['sendHeader'] as String?,
            sendTime: row['sendTime'] as int?,
            cmdId: row['cmdId'] as String?,
            msgFrom: row['msgFrom'] as int?,
            extendOne: row['extendOne'] as String?,
            extendTwo: row['extendTwo'] as String?,
            extendThree: row['extendThree'] as String?,
            showTime: row['showTime'] as int?,
            isReaded: row['isReaded'] as int?,
            fileId: row['fileId'] as String?,
            text: row['text'] as String?,
            localUrl: row['localUrl'] as String?,
            imgUrl: row['imgUrl'] as String?,
            miniImgUrl: row['miniImgUrl'] as String?,
            imgWidth: row['imgWidth'] as double?,
            imgHeight: row['imgHeight'] as double?,
            voiceUrl: row['voiceUrl'] as String?,
            fileSize: row['fileSize'] as int?,
            voiceTime: row['voiceTime'] as int?,
            longitude: row['longitude'] as double?,
            latitude: row['latitude'] as double?,
            addressTitle: row['addressTitle'] as String?,
            addressDetail: row['addressDetail'] as String?,
            addressImgUrl: row['addressImgUrl'] as String?,
            callType: row['callType'] as int?,
            callContent: row['callContent'] as String?,
            meetingId: row['meetingId'] as String?,
            isSuccess: row['isSuccess'] as int?,
            appChatId: row['appChatId'] as String?,
            fileName: row['fileName'] as String?,
            videoImageId: row['videoImageId'] as String?,
            videoImagePath: row['videoImagePath'] as String?,
            isDelete: row['isDelete'] as int?,
            upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId],
        queryableName: 'Message',
        isView: false);
  }

  @override
  Future<List<Message>> queryMessagesByOwnerIdAndSessionIdByPage(
    String userId,
    String sessionId,
    int offset,
    int pageSize,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 AND (isDelete <> 1 OR isDelete IS NULL) LIMIT ?4 OFFSET ?3',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId, offset, pageSize]);
  }

  @override
  Future<List<Message>> queryMessageByCmdId(
    String userId,
    String cmdId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND cmdId = ?2 AND msgType != 999993',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, cmdId]);
  }

  @override
  Future<List<Message>> queryChatMessage(
    String userId,
    String keyword,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE sessionType in (1, 2) And msgType != 9 And uid = ?1 AND text LIKE ?2 ORDER BY sendTime DESC',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, keyword]);
  }

  @override
  Future<List<Message>> queryChatGroupMessage(
    String userId,
    String keyword,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE sessionType = 2 And msgType != 9 And uid = ?1 AND text LIKE ?2 ORDER BY sendTime DESC',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, keyword]);
  }

  @override
  Future<List<Message>> getLocalMessageData(
    String userId,
    String sessionId,
    int sendTime,
    int maxCount,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 AND sendTime < ?3 AND (isDelete <> 1 OR isDelete IS NULL) order by sendTime desc LIMIT ?4',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId, sendTime, maxCount]);
  }

  @override
  Future<void> deleteMessageWithMsgType(
    String userId,
    String sessionId,
    int msgType,
    int startTime,
    int endTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM Message WHERE uid = ?1 AND sessionId = ?2 AND msgType = ?3 AND sendTime >= ?4 AND sendTime <= ?5',
        arguments: [userId, sessionId, msgType, startTime, endTime]);
  }

  @override
  Future<List<Message>> getChatMessageWihtMsgId(
    String userId,
    String sessionId,
    String msgId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 AND msgId = ?3',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId, msgId]);
  }

  @override
  Future<void> updateMessageTypeByMsgId(
    String userId,
    String msgId,
    int msgType,
    int upDateTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Message SET msgType = ?3 , upDateTime = ?4 WHERE uid = ?1 AND msgId = ?2',
        arguments: [userId, msgId, msgType, upDateTime]);
  }

  @override
  Future<void> updateMessageToReaded(
    String userId,
    String sessionId,
    int upDateTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Message SET isReaded = 1 ,upDateTime = ?3 WHERE uid = ?1 AND sessionId = ?2 AND msgFrom = 1 AND isReaded != 1',
        arguments: [userId, sessionId, upDateTime]);
  }

  @override
  Future<List<Message>> queryMessageByUidAndMsgId(
    String userId,
    String msgId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND msgId = ?2',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String,
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            sendId: row['sendId'] as String?,
            sendName: row['sendName'] as String?,
            sendHeader: row['sendHeader'] as String?,
            sendTime: row['sendTime'] as int?,
            cmdId: row['cmdId'] as String?,
            msgFrom: row['msgFrom'] as int?,
            extendOne: row['extendOne'] as String?,
            extendTwo: row['extendTwo'] as String?,
            extendThree: row['extendThree'] as String?,
            showTime: row['showTime'] as int?,
            isReaded: row['isReaded'] as int?,
            fileId: row['fileId'] as String?,
            text: row['text'] as String?,
            localUrl: row['localUrl'] as String?,
            imgUrl: row['imgUrl'] as String?,
            miniImgUrl: row['miniImgUrl'] as String?,
            imgWidth: row['imgWidth'] as double?,
            imgHeight: row['imgHeight'] as double?,
            voiceUrl: row['voiceUrl'] as String?,
            fileSize: row['fileSize'] as int?,
            voiceTime: row['voiceTime'] as int?,
            longitude: row['longitude'] as double?,
            latitude: row['latitude'] as double?,
            addressTitle: row['addressTitle'] as String?,
            addressDetail: row['addressDetail'] as String?,
            addressImgUrl: row['addressImgUrl'] as String?,
            callType: row['callType'] as int?,
            callContent: row['callContent'] as String?,
            meetingId: row['meetingId'] as String?,
            isSuccess: row['isSuccess'] as int?,
            appChatId: row['appChatId'] as String?,
            fileName: row['fileName'] as String?,
            videoImageId: row['videoImageId'] as String?,
            videoImagePath: row['videoImagePath'] as String?,
            isDelete: row['isDelete'] as int?,
            upDateTime: row['upDateTime'] as int?),
        arguments: [userId, msgId]);
  }

  @override
  Future<void> updateMessageDelete(
    String userId,
    String sessionId,
    int upDateTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Message SET isDelete = 1 ,upDateTime = ?3 WHERE uid = ?1 AND sessionId = ?2',
        arguments: [userId, sessionId, upDateTime]);
  }

  @override
  Future<List<Message>> queryDeleteTypeMsg(
    String userId,
    String sessionId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 AND msgType = 24',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId]);
  }

  @override
  Future<void> updateSenderInfo(
    String userId,
    String sessionId,
    String sendId,
    String sendName,
    String sendHeader,
    int upDateTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Message SET sendName = ?4,sendHeader = ?5 ,upDateTime = ?6 WHERE uid = ?1 AND sessionId = ?2 AND sendId = ?3 AND (sendName <> ?4 OR sendHeader <> ?5)',
        arguments: [
          userId,
          sessionId,
          sendId,
          sendName,
          sendHeader,
          upDateTime
        ]);
  }

  @override
  Future<List<Message>> querySendingMsgList(String userId) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND isSuccess = 2',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String,
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            msgType: row['msgType'] as int?,
            sessionType: row['sessionType'] as int?,
            sendId: row['sendId'] as String?,
            sendName: row['sendName'] as String?,
            sendHeader: row['sendHeader'] as String?,
            sendTime: row['sendTime'] as int?,
            cmdId: row['cmdId'] as String?,
            msgFrom: row['msgFrom'] as int?,
            extendOne: row['extendOne'] as String?,
            extendTwo: row['extendTwo'] as String?,
            extendThree: row['extendThree'] as String?,
            showTime: row['showTime'] as int?,
            isReaded: row['isReaded'] as int?,
            fileId: row['fileId'] as String?,
            text: row['text'] as String?,
            localUrl: row['localUrl'] as String?,
            imgUrl: row['imgUrl'] as String?,
            miniImgUrl: row['miniImgUrl'] as String?,
            imgWidth: row['imgWidth'] as double?,
            imgHeight: row['imgHeight'] as double?,
            voiceUrl: row['voiceUrl'] as String?,
            fileSize: row['fileSize'] as int?,
            voiceTime: row['voiceTime'] as int?,
            longitude: row['longitude'] as double?,
            latitude: row['latitude'] as double?,
            addressTitle: row['addressTitle'] as String?,
            addressDetail: row['addressDetail'] as String?,
            addressImgUrl: row['addressImgUrl'] as String?,
            callType: row['callType'] as int?,
            callContent: row['callContent'] as String?,
            meetingId: row['meetingId'] as String?,
            isSuccess: row['isSuccess'] as int?,
            appChatId: row['appChatId'] as String?,
            fileName: row['fileName'] as String?,
            videoImageId: row['videoImageId'] as String?,
            videoImagePath: row['videoImagePath'] as String?,
            isDelete: row['isDelete'] as int?,
            upDateTime: row['upDateTime'] as int?),
        arguments: [userId]);
  }

  @override
  Future<List<Message>> queryLastMsgBySessionId(
    String userId,
    String sessionId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND sessionId = ?2 AND (isDelete <> 1 OR isDelete IS NULL) ORDER BY sendTime DESC LIMIT 1',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId, sessionId]);
  }

  @override
  Future<void> deleteMessageIsReadedColumn() async {
    await _queryAdapter
        .queryNoReturn('ALTER TABLE Message DROP COLUMN `isReaded`');
  }

  @override
  Future<List<Message>> querySendUnSuccessedMsgList(String userId) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Message WHERE uid = ?1 AND (isSuccess = 2 OR isSuccess = 0)',
        mapper: (Map<String, Object?> row) => Message(row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgType: row['msgType'] as int?, sessionType: row['sessionType'] as int?, sendId: row['sendId'] as String?, sendName: row['sendName'] as String?, sendHeader: row['sendHeader'] as String?, sendTime: row['sendTime'] as int?, cmdId: row['cmdId'] as String?, msgFrom: row['msgFrom'] as int?, extendOne: row['extendOne'] as String?, extendTwo: row['extendTwo'] as String?, extendThree: row['extendThree'] as String?, showTime: row['showTime'] as int?, isReaded: row['isReaded'] as int?, fileId: row['fileId'] as String?, text: row['text'] as String?, localUrl: row['localUrl'] as String?, imgUrl: row['imgUrl'] as String?, miniImgUrl: row['miniImgUrl'] as String?, imgWidth: row['imgWidth'] as double?, imgHeight: row['imgHeight'] as double?, voiceUrl: row['voiceUrl'] as String?, fileSize: row['fileSize'] as int?, voiceTime: row['voiceTime'] as int?, longitude: row['longitude'] as double?, latitude: row['latitude'] as double?, addressTitle: row['addressTitle'] as String?, addressDetail: row['addressDetail'] as String?, addressImgUrl: row['addressImgUrl'] as String?, callType: row['callType'] as int?, callContent: row['callContent'] as String?, meetingId: row['meetingId'] as String?, isSuccess: row['isSuccess'] as int?, appChatId: row['appChatId'] as String?, fileName: row['fileName'] as String?, videoImageId: row['videoImageId'] as String?, videoImagePath: row['videoImagePath'] as String?, isDelete: row['isDelete'] as int?, upDateTime: row['upDateTime'] as int?),
        arguments: [userId]);
  }

  @override
  Future<void> insertList(List<Message> messageList) async {
    await _messageInsertionAdapter.insertList(
        messageList, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertMessage(Message message) async {
    await _messageInsertionAdapter.insert(message, OnConflictStrategy.replace);
  }

  @override
  Future<void> removeMessage(Message message) async {
    await _messageDeletionAdapter.delete(message);
  }

  @override
  Future<void> deleteAllMessage(
    String userId,
    String sessionId,
    int upDateTime,
  ) async {
    if (database is sqflite.Transaction) {
      await super.deleteAllMessage(userId, sessionId, upDateTime);
    } else {
      await (database as sqflite.Database)
          .transaction<void>((transaction) async {
        final transactionDatabase = _$AppDataBase(changeListener)
          ..database = transaction;
        await transactionDatabase.getMessageDao
            .deleteAllMessage(userId, sessionId, upDateTime);
      });
    }
  }
}

class _$NoticeDao extends NoticeDao {
  _$NoticeDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _noticeInsertionAdapter = InsertionAdapter(
            database,
            'Notice',
            (Notice item) => <String, Object?>{
                  'msgId': item.msgId,
                  'uid': item.uid,
                  'sessionId': item.sessionId,
                  'msgTime': item.msgTime,
                  'title': item.title,
                  'subtitle': item.subtitle,
                  'context': item.context,
                  'ext': item.ext,
                  'data': item.data,
                  'type': item.type,
                  'companyId': item.companyId,
                  'msgType': item.msgType,
                  'cmdId': item.cmdId,
                  'tempStatus': item.tempStatus,
                  'classifyType': item.classifyType
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<Notice> _noticeInsertionAdapter;

  @override
  Future<List<Notice>> getLocalNoticeData(
    String userId,
    String sessionId,
    String companyId,
    int msgTime,
    int maxCount,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Notice WHERE uid = ?1 AND sessionId = ?2 AND companyId = ?3 AND msgTime < ?4 order by msgTime desc LIMIT ?5',
        mapper: (Map<String, Object?> row) => Notice(msgId: row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgTime: row['msgTime'] as int?, title: row['title'] as String?, subtitle: row['subtitle'] as String?, context: row['context'] as String?, ext: row['ext'] as String?, data: row['data'] as String?, type: row['type'] as int?, companyId: row['companyId'] as String?, msgType: row['msgType'] as int?, cmdId: row['cmdId'] as String?, tempStatus: row['tempStatus'] as int?, classifyType: row['classifyType'] as int?),
        arguments: [userId, sessionId, companyId, msgTime, maxCount]);
  }

  @override
  Future<List<Notice>> getSystemmLocalNoticeData(
    String userId,
    String sessionId,
    int msgTime,
    int maxCount,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Notice WHERE uid = ?1 AND sessionId = ?2 AND msgTime < ?3 order by msgTime desc LIMIT ?4',
        mapper: (Map<String, Object?> row) => Notice(msgId: row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgTime: row['msgTime'] as int?, title: row['title'] as String?, subtitle: row['subtitle'] as String?, context: row['context'] as String?, ext: row['ext'] as String?, data: row['data'] as String?, type: row['type'] as int?, companyId: row['companyId'] as String?, msgType: row['msgType'] as int?, cmdId: row['cmdId'] as String?, tempStatus: row['tempStatus'] as int?, classifyType: row['classifyType'] as int?),
        arguments: [userId, sessionId, msgTime, maxCount]);
  }

  @override
  Future<void> clearNotice() async {
    await _queryAdapter.queryNoReturn('DELETE FROM Notice');
  }

  @override
  Stream<List<Notice>> listenNoticeList(String userId) {
    return _queryAdapter.queryListStream('SELECT * FROM Notice WHERE uid = ?1',
        mapper: (Map<String, Object?> row) => Notice(
            msgId: row['msgId'] as String,
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            msgTime: row['msgTime'] as int?,
            title: row['title'] as String?,
            subtitle: row['subtitle'] as String?,
            context: row['context'] as String?,
            ext: row['ext'] as String?,
            data: row['data'] as String?,
            type: row['type'] as int?,
            companyId: row['companyId'] as String?,
            msgType: row['msgType'] as int?,
            cmdId: row['cmdId'] as String?,
            tempStatus: row['tempStatus'] as int?,
            classifyType: row['classifyType'] as int?),
        arguments: [userId],
        queryableName: 'Notice',
        isView: false);
  }

  @override
  Future<void> deleteMessageWithMsgType(
    String userId,
    String sessionId,
    String companyId,
    int msgType,
    int startTime,
    int endTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM Notice WHERE uid = ?1 AND sessionId = ?2 AND companyId = ?3 AND msgType = ?4 AND msgTime >= ?5 AND msgTime <= ?6',
        arguments: [userId, sessionId, companyId, msgType, startTime, endTime]);
  }

  @override
  Future<void> deleteSystemMessageWithMsgType(
    String userId,
    String sessionId,
    int msgType,
    int startTime,
    int endTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM Notice WHERE uid = ?1 AND sessionId = ?2 AND msgType = ?3 AND msgTime >= ?4 AND msgTime <= ?5',
        arguments: [userId, sessionId, msgType, startTime, endTime]);
  }

  @override
  Future<List<Notice>> getMessageWihtMsgId(
    String userId,
    String sessionId,
    String msgId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Notice WHERE uid = ?1 AND sessionId = ?2 AND msgId = ?3',
        mapper: (Map<String, Object?> row) => Notice(
            msgId: row['msgId'] as String,
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            msgTime: row['msgTime'] as int?,
            title: row['title'] as String?,
            subtitle: row['subtitle'] as String?,
            context: row['context'] as String?,
            ext: row['ext'] as String?,
            data: row['data'] as String?,
            type: row['type'] as int?,
            companyId: row['companyId'] as String?,
            msgType: row['msgType'] as int?,
            cmdId: row['cmdId'] as String?,
            tempStatus: row['tempStatus'] as int?,
            classifyType: row['classifyType'] as int?),
        arguments: [userId, sessionId, msgId]);
  }

  @override
  Future<List<Notice>> getLastNoticeDataWithCompanyId(
    String userId,
    String sessionId,
    String companyId,
    int msgType,
    int maxCount,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Notice WHERE uid = ?1 AND sessionId = ?2 AND companyId = ?3 AND msgType <> ?4 order by msgTime desc LIMIT ?5',
        mapper: (Map<String, Object?> row) => Notice(msgId: row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgTime: row['msgTime'] as int?, title: row['title'] as String?, subtitle: row['subtitle'] as String?, context: row['context'] as String?, ext: row['ext'] as String?, data: row['data'] as String?, type: row['type'] as int?, companyId: row['companyId'] as String?, msgType: row['msgType'] as int?, cmdId: row['cmdId'] as String?, tempStatus: row['tempStatus'] as int?, classifyType: row['classifyType'] as int?),
        arguments: [userId, sessionId, companyId, msgType, maxCount]);
  }

  @override
  Future<List<Notice>> getLocalWorkNoticeDataWithClassifyType(
    String userId,
    String sessionId,
    int classifyType,
    int msgTime,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM Notice WHERE uid = ?1 AND sessionId = ?2 AND classifyType = ?3 AND msgTime < ?4 order by msgTime desc',
        mapper: (Map<String, Object?> row) => Notice(msgId: row['msgId'] as String, uid: row['uid'] as String?, sessionId: row['sessionId'] as String?, msgTime: row['msgTime'] as int?, title: row['title'] as String?, subtitle: row['subtitle'] as String?, context: row['context'] as String?, ext: row['ext'] as String?, data: row['data'] as String?, type: row['type'] as int?, companyId: row['companyId'] as String?, msgType: row['msgType'] as int?, cmdId: row['cmdId'] as String?, tempStatus: row['tempStatus'] as int?, classifyType: row['classifyType'] as int?),
        arguments: [userId, sessionId, classifyType, msgTime]);
  }

  @override
  Future<void> upDateNoticeTempStatus(
    String uid,
    String cmdId,
    int tempStatus,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Notice SET tempStatus = ?3 WHERE uid = ?1 AND cmdId = ?2',
        arguments: [uid, cmdId, tempStatus]);
  }

  @override
  Future<void> upDateNoticeData(
    String uid,
    String cmdId,
    String data,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE Notice SET data = ?3 WHERE uid = ?1 AND cmdId = ?2',
        arguments: [uid, cmdId, data]);
  }

  @override
  Future<void> deleteNoticeMsgWithMsgId(String msgId) async {
    await _queryAdapter.queryNoReturn('DELETE FROM Notice WHERE msgId = ?1',
        arguments: [msgId]);
  }

  @override
  Future<void> insertNoticeList(List<Notice> noticeList) async {
    await _noticeInsertionAdapter.insertList(
        noticeList, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertNotice(Notice notice) async {
    await _noticeInsertionAdapter.insert(notice, OnConflictStrategy.replace);
  }
}

class _$GroupDao extends GroupDao {
  _$GroupDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _groupInsertionAdapter = InsertionAdapter(
            database,
            'group_chat',
            (Group item) => <String, Object?>{
                  'groupId': item.groupId,
                  'uid': item.uid,
                  'name': item.name,
                  'logo': item.logo,
                  'createUserId': item.createUserId,
                  'createTime': item.createTime,
                  'type': item.type,
                  'orgId': item.orgId,
                  'receiveMode': item.receiveMode,
                  'status': item.status,
                  'banned': item.banned,
                  'logoText': item.logoText,
                  'colour': item.colour,
                  'isAdmin': item.isAdmin,
                  'updateTime': item.updateTime
                },
            changeListener),
        _groupDeletionAdapter = DeletionAdapter(
            database,
            'group_chat',
            ['groupId', 'uid'],
            (Group item) => <String, Object?>{
                  'groupId': item.groupId,
                  'uid': item.uid,
                  'name': item.name,
                  'logo': item.logo,
                  'createUserId': item.createUserId,
                  'createTime': item.createTime,
                  'type': item.type,
                  'orgId': item.orgId,
                  'receiveMode': item.receiveMode,
                  'status': item.status,
                  'banned': item.banned,
                  'logoText': item.logoText,
                  'colour': item.colour,
                  'isAdmin': item.isAdmin,
                  'updateTime': item.updateTime
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<Group> _groupInsertionAdapter;

  final DeletionAdapter<Group> _groupDeletionAdapter;

  @override
  Future<List<Group>> findAllGroupByOwnerId(String userId) async {
    return _queryAdapter.queryList('SELECT * FROM group_chat WHERE uid = ?1',
        mapper: (Map<String, Object?> row) => Group(row['groupId'] as String,
            uid: row['uid'] as String?,
            name: row['name'] as String?,
            logo: row['logo'] as String?,
            createUserId: row['createUserId'] as String?,
            createTime: row['createTime'] as int?,
            type: row['type'] as int?,
            orgId: row['orgId'] as String?,
            receiveMode: row['receiveMode'] as int?,
            status: row['status'] as int?,
            banned: row['banned'] as int?,
            logoText: row['logoText'] as String?,
            colour: row['colour'] as String?,
            isAdmin: row['isAdmin'] as int?,
            updateTime: row['updateTime'] as int?),
        arguments: [userId]);
  }

  @override
  Future<void> delGroupByGroupId(String groupId) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM group_chat WHERE groupId = ?1',
        arguments: [groupId]);
  }

  @override
  Future<List<Group>> queryGroupByGroupId(
    String userId,
    String groupId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM group_chat WHERE uid = ?1 AND groupId = ?2',
        mapper: (Map<String, Object?> row) => Group(row['groupId'] as String,
            uid: row['uid'] as String?,
            name: row['name'] as String?,
            logo: row['logo'] as String?,
            createUserId: row['createUserId'] as String?,
            createTime: row['createTime'] as int?,
            type: row['type'] as int?,
            orgId: row['orgId'] as String?,
            receiveMode: row['receiveMode'] as int?,
            status: row['status'] as int?,
            banned: row['banned'] as int?,
            logoText: row['logoText'] as String?,
            colour: row['colour'] as String?,
            isAdmin: row['isAdmin'] as int?,
            updateTime: row['updateTime'] as int?),
        arguments: [userId, groupId]);
  }

  @override
  Stream<List<Group>> listenGroupList(
    String userId,
    String groupId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM group_chat WHERE uid = ?1 AND groupId = ?2',
        mapper: (Map<String, Object?> row) => Group(row['groupId'] as String,
            uid: row['uid'] as String?,
            name: row['name'] as String?,
            logo: row['logo'] as String?,
            createUserId: row['createUserId'] as String?,
            createTime: row['createTime'] as int?,
            type: row['type'] as int?,
            orgId: row['orgId'] as String?,
            receiveMode: row['receiveMode'] as int?,
            status: row['status'] as int?,
            banned: row['banned'] as int?,
            logoText: row['logoText'] as String?,
            colour: row['colour'] as String?,
            isAdmin: row['isAdmin'] as int?,
            updateTime: row['updateTime'] as int?),
        arguments: [userId, groupId],
        queryableName: 'group_chat',
        isView: false);
  }

  @override
  Future<void> insertList(List<Group> messageList) async {
    await _groupInsertionAdapter.insertList(
        messageList, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertGroup(Group group) async {
    await _groupInsertionAdapter.insert(group, OnConflictStrategy.replace);
  }

  @override
  Future<void> removeGroup(Group message) async {
    await _groupDeletionAdapter.delete(message);
  }

  @override
  Future<void> insertAllGroupInTransaction(List<Group> groups) async {
    if (database is sqflite.Transaction) {
      await super.insertAllGroupInTransaction(groups);
    } else {
      await (database as sqflite.Database)
          .transaction<void>((transaction) async {
        final transactionDatabase = _$AppDataBase(changeListener)
          ..database = transaction;
        await transactionDatabase.getGroupDao
            .insertAllGroupInTransaction(groups);
      });
    }
  }
}

class _$SessionReadDao extends SessionReadDao {
  _$SessionReadDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _sessionReadInsertionAdapter = InsertionAdapter(
            database,
            'SessionRead',
            (SessionRead item) => <String, Object?>{
                  'uid': item.uid,
                  'sessionId': item.sessionId,
                  'appChatId': item.appChatId,
                  'lastReadTime': item.lastReadTime
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<SessionRead> _sessionReadInsertionAdapter;

  @override
  Future<List<SessionRead>> querySessionReadBySessionId(
    String userId,
    String sessionId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM SessionRead WHERE uid = ?1 AND sessionId = ?2',
        mapper: (Map<String, Object?> row) => SessionRead(
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            appChatId: row['appChatId'] as String?,
            lastReadTime: row['lastReadTime'] as int?),
        arguments: [userId, sessionId]);
  }

  @override
  Future<void> upDateSessionUnReadCount(
    String userId,
    String sessionId,
    int lastReadTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE SessionRead SET lastReadTime = ?3 WHERE uid = ?1 AND sessionId = ?2',
        arguments: [userId, sessionId, lastReadTime]);
  }

  @override
  Stream<List<SessionRead>> listenSessionReadList(
    String userId,
    String sessionId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM SessionRead WHERE uid = ?1 AND sessionId = ?2 order by lastReadTime desc',
        mapper: (Map<String, Object?> row) => SessionRead(
            uid: row['uid'] as String?,
            sessionId: row['sessionId'] as String?,
            appChatId: row['appChatId'] as String?,
            lastReadTime: row['lastReadTime'] as int?),
        arguments: [userId, sessionId],
        queryableName: 'SessionRead',
        isView: false);
  }

  @override
  Future<void> insertSessionRead(SessionRead sessionRead) async {
    await _sessionReadInsertionAdapter.insert(
        sessionRead, OnConflictStrategy.replace);
  }
}

class _$DraftMessageDao extends DraftMessageDao {
  _$DraftMessageDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _draftMessageInsertionAdapter = InsertionAdapter(
            database,
            'DraftMessage',
            (DraftMessage item) => <String, Object?>{
                  'sessionId': item.sessionId,
                  'uid': item.uid,
                  'text': item.text,
                  'quoteMessageId': item.quoteMessageId,
                  'createTime': item.createTime,
                  'updateTime': item.updateTime
                }),
        _draftMessageUpdateAdapter = UpdateAdapter(
            database,
            'DraftMessage',
            ['sessionId', 'uid'],
            (DraftMessage item) => <String, Object?>{
                  'sessionId': item.sessionId,
                  'uid': item.uid,
                  'text': item.text,
                  'quoteMessageId': item.quoteMessageId,
                  'createTime': item.createTime,
                  'updateTime': item.updateTime
                }),
        _draftMessageDeletionAdapter = DeletionAdapter(
            database,
            'DraftMessage',
            ['sessionId', 'uid'],
            (DraftMessage item) => <String, Object?>{
                  'sessionId': item.sessionId,
                  'uid': item.uid,
                  'text': item.text,
                  'quoteMessageId': item.quoteMessageId,
                  'createTime': item.createTime,
                  'updateTime': item.updateTime
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<DraftMessage> _draftMessageInsertionAdapter;

  final UpdateAdapter<DraftMessage> _draftMessageUpdateAdapter;

  final DeletionAdapter<DraftMessage> _draftMessageDeletionAdapter;

  @override
  Future<void> deleteDraftBySessionId(
    String sessionId,
    String uid,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM DraftMessage WHERE sessionId = ?1 AND uid = ?2',
        arguments: [sessionId, uid]);
  }

  @override
  Future<DraftMessage?> getDraftBySessionId(
    String sessionId,
    String uid,
  ) async {
    return _queryAdapter.query(
        'SELECT * FROM DraftMessage WHERE sessionId = ?1 AND uid = ?2',
        mapper: (Map<String, Object?> row) => DraftMessage(
            row['sessionId'] as String, row['uid'] as String,
            text: row['text'] as String?,
            quoteMessageId: row['quoteMessageId'] as String?,
            createTime: row['createTime'] as int,
            updateTime: row['updateTime'] as int),
        arguments: [sessionId, uid]);
  }

  @override
  Future<void> insertDraft(DraftMessage draft) async {
    await _draftMessageInsertionAdapter.insert(
        draft, OnConflictStrategy.replace);
  }

  @override
  Future<void> updateDraft(DraftMessage draft) async {
    await _draftMessageUpdateAdapter.update(draft, OnConflictStrategy.abort);
  }

  @override
  Future<void> deleteDraft(DraftMessage draft) async {
    await _draftMessageDeletionAdapter.delete(draft);
  }
}
