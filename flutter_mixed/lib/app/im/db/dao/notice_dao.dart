import 'package:floor/floor.dart';

import '../entity/notice.dart';

@dao
abstract class NoticeDao {
  @Query(
      'SELECT * FROM Notice WHERE uid = :userId AND sessionId = :sessionId AND companyId = :companyId AND msgTime < :msgTime order by msgTime desc LIMIT :maxCount')
  Future<List<Notice>> getLocalNoticeData(String userId, String sessionId,
      String companyId, int msgTime, int maxCount);
  
  @Query(
      'SELECT * FROM Notice WHERE uid = :userId AND sessionId = :sessionId AND msgTime < :msgTime order by msgTime desc LIMIT :maxCount')
  Future<List<Notice>> getSystemmLocalNoticeData(String userId, String sessionId, int msgTime, int maxCount);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertNoticeList(List<Notice> noticeList);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertNotice(Notice notice);

  @Query('DELETE FROM Notice')
  Future<void> clearNotice();

  @Query('SELECT * FROM Notice WHERE uid = :userId')
  Stream<List<Notice>> listenNoticeList(String userId);

  @Query(
      'DELETE FROM Notice WHERE uid = :userId AND sessionId = :sessionId AND companyId = :companyId AND msgType = :msgType AND msgTime >= :startTime AND msgTime <= :endTime')
  Future<void> deleteMessageWithMsgType(String userId, String sessionId,
      String companyId, int msgType, int startTime, int endTime);

  @Query(
      'DELETE FROM Notice WHERE uid = :userId AND sessionId = :sessionId AND msgType = :msgType AND msgTime >= :startTime AND msgTime <= :endTime')
  Future<void> deleteSystemMessageWithMsgType(String userId, String sessionId, int msgType, int startTime, int endTime);

  @Query(
      'SELECT * FROM Notice WHERE uid = :userId AND sessionId = :sessionId AND msgId = :msgId')
  Future<List<Notice>> getMessageWihtMsgId(
      String userId, String sessionId, String msgId);

  @Query(
      'SELECT * FROM Notice WHERE uid = :userId AND sessionId = :sessionId AND companyId = :companyId AND msgType <> :msgType order by msgTime desc LIMIT :maxCount')
  Future<List<Notice>> getLastNoticeDataWithCompanyId(String userId,
      String sessionId, String companyId, int msgType, int maxCount);

  @Query(
      'SELECT * FROM Notice WHERE uid = :userId AND sessionId = :sessionId AND classifyType = :classifyType AND msgTime < :msgTime order by msgTime desc')
  Future<List<Notice>> getLocalWorkNoticeDataWithClassifyType(
      String userId, String sessionId, int classifyType, int msgTime);

  @Query(
      'UPDATE Notice SET tempStatus = :tempStatus WHERE uid = :uid AND cmdId = :cmdId')
  Future<void> upDateNoticeTempStatus(String uid, String cmdId, int tempStatus);

  @Query('UPDATE Notice SET data = :data WHERE uid = :uid AND cmdId = :cmdId')
  Future<void> upDateNoticeData(String uid, String cmdId, String data);

  @Query('DELETE FROM Notice WHERE msgId = :msgId')
  Future<void> deleteNoticeMsgWithMsgId(String msgId);
}
