import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';

@dao
abstract class MessageDao {
  @Query('SELECT * FROM Message WHERE uid = :userId')
  Future<List<Message>> findAllMessagesByOwnerId(String userId);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<Message> messageList);

  @delete
  Future<void> removeMessage(Message message);

  @Query("DELETE FROM Message WHERE msgId = :msgId")
  Future<void> delMessageByMsgId(String msgId);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertMessage(Message message);

  @Query('UPDATE Message SET msgId = :newId , isSuccess = :isSuccess , sendTime = :sendTime , upDateTime = :upDateTime WHERE msgId = :oldId')
  Future<void> updateMessageSendStatus(String newId , String oldId , int isSuccess , int sendTime, int upDateTime);

  @Query('DELETE FROM Message')
  Future<void> clearMessage();

  @Query('SELECT * FROM Message WHERE uid = :userId AND appChatId = :appChatId')
  Future<List<Message>> getMessagesByOwnerId_chatId(
      String userId, String appChatId);

  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId AND sendTime = :sendTime AND msgFrom = :msgFrom AND msgType = :msgType')
  Future<List<Message>> queryOffLineFaultMessage(
      String userId, String sessionId, int sendTime, int msgFrom, int msgType);

  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId order by sendTime desc LIMIT 35 OFFSET 0')
  Future<List<Message>> queryLastFaultMessage(String userId, String sessionId);

  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId order by sendTime desc')
  Future<List<Message>> queryAllMessageBySessionId(
      String userId, String sessionId);

  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId AND sendTime = :sendTime AND msgType = :msgType AND (isDelete <> 1 OR isDelete IS NULL)')
  Future<List<Message>> queryMsgByUid_sessionId_sendTime_msgType(
      String userId, String sessionId, int sendTime, int msgType);

  // 监听全部数据
  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId order by sendTime desc')
  Stream<List<Message>> listenMessageList(String userId, String sessionId);

  // 本地分页查询
  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId AND (isDelete <> 1 OR isDelete IS NULL) LIMIT :pageSize OFFSET :offset')
  Future<List<Message>> queryMessagesByOwnerIdAndSessionIdByPage(
      String userId, String sessionId, int offset, int pageSize);

  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND cmdId = :cmdId AND msgType != 999993')
  Future<List<Message>> queryMessageByCmdId(String userId, String cmdId);

  @Query('SELECT * FROM Message WHERE sessionType in (1, 2) And msgType != 9 And uid = :userId AND text LIKE :keyword ORDER BY sendTime DESC')
  Future<List<Message>> queryChatMessage(String userId, String keyword);

  @Query('SELECT * FROM Message WHERE sessionType = 2 And msgType != 9 And uid = :userId AND text LIKE :keyword ORDER BY sendTime DESC')
  Future<List<Message>> queryChatGroupMessage(String userId, String keyword);
  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId AND sendTime < :sendTime AND (isDelete <> 1 OR isDelete IS NULL) order by sendTime desc LIMIT :maxCount')
  Future<List<Message>> getLocalMessageData(
      String userId, String sessionId, int sendTime, int maxCount);
  @Query(
      'DELETE FROM Message WHERE uid = :userId AND sessionId = :sessionId AND msgType = :msgType AND sendTime >= :startTime AND sendTime <= :endTime')
  Future<void> deleteMessageWithMsgType(
      String userId, String sessionId, int msgType, int startTime, int endTime);
  @Query(
      'SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId AND msgId = :msgId')
  Future<List<Message>> getChatMessageWihtMsgId(
      String userId, String sessionId, String msgId);

  @Query(
      'UPDATE Message SET msgType = :msgType , upDateTime = :upDateTime WHERE uid = :userId AND msgId = :msgId')
  Future<void> updateMessageTypeByMsgId(String userId, String msgId, int msgType ,int upDateTime);

  @Query('UPDATE Message SET isReaded = 1 ,upDateTime = :upDateTime WHERE uid = :userId AND sessionId = :sessionId AND msgFrom = 1 AND isReaded != 1')
  Future<void> updateMessageToReaded(String userId, String sessionId, int upDateTime);

  @Query('SELECT * FROM Message WHERE uid = :userId AND msgId = :msgId')
  Future<List<Message>> queryMessageByUidAndMsgId(String userId, String msgId);

  @Query('UPDATE Message SET isDelete = 1 ,upDateTime = :upDateTime WHERE uid = :userId AND sessionId = :sessionId')
  Future<void> updateMessageDelete(String userId, String sessionId, int upDateTime);

  @transaction
  Future<void> deleteAllMessage(String userId, String sessionId,  int upDateTime) async {
    await updateMessageDelete(userId, sessionId, upDateTime);
  }

  @Query('SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId AND msgType = 24')
  Future<List<Message>> queryDeleteTypeMsg(String userId, String sessionId);

  @Query('UPDATE Message SET sendName = :sendName,sendHeader = :sendHeader ,upDateTime = :upDateTime WHERE uid = :userId AND sessionId = :sessionId AND sendId = :sendId AND (sendName <> :sendName OR sendHeader <> :sendHeader)')
  Future<void> updateSenderInfo(String userId, String sessionId,String sendId,String sendName,String sendHeader, int upDateTime);

  @Query('SELECT * FROM Message WHERE uid = :userId AND isSuccess = 2')
  Future<List<Message>> querySendingMsgList(String userId);

  @Query('SELECT * FROM Message WHERE uid = :userId AND sessionId = :sessionId AND (isDelete <> 1 OR isDelete IS NULL) ORDER BY sendTime DESC LIMIT 1')
  Future<List<Message>> queryLastMsgBySessionId(String userId, String sessionId);

  @Query('ALTER TABLE Message DROP COLUMN `isReaded`')
  Future<void> deleteMessageIsReadedColumn();

  @Query('SELECT * FROM Message WHERE uid = :userId AND (isSuccess = 2 OR isSuccess = 0)')
  Future<List<Message>> querySendUnSuccessedMsgList(String userId);
}
