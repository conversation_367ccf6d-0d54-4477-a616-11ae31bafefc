import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/im/db/entity/session_read.dart';

@dao
abstract class SessionReadDao {
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertSessionRead(SessionRead sessionRead);

  @Query('SELECT * FROM SessionRead WHERE uid = :userId AND sessionId = :sessionId')
  Future<List<SessionRead>> querySessionReadBySessionId(String userId, String sessionId);

  @Query(
      'UPDATE SessionRead SET lastReadTime = :lastReadTime WHERE uid = :userId AND sessionId = :sessionId')
  Future<void> upDateSessionUnReadCount(
      String userId, String sessionId, int lastReadTime);

  @Query(
      'SELECT * FROM SessionRead WHERE uid = :userId AND sessionId = :sessionId order by lastReadTime desc')
  Stream<List<SessionRead>> listenSessionReadList(String userId, String sessionId);

}