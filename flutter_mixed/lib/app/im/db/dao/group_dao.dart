import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/im/db/entity/group.dart';

@dao
abstract class GroupDao {
  @transaction
  Future<void> insertAllGroupInTransaction(List<Group> groups) async {
    // 使用批量插入替代单条插入
    await insertList(groups);
  }

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<Group> messageList);

  @Query('SELECT * FROM group_chat WHERE uid = :userId')
  Future<List<Group>> findAllGroupByOwnerId(String userId);

  @delete
  Future<void> removeGroup(Group message);

  @Query("DELETE FROM group_chat WHERE groupId = :groupId")
  Future<void> delGroupByGroupId(String groupId);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertGroup(Group group);

  @Query('SELECT * FROM group_chat WHERE uid = :userId AND groupId = :groupId')
  Future<List<Group>> queryGroupByGroupId(String userId, String groupId);

  @Query('SELECT * FROM group_chat WHERE uid = :userId AND groupId = :groupId ')
  Stream<List<Group>> listenGroupList(String userId, String groupId);
}