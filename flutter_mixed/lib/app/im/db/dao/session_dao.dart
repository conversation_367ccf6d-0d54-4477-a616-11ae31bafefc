import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';

@dao
abstract class SessionDao {
  @Query('SELECT * FROM Session WHERE uid = :userId order by msgTime desc')
  Future<List<Session>> findAllSessionsByOwnerId(String userId);

  @Query(
      'SELECT * FROM Session WHERE uid = :userId AND sessionHidden = 0 order by msgTime desc')
  Future<List<Session>> findAllSessionsNotHiddenByOwnerId(String userId);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<Session> sessionList);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertSession(Session session);

  @transaction
  Future<void> insertAllSessionsInTransaction(List<Session> sessions) async {
    // 使用批量插入替代单条插入
    await insertList(sessions);
  }

  @Query('DELETE FROM Session')
  Future<void> clearSession();

  @Query("DELETE FROM Session WHERE appChatId = :appChatId AND uid = :userId")
  Future<void> delSessionBySessionId(String appChatId, String userId);

  @Query('SELECT * FROM Session WHERE uid = :userId AND sessionId = :sessionId')
  Future<List<Session>> getSessionByOwnerId_sessionId(
      String userId, String sessionId);

  // @Query('SELECT SUM(notReadCount) FROM Session WHERE uid = :userId AND sessionHidden = :sessionHidden AND noDisturb = :noDisturb')
  // Future<double?> getUnreadMsgCount(String userId , {int sessionHidden = 0 , int noDisturb = 0});

  @Query('SELECT * FROM Session WHERE uid = :userId')
  Stream<List<Session>> listenSessionList(String userId);

  @Query(
      'UPDATE Session SET notReadCount = :notReadCount WHERE uid = :userId AND sessionId = :sessionId')
  Future<void> upDateSessionUnReadCount(
      String userId, String sessionId, int notReadCount);

  @Query(
      'UPDATE Session SET headerUrl = :headerUrl WHERE uid = :userId AND sessionId = :sessionId')
  Future<void> upDateSessionGroupLogo(
      String userId, String sessionId, String headerUrl);

  @Query(
      'UPDATE Session SET name = :name,headerUrl = :headerUrl WHERE uid = :userId AND sessionId = :sessionId')
  Future<void> upDateSessionSessionNameAndLogo(
      String userId, String sessionId, String name, String headerUrl);

  @Query(
      'UPDATE Session SET msgContent = :msgContent WHERE sessionType = :sessionType AND appChatId = :appChatId AND msgTime = :msgTime')
  Future<void> upDateSessionWithContent(
      int sessionType, String appChatId, int msgTime,String msgContent);
}
