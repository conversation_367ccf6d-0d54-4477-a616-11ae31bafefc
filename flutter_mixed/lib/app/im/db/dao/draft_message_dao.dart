import 'package:floor/floor.dart';

import '../entity/draft_message.dart';

@dao
abstract class DraftMessageDao {

  @Query('SELECT * FROM DraftMessage WHERE uid = :userId')
  Future<List<DraftMessage>> findAllGroupByOwnerId(String userId);

  @update
  Future<void> updateDraft(DraftMessage draft);

  @delete
  Future<void> deleteDraft(DraftMessage draft);

  @Query('DELETE FROM DraftMessage WHERE sessionId = :sessionId AND uid = :uid')
  Future<void> deleteDraftBySessionId(String sessionId, String uid);

  @Query('SELECT * FROM DraftMessage WHERE sessionId = :sessionId AND uid = :uid')
  Future<DraftMessage?> getDraftBySessionId(String sessionId, String uid);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertDraft(DraftMessage draft);
}