


import 'package:floor/floor.dart';

import 'package:floor/floor.dart';

@Entity(primaryKeys: ['sessionId', 'uid'])
class DraftMessage {
  String sessionId; // 会话ID
  String uid; // 用户ID
  String? text; // 草稿文本内容
  String? quoteMessageId; // 引用消息ID，如果有的话
  int createTime; // 创建时间
  int updateTime; // 更新时间

  DraftMessage(
      this.sessionId,
      this.uid, {
        this.text,
        this.quoteMessageId,
        required this.createTime,
        required this.updateTime,
      });

  Map<String, dynamic> toJson() => {
    'sessionId': sessionId,
    'uid': uid,
    'text': text,
    'quoteMessageId': quoteMessageId,
    'createTime': createTime,
    'updateTime': updateTime,
  };

  factory DraftMessage.fromJson(Map<String, dynamic> json) => DraftMessage(
    json['sessionId'],
    json['uid'],
    text: json['text'],
    quoteMessageId: json['quoteMessageId'],
    createTime: json['createTime'],
    updateTime: json['updateTime'],
  );
}