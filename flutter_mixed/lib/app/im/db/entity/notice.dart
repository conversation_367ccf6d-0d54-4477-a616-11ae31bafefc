import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';

@entity
class Notice {
  @PrimaryKey()
  String msgId = ''; // 消息id ，唯一

  String? uid = ''; // ownid
  String? sessionId = ''; // 会话id
  int? msgTime = 0; // 消息时间戳
  String? title;
  String? subtitle;
  String? context;
  String? ext;
  String? data;
  int? type = 0;
  String? companyId = '';
  int? msgType = 0;
  String? cmdId = ''; //更新指令id
  int? tempStatus = -1;
  int? classifyType = 0;
  Notice(
      {required this.msgId,
      this.uid,
      this.sessionId,
      this.msgTime,
      this.title,
      this.subtitle,
      this.context,
      this.ext,
      this.data,
      this.type,
      this.companyId,
      this.msgType,
      this.cmdId,
      this.tempStatus,
      this.classifyType});

  Notice.fromJson(Map<String, dynamic> json) {
    msgId = json['msgId'] ?? '';
    uid = json['uid'] ?? '';
    sessionId = json['sessionId'] ?? '';
    msgTime = json['msgTime'] ?? 0;
    title = json['title'] ?? '';
    subtitle = json['subtitle'] ?? '';
    context = json['context'] ?? '';
    data = json['data'] ?? '';
    ext = json['ext'] ?? '';
    type = json['type'] ?? 0;
    companyId = json['companyId'] ?? '';
    msgType = json['msgType'] ?? 0;
    cmdId = json['cmdId'] ?? '';
    tempStatus = json['tempStatus'] ?? -1;
    classifyType = json['classifyType'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> map = Map<String, dynamic>();
    map['msgId'] = msgId;
    map['uid'] = uid;
    map['sessionId'] = sessionId;
    map['msgTime'] = msgTime;
    map['title'] = title;
    map['subtitle'] = subtitle;
    map['context'] = context;
    map['data'] = data;
    map['ext'] = ext;
    map['type'] = type;
    map['companyId'] = companyId;
    map['msgType'] = msgType;
    map['cmdId'] = cmdId;
    map['tempStatus'] = tempStatus;
    map['classifyType'] = classifyType;
    return map;
  }

  @override
  String toString() {
    return 'Notice{msgId: $msgId,sessionId: $sessionId, uid: $uid, msgTime: $msgTime, title: $title, subtitle: $subtitle, context: $context, data: $data ext: $ext, type: $type,companyId:$companyId,msgType:$msgType,cmdId:$cmdId,tempStatus:$tempStatus,classifyType:$classifyType}';
  }
}
