import 'package:floor/floor.dart';

@Entity(primaryKeys: ['uid', 'sessionId'])
class SessionRead {
  String? uid = ''; // ownid
  String? sessionId = ''; // 会话id
  String? appChatId = ''; // 会话担当id
  int? lastReadTime = 0; // 最后的已读时间戳
  SessionRead(
      {required this.uid,
      this.sessionId,
      this.appChatId,
      this.lastReadTime});

  SessionRead.fromJson(Map<String, dynamic> json) {
    uid = json['uid'] ?? '';
    sessionId = json['sessionId'] ?? '';
    appChatId = json['appChatId'] ?? '';
    lastReadTime = json['lastReadTime'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> map = Map<String, dynamic>();
    map['uid'] = uid;
    map['sessionId'] = sessionId;
    map['appChatId'] = appChatId;
    map['lastReadTime'] = lastReadTime;
    return map;
  }

  @override
  String toString() {
    return 'SessionRead{sessionId: $sessionId, uid: $uid, appChatId: $appChatId, lastReadTime: $lastReadTime}';
  }
}