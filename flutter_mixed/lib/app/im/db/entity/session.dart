
import 'package:floor/floor.dart';

@Entity(primaryKeys: ['sessionId', 'uid'])
class Session {

  String sessionId; // 会话id ，唯一

  String? uid; // ownid
  int? sessionTop = 0;  // 会话是否置顶， 0未置顶，1已置顶
  int? sessionHidden = 0;  // 会话是否删除， 0显示， 1删除  -1仅置顶用(有置顶无会话，会话不显示)
  int? msgType ;  // 消息类型，  // 1 文本， 2 图片 3 语音...
  int? sessionType ;  // 会话类型： 单聊1，群聊2， 系统消息10000  工作通知20000 合作通知40000  审批通知30000  工单通知60000
  String? extend;  // 扩展字段
  String? name;  // 会话标题
  int? chatStatus;  // 单聊群组会话状态 默认0正常, 1单聊用户已注销、群组已解散, 2不是群组成员
  String? cmdId = '';
  String? headerUrl = '';
  String? msgId = '';  // '最新一条消息id'
  String? msgContent = '';  // 最新一条消息内容
  int? notReadCount = 0;  // 未读消息数
  int? msgTime = 0;  // 最新消息时间
  int? deleteLastTime = 0;  // 删除最新消息时间，仅记录
  int? noDisturb = 0;  //'消息免打扰'  0代表未开启，1代表开启
  String? appChatId;  //表示聊天中【对方】在担当办公中的userId或群组id，用于获取用户、群组详情

  // Session(this.sessionId);

  Session({required this.sessionId,
    this.uid,
    this.sessionTop,
    this.sessionHidden,
    this.msgType,
    this.sessionType,
    this.extend,
    this.name,
    this.chatStatus,
    this.cmdId,
    this.headerUrl,
    this.msgId,
    this.msgContent,
    this.notReadCount,
    this.msgTime,
    this.noDisturb,
    this.appChatId,
    this.deleteLastTime});

  @override
  String toString() {
    return 'Session{sessionId: $sessionId, uid: $uid, sessionTop: $sessionTop, sessionHidden: $sessionHidden, msgType: $msgType, sessionType: $sessionType, extend: $extend, name: $name, chatStatus: $chatStatus, cmdId: $cmdId, headerUrl: $headerUrl, msgId: $msgId, msgContent: $msgContent, notReadCount: $notReadCount, msgTime: $msgTime, noDisturb: $noDisturb, appChatId: $appChatId}';
  }

  factory Session.fromJson(Map<String, dynamic> json) {
    return Session(
      sessionId: json['sessionId'],
      uid: json['uid'],
      sessionTop: json['sessionTop'],
      sessionHidden: json['sessionHidden'],
      msgType: json['msgType'],
      sessionType: json['sessionType'],
      extend: json['extend'],
      name: json['name'],
      chatStatus: json['chatStatus'],
      cmdId: json['cmdId'],
      headerUrl: json['headerUrl'],
      msgId: json['msgId'],
      msgContent: json['msgContent'],
      notReadCount: json['notReadCount'],
      msgTime: json['msgTime'],
      noDisturb: json['noDisturb'],
      appChatId: json['appChatId'],
      deleteLastTime: json['deleteLastTime'],
    );
  }

  Map<String, dynamic> toJson() => {
        'sessionId': sessionId,
        'uid': uid,
        'sessionTop': sessionTop,
        'sessionHidden': sessionHidden,
        'msgType': msgType,
        'sessionType': sessionType,
        'extend': extend,
        'name': name,
        'chatStatus': chatStatus,
        'cmdId': cmdId,
        'headerUrl': headerUrl,
        'msgId': msgId,
        'msgContent': msgContent,
        'notReadCount': notReadCount,
        'msgTime': msgTime,
        'noDisturb': noDisturb,
        'appChatId': appChatId,
        'deleteLastTime':deleteLastTime
      };
}