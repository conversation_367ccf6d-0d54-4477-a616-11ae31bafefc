import 'package:floor/floor.dart';

@Entity(primaryKeys: ['groupId', 'uid'], tableName: 'group_chat')
class Group {
  String groupId = '';  // 群组 Id
  String? uid; 
  String? name;  //  群组名称
  String? logo;  // 群头像
  String? createUserId;  // 创建者id
  int? createTime;  // 创建时间
  int? type;  // 1组织群组  2私有群组
  String? orgId;  // 组织 id type为2时为null
  int? receiveMode; // 接收模式 1正常接收 2屏蔽群消息
  int? status;  // 群组状态 0正常 1已解散
  int? banned;  // 封禁状态
  String? logoText; // 头像文字
  String? colour; // 头像颜色
  int? isAdmin; // 是否是管理员
  int? updateTime;

  Group(this.groupId, {
    this.uid,
    this.name,
    this.logo,
    this.createUserId,
    this.createTime,
    this.type,
    this.orgId,
    this.receiveMode,
    this.status,
    this.banned,
    this.logoText,
    this.colour,
    this.isAdmin,
    this.updateTime,
  });

  Group.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    uid = json['uid'];
    name = json['name'];
    logo = json['logo'];
    createUserId = json['createUserId'];
    createTime = json['createTime'];
    type = json['type'];
    orgId = json['orgId'];
    receiveMode = json['receiveMode'];
    status = json['status'];
    banned = json['banned'];
    logoText = json['logoText'];
    colour = json['colour'];
    isAdmin = json['isAdmin'];
    updateTime = json['updateTime'];
  }

  Map<String, dynamic> toJson() => {
    'groupId': groupId,
    'uid': uid,
    'name': name,
    'logo': logo,
    'createUserId': createUserId,
    'createTime': createTime,
    'type': type,
    'orgId': orgId,
    'receiveMode': receiveMode,
    'status': status,
    'banned': banned,
    'logoText': logoText,
    'colour': colour,
    'isAdmin': isAdmin,
    'updateTime': updateTime,
  };

  @override
  String toString() {
    return 'Group{groupId: $groupId, uid: $uid, name: $name, logo: $logo, createUserId: $createUserId, createTime: $createTime, type: $type, orgId: $orgId, receiveMode: $receiveMode, status: $status, banned: $banned, logoText: $logoText, colour: $colour, isAdmin: $isAdmin, updateTime: $updateTime}';
  }
}