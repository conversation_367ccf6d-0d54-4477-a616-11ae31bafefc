

import 'package:floor/floor.dart';

@Entity(primaryKeys: ['msgId', 'uid'])
class Message {

  String msgId = '';
  
  String? uid;  // 用户id
  String? sessionId;   //会话id  ' 改为使用 IM服务返回的userId
  int? msgType = 1; //消息类型'  1文本 2图片 3语音。。。。  撤销   13发起音视频
  int? sessionType = 1;//聊天类型' 单聊1，群聊2， -2系统消息  -3工作通知 -4合作通知
  String? sendId;  //消息发送者userId
  String? sendName;//
  String? sendHeader;//
  int? sendTime = 0;//
  String? cmdId = '';
  int? msgFrom = 1;  //2对方   1本人
  String? extendOne = '';//扩展字段
  String? extendTwo = '';//扩展字段
  String? extendThree = '';//扩展字段
  int? showTime = 0; //是否超过时间展示 0不显示   1显示
  int? isReaded = 0;//已听 未听，1已读  0未读
  String? fileId = ''; //文件id用于获取token

  String? text;//只用于文本
  String? localUrl; //本地路径  图片 录音 视频 文件 本地路径
  String? imgUrl;//复用做文件和视频的远程地址
  String? miniImgUrl;//
  double? imgWidth = 0;//
  double? imgHeight = 0;//
  String? voiceUrl; //语音地址
  int? fileSize = 0; //文件大小
  int? voiceTime = 0;//语音时间产长短
  double? longitude = 0; //经度=====用经度和纬度控制上传文件时的进度条
  double? latitude = 0;  //纬度
  String? addressTitle;//
  String? addressDetail;//
  String? addressImgUrl;//
  int? callType = 1;//通话类型 1语音  2视频
  String? callContent;//具体内容   根据msgType判断  13发起者名字, 14时代表通话时长(string) 15未接听    16的时候  1已在通话会议中拒绝,2接收者主动挂断
  String? meetingId; //音视频会话id
  int? isSuccess = 1; //  1为发送成功  0为发送失败   2发送中
  String? appChatId = "";  //表示聊天中对方在担当办公中的userId或群组id，用于获取用户群组详情
  //小视频、文件所需字段，接收到的消息根据fileID获取到URL后，均保存在localUrl字段中
  String? fileName;// 文件名 例子: 学习资料.zip
  String? videoImageId;//视频的封面图id，小视频消息要复用imgWidth和imgHeight字段记录封面图的宽高
  String? videoImagePath;//视频的封面图本地路径

  // 是否本地删除
  int? isDelete = 0;
  int? upDateTime = 0;//数据更新时间

  @ignore
  String? quoteText;
  @ignore
  bool showImmediateTime = false;

  @ignore
  bool showGapTime = false;

  Message(
      this.msgId,{this.uid,
        this.sessionId,
        this.msgType,
        this.sessionType,
        this.sendId,
        this.sendName,
        this.sendHeader,
        this.sendTime,
        this.cmdId,
        this.msgFrom,
        this.extendOne,
        this.extendTwo,
        this.extendThree,
        this.showTime,
        this.isReaded,
        this.fileId,
        this.text,
        this.localUrl,
        this.imgUrl,
        this.miniImgUrl,
        this.imgWidth,
        this.imgHeight,
        this.voiceUrl,
        this.fileSize,
        this.voiceTime,
        this.longitude,
        this.latitude,
        this.addressTitle,
        this.addressDetail,
        this.addressImgUrl,
        this.callType,
        this.callContent,
        this.meetingId,
        this.isSuccess,
        this.appChatId,
        this.fileName,
        this.videoImageId,
        this.videoImagePath,
        this.isDelete,
        this.upDateTime
      });


  Message.fromJson(Map<String, dynamic> json) {
      msgId =  json['msgId'] ?? '';
      uid= json['uid'];
      sessionId= json['sessionId'];
      msgType= json['msgType'];
      sessionType= json['sessionType'];
      sendId= json['sendId'];
      sendName= json['sendName'];
      sendHeader= json['sendHeader'];
      sendTime= (json['sendTime'] is int) ? json['sendTime'] : 0;
      cmdId= json['cmdId'];
      msgFrom= json['msgFrom'];
      extendOne= json['extendOne'];
      extendTwo= json['extendTwo'];
      extendThree= json['extendThree'];
      showTime= (json['showTime'] is int) ? json['showTime'] : 0;
      isReaded= (json['isReaded'] is String) ? 0 : json['isReaded'];
      fileId= json['fileId'];
      text= json['text'];
      localUrl= json['localUrl'];
      imgUrl= json['imgUrl'];
      miniImgUrl= json['miniImgUrl'];
      imgWidth=  (json['imgWidth'] is int) ? (json['imgWidth'] as int).toDouble() : 0;
      imgHeight= (json['imgHeight'] is int) ? (json['imgHeight'] as int).toDouble() : 0;
      voiceUrl= json['voiceUrl'];
      fileSize= (json['fileSize'] is String) ? 0: json['fileSize'];
      voiceTime= (json['voiceTime'] is String) ? 0 : json['voiceTime'];
      longitude= (json['longitude'] is double) ? (json['longitude'] as double).toDouble() : 0;
      latitude= (json['latitude'] is double) ? (json['latitude'] as double).toDouble() : 0;
      addressTitle= json['addressTitle'];
      addressDetail= json['addressDetail'];
      addressImgUrl= json['addressImgUrl'];
      callType= (json['callType'] is String) ? 0: json['callType'];
      callContent= json['callContent'];
      meetingId= json['meetingId'];
      isSuccess= (json['isSuccess'] is int) ? json['isSuccess'] : 0;
      appChatId= json['appChatId'];
      fileName= json['fileName'];
      videoImageId= json['videoImageId'];
      videoImagePath= json['videoImagePath'];
      isDelete= json['isDelete'] ?? 0;
      quoteText= json['quoteText'] ?? '';
      showImmediateTime= json['showImmediateTime'] ?? false;
      showGapTime= json['showGapTime'] ?? false;
      upDateTime = json['upDateTime'] ?? 0;
  }

  Map<String, dynamic> toJson() => {
        'msgId': msgId,
        'uid': uid,
        'sessionId': sessionId,
        'msgType': msgType,
        'sessionType': sessionType,
        'sendId': sendId,
        'sendName': sendName,
        'sendHeader': sendHeader,
        'sendTime': sendTime,
        'cmdId': cmdId,
        'msgFrom': msgFrom,
        'extendOne': extendOne,
        'extendTwo': extendTwo,
        'extendThree': extendThree,
        'showTime': showTime,
        'isReaded': isReaded,
        'fileId': fileId,
        'text': text,
        'localUrl': localUrl,
        'imgUrl': imgUrl,
        'miniImgUrl': miniImgUrl,
        'imgWidth': imgWidth,
        'imgHeight': imgHeight,
        'voiceUrl': voiceUrl,
        'fileSize': fileSize,
        'voiceTime': voiceTime,
        'longitude': longitude,
        'latitude': latitude,
        'addressTitle': addressTitle,
        'addressDetail': addressDetail,
        'addressImgUrl': addressImgUrl,
        'callType': callType,
        'callContent': callContent,
        'meetingId': meetingId,
        'isSuccess': isSuccess,
        'appChatId': appChatId,
        'fileName': fileName,
        'videoImageId': videoImageId,
        'videoImagePath': videoImagePath,
        'isDelete': isDelete,
        'quoteText': quoteText,
        'showImmediateTime': showImmediateTime,
        'showGapTime': showGapTime,
        'upDateTime':upDateTime
      };

  @override
  String toString() {
    return 'Message{ msgId: $msgId , text: $text, cmdid: $cmdId, videoImageId: $videoImageId} , updateTime: $upDateTime';
  }

  @override
  int get hashCode => msgId.hashCode;

  @override
  bool operator ==(Object other) {
    return identical(this, other)|| (other is Message && other.msgId == msgId);
  }
}