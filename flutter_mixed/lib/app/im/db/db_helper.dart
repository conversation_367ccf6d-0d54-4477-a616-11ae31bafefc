import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/db/entity/group.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/db/entity/session_read.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_single_chat_resp.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../common/user/user_helper.dart';
import '../constant/ImMsgConstant.dart';
import 'app_database.dart';
import 'entity/draft_message.dart';

// const String APP_DATABASE_NAME = 'joinu_dd_im.db';

//final appDatabase = $FloorAppDataBase.databaseBuilder('joinu_dd_im.db').build();
final appDatabase = $FloorAppDataBase
    .databaseBuilder('joinu_dd_im.db')
    .addMigrations([migration1to2,migration2to3 ,migration3to4]).build();
//Session表添加deleteLastTime 
final migration1to2 = Migration(1, 2, (database) async {
  await database.execute(
      'ALTER TABLE Session ADD `deleteLastTime` INTEGER');
});

final migration2to3 = Migration(2, 3, (database) async {
  await database.execute(
            'CREATE TABLE IF NOT EXISTS `SessionRead` (`uid` TEXT NOT NULL, `sessionId` TEXT NOT NULL, `appChatId` TEXT,  `lastReadTime` INTEGER, PRIMARY KEY (`uid`, `sessionId`))');
});

final migration3to4 = Migration(3, 4, (database) async {
  await database.execute(
      'CREATE TABLE IF NOT EXISTS `DraftMessage` (`uid` TEXT NOT NULL, `sessionId` TEXT NOT NULL, `quoteMessageId` TEXT, `text` TEXT,  `createTime` INTEGER, `updateTime` INTEGER, PRIMARY KEY (`uid`, `sessionId`))');
});


class DbHelper {

  static initDb() async{
    await appDatabase;
  }

  // ------------------------------------ session start ------------------------------------
  static Future insertSession(Session session) async {
    // logger('insertSession ==> ${session}');
    final db = await appDatabase;
    return db.getSessionDao.insertSession(session);
  }

  static Future clearSession() async {
    final db = await appDatabase;
    return db.getSessionDao.clearSession();
  }

  static Future insertSessions(List<Session> sessions) async {
    return insertSessionsTransaction(sessions);
    final db = await appDatabase;
    return db.getSessionDao.insertList(sessions);
  }

  static Future insertSessionsTransaction(List<Session> sessions) async{
    final db = await appDatabase;
    var dao = db.getSessionDao;
    return await dao.insertAllSessionsInTransaction(sessions);
  }

  static Future<List<Session>> getSessionListByOwnerId(String userId) async {
    final db = await appDatabase;
    return db.getSessionDao.findAllSessionsNotHiddenByOwnerId(userId);
  }

  static Future<Session?> getSessionByOwnerId2SessionId(
      String userId, String sessionId) async {
    final db = await appDatabase;
    var list =
        await db.getSessionDao.getSessionByOwnerId_sessionId(userId, sessionId);
    if (list.isNotEmpty) return list[0];
    return null;
  }

  static Future<List<Session>> getSessionNotHiddenByOwnerId(
      String userId) async {
    final db = await appDatabase;
    var list = await db.getSessionDao.findAllSessionsNotHiddenByOwnerId(userId);
    return list;
  }

  // 获取当前用户下的所有会话
  static Future<List<Session>> getMySessionList(String userId) async {
    final db = await appDatabase;
    var list = await db.getSessionDao.findAllSessionsByOwnerId(userId);
    return list;
  }

  static Stream<List<Session>> listenSessionList(
    String userId,
  ) async* {
    final db = await appDatabase;
    yield* db.getSessionDao.listenSessionList(userId).map((event) => event);
  }

  //更新会话未读数
  static Future upDateSessionUnReadCount(
      String userId, String sessionId, int notReadCount) async {
    logger('upDateSessionUnReadCount =session===$userId===$sessionId=> $notReadCount');
    final db = await appDatabase;
    return db.getSessionDao
        .upDateSessionUnReadCount(userId, sessionId, notReadCount);
  }

  // 更新 session 列表群logo
  static Future updateSessionGroupLogo(String sessionId, String headImg) async {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    return db.getSessionDao.upDateSessionGroupLogo(ownId, sessionId, headImg);
  }

  // 更新 session 列表名称和logo
  static Future upDateSessionSessionNameAndLogo(
      String sessionId, String name, String headUrl,String ownId) async {
    final db = await appDatabase;
    return db.getSessionDao
        .upDateSessionSessionNameAndLogo(ownId, sessionId, name, headUrl);
  }

  // 删除 session
  static Future deleteSession(String appChatId) async {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    return db.getSessionDao.delSessionBySessionId(appChatId, ownId);
  }

  //更新sessionn
  static Future upDateSessionWithContent(int sessionType,String appChatId,int msgTime,String msgContent) async {
    final db = await appDatabase;
    return db.getSessionDao.upDateSessionWithContent(sessionType, appChatId,msgTime,msgContent);
  }
  // ------------------------------------ session end ------------------------------------

  // ------------------------------------ message start ------------------------------------

  static Future deleteMsg(Message message) async {
    final db = await appDatabase;
    String msgId = message.msgId;
    var ownId = await UserHelper.getUid();
    var msg = await DbHelper.getMessageByUidAndMsgId(ownId, msgId);
    if (msg.isNotEmpty) {
      msg.first.isDelete = 1;
      msg.first.upDateTime = DateTime.now().millisecondsSinceEpoch;
      return await db.getMessageDao.insertMessage(msg.first);
    }
    return;
  }

  static Future deleteMsgReal(Message message) async {
    final db = await appDatabase;
    message.upDateTime = DateTime.now().millisecondsSinceEpoch;
    return await db.getMessageDao.removeMessage(message);
  }

  static Future deleteMsgByMsgId(String msgId) async {
    final db = await appDatabase;
    return await db.getMessageDao.delMessageByMsgId(msgId);
  }

  static Future insertMsg(Message message) async {
    logger('执行 insertMsg。。。${message.toString()}');
    message.upDateTime = DateTime.now().millisecondsSinceEpoch;
    final db = await appDatabase;
    return await db.getMessageDao.insertMessage(message);
  }

  static Future updateMsgSendStatusMsgId(
      String newId, String oldId, int isSuccess, int time) async {
    int  upDateTime = DateTime.now().millisecondsSinceEpoch;
    final db = await appDatabase;
    return await db.getMessageDao
        .updateMessageSendStatus(newId, oldId, isSuccess, time, upDateTime);
  }

  static Future<List<Message>> getMessageByUidAndMsgId(
      String uid, String msgId) async {
    final db = await appDatabase;
    return await db.getMessageDao.queryMessageByUidAndMsgId(uid, msgId);
  }

  static Future insertMessages(List<Message> messageList) async {
    for (var message in messageList) {message.upDateTime = DateTime.now().millisecondsSinceEpoch;}
    final db = await appDatabase;
    return db.getMessageDao.insertList(messageList);
  }

  static Future insertFaultMsg(int time, String sessionId, String uid) async {
    if (time == -1) return;
    final db = await appDatabase;
    var faultMsg = Message(getUUid())
      ..sendTime = time
      ..msgType = ConstantImMsgType.SSChatMessageTypeOfflineFault
      ..uid = uid
      ..sessionId = sessionId;
    return await db.getMessageDao.insertMessage(faultMsg);
  }

  // 单聊消息
  static Future<List<OfflineSingleChatResp>> getSingleChatList(
      String userId) async {
    final db = await appDatabase;

    return [];
  }

  static Future<List<Message>> getMessagesByOwnerId_chatId(
      String userId, String appchatId) async {
    final db = await appDatabase;
    return db.getMessageDao.getMessagesByOwnerId_chatId(userId, appchatId);
  }

  static Future<List<Message>> queryMessagesByOwnerIdAndSessionIdByPage(
      String userId, String sessionId, int offset, int pageSize) async {
    final db = await appDatabase;
    return db.getMessageDao.queryMessagesByOwnerIdAndSessionIdByPage(
        userId, sessionId, offset, pageSize);
  }

  static Future<Message?> queryLastFaultMessageBySessionId(
      String userId, String sessionId) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.queryLastFaultMessage(userId, sessionId);
    var faults = list.where((element) =>
        element.msgType == ConstantImMsgType.SSChatMessageTypeOfflineFault);
    if (faults.isEmpty) return null;
    return faults.first;
  }

  static Future<List<Message>> queryAllMessageBySessionId(
      String userId, String sessionId) async {
    final db = await appDatabase;
    var list =
        await db.getMessageDao.queryAllMessageBySessionId(userId, sessionId);
    return list;
  }

  static Future<Message?> queryOffLineFaultMessage(
      String userId, String sessionId, int sendTime, int msgFrom) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.queryOffLineFaultMessage(
        userId,
        sessionId,
        sendTime,
        msgFrom,
        ConstantImMsgType.SSChatMessageTypeOfflineFault);
    if (list.isEmpty) return null;
    return list[0];
  }

  static Future<Message?> queryMsgByCmdId(String cmdId) async {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    var list = await db.getMessageDao.queryMessageByCmdId(ownId, cmdId);
    if (list.isEmpty) return null;
    return list.first;
  }

  static Future<List<Message>> queryMsgListByCmdId(String cmdId) async {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    var list = await db.getMessageDao.queryMessageByCmdId(ownId, cmdId);
    return list;
  }

  // 更新/新增 断层
  static Future updateLineFaultAgeMessage(
    String userId,
    String sessionId,
    int sendTime,
    int msgFrom,
    String msgContent,
  ) async {
    var historyFault =
        await queryOffLineFaultMessage(userId, sessionId, sendTime, msgFrom);
    if (historyFault == null) {
      var msgId = ImClientManager.instance.loginConnectTime.toString() +
          sessionId +
          msgFrom.toString();
      var faultMessage = Message(msgId);
      faultMessage.msgFrom = msgFrom; //1代表上面的断点，2代表下面的断点
      faultMessage.uid = userId;
      faultMessage.sessionId = sessionId;
      faultMessage.text = msgContent;
      faultMessage.sendTime = sendTime;
      faultMessage.msgType = ConstantImMsgType.SSChatMessageTypeOfflineFault;
      insertMsg(faultMessage);
    }
  }

  // 插入一个断点消息,如果有对应断点就是更新断点
  static Future updateLineFaultMessage(String userId, String sessionId,
      int sendTime, int timeEdn, int msgFrom) async {
    var timeEndFaultMsg =
        await queryOffLineFaultMessage(userId, sessionId, timeEdn, msgFrom);
    var sendTimeFaultMsg =
        await queryOffLineFaultMessage(userId, sessionId, sendTime, msgFrom);
    if (timeEndFaultMsg == null && sendTimeFaultMsg == null) {
      var msgId = ImClientManager.instance.loginConnectTime.toString() +
          sessionId +
          msgFrom.toString();
      var faultMessage = Message(msgId);
      faultMessage.msgFrom = msgFrom; //1代表上面的断点，2代表下面的断点
      faultMessage.uid = userId;
      faultMessage.sessionId = sessionId;
      faultMessage.sendTime = sendTime;
      faultMessage.msgType = ConstantImMsgType.SSChatMessageTypeOfflineFault;
      insertMsg(faultMessage);
    } else if (timeEndFaultMsg != null && sendTimeFaultMsg == null) {
      timeEndFaultMsg.msgFrom = msgFrom; //1代表上面的断点，2代表下面的断点
      timeEndFaultMsg.uid = userId;
      timeEndFaultMsg.sessionId = sessionId;
      timeEndFaultMsg.sendTime = sendTime;
      timeEndFaultMsg.msgType = ConstantImMsgType.SSChatMessageTypeOfflineFault;
      timeEndFaultMsg.msgId =
          ImClientManager.instance.loginConnectTime.toString() +
              sessionId +
              msgFrom.toString();
      insertMsg(timeEndFaultMsg);
    } else if (timeEndFaultMsg != null && sendTimeFaultMsg != null) {
      final db = await appDatabase;
      var faultTimeList = await db.getMessageDao
          .queryMsgByUid_sessionId_sendTime_msgType(userId, sessionId, sendTime,
              ConstantImMsgType.SSChatMessageTypeOfflineFault);
      faultTimeList.forEach((element) {
        deleteMsg(element);
      });
    }
  }

  static Stream<List<Message>> listenMessageList(
      String userId, String sessionId) async* {
    final db = await appDatabase;
    yield* db.getMessageDao
        .listenMessageList(userId, sessionId)
        .map((event) => event);
  }

  //根据时间 倒序查找 顺序返回
  static Future<List<Message>> getLocalMessageData(
      String userId, String sessionId, int sendTime, int maxCount) async {
    final db = await appDatabase;
    List<Message> messages = await db.getMessageDao
        .getLocalMessageData(userId, sessionId, sendTime, maxCount);
    return messages.reversed.toList();
  }

  //删除此时间段内的断层
  static deleteChatMessageWithMsgType(String userId, String sessionId,
      int msgType, int startTime, int endTime) async {
    final db = await appDatabase;
    return await db.getMessageDao.deleteMessageWithMsgType(
        userId, sessionId, msgType, startTime, endTime);
  }

  //查询msgId是否存在
  static Future<List<Message>> getChatMessageWihtMsgId(
      String userId, String sessionId, String msgId) async {
    final db = await appDatabase;
    return await db.getMessageDao
        .getChatMessageWihtMsgId(userId, sessionId, msgId);
  }

  //更新消息类型
  static Future upDateMessageMsgType(
      String userId, String msgId, int msgType) async {
    int upDateTime = DateTime.now().millisecondsSinceEpoch;
    final db = await appDatabase;
    return db.getMessageDao.updateMessageTypeByMsgId(userId, msgId, msgType, upDateTime);
  }

  //更新会话未读数
  static Future upDateMessageReadStatus(String userId, String sessionId) async {
    logger("update--readed--$sessionId");
    int  upDateTime = DateTime.now().millisecondsSinceEpoch;
    final db = await appDatabase;
    return db.getMessageDao.updateMessageToReaded(userId, sessionId, upDateTime);
  }

  static Future<List<Message>> queryChatMessage(
      String userId, String keyword) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.queryChatMessage(userId, keyword);
    return list;
  }

  static Future<List<Message>> queryChatGroupMessage(
      String userId, String keyword) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.queryChatGroupMessage(userId, keyword);
    return list;
  }

  static Future<List<Session>> queryGroupChatList(String userId, String keyword) async {
    final db = await appDatabase;
    var allsessionList = await db.getSessionDao.findAllSessionsByOwnerId(userId);
    return allsessionList.where((s) => s.name?.contains(keyword) == true && s.isGroupChat()).toList();
  }

  static Future updateDeleteStatus(String sessionId) async {
    int upDateTime = DateTime.now().millisecondsSinceEpoch;
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    return await db.getMessageDao.deleteAllMessage(ownId, sessionId,upDateTime);
  }

  static Future<Message?> queryDeleteTypeMsg(String sessionId) async {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    var list = await db.getMessageDao.queryDeleteTypeMsg(ownId, sessionId);
    if (list.isEmpty) return null;
    return list.last;
  }

  //更新消息头像名称
  static Future updateSenderInfo(String userId, String sessionId, String sendId,
      String name, String headerUrl) async {
    int upDateTime = DateTime.now().millisecondsSinceEpoch;
    final db = await appDatabase;
    return await db.getMessageDao
        .updateSenderInfo(userId, sessionId, sendId, name, headerUrl ,upDateTime);
  }

  // 获取发送中的数据
  static Future<List<Message>> getMySendingMsgList(String userId) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.querySendingMsgList(userId);
    return list;
  }

  // 获取自己发送中 未成功的数据
  static Future<List<Message>> getMyUnSuccessMsgList(String userId) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.querySendUnSuccessedMsgList(userId);
    return list.where((m) => m.msgFrom == 1).toList();
  }

  // 获取会话最后一条消息
  static Future<Message?> getLastMsgBySessionId(String userId, String sessionId) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.queryLastMsgBySessionId(userId, sessionId);
    if (list.isEmpty) return null;
    return list.first;
  }

  // 获取会话关联的消息 最后一条可显示的 Message
  static Future<Message?> getLastAvailableMsgBySessionId(String userId, String sessionId) async {
    final db = await appDatabase;
    var list = await db.getMessageDao.queryAllMessageBySessionId(userId, sessionId);
    var filters = list.where((e) => e.msgType != ConstantImMsgType.SSChatMessageTypeOfflineFault
        && e.msgType != ConstantImMsgType.SSChatMessageTypeDel
        && e.msgType != ConstantImMsgType.SSChatMessageTypeClearChatRecord
    ).toList();
    if (filters.isEmpty) return null;
    return filters.first;
  }

  static Future<void> deleteMessageIsReadedColumn() async {
    final db = await appDatabase;
    await db.getMessageDao.deleteMessageIsReadedColumn();
  }

// ------------------------------------ message end ------------------------------------

// ------------------------------------ notice start ------------------------------------

  //根据时间 倒序查找 顺序返回
  static Future<List<Notice>> getLocalNoticeData(String userId,
      String sessionId, String companyId, int timestamp, int maxCount) async {
            
    final db = await appDatabase;
    List<Notice> noticeList = await db.getNoticeDao
        .getLocalNoticeData(userId, sessionId, companyId, timestamp, maxCount);
    return noticeList.reversed.toList();
  }

  //获取本地数据-不筛选公司 系统通知用到
  static Future<List<Notice>> getSystemmLocalNoticeData(
      String userId, String sessionId, int timestamp, int maxCount) async {
    final db = await appDatabase;
    List<Notice> noticeList = await db.getNoticeDao
        .getSystemmLocalNoticeData(userId, sessionId, timestamp, maxCount);
    return noticeList.reversed.toList();
  }

  static Future insertNotice(Notice notice) async {
    final db = await appDatabase;
    return await db.getNoticeDao.insertNotice(notice);
  }

  static Future insertNoticeList(List<Notice> noticeList) async {
    final db = await appDatabase;
    return db.getNoticeDao.insertNoticeList(noticeList);
  }

  static Stream<List<Notice>> listenNoticeList(String userId) async* {
    final db = await appDatabase;
    yield* db.getNoticeDao.listenNoticeList(userId).map((event) => event);
  }

  //删除此时间段内的断层
  static deleteMessageWithMsgType(String userId, String sessionId,
      String companyId, int msgType, int startTime, int endTime) async {
    final db = await appDatabase;
    return await db.getNoticeDao.deleteMessageWithMsgType(
        userId, sessionId, companyId, msgType, startTime, endTime);
  }

  //删除系统消息此时间段内的断层
  static deleteSystemMessageWithMsgType(String userId, String sessionId,
   int msgType, int startTime, int endTime) async {
    final db = await appDatabase;
    return await db.getNoticeDao.deleteSystemMessageWithMsgType(
        userId, sessionId, msgType, startTime, endTime);
  }

  //查询msgId是否存在
  static Future<List<Notice>> getMessageWihtMsgId(
      String userId, String sessionId, String msgId) async {
    final db = await appDatabase;
    return await db.getNoticeDao
        .getMessageWihtMsgId(userId, sessionId, msgId);
  }

  //获取此公司最后一条本地记录
  static Future<List<Notice>> getLastNoticeDataWithCompanyId(String userId,
      String sessionId, String companyId, int msgType, int maxCount) async {
    final db = await appDatabase;
    List<Notice> noticeList = await db.getNoticeDao
        .getLastNoticeDataWithCompanyId(
            userId, sessionId, companyId, msgType, maxCount);
    return noticeList.reversed.toList();
  }

  //获取工作通知classifyType类型数据
  static Future<List<Notice>> getLocalWorkNoticeDataWithClassifyType(
      String userId, String sessionId, int classifyType, int timestamp) async {
    final db = await appDatabase;
    List<Notice> noticeList = await db.getNoticeDao
        .getLocalWorkNoticeDataWithClassifyType(
            userId, sessionId, classifyType, timestamp);
    return noticeList.reversed.toList();
  }

  //更新审批状态
  static Future upDateNoticeTempStatus(
      String uid, String cmdId, int tempStatus) async {
    final db = await appDatabase;
    return db.getNoticeDao.upDateNoticeTempStatus(uid, cmdId, tempStatus);
  }

  //更新审批数据
  static Future upDateNoticeTempData(
      String uid, String cmdId, String data) async {
    final db = await appDatabase;
    return db.getNoticeDao.upDateNoticeData(uid, cmdId, data);
  }

  //删除msgId
  static Future deleteNoticeMsgWithMsgId(
      String msgId) async {
    final db = await appDatabase;
    return db.getNoticeDao.deleteNoticeMsgWithMsgId(msgId);
  }

// ------------------------------------ notice end ------------------------------------

// ------------------------------------ notice start ------------------------------------

  
  static Future insertAllGroupInTransaction(List<Group> groups) async {
    final db = await appDatabase;
    return await db.getGroupDao.insertAllGroupInTransaction(groups);
  }
  
  static Future insertGroup(Group group) async {
    final db = await appDatabase;
    return await db.getGroupDao.insertGroup(group);
  }

  static Future insertGroupList(List<Group> groupList) async {
    final db = await appDatabase;
    return await db.getGroupDao.insertList(groupList);
  }

  static Future<Group?> queryGroupByGroupId(String groupId) async {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    var list = await db.getGroupDao.queryGroupByGroupId(ownId, groupId);
    if (list.isEmpty) return null;
    return list.first;
  }

  static Stream<List<Group>> listenGroupList(String groupId) async* {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    yield* db.getGroupDao.listenGroupList(ownId, groupId).map((event) => event);
  }

// ------------------------------------ notice end ------------------------------------

// ------------------------------------ session_read start ------------------------------------
  static Future insertSessionRead(SessionRead sessionRead) async {
    final db = await appDatabase;
    return await db.getSessionReadDao.insertSessionRead(sessionRead);
  }

  static Future<SessionRead?> querySessionReadBySessionId(String sessionId) async {
    final db = await appDatabase;
    var ownId = await UserHelper.getUid();
    var list = await db.getSessionReadDao.querySessionReadBySessionId(ownId, sessionId);
    if (list.isEmpty) return null;
    return list.first;
  }

  static Stream<List<SessionRead>> listenSessionReadList(
      String userId, String sessionId) async* {
    final db = await appDatabase;
    yield* db.getSessionReadDao
        .listenSessionReadList(userId, sessionId)
        .map((event) => event);
  }
  // ------------------------------------ session_read end ------------------------------------


  // 添加草稿相关方法
  static Future<DraftMessage?> getDraft(String sessionId, String uid) async {
    final db = await appDatabase;
    return await db.draftMessageDao.getDraftBySessionId(sessionId, uid);
  }

  static Future saveDraft(String sessionId, String uid, String? text, String? quoteMessageId) async {
    final db = await appDatabase;
    final now = DateTime.now().millisecondsSinceEpoch;

    // 检查是否已存在草稿
    final existingDraft = await db.draftMessageDao.getDraftBySessionId(sessionId, uid);

    if (existingDraft != null) {
      // 更新现有草稿
      existingDraft.text = text;
      existingDraft.quoteMessageId = quoteMessageId;
      existingDraft.updateTime = now;
      return await db.draftMessageDao.updateDraft(existingDraft);
    } else {
      // 创建新草稿
      final draft = DraftMessage(
        sessionId,
        uid,
        text: text,
        quoteMessageId: quoteMessageId,
        createTime: now,
        updateTime: now,
      );
      return await db.draftMessageDao.insertDraft(draft);
    }
  }

  static Future deleteDraft(String sessionId, String uid) async {
    final db = await appDatabase;
    return await db.draftMessageDao.deleteDraftBySessionId(sessionId, uid);
  }


}
