import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';

import '../../common/config/config.dart';
import '../../common/widgets/image_loader.dart';
import 'package:badges/badges.dart' as badge;

class ChooseOrgComponentWidget extends StatelessWidget {
  OrgModel? model;
  ChooseOrgComponentWidget(this.model);
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              ImageLoader(
                url: model!.logo,
                width: 40,
                height: 40,
                isCircle: true,
                border: 1,
              ),
              Positioned(
                  top: 0,
                  right: 0,
                  child: _badge(
                      Key('${model!.companyId}-${model!.name}-${model!.haveApprove}}'),
                      model!.haveApprove,
                      Container()))
            ],
          ),
        ),
        SizedBox(
          width: 8,
        ),
        Expanded(
            child: Container(
          height: 56,
          alignment: Alignment.centerLeft,
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: ColorConfig.lineColor,
                width: 1,
                style: BorderStyle.solid, // 设置下边框样式为实线
              ),
            ),
          ),
          child: Text(
            model!.name,
            style: TextStyle(
                fontSize: 15,
                color: model!.chooseState == 1
                    ? ColorConfig.themeCorlor
                    : ColorConfig.mainTextColor),
          ),
        ))
      ],
    );
  }

  _badge(Key key, int count, Widget child,
      {int maxShowInt = 99,
      double top = 0,
      double start = 0,
      Color backColor = ColorConfig.deleteCorlor,
      Color textColor = ColorConfig.whiteColor}) {
    var value = '';
    var shape = badge.BadgeShape.circle;

    if (count <= 0) {
      value = '';
    } else if (count > 99) {
      shape = badge.BadgeShape.square;
      value = '$count';
      if (count > maxShowInt) {
        value = '$maxShowInt+';
      }
    } else {
      value = '$count';
      shape = badge.BadgeShape.circle;
    }
    double fontSize = 10;
    return Padding(
        padding: EdgeInsets.all(0),
        child: badge.Badge(
          // shape: BadgeShape.circle,
          ignorePointer: false,
          badgeAnimation: const badge.BadgeAnimation.scale(
            animationDuration: Duration(seconds: 1),
            colorChangeAnimationDuration: Duration(seconds: 1),
            loopAnimation: false,
            curve: Curves.fastOutSlowIn,
            colorChangeAnimationCurve: Curves.easeInCubic,
          ),
          badgeStyle: badge.BadgeStyle(
            shape: shape,
            badgeColor: backColor,
            padding: EdgeInsets.all(1),
            borderRadius: BorderRadius.circular(12),
            elevation: 0,
          ),
          badgeContent: Container(
            padding: EdgeInsets.only(bottom: Platform.isIOS ? 0.5 : 0),
            key: key,
            constraints: BoxConstraints(minWidth: 22),
            child: Center(
              child: Text(
                value,
                style: TextStyle(fontSize: fontSize, color: textColor),
              ),
            ),
          ),
          showBadge: count > 0,
          position: badge.BadgePosition.topStart(top: -8, start: -10),
          child: child,
        ));
  }
}
