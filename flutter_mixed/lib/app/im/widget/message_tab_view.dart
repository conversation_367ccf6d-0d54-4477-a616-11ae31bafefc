import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/request/datasource/offline_datasource.dart';

import '../../../main.dart';
import '../../common/api/LoginApi.dart';
import '../request/entity/offline_notice_session_reqbody.dart';
import '../request/entity/offline_session_reqbody.dart';
import '../ui/session_list/session_view.dart';

class MessageTabView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _MessageState();

}


class _MessageState extends State<MessageTabView> {
  @override
  Widget build(BuildContext context) {
    return SessionView();
  }

  @override
  void initState() {
    super.initState();


  }

  @override
  void dispose() {
    super.dispose();

  }

}