import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../main.dart';
import '../im_client_manager.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification.dart';

class AppLifeContainer extends StatefulWidget {

  Widget body;

  VoidCallback? resumed; 
  
  AppLifeContainer(this.body ,{this.resumed});

  @override
  State<StatefulWidget> createState() => _AppLifeState();
}

class _AppLifeState extends State<AppLifeContainer> with WidgetsBindingObserver{

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return widget.body;
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
    //进入应用时候不会触发该状态 应用程序处于可见状态，并且可以响应用户的输入事件。它相当于 Android 中Activity的onResume
      case AppLifecycleState.resumed:
        debugPrint("应用进入前台======");
        LocalNotification().hideNotification();
        widget.resumed?.call();
        // test todo
        if(kDebugMode) {
          // ImClientManager.instance.testReConnect();
          ImClientManager.instance.reConnect();
          eventBus.fire(ResumeSender());
        };
        // 在这里进行判断
        break;
    //应用状态处于闲置状态，并且没有用户的输入事件，
    // 注意：这个状态切换到 前后台 会触发，所以流程应该是先冻结窗口，然后停止UI
      case AppLifecycleState.inactive:
        debugPrint("应用处于闲置状态，这种状态的应用应该假设他们可能在任何时候暂停 切换到后台会触发======");
        break;
    //当前页面即将退出
      case AppLifecycleState.detached:
        debugPrint("当前页面即将退出======");
        break;
    // 应用程序处于不可见状态
      case AppLifecycleState.paused:
        debugPrint("应用处于不可见状态 后台======");
        break;

      case AppLifecycleState.hidden:
        debugPrint("此状态不起作用");
        break;
    }
  }
}