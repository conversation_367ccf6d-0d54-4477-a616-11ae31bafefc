import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/click_card/notice_funtion_type.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/convert/notice_temp_type.dart';
import 'package:flutter_mixed/app/im/ext/color_ext.dart';
import 'package:flutter_mixed/app/im/ext/im_time_ext.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/res/assets_res.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

import '../request/entity/notice_detail_data.dart';
import '../request/entity/offline_notice_msg.dart';

/// 【卡片】审批 风格
class ApproveStyleCard extends StatelessWidget {
  final OfflineNoticeItemResp? itemBody;

  final VoidCallback? itemClick; //卡片点击

  final Function? buttonClick; //按钮点击

  const ApproveStyleCard(
      {super.key, this.itemClick, this.buttonClick, this.itemBody});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        5.gap,
        // 时间
        NoticeCommonWidget.getNoticeTimeWidget(itemBody),
        8.gap,
        Container(
          padding: const EdgeInsets.only(bottom: 18, right: 34),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              16.gap,
              NoticeCommonWidget.getHeadImgWidget(itemBody),

              10.gap,
              // 卡片区域
              Expanded(
                  child: InkWell(
                onTap: () => itemClick?.call(),
                child: Container(
                  alignment: Alignment.topLeft,
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 1, color: ColorConfig.lineColor),
                      borderRadius: BorderRadius.circular(5),
                      color: ColorConfig.whiteColor),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      NoticeCommonWidget.approveNameWidget(itemBody),
                      if (itemBody?.noticeMsg?.getDataP()?.tempType !=
                          NoticeTempType.DDNotificationTemplateTypeOne) ...[
                        NoticeCommonWidget.approveTitleWidget(itemBody),
                        NoticeCommonWidget.approveContentWidget(itemBody),
                      ],
                      NoticeCommonWidget.bottomButtons(
                          itemBody, itemClick, buttonClick),
                    ],
                  ),
                ),
              ))
            ],
          ),
        ),
      ],
    );
  }
}

//基础控件
class NoticeCommonWidget {
  //时间控件
  static getNoticeTimeWidget(OfflineNoticeItemResp? itemBody) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '${itemBody?.msgTime?.convertDate()}',
        style: const TextStyle(fontSize: 13, color: ColorConfig.msgTextColor),
      ),
    );
  }

  //头像控件
  static getHeadImgWidget(OfflineNoticeItemResp? itemBody) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: ColorConfig.lineColor),
          borderRadius: BorderRadius.circular(8)),
      child: ImageLoader(
        url: itemBody?.getAvatar(),
        width: 32,
        height: 32,
        radius: 6,
      ),
    );
  }

  //底部按钮控件
  static bottomButtons(OfflineNoticeItemResp? itemBody, VoidCallback? itemClick,
      Function? buttonClick) {
    NoticeData? noticeData = itemBody?.noticeMsg?.getDataP();
    Map infoDic = itemBody?.backShowButton();
    int type = infoDic['type'];
    dynamic buttonInfo = infoDic['info'];
    NoticeDataDetail? detail = itemBody?.noticeMsg?.getDataP()?.getDetail();

    if (noticeData?.tempType == NoticeTempType.DDNotificationTemplateTypeTwo ||
        noticeData?.tempType == NoticeTempType.DDNotificationTemplateTypeFive) {
      type = -1;
    }
    if (type == -1) {
      if (noticeData?.functionType == NoticeFunctonType.DDJumpFunctionTypeDefaultZero) {
        return Container();
      }
      return 22.gap;
    } else {
      if (buttonInfo == null) {
        return 22.gap;
      } else {
        if (buttonInfo!.isEmpty) {
          return 22.gap;
        }
      }
      Map? map0;
      Map? map1;
      for (var i = 0; i < buttonInfo.length; i++) {
        map0 = buttonInfo.first;
        if (buttonInfo.length > 1) {
          map1 = buttonInfo.last;
        }
      }
      double height = 1;
      if (itemBody?.noticeMsg?.getDataP()?.tempType == NoticeTempType.DDNotificationTemplateTypeOne) {
        height = 0;
      }
      return Container(
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: Column(
          children: [
             Divider(
              height: height,
              color: ColorConfig.lineColor,
            ),
            16.gap,
            if (type == 0) ...[
              if (buttonInfo.length == 1) ...[
                approveButtonWidget(detail, map0!, type, itemClick, buttonClick)
              ],
              if (buttonInfo.length > 1) ...[
                Container(
                  height: 38,
                  child: Row(
                    children: [
                      Expanded(
                          flex: 1,
                          child: approveButtonWidget(
                              detail, map1!, type, itemClick, buttonClick)),
                      16.gap,
                      Expanded(
                          flex: 1,
                          child: approveButtonWidget(
                              detail, map0!, type, itemClick, buttonClick))
                    ],
                  ),
                )
              ]
            ],
            if (type > 0) ...[
              approveButtonWidget(detail, map0!, type, itemClick, buttonClick)
            ],
            16.gap
          ],
        ),
      );
    }
  }

  static approveButtonWidget(NoticeDataDetail? detail, Map map, int type,
      VoidCallback? itemClick, Function? buttonClick) {
    String text = map['text'];
    Color color = map['color'];
    dynamic button = map['button'];
    bool isSign = false;

    if (detail != null) {
      if (detail.signatureButton != null && button != null) {
        if (detail.signatureButton!
            .contains((button as NoticeButton).buttonId)) {
          isSign = true;
        }
      }
    }
    return InkWell(
      onTap: () {
        if (button == null) {
          if (itemClick != null) {
            itemClick.call();
          }
        } else {
          if (buttonClick != null) {
            buttonClick(button, isSign);
          }
        }
      },
      child: Container(
        height: 38,
        decoration: BoxDecoration(
            border: Border.all(
                width: 1, color: type == 2 ? ColorConfig.lineColor : color),
            borderRadius: BorderRadius.circular(4)),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(fontSize: 14, color: color),
        ),
      ),
    );
  }

  //名称控件
  static approveNameWidget(OfflineNoticeItemResp? itemBody) {
    String nameBackColor = '';
    NoticeData? noticeData = itemBody?.noticeMsg?.getDataP();
    NoticeDataDetail? noticeDataDetail = noticeData?.getDetail();
    if (noticeDataDetail?.nameBackColor != null) {
      nameBackColor = noticeDataDetail?.nameBackColor ?? '';
    }
    String nameText = noticeData?.noticeName ?? '';
    if (noticeData?.tempType == NoticeTempType.DDNotificationTemplateTypeOne) {
      nameText = itemBody?.noticeMsg?.context ?? '';
    }
    return Column(
      children: [
        Container(
          color: nameBackColor.isEmpty
              ? Colors.transparent
              : Color(int.parse(nameBackColor)),
          padding: const EdgeInsets.only(left: 16, right: 16),
          height: 50,
          alignment: Alignment.centerLeft,
          child: Text(
            nameText,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: ColorConfig.mainTextColor),
          ),
        ),
        Offstage(
          offstage: nameBackColor.isNotEmpty,
          child: Container(
            padding: const EdgeInsets.only(left: 16, right: 16),
            height: 1,
            color: ColorConfig.lineColor,
          ),
        )
      ],
    );
  }

  //标题控件
  static approveTitleWidget(OfflineNoticeItemResp? itemBody) {
    NoticeData? noticeData = itemBody?.noticeMsg?.getDataP();
    return Column(
      children: [
        8.gap,
        Offstage(
          offstage: noticeData?.noticeTitle?.isEmpty == true,
          child: Container(
            padding: const EdgeInsets.only(left: 16, right: 16),
            alignment: Alignment.centerLeft,
            height: 25,
            child: Text(
              noticeData?.noticeTitle ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: ColorConfig.mainTextColor),
            ),
          ),
        ),
        4.gap
      ],
    );
  }

  //内容控件
  static approveContentWidget(OfflineNoticeItemResp? itemBody) {
    NoticeData? noticeData = itemBody?.noticeMsg?.getDataP();
    NoticeDataDetail? noticeDataDetail = noticeData?.getDetail();
    if (noticeData?.tempType == NoticeTempType.DDNotificationTemplateTypeTwo) {
      return Container(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 4),
        child: AspectRatio(
          aspectRatio: 2 / 1,
          child: Container(
            width: double.infinity,
            child: Stack(
              children: [
                ImageLoader(
                  url: noticeDataDetail?.backgroundUrl ?? '',
                  width: double.infinity,
                  height: double.infinity * 0.5,
                  radius: 0,
                ),
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    noticeDataDetail?.promptTitle ?? '',
                    style: const TextStyle(
                        fontSize: 15, color: ColorConfig.whiteColor),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    } else if (noticeData?.tempType ==
            NoticeTempType.DDNotificationTemplateTypeEight ||
        noticeData?.tempType == NoticeTempType.DDNotificationTemplateTypeSeven) {
      var list = noticeData?.getContentWithTempType();
      List contentList = [];
      if (list is List) {
        contentList = list;
      }

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
        children: backSorEWidget(noticeData?.tempType ?? 0, contentList),
      ),
      );
    } else {
      var textStr = noticeData?.getContentWithTempType();
      if (textStr is String) {
        if (StringUtil.isEmpty(textStr)) {
          return Container();
        } else {
          return Container(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10),
            child: noticeDataDetail?.contentHtml == 1
                ? HtmlWidget(textStr)
                : Text(
                    textStr,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                        fontSize: 14,
                        color: ColorConfig.msgTextColor,
                        height: 2),
                  ),
          );
        }
      }
    }
  }

  static backSorEWidget(int tempType, List contentList) {
    if (tempType == NoticeTempType.DDNotificationTemplateTypeSeven) {
      return dealTempTypeSeven(contentList);
    } else if (tempType == NoticeTempType.DDNotificationTemplateTypeEight) {
      return dealTempTypeEight(contentList);
    }else{
      return [Container()];
    }
  }

  //类型8 新版审批一行可双内容样式
  static dealTempTypeEight(List contentList) {
    List<Widget> lists = [];
    if (contentList.isEmpty) {
      lists.add(Container());
    }

    for (var i = 0; i < contentList.length; i++) {
      var list = contentList[i];
      List dataList = [];
      if (list is List) {
        dataList = list;
      }
      if (dataList.length == 1) {
        lists.add(Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(top: 0, bottom: 10),
          child: Text(
            dataList.first,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            style:
                const TextStyle(fontSize: 14, color: ColorConfig.msgTextColor),
          ),
        ));
      } else if (dataList.length > 1) {
        lists.add(Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(top: 0, bottom: 10),
          child: Row(
            children: [
              Expanded(
                  flex: 1,
                  child: Container(
                    child: Text(
                      dataList.first,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: const TextStyle(
                          fontSize: 14, color: ColorConfig.msgTextColor),
                    ),
                  )),
              10.gap,
              Expanded(
                  flex: 1,
                  child: Container(
                    child: Text(
                      dataList.last,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: const TextStyle(
                          fontSize: 14, color: ColorConfig.msgTextColor),
                    ),
                  )),
            ],
          ),
        ));
      }
    }
    return lists;
  }

  //类型7 机器人样式
  static dealTempTypeSeven(contentList) {
    List<Widget> lists = [];
    if (contentList.isEmpty) {
      lists.add(Container());
    }
    for (var i = 0; i < contentList.length; i++) {
      var contentStr = contentList[i];
      logger('====contentStr====$contentStr');
      if (contentStr is String) {
        Map<String, dynamic> map = json.decode(contentStr);
        logger('====map====$map');
        int type = map['type']; ////0左右结构样式 1副标题样式 2纯内容样式
        String leftText = '';
        String rightText = '';
        if (map['leftText'] is String) {
          leftText = map['leftText'];
        }
        if (map['rightText'] is String) {
          rightText = map['rightText'];
        }
        String leftColor = map['leftColor'];
        String rightColor = map['rightColor'];
        if (type == 0) {
          lists.add(Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(top: 0,bottom: 5),
            child: RichText(
                maxLines: null,
                text: TextSpan(
                    text: '$leftText ',
                    style: TextStyle(
                        fontSize: 14, color: leftColor.toColor()),
                    children: [
                      TextSpan(
                          text: rightText,
                          style: TextStyle(
                              fontSize: 14,
                              color: rightColor.toColor()))
                    ])),
          ));
        }
        if (type == 1) {
          return lists.add(Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              leftText,
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: leftColor.toColor()),
            ),
          ));
        }
        if (type == 2) {
          return lists.add(Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              leftText,
              style: TextStyle(
                  fontSize: 14, color: leftColor.toColor(defaultColor: ColorConfig.desTextColor)),
            ),
          ));
        }
      } else {
        break;
      }
    }
    lists.add(11.gap);
    return lists;
  }
}
