import 'dart:convert';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/im/model/idc_model.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/widget/notice_card.dart';
import 'package:flutter_mixed/app/im/widget/some_widget.dart';
import 'package:flutter_mixed/logger/logger.dart';

class IdcCard extends StatelessWidget {
  OfflineNoticeItemResp? itemBody;
  Function? urlCallBack;
  IdcCard({this.itemBody, this.urlCallBack});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        5.gap,
        // 时间
        NoticeCommonWidget.getNoticeTimeWidget(itemBody),
        8.gap,
        Container(
          alignment: Alignment.topLeft,
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 18),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
              border: Border.all(width: 1, color: ColorConfig.lineColor),
              borderRadius: BorderRadius.circular(5),
              color: ColorConfig.whiteColor),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: _buildWidaget(),
          ),
        )
      ],
    );
  }

  _buildWidaget() {
    List<Widget> lists = [];
    NoticeData? noticeData = itemBody?.noticeMsg?.getDataP();
    String content = noticeData?.content ?? '';
    List contentList = [];
    try {
      if (content.startsWith('[')) {
        //json数组
        contentList = json.decode(content);
        for (var i = 0; i < contentList.length; i++) {
          Map<String, dynamic>? map;
          var item = contentList[i];
          if (item is String) {
            map = json.decode(item);
          } else if (item is Map<String, dynamic>) {
            map = item;
          }

          IdcModel model = IdcModel.fromJson(map ?? {});
          if (i == 0) {
            lists.add(InkWell(
              onTap: () {
                if (urlCallBack != null) {
                  urlCallBack!(model.mobileUrl);
                }
              },
              child: _buildTopWidget(model),
            ));
          } else {
            lists.add(InkWell(
              onTap: () {
                if (urlCallBack != null) {
                  urlCallBack!(model.mobileUrl);
                }
              },
              child: _buildCustomWidegt(model,
                  isLast: i == contentList.length - 1),
            ));
          }
        }
      }
    } catch (e) {
      logger('=======error===$e==${itemBody?.msgTime}');
      lists.add(Container(
        child: const Text(
          '数据异常',
          style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
        ),
      ));
      return lists;
    }

    return lists;
  }

  //大图
  _buildTopWidget(IdcModel model) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            image: DecorationImage(
                image: ExtendedNetworkImageProvider(model.coverImageId ?? '',cache: true),
                fit: BoxFit.cover)),
        child: Stack(
          children: [
            Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                height: 68,
                child: Container(
                  padding: const EdgeInsets.all(10),
                  decoration:  BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                      gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0x00000000), Color(0xB3000000)])),
                  alignment: Alignment.bottomLeft,
                  child: Text(
                    model.customTitle ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                        fontSize: 16, color: ColorConfig.whiteColor),
                  ),
                ))
          ],
        ),
      ),
    );
  }

  //平铺
  _buildCustomWidegt(IdcModel model, {bool isLast = false}) {
    return Column(
      children: [
        Container(
          height: 91,
          padding: const EdgeInsets.only(top: 16),
          child: Row(
            children: [
              Expanded(
                  child: Column(
                children: [
                  Expanded(
                    child: Container(
                      alignment: Alignment.topLeft,
                      child: Text(
                        model.customTitle ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            fontSize: 16, color: ColorConfig.mainTextColor),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    height: 18,
                    child: Text(
                      model.customAuthor ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                          fontSize: 12, color: ColorConfig.desTextColor),
                    ),
                  )
                ],
              )),
              10.gap,
              Container(
                width: 100,
                height: 75,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    image: DecorationImage(
                        image: ExtendedNetworkImageProvider(
                            model.coverImageId ?? '',cache: true),
                        fit: BoxFit.cover)),
              )
            ],
          ),
        ),
        Offstage(
          offstage: isLast,
          child: Container(
            padding: const EdgeInsets.only(top: 16),
            width: double.infinity,
            height: 17,
            child: Container(
              width: double.infinity,
              height: 1,
              color: ColorConfig.lineColor,
            ),
          ),
        )
      ],
    );
  }
}
