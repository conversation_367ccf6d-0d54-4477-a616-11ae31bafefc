

import 'package:flutter/material.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

class ProgressBar extends StatelessWidget {

  final double progress;
  final double? radius;

  const ProgressBar(this.progress ,{super.key,this.radius});

  @override
  Widget build(BuildContext context) {
    return CircularPercentIndicator(
      radius: radius ?? 30.0,
      lineWidth: 4.0,
      percent: progress.clamp(0, 1),
      progressColor: const Color(0xFF5777FF),
    );
  }


}