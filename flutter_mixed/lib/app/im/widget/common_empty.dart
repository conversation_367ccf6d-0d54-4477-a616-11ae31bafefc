import 'package:flutter/material.dart';
import 'package:flutter_mixed/res/assets_res.dart';


/// 空页面
class Empty extends StatelessWidget {
  final String? msg;

  final Function? reload;

  Empty({Key? key, this.msg , this.reload}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xffF2F3F5),
      child:  Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 200,
            child: Image.asset(AssetsRes.IC_EMPTY_MSG),
          ),

          Text(
            msg ?? '没有数据',
            style: TextStyle(fontSize: 13, color: Color(0x66000000)),
          ),
          SizedBox(
            height: 18,
          ),

          reload == null ? Container() :
          MaterialButton(
              padding: const EdgeInsets.only(top: 10 , bottom: 10 , left: 48 , right: 48),
              color: const Color(0xff29A0F2),
              shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8))
              ),
              child: const Text(
                '重新加载',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
              onPressed: () => reload!()),
        ],
      ),
    );
  }
}
