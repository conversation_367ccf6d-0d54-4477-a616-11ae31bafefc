import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:get/get.dart';

/// IM 路由工具
class RouteHelper {
  static routeTotag(dynamic page, String routeName,
      {dynamic arguments, Bindings? binding}) async {
    return await Get.to(page,
        routeName: routeName, arguments: arguments, binding: binding,preventDuplicates: false);
  }

  static route(String routePath, {dynamic arguments}) async {
    return await Get.toNamed(routePath,
        arguments: arguments, preventDuplicates: false);
  }

  static routeChat({dynamic arguments}) async {
    bool chatIsOpen = Get.isRegistered<ChatController>();
    if (chatIsOpen) {
      await Get.delete<ChatController>();
      Get.until((route) => route.settings.name != Routes.IM_CHAGE_PAGE);
    }
    return await Get.toNamed(Routes.IM_CHAGE_PAGE, arguments: arguments);
  }

  static routePath(String page,
      {dynamic arguments, preventDuplicates = true}) async {
    return await Get.toNamed(page,
        arguments: arguments, preventDuplicates: preventDuplicates);
  }

  static offRoutePath(String page,
      {dynamic arguments, preventDuplicates = true}) async {
    return await Get.offNamed(page,
        arguments: arguments, preventDuplicates: preventDuplicates);
  }
}
