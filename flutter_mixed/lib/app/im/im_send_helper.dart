import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/int64_ext.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/im_send_msg.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/simple_group_memebers_page/at_somebody.dart';
import 'package:flutter_mixed/app/im/utils/file_util.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:uuid/uuid.dart';

import 'constant/ImMsgConstant.dart';
import 'convert/in_time_im_convert_to_session.dart';
import 'db/entity/message.dart';

// 发送消息 ，基于session 直接封装 发送 Socket byte
extension SendMsg on Session {
  // 发送文本消息
  Future sendTxtBySocket(String uuid, String? txt, List<String?> atSbIds,
      {int? sessionType, String? targetSessionId, String? targetName}) async {
    if (txt == null || txt == '') {
      toast('不能发送空消息');
      return;
    }
    var isGroup = isGroupChat();

    if (sessionType != null) {
      isGroup = sessionType == 2;
    }

    ImMsg msg = await _sendText(uuid, txt, isGroup, atSbIds,
        targetSessionId: targetSessionId, targetName: targetName);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 发送引用类型消息
  Future sendQuoteBySocket(
      String uuid, String? txt, Message quote, List<String?> atSbIds,
      {int? sessionType, String? targetSessionId, String? targetName}) async {
    if (txt == null || txt == '') {
      toast('不能发送空消息');
      return;
    }
    var realTxt = (txt.characters.length > 50) ? txt.getRange(0, 50) : txt;
    var isGroup = isGroupChat();
    ImMsg msg = await _sendQuote(uuid, realTxt, quote, isGroup, atSbIds,
        targetName: targetName);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 发送 [图片消息]
  Future sendImageBySocket(String uuid, Message message,
      {int? sessionType, String? targetSessionId, String? targetName}) async {
    var isGroup = isGroupChat();
    if (sessionType != null) {
      isGroup = sessionType == 2;
    }
    ImMsg msg = await _sendImage(uuid, isGroup, message,
        targetSessionId: targetSessionId, targetName: targetName);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 发送 [视频消息]
  Future sendVideoBySocket(String uuid, Message message,
      {int? sessionType, String? targetSessionId, String? targetName}) async {
    var isGroup = isGroupChat();
    if (sessionType != null) {
      isGroup = sessionType == 2;
    }
    ImMsg msg = await _sendVideo(uuid, isGroup, message,
        targetSessionId: targetSessionId, targetName: targetName);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 发送位置
  Future sendLocationBySocket(String uuid, Message message,
      {int? sessionType, String? targetSessionId, String? targetName}) async {
    var isGroup = isGroupChat();
    if (sessionType != null) {
      isGroup = sessionType == 2;
    }
    ImMsg msg = await _sendLocation(uuid, isGroup, message,
        targetSessionId: targetSessionId, targetName: targetName);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 发送 [扩展消息] -- 机器人扩展消息
  Future sendExtBySocket(String uuid, Message message,
      {int? sessionType, String? targetSessionId, String? targetName , String? targetLogo}) async {
    var isGroup = isGroupChat();
    if (sessionType != null) {
      isGroup = sessionType == 2;
    }
    ImMsg msg = await _sendBotExt(uuid, isGroup, message,
        targetSessionId: targetSessionId, targetName: targetName , targetLogo: targetLogo);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 发送 [文件消息]
  Future<bool> sendFileBySocket(String uuid, Message message,
      {int? sessionType, String? targetSessionId, String? targetName}) async {
    try {
      var isGroup = isGroupChat();
      if (sessionType != null) {
        isGroup = sessionType == 2;
      }
      ImMsg msg = await _sendFile(uuid, isGroup, message,
          targetSessionId: targetSessionId, targetName: targetName);
      ImClientManager.instance.sendBuffer(msg);
      return true;
    }catch(e){
      return false;
    }

  }

  // 发送音视频Call 只有单聊有
  Future sendAudioAndVideoSocket(Message message) async {
    ImMsg? msg = await _sendVoiceAndVideoCall(message);
    logger('sendAudioAndVideoSocket-------->>>> ${msg}');

    if (msg == null) return;
    ImClientManager.instance.sendBuffer(msg);
  }
  // 发送 [语音消息]
  Future sendAudioBySocket(String uuid ,Message message) async {
    var isGroup = isGroupChat();
    ImMsg msg = await _sendAudio(uuid ,isGroup , message);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 包含 @sb @全体人员 消息类型
  Future<ImMsg> _sendText(
      String uuid, String txt, bool isGroup, List<String?> atSbIds,
      {String? targetSessionId, String? targetName}) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var type = ConstantImMsgType.SSChatMessageTypeText;
    if (isGroup) {
      type = atSbExtStringMsgType(atSbIds);
    }
    logger('发送文本的类型： $type');

    var realTxt = (txt.characters.length > 50) ? txt.getRange(0, 50) : txt;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = DateTime.now().millisecondsSinceEpoch.toInt64()
      ..type = type
      ..msgContent = realTxt
      ..recordIgnore = false;

    if (isGroup) {
      var groupId = targetSessionId ?? (sessionId);
      var groupName = targetName ?? name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';
      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..msg = txt;

      var ext = atSbExtString(atSbIds, txt);
      if (ext != null) {
        groupMsgRequest.ext = ext;
      }

      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId ?? sessionId
        ..msg = txt;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }
    return sendMsg;
  }

  // 发送 [撤回消息]
  Future sendWithDrawBySocket(String uuid, Message message) async {
    var isGroup = isGroupChat();
    ImMsg msg = await _sendWithDraw(uuid, isGroup, message);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 发送 [已读状态]
  Future sendReadedSocket() async {
    var uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    ImMsg msg = await _sendReaded(uuid);
    ImClientManager.instance.sendBuffer(msg);
  }

  // 获取已读数据 不发送
  Future<ImMsg> getReadedSocket() async {
    var uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    ImMsg msg = await _sendReaded(uuid);
    return msg;
  }
  // 包含 @sb @全体人员 消息类型
  Future<ImMsg> _sendQuote(String uuid, String text, Message quoteMessage,
      bool isGroup, List<String?> atSbIds,
      {String? targetName}) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var type = ConstantImMsgType.SSChatMessageTypeQuote;
    if (isGroup && atSbIds.isNotEmpty) {
      logger('atSbIds ===> $atSbIds');
      type = atSbExtStringMsgType(atSbIds);
    }

    quoteMessage
      ..quoteText = text
      ..text = quoteMessage.alias();

    var data = {'quoteInfo': quoteMessage};

    // 引用的实体扩展
    var quoteString = jsonEncode(type == ConstantImMsgType.SSChatMessageTypeQuote ? quoteMessage : data);
    logger('发送引用类型socket， quoteString = ${quoteString}');

    var extMsg = ExtMsg()..ext1 = quoteString;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = DateTime.now().millisecondsSinceEpoch.toInt64()
      ..type = type
      ..msgContent = text ?? ''
      ..recordIgnore = false;

    if (isGroup) {
      var groupId = sessionId ?? '';
      var groupName = targetName ?? name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';
      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..ext = extMsg;

      if (atSbIds.isNotEmpty) {
        var ext = atSbExtString(atSbIds, text ?? '', quoteString: quoteString);
        if (ext != null) {
          groupMsgRequest.ext = ext;
        }
      }
      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var targetSessionId = this.sessionId ?? '';
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId
        ..ext = extMsg;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }
    logger('发送引用类型socket的 buffer:type = ${type}, ${sendMsg.toString()}');
    return sendMsg;
  }

  Future<ImMsg> _sendImage(String uuid, bool isGroup, Message message,
      {String? targetSessionId, String? targetName}) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var image = ImgMsg()
      ..imageId = message.fileId ?? ''
      ..width = message.imgWidth ?? 0
      ..height = message.imgHeight ?? 0;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeImage
      ..msgContent = ImPrefixMsg.imageCardString
      ..recordIgnore = false;

    if (isGroup) {
      var groupId = targetSessionId ?? sessionId ?? '';
      var groupName = targetName ?? name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';

      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..image = image;
      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId ?? sessionId
        ..image = image;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }

    return sendMsg;
  }

  Future<ImMsg> _sendLocation(String uuid, bool isGroup, Message message,
      {String? targetSessionId, String? targetName}) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var locationMsg = Location()
      ..title = message.addressTitle ?? ''
      ..address = message.addressDetail ?? ''
      ..latitude = message.latitude ?? 0
      ..longitude = message.longitude ?? 0
      ..uri = message.addressImgUrl ?? '';

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeMap
      ..msgContent = ImPrefixMsg.locationCardString
      ..recordIgnore = false;

    if (isGroup) {
      var groupId = targetSessionId ?? sessionId ?? '';
      var groupName = targetName ?? name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';

      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..location = locationMsg;
      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId ?? sessionId
        ..location = locationMsg;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }

    return sendMsg;
  }

  Future<ImMsg> _sendVideo(String uuid, bool isGroup, Message message,
      {String? targetSessionId, String? targetName}) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var cover = ImgMsg()
      ..imageId = message.videoImageId ?? ''
      ..width = message.imgWidth ?? 0
      ..height = message.imgHeight ?? 0;

    var videoMsg = VideoMsg()
      ..cover = cover
      ..fileSize = (message.fileSize ?? 0).toInt64()
      ..fileId = message.fileId ?? ''
      ..duration = message.voiceTime ?? 0;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeVideo
      ..msgContent = ImPrefixMsg.videoCardString
      ..recordIgnore = false;

    if (isGroup) {
      var groupId = targetSessionId ?? sessionId;
      var groupName = targetName ?? name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';

      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..video = videoMsg;
      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId ?? sessionId
        ..video = videoMsg;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }

    return sendMsg;
  }

  Future<ImMsg> _sendFile(String uuid, bool isGroup, Message message,
      {String? targetSessionId, String? targetName}) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var fileBean = FileMsg()
      ..fileSize = (message.fileSize ?? 0).toInt64()
      ..fileId = message.fileId ?? ''
      ..name = message.fileName ?? '';
    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeFile
      ..msgContent = ImPrefixMsg.fileCardString
      ..recordIgnore = false;

    if (isGroup) {
      var groupId = targetSessionId ?? sessionId ?? '';
      var groupName = targetName ?? name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';

      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..file = fileBean;
      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId ?? sessionId
        ..file = fileBean;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }

    return sendMsg;
  }

  Future<ImMsg> _sendBotExt(String uuid, bool isGroup, Message message,
      {String? targetSessionId, String? targetName , String? targetLogo}) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var extMsg = ExtMsg()..ext1 = message.extendOne ?? '';

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = message.msgType ?? -1
      ..msgContent = message.text ?? ''
      ..recordIgnore = false;

    if (isGroup) {
      var groupId = targetSessionId ?? sessionId ?? '';
      var groupName = targetName ?? name ?? '';
      var groupLogo = targetLogo ?? (getEffectiveHeaderUrl() ?? '');

      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..ext = extMsg;

      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId ?? sessionId
        ..ext = extMsg;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }

    return sendMsg;
  }

  Future<ImMsg> _sendWithDraw(
      String uuid, bool isGroup, Message message) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();
    var targetSessionId = this.sessionId ?? '';

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeUndo
      ..msgContent = "${senderInfo.nickname}撤回了一条消息"
      ..recordIgnore = true;

    var withDrawBean = Withdraw()..msgId = message.msgId;

    if (isGroup) {
      var groupId = sessionId ?? '';
      var groupName = name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';

      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..withdraw = withDrawBean;
      sendMsg.groupMsgRequest = groupMsgRequest;
    } else {
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId
        ..withdraw = withDrawBean;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }

    return sendMsg;
  }

  Future<ImMsg> _sendReaded(String uuid) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();
    var targetSessionId = sessionId;

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeReadOthers
      ..recordIgnore = true;

    var c2CMsgRequest = C2CMsgRequest()
      ..receiver = targetSessionId
      ..read = ReadMsg(msgId: []);

    sendMsg.c2cMsgRequest = c2CMsgRequest;

    return sendMsg;
  }
  Future<ImMsg> _sendAudio(String uuid ,bool isGroup , Message message) async {
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();
    var targetSessionId = this.sessionId ?? '';

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var fileBean = Voice()
      ..voiceId = message.fileId ?? ''
      ..duration = message.voiceTime ?? 0
    ;

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = ConstantImMsgType.SSChatMessageTypeVoice
      ..msgContent = ImPrefixMsg.voiceCardString
      ..recordIgnore = false;

    if(isGroup){
      var groupId = sessionId ?? '';
      var groupName = name ?? '';
      var groupLogo = getEffectiveHeaderUrl() ?? '';

      var groupMsgRequest = GroupMsgRequest()
        ..groupId = groupId
        ..groupName = groupName
        ..groupLogo = groupLogo
        ..voice = fileBean
      ;
      sendMsg.groupMsgRequest = groupMsgRequest;
    }else{
      var c2CMsgRequest = C2CMsgRequest()
        ..receiver = targetSessionId
        ..voice = fileBean
      ;
      sendMsg.c2cMsgRequest = c2CMsgRequest;
    }

    return sendMsg;
  }

  Future<ImMsg?> _sendVoiceAndVideoCall(Message message) async {
    if (message.msgType == null ||
        message.callType == null ||
        message.meetingId == null) {
      return null;
    }
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();
    var targetSessionId = sessionId;

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;
    String cmdId = message.cmdId ?? getUUid();
    var sendMsg = ImMsg()
      ..cmdId = cmdId
      ..senderInfo = senderInfo
      ..msgTime = (DateTime.now().millisecondsSinceEpoch).toInt64()
      ..type = message.msgType!
      ..msgContent = message.text.getRange(0, 50);
    ;

    var call = AudioAndVideoCall()
      ..durationType = AudioAndVideoCall_DurationType.values[message.callType!]
      ..metingId = message.meetingId!
      ..content = message.callContent ?? '';

    var c2CMsgRequest = C2CMsgRequest()
      ..receiver = targetSessionId
      ..call = call;

    sendMsg.c2cMsgRequest = c2CMsgRequest;
    if (message.msgType ==
        ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused) {
      sendMsg.recordIgnore = true;
    }
    return sendMsg;
  }
}

// 封装为Message ， 发送消息的时候缓存到本地
extension SendCacheMsg on Session {
  // 【文本】发送消息缓存到本地db (单群聊)
  Future<Message> cacheLocalMessage(String? txt,
      {String? targetSessionId, int? targetSessionType}) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = targetSessionType ?? sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2 //
      ..msgType = ConstantImMsgType.SSChatMessageTypeText // 文本
      ..text = txt;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = appChatId
      ..sessionId = targetSessionId ?? sessionId;

    DbHelper.insertMsg(message);
    return message;
  }

  // 【引用】发送消息缓存到本地db (单群聊)
  Future<Message> cacheLocalQuoteMessage(
      String? txt, Message quote, List<String?> atSbIds) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var msgType = ConstantImMsgType.SSChatMessageTypeQuote;

    if (atSbIds.isNotEmpty) {
      msgType = atSbExtStringMsgType(atSbIds);
    }

    var extendOne = jsonEncode(quote);

    var extendThere = '';

    if (msgType != ConstantImMsgType.SSChatMessageTypeQuote) {
      extendThere = jsonEncode({'quoteInfo': quote});
    }

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2 //
      ..msgType = msgType //
      ..extendOne = extendOne
      ..text = txt;
    if(msgType != ConstantImMsgType.SSChatMessageTypeQuote) message.extendThree = extendThere;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = appChatId
      ..sessionId = sessionId;

    DbHelper.insertMsg(message);
    return message;
  }

  // 发送【图片】
  Future<Message> cacheLocalImageMessage(String imagePath,
      {String? targetSessionId, int? targetSessionType}) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = targetSessionType ?? sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2 // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeImage
      // 因为是预缓存 msg ，以下都为空 --------
      ..fileId = ""
      ..fileSize = 0
      ..imgUrl = ''
      ..videoImageId = ''
      ..localUrl = imagePath
      ..imgWidth = 50
      ..imgHeight = 100
      // 因为是预缓存 msg ，以上都为空 --------
      ..text = ImPrefixMsg.imageCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = appChatId
      ..sessionId = targetSessionId ?? sessionId;
    logger("cacheLocalImageMessage message对象：$message");
    await DbHelper.insertMsg(message);
    return message;
  }

  // 发送【视频】
  Future<Message> cacheLocalVideoMessage(String videoPath) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2 // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeVideo
      // 因为是预缓存video msg ，以下都为空 --------
      ..fileId = ""
      ..fileSize = 0
      ..imgUrl = ''
      ..videoImageId = ''
      ..localUrl = videoPath
      ..videoImagePath = ''
      ..imgWidth = 50
      ..imgHeight = 100
      ..voiceTime = 0
      // 因为是预缓存video msg ，以上都为空 --------
      ..text = ImPrefixMsg.videoCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = appChatId
      ..sessionId = sessionId;

    return message;
  }

  // 缓存本地【文件】类型的 Message
  Future<Message> cacheLocalFileMessage(String filePath) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var fileInfo = await FileUtil.getFileInfo(filePath);

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2 // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeFile
      ..fileId = ""
      ..fileSize = fileInfo.fileSize
      ..fileName = fileInfo.fileName
      ..localUrl = filePath
      ..voiceTime = 0
      ..text = ImPrefixMsg.fileCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = appChatId
      ..sessionId = sessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  // 缓存本地【撤回】类型的 Message
  Future cacheLocalWithDrawMessage(String msgId, String cmdId) async {
    var ownId = await UserHelper.getUid();
    await DbHelper.upDateMessageMsgType(
        ownId, msgId, ConstantImMsgType.SSChatMessageTypeUndo);
    // var updatedMessage = await DbHelper.queryMsgByCmdId(cmdId);
    var updatedMessage = await DbHelper.getMessageByUidAndMsgId(ownId ,msgId);
    return updatedMessage.first;
  }

  //缓存本地音视频call
  Future<Message> cacheAudioAndVideoCallMsg(Message message) async {
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();
    var cmdId = getUUid();
    message
      ..uid = ownId
      ..cmdId = cmdId
      ..sessionType = ConstantImMsgType.SSChatConversationTypeChat
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = ownId;
    if (message.msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused) {
      return message;
    }
    await DbHelper.insertMsg(message);
    return message;
  }

  // 缓存本地【地图】类型的 Message
  Future<Message> cacheLocationMessage(TempLocationData? location,
      {String? targetSessionId, int? targetSessionType}) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = targetSessionType ?? sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2 //
      ..msgType = ConstantImMsgType.SSChatMessageTypeMap // 位置
      ..text = ImPrefixMsg.locationCardString
      ..longitude = location?.longitude ?? 0
      ..latitude = location?.latitude ?? 0
      ..addressTitle = location?.title ?? ''
      ..addressDetail = location?.address ?? ''
      ..addressImgUrl = location?.addressImgUrl ?? '';

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = appChatId
      ..sessionId = targetSessionId ?? sessionId;

    DbHelper.insertMsg(message);
    return message;
  }
  // 缓存本地【文件】类型的 Message
  Future<Message> cacheLocalAudioMessage(String filePath , Duration? duration) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();
    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = ConstantImMsgType.SSChatMessageTypeVoice
      ..voiceUrl = ''
      ..localUrl = filePath
      ..voiceTime = duration?.inSeconds ?? 0
      ..text = ImPrefixMsg.voiceCardString;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = appChatId
      ..sessionId = sessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

  Future<Message> cacheExtMessage(String targetSessionId , int sessionType , Map<String,dynamic> extMap ,int messageType) async {
    final uuid = const Uuid().v1().replaceAll(RegExp('-'), '');
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var extendOne = jsonEncode(extMap);

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = sessionType
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2  // 发送中
      ..msgType = messageType
      ..extendOne = extendOne
      ..text = extMap['text'];

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = targetSessionId
      ..sessionId = targetSessionId;

    await DbHelper.insertMsg(message);
    return message;
  }

}

extension MapSendSocket on Map {
  Future createSinglePageParam() async {
    var dataDic = this;
    var userId = dataDic['userId'];
    var name = dataDic['name'];
    var headimg = dataDic['headimg'];
    var sessionId = dataDic['imId'];
    var ownId = await UserHelper.getUid();
    var session = await DbHelper.getSessionByOwnerId2SessionId(ownId, sessionId);
    if (session == null) {
      session = await createNewSession(
          sessionId, headimg, name, 1, 1, '', '', 0, 0, 0, false, userId, 0);

      await DbHelper.insertSession(session);
    }else {
      // 已有session的情况下，需要恢复显示
      await session.reShowSession();
    }
    return session;
  }

  // 创建进入群聊的参数
  Future createGroupSessionAndEnter({bool? enter = true}) async {
    var dataDic = this;
    var group = dataDic['group'];
    var groupId = group['groupId'];
    var name = group['name'];
    var createUserId = group['createUserId'];
    var logo = group['logo'];
    var orgId = group['orgId'];
    var ownId = await UserHelper.getUid();

    var session = await DbHelper.getSessionByOwnerId2SessionId(ownId, groupId);
    if (session == null) {
      session = await createNewSession(
          groupId, logo, name, 2, 1, '', '', 0, 0, 0, false, groupId, 0);
      await DbHelper.insertSession(session);
    } else {
      await session.reShowSession();
    }
    if(enter == true){
      await RouteHelper.routeTotag(ChatPage(tag: session.sessionId),Routes.IM_CHAGE_PAGE,arguments: session,binding: ChatBinding(tag: session.sessionId));;
    }
    return session;
  }

  Future sendMsgAfterFriend() async {
    var sessionId = this['imId'] ?? '';
    var userId = this['userId'] ?? '';
    var name = this['name'] ?? '';
    var headimg = this['headimg'] ?? '';
    var text = "我们已经是好友了,快来和我一起聊天吧!";

    var ownId = await UserHelper.getUid();
    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    var senderInfo = UserInfo()
      ..userId = ownId
      ..nickname = ownName
      ..avatar = ownAvatar;

    var type = ConstantImMsgType.SSChatMessageTypeText;

    var uuid = StringUtil.getUUID();

    var sendMsg = ImMsg()
      ..cmdId = uuid
      ..senderInfo = senderInfo
      ..msgTime = DateTime.now().millisecondsSinceEpoch.toInt64()
      ..type = type
      ..msgContent = text
      ..recordIgnore = false;

    var c2CMsgRequest = C2CMsgRequest()
      ..receiver = sessionId
      ..msg = text;
    sendMsg.c2cMsgRequest = c2CMsgRequest;
    ImClientManager.instance.sendBuffer(sendMsg);

    var message = await cacheLocalMessage(text, uuid, sessionId);
    var session = await DbHelper.getSessionByOwnerId2SessionId(ownId, sessionId);
    if(session != null) {
      session.msgContent = message.text;
      await DbHelper.insertSession(session);
    }
    await DbHelper.insertMsg(message);
  }

  // 【文本】发送消息缓存到本地db (单群聊)
  Future<Message> cacheLocalMessage(
    String? txt,
    String uuid,
    String sessionId,
  ) async {
    var sendTime = DateTime.now().millisecondsSinceEpoch;
    var ownId = await UserHelper.getUid();

    var message = Message(sendTime.toString())
      ..uid = ownId
      ..cmdId = uuid
      ..sessionType = 1
      ..sendTime = sendTime
      ..isReaded = 0
      ..msgFrom = 1
      ..isSuccess = 2 //
      ..msgType = ConstantImMsgType.SSChatMessageTypeText // 文本
      ..text = txt;

    var ownName = await UserHelper.getOwnName();
    var ownAvatar = await UserHelper.getOwnAvatar();

    message
      ..sendId = ownId
      ..sendName = ownName
      ..sendHeader = ownAvatar
      ..appChatId = ownId
      ..sessionId = sessionId;

    DbHelper.insertMsg(message);
    return message;
  }

}
