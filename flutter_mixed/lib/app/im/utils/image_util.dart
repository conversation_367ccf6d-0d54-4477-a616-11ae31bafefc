import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:dio/dio.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_native_image/flutter_native_image.dart';
import 'package:get/instance_manager.dart';

import '../../utils/string-util.dart';
import 'date_util.dart';


class ImageUtil {
  static Future<File?> nativeCompressFile(String path) async {

    if(Platform.isAndroid){
      ui.Image image = await loadImageByFile(path);
      var width = image.width;
      var height = image.height;

      // print('nativeCompressFile : $width -- $height');

      return await FlutterNativeImage.compressImage(path, percentage: 100, quality: 90,
        targetWidth: width,
        targetHeight: height,
      );
    }
    return await FlutterNativeImage.compressImage(path, percentage: 100, quality: 90);
  }

  static Future<String> downloadImage(String? url) async {
    if (url == null) return '';
    var savePath = '${Directory.systemTemp.path}/${DateUtil.getCurrentTimeStamp2String()}.jpg';
    Get.loading();
    try {
      await Dio().download(url, savePath, onReceiveProgress: (received, total) {
        if (total != -1) {
          print((received / total * 100).toStringAsFixed(0) + "%");
        }
      });
      Get.dismiss();
      return savePath;
    } catch (e) {
      Get.dismiss();
      return '';
    }
  }

  static Future<String> downloadFile(String? url) async {
    if (url == null) return '';
    String suffix = url.split('.').last;

    var savePath =
        '${Directory.systemTemp.path}/${DateUtil.getCurrentTimeStamp2String()}.${suffix}';
    Get.loading();
    try {
      await Dio().download(url, savePath, onReceiveProgress: (received, total) {
        if (total != -1) {
          print((received / total * 100).toStringAsFixed(0) + "%");
        }
      });
      Get.dismiss();
      return savePath;
    } catch (e) {
      Get.dismiss();
      return '';
    }
  }

  static Future<File> moveFile(File sourceFile, String newPath) async {
    final newFile = await sourceFile.copy(newPath);
    return newFile;
  }

  //拿到图片的字节数组
  static Future<ui.Image> loadImageByFile(String path) async {
    var list = await File(path).readAsBytes();
    return loadImageByUint8List(list);
  }

  //通过[Uint8List]获取图片
  static Future<ui.Image> loadImageByUint8List(Uint8List list) async {
    ui.Codec codec = await ui.instantiateImageCodec(list);
    ui.FrameInfo frame = await codec.getNextFrame();
    return frame.image;
  }


  // issue : 时间过长


  static Future<String> rotateImage(String? filePath , {int rotate = 0}) async{
    if(StringUtil.isEmpty(filePath)) return filePath ?? '';
    List<String> arr = filePath!.split('/');
    var lastPath = arr.last;
    var targetPath =
        '${filePath.replaceAll(lastPath, '')}${DateUtil.getCurrentTimeStamp2String()}.jpg';

    await FlutterImageCompress.compressAndGetFile(filePath ,targetPath, rotate: rotate);
    return targetPath;
  }
}

class WaterMarkFile {
  late File file;
  late String mark;
}
