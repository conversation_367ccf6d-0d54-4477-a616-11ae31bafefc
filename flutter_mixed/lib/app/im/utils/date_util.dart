
import 'package:flutter_mixed/app/im/ext/date_extension.dart';
import 'package:intl/intl.dart';

class DateUtil {

  /// 13位(int类型)时间戳  转  datetime
  static DateTime timeStamp2dateTime(int timeStamp) {
    return DateTime.fromMillisecondsSinceEpoch(timeStamp).toUtc();
  }

  static String getCurrentTimeStamp2String() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// dateTime 转 mm/dd/yyyy字符串 (本地统一格式)
  static String dateTime2String(DateTime? dateTime) {
    if(dateTime == null){
      return '00/00/0000';
    }
    return dateTime.formatMMDDYYYY();
  }

  static DateTime string2Date(String dateStr) {
    return DateFormat('MM/dd/yyyy').parse(dateStr,true);
  }

  // 仅为支持接口
  static String dateTime2ReqString(DateTime? dateTime) {
    if(dateTime == null) return '';
    // var utc = dateTime.toUtc().toString();
    // print('仅为支持接口>> ${utc}');

    return dateTime.millisecondsSinceEpoch.toString();
  }

  //mm/dd/yyyy
  // server 13位时间戳 to local 格式化显示 (02/14/2023)
  static String timeStamp2StringDateMMDDYYY(num timeStamp ,{int? duringDay}) {
    if(duringDay != null){
      var endtimestamp = (timeStamp?? 0).toInt() + (duringDay ??0).toInt() * 1000 * 60 * 60 * 24;
      // 计算结束时间
      return timeStamp2dateTime(endtimestamp.toInt()).formatMMDDYYYY();
    }
    return timeStamp2dateTime(timeStamp.toInt()).formatMMDDYYYY();
  }

  /// 1660003200000.0 (NUM) 转字符串日期 09/02/2022
  static String timeStamp2StringDate(num timeStamp) {
    return dateTime2String(convert2ZeroTimeStamp(timeStamp.toInt()));
  }

  /// 1660003200000.0 (NUM) 转字符串日期 09/02/2022 12:11
  static String timeStamp2MMDDYYYYHHMM(num timeStamp) {
    // 不用转utc
    var dateTime = DateTime.fromMillisecondsSinceEpoch(timeStamp.toInt());
    return dateTime.formatMMDDYYYYHHMM();
  }

  static String timeStamp2YYYYMMDDHHMMSS(num timeStamp) {
    var dateTime = DateTime.fromMillisecondsSinceEpoch(timeStamp.toInt()).toUtc();
    return dateTime.formatYYYYMMDDHHMMSS();
  }

  static String timeStamp2DayStartMMDDYYYYHH(num timeStamp) {
    var date = DateTime.fromMillisecondsSinceEpoch(timeStamp.toInt()).toUtc();
    return dayStartMMDDYYYYHH(date: date);
  }

  static String dayStartMMDDYYYYHH({DateTime? date}) {
    var now = date??DateTime.now();
    var todayStart = DateTime(now.year,now.month,now.day).toUtc();
    return todayStart.formatYYYYMMDDHHMMSS();
  }

  static String timeStamp2MMMYYYY(num timeStamp) {
    var dateTime = DateTime.fromMillisecondsSinceEpoch(timeStamp.toInt());
    return dateTime.formatMMMYYYY();
  }

  // 本地时间 根据开始时间戳和时长计算截止日期（字符串）
  static String getEndStateStringFromDateTimeLocal(int startDateTimeStamp , int duringDay) {
    int endMicroSecond = startDateTimeStamp + (duringDay * oneDayTimeStamp());
    print('开始的时间戳：${startDateTimeStamp}');
    print('结束的时间戳：${endMicroSecond}');
    var rangeEnd = DateTime.fromMicrosecondsSinceEpoch(endMicroSecond * 1000).toUtc();
    return DateUtil.dateTime2String(rangeEnd);
  }

  static int oneDayTimeStamp() {
    return 1000 * 60 * 60 * 24;
  }

  static String currentDate4Req(){
    var now = DateTime.now();
    return dateTime2ReqString(now);
  }

  // 指定的时间戳是否是今天
  static bool isToday(num timeStamp) {
    var d = timeStamp2dateTime(timeStamp.toInt());
    var n = DateTime.now();
    if(d.year == n.year && d.month == n.month && d.day == n.day){
      return true;
    }
    return false;
  }

  // 本周的第一天 零点时间戳
  static int thisWeekStart() {
    DateTime today = DateTime.now();
    DateTime _firstDayOfTheweek =
    today.subtract(Duration(days: today.weekday));
    // print(_firstDayOfTheweek.day);
    return _firstDayOfTheweek.microsecondsSinceEpoch ~/ 1000;
  }

  // 本周最后一天 24点的时间戳
  static int thisWeekEnd(){
    int weekStart= thisWeekStart();
    return weekStart + (8 * 1000 * 60 * 60 * 24);
  }


  static bool getDateTimeFrom(DateTime dateTime) {
    var y = dateTime.year;
    var m = dateTime.month;
    var d = dateTime.day;
    var selectD = DateTime(y, m, d).millisecondsSinceEpoch;

    var nowDate = DateTime.now();
    var nY = nowDate.year;
    var nM = nowDate.month;
    var nD = nowDate.day;
    var nowD = DateTime(nY, nM, nD).millisecondsSinceEpoch;

    // print('${selectD} , ${nowD}');
    return selectD >= nowD;
  }

  // 由于server返回的时间戳带有时区，转换下为0点的时间戳
  // 日历（utc）相关使用
  static DateTime convert2ZeroTimeStamp(num? timeStamp) {
    var t = timeStamp?.toInt() ?? 0;
    var date = DateTime.fromMillisecondsSinceEpoch(t).toUtc();
    var newDate = DateTime(date.year ,date.month ,date.day);
    return newDate;
  }

// 正常utc 转换 本地上面的不是本地时间。。。
  static DateTime convert2ZeroTimeStamp2(num? timeStamp) {
    var t = timeStamp?.toInt() ?? 0;
    var date = DateTime.fromMillisecondsSinceEpoch(t).toUtc();

    date = date.toLocal();
    var newDate = DateTime(date.year ,date.month ,date.day, date.hour , date.minute);
    return newDate;
  }

  static String nowTime() {
    var dateTime = DateTime.now();
    var h = dateTime.hour;
    String hourShow = '';
    String minShow = '';
    if(h<10){
      hourShow = '0$h';
    }else{
      hourShow = '$h';
    }
    var m = dateTime.minute;
    if(m < 10){
      minShow = '0$m';
    }else{
      minShow = '$m';
    }
    // var s = dateTime.second;
    return '$hourShow:$minShow';
  }

  //  server utc ~ local
  static DateTime utc2localDateTime(num time){
    return DateTime.fromMillisecondsSinceEpoch(time.toInt()).toLocal();
  }

  // 服务器时间创建 ，显示给日历 ，日历有问题， 用的utc todo
  static DateTime utc2DateTime4Calendar(num time){
    return DateTime.fromMillisecondsSinceEpoch(time.toInt()).toLocal();
  }

  //时间转换 将秒转换为小时分钟
  static String durationTransform(int millseconds) {
    var d = Duration(milliseconds:millseconds);
    List<String> parts = d.toString().split(':');
    return '${parts[0]}H ${parts[1]}M';
  }

  static String durationTransformHours(int millSeconds) {
    print('durationTransformHours : ${millSeconds}');
    var t= millSeconds / (3600 * 1000) ;
    return t.toStringAsFixed(2);
  }

  // ok
  static String formatDateFromServer(int millseconds) {
    return DateUtil.dateTime2String(DateTime.fromMillisecondsSinceEpoch(millseconds).toLocal());
  }

  static bool isThisYear(DateTime dateTime) {
    var nowDate = DateTime.now();
    var nY = nowDate.year;
    return dateTime.year == nY;
  }


}