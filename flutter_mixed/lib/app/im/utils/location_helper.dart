// import 'dart:io';
//
// import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
// import 'package:flutter_bmflocation/flutter_bmflocation.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart'
//     show BMFMapSDK, BMF_COORD_TYPE;
//
//
// // todo 定位初始化 ：1 需要隐私权限的确认 2 定位权限的申请（注意android端自定义说明窗口，否则审核不通过） 3 ak的初始化
// class LocationHelper {
//
//   BaiduLocationIOSOption _initIOSOptions() {
//     BaiduLocationIOSOption options = BaiduLocationIOSOption(
//         coordType: BMFLocationCoordType.bd09ll,
//         desiredAccuracy: BMFDesiredAccuracy.best,
//         allowsBackgroundLocationUpdates: true,
//         pausesLocationUpdatesAutomatically: false);
//     return options;
//   }
//
//   LocationFlutterPlugin myLocPlugin = LocationFlutterPlugin();
//
//   initLocation() async {
//     BMFMapSDK.setAgreePrivacy(true);
//     myLocPlugin.setAgreePrivacy(true);
//     var hasPermission = await requestPermission();
//
//     // 百度地图sdk初始化鉴权
//     // todo
//     if (Platform.isIOS) {
//       myLocPlugin.authAK('请在此输入您在开放平台上申请的API_KEY');
//       BMFMapSDK.setApiKeyAndCoordType(
//           '请在此输入您在开放平台上申请的API_KEY', BMF_COORD_TYPE.BD09LL);
//     } else if (Platform.isAndroid) {
//       /// 初始化获取Android 系统版本号，如果低于10使用TextureMapView 等于大于10使用Mapview
//       await BMFAndroidVersion.initAndroidVersion();
//       // Android 目前不支持接口设置Apikey,
//       // 请在主工程的Manifest文件里设置，详细配置方法请参考官网(https://lbsyun.baidu.com/)demo
//       BMFMapSDK.setCoordType(BMF_COORD_TYPE.BD09LL);
//     }
//
//   }
//
//
//   // 开始定位
//   void startLocation() {
//     LocationFlutterPlugin myLocPlugin = LocationFlutterPlugin();
//     BMFMapSDK.setAgreePrivacy(true);
//     myLocPlugin.setAgreePrivacy(true);
//
//   }
//
//   void locationAction() async {
//     /// 设置android端和ios端定位参数
//     /// android 端设置定位参数
//     /// ios 端设置定位参数
//
//   }
//
//   // 动态申请定位权限
//    requestPermission() async {
//     // 申请权限
//     bool hasLocationPermission = await requestLocationPermission();
//     if (hasLocationPermission) {
//       // 权限申请通过
//       return true;
//     } else {
//       return false;
//     }
//   }
//
//   /// 申请定位权限
//   /// 授予定位权限返回true， 否则返回false
//   Future<bool> requestLocationPermission() async {
//     //获取当前的权限
//     var status = await Permission.location.status;
//     if (status == PermissionStatus.granted) {
//       //已经授权
//       return true;
//     } else {
//       //未授权则发起一次申请
//       status = await Permission.location.request();
//       if (status == PermissionStatus.granted) {
//         return true;
//       } else {
//         return false;
//       }
//     }
//   }
//
//
// }