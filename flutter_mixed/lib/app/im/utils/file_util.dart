
import 'dart:io';

import 'package:flutter_mixed/res/assets_res.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

import '../../common/channel/channel.dart';

import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

var fileIconMap = {
  'ai': AssetsRes.IC_FILE_TYPE_AI,
  'doc': AssetsRes.IC_FILE_TYPE_DOC,
  'docx': AssetsRes.IC_FILE_TYPE_DOC,
  'dwf': AssetsRes.IC_FILE_TYPE_DWF,
  'dwg': AssetsRes.IC_FILE_TYPE_DWG,
  'xls': AssetsRes.IC_FILE_TYPE_EXCEL,
  'xlsx': AssetsRes.IC_FILE_TYPE_EXCEL,
  'gif': AssetsRes.IC_FILE_TYPE_GIF,
  'html': AssetsRes.IC_FILE_TYPE_HTML,
  'jpg': AssetsRes.IC_FILE_TYPE_JPG,
  'js': AssetsRes.IC_FILE_TYPE_JS,
  'mp3': AssetsRes.IC_FILE_TYPE_MP3,
  'mp4': AssetsRes.IC_FILE_TYPE_MP4,
  'pdf': AssetsRes.IC_FILE_TYPE_PDF,
  'ppt': AssetsRes.IC_FILE_TYPE_PPT,
  'pptx': AssetsRes.IC_FILE_TYPE_PPT,
  'psd': AssetsRes.IC_FILE_TYPE_PSD,
  'rp': AssetsRes.IC_FILE_TYPE_RP,
  'skp': AssetsRes.IC_FILE_TYPE_SKP,
  'swf': AssetsRes.IC_FILE_TYPE_SWF,
  'txt': AssetsRes.IC_FILE_TYPE_TXT,
  'vsd': AssetsRes.IC_FILE_TYPE_VSD,
  'xmind': AssetsRes.IC_FILE_TYPE_XMIND,
  'zip': AssetsRes.IC_FILE_TYPE_ZIP,
};

class FileUtil {

  static Future<bool> isExist(String? toFilePath) async{
    if(toFilePath == null) return false;
    File file = File(toFilePath);
    return await file.exists();
  }

  static String getFileType(String? fileName){
    if(fileName == null) return "";
    if(!fileName.contains('.')){
      return "";
    }
    var key = fileName.split('.').last;
    return key;
  }

  static String getFileName(String? path){
    if(path == null) return "";
    if(!path.contains('/')){
      return "";
    }
    var fileName = path.split('/').last;
    return fileName;
  }

  static String getFileTypeIcon(String? fileName){
    if(fileName == null) return AssetsRes.IC_FILE_TYPE_WU;
    if(!fileName.contains('.')){
      return AssetsRes.IC_FILE_TYPE_WU;
    }
    var key = fileName.split('.').last;
    if(fileIconMap.containsKey(key)){
      return fileIconMap[key] ?? AssetsRes.IC_FILE_TYPE_WU;
    }
    return AssetsRes.IC_FILE_TYPE_WU;
  }

  static String formatBytes(int bytes) {
    if(bytes < 1024) return '$bytes B';
    if(bytes < 1024 * 1024){
      var result =  bytes / 1024;
      return '${result.toStringAsFixed(2)} kb';
    }else {
      double megabytes = bytes / (1024 * 1024);
      return '${megabytes.toStringAsFixed(2)} M';
    }
  }

  static Future<String> captureImageByKey(GlobalKey key) async {
    RenderRepaintBoundary? boundary =
    key.currentContext!.findRenderObject() as RenderRepaintBoundary?;
    double dpr = ui.window.devicePixelRatio; // 获取当前设备的像素比
    var image = await boundary!.toImage(pixelRatio: dpr);
    // 将image转化成byte
    ByteData? byteData = await image.toByteData(format: ImageByteFormat.png);

    var filePath = "";

    Uint8List pngBytes = byteData!.buffer.asUint8List();
    // 获取手机存储（getTemporaryDirectory临时存储路径）
    Directory applicationDir = await getTemporaryDirectory();
    // getApplicationDocumentsDirectory();
    // 判断路径是否存在
    bool isDirExist = await Directory(applicationDir.path).exists();
    if (!isDirExist) Directory(applicationDir.path).create();
    // 直接保存，返回的就是保存后的文件
    File saveFile = await File(
        applicationDir.path + "${DateTime.now().toIso8601String()}.jpg")
        .writeAsBytes(pngBytes);
    filePath = saveFile.path;
    return filePath;
  }


  static openFile(String filePath) {
    OpenFilex.open(filePath);
    // if (Platform.isAndroid) {
    //   OpenFilex.open(filePath);
    // }
    // if (Platform.isIOS) {
    //   Channel().invoke(Channel_openWebView, {
    //     'url': filePath,
    //     'title': '文件预览',
    //     'isWebNavigation': 0,
    //   });
    // }
  }

  static Future<FileInfo> getFileInfo(String path ,{String? fileId}) async {
    var file = File(path);
    var size = await file.length();
    var fileName = await getFileName(path);
    var fileType = await getFileType(fileName);
    return FileInfo(fileId ,size , fileName , path , fileType);
  }

  static clearDirectoryFile() async {
    String docment = (await getApplicationDocumentsDirectory()).path;
    String path = '$docment/approve';
    var directory = Directory(path);
    if (await directory.exists()) {
      await directory.delete(recursive: true);
    }
  }

  /// 写入字符串内容到指定文件。
  /// 如果文件或其父目录不存在，将会自动创建。
  /// [fileName] 文件名，例如 'my_data.txt'。
  /// [content] 要写入的字符串内容。
  /// 返回 true 表示成功，false 表示失败。
  static Future<bool> writeStringToFile(String fileName, String content) async {
    try {
      // 获取文件路径
      final filePath = await _getFilePath(fileName);
      final file = File(filePath);

      // 确保文件所在目录存在
      final directory = Directory(file.parent.path);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // 写入内容
      await file.writeAsString(content ,mode: FileMode.append);
      print('文件写入成功：$filePath');
      return true;
    } catch (e) {
      print('文件写入失败：$e');
      return false;
    }
  }

  static Future<String> _getFilePath(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/$fileName';
  }

}

class FileInfo {
  String? fileId;
  int? fileSize;
  String? fileName;
  String? filePath;
  String? fileType;

  FileInfo(this.fileId, this.fileSize, this.fileName, this.filePath,
      this.fileType);

}