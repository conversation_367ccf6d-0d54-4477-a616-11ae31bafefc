

import '../request/entity/chat_resp_ext.dart';
import '../request/entity/offline_group_chat_resp.dart';

class ImHtmlParser {

  static String getParseColor(int? colorType) {
    if(colorType == 1){
      return "#1D2129";
    }else if(colorType == 2){
      return "#FF9A3C";
    }else if (colorType == 3) {
      return "#4690f5";
    } else {
      return "rgba(34, 34, 34, 0.2)";
    }
  }
  
  static String getParseSymbol(int? symbol , String? content) {
    if(symbol == 1){
      return "[$content]";
    }else if(symbol == 2){
      return "$content  ";
    }else if(symbol == 3){
      return "\"$content\"";
    }
    return content ?? '';
  }
  
  static String dealNoticeContent(List<ReNameReCreaterContentBean> contents) {
    var sb = '';
    contents.forEach((e) {
      sb += "<font color='${getParseColor(e.color)}'>${getParseSymbol(e.symbol,e.content)}</font>";
    });
    return sb;
  }
}

