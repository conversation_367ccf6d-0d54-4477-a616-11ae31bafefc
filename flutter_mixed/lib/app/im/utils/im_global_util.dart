

import '../../common/api/Define.dart';
import '../../utils/storage.dart';
import '../proto_model/Msg.pb.dart';

class ImGlobalUtil {

  static Future<String> currentUserId() async{
    var userInfo = await UserDefault.getData(Define.TOKENKEY);
    if (userInfo == null) return '';
    String ownerId = userInfo['userId'] ?? '';
    return ownerId;
  }

  static Future<LocalUserInfo?> currentUserInfo() async {
    var userInfo = await UserDefault.getData(Define.TOKENKEY);
    if(userInfo == null) return null;
    String avatar = userInfo['avatar'] ?? '';
    String name = userInfo['name'] ?? '';
    String ownerId = userInfo['userId'] ?? '';
    String mobile = userInfo['mobile'] ?? '';
    String imId = userInfo['imId'] ?? '';
    return LocalUserInfo(ownerId , name , avatar , mobile, imId);
  }

}

class LocalUserInfo {
  String? userId;
  String? name;
  String? avatar;
  String? mobile;
  String? imId;
  LocalUserInfo(this.userId, this.name, this.avatar , this.mobile, this.imId);
}