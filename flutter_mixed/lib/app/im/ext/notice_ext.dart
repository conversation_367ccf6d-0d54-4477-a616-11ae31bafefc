import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';

extension NoticeExt on Notice {
  //转换成离线model
  OfflineNoticeItemResp getOfflineNoticeItemResp() {
    Map<String, dynamic> offlineMap = toJson();
    ClientNoticeMsg noticeMsg = ClientNoticeMsg.fromJson(offlineMap);
    offlineMap['noticeMsg'] = noticeMsg.toJson();
    return OfflineNoticeItemResp.fromJson(offlineMap);
  }
}
