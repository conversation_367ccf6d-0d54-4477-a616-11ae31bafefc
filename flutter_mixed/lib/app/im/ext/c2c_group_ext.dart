import 'dart:convert';

import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ext/notice_button_ext.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pb.dart';
import 'package:flutter_mixed/app/im/request/entity/chat_resp_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/im/ui/chat/message/bot_parse.dart';
import 'package:flutter_mixed/app/im/utils/im_html_parser.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../constant/ImMsgConstant.dart';
import '../db/db_helper.dart';
import '../db/entity/message.dart';
import '../ui/chat/message/custom_msg_model.dart';
import '../utils/im_global_util.dart';
import 'im_msg_ext.dart';

mixin SocketRoleParse {
  var sessionType = 1;
  var appChatId;
  var sessionId;
  var msgFrom;
  var uid;

  Future parseRole(
      ImCommonData commonData, String? groupId, String? receiver) async {
    uid = await ImGlobalUtil.currentUserId();
    msgFrom = uid == commonData.senderInfo.userId ? 1 : 2; //
    if (groupId != null) {
      sessionType = 2;
      appChatId = groupId;
      sessionId = groupId;
    } else {
      sessionType = 1;
      appChatId = socket_createAppChatId(commonData, sessionType, '');
      sessionId = await socket_getSessionId(commonData, receiver ?? '');
    }
  }
}

extension ParseC2CgGroupMsgExt on ImCommonData {
  /// socket 消息 转换为文本 Message
  Future<Message> createTxtMessage(String txt,
      {String? groupId, String? receiver}) async {
    await parseRole(this, groupId, receiver);

    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = ConstantImMsgType.SSChatMessageTypeText
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..text = txt
      ..appChatId = appChatId
      ..sessionId = sessionId;
    DbHelper.insertMsg(message);
    return message;
  }

  // socket 消息 转换为图片 Message
  Future<Message> createImgMessage({String? groupId, String? receiver}) async {
    await parseRole(this, groupId, receiver);
    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = ConstantImMsgType.SSChatMessageTypeImage
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..fileId = this.imgMsg?.imageId
      ..imgWidth = this.imgMsg?.width
      ..imgHeight = this.imgMsg?.height
      ..imgUrl = this.imgMsg?.url
      ..extendOne = this.extMsg?.ext1
      ..text = ImPrefixMsg.imageCardString
      ..appChatId = appChatId
      ..sessionId = sessionId;
    DbHelper.insertMsg(message);

    return message;
  }

  // socket 消息 转换为视频 Message
  Future<Message> createVideoMessage(
      {String? groupId, String? receiver}) async {
    await parseRole(this, groupId, receiver);
    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = ConstantImMsgType.SSChatMessageTypeVideo
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..fileId = this.videoMsg?.fileId
      ..fileSize = this.videoMsg?.fileSize?.toInt()
      ..voiceTime = this.videoMsg?.duration
      ..videoImageId = this.videoMsg?.cover.imageId
      ..imgWidth = this.videoMsg?.cover.width
      ..imgHeight = this.videoMsg?.cover.height
      ..miniImgUrl = this.videoMsg?.cover.thumbnailUrl
      ..text = ImPrefixMsg.videoCardString
      ..appChatId = appChatId
      ..sessionId = sessionId;
    DbHelper.insertMsg(message);

    return message;
  }

  // socket 消息 转换为文件 Message
  Future<Message> createFileMessage({String? groupId, String? receiver}) async {
    await parseRole(this, groupId, receiver);
    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = ConstantImMsgType.SSChatMessageTypeFile
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..fileId = this.fileMsg?.fileId
      ..fileSize = this.fileMsg?.fileSize?.toInt()
      ..fileName = this.fileMsg?.name
      ..imgUrl = this.fileMsg?.url
      ..text = ImPrefixMsg.fileCardString
      ..appChatId = appChatId
      ..sessionId = sessionId;
    DbHelper.insertMsg(message);

    return message;
  }

  // socket 消息 转换为语音 Message
  Future<Message> createVoiceMessage(
      {String? groupId, String? receiver}) async {
    await parseRole(this, groupId, receiver);
    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = ConstantImMsgType.SSChatMessageTypeVoice
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..fileId = this.voiceMsg?.voiceId
      ..voiceUrl = this.voiceMsg?.url
      ..voiceTime = this.voiceMsg?.duration
      ..text = ImPrefixMsg.voiceCardString
      ..appChatId = appChatId
      ..sessionId = sessionId;
    DbHelper.insertMsg(message);

    return message;
  }

  // socket 消息 转换为音视频 Message
  Future<Message> createSingleCallMessage(
      {String? groupId, String? receiver, bool isNeedInsert = true}) async {
    await parseRole(this, groupId, receiver);
    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = type
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..appChatId = appChatId
      ..sessionId = sessionId
      ..callContent = call?.content
      ..meetingId = call?.metingId;

    String typeStr = '语音';
    message.callType = 1;
    if (call?.durationType == AudioAndVideoCall_DurationType.VIDEO) {
      typeStr = '视频';
      message.callType = 2;
    }
    switch (type) {
      case ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo:
        var callStr = '发起了$typeStr通话';
        message.text = '${call?.content}$callStr';
        message.voiceUrl = callStr;
        break;
      case ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd:
        message.text = '$typeStr${call?.content}';
        break;
      case ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure:
        message.text = '$typeStr通话-${call?.content}';
        break;
      default:
    }
    if (isNeedInsert) {
      DbHelper.insertMsg(message);
    }
    return message;
  }

  // socket 消息 更改 messageType 为撤回状态
  updateMessageToWithDraw({String? groupId, String? receiver, msgId}) async {
    await parseRole(this, groupId, receiver);
    await DbHelper.upDateMessageMsgType(
        uid, msgId, ConstantImMsgType.SSChatMessageTypeUndo);

    var msgs = await DbHelper.getMessageByUidAndMsgId(uid, msgId);
    if(msgs.isNotEmpty){
      msgs.first.resetWithDrawReplayMessage();
      return msgs.first;
    }
  }

  // socket 消息 已读
  updateMessageToReaded({String? groupId, String? receiver, sessionId}) async {
    await parseRole(this, groupId, receiver);
    await DbHelper.upDateMessageReadStatus(uid, sessionId);
  }

  // socket 消息 群组通知类消息
  Future<Message> createGroupNoticeMessage(
      {String? groupId,
      String? receiver,
      required NoticeMsg notice,
      required String text,
      required int noticeSessionType,
      bool isNeedInsert = true}) async {
    await parseRole(this, groupId, receiver);
    String dataStr = notice.data;
    Map<String, dynamic> dataMap = json.decode(dataStr);
    int msgType = parseGroupNoticeMsgType(dataMap);
    if(noticeSessionType == ConstantImMsgType.SSChatMessageTypeRobot){
      msgType = noticeSessionType;
    }
    var message = Message(msgId)
      ..uid = uid
      ..cmdId = cmdId
      ..sessionType = noticeSessionType
      ..sendId = senderInfo.userId
      ..sendName = senderInfo.nickname
      ..sendHeader = senderInfo.avatar
      ..sendTime = msgTime
      ..isReaded = 0
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..text = text
      ..extendOne = dataStr ??''
      ..msgType = msgType
      ..appChatId = appChatId
      ..sessionId = sessionId;
    _parseGroupNotice(notice, message, uid);
    if (isNeedInsert && message.msgType != ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate) {
      DbHelper.insertMsg(message);
    }
    return message;
  }

  // socket 消息 转换为位置 Message
  Future<Message> createLocationMessage(
      {String? groupId, String? receiver}) async {
    await parseRole(this, groupId, receiver);
    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = ConstantImMsgType.SSChatMessageTypeMap
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..longitude = this.locationMsg?.longitude
      ..latitude = this.locationMsg?.latitude
      ..addressTitle = this.locationMsg?.title
      ..addressDetail = this.locationMsg?.address
      ..addressImgUrl = this.locationMsg?.uri
      ..text = ImPrefixMsg.locationCardString
      ..appChatId = appChatId
      ..sessionId = sessionId;
    DbHelper.insertMsg(message);

    return message;
  }

  // socke 转为 ext 扩展类型的消息
  Future<Message> createExtMessage(ExtMsg ext,
      {String? groupId, String? receiver}) async {
    await parseRole(this, groupId, receiver);

    if (groupId == null) {
      appChatId = senderInfo.userId;
    }

    var msgType = type;
    var msgText = msgContent;

    CustomMsgModel? customMsg = CustomMsgModel.fromJson(jsonDecode(ext.ext1));

    String extendOne = ext.ext1;

    if (type == ConstantImMsgType.SSChatMessageTypeBlacklistChange) {
      // 黑名单变化 , 机器人 ， 转发记录 todo
      msgText = ext.ext1;
    } else if (type == ConstantImMsgType.SSChatMessageTypeQuote) {
      // 引用，
      var msg = Message.fromJson(jsonDecode(ext.ext1));
      msgText = msg.quoteText ?? '';
    } else if (type == ConstantImMsgType.SSChatMessageTypeRobot) {
      // 机器人
      logger('群 socket 机器人消息 ${ext.ext1}');
      try {
        var botEntity = ImBotExtEntity.fromJson(jsonDecode(ext.ext1));
        extendOne = botEntity.data ?? '';
        msgText = botEntity.context ?? '';
      } catch (e) {
        var botEntity = ClientNoticeMsg.fromJson(jsonDecode(ext.ext1));
        extendOne = botEntity.data ?? '';
        msgText = botEntity.context ?? '';
      }
    }

    var message = Message(this.msgId)
      ..uid = uid
      ..cmdId = this.cmdId
      ..sessionType = sessionType
      ..sendId = this.senderInfo.userId
      ..sendName = this.senderInfo.nickname
      ..sendHeader = this.senderInfo.avatar
      ..sendTime = this.msgTime
      ..isReaded = 0
      ..msgType = msgType
      ..msgFrom = msgFrom
      ..isSuccess = 1
      ..extendOne = extendOne
      ..extendTwo = customMsg?.extendTwo
      ..extendThree = customMsg?.extendThree
      ..imgUrl = customMsg?.imgUrl
      ..miniImgUrl = customMsg?.miniImgUrl
      ..voiceUrl = customMsg?.voiceUrl
      ..text = msgText
      ..appChatId = appChatId
      ..sessionId = sessionId;
    DbHelper.insertMsg(message);

    return message;
  }

  _parseGroupNotice(NoticeMsg noticeMsg, Message message, String userId) {
    switch (message.msgType) {
      //建群或者邀请
      case ConstantImMsgType.SSChatMessageTypeChatGroupInvitation:
        var noticeData = jsonDecode(noticeMsg.data);
        var notice = GroupNoticeDataBean.fromJson(noticeData);
        //
        Iterable l = jsonDecode(notice.content ?? '');
        List<InviteGroupNoticeContentBean> noticeList =
            List<InviteGroupNoticeContentBean>.from(
                l.map((model) => InviteGroupNoticeContentBean.fromJson(model)));
        String sb = '';
        noticeList.forEach((element) {
          sb += '${element.name},';
        });
        if (StringUtil.isEmpty(sb)) return;
        var content = sb.getRange(0, sb.length - 1);
        if (notice.userId == userId) {
          message.text = "<font color='#4D91EF'>你</font>" +
              "邀请" +
              "<font color='#4D91EF'>$content</font>" +
              "加入群组";
        } else {
          message.text = "<font color='#4D91EF'>${notice.name}</font>" +
              "邀请" +
              "<font color='#4D91EF'>$content</font>" +
              "加入群组";
        }
        break;
      case ConstantImMsgType
            .SSChatMessageTypeChatGroupOwnerConfigInfo: // 群主配置信息
      case ConstantImMsgType.SSChatMessageTypeChatGroupNameChange: //修改群名称
      case ConstantImMsgType.SSChatMessageTypeChatGroupCreateChange:
        var noticeData = jsonDecode(noticeMsg.data);
        var notice = GroupNoticeDataBean.fromJson(noticeData);
        Iterable l = jsonDecode(notice.content ?? '');
        List<ReNameReCreaterContentBean> noticeList =
            List<ReNameReCreaterContentBean>.from(
                l.map((model) => ReNameReCreaterContentBean.fromJson(model)));
        if (noticeList != null && noticeList.isNotEmpty) {
          message.text = ImHtmlParser.dealNoticeContent(noticeList);
        } else {
          message.text = '';
        }

        break;
      case ConstantImMsgType.SSChatMessageTypeChatGroupOrganizationAdd: //团队群组加入
        message.text = noticeMsg.context;
        break;
      case ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo:
        var noticeData = jsonDecode(noticeMsg.data);
        var videoBean = GroupVoiceVideoReceiveMsgBean.fromJson(noticeData);
        message.sendId = videoBean.sendId ?? "";
        message.sendName = videoBean.content ?? "";
        ;
        message.callType = videoBean.callType ?? 0;
        message.callContent = videoBean.content ?? "";
        message.meetingId = videoBean.meetingId ?? "";
        message.msgType = int.parse((videoBean.msgType ?? '0'));
        message.extendTwo = videoBean.personList ?? "";
        var tempStr = '发起了音视频通话';
        message.voiceUrl = tempStr;
        if (message.sendId == userId) {
          message.text = '你$tempStr';
        } else {
          message.text = '${videoBean.content}$tempStr';
        }
        break;
      case ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd:
        var noticeData = jsonDecode(noticeMsg.data);
        var videoBean = GroupVoiceVideoReceiveMsgBean.fromJson(noticeData);
        message.callType = videoBean.callType ?? 0;
        message.callContent = videoBean.content ?? "";
        message.meetingId = videoBean.meetingId ?? "";
        message.msgType = int.parse((videoBean.msgType ?? '0'));
        message.text = videoBean.content ?? "";
        break;
      case ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate:
        var noticeData = jsonDecode(noticeMsg.data);
        var videoBean = GroupVoiceVideoReceiveMsgBean.fromJson(noticeData);
        message.sendId = videoBean.sendId ?? "";
        message.sendName = videoBean.content ?? "";
        ;
        message.callType = videoBean.callType ?? 0;
        message.callContent = videoBean.content ?? "";
        message.meetingId = videoBean.meetingId ?? "";
        message.msgType = int.parse((videoBean.msgType ?? '0'));
        message.extendTwo = videoBean.personList ?? "";
        var tempStr = '发起了音视频通话';
        message.voiceUrl = tempStr;
        if (message.sendId == userId) {
          message.text = '你$tempStr';
        } else {
          message.text = '${videoBean.content}$tempStr';
        }

        break;
      default:
        break;
    }
  }

  updateGroupDbData(String groupId, NoticeMsg notice) async {
    await parseRole(this, groupId, null);

    var currentGroup = await DbHelper.queryGroupByGroupId(groupId);
    if (currentGroup == null) return;

    String dataStr = notice.data;
    Map<String, dynamic> dataMap = json.decode(dataStr);

    int msgType = 0;
    if (dataMap['msgType'] is int) {
      msgType = dataMap['msgType'];
    } else if (dataMap['msgType'] is String) {
      msgType = int.parse(dataMap['msgType']);
    }
    switch (msgType) {
      case ConstantImMsgType.SSChatMessageTypeChatGroupOwnerConfigInfo:
        String targetUserId = dataMap['targetUserId'];
        if (targetUserId != "" && targetUserId == uid) {
          int isAdmin = dataMap['operation'] == 2 ? 1 : 0;
          currentGroup.isAdmin = isAdmin;
        } else {
          int updateTime = DateTime.now().millisecondsSinceEpoch;
          currentGroup.updateTime = updateTime;
        }
        DbHelper.insertGroup(currentGroup);
        break;
      case ConstantImMsgType.SSChatMessageTypeChatGroupNameChange:
        String groupName = notice.title;
        currentGroup.name = groupName;
        DbHelper.insertGroup(currentGroup);
        break;
      case ConstantImMsgType.SSChatMessageTypeChatGroupInvitation:
      case ConstantImMsgType.SSChatMessageTypeChatGroupCreateChange:
      case ConstantImMsgType.SSChatMessageTypeChatGroupMemberChange:
        int updateTime = DateTime.now().millisecondsSinceEpoch;
        currentGroup.updateTime = updateTime;
        DbHelper.insertGroup(currentGroup);
        break;
      default:
    }
  }
}

String socket_createAppChatId(
  ImCommonData commonData,
  int sessionType,
  String appChatId, {
  String groupId = "",
  int msgFrom = 2,
}) {
  var mAppChatId = "";
  if (sessionType == 2) {
    mAppChatId = groupId;
  } else {
    if (msgFrom == 2) {
      mAppChatId = commonData.senderInfo.userId;
    } else {
      mAppChatId = appChatId ?? '';
    }
  }
  return mAppChatId;
}

Future<String> socket_getSessionId(
    ImCommonData commonData, String receiver) async {
  var uid = await ImGlobalUtil.currentUserId();
  if (commonData.senderInfo.userId == uid) {
    return receiver;
  }
  return commonData.senderInfo.imUserId;
}

int parseGroupNoticeMsgType(Map<String, dynamic> dataMap) {
  var msgType = 0;
  if (dataMap['msgType'] is String) {
    msgType = int.parse(dataMap['msgType']);
  } else if (dataMap['msgType'] is int) {
    msgType = dataMap['msgType'];
  }
  return msgType;
}
