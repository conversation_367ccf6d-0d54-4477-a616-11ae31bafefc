

import 'package:intl/intl.dart';

extension ImTimeExt on int? {

    /// 不是今年显示： 年月日 时分
    /// 今年 显示月日 时分
    /// 今天 显示 十分
    String convertDate() {
      if(this == null || this == 0) return '';
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(this!);
      DateTime now = DateTime.now();
      if(dateTime.year < now.year) {
        // 不是今年
        return DateFormat("yyyy-MM-dd HH:mm").format(dateTime);
      }
      if(isToday(this!)){
        return DateFormat("HH:mm").format(dateTime);
      }
      return DateFormat("MM-dd HH:mm").format(dateTime);;
    }

    bool isToday(int timeStamp) {
      var d = DateTime.fromMillisecondsSinceEpoch(timeStamp);
      var n = DateTime.now();
      if(d.year == n.year && d.month == n.month && d.day == n.day){
        return true;
      }
      return false;
    }

    /// 点击消息后 显示精确的时间
    String convertAccurateTime() {
      if(this == null || this == 0) return '';
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(this!);
      return DateFormat("yyyy-MM-dd HH:mm:ss").format(dateTime);
    }

}