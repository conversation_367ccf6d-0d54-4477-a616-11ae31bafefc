import 'dart:convert';

import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/ext/notice_msg_ext.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_model.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_payLoad_model.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../proto_model/Msg.pb.dart';
import 'c2c_group_ext.dart';

extension IMMSGExtension on ImMsg {
  ImCommonData createImCommonReceiverData() {
    return ImCommonData(
        msgId, cmdId, msgTime.toInt(), type, selfMsg, senderInfo, msgContent);
  }

  // 区分收到的消息类型
  bool isIMLoginResp() => hasField(5);
  bool isIMSingleChat() => hasField(6);
  bool isIMSingleResp() => hasField(8);
  bool isIMGroup() => hasField(9);
  bool isIMGroupResp() => hasField(11);
  bool isPush() => hasField(12);
  bool isImApproveChanged() => hasField(13);
  bool isNotice() => hasField(14);
  bool isImException() => hasField(3);
  bool isKickOff() => hasField(23);
  bool isImPush() => hasField(24); // 接收pc端或者移动端进行了黑名单或者免打扰的设置---接收投诉提醒的提醒消息
  bool isImMultiSelf() => hasField(7);

  LoginResult iMLoginResp() => getField(5) as LoginResult; // 收到， 登录结果
  C2CMsg singleChatData() => getField(6) as C2CMsg; // 单聊
  C2CMsgResponse getSingleRespData() => getField(8) as C2CMsgResponse; // 单聊响应
  GroupMsg getGroupData() => getField(9) as GroupMsg; // 群聊
  GroupMsgResponse getGroupRespData() =>
      getField(11) as GroupMsgResponse; // 群聊响应
  PushMsg getPushData() => getField(12) as PushMsg; //
  ServerMsg imApproveChangedData() => getField(13) as ServerMsg; // 审批
  NoticeMsg systemNoticeData() => getField(14) as NoticeMsg; // 收到 【系统通知】
  Exception imException() => getField(3) as Exception; // 收到 异常
  KickMsg iMkickOff() => getField(23) as KickMsg; // 被踢出
  ImPushMsg getImPushData() =>
      getField(24) as ImPushMsg; // 接收pc端或者移动端进行了黑名单或者免打扰的设置---接收投诉提醒的提醒消息--
  C2CMsgRequest imMultiSelf() =>
      getField(7) as C2CMsgRequest; // 多端同步时用于接收另一端给自己设备发送的消息

  Future isNeedSendLocalNotification() async {
    var myUid = await UserHelper.getUid();
    var myImId = await UserHelper.getOwnImId();
    //单群聊是否需要发送本地通知的判断
    if (isIMSingleChat()) {
      if (c2cMsg.receiver == senderInfo.imUserId) {
        //发送方和接收方为同一人
        return false;
      }

      if (senderInfo.imUserId == myImId) {
        return false;
      }

      var session = await DbHelper.getSessionByOwnerId2SessionId(myUid, senderInfo.imUserId);
      if (session?.noDisturb == 1) return false;

      return !(type == ConstantImMsgType.SSChatMessageTypeUndo ||
          type == ConstantImMsgType.SSChatMessageTypeReadOthers ||
          type == ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused ||
          type == ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange ||
          type == ConstantImMsgType.SSChatMessageTypeBlacklistChange);
    } else if (isIMGroup()) {
      var myImId = await UserHelper.getOwnImId();
      var session = await DbHelper.getSessionByOwnerId2SessionId(myUid, groupMsg.groupId);
      if (session?.noDisturb == 1) return false;

      if (myImId == senderInfo.imUserId) {
        return false;
      } else if (type == ConstantImMsgType.SSChatMessageTypeUndo) {
        return false;
      } else if (type == ConstantImMsgType.SSChatMessageTypeChatGroupNotice) {
        String dataStr = groupMsg.noticeMsg.data;
        Map<String, dynamic> dataMap = json.decode(dataStr);
        var msgType = 0;
        if (dataMap['msgType'] is int) {
          msgType = dataMap['msgType'];
        }else if(dataMap['msgType'] is String){
          msgType = int.parse(dataMap['msgType']);
        }
        if (msgType ==
                ConstantImMsgType.SSChatMessageTypeChatGroupMemberChange ||
            msgType >=
                ConstantImMsgType.SSChatMessageTypeChatGroupOwnerConfigInfo) {
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  }

  createNotificationModel(LocalNotiModel settingModel) {
    if (isIMSingleChat()) {
      return getSingleNotificationModel(settingModel);
    }
    if (isIMGroup()) {
      return getGroupNotificationModel(settingModel);
    }
    if (isNotice()) {
      return getNoticeNotificationModel(settingModel);
    }
  }

  //获取单聊通知model
  getSingleNotificationModel(settingModel) {
    LocalNotiPayLoadModel payLoadModel = LocalNotiPayLoadModel()
      ..type = ConstantImMsgType.SSChatConversationTypeChat
      ..userId = senderInfo.userId
      ..imUserId = senderInfo.imUserId
      ..nickname = senderInfo.nickname
      ..avatar = senderInfo.avatar;

    settingModel
      ..title = senderInfo.nickname
      ..payload = jsonEncode(payLoadModel.toJson());
    return settingModel;
  }

  //获取群聊通知model
  getGroupNotificationModel(settingModel) {
    LocalNotiPayLoadModel payLoadModel = LocalNotiPayLoadModel()
      ..type = ConstantImMsgType.SSChatConversationTypeGroupChat
      ..groupId = groupMsg.groupId
      ..groupName = groupMsg.groupName
      ..groupLogo = groupMsg.groupLogo;

    settingModel
      ..title = groupMsg.groupName
      ..payload = jsonEncode(payLoadModel.toJson());
    return settingModel;
  }

  //获取通知类型 通知model
  getNoticeNotificationModel(settingModel) {
    String noticeTitle = '';

    Map<String, dynamic> map = json.decode(noticeMsg.data);
    if (type == ConstantImMsgType.SystemMsgSessionType) {
      noticeTitle = '系统通知';
    } else {
      noticeTitle = map['companyName'] ?? '';
    }
    dynamic payLoadData;
    
    map.removeWhere(
        (key, value) => key == 'content' || key == 'buttons'); //去除数据可能多的无用字段
    map['tempStatus'] = noticeMsg.tempStatus;
    payLoadData = jsonEncode(map);
    LocalNotiPayLoadModel payLoadModel = LocalNotiPayLoadModel()
      ..type = type
      ..data = payLoadData;
    settingModel
      ..title = noticeTitle
      ..body = noticeMsg.context
      ..payload = jsonEncode(payLoadModel.toJson());

    return settingModel;
  }

  //处理单聊收到音视频消息
  Future<Message?> dealSignleCall(
      ImCommonData imCommonData, bool isNeedInsert) async {
    if (type == ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused) {
      //callContent 1已在通话会议中拒绝，2接收者主动挂断--指令
      var myUserId = await UserHelper.getUid();
      if (myUserId != senderInfo.userId) {
        Channel().invoke(Channel_Receive_Call_Msg, {
          'userId': senderInfo.userId,
          'content': c2cMsg.call.content,
          'msgType': type
        });
      }
      return null;
    } else if (type == ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo ||
        type == ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd ||
        type == ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure) {
      imCommonData.call = c2cMsg.call;
      Message singleMessage = await imCommonData.createSingleCallMessage(
          receiver: c2cMsg.receiver, isNeedInsert: isNeedInsert);
      if (type == ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo) {
        // 接到别人的音视频邀请
        // 发起音视频通话指令成功后，通知native 进入音频界面 接听页面
        Map<String, dynamic> messageJson = singleMessage.toJson();
        Channel().invoke(
          Channel_Receive_Call_Msg,
          messageJson, //群聊添加groupName,groupLogo字段
        );
      }
      if (type == ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure) {
        // 发起音视频通话指令失败后，通知native
        Channel().invoke(Channel_Receive_Call_Msg, {
          'meetingId': singleMessage.meetingId,
          'msgType': ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure
        });
      }
      return singleMessage;
    }
  }
}

// 单聊
extension C2CMSGExtension on C2CMsg {
  bool canCreateSession() {
    return isText() ||
        isImg() ||
        isFile() ||
        isVideo() ||
        isVoice() ||
        isLocation() ||
        isWithDraw() ||
        isExt() ||
        isCall();
  }

  // 区分收到的消息类型
  bool isText() => hasField(6); // 文本
  bool isImg() => hasField(7); // 图片
  bool isFile() => hasField(11); // 文件
  bool isVideo() => hasField(10); // video
  bool isVoice() => hasField(8); // voice
  bool isLocation() => hasField(9); // location
  bool isWithDraw() => hasField(12); // withDraw
  bool isRead() => hasField(13); // hasRead
  bool isCall() => hasField(15); // call
  bool isExt() => hasField(14); // ext

  String getText() => msg;
  ImgMsg getImg() => image;
  FileMsg getFile() => file;
  VideoMsg getVideo() => video;
  Voice getVoice() => voice;
  Location getLocation() => location;
  ReadMsg getRead() => read;
  AudioAndVideoCall getCall() => call;
  ExtMsg getExt() => ext;
}

/// 群聊
extension GroupMsgExtension on GroupMsg {
  bool canCreateSession() {
    return isText() ||
        isImg() ||
        isFile() ||
        isVideo() ||
        isVoice() ||
        isLocation() ||
        isWithDraw() ||
        isExt() ||
        isNotice();
  }

  // 区分收到的消息类型
  bool isText() => hasField(1); // 文本
  bool isImg() => hasField(2); // 图片
  bool isFile() => hasField(9); // 文件
  bool isVideo() => hasField(8); // video
  bool isVoice() => hasField(3); // voice
  bool isLocation() => hasField(4); // location
  bool isWithDraw() => hasField(7); // withDraw
  bool isCall() => hasField(10); // call
  bool isExt() => hasField(6); // ext
  bool isNotice() => hasField(11); // 通知

  String getText() => msg;
  ImgMsg getImg() => image;
  FileMsg getFile() => file;
  VideoMsg getVideo() => video;
  Voice getVoice() => voice;
  Location getLocation() => location;
  Withdraw getWithDraw() => withdraw;
  AudioAndVideoCall getCall() => call;
  NoticeMsg getNotice() => noticeMsg;
  ExtMsg getExt() => ext;
}

// 单聊响应
extension C2CMsgResponseExtension on C2CMsgResponse {
  bool isSuccess() => failType.value == 0 || failType.value == 2;
}

/// 群聊响应
extension GroupMsgResponseExtension on GroupMsgResponse {
  bool isSuccess() => failType.value == 0 || failType.value == 2;
}

/// 黑名单或者免打扰的设置实体
extension PushMsgExtension on ImPushMsg {
  bool isAddedBlackList() => hasField(1);
  bool isRemovedBlackList() => hasField(2);
  bool isSingleChatMuteAdd() => hasField(3); // 单聊打开免打扰
  bool isSingleChatMuteRemoved() => hasField(4); // 单聊关闭免打扰
  bool isGroupChatMuteAdd() => hasField(5); // 群聊打开免打扰
  bool isGroupChatMuteRemoved() => hasField(6); // 群聊关闭免打扰
  bool isWarning() => hasField(7); // 是否是接收投诉提醒的警告信息

  String blackListAddSessionId() => blacklistAdd; // 加入黑名单的sessionId
  String blackListRemovedSessionId() => blacklistRemove; // 移除黑名单的sessionId
  String singleChatIdIfMuteAdd() => singleMuteListAdd; // 开启免打扰的 单聊的用户id
  String singleChatIdIfMuteRemoved() => singleMuteListRemove; // 关闭免打扰的 单聊的用户id
  String groupIdIfMuteAdd() => groupMuteListAdd; // 关闭免打扰的 群聊的id
  String groupIdIfMuteRemoved() => groupMuteListRemove; // 关闭免打扰的 群聊的id
  String? warningMsg() => warnMessage.msg; //
  String? warningSessionId() => warnMessage.sessionId; //
}

/// 多端同步 接收另一端给自己设备发送的消息
extension C2CMsgRequestExtension on C2CMsgRequest {}

// 存储下常用数据
class ImCommonData with SocketRoleParse {
  String msgId;
  String cmdId;
  int msgTime;
  int type;
  bool selfMsg;
  UserInfo senderInfo;
  String msgContent;

  // 其他
  ImgMsg? imgMsg;
  FileMsg? fileMsg;
  VideoMsg? videoMsg;
  Voice? voiceMsg;
  Location? locationMsg;
  ExtMsg? extMsg;
  AudioAndVideoCall? call;

  ImCommonData(this.msgId, this.cmdId, this.msgTime, this.type, this.selfMsg,
      this.senderInfo, this.msgContent);
}
