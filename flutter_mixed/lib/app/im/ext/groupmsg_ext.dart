

import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/proto_model/Msg.pbserver.dart';

import '../constant/ImMsgConstant.dart';
import '../convert/in_time_im_convert_to_session.dart';
import '../db/db_helper.dart';
import '../db/entity/message.dart';
import '../db/entity/session.dart';
import '../utils/im_global_util.dart';
import 'im_msg_ext.dart';


// 即时收到的群聊 proto buffer 实体扩展
extension ParseGroupMsgExt on GroupMsg {

  

}