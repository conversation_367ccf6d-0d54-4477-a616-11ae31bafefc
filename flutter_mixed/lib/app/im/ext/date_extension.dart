import 'package:intl/intl.dart';

extension DateExtension on DateTime {

  String formatMMDDYYYY() {
    return DateFormat('MM/dd/yyyy').format(this);
  }

  String formatMMMYYYY() {
    return DateFormat('MMM/yyyy').format(this);
  }

  String formatMMDDYYYYHHMM() {
    return DateFormat("MM/dd/yyyy HH:mm").format(this);
  }

  String formatYYYYMMDDHHMMSS() {
    return DateFormat("yyyy-MM-dd HH:mm:ss").format(this);
  }

  String formatYYYYMMDD() {
    return DateFormat("yyyy-MM-dd").format(this);
  }

  DateFormat _messageDateFormat() {

    if(isToday()) {
      return DateFormat("HH:mm a");

    }else if (isYesterday()) {
      return DateFormat("HH:mm a");

    }else {
      return DateFormat("MM/dd/yyyy HH:mm a");
    }
  }

  String formatMessageDte() {

    if(isToday()) {
    return DateFormat('h:mm a').format(this);

    }else if (isYesterday()) {
      return "Yesterday ${DateFormat('h:mm a').format(this)}";

    }else {
      return DateFormat("MM/dd/yyyy h:mm a").format(this);
    }

  }

  String formatMsgListDate() {

    if(isToday()) {
      return DateFormat('h:mm a').format(this);

    }else if (isYesterday()) {
      return "Yesterday\n${DateFormat('h:mm a').format(this)}";

    }else {
      return DateFormat("MM/dd\nh:mm a").format(this);
    }

  }


  bool isToday() {
    DateTime now = DateTime.now();
    DateTime localDateTime = toLocal();

    if (localDateTime.day == now.day && localDateTime.month == now.month &&
        localDateTime.year == now.year) {
      return true;
    }

    return false;
  }

  bool isYesterday() {

    DateTime now = DateTime.now();
    DateTime localDateTime = toLocal();
    DateTime yesterday = now.subtract(const Duration(days: 1));

    if (localDateTime.day == yesterday.day && localDateTime.month == yesterday.month &&
        localDateTime.year == yesterday.year) {
      return true;
    }

    return false;
  }
}
