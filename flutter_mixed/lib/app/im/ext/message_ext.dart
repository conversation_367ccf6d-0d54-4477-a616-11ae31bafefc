import 'dart:convert';

import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/request/entity/chat_resp_ext.dart';
import 'package:flutter_mixed/app/im/ui/chat/common/picture_manager.dart';
import 'package:flutter_mixed/app/im/ui/chat/entity/message_item_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/message/custom_msg_model.dart';
import 'package:flutter_mixed/app/im/ui/chat/message_bubble.dart';
import 'package:flutter_mixed/app/im/utils/date_util.dart';
import 'package:flutter_mixed/app/im/utils/file_util.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../../utils/video_util.dart';
import '../../utils/image_util.dart';
import '../constant/ImMsgConstant.dart';
import '../db/entity/session.dart';

extension MessageExt on Message {
  bool isText() => msgType == ConstantImMsgType.SSChatMessageTypeText;
  bool isVoice() => msgType == ConstantImMsgType.SSChatMessageTypeVoice;
  bool isImage() => msgType == ConstantImMsgType.SSChatMessageTypeImage;
  bool isGif() => msgType == ConstantImMsgType.SSChatMessageTypeGifImage;
  bool isVideo() => msgType == ConstantImMsgType.SSChatMessageTypeVideo;
  bool isFile() => msgType == ConstantImMsgType.SSChatMessageTypeFile;
  bool isLocation() => msgType == ConstantImMsgType.SSChatMessageTypeMap;
  bool isUndo() => msgType == ConstantImMsgType.SSChatMessageTypeUndo;
  bool isAtSingle() =>
      msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice;
  bool isAtMore() =>
      msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice;
  bool isMsgRecord() =>
      msgType == ConstantImMsgType.SSChatMessageTypeForwardRecord;
  bool isBot() => msgType == ConstantImMsgType.SSChatMessageTypeRobot;

  // 删除类型
  bool isDel() => msgType == ConstantImMsgType.SSChatMessageTypeDel;

  bool isSoftDel() => isDelete == 1;

  bool isQuote() => msgType == ConstantImMsgType.SSChatMessageTypeQuote;

  bool isAtContent() => msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice ||
      msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice;

  bool isMyself() => msgFrom == 1;

  bool isAudioAndVideoCall() =>
      msgType == ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo ||
      msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd ||
      msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure ||
      msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused;

  bool isCompanyInvite() => msgType == ConstantImMsgType.SSChatMessageTypeInvite;

  MessageSendStatus sendStatus() {
    return MessageSendStatus.getSendStatus(isSuccess ?? 0);
  }

  // 引用 或者简略示意的时候的标记
  String alias() {
    if (isText()) return text ?? '';
    if (isImage()) return ImPrefixMsg.imageCardString;
    if (isVoice()) return '${ImPrefixMsg.voiceCardString}${voiceTime ?? 0}s';
    if (isVideo()) return ImPrefixMsg.videoCardString;
    if (isGif()) return ImPrefixMsg.gifCarString;
    if (isLocation()) return ImPrefixMsg.locationCardString;
    if (isFile()) return ImPrefixMsg.fileCardString;
    if (isMsgRecord()) {
      var extendOne = this.extendOne ?? '';
      MsgUIRecord record = MsgUIRecord.fromJson(jsonDecode(extendOne));
      if (record.sessionType == 1) {
        return ImPrefixMsg.singleRecordString;
      } else {
        return ImPrefixMsg.groupRecordString;
      }
    };
    if (isQuote()) return text ?? '';
    if (isUndo()) return '你撤回了一条消息';
    if (isAudioAndVideoCall()) return text ?? '';
    if (isCompanyInvite()) return text ?? '';
    if (isAtContent()) return text ?? '';
    return text ?? '';
  }

  String createTime() => DateUtil.timeStamp2YYYYMMDDHHMMSS(sendTime ?? 0);

  /// --------------------------------------------------------
  /// 原生Message 转换为具体uiData 类型
  Future<MessageItem> toMessageItem({bool? isRecordStyle = false}) async {
    if (sessionType == ConstantImMsgType.SSChatMessageTypeChatGroupNotice) {
      return MessageSysData(this, sendName, sendHeader, text, isMyself(),
          sendStatus(), createTime());
    }

    switch (msgType) {
      case ConstantImMsgType.SSChatMessageTypeOfflineFault:
        // 断层
        return MessageOfflineFault(this, sendName, sendHeader, text, isMyself(),
            sendStatus(), createTime());
      case ConstantImMsgType.SSChatMessageTypeText:
        return MessageTextData(this, sendName, sendHeader, text, isMyself(),
            sendStatus(), createTime());
      case ConstantImMsgType.SSChatMessageTypeImage:
        return await _buildImage();
      case ConstantImMsgType.SSChatMessageTypeVoice:
        return _buildVoice();
      case ConstantImMsgType.SSChatMessageTypeVideo:
        return _buildVideo();
      case ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo: // 发起音视频
        return _buildStartVideoCall();
      case ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd: // 结束音视频
        return _buildEndVideoCall();
      case ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure:
        return _buildVideoCallFailure();
      case ConstantImMsgType.SSChatMessageTypeUndo:
        if (isRecordStyle == true) {
          var reCalltext = '「${this.sendName ?? ""}撤回了一条消息」';
          return MessageTextData(this, sendName, sendHeader, reCalltext,
              isMyself(), sendStatus(), createTime());
        }
        return _buildReCallMsg();
      case ConstantImMsgType.SSChatMessageTypeBlack: //被拒绝时需要显示的灰条消息
        return _buildInBlackMsg();
      case ConstantImMsgType.SSChatMessageTypeMap:
        return _buildLocation();
      case ConstantImMsgType.SSChatMessageTypeInvite: //邀请加入团队
        return _buildInvite();
      case ConstantImMsgType.SSChatMessageTypeRobot:
        return _buildBotMsg();
      case ConstantImMsgType.SSChatMessageTypeForwardRecord:
        return _buildRecordMsg();
      case ConstantImMsgType.SSChatMessageTypeFile:
        return _buildFileMsg();
      case ConstantImMsgType.SSChatMessageTypeQuote:
        return _buildQuoteMsg();
      case ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice:
      case ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice:
        if(extendThree != null) {
          return _buildQuoteMsg();
        }
        return MessageTextData(this, sendName, sendHeader, text, isMyself(),
            sendStatus(), createTime());
      default:
        return MessageTextData(this, sendName, sendHeader, text, isMyself(),
            sendStatus(), createTime());
    }
  }

  MessageVoiceData _buildVoice() {
    return MessageVoiceData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..fileId = fileId
      ..duration = voiceTime
      ..localUrl = localUrl;
  }

  MessageVideoData _buildVideo() {
    return MessageVideoData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..fileName = fileName
      ..videoImageId = videoImageId
      ..coverWidth = imgWidth ?? 100
      ..coverHeight = imgHeight ?? 100
      ..cover = videoImagePath
      ..fileId = fileId
      ..localUrl = localUrl
      ..progress = longitude;
  }

  Future<MessageImageData> _buildImage() async {
    var data = MessageImageData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..fileId = this.fileId
      ..imgWidth = this.imgWidth ?? 100
      ..imgHeight = this.imgHeight ?? 50
      ..progress = this.longitude;

    var exist = await FileUtil.isExist(localUrl);
    if(exist){
      data.localUrl = localUrl;
    }else{
      data.localUrl = await FileThumbHelper.thumbPathByFileId(fileId);
    }

    return data;
  }

  MessageSysData _buildStartVideoCall() {
    var callText = '发起了音视频通话';
    if (sendId == uid) {
      callText = '你$callText';
    } else {
      callText = '$callContent$callText';
    }
    return MessageSysData(this, sendName, sendHeader, callText, isMyself(),
        sendStatus(), createTime());
  }

  MessageAudioAndVideoCallData _buildEndVideoCall() {
    return MessageAudioAndVideoCallData(this, sendName, sendHeader,
        callContent ?? '', isMyself(), sendStatus(), createTime());
  }

  // 发起人，未接听
  MessageAudioAndVideoCallData _buildVideoCallFailure() {
    logger('======weijieting====${toJson()}');
    return MessageAudioAndVideoCallData(this, sendName, sendHeader,
        callContent ?? '', isMyself(), sendStatus(), createTime());
  }

  MessageSysData _buildReCallMsg() {
    var reCallText = "你撤回了一条消息";
    if (uid != sendId) {
      reCallText = "$sendName撤回了一条消息";
    }
    return MessageSysData(this, sendName, sendHeader, reCallText, isMyself(),
        sendStatus(), createTime());
  }

  MessageLocationData _buildLocation() {
    return MessageLocationData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..longitude = longitude
      ..latitude = latitude
      ..addressTitle = addressTitle
      ..addressDetail = addressDetail
      ..addressImgUrl = addressImgUrl;
  }

  MessageInviteInTeamData _buildInvite() {
    var ext1  = jsonDecode(extendOne ??'');
    var custoMsg = CustomMsgModel.fromJson(ext1);
    var companyName = custoMsg.extendThree;
    var companyId = custoMsg.extendTwo;
    var logo = custoMsg.imgUrl;
    return MessageInviteInTeamData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..companyId = companyId
      ..logo = logo
      ..companyName = companyName;
  }

  MessageInBlackData _buildInBlackMsg() {
    return MessageInBlackData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime());
  }

  MessageRecordUiData _buildRecordMsg() {
    MsgUIRecord? record;
    if (!StringUtil.isEmpty(extendOne)) {
      record = MsgUIRecord.fromJson(json.decode(extendOne!));
    }
    return MessageRecordUiData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..uiRecord = record;
  }

  MessageFileData _buildFileMsg() {
    return MessageFileData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..fileId = fileId
      ..fileName = fileName
      ..fileType = FileUtil.getFileType(fileName)
      ..fileTypeIcon = FileUtil.getFileTypeIcon(fileName)
      ..size = FileUtil.formatBytes(fileSize ?? 0)
      ..progress = longitude
      ..localUrl = localUrl;
  }

  MessageReplyUiData _buildQuoteMsg() {
    Message? quote;

    if (!StringUtil.isEmpty(extendOne)) {
      // logger('extendOne ===> $extendOne');

      var ext = (json.decode(extendOne!));
      var msgId = ext['msgId'];
      try{
        quote = Message.fromJson(ext);
      }catch(e){
        print('>>>>>> <<<$e');
      }
    }

     if (!StringUtil.isEmpty(extendThree) && [
       ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice,
       ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice,
      ].contains(msgType)) 
     {

      var extThere = (json.decode(extendThree!));
      if(extThere["quoteInfo"] is! String){
        var quoteInfo = extThere["quoteInfo"];
        if(quoteInfo != null && quoteInfo is Map){
          quote = Message.fromJson((quoteInfo as Map<String,dynamic>));
        }
      }
    }

    return MessageReplyUiData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..quoteMsg = quote;
  }

  MessageBotUIData _buildBotMsg() {
    MsgBotUIData _uiData = MsgBotUIData();
    _uiData.parse(extendOne);

    return MessageBotUIData(this, sendName, sendHeader, text, isMyself(),
        sendStatus(), createTime())
      ..botUIData = _uiData;
  }

  sendFail() async {
    isSuccess = 0;
    DbHelper.insertMsg(this);
  }

  // 视频上传成功后补充视频信息 更新数据库
  fillVideoInfo(VideoInfo videoInfo) async {
    this
      ..voiceTime = videoInfo.duration.inSeconds
      ..fileId = videoInfo.fileId
      ..fileSize = videoInfo.size
      ..videoImageId = videoInfo.coverFileId
      ..videoImagePath = videoInfo.cover
      ..imgWidth = videoInfo.coverWidth?.toDouble()
      ..imgHeight = videoInfo.coverHeight?.toDouble()
      ..fileName = "${videoInfo.fileId}.png";
    await DbHelper.insertMsg(this);
  }

  fillImageInfo(ImageInfo imageInfo) async {
    this
      ..fileId = imageInfo.fileId
      ..imgWidth = imageInfo.width.toDouble()
      ..imgHeight = imageInfo.height.toDouble()
      ..localUrl = imageInfo.path;
    await DbHelper.insertMsg(this);
  }

  // 文件上传成功后补充视频信息 更新数据库
  fileFileInfoAndSaveDB(FileInfo fileInfo) async {
    this
      ..fileId = fileInfo.fileId
      ..fileSize = fileInfo.fileSize ?? 0
      ..fileName = fileInfo.fileName;
    await DbHelper.insertMsg(this);
  }

  // 文件上传成功后补充视频信息 更新数据库
  fileAudioInfo(String? fileId, int duration) async {
    this
      ..fileId = fileId
      ..voiceTime = duration;
    await DbHelper.insertMsg(this);
  }

  Future<String> getMsgContent(Session session) async {
    if (isText()) return text ?? '';
    if (isImage()) return ImPrefixMsg.imageCardString;
    if (isVoice()) return ImPrefixMsg.voiceCardString;
    if (isVideo()) return ImPrefixMsg.videoCardString;
    if (isGif()) return ImPrefixMsg.gifCarString;
    if (isLocation()) return ImPrefixMsg.locationCardString;
    if (isFile()) return ImPrefixMsg.fileCardString;
    if (isMsgRecord()) return text ?? '';
    if (isQuote()) return text ?? '';
    if (isCompanyInvite()) return text ?? '';
    if (isAtContent()) return text ?? '';
    return text ?? ImPrefixMsg.unKnownString;
  }


  // 当撤回了一条消息后，之前引入这条消息的数据引用部分都提示为 ‘你撤回了一条消息’
  resetWithDrawReplayMessage() async {
    var ownerId = await UserHelper.getUid();
    var msgList = await DbHelper.queryAllMessageBySessionId(ownerId, sessionId ??'');
    await Future.forEach(msgList.where((m) => m.isQuote()).toList(), (q) async {
      var ext1 = q.extendOne;
      if(StringUtil.isEmpty(ext1)) return;
      Message qM = Message.fromJson(json.decode(ext1!));
      if(qM.msgId == msgId){
        qM.msgType = ConstantImMsgType.SSChatMessageTypeUndo;
        var json = jsonEncode(qM);
        q.extendOne = json;
        await DbHelper.insertMsg(q);
      }
    });

  }
}
