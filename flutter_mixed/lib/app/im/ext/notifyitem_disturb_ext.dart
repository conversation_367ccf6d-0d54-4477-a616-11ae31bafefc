import '../../common/api/Define.dart';
import '../../modules/contact/model/org/org_model.dart';
import '../../utils/storage.dart';
import '../constant/ImMsgConstant.dart';
import '../constant/im_cache_global.dart';
import '../db/entity/session.dart';
import '../request/entity/offline_notice_session_reqbody.dart';

extension NotifyItemDisturbExt on NotifyItemDisturb {

    int? getDisturbState(Session session) {
      var notify = this;
      if(session.sessionType == ConstantImMsgType.SystemMsgSessionType) {
        return systemPushSwitch ;
      }else if(session.sessionType == ConstantImMsgType.ApprovalMsgSessionType){
        return notify.approvalPushSwitch ;
      }else if(session.sessionType == ConstantImMsgType.KingdeeMsgSessionType){
        return notify.kingdeePushSwitch ;
      }else if(session.sessionType == ConstantImMsgType.TicketMsgSessionType){
        return notify.ticketPushSwitch ;
      }else if(session.sessionType == ConstantImMsgType.TrainMsgSessionType){
        return notify.trainingSwitch ;
      }else if(session.sessionType == ConstantImMsgType.MatterMsgSessionType){
        return notify.inventorySwitch ;
      }else if(session.sessionType == ConstantImMsgType.GeneralManagementSessionType){
        return notify.managementSwitch ;
      }else if(session.sessionType == ConstantImMsgType.RangeParkMsgSessionType){
        return notify.rangeParkSwitch ;
      }else if(session.sessionType == ConstantImMsgType.IDCMsgSessionType){
        return notify.idcSwitch ;
      }else {
        return 0;
      }
    }
}



