import 'dart:collection';
import 'dart:convert';
import 'dart:math';

import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/constant/im_cache_global.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import "package:collection/collection.dart";
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/ext/notifyitem_disturb_ext.dart';
import 'package:flutter_mixed/app/im/request/entity/system_notice_body.dart';
import 'package:flutter_mixed/app/im/ui/base/common_check.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import '../../common/api/Define.dart';
import '../../modules/contact/model/org/org_model.dart';
import '../constant/ImMsgConstant.dart';
import '../constant/constant_tcp_util.dart';
import '../request/entity/im_offline_notice_resp.dart';
import '../request/entity/im_offline_session_resp.dart';

extension SessionExt on Session {
  bool isSingleChat() => sessionType == 1;

  bool isNoticeType() =>
      NoticeTypeManager.noticeTypeMap.keys.contains(sessionType);

  bool isGroupChat() =>
      sessionType == 2 ||
      sessionType == ConstantImMsgType.SSChatMessageTypeChatGroupNotice;

  bool isNotice() => (!isSingleChat()) && (!isGroupChat());

  int topType() {
    if (isSingleChat()) return 1;
    if (isGroupChat()) return 2;
    return 3;
  }

  bool isOpenedDisturb() => noDisturb == 1;
  // bool isOpenedDisturb() => true;

  bool isTop() => sessionTop == 1;

  // 单聊已注销
  bool cancelled() {
    if (!isGroupChat()) {
      return chatStatus == 1;
    }
    return false;
  }

  // 群聊已解散
  bool isDissolved() {
    if (isGroupChat()) {
      return chatStatus == 1;
    }
    return false;
  }

  String recordContentAlias() => (!isGroupChat()) ? '会话记录' : '群聊会话记录';

  String recordSessionAlias() => (!isGroupChat()) ? ImPrefixMsg.singleRecordString : ImPrefixMsg.groupRecordString;

  createSingleInfoRouteParam() {
    return {
      'sessionId': sessionId,
      'name': name,
      'avatar': headerUrl,
      'imChatId': appChatId
    };
  }

  createGroupInfoRouteParam() {
    return {'sessionId': sessionId, 'groupName': name};
  }

  resetDisturbState(NotifyItemDisturb? notify) {
    noDisturb = notify?.getDisturbState(this);
  }

  Future reShowSession() async {
    sessionHidden = 0;
    await DbHelper.insertSession(this);
  }

  Future<String> getMsgContent(String text) async {
    if (isText()) return text ?? '';
    if (isImage()) return ImPrefixMsg.imageCardString;
    if (isVoice()) return ImPrefixMsg.voiceCardString;
    if (isVideo()) return ImPrefixMsg.videoCardString;
    if (isGif()) return ImPrefixMsg.gifCarString;
    if (isLocation()) return ImPrefixMsg.locationCardString;
    if (isFile()) return ImPrefixMsg.fileCardString;
    if (isMsgRecord()) return text ?? '';
    if (isQuote()) return msgContent ?? '';
    if (isWithDraw()) return text;
    if (isCall()) return text;
    if (isAtContent()) return text ?? '';
    return text;
  }

  bool isText() => msgType == ConstantImMsgType.SSChatMessageTypeText;
  bool isVoice() => msgType == ConstantImMsgType.SSChatMessageTypeVoice;
  bool isImage() => msgType == ConstantImMsgType.SSChatMessageTypeImage;
  bool isGif() => msgType == ConstantImMsgType.SSChatMessageTypeGifImage;
  bool isVideo() => msgType == ConstantImMsgType.SSChatMessageTypeVideo;
  bool isFile() => msgType == ConstantImMsgType.SSChatMessageTypeFile;
  bool isLocation() => msgType == ConstantImMsgType.SSChatMessageTypeMap;
  bool isUndo() => msgType == ConstantImMsgType.SSChatMessageTypeUndo;
  bool isAtSingle() =>
      msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice;
  bool isAtMore() =>
      msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice;
  bool isMsgRecord() =>
      msgType == ConstantImMsgType.SSChatMessageTypeForwardRecord;
  bool isQuote() => msgType == ConstantImMsgType.SSChatMessageTypeQuote;
  bool isWithDraw() => msgType == ConstantImMsgType.SSChatMessageTypeUndo;
  bool isCall() =>
      msgType == ConstantImMsgType.SSChatMessageTypeInitiateVoiceVideo ||
      msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoEnd ||
      msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoCallFailure ||
      msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused ||
      msgType == ConstantImMsgType.SSChatMessageTypeVoiceVideoTwoInitiate;

  bool isAtContent() => msgType == ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice ||
      msgType == ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice;

  static final Map<String, String?> _targetLogos = {};
  
  void setTargetLogo(String? logo) {
    _targetLogos[sessionId] = logo;
  }
  
  String? getEffectiveHeaderUrl() {
    return _targetLogos[sessionId] ?? headerUrl;
  }
}

extension ImOfflineSessionExt on ImOfflineSessionResp {
  bool isSingleChat() => sessionType == 1;

  Future<List<Session>> _filterGroupsAvatar2Name(List<Session> sessions) async {
    var groups = await ImCacheData.instance.getLocalGroupInfo();
    if (groups == null || groups.isEmpty) return sessions;
    HashMap groupMap = HashMap();
    groups.forEach((group) {
      groupMap.putIfAbsent(group['groupId'], () => group);
    });

    sessions.forEach((session) {
      if (session.isGroupChat() && groupMap.containsKey(session.sessionId)) {
        session.headerUrl = groupMap[session.sessionId]['logo'] ?? '';
        session.name = groupMap[session.sessionId]['name'] ?? '';
      }
    });
    return sessions;
  }

  Future<Session> _filterGroupAvatar2Name(Session session) async {
    var groups = await ImCacheData.instance.getLocalGroupInfo();
    if (groups == null || groups.isEmpty) return session;
    HashMap groupMap = HashMap();
    groups.forEach((group) {
      groupMap.putIfAbsent(group['groupId'], () => group);
    });

    if (session.isGroupChat()) {
      if (groupMap.containsKey(session.sessionId)) {
        session.headerUrl = groupMap[session.sessionId]['logo'] ?? '';
        session.name = groupMap[session.sessionId]['name'] ?? '';
      }else{
        //后端接口拉取群组无群组logo和name字段 群组被解散的情况
        session.headerUrl = '';
        session.name = '';
      }

    }
    return session;
  }

  bool isGroupChat() =>
      sessionType == 2 ||
      sessionType == ConstantImMsgType.SSChatMessageTypeChatGroupNotice;

  Future<Session?> saveOfflineSession(
      String userId, String sessionId, Map<String, int> map) async {
    var faultTime = DateTime.now().millisecondsSinceEpoch;
    var session =
        await DbHelper.getSessionByOwnerId2SessionId(userId, sessionId); // 数据库
    int disturb = 0;
    int sessionTop = session?.sessionTop ?? 0;

    // 单群聊的免打扰
    var ll = await ImCacheData.instance.getSingleGroupDisturbIds();
    if (ll.contains(userId)) {
      disturb = 1;
    } else {
      disturb = 0;
    }

    // 其他通知类型的免打扰 todo

    var dbSession = getSessionFromOfflineSession(userId, disturb);
    if (dbSession != null) {
      if (session != null) {
        if (session.isGroupChat()) {
          dbSession.headerUrl = session.headerUrl;
          dbSession.name = session.name;
        }
        dbSession.sessionHidden = session.sessionHidden == 1 ? 1 : 0;//-1的情况
        dbSession.notReadCount = (dbSession.notReadCount ?? 0) + (session.notReadCount ?? 0);
        if(dbSession.msgContent?.isEmpty == true){
          dbSession.msgContent = session.msgContent;
        }else{
          if(session.msgContent?.contains("有人@你") == true
            && session.sessionType == ConstantImMsgType.SSChatConversationTypeGroupChat
            && (session.notReadCount ?? 0) > 0
            && [ConstantImMsgType.SSChatMessageTypeGroupMoreMemberNotice,
              ConstantImMsgType.SSChatMessageTypeGroupAllMemberNotice].contains(session.msgType)
          ){
            dbSession.msgContent = "<font color='#EC0A39'>[有人@你]</font>${dbSession.msgContent}";
            if(!SessionListController.atList.contains(sessionId)){
              SessionListController.atList.add(sessionId);
            }
          }
        }
      }
      // todo 已注销的用户（赋值为1） ，来源 webSocket
      //....

      if (msgCount > 0) {
        dbSession.sessionHidden = 0;
      }

      dbSession.sessionTop = sessionTop;

      if (map.containsKey(dbSession.sessionId)) {
        var noDisturb = map[dbSession.sessionId];
        if (noDisturb != null) {
          dbSession.noDisturb = noDisturb;
        }
      }

      Session? ss;
      if ((dbSession.msgTime ?? 0) > (session?.msgTime ?? 0)) {

        ss = await _filterGroupAvatar2Name(dbSession);
        //查询是否在聊天页面
        if (CommonCheck.checkIsOnTheChatPage(sessionId)) {
          ss.notReadCount = 0;
          CommonCheck.chatControllerFetchMessage(sessionId);
        }
        if (session?.msgId == dbSession.msgId) {
          //删除了最后一条消息的情况
          var lastMsg = await DbHelper.getLastMsgBySessionId(userId, sessionId );
          if (lastMsg != null) {
            ss.msgContent = lastMsg.alias();
            ss.msgTime = lastMsg.sendTime;
            ss.msgType = lastMsg.msgType;
          }else{
            ss.msgContent = '';
          }
          ss.deleteLastTime = session?.deleteLastTime;
          ss.headerUrl = session?.headerUrl ?? dbSession.headerUrl;
          ss.name = session?.name ?? dbSession.name;
        }else{
          ss.sessionHidden = 0;
        }
        // DbHelper.insertSession(ss);

      }

      int msgTime = session?.msgTime ?? 0; //之前最新消息时间
      int lastTime = dbSession.msgTime ?? 0; //最新的时间
      if (msgTime != lastTime || msgCount > 0) {
        DbHelper.updateLineFaultAgeMessage(userId, sessionId,
            lastTime+1, 1, dbSession.msgContent ?? '');
      }

      if (session == null) {
        ss = await _filterGroupAvatar2Name(dbSession);
        // DbHelper.insertSession(ss);
        //return ss;
      }
      return ss;
      // UserHolder.saveOffLineNotReadCount(dbSession.sessionId, it.msgCount) todo
    } else {
      // 插入新离线会话
      var newSession = getSessionFromNoDB(userId);
      if (msgCount > 0) {
        newSession.sessionHidden = 0;
      }

      if (msgCount > 0) {
        DbHelper.updateLineFaultAgeMessage(userId, newSession.sessionId,
            (newSession.msgTime ?? 0)+1, 1, newSession.msgContent ?? '');
      }

      var ss = await _filterGroupAvatar2Name(newSession);
      // await DbHelper.insertSession(ss);
      return ss;
    }

    return null;
  }
  

  Session? getSessionFromOfflineSession(String userId, int isDisturb) {
    if (isGroupChat()) {
      //TODO 获取群组信息 群组信息 return null ?
    }

    // todo 获取 会话是否展示隐藏
    var sessionHidden = 0;
    var sessionTop = 0;
    var appChatId = '';
    int isDisturb = 0;
    String sendNameTxt = '';

    if (type == ConstantImMsgType.SSChatMessageTypeUndo) {
      msgContent = '撤回了一条消息';
    }
    if (isSingleChat()) {
      appChatId = appUid ?? '';
    } else if (isGroupChat()) {
      appChatId = sessionId ?? '';
      if(!StringUtil.isEmpty(nickname)){
        sendNameTxt = '$nickname:';
      }
      msgContent = '$sendNameTxt$msgContent';
    }
    return Session(sessionId: sessionId ?? '')
      ..uid = userId
      ..sessionTop = sessionTop
      ..sessionHidden = sessionHidden
      ..msgType = type ?? ConstantImMsgType.OffLineSessionMsgType
      ..sessionType = sessionType ?? 1
      ..extend = ''
      ..name = this.nickname
      ..chatStatus = 0
      ..cmdId = ''
      ..headerUrl = avatar
      ..msgId = msgId
      ..msgContent = msgContent
      ..notReadCount = msgCount
      ..msgTime = msgTime ?? 0
      ..noDisturb = isDisturb
      ..appChatId = appChatId;
  }

  Session getSessionFromNoDB(String userId) {
    var headUrl = '';
    var name = '';
    String sendNameTxt = '';

    if (type == ConstantImMsgType.SSChatMessageTypeUndo) {
      msgContent = '撤回了一条消息';
    }
    if (isSingleChat()) {
      headUrl = this.avatar ?? '';
      name = this.nickname ?? '';
    } else if (isGroupChat()) {
      headUrl = 'https://cdn.ddbes.com/IM/GROUP/group_icon4.png';
      name = '群聊';
      if(!StringUtil.isEmpty(nickname)){
        sendNameTxt = '$nickname:';
      }
      msgContent = '$sendNameTxt$msgContent';
    }

    return Session(sessionId: sessionId ?? '')
      ..uid = userId
      ..sessionTop = 0
      ..sessionHidden = 0
      ..msgType = type ?? ConstantImMsgType.OffLineSessionMsgType
      ..sessionType = sessionType ?? 0
      ..extend = ''
      ..name = name
      ..chatStatus = 0
      ..cmdId = ''
      ..headerUrl = headUrl
      ..msgId = msgId
      ..msgContent = msgContent
      ..notReadCount = msgCount
      ..msgTime = msgTime ?? 0
      ..noDisturb = 0
      ..appChatId = appUid;
  }
}

extension ImOfflineNoticeSessionExt on ImOfflineNoticeResp {
  Future<Session?> createSessionFromOfflineNoticeSession(String userId,
      Map disturbMap, List<OrgModel> allOrgs, List<OrgModel> exOrgs) async {
    if (data.isNotBlank()) {
      Session? session;

      var sessionId = NoticeTypeManager.noticeTypeMap[type];
      if (type == ConstantImMsgType.WorkMsgSessionType) {
        sessionId = tag;
      } else if (type == ConstantImMsgType.TeamMsgSessionType) {
        sessionId = ConstantTcpUtil.TCP_TEAM_SESSIONID;
      }

      if (sessionId != null) {
        session =
            await DbHelper.getSessionByOwnerId2SessionId(userId, sessionId);
      } else {
        return null;
      }
      var dbSession = createDbSessionFromOfflineNoticeSession(data, userId);
      if (session != null && dbSession != null) {
        dbSession.notReadCount =
            (dbSession.notReadCount ?? 0) + (session.notReadCount ?? 0);
        dbSession.sessionTop = session.sessionTop;
        dbSession.sessionHidden = session.sessionHidden == 1 ? 1 :0;//-1的情况
        dbSession.noDisturb = session.noDisturb;
      }
      if (dbSession != null) {
        // 这样做的目的是防止 离线接口数据覆盖 免打扰开关的设置
        logger('====disturbMap===$disturbMap');
        // if (disturbMap.containsKey(dbSession.sessionId)) {
        //   dbSession.noDisturb = disturbMap[dbSession.sessionId];
        // }
        DbHelper.insertSession(dbSession);
        // TODO
        int msgTime = session?.msgTime ?? 0; //之前最新消息时间
        int lastTime = dbSession.msgTime ?? 0; //最新的时间
        //有离线的未读数则先把对应通知的表都加断点
        if (msgTime != lastTime || (dbSession.notReadCount ?? 0) > 0) {
          if (dbSession.sessionType == ConstantImMsgType.SystemMsgSessionType ||
              dbSession.sessionType == ConstantImMsgType.TeamMsgSessionType) {
            if (CommonCheck.checkIsOnTheNoticePage(dbSession.sessionId)) {
              CommonCheck.noticeFetchMessage(sessionId);
              dbSession.notReadCount = 0;
            }
            _saveNoticeMessageOfflineFault(
                dbSession, '${dbSession.sessionId}${dbSession.sessionType}');
          } else if (dbSession.sessionType ==
              ConstantImMsgType.WorkMsgSessionType) {
            if (CommonCheck.checkIsOnTheNoticePage(dbSession.sessionId)) {
              dbSession.notReadCount = 0;
            }
            _saveNoticeMessageOfflineFault(dbSession, dbSession.sessionId);
          } else if (dbSession.isNoticeType()) {
            if (CommonCheck.checkIsOnTheNoticePage(dbSession.sessionId)) {
              CommonCheck.noticeRequestUnread(dbSession.sessionId);
            }
            if (dbSession.sessionType ==
                ConstantImMsgType.ApprovalMsgSessionType) {
              if (allOrgs.isNotEmpty) {
                for (var i = 0; i < allOrgs.length; i++) {
                  OrgModel model = allOrgs[i];
                  _saveNoticeMessageOfflineFault(dbSession, model.companyId);
                }
              }
              if (exOrgs.isNotEmpty) {
                for (var i = 0; i < exOrgs.length; i++) {
                  OrgModel model = exOrgs[i];
                  _saveNoticeMessageOfflineFault(dbSession, model.companyId);
                }
              }
            } else {
              if (allOrgs.isNotEmpty) {
                for (var i = 0; i < allOrgs.length; i++) {
                  OrgModel model = allOrgs[i];
                  _saveNoticeMessageOfflineFault(dbSession, model.companyId);
                }
              }
            }
          }
        }
      }
      return dbSession;
    }

    return null;
  }

  //存入断点
  _saveNoticeMessageOfflineFault(Session dbSession, String appChatId) {
    Notice notice = Notice(
        msgId: getUUid(),
        uid: dbSession.uid,
        sessionId: dbSession.sessionId,
        companyId: appChatId,
        msgType: ConstantImMsgType.SSChatMessageTypeOfflineFault,
        msgTime: dbSession.msgTime! + 1);
    DbHelper.insertNotice(notice);
  }

  Session? createDbSessionFromOfflineNoticeSession(
      String? data, String userId) {
    if (!data.isNotBlank()) return null;
    Map<String, dynamic> j = jsonDecode(data!);
    SystemNoticeDataBean? dataBean = SystemNoticeDataBean.fromJson(j);
    var dbSession = Session(sessionId: '')
      ..sessionHidden = 0
      ..uid = userId
      ..msgType =
          dataBean.msgType ?? ConstantImMsgType.OffLineNoticeSessionMsgType
      ..sessionType = type
      ..headerUrl = dataBean.iconUrl ?? ''
      ..msgContent = msgContent;
    logger('-------count is --$count');
    switch (type) {
      case ConstantImMsgType.SystemMsgSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_SYSTEM_SESSIONID;

        break;
      case ConstantImMsgType.WorkMsgSessionType:
        dbSession.name = "[工作通知] ${dataBean?.companyName ?? ''}";
        dbSession.sessionId = tag ?? '';
        dbSession.msgContent =
            "<font color='#107EEB'>[${dataBean?.noticeName}]</font> ${(msgContent ?? '')}";
        dbSession.headerUrl = dataBean?.companyLogo;
        break;
      case ConstantImMsgType.ApprovalMsgSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_APPROVAL_SESSIONID;
        dbSession.msgContent =
            "<font color='#FD973C'>[${dataBean?.companyName}]</font> ${(msgContent ?? '')}";
        dbSession.headerUrl = dataBean?.companyLogo;
        break;
      case ConstantImMsgType.KingdeeMsgSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_KINGDEE_SESSIONID;
        dbSession.msgContent =
            "<font color='#FD973C'>[${dataBean?.companyName}]</font> ${(msgContent ?? '')}";
        break;

      case ConstantImMsgType.TicketMsgSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_TICKET_SESSIONID;
        dbSession.msgContent =
            "<font color='#FD973C'>[${dataBean?.companyName}]</font> ${msgContent ?? ''}";
        break;

      case ConstantImMsgType.TrainMsgSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_TRAIN_SESSIONID;
        dbSession.msgContent =
            "<font color='#FD973C'>[${dataBean?.companyName}]</font> ${(msgContent ?? '')}";
        break;

      case ConstantImMsgType.MatterMsgSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_MATTER_SESSIONID;
        dbSession.msgContent =
            "<font color='#FD973C'>[${dataBean?.companyName}]</font> ${(msgContent ?? '')}";
        break;

      case ConstantImMsgType.TeamMsgSessionType:
        dbSession.name = "合作企业消息";
        dbSession.sessionId = ConstantTcpUtil.TCP_TEAM_SESSIONID;
        break;

      case ConstantImMsgType.GeneralManagementSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_GENER_MANAGEMENT_SESSIONID;
        dbSession.msgContent =
            "<font color='#FD973C'>[${dataBean?.companyName}]</font> ${(msgContent ?? '')}";
        break;
      
      case ConstantImMsgType.RangeParkMsgSessionType:
        dbSession.name = NoticeTypeManager.noticeNameMap[type] ?? '';
        dbSession.sessionId = ConstantTcpUtil.TCP_RANGE_PARK_SESSIONID;
        break;
      case ConstantImMsgType.IDCMsgSessionType:
        dbSession.name = "IDC知识库";
        dbSession.sessionId = ConstantTcpUtil.TCP_IDC_SESSIONID;
    }

    dbSession.msgId = DateTime.now().millisecondsSinceEpoch.toString();
    dbSession.notReadCount = count ?? 0;
    dbSession.msgTime = msgTime ?? 0;
    dbSession.noDisturb = 0;
    dbSession.appChatId = dataBean?.companyId ?? "";
    dbSession.sessionTop = 0;
    dbSession.extend = dataBean?.companyName ?? "";
    if (!dbSession.sessionId.isNotBlank()) {
      return null;
    }
    logger(
        '----返回的dbSession--${dbSession.notReadCount}--${dbSession.sessionId}');
    return dbSession;
  }
}
