import 'dart:convert';

import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/im/convert/in_time_im_convert_to_session.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/request/entity/chat_resp_ext.dart';
import 'package:flutter_mixed/app/im/ui/base/common_check.dart';
import 'package:flutter_mixed/app/im/utils/im_prefix_msg.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import '../constant/ImMsgConstant.dart';
import '../db/entity/message.dart';
import '../db/entity/session.dart';
import '../model/im_session_info.dart';
import '../proto_model/Msg.pb.dart';
import '../request/entity/offline_notice_msg.dart';
import '../ui/chat/message/bot_parse.dart';
import '../utils/im_global_util.dart';
import 'im_msg_ext.dart';

extension ExtExt on ExtMsg {
  String getMessageText(int type) {
    var msgText = '';
    if (type == ConstantImMsgType.SSChatMessageTypeBlacklistChange) {
      // 黑名单变化 , 机器人 ， 转发记录 todo
      msgText = this.ext1;
    } else if (type == ConstantImMsgType.SSChatMessageTypeQuote) {
      // 引用，
      var msg = Message.fromJson(jsonDecode(ext1));
      msgText = msg.quoteText ?? '';
    } else if (type == ConstantImMsgType.SSChatMessageTypeRobot) {
      // 机器人
      try {
        var botEntity = ImBotExtEntity.fromJson(jsonDecode(ext1));
        msgText = botEntity.context ?? '';
      } catch (e) {
        var botEntity = ClientNoticeMsg.fromJson(jsonDecode(ext1));
        msgText = botEntity.context ?? '';
      }
    } else if (type == ConstantImMsgType.SSChatMessageTypeForwardRecord) {
      try {
        var record = MsgUIRecord.fromJson(jsonDecode(ext1));
        if (record.sessionType == 1) {
          msgText = ImPrefixMsg.singleRecordString;
        } else {
          msgText = ImPrefixMsg.groupRecordString;
        }
      } catch (e) {}
    } else {

       try{
         var extObj =  jsonDecode(ext1);
         msgText = extObj['text'];
       }catch(e){}

    }
    return msgText;
  }
}

// 即时收到的单聊proto buffer 实体扩展
extension C2CMsgExt on C2CMsg {
  /// 来了新消息后
  Future<Session?> createSession(ImCommonData imCommonData, String? text) async {
    //text 解析后的text
    var userId = await ImGlobalUtil.currentUserId();
    var sender = imCommonData.senderInfo;
    bool isSelfSend = imCommonData.senderInfo.userId == userId;

    var sessionId = isSelfSend ? receiver : imCommonData.senderInfo.imUserId;
    var dbSession =
        await DbHelper.getSessionByOwnerId2SessionId(userId, sessionId);
    var sessionTop = dbSession?.sessionTop ?? 0;
    var isDisturb = dbSession?.noDisturb ?? 0;
    var avatar = isSelfSend ? dbSession?.headerUrl ?? '' : sender.avatar;
    var name = isSelfSend ? dbSession?.name ?? '' : sender.nickname;
    var appChatId = isSelfSend ? dbSession?.appChatId ?? '' : imCommonData.senderInfo.userId;
    int unReadCount = 0;
    if (dbSession != null) {
      unReadCount = isSelfSend ? (dbSession.notReadCount ?? 0) : (dbSession.notReadCount ?? 0) + 1;
    } else {
      unReadCount = isSelfSend ? 0 : 1;
      _createFaultMessage(userId,sessionId,imCommonData.msgTime,text ?? imCommonData.msgContent);
    }
    if (CommonCheck.checkIsOnTheChatPage(sessionId)) {
      //在当前页面
      unReadCount = 0;
    }
    if (StringUtil.isEmpty(avatar) || StringUtil.isEmpty(name) || StringUtil.isEmpty(appChatId)) {
      dynamic receiveInfo = await BaseInfo.getUserInfoWithImIds([sessionId]);
      if (receiveInfo is List) {
        dynamic userModel = receiveInfo.first;
        if (userModel is ImSessionInfo) {
          avatar = userModel.avatar ?? '';
          name = userModel.name ?? '';
          appChatId = userModel.appChatId ?? '';
        }
      }
    }
    var msgId = imCommonData.msgId;
    var msgTime = imCommonData.msgTime;
    var msgContent = text ?? imCommonData.msgContent;
    var msgType = imCommonData.type;
    if (msgType == ConstantImMsgType.SSChatMessageTypeForwardRecord) {
      msgContent = ext.getMessageText(msgType);
    } else if (isExt()) {
      msgContent = ext.getMessageText(msgType);
    }

    return createNewSession(
        sessionId,
        avatar,
        name,
        1,
        msgType,
        msgContent,
        msgId,
        msgTime,
        unReadCount,
        isDisturb,
        false,
        appChatId,
        sessionTop);
  }

  //无session 强行创建断层，场景：长时间不聊天，换设备登录，在线收到消息 无断层会拉不到之前的消息
  _createFaultMessage(String userId,String sessionId,int msgTime,String? msgContent){
    DbHelper.updateLineFaultAgeMessage(userId, sessionId,
            msgTime-1, 1, msgContent ?? '');
  }
}
