import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/notice.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../common/api/Define.dart';
import '../../modules/contact/model/org/org_model.dart';
import '../../utils/storage.dart';
import '../constant/ImMsgConstant.dart';
import '../db/entity/session.dart';
import '../request/entity/offline_notice_session_reqbody.dart';

extension MapExt on Map {
  // 所有公司
  CompanyEntity getCompanyEntity() {
    Map dataDic = this;
    List externalCompanies = [];
    if (dataDic['externalCompanies'] != null) {
      externalCompanies = dataDic['externalCompanies'];
    }
    List companies = [];
    if (dataDic['companies'] != null) {
      companies = dataDic['companies'];
    }
    List<OrgModel> createList = [];
    List<OrgModel> joinList = [];
    List<OrgModel> externalList = [];
    if (companies.isNotEmpty) {
      for (var i = 0; i < companies.length; i++) {
        OrgModel model = OrgModel.fromJson(companies[i]);
        if (model.deptId == '0') {
          createList.add(model);
        } else {
          joinList.add(model);
        }
      }
    }

    if (externalCompanies.isNotEmpty) {
      for (var i = 0; i < externalCompanies.length; i++) {
        Map<String, dynamic> extMap = externalCompanies[i];
        OrgModel model = OrgModel.fromJson(extMap);
        model.isExternal = true;
        externalList.add(model);
      }
    }

    return CompanyEntity(createList, joinList, externalList);
  }
}

class CompanyEntity {
  List<OrgModel> createdList = [];
  List<OrgModel> joinedList = [];
  List<OrgModel> externalList = [];

  CompanyEntity(this.createdList, this.joinedList, this.externalList);
}

Future<OfflineNoticeSessionReqBody> createOfflineNoticeReqBody(
    List<Session> dbSessionList) async {
  var orgList = await UserDefault.getData(Define.ORGLIST);
  if(orgList == null) {
    return OfflineNoticeSessionReqBody(
      disassembleTag: [], list: []);
  };
  Map dataDic = orgList;
  CompanyEntity companyEntity = dataDic.getCompanyEntity();

  List<OrgModel> allOrgs = companyEntity.createdList + companyEntity.joinedList;
  List<OrgModel> exOrgs = companyEntity.externalList;
  List<String> companyIdList = allOrgs.map((e) => e.companyId).toList();

  List<int> disassembleTagList = [];
  List<ListItem> noticeDataList = [];
  if (allOrgs.isNotEmpty) {
    disassembleTagList.add(ConstantImMsgType.WorkMsgSessionType);
  }
  if (exOrgs.isNotEmpty) {
    noticeDataList.add(ListItem(type: ConstantImMsgType.TeamMsgSessionType));
  }

  _addSessionNoticeReqMstTimeAndType(dbSessionList, noticeDataList);

  _addSessionNoticeReqType(dbSessionList, noticeDataList);

  _addSessionWorkReq(companyIdList, dbSessionList, noticeDataList);
  OfflineNoticeSessionReqBody reqBody = OfflineNoticeSessionReqBody(
      disassembleTag: disassembleTagList, list: noticeDataList);

  return reqBody;
}

//获取除当前公司外的body-审批未读
Future<OfflineNoticeSessionReqBody> createApproveUnreadReqBody(
    String companyId, Session session) async {
  int type = session.sessionType!;
  Map dataDic = await UserDefault.getData(Define.ORGLIST);
  CompanyEntity companyEntity = dataDic.getCompanyEntity();

  List<OrgModel> allOrgs = companyEntity.createdList +
      companyEntity.joinedList +
      companyEntity.externalList;
  List<String> companyIdList = allOrgs.map((e) => e.companyId).toList();
  List<ListItem> noticeDataList = [];
  for (var i = 0; i < companyIdList.length; i++) {
    String orgId = companyIdList[i];
    if (orgId != companyId) {
      List<Notice> lastList = await DbHelper.getLastNoticeDataWithCompanyId(
          session.uid!,
          session.sessionId,
          orgId,
          ConstantImMsgType.SSChatMessageTypeOfflineFault,
          1);
      ListItem item = ListItem(tag: orgId, type: type);
      if (lastList.isNotEmpty) {
        Notice lastNotice = lastList.last;
        item.timeStart = lastNotice.msgTime;
      }
      noticeDataList.add(item);
    }
  }

  OfflineNoticeSessionReqBody reqBody =
      OfflineNoticeSessionReqBody(list: noticeDataList);

  return reqBody;
}

_addSessionNoticeReqType(
    List<Session> dbSessionList, List<ListItem> noticeDataList) {
  NoticeTypeManager.noticeTypeMap.keys.forEach((noticeType) {
    var noticeList = dbSessionList
        .where((element) => element.sessionType == noticeType)
        .toList();
    if (noticeList.isEmpty) {
      noticeDataList.add(ListItem(type: noticeType));
    }
  });
}

_addSessionNoticeReqMstTimeAndType(
    List<Session> dbSessionList, List<ListItem> noticeDataList) {
  dbSessionList.forEach((element) {
    if (NoticeTypeManager.noticeTypeMap.keys.contains(element.sessionType) ||
        element.sessionType == ConstantImMsgType.TeamMsgSessionType) {
      ListItem listItem =
          ListItem(timeStart: element.msgTime, type: element.sessionType);
      noticeDataList.add(listItem);
    }
  });
}

_addSessionWorkReq(List<String> companyIdList, List<Session> dbSessionList,
    List<ListItem> noticeDataList) {
  companyIdList.forEach((element) {
    ListItem listItem =
        ListItem(tag: element, type: ConstantImMsgType.WorkMsgSessionType);
    noticeDataList.add(listItem);
  });
  dbSessionList.forEach((element) {
    noticeDataList.forEach((item) {
      if (element.sessionId == item.tag) {
        item.timeStart = element.msgTime;
      }
    });
  });
}
