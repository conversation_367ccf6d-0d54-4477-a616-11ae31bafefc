

import 'package:flutter/material.dart';

extension ColorExt on String? {

  // 将 字符串 ‘#ff000000’ 转换为16进制
  Color toColor({Color? defaultColor}) {
    try{
      if(this == null) return defaultColor ?? Colors.transparent;
      var c = this;
      if(this?.contains("#") == true){
        c = this!.replaceAll("#", "");
      }
      return Color(int.parse(c ?? '',radix:16)|0xFF000000);
    }catch(e){
      return defaultColor ?? Colors.transparent;
    }

  }

}