import 'dart:async';
import 'dart:convert';

import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/string_extension.dart';
import 'package:flutter_mixed/app/im/request/entity/offline_notice_msg.dart';
import 'package:flutter_mixed/app/utils/http.dart';
import 'package:get/get.dart';

import '../../common/channel/channel.dart';
import '../click_card/click_card_jump.dart';
import '../click_card/method_type.dart';

extension NoticeButtonExt on NoticeButton {
  //处理按钮
  Future<int> dealNoticeButtonWith(String companyId) async {
    return await httpRequestWithType(companyId);
  }

  Future<dynamic> httpRequestWithType(companyId) async {
    Map<String, dynamic>? jsonData;
    if (body != null) {
      jsonData = json.decode(body!);
    }
    Completer completer = Completer<int>();
    switch (methodType) {
      case NetWorkMethodType.DDNetworkMethodTypeGET:
        if (!apiUrl.isNotBlank()) {
          _dealComplete(completer, 0);
        } else {
          if (apiUrl!.startsWith('/')) {
            apiUrl = apiUrl!.substring(1, apiUrl!.length);
          }
          await DioUtil().get(apiUrl!, body == null ? null : jsonData, true,
              () {
            _dealComplete(completer, 0);
          }).then((data) {
            if (data == null) {
              _dealComplete(completer, 0);
            } else {
              if (data!['code'] == 1) {
                _dealComplete(completer, 1);
              } else {
                toast(data['msg'] ?? '');
                _dealComplete(completer, 0);
              }
            }
          });
        }
        break;
      case NetWorkMethodType.DDNetworkMethodTypePOST:
        if (!apiUrl.isNotBlank()) {
          _dealComplete(completer, 0);
        } else {
          if (apiUrl!.startsWith('/')) {
            apiUrl = apiUrl!.substring(1, apiUrl!.length);
          }
          await DioUtil().post(apiUrl!, body == null ? null : jsonData, true,
              () {
            _dealComplete(completer, 0);
          }).then((data) {
            if (data == null) {
              _dealComplete(completer, 0);
            } else {
              if (data!['code'] == 1) {
                _dealComplete(completer, 1);
              } else {
                toast(data['msg'] ?? '');
                _dealComplete(completer, 0);
              }
            }
          });
        }
        break;
      case NetWorkMethodType.DDNetworkMethodTypePUT:
        if (!apiUrl.isNotBlank()) {
          _dealComplete(completer, 0);
        } else {
          if (apiUrl!.startsWith('/')) {
            apiUrl = apiUrl!.substring(1, apiUrl!.length);
          }
          await DioUtil().put(apiUrl!, body == null ? null : jsonData, true,
              () {
            _dealComplete(completer, 0);
          }).then((data) {
            if (data == null) {
              _dealComplete(completer, 0);
            } else {
              if (data!['code'] == 1) {
                _dealComplete(completer, 1);
              } else {
                toast(data['msg'] ?? '');
                _dealComplete(completer, 0);
              }
            }
          });
        }

        break;
      case NetWorkMethodType.DDNetworkMethodTypeDELETE:
        if (!apiUrl.isNotBlank()) {
          _dealComplete(completer, 0);
        } else {
          if (apiUrl!.startsWith('/')) {
            apiUrl = apiUrl!.substring(1, apiUrl!.length);
          }
          await DioUtil().delete(apiUrl!, body == null ? null : jsonData, true,
              () {
            _dealComplete(completer, 0);
          }).then((data) {
            if (data == null) {
              _dealComplete(completer, 0);
            } else {
              if (data!['code'] == 1) {
                _dealComplete(completer, 1);
              } else {
                toast(data['msg'] ?? '');
                _dealComplete(completer, 0);
              }
            }
          });
        }
        break;
      case NetWorkMethodType.DDNetworkMethodTypePATCH:
        if (!apiUrl.isNotBlank()) {
          _dealComplete(completer, 0);
        } else {
          if (apiUrl!.startsWith('/')) {
            apiUrl = apiUrl!.substring(1, apiUrl!.length);
          }
          await DioUtil().patch(apiUrl!, body == null ? null : jsonData, true,
              () {
            _dealComplete(completer, 0);
          }).then((data) {
            if (data == null) {
              _dealComplete(completer, 0);
            } else {
              if (data!['code'] == 1) {
                _dealComplete(completer, 1);
              } else {
                toast(data['msg'] ?? '');
                _dealComplete(completer, 0);
              }
            }
          });
        }
        break;
      case NetWorkMethodType.DDNetworkMethodTypeWeb:
        if (!apiUrl.isNotBlank()) {
          _dealComplete(completer, 0);
        } else {
          ClickCardJump.webJump(apiUrl ?? '', companyId);
          _dealComplete(completer, 1);
        }

        break;
      case NetWorkMethodType.DDNetworkMethodTypeFlutter:
        if (!apiUrl.isNotBlank()) {
          _dealComplete(completer, 0);
        } else {
          Get.toNamed(apiUrl!, arguments: jsonData, preventDuplicates: false);
          _dealComplete(completer, 1);
        }
        break;
      default:
        return _dealComplete(completer, 0);
    }
    return completer.future;
  }

  _dealComplete(Completer completer, int result) {
    if (!completer.isCompleted) {
      completer.complete(result);
    }
  }
}
