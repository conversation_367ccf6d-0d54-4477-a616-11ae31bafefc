
import 'dart:async';

import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/db/log/req_log.dart';
import 'package:flutter_mixed/app/db/org/db_org_dao.dart';
import 'package:flutter_mixed/app/db/org/db_org_model.dart';
import 'package:flutter_mixed/app/db/user/db_user_dao.dart';
import 'package:flutter_mixed/app/db/user/db_user_model.dart';
import 'package:sqflite/sqflite.dart' as sqflite;

import 'log/req_log_dao.dart';

part 'app_database.g.dart';   // build之前收到引入 part， 名字必须和原db名字一致

@Database(version: 2, entities: [ReqLog,DBOrgModel,DBUserModel])
abstract class AppDataBase extends FloorDatabase {

  ReqLogDao get getReqLogDao;
  DBOrgDao get getDBOrgDao;
  DBUserModelDao get getDBUserModelDao;
}