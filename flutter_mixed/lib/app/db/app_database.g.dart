// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// **************************************************************************
// FloorGenerator
// **************************************************************************

abstract class $AppDataBaseBuilderContract {
  /// Adds migrations to the builder.
  $AppDataBaseBuilderContract addMigrations(List<Migration> migrations);

  /// Adds a database [Callback] to the builder.
  $AppDataBaseBuilderContract addCallback(Callback callback);

  /// Creates the database and initializes it.
  Future<AppDataBase> build();
}

// ignore: avoid_classes_with_only_static_members
class $FloorAppDataBase {
  /// Creates a database builder for a persistent database.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDataBaseBuilderContract databaseBuilder(String name) =>
      _$AppDataBaseBuilder(name);

  /// Creates a database builder for an in memory database.
  /// Information stored in an in memory database disappears when the process is killed.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDataBaseBuilderContract inMemoryDatabaseBuilder() =>
      _$AppDataBaseBuilder(null);
}

class _$AppDataBaseBuilder implements $AppDataBaseBuilderContract {
  _$AppDataBaseBuilder(this.name);

  final String? name;

  final List<Migration> _migrations = [];

  Callback? _callback;

  @override
  $AppDataBaseBuilderContract addMigrations(List<Migration> migrations) {
    _migrations.addAll(migrations);
    return this;
  }

  @override
  $AppDataBaseBuilderContract addCallback(Callback callback) {
    _callback = callback;
    return this;
  }

  @override
  Future<AppDataBase> build() async {
    final path = name != null
        ? await sqfliteDatabaseFactory.getDatabasePath(name!)
        : ':memory:';
    final database = _$AppDataBase();
    database.database = await database.open(
      path,
      _migrations,
      _callback,
    );
    return database;
  }
}

class _$AppDataBase extends AppDataBase {
  _$AppDataBase([StreamController<String>? listener]) {
    changeListener = listener ?? StreamController<String>.broadcast();
  }

  ReqLogDao? _getReqLogDaoInstance;

  DBOrgDao? _getDBOrgDaoInstance;

  DBUserModelDao? _getDBUserModelDaoInstance;

  Future<sqflite.Database> open(
    String path,
    List<Migration> migrations, [
    Callback? callback,
  ]) async {
    final databaseOptions = sqflite.OpenDatabaseOptions(
      version: 2,
      onConfigure: (database) async {
        await database.execute('PRAGMA foreign_keys = ON');
        await callback?.onConfigure?.call(database);
      },
      onOpen: (database) async {
        await callback?.onOpen?.call(database);
      },
      onUpgrade: (database, startVersion, endVersion) async {
        await MigrationAdapter.runMigrations(
            database, startVersion, endVersion, migrations);

        await callback?.onUpgrade?.call(database, startVersion, endVersion);
      },
      onCreate: (database, version) async {
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `ReqLog` (`id` INTEGER, `url` TEXT, `network` TEXT, `method` TEXT, `code` INTEGER, `startTime` INTEGER, `endTime` INTEGER, `userId` TEXT, `reportTime` INTEGER, `time` TEXT, `os` TEXT, `msg` TEXT, `osVersion` TEXT, `deviceId` TEXT, `version` TEXT, `extra` TEXT, PRIMARY KEY (`id`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `DBOrgModel` (`companyId` TEXT NOT NULL, `uid` TEXT NOT NULL, `users` TEXT, `deptId` TEXT, `name` TEXT, `logo` TEXT, `haveApprove` INTEGER, `rejectInvitation` INTEGER, `rejectJoin` INTEGER, `isJoin` INTEGER, `power` TEXT, `type` TEXT, `scale` TEXT, `industry` TEXT, `linkManPhone` TEXT, `linkManMail` TEXT, `officialWebsite` TEXT, `profile` TEXT, `content` TEXT, `noticeId` TEXT, `corgId` TEXT, PRIMARY KEY (`companyId`, `uid`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `DBUserModel` (`uid` TEXT NOT NULL, `userId` TEXT NOT NULL, `positionId` TEXT, `level` INTEGER, `avatar` TEXT, `initial` TEXT, `imId` TEXT, `remark` TEXT, `userName` TEXT, `mobile` TEXT, `type` INTEGER, PRIMARY KEY (`uid`, `userId`))');

        await callback?.onCreate?.call(database, version);
      },
    );
    return sqfliteDatabaseFactory.openDatabase(path, options: databaseOptions);
  }

  @override
  ReqLogDao get getReqLogDao {
    return _getReqLogDaoInstance ??= _$ReqLogDao(database, changeListener);
  }

  @override
  DBOrgDao get getDBOrgDao {
    return _getDBOrgDaoInstance ??= _$DBOrgDao(database, changeListener);
  }

  @override
  DBUserModelDao get getDBUserModelDao {
    return _getDBUserModelDaoInstance ??=
        _$DBUserModelDao(database, changeListener);
  }
}

class _$ReqLogDao extends ReqLogDao {
  _$ReqLogDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _reqLogInsertionAdapter = InsertionAdapter(
            database,
            'ReqLog',
            (ReqLog item) => <String, Object?>{
                  'id': item.id,
                  'url': item.url,
                  'network': item.network,
                  'method': item.method,
                  'code': item.code,
                  'startTime': item.startTime,
                  'endTime': item.endTime,
                  'userId': item.userId,
                  'reportTime': item.reportTime,
                  'time': item.time,
                  'os': item.os,
                  'msg': item.msg,
                  'osVersion': item.osVersion,
                  'deviceId': item.deviceId,
                  'version': item.version,
                  'extra': item.extra
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<ReqLog> _reqLogInsertionAdapter;

  @override
  Future<List<ReqLog>> findAll() async {
    return _queryAdapter.queryList('SELECT * FROM ReqLog',
        mapper: (Map<String, Object?> row) => ReqLog(row['url'] as String?,
            network: row['network'] as String?,
            method: row['method'] as String?,
            code: row['code'] as int?,
            startTime: row['startTime'] as int?,
            endTime: row['endTime'] as int?,
            userId: row['userId'] as String?,
            reportTime: row['reportTime'] as int?,
            time: row['time'] as String?,
            os: row['os'] as String?,
            msg: row['msg'] as String?,
            osVersion: row['osVersion'] as String?,
            deviceId: row['deviceId'] as String?,
            version: row['version'] as String?,
            extra: row['extra'] as String?));
  }

  @override
  Future<List<ReqLog>> findUnUploadLogs(
    int startTime,
    int endTime,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM ReqLog WHERE startTime > ?1 AND startTime < ?2',
        mapper: (Map<String, Object?> row) => ReqLog(row['url'] as String?,
            network: row['network'] as String?,
            method: row['method'] as String?,
            code: row['code'] as int?,
            startTime: row['startTime'] as int?,
            endTime: row['endTime'] as int?,
            userId: row['userId'] as String?,
            reportTime: row['reportTime'] as int?,
            time: row['time'] as String?,
            os: row['os'] as String?,
            msg: row['msg'] as String?,
            osVersion: row['osVersion'] as String?,
            deviceId: row['deviceId'] as String?,
            version: row['version'] as String?,
            extra: row['extra'] as String?),
        arguments: [startTime, endTime]);
  }

  @override
  Future<void> clear(
    int startTime,
    int endTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'delete FROM ReqLog WHERE startTime > ?1 AND startTime < ?2',
        arguments: [startTime, endTime]);
  }

  @override
  Future<void> insertList(List<ReqLog> logs) async {
    await _reqLogInsertionAdapter.insertList(logs, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertDevice(ReqLog reqLog) async {
    await _reqLogInsertionAdapter.insert(reqLog, OnConflictStrategy.replace);
  }
}

class _$DBOrgDao extends DBOrgDao {
  _$DBOrgDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _dBOrgModelInsertionAdapter = InsertionAdapter(
            database,
            'DBOrgModel',
            (DBOrgModel item) => <String, Object?>{
                  'companyId': item.companyId,
                  'uid': item.uid,
                  'users': item.users,
                  'deptId': item.deptId,
                  'name': item.name,
                  'logo': item.logo,
                  'haveApprove': item.haveApprove,
                  'rejectInvitation': item.rejectInvitation,
                  'rejectJoin': item.rejectJoin,
                  'isJoin': item.isJoin,
                  'power': item.power,
                  'type': item.type,
                  'scale': item.scale,
                  'industry': item.industry,
                  'linkManPhone': item.linkManPhone,
                  'linkManMail': item.linkManMail,
                  'officialWebsite': item.officialWebsite,
                  'profile': item.profile,
                  'content': item.content,
                  'noticeId': item.noticeId,
                  'corgId': item.corgId
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<DBOrgModel> _dBOrgModelInsertionAdapter;

  @override
  Future<List<DBOrgModel>> getAllLocalOrg(String uid) async {
    return _queryAdapter.queryList('SELECT * FROM DBOrgModel WHERE uid = ?1',
        mapper: (Map<String, Object?> row) => DBOrgModel(
            row['companyId'] as String, row['uid'] as String,
            users: row['users'] as String?,
            deptId: row['deptId'] as String?,
            name: row['name'] as String?,
            logo: row['logo'] as String?,
            haveApprove: row['haveApprove'] as int?,
            rejectInvitation: row['rejectInvitation'] as int?,
            rejectJoin: row['rejectJoin'] as int?,
            isJoin: row['isJoin'] as int?,
            power: row['power'] as String?,
            type: row['type'] as String?,
            scale: row['scale'] as String?,
            industry: row['industry'] as String?,
            linkManPhone: row['linkManPhone'] as String?,
            linkManMail: row['linkManMail'] as String?,
            officialWebsite: row['officialWebsite'] as String?,
            profile: row['profile'] as String?,
            content: row['content'] as String?,
            noticeId: row['noticeId'] as String?,
            corgId: row['corgId'] as String?),
        arguments: [uid]);
  }

  @override
  Future<void> deleteLocalOrg(
    String uid,
    String companyId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM DBOrgModel WHERE uid = ?1 AND companyId = ?2',
        arguments: [uid, companyId]);
  }

  @override
  Future<void> clearLocalOrg(String uid) async {
    await _queryAdapter.queryNoReturn('DELETE FROM DBOrgModel WHERE uid = ?1',
        arguments: [uid]);
  }

  @override
  Future<void> updateUsers(
    String uid,
    String companyId,
    String users,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE DBOrgModel SET users = ?3 WHERE uid = ?1 AND companyId = ?2',
        arguments: [uid, companyId, users]);
  }

  @override
  Future<void> insertList(List<DBOrgModel> models) async {
    await _dBOrgModelInsertionAdapter.insertList(
        models, OnConflictStrategy.replace);
  }
}

class _$DBUserModelDao extends DBUserModelDao {
  _$DBUserModelDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _dBUserModelInsertionAdapter = InsertionAdapter(
            database,
            'DBUserModel',
            (DBUserModel item) => <String, Object?>{
                  'uid': item.uid,
                  'userId': item.userId,
                  'positionId': item.positionId,
                  'level': item.level,
                  'avatar': item.avatar,
                  'initial': item.initial,
                  'imId': item.imId,
                  'remark': item.remark,
                  'userName': item.userName,
                  'mobile': item.mobile,
                  'type': item.type
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<DBUserModel> _dBUserModelInsertionAdapter;

  @override
  Future<List<DBUserModel>> getAllLocalUser(String uid) async {
    return _queryAdapter.queryList('SELECT * FROM DBUserModel WHERE uid = ?1',
        mapper: (Map<String, Object?> row) => DBUserModel(
            row['uid'] as String, row['userId'] as String,
            positionId: row['positionId'] as String?,
            level: row['level'] as int?,
            avatar: row['avatar'] as String?,
            initial: row['initial'] as String?,
            imId: row['imId'] as String?,
            remark: row['remark'] as String?,
            userName: row['userName'] as String?,
            mobile: row['mobile'] as String?,
            type: row['type'] as int?),
        arguments: [uid]);
  }

  @override
  Future<void> deleteLocalUser(
    String uid,
    String userId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'DELETE FROM DBUserModel WHERE uid = ?1 AND userId = ?2',
        arguments: [uid, userId]);
  }

  @override
  Future<void> clearLocalUser(String uid) async {
    await _queryAdapter.queryNoReturn('DELETE FROM DBUserModel WHERE uid = ?1',
        arguments: [uid]);
  }

  @override
  Future<void> insertList(List<DBUserModel> models) async {
    await _dBUserModelInsertionAdapter.insertList(
        models, OnConflictStrategy.replace);
  }
}
