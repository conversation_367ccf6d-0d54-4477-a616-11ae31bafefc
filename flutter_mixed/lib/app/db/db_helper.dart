import 'dart:async';

import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/db/log/req_log.dart';
import 'package:flutter_mixed/app/db/org/db_org_model.dart';
import 'package:flutter_mixed/app/db/org/org_model_ext.dart';
import 'package:flutter_mixed/app/db/user/db_user_model.dart';

import 'app_database.dart';
import 'db_constant.dart';

final appDatabase = $FloorAppDataBase
    .databaseBuilder(APP_DATABASE_NAME)
    .addMigrations([migration1to2]).build();
//数据库版本2 添加公司表、人员表
final migration1to2 = Migration(1, 2, (database) async {
  await database.execute(
      'CREATE TABLE IF NOT EXISTS `DBOrgModel` (`companyId` TEXT NOT NULL, `uid` TEXT NOT NULL, `users` TEXT, `deptId` TEXT, `name` TEXT, `logo` TEXT, `haveApprove` INTEGER, `rejectInvitation` INTEGER, `rejectJoin` INTEGER, `isJoin` INTEGER, `power` TEXT, `type` TEXT, `scale` TEXT, `industry` TEXT, `linkManPhone` TEXT, `linkManMail` TEXT, `officialWebsite` TEXT, `profile` TEXT, `content` TEXT, `noticeId` TEXT, `corgId` TEXT, PRIMARY KEY (`companyId`, `uid`))');
  await database.execute(
      'CREATE TABLE IF NOT EXISTS `DBUserModel` (`uid` TEXT NOT NULL, `userId` TEXT NOT NULL, `positionId` TEXT, `level` INTEGER, `avatar` TEXT, `initial` TEXT, `imId` TEXT, `remark` TEXT, `userName` TEXT, `mobile` TEXT, `type` INTEGER, PRIMARY KEY (`uid`, `userId`))');
});

class DBHelper {
  static insertReqLog(ReqLog reqLog) async {
    final db = await appDatabase;
    var r = await db.getReqLogDao.insertDevice(reqLog);
    return r;
  }

  static Future<List<ReqLog>> findAllReqLogs() async {
    final db = await appDatabase;
    var list = await db.getReqLogDao.findAll();
    return list;
  }

  static Future<List<ReqLog>> findReqLogsByStartTime(
      int startTime, int endTime) async {
    final db = await appDatabase;
    var list = await db.getReqLogDao.findUnUploadLogs(startTime, endTime);
    return list;
  }

  static Future<void> delReqLogsByStartTime(int startTime, int endTime) async {
    final db = await appDatabase;
    var result = await db.getReqLogDao.clear(startTime, endTime);
    return result;
  }

  //====================公司数据=====
  //获取所有公司数据
  static Future<List<DBOrgModel>> getAllLocalOrg(String uid) async {
    final db = await appDatabase;
    var list = await db.getDBOrgDao.getAllLocalOrg(uid);
    return list;
  }

  //插入数据
  static insertOrg(List<DBOrgModel> models) async {
    final db = await appDatabase;
    var r = await db.getDBOrgDao.insertList(models);
    return r;
  }

  //更新users
  static Future<void> updateUsers(
      String uid, String companyId, String users) async {
    final db = await appDatabase;
    var result = await db.getDBOrgDao.updateUsers(uid, companyId, users);
    return result;
  }

  //删除此公司数据
  static Future<void> deleteLocalOrg(String uid, String companyId) async {
    final db = await appDatabase;
    var result = await db.getDBOrgDao.deleteLocalOrg(uid, companyId);
    return result;
  }

  //删除所有公司数据
  static Future<void> clearLocalOrg(String uid) async {
    final db = await appDatabase;
    var result = await db.getDBOrgDao.clearLocalOrg(uid);
    return result;
  }

  //=========================人员数据====================
  //获取所有人员数据
  static Future<List<DBUserModel>> getAllLocalUser(String uid) async {
    final db = await appDatabase;
    var list = await db.getDBUserModelDao.getAllLocalUser(uid);
    return list;
  }

  //插入数据
  static insertMemberList(List<DBUserModel> models) async {
    final db = await appDatabase;
    var r = await db.getDBUserModelDao.insertList(models);
    return r;
  }

  //删除对应人员数据
  static Future<void> deleteLocalUser(String uid, String userId) async {
    final db = await appDatabase;
    var result = await db.getDBUserModelDao.deleteLocalUser(uid, userId);
    return result;
  }

  //删除所有人员
  static Future<void> clearLocalUser(String uid) async {
    final db = await appDatabase;
    var result = await db.getDBUserModelDao.clearLocalUser(uid);
    return result;
  }
}
