import 'package:floor/floor.dart';

@Entity(primaryKeys: ['userId', 'uid'])
class DBUserModel {
  String uid = ''; //本人userId
  String userId = '';
  String? positionId = '';
  int? level = 0;
  String? avatar = '';
  String? initial = '';
  String? imId = '';
  String? remark = '';
  String? userName = '';
  String? mobile = '';
  int? type = 0; //0普通 1创建者 -1超级管理员 2负责人

  DBUserModel(this.uid, this.userId,
      {this.positionId,
      this.level,
      this.avatar,
      this.initial,
      this.imId,
      this.remark,
      this.userName,
      this.mobile,
      this.type});

  DBUserModel.fromJson(Map<String, dynamic> json) {
    uid = json['uid'] ?? '';
    userId = json['userId'] ?? "";
    if (json['userName'] == null) {
      userName = json['name'] ?? '';
    } else {
      userName = json['userName'];
    }
    if (json['avatar'] == null) {
      avatar = json['headimg'] ?? '';
    } else {
      avatar = json['avatar'];
    }
    positionId = json['positionId'] ?? '';
    level = json['level'] ?? 0;
    initial = json['initial'] ?? '';
    imId = json['imId'] ?? '';
    remark = json['remark'] ?? '';
    type = json['type'] ?? 0;
    mobile = json['mobile'] ?? '';
  }

  Map<String, dynamic> toJson() => {
        'userName': userName,
        'avatar': avatar,
        'userId': userId,
        'positionId': positionId,
        'level': level,
        'initial': initial,
        'imId': imId,
        'remark': remark,
        'type': type,
        'mobile': mobile
      };
}
