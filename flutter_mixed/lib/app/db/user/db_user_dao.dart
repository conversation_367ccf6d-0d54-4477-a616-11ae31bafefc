import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/db/user/db_user_model.dart';

@dao
abstract class DBUserModelDao {
  @Query('SELECT * FROM DBUserModel WHERE uid = :uid')
  Future<List<DBUserModel>> getAllLocalUser(String uid);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<DBUserModel> models);

  @Query('DELETE FROM DBUserModel WHERE uid = :uid AND userId = :userId')
  Future<void> deleteLocalUser(String uid, String userId);

  @Query('DELETE FROM DBUserModel WHERE uid = :uid')
  Future<void> clearLocalUser(String uid);
}
