import 'package:floor/floor.dart';

@Entity(primaryKeys: ['companyId', 'uid'])
class DBOrgModel {
  String companyId = '';
  String uid = '';

  String? users = ''; //userId集合

  String? deptId = '';
  String? name = '';
  String? logo = '';
  int? haveApprove = 0;
  int? rejectInvitation = 0;
  int? rejectJoin = 0;
  int? isJoin = 1;

  String? power = '';
  String? type;
  String? scale;
  String? industry;
  String? linkManPhone;
  String? linkManMail;
  String? officialWebsite;
  String? profile;
  String? content = '';
  String? noticeId = '0';

  String? corgId = '';

  DBOrgModel(this.companyId, this.uid,
      {this.users,
      this.deptId,
      this.name,
      this.logo,
      this.haveApprove,
      this.rejectInvitation,
      this.rejectJoin,
      this.isJoin,
      this.power,
      this.type,
      this.scale,
      this.industry,
      this.linkManPhone,
      this.linkManMail,
      this.officialWebsite,
      this.profile,
      this.content,
      this.noticeId,
      this.corgId});

  DBOrgModel.fromJson(Map<String, dynamic> json) {
    if (json['companyId'] == null) {
      companyId = json['orgId'] ?? '';
    } else {
      companyId = json['companyId'];
    }
    corgId = json['corgId'] ?? '';
    name = json['name'] ?? '';
    haveApprove = json['haveApprove'] ?? 0;
    logo = json['logo'] ?? '';
    rejectJoin = json['rejectJoin'] ?? 0;
    isJoin = json['isJoin'] ?? 0;
    deptId = json['deptId'] ?? '';
    rejectInvitation = json['rejectInvitation'] ?? 0;
    power = json['power'] ?? '';
    type = json['type'] ?? '';
    scale = json['scale'] ?? '';
    industry = json['industry'] ?? '';
    linkManPhone = json['linkManPhone'] ?? '';
    linkManMail = json['linkManMail'] ?? '';
    officialWebsite = json['officialWebsite'] ?? '';
    profile = json['profile'] ?? '';
    content = json['content'] ?? '';
    noticeId = json['noticeId'] ?? '';
    uid = json['uid'] ?? '';
    users = json['users'] ?? '';
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'companyId': companyId,
        'corgId': corgId,
        'logo': logo,
        'haveApprove': haveApprove,
        'rejectJoin': rejectJoin,
        'isJoin': isJoin,
        'deptId': deptId,
        'rejectInvitation': rejectInvitation,
        'power': power,
        'type': type,
        'scale': scale,
        'industry': industry,
        'linkManPhone': linkManPhone,
        'linkManMail': linkManMail,
        'officialWebsite': officialWebsite,
        'profile': profile,
        'content': content,
        'noticeId': noticeId,
        'users': users,
        'uid': uid
      };
}
