import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/db/org/db_org_model.dart';

@dao
abstract class DBOrgDao {
  @Query('SELECT * FROM DBOrgModel WHERE uid = :uid')
  Future<List<DBOrgModel>> getAllLocalOrg(String uid);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<DBOrgModel> models);

  @Query('DELETE FROM DBOrgModel WHERE uid = :uid AND companyId = :companyId')
  Future<void> deleteLocalOrg(String uid, String companyId);

  @Query('DELETE FROM DBOrgModel WHERE uid = :uid')
  Future<void> clearLocalOrg(String uid);

  @Query(
      'UPDATE DBOrgModel SET users = :users WHERE uid = :uid AND companyId = :companyId')
  Future<void> updateUsers(String uid, String companyId, String users);
}
