

import 'dart:collection';

import 'package:flutter_mixed/app/db/log/req_log.dart';
import 'package:flutter_mixed/app/statis/statistics_helper.dart';
import 'package:intl/intl.dart';

import '../../common/user/user_helper.dart';

extension ReqLogMapExt on Map<String,dynamic> {

  Future<ReqLog> convertReqLog() async{
    var map = this;

    var common = await StatisticsHelper.preInitCommonParams();
    var uid = await UserHelper.getUid();

    return ReqLog(map['url'])
      ..code = map.containsKey('code') ? map['code'] : 1
      ..userId = map.containsKey('userId') ? map['userId'] : ''
      ..method = map.containsKey('method') ? map['method'] : ''
      ..network = map.containsKey('network') ? map['network'] : ''
      ..startTime = DateTime.now().millisecondsSinceEpoch
      ..endTime = map.containsKey('endTime') ? map['endTime'] : 0
      ..os = common.containsKey('os') ? common['os']: ''
      ..osVersion = common.containsKey('osVersion') ? common['osVersion']: ''
      ..version = common.containsKey('version') ? common['version']: ''
      ..userId = uid
      ..deviceId =  common.containsKey('deviceId') ? common['deviceId']: ''
      ..time = DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())
      ..reportTime = DateTime.now().millisecondsSinceEpoch;

  }

}


