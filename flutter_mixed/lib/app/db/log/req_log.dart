import 'package:floor/floor.dart';

@entity
class ReqLog {
  @PrimaryKey()
  int? id;

  String? url;

  String? network;

  String? method;

  int? code;

  int? startTime;

  int? endTime;

  String? userId;

  int? reportTime;

  String? time;

  String? os;

  String? msg;

  String? osVersion;

  String? deviceId;

  String? version;

  String? extra;

  ReqLog(
      this.url,{
        this.network,
        this.method,
        this.code,
        this.startTime,
        this.endTime,
        this.userId,
        this.reportTime,
        this.time,
        this.os,
        this.msg,
        this.osVersion,
        this.deviceId,
        this.version,
        this.extra
  }
      );

  Map<String, dynamic> toMap() => {
        'id': id ?? 0,
        'url': url ?? '',
        'network': network ?? '',
        'method': method ?? '',
        'code': code ?? 1,
        'startTime': startTime ?? 0,
        'endTime': endTime ?? 0,
        'userId': userId ?? '',
        'reportTime': reportTime ?? 0,
        'os': os ?? '',
        'msg': msg ?? '',
        'time': time ?? '',
        'osVersion': osVersion ?? '',
        'deviceId': deviceId ?? '',
        'version':version ?? '',
        'extra': extra ?? ''
      };
  ReqLog.fromJson(Map<String, dynamic> json) {
    id = json['publicInfo'];
    url = json['url'];
    network = json['network'];
    method = json['method'];
    code = json['code'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    userId = json['userId'];
    reportTime = json['reportTime'];
    os = json['os'];
    msg = json['msg'];
    time = json['time'];
    osVersion = json['osVersion'];
    deviceId = json['deviceId'];
    version = json['version'];
    extra = json['extra'];
  }

  @override
  String toString() {
    return 'ReqLog{id: $id, url: $url, network: $network, method: $method, code: $code, startTime: $startTime, endTime: $endTime, userId: $userId, reportTime: $reportTime, time: $time, os: $os, msg: $msg, osVersion: $osVersion, deviceId: $deviceId, version: $version, extra: $extra}';
  }
}
