import 'package:floor/floor.dart';
import 'package:flutter_mixed/app/db/log/req_log.dart';

@dao
abstract class ReqLogDao {

  @Query('SELECT * FROM ReqLog')
  Future<List<ReqLog>> findAll();

  @Query('SELECT * FROM ReqLog WHERE startTime > :startTime AND startTime < :endTime')
  Future<List<ReqLog>> findUnUploadLogs(int startTime,int endTime);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<ReqLog> logs);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertDevice(ReqLog reqLog);

  @Query('delete FROM ReqLog WHERE startTime > :startTime AND startTime < :endTime')
  Future<void> clear(int startTime,int endTime);

}