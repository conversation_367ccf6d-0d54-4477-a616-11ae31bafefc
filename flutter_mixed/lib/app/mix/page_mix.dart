import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:get/get.dart';

mixin PageFinishMix {

  RxBool isNative = false.obs; //是否是原生跳转

  nativeOpen() {
    isNative.value = true;
  }

  bool isFromNative() => isNative.value;

  goBack() {
    isNative.closePage();
  }

  initNativeValue() {
    if(Get.arguments != null){
      try{
        var isFromNative = Get.arguments['isFromNative'];
        if(isFromNative != null){
          isNative.value = isFromNative;
        }
      }catch(e){}

    }
  }

}