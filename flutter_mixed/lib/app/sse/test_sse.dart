

import 'native_sse_client.dart';

/// 测试sse使用
void main() async {

  var header = {
    'Authorization': 'Bearer 7e1dd26d-955c-4b72-be14-f322f7fd6e5d',
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    // 'Accept': 'application/json',
    'Accept': 'text/event-stream',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
  };

  var _sseResponse = '';

  var nativeClient = NativeSseClient(
    // url: 'http://*********:8989/agent/chat/mcp/approve',
    url: 'http://*********:8989/qwen/vl/chat/video',
    headers: header,
  );

  final Map<String, dynamic> txtBody = {
    "id": "58c3f33e-90e9-4f5c-86f0-0c400b60ed26",
    "messages": [
      {"role": "user", "content": "你好"}
    ],
    "model": "qwen-turbo-latest",
    "stream": true,
    "temperature": 0.7,
    "tools": ["string"]
  };

  final Map<String, dynamic> imgBody = {
    "id": "58c3f33e-90e9-4f5c-86f0-0c400b60ed26",
    "messages": [
      {"role": "user", "content": [
        {"type" : "text", "text": "你好，这是什么图"},
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,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"
          }
        }
      ]},
      // {"role": "user", "content": "给我生成一个手机图片"},
    ],
    // "model": "qwen-turbo-latest",
    "model": "/home/<USER>/model/Qwen/Qwen2.5-VL-32B-Instruct-AWQ",
    "stream": true,
    "temperature": 0.7,
    "tools": ["string"]
  };

  nativeClient?.connect(body: imgBody).listen((event) {
    print('[UI] onData: $event');
    print('[NativeSseClient][UI] event: ' + event.toString());
    print('[NativeSseClient][UI] event.choices: ' + event.choices.toString());
    // setState(() {
      _sseResponse += (event.choices != null && event.choices!.isNotEmpty && event.choices![0]['delta'] != null && event.choices![0]['delta']['content'] != null)
          ? event.choices![0]['delta']['content']
          : '';
    // });
  },
      onDone: () {
        print('[UI] onDone');
        // setState(() {
        //   _isSseLoading = false;
        // });
      },
      onError: (error) {
        print('[UI] onError: $error');
        // setState(() {
          _sseResponse = 'Native SSE Error: ' + error.toString();
          // _isSseLoading = false;
        // });
      });

  // close
  nativeClient.close();
}