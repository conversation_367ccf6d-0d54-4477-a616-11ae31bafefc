import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter_mixed/app/sse/sse_models.dart';

/// SSE 客户端，仅适用内置依赖
class NativeSseClient {
  final String url;
  final Map<String, String>? headers;
  HttpClientRequest? _request;
  HttpClientResponse? _response;
  StreamController<String>? _controller;
  StreamController<SSeModel>? _modelController;

  NativeSseClient({required this.url, this.headers});

  /// 原始字符串流
  Stream<String> connectString({Map<String, dynamic>? body}) {
    _controller = StreamController<String>(onCancel: close);
    _start(body);
    return _controller!.stream;
  }

  /// 高级：直接返回 SSeModel 流，和 SseClient.send 一致
  Stream<SSeModel> connect({Map<String, dynamic>? body}) {
    _modelController = StreamController<SSeModel>(onCancel: close);
    _startModel(body);
    return _modelController!.stream;
  }

  void _start(Map<String, dynamic>? body) async {
    final client = HttpClient();
    try {
      final uri = Uri.parse(url);
      _request = await client.openUrl('POST', uri);
      headers?.forEach((key, value) {
        _request!.headers.set(key, value);
      });
      if (body != null) {
        _request!.add(utf8.encode(jsonEncode(body)));
      }
      _response = await _request!.close();

      _sseLog('>>>> $_response');

      // 监听 SSE 数据
      _response!.transform(utf8.decoder).listen((data) {
        for (var line in data.split('\n')) {
          if (line.startsWith('data:')) {
            final payload = line.substring(5).trim();
            _controller?.add(payload);
          }
        }
      }, onDone: close, onError: (e) {
        _controller?.addError(e);
        close();
      }, cancelOnError: true);
    } catch (e) {
      _controller?.addError(e);
      close();
    }
  }

  void _startModel(Map<String, dynamic>? body) async {
    final client = HttpClient();
    try {
      final uri = Uri.parse(url);
      _sseLog('[NativeSseClient] 请求发起: $uri');
      _request = await client.openUrl('POST', uri);
      headers?.forEach((key, value) {
        _request!.headers.set(key, value);
      });
      if (body != null) {
        _sseLog('[NativeSseClient] 请求体: ${jsonEncode(body)}');
        _request!.add(utf8.encode(jsonEncode(body)));
      }
      _response = await _request!.close();
      _sseLog('[NativeSseClient] 响应状态: ${_response?.statusCode}');
      if (_response?.statusCode != 200) {
        _modelController?.addError('HTTP ${_response?.statusCode}');
        close();
        return;
      }
      _response!.transform(utf8.decoder).listen((data) {
        _sseLog('[NativeSseClient] 收到原始数据块: $data');
        // 兼容 \r\n、\n、\r 换行
        final lines = data.split(RegExp(r'\r?\n'));
        for (var line in lines) {
          _sseLog('[NativeSseClient] 行: $line');
          if (line.startsWith('data:')) {
            final payload = line.substring(5).trim();
            _sseLog('[NativeSseClient] data: $payload');
            if (payload == '[DONE]') {
              if (_modelController != null && !_modelController!.isClosed) {
                _modelController?.add(SSeModel.empty());
              }
              continue;
            }
            if (payload.isNotEmpty) {
              try {
                if (_modelController != null && !_modelController!.isClosed) {

                  _sseLog('[NativeSseClient] add to stream: $payload');

                  _modelController?.add(SSeModel.fromJson(jsonDecode(payload)));
                }
              } catch (e) {
                _sseLog('[NativeSseClient] 解析异常: $e');
                if (_modelController != null && !_modelController!.isClosed) {
                  _modelController?.add(SSeModel.empty());
                }
              }
            } else {
              if (_modelController != null && !_modelController!.isClosed) {
                _modelController?.add(SSeModel.empty());
              }
            }
          }
        }
      }, onDone: () {
        _sseLog('[NativeSseClient] 流结束');
        close();
      }, onError: (e) {
        _sseLog('[NativeSseClient] 流异常: $e');
        _modelController?.addError(e);
        close();
      }, cancelOnError: true);
    } catch (e) {
      _sseLog('[NativeSseClient] 外层异常: $e');
      _modelController?.addError(e);
      close();
    }
  }

  /// 主动关闭 SSE
  void close() {
    // 不再调用 _response?.detachSocket()，避免已关闭时的空指针异常
    if (_controller != null && !_controller!.isClosed) {
      _controller!.close();
    }
    if (_modelController != null && !_modelController!.isClosed) {
      _modelController!.close();
    }
    _response = null;
    _request = null;
    _controller = null;
    _modelController = null;
  }
}

_sseLog(Object obj) {
   print(obj);
}