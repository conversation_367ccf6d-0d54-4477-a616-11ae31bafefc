

/// 响应数据模型（可根据实际返回结构调整）
class SSeModel {
  final String? id;
  final String? object;
  final int? created;
  final String? model;
  final List<dynamic>? choices;

  SSeModel({this.id, this.object, this.created, this.model, this.choices});

  factory SSeModel.fromJson(Map<String, dynamic> json) {
    return SSeModel(
      id: json['id'],
      object: json['object'],
      created: json['created'],
      model: json['model'],
      choices: json['choices'],
    );
  }

  factory SSeModel.empty() => SSeModel();
}
