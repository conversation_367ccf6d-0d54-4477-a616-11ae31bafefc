
// 天地图 base url
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';

import '../common/base_info/info.dart';

var BASE_TIANDITU_URL = "https://mobile.ddbes.com/test-map/index.html#/";


enum MapRouterType {
  sharelocation(1) ,
  tonavigate(2),
  auto_location(3);

  final int status;

  const MapRouterType(this.status);

}
///openWebView({
//       'url': url,
//       'title': toolMap['name'],
//       'isWebNavigation': backspaceKey == 1 ? 0 : 1,
//       'orgId': currentModel!.companyId
//     });
Future getLocation() async {

  var url = "${BASE_TIANDITU_URL}?mapViewPageType=${MapRouterType.sharelocation.status}";
  return {
    'url': await appendMapUrl(url),
    'title': '获取位置',
    'isWebNavigation': 1,
    'type': '${MapRouterType.sharelocation.status}',
  };
}

Future toNavigate(double? lat ,double? lng, String? poiName,String? poiAddress) async {
  var url = "${BASE_TIANDITU_URL}?mapViewPageType=${MapRouterType.tonavigate.status}&lat=${lat}&lon=${lng}&poiName=${poiName}&poiAddress=${poiAddress}";
  return {
    'url': await appendMapUrl(url),
    'title': '去导航',
    'isWebNavigation': 1,
    'type': '${MapRouterType.tonavigate.status}',
  };
}

Future appendMapUrl(String url) async{
  int platform = Platform.isIOS ? 2: 1;
  String appVersion = await BaseInfo().getAppVersion();
  if (url.contains('?')) {
    url = '$url&platform=$platform&appVersion=$appVersion';
  } else {
    url = '$url?platform=$platform&appVersion=$appVersion';
  }
  return url;

}

String createMapImage(double lon, String lat) {
  return "http://api.tianditu.gov.cn/staticimage?center=${lon},${lat}&width=400&height=300&zoom=10&tk=2e57941f466625166d5954aaffa7a0be";
}