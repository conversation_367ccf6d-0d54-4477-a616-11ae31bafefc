
import 'dart:async';

import 'package:flutter_mixed/logger/logger.dart';

import '../common/widgets/widgets.dart';

class CustomLock {
  bool _isLocked = false;
  final List<Completer<void>> _waitingQueue = [];

  bool isLocked() => _isLocked;

  Future<void> acquire() async {
    if (!_isLocked) {
      logger('CustomLock..._not  isLocked');
      _isLocked = true;
      return;
    }

    logger('CustomLock..._is   Locked');
    final completer = Completer<void>();
    _waitingQueue.add(completer);
    await completer.future;
  }

  void release() {
    if (_waitingQueue.isNotEmpty) {
      final next = _waitingQueue.removeAt(0);
      next.complete();
    } else {
      _isLocked = false;
    }
  }
}