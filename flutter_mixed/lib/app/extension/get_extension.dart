import 'dart:async';
import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/utils/dialog_hud.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../main.dart';
import '../common/widgets/loading_dialog.dart';
import '../common/channel/channel.dart';

extension GetExtension on GetInterface {
  Future dismiss({String tag = ''}) async {
    // if(Get.isDialogOpen == null) return;
    // if (Get.isDialogOpen!) {
    //   Get.back();
    // }
    await DialogHud.dismiss(tag: tag);
  }

  loading({bool dismissible = false, String loadingText = 'loading...'}) {
    DialogHud.showLoading(clickMaskDismiss: dismissible, msg: loadingText);
    // if(Get.isDialogOpen == null) return;
    // if (Get.isDialogOpen!) {
    //   Get.back();
    // }
    // Get.dialog(LoadingDialog(dissable: dismissible ?? true , loadingTip: loadingText,));
  }

  tagLoading({String tag = '',String text = 'loading...',bool clickMaskDismiss = false}) {
    SmartDialog.show(
      tag: tag,
        clickMaskDismiss: clickMaskDismiss,
        builder: (context) {
          return Container(
            alignment: Alignment.center,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  color: ColorConfig.whiteColor,
                ),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Text(text,style: const TextStyle(fontSize: 16,color: ColorConfig.whiteColor,)),
                )
              ],
            ),
          );
        });
  }
  tagDissmiss({String tag = ''}){
    SmartDialog.dismiss(tag: tag);
  }

  StreamSubscription? getOrEventParams(String path,
      {Function? arg, RxBool? isNative, Function? voidBack}) {
    if (arguments != null) {
      arg?.call(arguments);
      voidBack?.call();
      return null;
    } else {
      var subscription = eventBus.on<Map>().listen((event) {
        var route = event['route'];
        if (route == path) {
          isNative?.value = true;
          var arguments = event['arguments'];
          arguments['isFromNative'] = true;
          arg?.call(arguments);
        }
        voidBack?.call();
      });
      return subscription;
    }
  }
  
}

extension IsNativeExtension on RxBool {
  closePage() {
    if (value) {
      Channel().invoke(Channel_Native_Back, {});
    } else {
      Get.back();
    }
  }
}
