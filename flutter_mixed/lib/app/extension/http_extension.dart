import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:dio_retry_plus/dio_retry_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_mixed/app/retrofit/interceptor/header_interceptor.dart';
import 'package:flutter_mixed/logger/logger_helper.dart';
import 'package:native_dio_adapter/native_dio_adapter.dart';
import '../../logger/logger.dart';
import '../../logger/logger_interceptor.dart';
import '../utils/request/interceptor/error_interceptor.dart';
import '../utils/request/interceptor/refresh_token_interceptor.dart';
import '../utils/request/interceptor/statistic_interceptor.dart';

extension DioExtension on Dio {
  Future addInterceptors({bool? isAddHeader}) async {

    if(Platform.isIOS){
      interceptors.add(RefreshTokenInterceptor());
      httpClientAdapter = NativeAdapter();
    }else{
      httpClientAdapter = IOHttpClientAdapter()
      ..onHttpClientCreate = (client) {
        // Config the client.
        if (Platform.isAndroid) {
          client.badCertificateCallback =
              (X509Certificate cert, String host, int port) {
            return true;
          };
        }

        // You can also create a new HttpClient for Dio instead of returning,
        // but a client must being returned here.
        return client;
      };
    }

    interceptors..add(StatisticInterceptor())
      ..add(ErrorInterceptor())
      ..add(RetryInterceptor(
          dio: this,
          retries: 2,
          logPrint: (msg) {
            logger('RetryInterceptor===$msg');
          },
          toNoInternetPageNavigator: () async {}));


    if (enableLog && Platform.isAndroid) {
      interceptors.add(LinqDioLogger(enableLog: true));
    }


    if (isAddHeader == true) {
      interceptors.add(HeaderInterceptor(this));
    }

    // interceptors.add(ImTokenInterceptor());

  }
}
