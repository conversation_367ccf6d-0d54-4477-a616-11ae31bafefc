import 'package:event_bus/event_bus.dart';
import 'package:flutter_mixed/app/utils/dialog_hud.dart';

extension BusExtension on EventBus {

  Future dismiss()  async {
    // if(Get.isDialogOpen == null) return;
    // if (Get.isDialogOpen!) {
    //   Get.back();
    // }
    await DialogHud.dismiss();
  }

  loading({bool? dismissible = true , String? loadingText}) {
    DialogHud.showLoading();
    // if(Get.isDialogOpen == null) return;
    // if (Get.isDialogOpen!) {
    //   Get.back();
    // }
    // Get.dialog(LoadingDialog(dissable: dismissible ?? true , loadingTip: loadingText,));
  }
}
