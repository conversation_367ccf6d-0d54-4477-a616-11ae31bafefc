

import 'dart:io';
import 'package:mime/mime.dart';


extension FileExtension on File? {


  Future<int> getFileSize() async {
    if(this == null) return 0;
    var size = await this!.length();
    return size;
  }

  // 文件超过上限了 100M
  Future<bool> beyondUpperSize() async {
    if(this == null) return false;
    try {
      final fileSizeBytes = await this!.length();
      final fileSizeMB = fileSizeBytes / 1048576;
      return fileSizeMB > 100;
    } on FileSystemException catch (e) {
      print('文件访问错误: $e');
      return false; // 或根据需求处理异常
    }
  }

  bool isImage() {
    if(this == null) return false;
    final mimeType = lookupMimeType(this!.path);
    return mimeType?.startsWith('image/') ?? false;
  }

  bool isVideo() {
    if(this == null) return false;
    final mimeType = lookupMimeType(this!.path);
    return mimeType?.startsWith('video/') ?? false;
  }


}