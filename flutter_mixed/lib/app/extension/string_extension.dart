

import 'package:flutter/material.dart';

extension StringExtension on String? {
  //为String类扩展首字母大写方法


  String? toNonNullStr() {
    return this ?? '';
  }

  bool isNotBlank() {
    if(this == null || this == "") return false;
    return true;
  }

  String spiltString(int length) {
    if(this == null) return '';
    if(this!.length < length) return this!;
    return '${this!.substring(0 , length)}...';
  }

  String hintMobile() {
    if(this == null) return '';
    if(this!.isEmpty) return '';
    return this!.replaceRange(3, 7, '****');
  }

  String getRange(int start , [int? end]) {
    if(this == null) return '';
    if(this == '') return '';
    Characters characters = this!.characters;
    int length = characters.length;
    if (start < 0) start = 0;
    if ((end ?? 0) > length) end = length;
    if (start >= length || start >= (end ?? 0)) return "";
    return characters.getRange(start, end).string;
  }


  bool isVideoPath() {
    if(this == null) return false;
    if(this == '') return false;
    return this!.toLowerCase().endsWith('.mp4');
  }
}