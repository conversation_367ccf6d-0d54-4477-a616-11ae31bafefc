import 'package:flutter/cupertino.dart';

typedef TapAction = void Function();


class NoDoubleClickGestureDetector extends StatelessWidget {

  final Widget child;
  final TapAction tapAction;
  final int tapDuration;
  DateTime? lastTapTime;

  NoDoubleClickGestureDetector(
      {Key? key,
        required this.child,
        required this.tapAction,
        required this.tapDuration,
      });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (lastTapTime == null || DateTime.now().difference(lastTapTime!) > Duration(seconds: tapDuration)) {
          lastTapTime = DateTime.now();
          tapAction();
        } else {
          // againAction();
        }
      },
      child: child,
    );
  }
}

