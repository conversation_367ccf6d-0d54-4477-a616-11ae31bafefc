import 'dart:io';
import 'dart:math';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/widgets/theme.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import '../../im/widget/some_widget.dart';
import '../config/config.dart';

class TitleBar {
  backAppbar(BuildContext context, String title, bool isHiddenBackBtn,
      List<Widget> actions,
      {required VoidCallback onPressed,
      double appbarHeight = 44,
      bool isNative = true,
      Color? backgroundColor
      }) {
    return PreferredSize(
        preferredSize: Size.fromHeight(appbarHeight),
        child: AppBar(
            title: Text(
              '$title${_flutterTarget()}',
              style: const TextStyle(
                  color: ColorConfig.mainTextColor, fontSize: 16,fontWeight: FontWeight.w500),
            ),
            centerTitle: true,
            leading: isHiddenBackBtn == true
                ? const Text('')
                : _leading(context, onPressed, isNative),
            backgroundColor: backgroundColor ?? const Color(0xF1FFFFFF),
            elevation: 0,
            systemOverlayStyle: translateOverlayStyle,
            surfaceTintColor: backgroundColor ?? const Color(0xF1FFFFFF),
            actions: actions));
  }

  _flutterTarget() {
    if (EnvConfig.mEnv == Env.Product) return '';
    switch (EnvConfig.mEnv) {
      case Env.Test:
        return "(测试环境)";
      case Env.Dev:
        return "(开发环境)";
      case Env.Fast:
        return "(Fast环境)";
      default:
        return "(${EnvConfig.mEnv.toString()})";
    }
    // return '[flutter]';
  }

  _leading(BuildContext context, VoidCallback onPressed, bool isNative) {
    return IconButton(
      onPressed: () {
        if (onPressed == null) {
          _popThis(context);
        } else {
          onPressed();
          if (isNative) {
            if (Platform.isAndroid) {
              Channel().invoke(Channel_call_android_backPage, {});
            }
          }
        }
      },
      icon: SizedBox(
        width: 24,
        height: 24,
        child: Image.asset('assets/images/3.0x/pic_return.png'),
      ),
    );
  }

  /**
   * 关闭页面
   */
  _popThis(BuildContext context) {
    Navigator.of(context).pop();
  }
}

Future toast(String? text) async {
  Fluttertoast.showToast(
      timeInSecForIosWeb: 2,
      msg: text ?? '',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      backgroundColor: Colors.black,
      textColor: Colors.white,
      fontSize: 16);
}

class SettingWidget {
  backSettingWidget(String selectImageName, String headImageName,
      String leftStr, String rightStr, bool isHave, double height,
      {String signStr = '', Color color = ColorConfig.whiteColor}) {

    var selectImage;
    if (StringUtil.isEmpty(selectImageName)) {
      selectImage = Container();
    } else {
      selectImage = Image.asset(selectImageName);
    }

    return Column(
      children: [
        Container(
          color: ColorConfig.whiteColor,
          height: height,
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Offstage(
                offstage: selectImageName.isEmpty,
                child: Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      alignment: Alignment.centerLeft,
                      child: selectImage,
                    ),
                    const SizedBox(
                      width: 5,
                    )
                  ],
                ),
              ),
              Offstage(
                offstage: headImageName.isEmpty,
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(1),
                      width: height - 16,
                      height: height - 16,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: SettingWidget.backImageProvider(headImageName), fit: BoxFit.contain),
                          borderRadius:
                              BorderRadius.circular((height - 16) * 0.5),
                          border: Border.all(
                              color: ColorConfig.lineColor, width: 0.5)),
                    ),
                    const SizedBox(
                      width: 5,
                    )
                  ],
                ),
              ),
              Expanded(
                  child: Row(
                children: [
                  Expanded(
                      child: Container(
                    // constraints: BoxConstraints(maxWidth: 200),
                    // width: headImageName.isNotEmpty
                    //     ? DeviceUtils().width.value - 140
                    //     : 200,
                    alignment: Alignment.centerLeft,
                    height: height - 1,
                    child: Text(leftStr,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 14)),
                  )),
                  8.gap,
                  Offstage(
                    offstage: signStr.isEmpty,
                    child: Container(
                      padding: const EdgeInsets.only(left: 5, right: 5),
                      height: 19,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          border: Border.all(
                              width: 0.5, color: ColorConfig.themeCorlor),
                          borderRadius: BorderRadius.circular(3)),
                      child: Text(signStr,
                          style: TextStyle(fontSize: 11, color: color)),
                    ),
                  ),
                ],
              )),
              Offstage(
                offstage: rightStr.isEmpty,
                child: Container(
                  alignment: Alignment.centerRight,
                  height: height - 1,
                  child: Text(rightStr,
                      maxLines: 1,
                      style: const TextStyle(
                          color: ColorConfig.mainTextColor, fontSize: 14)),
                ),
              ),
              Offstage(
                offstage: !isHave,
                child: const SizedBox(
                  width: 10,
                ),
              ),
              Offstage(
                  offstage: isHave == false,
                  child: SizedBox(
                    width: 9,
                    height: 17,
                    child: Image.asset('assets/images/3.0x/mine_right.png'),
                  ))
            ],
          ),
        ),
        // const Divider(height: 1, color: ColorConfig.lineColor, indent: 15)
      ],
    );
  }

  backSearchWidget(
    TextEditingController searchController,
    FocusNode node,
    String hitText, {
    required Function onSub,
    required Function onTap,
    Function? onChange,
  }) {
    return Container(
        //width: double.infinity,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: ColorConfig.backgroundColor),
        child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
          const SizedBox(
            width: 12,
          ),
          SizedBox(
            width: 19,
            height: 19,
            child: Image.asset('assets/images/3.0x/org_search.png'),
          ),
          const SizedBox(
            width: 5,
          ),
          Expanded(
              child: Container(
            alignment: Alignment.centerLeft,
            child: TextField(
              onSubmitted: (value) {
                onSub(value);
              },
              onTap: () {
                onTap();
              },
              onChanged: (value) {
                if (onChange != null) {
                  onChange(value);
                }
              },
              cursorColor: ColorConfig.themeCorlor,
              focusNode: node,
              textInputAction: TextInputAction.search,
              controller: searchController,
              style: const TextStyle(
                color: ColorConfig.mainTextColor,
                fontSize: 14,
              ),
              decoration: InputDecoration(
                  isCollapsed: true,
                  contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                  border: const OutlineInputBorder(borderSide: BorderSide.none),
                  hintText: hitText,
                  hintStyle: const TextStyle(
                    color: ColorConfig.desTextColor,
                    fontSize: 14,
                  )),
            ),
          ))
        ]));
  }

  backEditOrgWidget(String leftStr, String rightStr, bool isHaveRightArrow) {
    return Container(
      color: Colors.white,
      width: double.infinity,
      height: 47,
      padding: const EdgeInsets.only(left: 15, right: 15),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: 46,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 110,
                  height: 46,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    leftStr,
                    textAlign: TextAlign.left,
                    style: const TextStyle(
                        color: ColorConfig.mainTextColor, fontSize: 14),
                  ),
                ),
                Expanded(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Expanded(
                        child: Container(
                      height: 46,
                      alignment: Alignment.centerRight,
                      child: Text(
                        rightStr,
                        textAlign: TextAlign.right,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: const TextStyle(
                            color: ColorConfig.mainTextColor, fontSize: 14),
                      ),
                    )),
                    Offstage(
                      offstage: !isHaveRightArrow,
                      child: const SizedBox(
                        width: 10,
                      ),
                    ),
                    Offstage(
                        offstage: isHaveRightArrow == false,
                        child: SizedBox(
                          width: 9,
                          height: 17,
                          child:
                              Image.asset('assets/images/3.0x/mine_right.png'),
                        ))
                  ],
                ))
              ],
            ),
          ),
          line,
        ],
      ),
    );
  }

  backLoginBackBtn({required VoidCallback onPressed}) {
    return Column(
      children: [
        Obx(() => SizedBox(height: DeviceUtils().top.value)),
        InkWell(
          onTap: () {
            onPressed();
          },
          child: Container(
            padding: const EdgeInsets.only(left: 4),
            width: double.infinity,
            height: 44,
            alignment: Alignment.centerLeft,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Image.asset('assets/images/3.0x/pic_return.png'),
            ),
          ),
        )
      ],
    );
  }

  //上下按钮
  Widget actionForInvite(String imageName, String bottomName, double width,
      double height, Color color) {
    return Container(
      width: width,
      height: height,
      color: color,
      padding: const EdgeInsets.only(top: 5, bottom: 5),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 20,
            height: 20,
            child: Image.asset(imageName),
          ),
          Container(
            alignment: Alignment.center,
            width: double.infinity,
            height: 20,
            child: Text(
              bottomName,
              style: const TextStyle(
                  color: ColorConfig.mainTextColor, fontSize: 12),
            ),
          )
        ],
      ),
    );
  }

  Widget backFunWidget(String imageName, String title) {
    return Container(
      width: double.infinity,
      height: 48,
      color: ColorConfig.whiteColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 20,
            height: 20,
            child: Image.asset(imageName),
          ),
          const SizedBox(
            width: 5,
          ),
          Text(
            title,
            style:
                const TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
          )
        ],
      ),
    );
  }

  Widget backApproveEditState(
      String showContent,
      int chooseState,
      Function onChooseAllPressed,
      Function onRefusePressed,
      Function onAgreePressed) {
    //0未选中1选中
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            onChooseAllPressed();
          },
          child: Container(
            alignment: Alignment.centerLeft,
            height: 40,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                8.gap,
                SizedBox(
                  width: 16,
                  height: 16,
                  child: Image.asset(chooseState == 0
                      ? 'assets/images/3.0x/approve_unselected.png'
                      : 'assets/images/3.0x/approve_selected.png'),
                ),
                8.gap,
                const Text(
                  '批量选择',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
                5.gap,
                Text(
                  showContent,
                  style: const TextStyle(
                      fontSize: 14, color: ColorConfig.desTextColor),
                ),
              ],
            ),
          ),
        ),
        16.gap,
        Flexible(
            child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Flexible(
                flex: 1,
                child: InkWell(
                  onTap: () {
                    onRefusePressed();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    //margin: const EdgeInsets.only(top: 10,bottom: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: ColorConfig.mybackgroundColor),
                    child: const Text(
                      '拒绝',
                      style: TextStyle(
                          fontSize: 14, color: ColorConfig.themeCorlor),
                    ),
                  ),
                )),
            16.gap,
            Flexible(
                flex: 1,
                child: InkWell(
                  onTap: () {
                    onAgreePressed();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    //  margin: const EdgeInsets.only(top: 10,bottom: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: ColorConfig.themeCorlor),
                    child: const Text(
                      '同意',
                      style: TextStyle(
                          fontSize: 14, color: ColorConfig.whiteColor),
                    ),
                  ),
                )),
            8.gap
          ],
        ))
      ],
    );
  }

  //底部sheet选择器
  showCupertinoActionSheetForPage(
      BuildContext context, List list, Function onPressed,
      {String messsage = ''}) async {
    var result = showCupertinoModalPopup(
        context: context,
        builder: (context) {
          return CupertinoActionSheet(
            message: messsage.isEmpty
                ? null
                : Container(
                    height: 70,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.only(left: 30, right: 30),
                    child: Text(
                      messsage,
                      style: const TextStyle(
                          fontSize: 14, color: ColorConfig.desTextColor),
                      maxLines: 3,
                    ),
                  ),
            actions: getTypeListWidget(context, list, onPressed),
            cancelButton: CupertinoActionSheetAction(
              child: const Text(
                '取消',
                style:
                    TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
              ),
              onPressed: () {
                Navigator.of(context).pop('cancel');
              },
            ),
          );
        });
  }

  getTypeListWidget(BuildContext context, List list, Function onPressed) {
    List<Widget> widgets = [];
    for (var i = 0; i < list.length; i++) {
      List textList = [];
      String textStr = list[i];
      if (textStr.contains('\n')) {
        textList = textStr.split('\n');
      }
      Widget action = CupertinoActionSheetAction(
        child: textList.isEmpty
            ? Text(
                list[i],
                style: const TextStyle(
                    fontSize: 14, color: ColorConfig.mainTextColor),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    textList[0],
                    style: const TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                  const SizedBox(
                    height: 2,
                  ),
                  Text(
                    textList[0],
                    style: const TextStyle(
                        fontSize: 12, color: ColorConfig.desTextColor),
                  )
                ],
              ),
        onPressed: () {
          Navigator.of(context).pop('cancel');
          onPressed(i);
        },
        isDefaultAction: true,
      );
      widgets.add(action);
    }
    return widgets;
  }
  static backImageProvider(String? url,{bool cache = false}){
    ImageProvider image;
    if (StringUtil.isEmpty(url)) {
      image = const AssetImage('assets/images/3.0x/contact_moren_head.png');
    } else {
      if (url!.contains('://')) {
        image = ExtendedNetworkImageProvider(url,cache: cache);
      } else {
        image = AssetImage(url);
      }
    }
    return image;
  }
}

//im播放动画
class SoundPainter extends CustomPainter {
  SoundPainter({required this.left, required this.showIndex});

  bool left;
  int showIndex;
  @override
  void paint(Canvas canvas, Size size) {

    var mPaint = Paint()
      ..color = ColorConfig.mainTextColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    var radius = size.width / 2;
    var sweepAngle = pi / 2;
    var startAngle;
    var center;

    if (left) {
      startAngle = -pi / 4;
      center = Offset(0, size.height / 2);
    } else {
      center = Offset(size.width, size.height / 2);
      startAngle = pi * 3 / 4;
    }

    if (showIndex >= 3) {
      canvas.drawArc(Rect.fromCircle(center: center, radius: radius),
          startAngle, sweepAngle, false, mPaint);
    }
    if (showIndex >= 2) {
      canvas.drawArc(Rect.fromCircle(center: center, radius: radius * 2 / 3),
          startAngle, sweepAngle, false, mPaint);
    }
    if (showIndex >= 1) {
      canvas.drawArc(Rect.fromCircle(center: center, radius: radius / 3),
          startAngle, sweepAngle, true, mPaint..style = PaintingStyle.fill);

      mPaint.style = PaintingStyle.stroke;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class DashedLine extends StatelessWidget {
  final double height;
  final double dashWidth;
  final double dashSpace;
  final Color color;

  DashedLine({
    required this.height,
    this.dashWidth = 2.0,
    this.dashSpace = 2.0,
    this.color = Colors.black,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _DashedLinePainter(
        dashWidth: dashWidth,
        dashSpace: dashSpace,
        color: color,
      ),
      size: Size(double.infinity, height),
    );
  }
}

class _DashedLinePainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final Color color;

  _DashedLinePainter({
    required this.dashWidth,
    required this.dashSpace,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = dashWidth
      ..style = PaintingStyle.stroke;

    final dashCount = (size.height / (dashWidth + dashSpace)).floor();

    for (var i = 0; i < dashCount; i++) {
      final offsetY = (i * (dashWidth + dashSpace)) + (dashWidth / 2);
      canvas.drawLine(
        Offset(0, offsetY),
        Offset(size.width, offsetY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

String flutterTarget() {
  logger('....${EnvConfig.mEnv}.....');
  if (EnvConfig.mEnv == Env.Product) return '';
  switch (EnvConfig.mEnv) {
    case Env.Test:
      return "(测试环境)";
    case Env.Dev:
      return "(开发环境)";
    case Env.Fast:
      return "(Fast环境)";
    default:
      return "(${EnvConfig.mEnv.toString()})";
  }
  // return '[flutter]';
}
