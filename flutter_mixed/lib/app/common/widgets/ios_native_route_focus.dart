


import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

import '../../../plugin/focus_detector/focus_detector.dart';
import '../channel/channel.dart';

/// 作用： iOs 端原生跳转到此页的时候fix手势问题
class IosNativeRouteFixGesture<T extends MixNativeController> extends StatelessWidget {

  final IosNativeRouteFocusBuilder childBuilder;

  GetxController? controller;

  IosNativeRouteFixGesture.builder({super.key,
    required this.controller,
    required this.childBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return FocusDetector(
        onFocusGained:(){
          if(controller is MixNativeController){
            var isNative = (controller as MixNativeController).isNative;
            if (isNative.value == true && Platform.isIOS) {
              Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 1});
            }
          }
        },
        onFocusLost: (){
          if(controller is MixNativeController){
            var isNative = (controller as MixNativeController).isNative;
            if (isNative.value == true && Platform.isIOS) {
              Channel().invoke(Channel_call_iOS_popGesture, {'popGesture': 0});
            }
          }
        },
        child: childBuilder());
  }
}


typedef IosNativeRouteFocusBuilder = Widget Function();



mixin MixNativeController {

  RxBool isNative = false.obs; //是否是原生跳转

}