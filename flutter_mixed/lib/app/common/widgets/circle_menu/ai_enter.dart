


import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/retrofit/entity/ai/ai_power_resp.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../../main.dart';
import '../../../../res/assets_res.dart';
import '../../../retrofit/datasource/workbench_datasource.dart';
import '../../api/LoginApi.dart';
import '../../base_info/info.dart';
import '../../channel/channel.dart';
import 'animated_floating_action_button.dart';

GlobalKey<AnimatedFloatingActionButtonState> _floatWidgetKey = GlobalKey();


class AiEnterFloat extends StatelessWidget {

  List<AIPowerItem> aiEntryList;

  AiEnterFloat({super.key, required this.aiEntryList});

  // 后期统统改为后端控制
  bool isShowOrder () => aiEntryList.any((element) => element.powerId == 'order');

  @override
  Widget build(BuildContext context) {
    return buildFloatButton();
  }

  buildFloatButton() {
    return Align(
      child: Container(
        alignment: Alignment.bottomRight,
        child: Container(
          // color: Colors.red,
          margin: const EdgeInsets.only(right: 15, bottom: 40),
          child: AnimatedFloatingActionButton(
            fabButtons: <Widget>[
              if(isShowOrder())
                floatButton('订餐', AssetsRes.AI_ORDER_MEAL, const Color(0xFFFF4848),  // 颜色 #FF4848
                    const Color(0xFFFFA502), (){
                       _orderMealAction("order");
                  }),
              floatButton('提问', AssetsRes.AI_QUESTION_ICON,const Color(0xFF2724ED),  // 颜色 #FF4848
                  const Color(0xFFA484F3), () {
                      _questionAction('chat');
                  }),
            ],
            // spaceBetween: 20,
            key: _floatWidgetKey,
            colorStartAnimation: Colors.red,
            colorEndAnimation: Colors.blue,
            animatedIconData: AnimatedIcons.menu_close,
            isOpenCallBack: (opened) {
                logger('isOpenCallBack.... $opened');
            }, //To principal button
          ),
        ),
      ),
    );
  }

  floatButton(String text, String icon, Color top, Color bottom, VoidCallback callBack) {
    return GestureDetector(
      behavior: HitTestBehavior.deferToChild,
      onTap: (){
        callBack.call();
      },
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                top,
                bottom,
              ],
            ),
            borderRadius: BorderRadius.circular(100)),
        width: 56,
        height: 56,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(icon , height: 15,),
            2.gap,
            Text(
              text,
              style: const TextStyle(fontSize: 10, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  _orderMealAction(String powerId) async {
    var url = await _createUrl(powerId);
    Channel().invoke(Channel_openWebView, {
      'url': url,
      'isWebNavigation': 1,
    });
    logger('>>>>> 订餐 ${url}');
  }

  _questionAction(String powerId) async {
    var url = await _createUrl(powerId);
    Channel().invoke(Channel_openWebView, {
      'url': url,
      'title': '',
      'isWebNavigation': 1,
    });
    logger('>>>>> 提问${url}');
  }

  _createUrl(String powerId) async {
    var enterItem = aiEntryList.firstWhere((element) => element.powerId == powerId);
    var url = "${enterItem.webUrl}?agent=${enterItem.agent}";
    //获取platform
    int platform = 1;
    if (Platform.isIOS) {
      platform = 2;
    }
    //获取appVersion
    String appVersion = await BaseInfo().getAppVersion();
    if (url.contains('?')) {
      url = '$url&platform=$platform&appVersion=$appVersion';
    } else {
      url = '$url?platform=$platform&appVersion=$appVersion';
    }
    return url;
  }

}

Future<List<AIPowerItem>> fetchAiEnterPermission() async {
  var api = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
  var resp = await api.getAiEntryList();
  var list = <AIPowerItem>[];
  if (resp.success()) {
    list = resp.data;
  }
  return list;
}


