// import 'package:animate_icons/animate_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/circle_menu/animate_icons.dart';
import 'package:flutter_mixed/app/common/widgets/circle_menu/transform_float_button.dart';
import 'package:flutter_mixed/res/assets_res.dart';

class AnimatedFloatingActionButton extends StatefulWidget {
  ///
  /// list of Floating Action Buttons.
  final List<Widget> fabButtons;

  final AnimatedIconData animatedIconData;
  final int durationAnimation;
  final Color colorStartAnimation;
  final Color colorEndAnimation;
  final String? tooltip;
  final Curve curve;
  final double spaceBetween;

  /// The [fabButtons] and [animatedIconData] arguments must not be null.
  /// The [durationAnimation], [colorStartAnimation], [colorEndAnimation],
  /// [curve] and [spaceBetween] default to the value given by the library
  /// but also the should not be null.
  //

  final Function(bool isOpen) isOpenCallBack;

  AnimatedFloatingActionButton({
    Key? key,
    required this.fabButtons,
    required this.animatedIconData,
    this.durationAnimation = 300,
    this.colorStartAnimation = Colors.blue,
    this.colorEndAnimation = Colors.red,
    this.curve = Curves.easeOut,
    this.spaceBetween = -5.0,
    this.tooltip = 'toggle',
    required this.isOpenCallBack
  })  : assert(
          durationAnimation > 150 && durationAnimation < 1250,
          'The duration of the animation should be '
          'greater than 150 and smaller than 12500.',
        ),
        assert(
          fabButtons.isNotEmpty,
          'The number of FABs should be more than 1 FAB.',
        ),
        assert(
          spaceBetween <= -5,
          'This is a space between the FABs when they are expanded, '
          'and the value should be lower than or '
          'equal to -5 to have a reasonable space between them.',
        ),
        super(key: key);

  @override
  AnimatedFloatingActionButtonState createState() =>
      AnimatedFloatingActionButtonState();
}

class AnimatedFloatingActionButtonState
    extends State<AnimatedFloatingActionButton>
    with SingleTickerProviderStateMixin {
  /// AnimationController object to control over the whole animation.
  late AnimationController _animationController;
  late Animation<Color?> _buttonColor;
  late Animation<double> _animateIcon;
  late Animation<double> _translateButton;
  double _fabHeight = 50.0;
  bool _isOpened = false;

  bool get isOpened => _isOpened;

  @override
  initState() {
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(
        milliseconds: widget.durationAnimation,
      ),
    )..addListener(
        () {
          /// We here changing the state of the widget
          /// upon any changes from animation controller.
          setState(() {});
        },
      );

    /// This Tween is to animate the icon of the main FAB.
    _animateIcon = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      _animationController,
    );

    /// This ColorTween is to animate the background Color of main FAB.
    _buttonColor = ColorTween(
      begin: widget.colorStartAnimation,
      end: widget.colorEndAnimation,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          0.00,
          1.00,
          curve: Curves.linear,
        ),
      ),
    );

    /// This Tween is to animate position of the current fab
    /// according to its position in the list.
    _translateButton = Tween<double>(
      begin: 0,
      end: 80,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          0.0,
          0.75,
          curve: widget.curve,
        ),
      ),
    );

    _animateIconController = AnimateIconController();

    super.initState();
  }

  @override
  dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: _buildFABs(),
    );
  }

  AnimateIconController? _animateIconController;

  Widget _buildMainFAB() {
    return Container(
      width: 60 ,
      height:60 ,
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isOpened ? Colors.white : Colors.transparent),
      child: AnimateIcons(
          startIconColor: Colors.white,
          endIconColor: Colors.transparent,
          startIcon: Image.asset(AssetsRes.AI_ENTER_ICON ,width: 56,),
          endIcon: Image.asset(AssetsRes.AI_CLOSE_ICON , width: 56, ),
          size: 56,
          onStartIconPress: (){
            _animateFABs();
            return true;
          },
          duration: const Duration(milliseconds: 300),
          onEndIconPress: (){
            _animateFABs();
            return true;

          }, controller: _animateIconController!),
    );
  }

  List<Widget> _buildFABs() {
    List<Widget> processButtons = [];

    if(_isOpened){
      var transions = [const Offset(-25, 50),
        const Offset(-70, 50)];
      for (int i = 0; i < widget.fabButtons.length; i++) {
        processButtons.add(
          TransformFloatActionButton(
            floatButton: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                widget.fabButtons[i],
              ],
            ),
            translateOffset: transions[i],
          ),
        );
      }
    }

    // var transions = [const Offset(-35, 30),
    //   const Offset(-70, 50)];
    // for (int i = 0; i < widget.fabButtons.length; i++) {
    //   processButtons.add(
    //     Opacity(
    //       opacity: _animateIcon.value,
    //       alwaysIncludeSemantics: true,
    //       child: TransformFloatActionButton(
    //         floatButton: Row(
    //           mainAxisAlignment: MainAxisAlignment.end,
    //           children: [
    //             widget.fabButtons[i],
    //           ],
    //         ),
    //         translateOffset: transions[i],
    //       ),
    //     ),
    //   );
    // }
    processButtons.add(_buildMainFAB());
    return processButtons;
  }

  void _animateFABs() {
    if (!_isOpened) {
      _animationController.forward();
      _animateIconController?.animateToStart();
    } else {
      _animationController.reverse();

      _animateIconController?.animateToStart();
    }
    _isOpened = !_isOpened;

    widget.isOpenCallBack(isOpened);
  }

  /// This method is visible from outside of this state widget throw
  /// GlobalKey<AnimatedFloatingActionButtonState>() object which is created
  /// and assign as a key object to [AnimatedFloatingActionButton] by user
  /// to close the [fabButtons] list.
  void closeFABs() {
    _animateFABs();
  }
}
