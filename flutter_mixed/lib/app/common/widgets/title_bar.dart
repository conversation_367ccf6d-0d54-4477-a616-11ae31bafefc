import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../config/config.dart';

/// 通用的title bar
class ToolBar extends StatelessWidget {
  final Color? backgroundColor;

  final Function? backFunction;

  final String? title;

  final Widget? titleWidget;

  final Widget? body;

  final List<Widget>? actions;

  final bool? showToolbar;

  final bool? isLightStatus;

  final Color? appBarBackgroundColor;

  final String? backIcon;

  const ToolBar(
      {super.key,
      this.backgroundColor,
      this.backFunction,
      this.title,
      this.titleWidget,
      this.body,
      this.actions,
      this.showToolbar = true,
      this.isLightStatus = false,
      this.appBarBackgroundColor,
      this.backIcon = 'assets/images/3.0x/pic_return.png'
      });

  @override
  Widget build(BuildContext context) {
    SystemUiOverlayStyle overlayStyle;
    if(isLightStatus == true){
      overlayStyle = const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.light,
      );
    }else{
      overlayStyle = const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      );
    }


    return Scaffold(
      backgroundColor: backgroundColor ?? ColorConfig.backgroundColor,
      extendBodyBehindAppBar: true,
      appBar: showToolbar == true ? PreferredSize(
          preferredSize: const Size.fromHeight(44),
          child: AppBar(
            leading: IconButton(
              onPressed: () {
                if (backFunction != null) {
                  backFunction!();
                } else {
                  Get.back();
                }
              },
              icon: SizedBox(
                width: 24,
                height: 24,
                child: Image.asset(backIcon??""),
              ),
            ),
            centerTitle: true,
            title: titleWidget ??
                Text(
                  title ?? '',
                  style: const TextStyle(fontSize: 16, color: ColorConfig.mainTextColor,fontWeight: FontWeight.w500),
                ),
            elevation: 0.25,
            backgroundColor: appBarBackgroundColor ?? ColorConfig.whiteColor,
            actions: actions,
            systemOverlayStyle: overlayStyle,
          )) : null,
      body: SafeArea(child: body ?? Container()),
    );
  }
}

final navigationBackIcon = SizedBox(
  width: 24,
  height: 24,
  child: Image.asset('assets/images/3.0x/pic_return.png'),
);
