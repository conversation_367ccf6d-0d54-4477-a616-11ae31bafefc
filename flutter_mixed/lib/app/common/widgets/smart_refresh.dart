import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';


///
smartRefreshFooter() {
  return CustomFooter(
    builder: (BuildContext context, LoadStatus? mode) {
      Widget? body;
      if (mode == LoadStatus.idle) {
        body = Text( '下拉刷新');
      } else if (mode == LoadStatus.loading) {
        body = CupertinoActivityIndicator();
      } else if (mode == LoadStatus.failed) {
        body = Text('加载失败，请重试!');
      } else if (mode == LoadStatus.canLoading) {
        body = Text('加载更多');
      } else {
        body = Text('没有更多数据了');
      }
      return SizedBox(
        height: 55.0,
        child: Center(child: body),
      );
    },
  );
}

smartRefresherHeader() {
  return CustomHeader(builder: (BuildContext context, RefreshStatus? mode) {
    if(mode == RefreshStatus.idle){
      return Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.arrow_downward),
            5.gap,
            Text('下拉刷新')
          ],
        ),
      );
    }else if(mode == RefreshStatus.canRefresh){
      return Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.refresh),
            5.gap,
            Text('松开手刷新')
          ],
        ),
      );
    }else if(mode==RefreshStatus.refreshing){
      return Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CupertinoActivityIndicator(),
            5.gap,
            Text('刷新中')
          ],
        ),
      );
    }else if(mode==RefreshStatus.completed){
      return Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('刷新成功')
          ],
        ),
      );
    }else {
      return Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('刷新失败')
          ],
        ),
      );
    }
  },);
}


class RefreshContainer extends StatelessWidget {

  final bool enablePullUp;

  final bool enablePullDown;

  final Widget child;

  final VoidCallback? onRefresh;

  final VoidCallback? onLoading;

  final RefreshListener controller;

  final Widget? footer;

  RefreshContainer({super.key , required this.enablePullDown
    , required this.enablePullUp , required this.child,this.onRefresh
    ,this.onLoading , required this.controller,this.footer});

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      controller: controller.refreshController,
      enablePullDown: enablePullDown,
      enablePullUp: enablePullUp,
      header: smartRefresherHeader(),
      footer: footer ?? smartRefreshFooter(),
      onRefresh: onRefresh,
      onLoading: onLoading,
      child: child,
    );
  }
}

class RefreshListener {

  final RefreshController refreshController = RefreshController(
    initialRefresh: false,
  );

  final bool? initialRefresh;

  RefreshListener({this.initialRefresh});

  void loadComplete() async {
    refreshController.loadComplete();
  }

  void loadNoData() async {
    refreshController.loadNoData();
  }

  void refreshCompleted() async {
    refreshController.refreshCompleted();
  }

  void requestRefresh() async {
    refreshController.requestRefresh();
  }
}

class RefreshConfig extends StatelessWidget {

  final Widget child;

  const RefreshConfig({super.key , required this.child});

  @override
  Widget build(BuildContext context) {
    return RefreshConfiguration(
        maxOverScrollExtent: 100,
        child: child);
  }

}

class ChatFooter extends StatelessWidget {
  const ChatFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomFooter(
      loadStyle: LoadStyle.ShowAlways,
      builder: (context, mode) {
        if (mode == LoadStatus.loading) {
          return Container(
            height: 30.0,
            // color: Colors.red,
            child: Container(
              height: 20.0,
              width: 20.0,
              child: CupertinoActivityIndicator(),
            ),
          );
        } else
          return Container();
      },
    );
  }
}