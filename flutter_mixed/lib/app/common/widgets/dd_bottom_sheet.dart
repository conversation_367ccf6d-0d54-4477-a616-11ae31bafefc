import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';

class DdBottomSheet extends StatelessWidget {
  final viewH = 46.0;
  List? sheetList = [];
  Function? onSelectPressed;
  Function? onCancelPressed;
  DdBottomSheet({this.sheetList, this.onSelectPressed, this.onCancelPressed});
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(alignment: Alignment.center, color: ColorConfig.maskColor),
          ),
          Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              height: (viewH + 0.5) * ((sheetList?.length ?? 0) + 1) +
                  8 +
                  DeviceUtils().bottom.value,
              child: Container(
                  decoration: const BoxDecoration(
                    color: ColorConfig.whiteColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Column(
                    children: _buildSheetContent(),
                  )))
        ],
      ),
    );
  }

  _buildSheetContent() {
    if (sheetList == null) return [];
    List<Widget> lists = [];
    for (var i = 0; i < sheetList!.length; i++) {
      Map sheetMap = sheetList![i];
      lists.add(InkWell(
        onTap: () {
          Get.back();
          if (onSelectPressed != null) {
            onSelectPressed!(i);
          }
        },
        child: Column(
          children: [
            Container(
              width: double.infinity,
              height: viewH,
              alignment: Alignment.center,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (!StringUtil.isEmpty(sheetMap['icon'])) ...[
                    Image.asset(
                      sheetMap['icon'] ?? '',
                      width: 20,
                      height: 20,
                    ),
                    8.gap,
                  ],
                  Text(
                    sheetMap['name'] ?? '',
                    style: const TextStyle(
                        fontSize: 16, color: ColorConfig.mainTextColor),
                  )
                ],
              ),
            ),
            if (i != sheetList!.length - 1) ...[
              Container(
                color: ColorConfig.backgroundColor,
                height: 0.5,
              )
            ]
          ],
        ),
      ));
    }
    lists.add(Container(
      color: ColorConfig.backgroundColor,
      height: 8,
    ));

    lists.add(InkWell(
      onTap: () {
        Get.back();
        if (onCancelPressed != null) {
          onCancelPressed!();
        }
      },
      child: Container(
        height: viewH,
        alignment: Alignment.center,
        child: const Text(
          '取消',
          style: TextStyle(fontSize: 16, color: ColorConfig.mainTextColor),
        ),
      ),
    ));
    return lists;
  }
}
