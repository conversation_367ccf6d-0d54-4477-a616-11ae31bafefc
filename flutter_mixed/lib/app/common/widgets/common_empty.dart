import 'package:flutter/material.dart';

import '../../../res/assets_res.dart';


/// 空页面
class Empty extends StatelessWidget {
  String? msg;

  Function? reload;

  Empty({Key? key, this.msg , this.reload}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 159,
          height: 137,
          child: Image.asset('assets/images/3.0x/ic_empty_company.png'),
        ),
        SizedBox(
          height: 17,
        ),
        Text(
          msg ?? '网络错误',
          style: TextStyle(fontSize: 13, color: Color(0x66000000)),
        ),
        SizedBox(
          height: 18,
        ),

        reload == null ? Container() :
        MaterialButton(
          padding: const EdgeInsets.only(top: 10 , bottom: 10 , left: 48 , right: 48),
            color: const Color(0xff29A0F2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8))
            ),
            child: Text(
              '点击重试',
              style: TextStyle(fontSize: 16, color: Colors.white),
            ),
            onPressed: () => reload!()),
      ],
    );
  }
}
