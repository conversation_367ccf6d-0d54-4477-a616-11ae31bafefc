import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/widgets/common_empty.dart';


mixin PageStatus {

  LoadState loadStatus = LoadState.loading;

  loadSuccess() => loadStatus = LoadState.success;

  loadError() => loadStatus = LoadState.error;

  loadEmpty() => loadStatus = LoadState.empty;

  loading() => loadStatus = LoadState.loading;
}


mixin PageLoadWidget {

  loadPage(LoadState loadState , Widget widget , Function reloadAction , { Widget? emptyWidget}) {
    switch (loadState) {
      case LoadState.loading:
        return const Center(
          child: CupertinoActivityIndicator(),
        );
      case LoadState.empty:
        return Center(
          child: emptyWidget ?? Empty(
            msg: '无数据',
          ),
        );
      case LoadState.success:
        return widget;

      case LoadState.error:
        return Center(
          child: Empty(
            msg: '网络错误',
            reload: () => reloadAction(),
          ),
        );
    }
  }
}


enum LoadState {
  loading,
  error,
  empty,
  success,
}