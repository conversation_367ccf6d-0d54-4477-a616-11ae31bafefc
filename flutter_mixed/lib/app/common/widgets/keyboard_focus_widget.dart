
import 'package:flutter/material.dart';
import 'package:flutter_mixed/logger/logger.dart';

// 监听软键盘
class KeyBoardWidget extends StatefulWidget {

  Widget child;

  Function changed;

  KeyBoardWidget({super.key , required this.child , required this.changed});

  @override
  State<StatefulWidget> createState() => _KeyBoardState();
}

class _KeyBoardState extends State<KeyBoardWidget> with WidgetsBindingObserver {
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if(mounted){
        setState(() {
          if(MediaQuery.of(context).viewInsets.bottom == 0){
            //关闭键盘
            logger('关闭软键盘');
            widget.changed(0);
          }else{
            //显示键盘
            logger('显示软键盘');
            widget.changed(1);
          }
        });
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}