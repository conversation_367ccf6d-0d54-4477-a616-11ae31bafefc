

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../config/config.dart';


class ImageLoader extends StatelessWidget {

  final String? url;  // 本地url， 远程路径， 占位图等
  final double? width;
  final double? height;
  final double? radius;
  final bool? isCircle;
  final bool? clearMemoryCacheWhenDispose;
  final BoxFit? boxFit;
  final FilterQuality? filterQuality;
  final double? border;
  final String? palaceHolder;
  final int? cacheWidth;
  final int? cacheHeight;

  const ImageLoader({super.key , this.url , this.width ,this.height , this.radius , this.isCircle
    , this.clearMemoryCacheWhenDispose = false
    , this.boxFit , this.filterQuality , this.border
    , this.palaceHolder , this.cacheWidth , this.cacheHeight});

  @override
  Widget build(BuildContext context) => _buildAvatar(context);

  _buildAvatar(BuildContext context) {
    if(StringUtil.isEmpty(url)) {
      return _buildNoneImage();
    }

    if(url!.isBase64Image()){
      return _buildBase64Image();
    }

    if(url!.isRemoteUrl()) {
      return _buildRemote(context);
    }

    if(url!.isAssetsResource()){
      return _buildAssets();
    }

    if(url!.isFile()) {
      return _buildFile(context);
    }
  }

  _buildNoneImage() {
    // 滑动期间 执行 ！！！
    return SizedBox(
      width: width ,
      height: height ,
      child: palaceHolder != null ?
      Image.asset(palaceHolder!, width: 160,height: 120,)
          : Image.asset('assets/images/3.0x/contact_moren_head.png', width: width,height: height,)
    );
  }

  _buildFile(BuildContext context) {
    if(isCircle == true){
      return Container(
          width: width,
          height: height,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(36),
            child: ExtendedImage.file(File(url!),
              gaplessPlayback: true,
              clearMemoryCacheWhenDispose: clearMemoryCacheWhenDispose ?? false,
            ),
          )
      );
    }

    return SizedBox(
      width: width,
      height: height,
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(radius ?? 0)),
        child: ExtendedImage.file(File(url!),
          clearMemoryCacheWhenDispose: clearMemoryCacheWhenDispose ?? false,),
      )
    );
  }

  _buildAssets() {
    if(isCircle == true){
      return SizedBox(
          width: width,
          height: height,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(36),
            child: Image.asset(url!),
          )
      );
    }
    return SizedBox(
      width: width ,
      height: height ,
      child: radius != null ? ClipRRect(
        borderRadius: BorderRadius.circular(radius ?? 10),
        child: Image.asset(url!),
      ) : Image.asset(url!),
    );
  }

  _buildRemote(BuildContext context) {
    return Container(
        width: width ,
        height: height ,
        child: ExtendedImage.network(
          url!,
          borderRadius: BorderRadius.all(Radius.circular( isCircle == true ? 0 : (radius ?? 15))),
          clearMemoryCacheWhenDispose: clearMemoryCacheWhenDispose ?? false,
          fit: boxFit ?? BoxFit.cover,
          enableLoadState: false,
          shape: isCircle == true ? BoxShape.circle : BoxShape.rectangle,
          filterQuality: filterQuality ?? FilterQuality.low,
          cacheWidth: (cacheWidth) ,
          cacheHeight: (cacheHeight) ,
          cache: true,
          gaplessPlayback: true,
          border : border == null ? null : Border.all(color: ColorConfig.btnGrayColor, width: border ?? 0),
          loadStateChanged: (state){
            switch (state.extendedImageLoadState) {
              case LoadState.loading:
                return Stack(
                  children: [
                    Center(
                      child: palaceHolder != null ?
                      Transform.scale(
                        scale: 1.2,
                        child: Image.asset(palaceHolder!, width: 160,height: 120,fit: BoxFit.cover,),
                      ): Container()
                    ),
                    Container(
                      alignment: Alignment.center,
                      child: const CupertinoActivityIndicator(),
                    ),
                  ],
                );

              case LoadState.completed:
                return state.completedWidget;

              case LoadState.failed:
                return palaceHolder != null ?
                  Image.asset(palaceHolder!, width: 160,height: 120, fit: BoxFit.cover,)
                : Image.asset('assets/images/3.0x/contact_moren_head.png', width: width,height: height,);
            }
          },
        )
    );
  }

  _buildBase64Image() {
    Uint8List bytes = base64.decode(url!.split(',')[1]);
    if(isCircle == true){
      return SizedBox(
          width: width,
          height: height,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(36),
            child: Image.memory(bytes),
          )
      );
    }
    return SizedBox(
      width: width ,
      height: height ,
      child: radius != null ? ClipRRect(
        borderRadius: BorderRadius.circular(radius ?? 10),
        child: Image.memory(bytes),
      ) : Image.memory(bytes),
    );
  }

}

extension ImageUrlExtension on String {

  bool isNone() {
    return StringUtil.isEmpty(this);
  }

  bool isFile() {
    return !(isAssetsResource() && isRemoteUrl());
  }

  bool isAssetsResource() {
    return startsWith('assets');
  }

  bool isRemoteUrl() {
    return startsWith('http');
  }

  bool isBase64Image() {
    return startsWith('data:image/');
  }

}