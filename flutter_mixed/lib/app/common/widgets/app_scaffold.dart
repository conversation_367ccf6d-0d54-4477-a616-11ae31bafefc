
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/theme.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:get/get.dart';

import '../channel/channel.dart';
import '../config/config.dart';

/// 脚手架
class AppScaffold extends StatelessWidget {

  double? appbarHeight;

  String? title;

  bool? showToolBar;

  bool? isHiddenBackBtn;

  VoidCallback? onPressed;

  List<Widget>? actions;

  bool? isExtendBodyBehindAppBar;

  Widget? bottomNavigationBar;

  final Widget body;

  Color? appBarColor;

  AppScaffold({super.key
    , this.title = ''
    , this.showToolBar = true
    , this.appbarHeight
    , this.isHiddenBackBtn = false
    , this.bottomNavigationBar
    , this.isExtendBodyBehindAppBar
    , this.onPressed
    , this.actions
    , this.appBarColor
    , required this.body});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Scaffold(
        backgroundColor: ColorConfig.backgroundColor,
        extendBodyBehindAppBar: isExtendBodyBehindAppBar ?? true,
        appBar: showToolBar == true ? PreferredSize(
            preferredSize: Size.fromHeight(appbarHeight ?? 44),
            child: AppBar(
                title: Text(
                  '$title${flutterTarget()}',
                  style: const TextStyle(
                      color: ColorConfig.mainTextColor, fontSize: 16,fontWeight: FontWeight.w500),
                ),
                centerTitle: true,
                leading: isHiddenBackBtn == true
                    ? Container()
                    : _leading(context, onPressed),
                backgroundColor: appBarColor ??const Color(0xF1FFFFFF),
                elevation: 0,
                systemOverlayStyle: translateOverlayStyle,
                surfaceTintColor: appBarColor ?? const Color(0xF1FFFFFF),
                actions: actions)) : null,
        bottomNavigationBar: bottomNavigationBar,
        body: body,
      ),
    );
  }

  _leading(BuildContext context, VoidCallback? onPressed) {
    return IconButton(
      onPressed: () {
        if (onPressed == null) {
          Get.back();
        } else {
          onPressed();
          if (Platform.isAndroid) {
            Channel().invoke(Channel_call_android_backPage, {});
          }
        }
      },
      icon: SizedBox(
        width: 24,
        height: 24,
        child: Image.asset('assets/images/3.0x/pic_return.png'),
      ),
    );
  }

}

