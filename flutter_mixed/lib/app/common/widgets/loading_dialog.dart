import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// 定义统一的页面弹窗式loading
/// 文字 + 圆形loading
class LoadingDialog extends StatelessWidget {

  final bool? dissable;

  final String? loadingTip;

  const LoadingDialog({Key? key ,this.dissable , this.loadingTip}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async => dissable ?? true,
        child: Material(
          type: MaterialType.transparency,
          child: Center(
            child: SizedBox(
              width: 90.0,
              height: 90.0,
              child: AnimatedOpacity(
                opacity: 1,
                duration: Duration(seconds: 0),
                child: Container(
                  decoration: const ShapeDecoration(
                    // color: Color(0x00ffffff),
                    color: Colors.black38,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(8.0),
                      ),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      const SizedBox(
                        child: CupertinoActivityIndicator(
                          color: Colors.white,
                        ),
                        width: 60,
                        height: 60,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        )
    );
  }
}
