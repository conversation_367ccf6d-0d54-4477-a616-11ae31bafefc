import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/logger/logger.dart';

typedef TapAction = void Function();


class NoDoubleClickFun  {
  final VoidCallback? onPressed;
  final int tapDuration;
  DateTime? lastTapTime;

  NoDoubleClickFun(
      {Key? key,
        required this.tapDuration,
        required this.onPressed,
      }) ;

  void noDoublePressed() {
    if (lastTapTime == null || DateTime.now().difference(lastTapTime!) > Duration(seconds: tapDuration)) {
      lastTapTime = DateTime.now();
      onPressed!();
    } else {
      toast("请勿重复点击");
      logger("=====请勿重复点击====");
    }
  }
}

