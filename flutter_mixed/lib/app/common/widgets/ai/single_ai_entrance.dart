import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/im/web_link/web_link.dart';
import 'package:flutter_mixed/logger/logger.dart';

import '../../../../res/assets_res.dart';
import '../../../retrofit/entity/ai/ai_power_resp.dart';
import '../../base_info/info.dart';

class DraggablePage extends StatefulWidget {

  List<AIPowerItem> aiEntryList;

  DraggablePage({super.key , required this.aiEntryList});

  @override
  _DraggablePageState createState() => _DraggablePageState();
}

class _DraggablePageState extends State<DraggablePage> with SingleTickerProviderStateMixin {
  Offset _position = Offset(0, 0);
  final GlobalKey _parentKey = GlobalKey();
  Size _screenSize = Size(0, 0);
  final double _buttonSize = 50.0;
  late AnimationController _controller;
  Offset? _targetPosition;


  // 新增的边缘间距
  final double _edgePadding = 10.0; // 左右边缘间距
  double _verticalPadding = 70.0; // 上下边缘间距

  @override
  void initState() {
    super.initState();

    _verticalPadding = DeviceUtils().bottom.value + 56;

    _controller = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    )..addListener(_updatePosition);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenSize = MediaQuery.of(context).size;
      logger('screenSize = $screenSize');

      setState(() {
        _screenSize = screenSize;
        _position = Offset(screenSize.width - _buttonSize -_edgePadding - 15,
            screenSize.height - _buttonSize - _verticalPadding - 60 - 15);
      });
    });
  }

  void _updatePosition() {
    if (_targetPosition != null) {
      setState(() {
        _position = Offset.lerp(_position, _targetPosition!, _controller.value)!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      key: _parentKey,
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          Positioned(
            left: _position.dx,
            top: _position.dy,
            child: RawGestureDetector(
                gestures: <Type, GestureRecognizerFactory>{
                  TapGestureRecognizer:
                  GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
                        () => TapGestureRecognizer()..onTap = _handleTap,
                        (TapGestureRecognizer instance) {},
                  ),
                  PanGestureRecognizer:
                  GestureRecognizerFactoryWithHandlers<PanGestureRecognizer>(
                        () => PanGestureRecognizer()
                      ..onUpdate = _handlePanUpdate
                      ..onEnd = _handlePanEnd,
                        (PanGestureRecognizer instance) {},
                  ),
                },
                child: Container(
                    color: Colors.transparent,
                    width: _buttonSize,
                    height: _buttonSize,
                    child: Image.asset(AssetsRes.AI_ENTER_ICON)
                )
            ),
          )
        ],
      ),
    );
  }

  void _handleTap() {
    _clickAction();
    // 在这里处理点击事件
  }

  void _handlePanUpdate(DragUpdateDetails details) {

    setState(() {
      _position += details.delta;
      // 限制上下拖动范围
      _position = Offset(
        _position.dx,
        _position.dy.clamp(_verticalPadding, _screenSize.height - _buttonSize - _verticalPadding),
      );
    });
  }

  void _handlePanEnd(DragEndDetails details) {
    if (_screenSize == null) return;

    // 定义吸附阈值
    double snapThreshold = (_screenSize!.width)/2; // 调整这个值来控制吸附的敏感度，例如设置为按钮宽度的一部分

    double currentX = _position.dx;
    double currentY = _position.dy;

    double snappedX = currentX;
    double snappedY = currentY;

    // 计算距离左边缘和右边缘的距离
    double distanceToLeft = currentX;
    double distanceToRight = _screenSize!.width - currentX - _buttonSize;

    // 左边缘
    if (distanceToLeft < snapThreshold) {
      snappedX = _edgePadding;
    }
    // 右边缘
    else if (distanceToRight < snapThreshold) {
      snappedX = _screenSize!.width - _buttonSize - _edgePadding;
    }

    // 设置目标位置为吸附后的位置
    _targetPosition = Offset(snappedX, currentY); // 只处理水平吸附，Y轴保持不变

    // 重置动画控制器并向前播放，实现平滑过渡
    _controller.reset();
    _controller.forward();
  }

  _clickAction() async {
    var url = await _createUrl('chat');
    logger('url = $url');
    openWebView({
      'url': url,
      'title': '',
      'isWebNavigation': 1,
    });
  }

  _createUrl(String powerId) async {
    var enterItem = widget.aiEntryList.firstWhere((element) => element.powerId == powerId);
    var url = "${enterItem.webUrl}?agent=${enterItem.agent}";
    //获取platform
    int platform = Platform.isIOS ? 2 : 1;
    //获取appVersion
    String appVersion = await BaseInfo().getAppVersion();
    if (url.contains('?')) {
      url = '$url&platform=$platform&appVersion=$appVersion';
    } else {
      url = '$url?platform=$platform&appVersion=$appVersion';
    }
    return url;
  }
}