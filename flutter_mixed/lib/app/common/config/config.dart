import 'dart:io';
import 'dart:ui';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:get/get.dart';

class ColorConfig {
  //页面背景色 背景色
  static const backgroundColor = Color(0xFFF2F3F6);
   //背景色
  static const mybackgroundColor = Color(0xFFE6ECFF);
  //灰色按钮色
  static const btnGrayColor = Color(0xFFBBBEC5);

  //灰色线按钮色
  static const lineColor = Color(0xFFD3D3D3);
  //标题黑色字
  static const mainTextColor = Color(0xE6222222);
  //副黑色字
  static const msgTextColor = Color(0x99222222);
  //内容部分黑色字
  static const desTextColor = Color(0x66222222);
  // 副标题颜色
  static const subTitleTextColor = Color(0xff999999);

  //主题蓝色
  static const themeCorlor = Color(0xFF0168FD);
  static const themeColorSecondary = Color(0xFF2C67EC);
  //删除红色
  static const deleteCorlor = Color(0xFFF53F3F);
  //浅红色
  static const lightRedCorlor = Color(0xFFFCE9E7);
  static const blackColor = Color.fromARGB(255, 0, 0, 0);
  static const whiteColor = Color.fromARGB(255, 255, 255, 255);

  //外部联系人
  static const externalLevelOne = Color(0xFF5D5FEF);
  static const externalLevelNormal = Color(0xFF46D5B3);

  //通讯录首页
  static const newFriendColor = Color(0xFFE97911);
  static const friendColor = Color(0xFF38E3F0);
  static const approveGreenColor = Color(0xFF1EB906);

  static const maskColor = Color(0x66222222);//透明页面蒙版色值

  //im 他人背景色
  static const imOtherBackColor = Color(0xFFF5F5F7);
   //im 我的背景色
  static const imMyBackColor = Color(0xFFD5E1FB);
}

class DeviceUtils with WidgetsBindingObserver {
  RxDouble width = _getScreenWidth().obs;
  RxDouble height = _getScreenHeight().obs;
  RxDouble top = _getDeviceTop().obs;
  RxDouble bottom = _getDeviceBottom().obs;

  static final DeviceUtils _instance = DeviceUtils._init();

  factory DeviceUtils() {
    return _instance;
  }

  DeviceUtils._init() {
    WidgetsBinding.instance.addObserver(this);
  }
  @override
  void didChangeMetrics() {

    width.value = _getScreenWidth();
    height.value = _getScreenHeight();
    top.value = _getDeviceTop();
    bottom.value = _getDeviceBottom();
  }

  static Future getUDIDStr() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor!;
    } else {
      try{
        dynamic deviceid = await Channel().invoke(Channel_getNativeDeviceId, {});
        if(deviceid != null){
          return deviceid['deviceId'];
        }else{
          AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
          return androidInfo.id;
        }
      }catch(e){
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id;
      }
    }
  }

  static getSystemVersion() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return 'iOS ${iosInfo.systemVersion}';
    } else {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.release;
    }
  }

    static getDeviceName() async {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return iosInfo.model;
      } else {
        try{
          String deviceModel = await Channel().invoke(Channel_getNativeDeviceModel, {});
          return deviceModel;
        }catch(e){
          AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
          return androidInfo.model;
        }

      }
  }

  /*
   * 获取屏幕宽 单位是dp
   */
  static double _getScreenWidth() {
    return MediaQueryData.fromWindow(window).size.width;
  }

  /*
   * 获取屏幕高 单位是dp
   */
  static double _getScreenHeight() {
    return MediaQueryData.fromWindow(window).size.height;
  }

  /*
   * 获取屏幕像素密度
   */
  static double _getDevicePixelRatio() {
    return MediaQueryData.fromWindow(window).devicePixelRatio;
  }

  /*
   * 获取上边距的值。(主要用于刘海屏)
   */
  static double _getDeviceTop() {
    return MediaQueryData.fromWindow(window).padding.top;
  }

  /*
   * 获取下边距的值。(主要用于刘海屏)
   */
  static double _getDeviceBottom() {
    return MediaQueryData.fromWindow(window).padding.bottom;
  }
}
