//import 'package:dd_applifecycle/AppLifeManager.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification.dart';
import 'package:flutter_mixed/app/utils/cache_helper.dart';
import 'package:mmkv/mmkv.dart';

import '../../modules/helper/wechat.dart';
import '../../statis/statics_log.dart';
import '../../statis/statistics_helper.dart';
import '../../../logger/logger.dart';
import '../../utils/storage.dart';
import '../api/Define.dart';
import '../channel/channel.dart';

void appInit() async {

  // AppLifeManager().addObserver((String currentState){
  //   logger('currentState====$currentState');
  // });


  MMKV.initialize();

  CacheHelper.initMemoryConfig();

  //禁用放大镜
  TextMagnifier.adaptiveMagnifierConfiguration = TextMagnifierConfiguration(
      shouldDisplayHandlesInMagnifier: false,
      magnifierBuilder: (
          BuildContext context,
          MagnifierController controller,
          ValueNotifier<MagnifierInfo> magnifierInfo,
          ) {
        //禁用返回 null
        return null;
      }
  );

  WechatHelper.instance.register();

  // CustomImageCache();
  // WidgetsFlutterBinding.ensureInitialized();

  //Channel().listener();
  initEasyRefresh();
  WechatHelper.instance.initAndroidWxListener();
  //获取header
  try{
    Channel().invoke(Channel_Native_GetHeader ,{}).then((headerMap) async{
      UserDefault.storeHeader(headerMap);
      logger('=====headerMap====$headerMap');
      var r = await UserDefault.setData(Define.HTTPHEADER, headerMap);
    });
  }catch(e){}

  LocalNotification().initLocalNotification();
  StatisticsHelper.init();
}

var easyHeader = const ClassicHeader(
  textStyle: TextStyle(fontSize: 16),
  messageStyle: TextStyle(fontSize: 12),
  dragText: '下拉刷新',
  armedText: '松开刷新',
  readyText: '刷新中',
  processingText: '刷新中...',
  processedText: '完成加载',
  noMoreText: '没有更多数据了',
  failedText: '失败了',
  messageText: '上次更新时间 %T',
);

void initEasyRefresh() {
  EasyRefresh.defaultHeaderBuilder = () => easyHeader;
  EasyRefresh.defaultFooterBuilder = () => const ClassicFooter(
      textStyle: TextStyle(fontSize: 15),
      messageStyle: TextStyle(fontSize: 12),
      dragText: '上拉加载',
      armedText: '松开刷新',
      readyText: '加载中...',
      processingText: '加载中...',
      processedText: '完成加载',
      noMoreText: '没有更多了',
      failedText: '加载失败',
      messageText: '上次更新时间 %T',
      infiniteHitOver: true
  );
}

var isLoginPage = false;
