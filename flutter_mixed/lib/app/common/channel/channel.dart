import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/event/event.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/constant/ImMsgConstant.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/im/ext/message_ext.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification_payLoad_model.dart';
import 'package:flutter_mixed/app/im/local_notification/notification_payLoad_ext.dart';
import 'package:flutter_mixed/app/im/local_notification/notification_task.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/utils/uuid_util.dart';
import 'package:flutter_mixed/app/modules/login/loginView/controllers/login_controller.dart';
import 'package:flutter_mixed/app/modules/news/newsHome/controllers/news_home_controller.dart';
import 'package:flutter_mixed/app/modules/real_name/auth_status/auth_status_controller.dart';
import 'package:flutter_mixed/app/modules/real_name/input_idcard/real_name_controller.dart';
import 'package:flutter_mixed/app/modules/workStand/workFlow/controllers/work_flow_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/cache_helper.dart';
import 'package:flutter_mixed/app/utils/login_util.dart';
import 'package:flutter_mixed/main.dart';
import 'package:flutter_mixed/scheme/scheme_helper.dart';
import 'package:get/get.dart';

import '../../../logger/logger.dart';
import '../../im/bridge_engine/bridge_engine.dart';
import '../../im/dispatch_parser/multi_sync_event_model.dart';
import '../../modules/home/<USER>/home_controller.dart';
import '../../utils/storage.dart';
import '../api/Define.dart';
import '../dialog/reset_login_dialog.dart';

class Channel {
  static Channel? _instance;

  Channel._();
  factory Channel() {
    return instance;
  }

  static Channel instance = Channel._();

  static LocalNotiPayLoadModel? notificationModel;
  final _channel = const MethodChannel("samples.flutter.io/flutterCallNative");

  // static const eventChannel =
  //     const MethodChannel("samples.flutter.io/nativeCallFlutter");

  Future invoke(String method, [Map? data]) async {
    try {
      return await _channel.invokeMethod(method, data);
    } catch (e) {
      // logger(e);
      return null;
    }
  }

  Future invokeMap(String method, [Map? data]) async {
    try {
      return await _channel.invokeMapMethod(method, data);
    } catch (e) {
      // logger(e);
      return null;
    }
  }

  listener() {
    _channel.setMethodCallHandler(flutterMethod);
  }

  Future<dynamic> flutterMethod(MethodCall call) async {
    logger('native 调用 flutter： method ==》 ${call.method} : ${call.arguments}');
    switch (call.method) {
      case 'loginOut':
        // 清除本地token
        logger('loginOut....清除本地token ...');
        await LoginUtil.reLogin(invokeNative: false);
        Get.offAllNamed(Routes.LOGIN);
        break;

      case 'jumpRoute':
        var routeName = call.arguments['route'];
        var arguments = call.arguments['arguments'];

        logger('....$routeName ..${arguments}');

        //Get.toNamed('/create-and-join');
        if (Platform.isAndroid) {
          Get.offAndToNamed(routeName, arguments: arguments ?? {});
          // Get.offAllNamed(routeName, arguments: arguments ?? {});
          // Get.offAll(routeName, arguments: arguments ?? {});
        } else {
          Get.toNamed(routeName, arguments: arguments ?? {});
        }

        break;

      case 'refreshOrgData':
        //刷新公司数据
        HomeController homeController = Get.find();
        homeController.getAllCompanies();

        break;

      // 场景：im 断开后，回到前台无法收到对应推送，强制刷新工作台审批和其他工单的红点
      case "forceRefreshWorkbench":
        bool isWorkFlow = Get.isRegistered<WorkFlowController>();
        if (isWorkFlow) {
          WorkFlowController workFlowController = Get.find();
          workFlowController.forceRefresh();
        }

        bool homeControllerIsReg = Get.isRegistered<HomeController>();
        if (homeControllerIsReg) {
          HomeController homeController = Get.find();
          homeController.getAllUnread();
        }

        break;

      // APP回到前台
      case 'onAppResumed':
        logger('onAppResumed');
        ImClientManager.instance.reConnect();
        eventBus.fire(ResumeSender());
        try {
          HomeController homeController = Get.find();
          homeController.getAllCompanies();
          homeController.getAllGroup();
          homeController.getSixMergeData();
        } catch (e) {}
  
        break;

      // APP退到后台
      case 'onAppPaused':
        try {
         eventBus.fire(EventAppLifeChange("onAppPaused"));
        } catch (e) {
          e.printError();
        }

        break;

      case 'refreshUserData':
        //刷新个人数据
        HomeController homeController = Get.find();
        homeController.getUserInfo();
        break;
      case 'refreshFriendData':
        //刷新好友数据
        HomeController homeController = Get.find();
        homeController.getFriendList();
        break;
      case 'refreshGroupData':
        //刷新群组数据
        HomeController homeController = Get.find();
        homeController.getAllGroup();
        break;
      case 'backSignatrueBase64':
        eventBus.fire(call.arguments);
        break;
      case 'nativeJumpArgument':
        Map argument = call.arguments;
        argument['native'] = 1;

        // Get.offAllNamed('/approve-detail',arguments: argument);
        Future.delayed(Duration(milliseconds: 100)).then((value) {
          eventBus.fire(argument);
        });

        break;

      case 'androidOnPressed':
        // android 点击返回键处理
        try {
          if (Platform.isAndroid) {
            var currentRoute = Get.currentRoute;
            if (currentRoute == Routes.HOME) {
              return 1;
            } else if(currentRoute == Routes.WEB_VIEW){
              Channel.instance.invoke('webviewGoBack');
              return 0;
            }else if (currentRoute == Routes.REAL_NAME ) {
              RealNameController realNameController = Get.find();
              if(realNameController.canBack){
                Get.back();
              }
              return 0;
            }else if (currentRoute == Routes.UPLOAD_PHOTO ) {
              return 0;
            }else if (currentRoute == Routes.REALNAME_STATUS ) {
              AuthStatusController authStatusController = Get.find();
              if(authStatusController.canBack){
                Get.until((route) => route.settings.name == Routes.HOME);
              }
              return 0;
            }else {
              Get.back();
              return 0;
            }
          }
        } catch (e) {
          logger(e);
        }

        break;
      case 'wexinLogin':
        LoginController loginController = Get.find();
        loginController.wxLoginAuth(call.arguments!['code']);
        break;
      case 'tokenError':
        //token失效或被踢出
        await LoginUtil.reLogin(invokeNative: true);
        dynamic msg = call.arguments['msg'];
        showReLoginDialog(msg);
        break;

      // 不接收native的刷新
      // case 'unReadMsgCountUpdate':
      //   dynamic type = call.arguments['type'];
      //   dynamic count = call.arguments['count'];
      //   if (type is int && count is int) {
      //     if (type == 0) {
      //       try {
      //         HomeController homeController = Get.find();
      //         homeController.updateMsgUnread(count);
      //       } catch (e) {}
      //     }
      //   }
      //   break;
      case 'newsHomeShowMemu':
        bool isHaveNews = Get.isRegistered<NewsHomeController>();
        if (isHaveNews) {
          double offsetY = call.arguments['offsetY'];
          NewsHomeController mineHomeController = Get.find();
          mineHomeController.popComponent(offsetY - 2);
        }
        break;
      case 'receiveMeetingDeleted': //会议被删除 或 解散
        String meetingId = call.arguments['meetingId'];
        eventBus.fire({'receiveMeetingDeleted': meetingId});
        eventBus.fire({'refreshMeetingList': 1});

        break;
      case 'enterMeeting': //进入会议 type:0无会议 1主持人 2参会人
        String meetingId = call.arguments['meetingId'];
        try {
          HomeController homeController = Get.find();
          homeController.enterMeeting(meetingId);
        } catch (e) {}

        break;
      case 'userdefault_tokenKey': //同步token数据
        UserDefault.setData(Define.TOKENKEY, call.arguments);
        // tokenKey 的 vlaue 数据格式
        // {access_token: 6414ef0d-5dfe-4e9a-a051-7420f5aef93a, token_type: bearer, refresh_token: 993536ce-c302-412f-98b9-6a9f302e735f, expires_in: 2591999, scope: all, loginTime: 1726217855918, userId: 2468510355769497597, mobile: 19903165454, name: 张一山111, avatar: https://cdn.ddbes.com/HEAD/2632002443419845629, gender: 2, birthday: 1991-01-09, intro: , address: 河北省-廊坊市-其他, email: <EMAIL>, profession: 人事专员, companyId: 2631043482449347581, openId: , imId: 20thFrKEoNu, nickName: 开心😁, orgName: 张一山公司}

        break;
      case 'clearMemoryImageCache':
        CacheHelper.clear();
        break;

      case CHANNEL_OFFLINE_NOTIFICATION_ROUTE:
        // native 调用 Flutter 传递离线推送数据，进入单群聊通知等页面
        Map<String, dynamic> argument = call.arguments.cast<String, dynamic>();
        var model = LocalNotiPayLoadModel.fromJson(argument);
        NotificationTask.cachePayLoadModel= model;
        try {
          bool isRegistered = Get.isRegistered<HomeController>();
          if (isRegistered) {
            await model.didClickNotification();
          }
          
        } catch (e) {
          logger(e);
        }
        
        break;

      case CHANNEL_SENDIMMSG:
        //发送IM消息
        logger('====call.argument==${call.arguments}');

        try {
          Map<String, dynamic> argument =
              call.arguments.cast<String, dynamic>();
          String ownId = await UserHelper.getUid();
          String ownImId = await UserHelper.getOwnImId();
          if (argument['msgType'] ==
              ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange) {
            //参数extentdOne,msgType
            ImClientManager.instance.sendSynchronous(
                ownId,
                ownImId,
                argument[
                    'extendOne']); //extentdOne Map类型{'handleType':$handleType,'handleId':$handleId}
          } else {
            //sessionId,text,callType,callContent,meetingId,msgType,发起多了voiceUrl
            if (argument['callContent'] == null) {
              //SSChatMessageTypeInitiateVoiceVideo callContent为本人姓名
              argument['callContent'] = await UserHelper.getOwnName();
            }
            Message message = Message.fromJson(argument);
            message.msgId = getUUid();
            if (message.sessionId == null || argument['msgType'] == null) {
              return;
            }
            var session = await DbHelper.getSessionByOwnerId2SessionId(
                ownId, message.sessionId!);
            logger('接收 msgType = ${argument['msgType']} ,session :${session}');
            if (session == null) return;
            Message localMsg = await session.cacheAudioAndVideoCallMsg(message);
            if (message.isAudioAndVideoCall()) {
              session.sendAudioAndVideoSocket(localMsg);
            }
            if (message.msgType !=
                ConstantImMsgType.SSChatMessageTypeVoiceVideoRefused) {
              bool isHaveChat = Get.isRegistered<ChatController>();
              if (isHaveChat) {
                ChatController? chatController =
                    Get.find<ChatController>(tag: session.sessionId);
                if (chatController != null) {
                  chatController.updateSession(localMsg);
                }
              }
            }
          }
        } catch (error) {
          logger('===error===$error');
        }

        break;
      case CHANNEL_REFRESH_MEETING_CHECK:
        // native 刷新 群组顶部 音视频状态 ，  挂断
        Map<String, dynamic> argument = call.arguments.cast<String, dynamic>();
        var sessionId = argument['sessionId'];

        bool isHaveChat = Get.isRegistered<ChatController>();
        if (isHaveChat) {
          ChatController? chatController =
              Get.find<ChatController>(tag: sessionId);
          if (chatController != null) {
            chatController.checkImCallDetail();
          }
        }
        break;
      case Channel_iOS_upLoad:
        //个人资料头像上传、扫码签名上传、考勤拍照打卡
        try {
          Map<String, dynamic> argument =
              call.arguments.cast<String, dynamic>();
          return await _nativeUpload(argument);
        } catch (error) {
          return {'method': Channel_iOS_upLoad};
        }

      case CHANNEL_MAIN_ENGINE_BRIDGE:
        // 子引擎想要调用主引擎的方法，进一步的桥接
        invokeMainEngine(call.arguments);

        break;

      case 'deleteKingdeeApprove':
        //删除金蝶审批此条数据
        eventBus.fire({'deleteKingdeeApprove':call.arguments});
        break;

      case 'close_current_page':
        Get.back();
        break;

      case GET_TIANDITU_POSITION:
        // native webview 返回了天地图信息
        var tiandituParam = call.arguments;
        logger('GET_TIANDITU_POSITION =》 ${tiandituParam}');
        if(tiandituParam is Map){
          var body = EventTianditu(tiandituParam);
          eventBus.fire(body);
        }

        break;
      case OPEN_FLUTTER:
        SchemeHelper.schemeNavigate(call.arguments);

        break;

      case FLUTTER_SOCKET_BYTE:
        if(call.arguments['data'] != null){
          var data = call.arguments['data'];
          ImClientManager.instance.handleMessage(data);
        }
        break;

      case FLUTTER_SOCKET_STATUS:
        // 0 失败， 1 成功， -1 断开 
        if(call.arguments['status'] != null){
          var status = call.arguments['status'];
          ImClientManager.instance.handSocketStatus(status);
        }
        break;

      case FETCH_SOCKET_PARAM:
        // 原生的长连接状态变化，向flutter获取 imtoken 和 url，进行重连等
        ImClientManager.instance.handSocketStatus(2);
        var param = await ImClientManager.instance.responseSocketParam();
        if(Platform.isIOS){
          param['method'] = FETCH_SOCKET_PARAM;
        }
        return param;
      default:
        break;
    }
  }

  Future<Map<String, dynamic>?> _nativeUpload(
      Map<String, dynamic> argument) async {
    //method 方法名 用于区分flutter返回值的用途(目前只有上传用到) backValue null失败
    String? filePath = argument['filePath']; //存储路径
    String? object = argument['object']; //头像为路径 其他需要field的不传
    int? type = argument[
        'type']; //type 0:bucket用于审批 1:userBucket用于组织、个人、群组头像 2:imBucket用于im 3:考勤打卡
    argument['method'] = Channel_iOS_upLoad;
    try {
      if (type == null || filePath == null) {
        return argument; //原生判断无backValue参数则为失败
      }
      await CosManager().initPans(isAttendance: type == 3);
      String? bucket = await CosManager().bucket();//type 0和3取bucket
      if (type == 1) {
        bucket = await CosManager().userBucket();
      }
      if (type == 2) {
        bucket = await CosManager().imBucket();
      }
      var backUrl =
          await CosUploadHelper.nativeUpload(filePath, object, bucket,isAttendacne: type == 3);
      if (backUrl != null) {
        argument['backValue'] = backUrl;
      }
      return argument;
    } catch (e) {
      return argument;
    }
  }
}

// channel login 账号 相关
const String Channel_LoginInfoFeedback = 'login'; // 登录成功后 userinfo + token 返给原生
const String Channel_minePageJumpNext =
    'minePageJumpNext'; // 我的页面 几个入口跳转原生（个人资料，设置，关于）
const String Channel_logOut = "logOut"; // 场景： 网络拦截器中弹出框，提示退出登录
const String Channel_didClickLoginOut =
    "didClickloginOut"; // 场景： 网络拦截器中弹出框，提示退出登录

//channel 工作台页面相关
const String Channel_Native_attendance = "native_page/attendance"; // 考勤
const String Channel_Native_attendance_setting =
    "native_page/attendance_setting"; // 考勤设置
const String Channel_Native_announce =
    "native_page/announce"; //type 0立即创建1更多2详情 isHaveAuth是否有组织权限 orgId公司id
//channel 会议相关
const String Channel_jumpMeeting = "jumpMeeting";
const String Channel_callNative_jumpRoute = 'callNative_jumpRoute';

const String Channel_Native_attendance_rule =
    "native_page/attendance_rule"; // 跳转考勤规则
const String Channel_Native_attendance_adjust =
    "native_page/attendance_adjust"; // 特殊工作日调整
const String Channel_Native_permissions = "native_page/permissions"; //权限详情

const String CHANNEL_MAIN_ENGINE_BRIDGE = "channel_receive_main_engine_bridge";
const String BRIDGET_TO_MAIN_NATIVE_ENGINE = "bridget_to_main_native_engine";

// channel native weibview 相关
const String Channel_openWebView =
    "jumpWebView"; //url跳转链接，title跳转WebView的标题，isWebNavigation:0不是web的标题栏1web标题栏，orgId公司id

//channel 跳转签名
const String Channel_Native_Approve_sign = "native_page/signatrue"; // 跳转签名页面

//channel 跳转定位
const String Channel_Native_Approve_Postion = "native_page/position"; // 跳转定位页面
const String Channel_Native_Approve_PostionAuto =
    "native_page/position_auto"; // 获取当前定位

//channel 原生返回
const String Channel_Native_Back = "native_page/goback"; // 原生返回
const String Channel_Native_RefreshCurrentOrg =
    "native_page/refreshCurrentOrg"; // 刷新当前企业

// 获取金蝶token
const String Channel_Native_KingDeeToken =
    "native_page/getKingDeeToken"; // 获取金蝶token

//原生腾讯云上传
const String Channel_Native_UploadFileList = 'native_uploadFileList'; //原生腾讯云上传

//多引擎 通知原生发送刷新好友到主引擎
const String Channel_Native_RereshFriend = "native_page/main_refreshFriend";
//多引擎 通知原生发送刷新组织到主引擎
const String Channel_Native_RereshOrgData = "native_page/main_refreshOrgData";
//多引擎 通知原生发送刷新通讯录红点到主引擎
const String Channel_Native_RereshPending = "native_page/main_refreshPending";
//多引擎 通知原生发送刷新审批未读到主引擎
const String Channel_Native_ApproveUnread = "native_page/main_approveUnread";

const String Channel_Native_Update = "native_page/update"; //点击了立即更新
const String Channel_Native_GetHeader = "native_page/getHeader"; //获取header
const String Channel_getNativeDeviceId = "getNativeDeviceId";
const String Channel_getNativeDeviceModel = "getNativeDeviceModel";

const String Channel_Native_Pop_Root =
    'native_popRoot'; //通知原生navigation Pop To Root控制器

const String reportException = "report_exception";
const String Channel_Native_Qr_Sign = "native_page/qr_sign"; //扫码签功能

const String Channel_userdefult_tokenKey = "userdefault_tokenKey";

const String Channel_data_toNative =
    "data_toNative"; //{'data':'返回数据','type':0}//0:公司数据 1:群组数据 2:好友数据 3:6合一数据 4:IM数据
const String CHANNEL_NATIVE_REGISTRATION_ID = "getNativeImToken"; //6合一接口需要的参数
const String Channel_sendIM_synchronous = "sendIM_synchronous"; //发送IM同步指令

const String Channel_getLocalNotificationSetting =
    "getLocalNotificationSetting";
const String CHANNEL_OFFLINE_NOTIFICATION_ROUTE =
    'channel_offline_notification_route';

// 接收 native 音视频状态 ---------
const String CHANNEL_SENDIMMSG = 'channel_send_im_msg'; //发送im消息
const String CHANNEL_REFRESH_MEETING_CHECK =
    'channel_refresh_meeting_check'; //刷新群组音视频状态
// 接收 native 音视频状态 ---------

// 发送给  native 音视频状态 -----------
const String Channel_Native_IM_Video = "native_page/im_video"; //跳转im音视频
const String Channel_Receive_Call_Msg =
    "native_page/receive_call_msg"; //接收到了音视频im消息
// 发送给  native 音视频状态 -----------

const String CHANNEL_OPEN_MAP = 'channel_open_map';
const String GET_TIANDITU_POSITION = 'get_tianditu_position';

//清空原生栈
const String CHANNEL_CLEAR_NATIVE_VC = 'chanel_clear_native_vc';

const String FLUTTER_HOME_ONREADAY = 'flutter_home_onready';//home页ready发送消息通知原生离线跳转准备好了

const String OPEN_FLUTTER = 'open_flutter';//打开flutter页面
const String DEALLOC_WEB_VIEW = 'dealloc_web_view';//销毁webview
//iOS
const String CHANNEL_SET_APP_BADGE = 'channel_set_app_badge'; //设置app角标数量

const String Channel_call_iOS_camera = 'native_page/tackCamera'; //调用原生相机
const String Channel_call_iOS_qrCode = 'native_page/qrCode'; //跳转二维码扫描
const String Channel_call_Play_sound = 'native_playSound'; //点击bottomItem触感效果
const String Channel_call_iOS_popGesture =
    'native_popGesture'; //通知原生navigation侧滑开启或关闭

const String ChannelReStartApp = 'ChannelReStartApp';
const String ChannelSwitchEnv = 'ChannelSwitchEnv';
const String Channel_Notification_Sound_shock =
    'notification_Sound_shock'; //iOS本地通知声音、震动分别配置
const String Channel_iOS_upLoad = 'channel_iOS_upload'; //iOS调用flutter上传

// android
const String Channel_call_android_backPage = "call_native_backPage"; //
const String Channel_callback_pressed = "callback_pressed"; //
const String Channel_sendWeChatCode = "sendWeChatCode";
const String Channel_version_name = "Channel_version_name";
const String Channel_version_code = "Channel_version_code";
const String about_check_update = "about_check_update";
const String refresh_group_info = "refresh_group_info";
const String requestAndroidLaunchPermission = "requestAndroidLaunchPermission";
const String homePageIsStart = "homePageIsStart";
const String getAndroidDeviceMode = "getAndroidDeviceMode";
const String takeMealSuccess = "takeMealSuccess";


// socket
const String STARTSOCKET = 'startWebSocket';  // flutter 向 native 发起长连接
const String FLUTTER_SOCKET_BYTE = 'flutter_socket_byte';  // native 向 Flutter 传递 im 的数据
const String FLUTTER_SOCKET_STATUS = 'flutter_socket_status';  // native 向Flutter 返回 连接状态
const String FLUTTER_SEND_SOCKET_DATA = 'flutter_send_socket_data'; //  flutter 发送im 数据 给 native
const String FETCH_SOCKET_PARAM = 'fetch_socket_param';  // native 连接时向 flutter 获取连接 参数 （imtoken ，url）
const String DISCONNECT_SOCKET = 'disconnect_socket';  // 告诉 native 主动断开连接