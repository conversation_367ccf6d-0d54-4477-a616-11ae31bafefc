import 'dart:io';

import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

import '../../../../modules/privacy_policy/privacy_policy/logic.dart';

class SplashPageController extends GetxController {

  @override
  void onInit() async {
    super.onInit();

    Future.delayed(const Duration(seconds: 0)).then((x) async{
      var r = await isToShowPrivacyPage();
      if(r){
        Get.offAndToNamed(Routes.PRIVACY_POLICY,arguments: {'isFirst':Platform.isIOS});
        return;
      }
      var userInfo = await UserDefault.getData(Define.TOKENKEY);
      if (userInfo == null) {
        Get.offAndToNamed(Routes.LOGIN);
      } else {
        Get.offAndToNamed(Routes.HOME);
      }
    });
  }
}
