import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

import '../../../event/event.dart';

class CommonVideoPlayController extends GetxController {

  VideoPlayerController? videoPlayerController;
  late ChewieController chewieController;
  File? file;
  RxBool isShow = false.obs;
  StreamSubscription? _withDrawsubscription;

  @override
  void onInit() {
    super.onInit();
    file = Get.arguments['file'];
    if(file == null) {
      toast('文件错误');
      Get.back();
      return;
    }
    if (isShow.value == true) return;

    var msgId = Get.arguments['msgId'];
    _withDrawsubscription = eventBus.on<EventWithDraw>().listen((withDrawData){
       if(msgId == withDrawData.message.msgId){
         toast('消息已撤回');
         videoPlayerController?.dispose();
         Get.back();
       }
    });

    logger('播放路径： ${file!.path}');

    if(file!.path.startsWith('http')){
      videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(file!.path)
          ,videoPlayerOptions: VideoPlayerOptions());
    }else{
      videoPlayerController = VideoPlayerController.file(file!,videoPlayerOptions: VideoPlayerOptions());
    }
    videoPlayerController?.addListener((){
      if(videoPlayerController?.value.isCompleted == true){
        logger('播放结束....');
        // videoPlayerController?.seekTo(const Duration(seconds: 0));
      }

      if(videoPlayerController?.value.isPlaying == true) {
        // chewieController.customControls = null;

      }
    });
    videoPlayerController?.initialize().then((value) {
        chewieController = ChewieController(
            overlay: Container(),
            videoPlayerController: videoPlayerController!,
            autoPlay: true,
            looping: true,
            customControls: MaterialControls(
              showPlayButton: true,),
            // startAt: Duration.zero,
            allowFullScreen: false,
            showControls: false,
            autoInitialize: true,
            showControlsOnInitialize: false,
            optionsTranslation: OptionsTranslation(
              playbackSpeedButtonText:'播放速度',
              cancelButtonText: '取消'
            ),
            aspectRatio: videoPlayerController!.value.aspectRatio);

        isShow.value = true;
      });
  }

  @override
  void onClose() async {
    super.onClose();
    videoPlayerController?.dispose();
    _withDrawsubscription?.cancel();
  }
}
