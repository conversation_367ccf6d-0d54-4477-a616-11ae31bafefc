import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/widgets/title_bar.dart';
import 'package:get/get.dart';
import '../controllers/common_video_play_controller.dart';

class CommonVideoPlayView extends GetView<CommonVideoPlayController> {
  const CommonVideoPlayView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return ToolBar(
      showToolbar: false,
      backgroundColor:  Colors.black,
      body: Obx(() => controller.isShow.value
          ? OrientationBuilder(builder: ((context, orientation) {
        return Stack(
          children: [
            Center(
              child: Chewie(

                  controller: controller.chewieController),
            ),
            Positioned(
                 top: 20,
                 left:20,
                child: InkWell(
                  child:const Icon(Icons.close , color: Colors.white, size: 35,),
                  onTap: (){
                    Get.back();
                  },
                ))
          ],
        );
      })) : Container())
    );
  }
}
