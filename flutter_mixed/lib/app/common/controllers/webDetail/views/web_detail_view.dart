import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';

import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/web_detail_controller.dart';
import '../../../../common/widgets/widgets.dart';

class WebDetailView extends GetView<WebDetailController> {
  const WebDetailView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      appBar: TitleBar().backAppbar(context, controller.title, false, [], onPressed: () {
        Get.back();
      }),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(() => Container(
            color:controller.webProgress.value==100?Colors.transparent:ColorConfig.themeCorlor,
            height: 1.5,
            width: DeviceUtils().width.value*controller.webProgress.value/100.00,//
          )),
          Expanded(child: WebViewWidget(controller: controller.webViewController))
          
        ],
      ),
    );
  }
}
