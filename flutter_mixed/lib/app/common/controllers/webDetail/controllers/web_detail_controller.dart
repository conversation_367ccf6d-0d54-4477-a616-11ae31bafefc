import 'package:flutter/material.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebDetailController extends GetxController {

  late WebViewController webViewController ;
  String url = '';
  RxInt webProgress = 0.obs;
  String title = '';
  @override
  void onInit() {
    super.onInit();
    url = Get.arguments['url'];
    logger('$url');
    title = Get.arguments['title'];
    webViewController = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setBackgroundColor(Colors.white)
    ..setNavigationDelegate(
      NavigationDelegate(
        onProgress: (progress) {
          webProgress.value = progress;
        },
      )
    )..loadRequest(Uri.parse(url));
    
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

 
}
