import 'package:get/get.dart';

class CommonScrollPicController extends GetxController {

  final count = 0.obs;
  List imageUrls = [];
  int currentIndex = 0;
  @override
  void onInit() {
    super.onInit();
    imageUrls = Get.arguments['imageUrls'];
    currentIndex = Get.arguments['currentIndex'];
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
