class Define {
  static const TOKENKEY = 'tokenKey';
  static const WXUSERINFOKEY = 'wxUserInfoKey'; //微信信息
  static const IMUSERINFOKEY = 'imUserInfoKey'; //im用户信息

  static const COSTOKENKEY = 'cosTokenKey'; //腾讯云信息
  static const COSSETTINGKEY = 'cosSettingKey'; //腾讯云配置
  static const COSPREKEY = 'cosPreKey'; //腾讯云前缀

  static const FRIENDVERSION = 'friendVersionKey'; //好友版本
  static const FRIENDLIST = 'friendListKey'; //好友列表

  static const ORGLIST = 'orgListKey'; //公司列表
  static const GROUPLIST = 'groupListKey'; //公司列表
  static const CURRENTORGID = 'currentOrgId'; //工作台存储的公司
  //static const APPROVELUNREADKEY = 'approveUnReadKey'; //公司审批未读数
  static const APPROVE_TOTAL_LUNREADKEY =
      'approve_total_UnReadKey'; //所有公司审批未读总数
  static const APPROVE_SEND_CACHEKEY = 'approve_send_CacheKey'; //审批数据缓存

  static const KINGDEETOKEN = 'kingDeeTokenKey'; //金蝶token
  static const KINGDEEEXPIRETIME = 'kingDeeExpriTimeKey'; //金蝶token过期时间

  static const SCANLOGIN = 'login.'; //扫码登录
  static const SCANUSER = 'r.'; //个人二维码
  static const SCANORG = 'o.'; //企业二维码
  static const SIGN = 'sign.'; //扫码签名
  static const GROUP = 'group.'; //扫描群二维码进群
  static const HAS_READ_PRIVACY_POLICY = 'HAS_READ_PRIVACY_POLICY';

  static const UPDATE_VERSION = 'UPDATE_VERSION'; //此版本不弹出更新提示
  static const HTTPHEADER = 'httpHeaderKey'; //请求header
  static const BANNERINFO = 'bannerInfoKey'; //banner信息


  //路径相关
  static const APPROVEDETAILPATH = 'approve/files'; //审批详情文件存储路径
  static const APPROVEEXPORTPATH = 'approve/files/export'; //审批导出存储路径
  static const APPROVELOADTPATH = 'approve/load/files'; //审批文件下载路径
  static const REGIMESLOADTPATH = 'regimes/load/files'; //规章管理制度文件下载路径

  
  static const TIMEMAX = 9999999999999; //比较大的时间
  
}
