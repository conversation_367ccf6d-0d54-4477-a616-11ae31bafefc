import 'dart:async';
import 'dart:math';

import 'package:flutter/widgets.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/cos/pan_datasource.dart';
import 'package:flutter_mixed/app/common/cos/real_name_cos.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/im_client_manager.dart';
import 'package:flutter_mixed/app/im/ui/chat/common/picture_manager.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:get/get.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos_transfer_manger.dart';
import 'package:tencentcloud_cos_sdk_plugin/fetch_credentials.dart';
import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart';
import 'package:tencentcloud_cos_sdk_plugin/transfer_task.dart';

import '../../../main.dart';
import '../../im/constant/im_share.dart';
import '../../im/request/datasource/im_datasource.dart';
import '../../modules/home/<USER>/home_controller.dart';
import '../../utils/http.dart';
import '../../../logger/logger.dart';
import '../../utils/video_util.dart';
import '../api/LoginApi.dart';
import 'cosConfigResp.dart';
import 'cosPanResp.dart';

/// 校验秘钥、 注册cos 、上传、下载资源
class CosManager {
  /// 缓存目录
  final IMAGE_CACHE_FOLDER = 'image_cache_folder'; // 图片目录
  final FILE_CACHE_FOLDER = 'file_cache_folder'; // 文件目录
  final VIDEO_SHOT_SCREEN_FOLDER = 'video_shot_screen_folder'; // 视频第一帧
  final VIDEO_COMPRESSED_FOLDER = 'video_compressed_folder'; // 视频压缩目录
  final VOICE_CACHE_FOLDER = 'voice_cache_folder'; // 语音消息

  static CosManager? _instance;

  CosManager._internal();

  factory CosManager() => _instance ??= CosManager._internal();

  Future<String?> bucket() async {
    dynamic cacheSession = await UserDefault.getData(Define.COSTOKENKEY);
    if (cacheSession != null) {
      CosPanResp cosPan = CosPanResp.fromJson(cacheSession);
      return cosPan.bucket;
    }
    return null;
  }

  Future<String?> imBucket() async {
    dynamic cacheSession = await UserDefault.getData(Define.COSTOKENKEY);
    if (cacheSession != null) {
      CosPanResp cosPan = CosPanResp.fromJson(cacheSession);
      return cosPan.imBucket;
    }
    return null;
  }

  Future<String?> userBucket() async {
    dynamic cacheSession = await UserDefault.getData(Define.COSTOKENKEY);
    if (cacheSession != null) {
      CosPanResp cosPan = CosPanResp.fromJson(cacheSession);
      return cosPan.userBucket;
    }
    return null;
  }

  // 初始化接口  pan/file/token/v2   ；  user/upload/config
  initPans({bool isAttendance = false}) async {
    // step1 校验 session
    try {
      bool hasRequest = false;
      if (isAttendance) {
        hasRequest = await _requestAttendanceSession();
      } else {
        hasRequest =  await _requestSession();
      }
      if(!hasRequest) return;

      // step 2 校验 config
      await _validateConfig();

      await Cos().registerDefaultTransferManger(
          CosRegister().serviceConfig, CosRegister().transferConfig);
    }catch(e){}
  }
  // 初始化接口  pan/file/token/v2   ；  user/upload/config
  initPansWithToken(RealNameCos tokenInfo) async {
    // step1 校验 session
    try {
      var cosPan = CosPanResp();
      CredentialsX attCredentitals = CredentialsX()
        ..sessionToken = tokenInfo.credentials!.sessionToken
        ..tmpSecretId = tokenInfo.credentials!.tmpSecretId
        ..tmpSecretKey = tokenInfo.credentials!.tmpSecretKey;
      cosPan.credentials = attCredentitals;
      cosPan.expiredTime = tokenInfo.expiredTime;
      cosPan.bucket = tokenInfo.bucket;
      await UserDefault.setData(Define.COSTOKENKEY, cosPan);
      await Cos().forceInvalidationCredential();
      await Cos().initWithSessionCredential(FetchCredentials());

      // step 2 校验 config
      CosXmlServiceConfig serviceConfig = CosXmlServiceConfig(
        region: tokenInfo.region??"ap-chongqing",
        isDebuggable: true,
        isHttps: true,
      );
      await Cos().registerDefaultService(serviceConfig);

      await Cos().registerDefaultTransferManger(
          CosRegister().serviceConfig, CosRegister().transferConfig);
    }catch(e){}
  }

  Future _requestSession() async {
    try {
      var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await panDatasource.getPanSession();
      if (resp.success()) {
        var cosPan = resp.data;
        await UserDefault.setData(Define.COSTOKENKEY, cosPan);
        logger('=======_requestSession===${cosPan.toJson()}');
        await Cos().forceInvalidationCredential();
        await Cos().initWithSessionCredential(FetchCredentials());
        return true;
      }else{
        return false;
      }
    } catch (e) {
      return false;
    }

  }

  //考勤获取密钥
  Future<bool> _requestAttendanceSession() async {
    var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
    var resp = await panDatasource.getAttendanceSession();
    if (resp.success()) {
      var cosPan = resp.data;
      CredentialsX attCredentitals = CredentialsX()
        ..sessionToken = cosPan.sessionToken
        ..tmpSecretId = cosPan.tmpSecretId
        ..tmpSecretKey = cosPan.tmpSecretKey;
      cosPan.credentials = attCredentitals;
      await UserDefault.setData(Define.COSTOKENKEY, cosPan);
      logger('=======_requestAttendanceSession===${cosPan.toJson()}');
      await Cos().forceInvalidationCredential();
      await Cos().initWithSessionCredential(FetchCredentials());
      return true;
    }else {
      return false;
    }
  }

  _validateConfig() async {
    var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
    dynamic cacheCosConfig = await UserDefault.getData(Define.COSSETTINGKEY);
    if (cacheCosConfig != null) {
      var resp = CosConfigResp.fromJson(cacheCosConfig);
      await _initCosConfig(resp);
    } else {
      var configResp = await panDatasource.getPanConfig();
      if (configResp.success()) {
        await _initCosConfig(configResp.data);
        await UserDefault.setData(Define.COSSETTINGKEY, configResp.data);
      }
    }
  }

  _initCosConfig(CosConfigResp config) async {
    CosXmlServiceConfig serviceConfig = CosXmlServiceConfig(
      region: config.region,
      isDebuggable: false,
      isHttps: true,
    );
    await Cos().registerDefaultService(serviceConfig);
  }
}

class FetchCredentials implements IFetchCredentials {
  FetchCredentials();

  @override
  Future<SessionQCloudCredentials> fetchSessionCredentials() async {
    try {
      dynamic cacheSession = await UserDefault.getData(Define.COSTOKENKEY);
      CosPanResp pan = CosPanResp.fromJson(cacheSession);
      logger('=====FetchCredentials===$pan');
      return SessionQCloudCredentials(
          secretId: pan.credentials?.tmpSecretId ?? '', // 临时密钥 SecretId
          secretKey: pan.credentials?.tmpSecretKey ?? '', // 临时密钥 SecretKey
          token: pan.credentials?.sessionToken ?? '', // 临时密钥 Token
          startTime: 0, //临时密钥有效起始时间，单位是秒  todo
          expiredTime: pan.expiredTime ?? 0 //临时密钥有效截止时间戳，单位是秒
          );
    } catch (exception) {
      logger('======FetchCredentials===error===$exception');
      throw ArgumentError();
    }
  }
}
class RealNameCredentials implements IFetchCredentials {
  RealNameCredentials({required this.realNameCos});
  RealNameCos realNameCos;
  @override
  Future<SessionQCloudCredentials> fetchSessionCredentials() async {
    try {
      return SessionQCloudCredentials(
          secretId: realNameCos.credentials?.tmpSecretId ?? '', // 临时密钥 SecretId
          secretKey: realNameCos.credentials?.tmpSecretKey ?? '', // 临时密钥 SecretKey
          token: realNameCos.credentials?.sessionToken ?? '', // 临时密钥 Token
          startTime: 0, //临时密钥有效起始时间，单位是秒  todo
          expiredTime: realNameCos.expiredTime ?? 0 //临时密钥有效截止时间戳，单位是秒
      );
    } catch (exception) {
      logger('======RealNameCredentials===error===$exception');
      throw ArgumentError();
    }
  }
}

class CosRegister {
  // 存储桶所在地域简称，例如广州地区是 ap-guangzhou
  static const String region = 'ap-chongqing';
  CosXmlServiceConfig serviceConfig = CosXmlServiceConfig(
    region: region,
    isDebuggable: false,
    isHttps: true,
  );

  TransferConfig transferConfig = TransferConfig(
    forceSimpleUpload: false,
    enableVerification: true,
    divisionForUpload: 2097152, // 设置大于等于 2M 的文件进行分块上传
    sliceSizeForUpload: 1048576, //设置默认分块大小为 1M
  );
}

class CosDownLoadUtil {
  static cacheThumb(String? fileId, Function(double) proCall,
      {String? folder = IMAGE_THUMB_FOLDER , Function? success}) async {
    var remoteUrl = await FileThumbHelper.fetchImageSignRemoteUrl(fileId);
    if (StringUtil.isEmpty(remoteUrl)) return;
    // CosManager().initPans();
    FileThumbHelper.downloadThumbOnly(
        fileId, remoteUrl, folder ?? IMAGE_THUMB_FOLDER, (pro) {
      proCall(pro);
    } , success: (){
      success?.call();
    });
  }

  ///  bucket:  存储桶名称，由 bucketname-appid 组成，appid 必须填入，可以在 COS 控制台查看存储桶名称。 https://console.cloud.tencent.com/cos5/bucket
  ///  cosPath: fileId
  ///  downloadPath: 保存到本地文件的绝对路径
  static downLoad(String? bucket, String? cosPath, String downloadPath,
      {Function(double)? progress , Function(bool)? success}) async {

    try{
      if (StringUtil.isEmpty(bucket) || StringUtil.isEmpty(cosPath)) {
        toast('腾讯云下载失败; bucket = $bucket , cosPath = $cosPath');
        return;
      }

      // CosManager().initPans();
      debugPrint('download-----开始----$bucket----$cosPath------$downloadPath');
      CosTransferManger transferManager = Cos().getDefaultTransferManger();

      // 下载成功回调
      successCallBack(Map<String?, String?>? header, CosXmlResult? result) {
        debugPrint('下载完成');
        success?.call(true);
      }

      //下载失败回调
      failCallBack(CosXmlClientException? clientException, serviceException) {
        debugPrint('下载失败');
        if (clientException != null) {
          logger(clientException);
        }
        if (serviceException != null) {
          debugPrint(serviceException);
        }
        CosManager().initPans();
      }

      //下载状态回调, 可以查看任务过程
      stateCallback(state) {
        debugPrint('下载状态回调: $state');
      }

      //下载进度回调
      progressCallBack(complete, target) {
        // debugPrint('下载进度------$complete----$target');
        var pro = complete / target;
        progress?.call(pro);
      }

      TransferTask transferTask = await transferManager.download(
          bucket!, cosPath!, downloadPath,
          resultListener: ResultListener(successCallBack, failCallBack),
          stateCallback: stateCallback,
          progressCallBack: progressCallBack);
    }catch(e){
      logger('cos downLoad..$e');
    }



  }
}

class CosUploadHelper {
  static Future<String?> getUploadFieldId() async {
    try{
      var datasource = ImDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);
      var resp = await datasource.getFileId();
      if (resp.success()) return resp.data;
      return null;
    }catch(e){
      return null;
    }
  }

  static Future<String?> upload(String? filePath, {String? fileId, String? userBucket}) async {

    try {
      Completer<String?> completer = Completer();
      if (fileId == null) {
        var tempFileId = await getUploadFieldId();
        logger('tempFileId====== null');
        if(tempFileId == null) {
          return null;
        };
        fileId = tempFileId;
      }

      var bucket = userBucket ?? await CosManager().imBucket();

      if (StringUtil.isEmpty(bucket) || StringUtil.isEmpty(fileId)) {
        toast('腾讯云上传参数获取失败; bucket = $bucket , cosPath = $fileId');
        return null;
      }


      logger('upload-----开始----bucket:$bucket----fileId: $fileId------');
      CosTransferManger transferManager = Cos().getDefaultTransferManger();

      successCallBack(Map<String?, String?>? header, CosXmlResult? result) {
        logger('上传完成');
        completer.complete(fileId);
      }

      //下载失败回调
      failCallBack(CosXmlClientException? clientException,CosXmlServiceException? serviceException) {
        logger('上传失败');
        if (clientException != null) {
          debugPrint('clientException.message=${clientException.message}');
        }
        if (serviceException != null) {
          serviceException.printError();
          debugPrint('serviceException=${serviceException.errorCode}===${serviceException.errorMessage}===============');
          CosManager().initPans();
        }
        completer.complete(null);
      }

      //下载状态回调, 可以查看任务过程
      stateCallback(state) {
        // state is TransferState
        debugPrint('上传状态回调: $state');
      }

      //上传进度回调
      progressCallBack(complete, target) {
        var pro = complete / target;
        // debugPrint('上传进度------$complete----$target --- $pro');
      }

      String? _uploadId;

      //初始化分块完成回调
      initMultipleUploadCallback(String bucket, String cosKey, String uploadId) {
        //用于下次续传上传的 uploadId
        _uploadId = uploadId;
      }

      TransferTask transferTask = await transferManager.upload(bucket!, fileId,
          uploadId: _uploadId,
          filePath: filePath,
          resultListener: ResultListener(successCallBack, failCallBack),
          stateCallback: stateCallback,
          progressCallBack: progressCallBack,
          initMultipleUploadCallback: initMultipleUploadCallback);

      return completer.future;
    }catch(e){
      logger('cos upload error: $e');
      return null;
    }


    // 暂停任务
    //transferTask.pause();
    //恢复任务
    //transferTask.resume();
    //取消任务
    //transferTask.cancel();
  }

  static Future<String?> nativeUpload(
      String filePath, String? object, String? bucket,
      {bool isAttendacne = false}) async {
    String? backUrl = object;
    try {
      if (StringUtil.isEmpty(bucket)) {
        toast('腾讯云上传参数获取失败; bucket = $bucket , object = $object');
        return backUrl;
      }

      Completer<String?> completer = Completer();
      if (object == null) {
        //需要field
        if (isAttendacne) {
          var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
          var resp = await panDatasource.getAttendanceFileId(1);
          if (!resp.success()) {
            toast('获取考勤文件id失败');
            return null;
          }
          if (resp.data == null) return null;
          if (resp.data!.isEmpty) return null;
          object = resp.data!.first;
          backUrl = object;
        } else {
          var datasource = ImDataSource(retrofitDio, baseUrl: Host.ROUTERHOST);
          var resp = await datasource.getFileId();
          if (!resp.success()) {
            toast('获取im文件id失败');
            return null;
          }
          object = resp.data;
          backUrl = object;
        }
      } else {
        if (object.startsWith('/')) {
          //公有桶-个人、组织、群组头像
          dynamic preMap = await UserDefault.getData(Define.COSPREKEY);
          if (preMap == null) return null;
          String preStr = preMap['pre'];
          backUrl = '$preStr$object';
        }else{
          backUrl = object;
        }
      }

      logger('upload-----开始----bucket:$bucket----object: $object------');
      CosTransferManger transferManager = Cos().getDefaultTransferManger();

      successCallBack(Map<String?, String?>? header, CosXmlResult? result) {
        logger('上传完成');
        completer.complete(backUrl);
      }

      //上传失败回调
      failCallBack(CosXmlClientException? clientException,CosXmlServiceException? serviceException) {
        logger('上传失败');
        toast('上传失败');
        if (clientException != null) {
          debugPrint('clientException.message=${clientException.message}');
        }
        if (serviceException != null) {
          debugPrint('serviceException=${serviceException.toString()}');
        }
        completer.complete(null);
      }

      //上传状态回调, 可以查看任务过程
      stateCallback(state) {
        // state is TransferState
        debugPrint('上传状态回调: $state');
      }

      //上传进度回调
      progressCallBack(complete, target) {
        var pro = complete / target;
        // debugPrint('上传进度------$complete----$target --- $pro');
      }

      String? _uploadId;

      //初始化分块完成回调
      initMultipleUploadCallback(
          String bucket, String cosKey, String uploadId) {
        //用于下次续传上传的 uploadId
        _uploadId = uploadId;
      }

      TransferTask transferTask = await transferManager.upload(bucket!, object,
          uploadId: _uploadId,
          filePath: filePath,
          resultListener: ResultListener(successCallBack, failCallBack),
          stateCallback: stateCallback,
          progressCallBack: progressCallBack,
          initMultipleUploadCallback: initMultipleUploadCallback);

      return completer.future;
    } catch (e) {
      return null;
    }
  }
}
