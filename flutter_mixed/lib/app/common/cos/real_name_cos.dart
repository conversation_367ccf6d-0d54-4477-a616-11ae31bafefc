import 'cosPanResp.dart';

class RealNameCos{
  String? bucket;
  CredentialsX? credentials;
  int? expireSeconds;
  String? region;
  String? appId;
  String? fileId;
  int? expiredTime;

  RealNameCos({
    this.bucket,
    this.credentials,
    this.expireSeconds,
    this.region,
    this.appId,
    this.fileId,
    this.expiredTime,
  });

  factory RealNameCos.fromJson(Map<String, dynamic> json) {
    return RealNameCos(
      bucket: json['bucket'],
      credentials: json['credentials'] == null
          ? null
          : CredentialsX.fromJson(json['credentials']),
      expireSeconds: json['expireSeconds'],
      region: json['region'],
      appId: json['appId'],
      fileId: json['fileId'],
      expiredTime: json['expiredTime'],
    );
  }

  Map<String, dynamic> toJson() => {
        'bucket': bucket,
        'credentials': credentials?.toJson(),
        'expireSeconds': expireSeconds,
        'region': region,
        'appId': appId,
        'fileId': fileId,
        'expiredTime': expiredTime,
      };
}

class CosToken{
  CredentialsX? credentials;
  int? expiredTime;

  CosToken({
    this.credentials,
    this.expiredTime,
  });

  factory CosToken.fromJson(Map<String, dynamic> json) {
    return CosToken(
      credentials: json['credentials'] == null
          ? null
          : CredentialsX.fromJson(json['credentials']),
      expiredTime: json['expiredTime'],
    );
  }

  Map<String, dynamic> toJson() => {
        'credentials': credentials?.toJson(),
        'expiredTime': expiredTime,
      };
}