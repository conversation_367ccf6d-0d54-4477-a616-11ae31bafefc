
// 样例
///{
//     "code": 1,
//     "msg": "请求成功",
//     "data": {
//         "bucket": "ddbes-pan-tmp-1304188286",
//         "perBucket": "ddbes-pan-test-1304188286",
//         "imBucket": "im-resources-dev-1304188286",
//         "userBucket": "ddbes-repository-1304188286",
//         "userEndpoint": "cdn.ddbes.com",
//         "credentials": {
//             "sessionToken": "17k89HLUh4U7phzop0djpzPcVEzJT4qa1f6280a602d4234a5b9b7558ba27d3beXkuQnJDHW5Y4FCyzDQD15uwqw06JbyxJ1j5v4KkU7WitC-LEfYSLKPZG0D2u8SzBh3rki5xa3SELDVVP3TQGLCIbI-993IKVwehS5a4zP3P6WoGOx3FSmywPogtHGxPxItiq4J_ReUxLEvlz6OK3XHEArw3HInKiruJkKNRL8b0IuGclXC-NNRAONtDhNBnz9D14ddNS1g-uakKLQBdk-FmK8hNoHcz_lNOA3CoeVICw_7RdtGVNxiFeRnpMFxqoLyFiLoCAMWj7GzJKI-gXteGxZ9ywfrZD2RKHkulFj1d5aHHYiQPKyjZeTUA2eYbU1kZJwoHFOxcFgO1lR9cdNdE5immPXH14XE5PkNr-u0RmL_RvwCVuV6Y3Dcos_6q4F5CW6k7IpzD8j9dHeeXyCbn6o7nHyJwlKZHnLu23FOV1tDAIPo_3Us1Z4ClTvhA4SH1mHWXJIyS_YvjqJsDv2VPIf6ewoWezoxsoSzt4lz7b54u-4x7BfElBTHsmBTKKMizvGNlOWvYSJoWoTCkiylbHavDy6-cWRG20lBFnjZMdRcwqEni3MPM2Ya_wcVP3NEu-VKZs71JFfhZwtX2JgBqNf4b0CFmxZGN9xQFMH2VOe9Lc2iigwItV1Pq-73k2LPGu2IWJOGIkopcw7zmcSr4t7lfZsZPBkxmUTWKrWLbNZoyMkhvU897LuLlFazKq8s2335v7TDv2XWt6HtLUeqVSJ1TYuB3v1pT7Qfywd-oq9RQZaCqvzvbNWKz95s47vGp9X_yVGL1Zuy-he4dDk2R7qICAPZ7q6SVAivKerEizHqGQhe4FcCIY8evB0zBKqqU7kgkVFbnF_TvKSTrozRPCS-STig1EWWfbrii85VOE6vL3Ibm4MFARBIh7kSbT33cAR7TfnVvjOQwycBCZqS4gQYwlq5cbPZZgXsI88cFhBUMiIHg4N20YDT_TUQmEsuV-YivxomdVUPE0O7mdOESBZTHvnUmizI2D-W836q_jGtFdWmYXVELyxYmhKl31wb_bZzXxo19U7_Jp6y7Z_TAEfd9xzAVzxaHn38Sk-97MrQiZlDkiLnm979cfCnb_isj6bJhrNZIMKO9ddoHKiokcWmC0tWVQNxfb1vKA6_90yGd0SPVgYjdEuDZxhJ2vcIV8T6UMOkZPY77tz64o7tPQAcBQBTjNR6m500U-EXliARdlYAfCagHiyHFsdPYNPCoJX23lz6BlClDU4NEcv9PMywaYzrvjWNiCaSJ0by9LjckLGLwoyPCiEuk5HTqInkwm-SkuZLVl7wdBrSJr1FY_-_Uc3-I0GNzClYCyWTyu0RpuLS3OL_HMCi9Fhd-dcuN2Hjf2YhUPEBGjHgOQ6q0rQae8OGwaZa554f9iYUc74ZJJE-qWtxZs7CohrqCZRX4T3RmidJrbFFQi7vYKasVeBm3kU-Z26_tEddn0c27OFKd4HSCbI4MZdHaQuEeVAdnCZJaniH4tedSzxKCNY-38tehJexju_w1JeH0TUTiC1DfY6bE3rAQrn0rYzKYNx4z017Qt-tXf9HWcjbyXhfsAjDHX00jKUc9i8lAc_rXKgRcjilrYP6KejZnC2kMjz7GEUoq2WHPPg_mGbZ_JaILghthG9cacCJLsdcDB3UdXgGlFzTRiD8aJ3oWY_8fvpn5xyiBLyQAK265fTK-7YnSO9mi383RVdI2_wcc9DtAqGiWSnM9v3gswutROR_SiEaauKK6zVoDRURK-c62u_dTl8YFSsQ0cG6b7pUQZe3YB5YiAbaa6spp_gKWYHvIoA6VoPRb2EvQlmyuxhJjWR1ylfGXysGYTJnw1PF7ENmCthcD_X0znr9HhsidSftKP8WU16Auz1sGaloz1Y6c1-EtsHviXNWtJ20DBN8i9YItjM7iS6b6Mod420WE7ANRrz6UMe3rn3w4dAjALxN1REEBzbXxqIv613hvtoCROvB9H8xjy6ruUT4Fe-jB6ZEkqLIf0k7CSGAajIDTt78jXqlURM99yed-mO9UrBPGxADGbOeUpsjCBa13bDb0kurYnCw4i5N-_i8seBUVZftVK6u5tPM3-fRRfxk1nIPYJo4InPKyG5XGILMCkCUpkOO-dH6A43lGcV2EBVLghqvM7tRO_s7piDDcVyrrpRNnXa2QEj8bwox5yPIpuxjMErUDznZxX1x_r2FN2uD7rnRkevI5yBTAkndiBG9unBcdADut1WeFhIuA1TqBHGZ0y83Ai5OL_NHqIZNGwjMC6Xgp6NuVhvaUaWZ6NJKlqhAS-Ag4PXxNK_hljnMcMHxtcbLEvozoTUiP7uNiMeZPLycfIZXur5vU7fL5bO8c9KbB4_RgyLiAaKLDb01DL2LbWH79xMZw1K175dyHDYYmgw9VbKBUqBbvzEMjVVx9T3HprgMo6-YjJpbCXgEypDxTZKWDQkesx7UdnGis2T1TirgCytuh5la6JH0ZmvkIkG0dlkcOCHQoTJkdSkT1v1c-NBY2KgmIb1QE-jiEMIwFM7aE16CsLCnUsOS7pE_oCAZmLIAe4aDBjHQ2-cUd81CA6whwR",
//             "tmpSecretId": "AKIDJWfNiH6JYRxhBXK7W74jgO7pYrTH49FXn5H90Q38y0Y1r8IlmUhKnDR49iEYXzcu",
//             "tmpSecretKey": "o/IpYLQv+3ESsnuE2IjVsb27GR8XGkpooC63AnlfUCY="
//         },
//         "expiredTime": 1733738781
//     }
// }

class CosPanResp {

  /**user 开放访问logo等存储 eg: ddbes-repository-1304188286*/
  String? userBucket;

  /**网盘存储 eg: ddbes-pan-tmp-1304188286*/
  String? bucket;

  /**im相关 eg: im-resources-dev-1304188286*/
  String? imBucket;

  /**文件预览? eg: ddbes-pan-test-1304188286*/
  String? perBucket;

  /**cdn 存储 eg: cdn.ddbes.com*/
  String? userEndpoint;

  String? imEndpoint;

  CredentialsX? credentials;

  int? expiredTime;

  //考勤json结构不一致 这三个在外层
  String? sessionToken;
  String? tmpSecretId;
  String? tmpSecretKey;

  CosPanResp({this.userBucket, this.bucket, this.imBucket, this.perBucket,
    this.userEndpoint, this.imEndpoint, this.credentials, this.expiredTime,this.sessionToken,this.tmpSecretId,this.tmpSecretKey});

  factory CosPanResp.fromJson(Map<String, dynamic> json) {
    return CosPanResp(
      userBucket: json['userBucket'] ?? '',
      bucket: json['bucket'] ?? '',
      imBucket: json['imBucket'] ?? '',
      perBucket: json['perBucket'] ?? '',
      userEndpoint: json['userEndpoint'] ?? '',
      imEndpoint: json['imEndpoint'] ?? '',
      credentials: json['credentials'] != null ? CredentialsX.fromJson(json['credentials']):null,
      expiredTime: json['expiredTime'] ?? 0,
      sessionToken: json['sessionToken'] ?? '',
      tmpSecretId: json['tmpSecretId'] ?? '',
      tmpSecretKey: json['tmpSecretKey'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'userBucket': userBucket,
        'bucket': bucket,
        'imBucket': imBucket,
        'perBucket': perBucket,
        'userEndpoint': userEndpoint,
        'imEndpoint': imEndpoint,
        'credentials': credentials?.toJson(),
        'expiredTime': expiredTime,
        'sessionToken': sessionToken,
        'tmpSecretId': tmpSecretId,
        'tmpSecretKey': tmpSecretKey,
      };
}

class CredentialsX {
  String? sessionToken;
  String? tmpSecretId;
  String? tmpSecretKey;

  CredentialsX({this.sessionToken, this.tmpSecretId, this.tmpSecretKey});

  factory CredentialsX.fromJson(Map<String, dynamic> json) {
    return CredentialsX(
      sessionToken: json['sessionToken'],
      tmpSecretId: json['tmpSecretId'],
      tmpSecretKey: json['tmpSecretKey'],
    );
  }

  Map<String, dynamic> toJson() => {
        'sessionToken': sessionToken,
        'tmpSecretId': tmpSecretId,
        'tmpSecretKey': tmpSecretKey,
      };
}