
// 样例
///{
//     "code": 1,
//     "msg": "请求成功",
//     "data": {
//         "region": "ap-chongqing",
//         "bucket": "ddbes-repository-1304188286",
//         "appId": "1304188286",
//         "pre": "https://cdn.ddbes.com"
//     }
// }

class CosConfigResp {

  String? region;

  String? bucket;

  String? appId;

  String? pre;

  CosConfigResp({
    this.region,
    this.bucket,
    this.appId,
    this.pre,
  });

  factory CosConfigResp.fromJson(Map<String, dynamic> json) {
    return CosConfigResp(
      region: json['region'],
      bucket: json['bucket'],
      appId: json['appId'],
      pre: json['pre'],
    );
  }

  Map<String, dynamic> toJson() => {
        'region': region,
        'bucket': bucket,
        'appId': appId,
        'pre': pre,
      };
}

