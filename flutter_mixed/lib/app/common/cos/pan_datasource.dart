import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

import '../../retrofit/entity/base_resp.dart';
import 'cosConfigResp.dart';
import 'cosPanResp.dart';

part 'pan_datasource.g.dart';

/// pan : baseUrl : Host.ROUTERHOST
@RestApi()
abstract class PanDataSource {

  factory PanDataSource(Dio dio, {String baseUrl}) = _PanDataSource;

  @GET('pan/file/token/v2')
  Future<BaseResp<CosPanResp>> getPanSession();

  @GET('pan/file/key/v2/{size}')
  Future<BaseResp<List<String?>?>> getPanFields(@Path("size") int? size);
  

  @GET('user/upload/config')
  Future<BaseResp<CosConfigResp>> getPanConfig();

  //获取考勤密钥
  @GET('att/cos')
  Future<BaseResp<CosPanResp>> getAttendanceSession();

  //获取考勤fileid
  @GET('att/cos/{count}')
  Future<BaseResp<List<String>?>> getAttendanceFileId(@Path("count") int count);
}