import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/utils/net_util.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/app/utils/system_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../main.dart';
import '../../im/request/datasource/im_datasource.dart';
import '../../modules/common/group_change/dialog/group_avatar_upgrade_ctl.dart';
import '../../retrofit/datasource/workbench_datasource.dart';
import '../../utils/http.dart';
import '../api/LoginApi.dart';
import '../channel/channel.dart';
import '../widgets/widgets.dart';

class BaseInfo {
  List letterList = [
    {'A': []},
    {'B': []},
    {'C': []},
    {'D': []},
    {'E': []},
    {'F': []},
    {'G': []},
    {'H': []},
    {'I': []},
    {'J': []},
    {'K': []},
    {'L': []},
    {'M': []},
    {'N': []},
    {'O': []},
    {'P': []},
    {'Q': []},
    {'R': []},
    {'S': []},
    {'T': []},
    {'U': []},
    {'V': []},
    {'W': []},
    {'X': []},
    {'Y': []},
    {'Z': []},
    {'#': []}
  ];

  static List<String> letters = [
    '⭐',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    '#'
  ];

  static getUserID() async {
    Map dict = await UserDefault.getData(Define.IMUSERINFOKEY);
    return dict['userID'];
  }

  //文件类型根据名字返回图片地址
  panFileType(String fileName) {
    Map fileDic = {
      "ai": ["ai"],
      "doc": ["doc", "docx"],
      "dwf": ["dwf"],
      "dwg": ["dwg"],
      "excel": ["xls", "xlsx"],
      "gif": ["gif"],
      "html": ["html"],
      'jpg': [
        'jpg',
        'png',
        "jpeg",
        "webp",
        "tif",
        "bmp",
        "pcx",
        "emf",
        "tga",
        "heif",
        "heic",
        "raw"
      ],
      "js": ["js"],
      "mp3": ["mp3", "wma", "wav", "midi", "amr", "aac", "flac"],
      'mp4': [
        'mp4',
        "avi",
        "rmvb",
        "mov",
        "flv",
        "3gp",
        "rm",
        "mkv",
        "wmv",
        "m4v",
        "m3u8"
      ],
      "pdf": ["pdf"],
      "ppt": ["ppt", "pptx", "pps", "pot", "ppa"],
      "psd": ["psd", "psb"],
      "rp": ["rp"],
      "skp": ["skp"],
      "stl": ["stl"],
      "swf": ["swf"],
      "txt": ["txt", "text", "rtf", "json"],
      "vsd": ["vsd", "vsdx"],
      "xmind": ["xmind"],
      "zip": ["zip", "rar", "tar", "cab", "7z", "xip"]
    };

    List fileArray = fileName.split('.');
    String lastStr = fileArray.last;
    lastStr = lastStr.toLowerCase();
    for (String key in fileDic.keys) {
      List array = fileDic[key];
      if (array.contains(lastStr)) {
        return 'assets/images/3.0x/pan_fileType$key.png';
      }
    }
    return 'assets/images/3.0x/pan_fileTypewu.png';
  }

  //是否可以预览
  bool canShowPreView(String fileName) {
    List extList = fileName.split('.');
    String ext = extList.last;
    ext = ext.toLowerCase();
    List canShowList = [
      'doc',
      'docx',
      'rtf',
      'ppt',
      'pptx',
      'xls',
      'xlsx',
      'xlsm',
      'csv',
      'pdf',
      'txt',
      'epub',
      'chm'
    ];
    return canShowList.contains(ext);
  }

  //是否是图片格式
  bool isImageFormat(String fileName) {
    List extList = fileName.split('.');
    String ext = extList.last;
    ext = ext.toLowerCase();
    List canShowList = [
      'png',
      'jpg',
      'jpeg',
      'webp',
      'tif',
      'bmp',
      'heif',
      'heic',
    ];
    return canShowList.contains(ext);
  }

  //根据图片大小返回展示内容
  backSizeStr(int size) {
    String sizeStr = '';
    if (1048576 <= size) {
      sizeStr = '${(size / 1048576.00).toStringAsFixed(2)}MB';
    } else if (1024 <= size) {
      sizeStr = '${(size / 1024.00).toStringAsFixed(2)}KB';
    } else {
      sizeStr = '$size' 'B';
    }
    return sizeStr;
  }

  //返回日期格式
  String backTimeStr(int sendTime) {
    String timeStr = '';

    if (BaseInfo().isCurrentYear(sendTime)) {
      //如果是今年

      if (BaseInfo().isWithin7Days(sendTime)) {
        //如果是7天内

        if (BaseInfo().isYesterday(sendTime)) {
          //如果是昨天
          timeStr = BaseInfo().formatTimestamp(sendTime, '昨天 HH:mm');
        } else if (BaseInfo().isToday(sendTime)) {
          //如果是今天
          timeStr = BaseInfo().formatTimestamp(sendTime, 'HH:mm');
        } else {
          timeStr = BaseInfo().formatTimestamp(
              sendTime, '${BaseInfo().backWeekDay(sendTime)} HH:mm');
        }
      } else {
        timeStr = BaseInfo().formatTimestamp(sendTime, 'MM月dd HH:mm');
      }
    } else {
      timeStr = BaseInfo().formatTimestamp(sendTime, 'yyyy年MM月dd HH:mm');
    }
    return timeStr;
  }

  //判断是不是今年
  bool isCurrentYear(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime now = DateTime.now();
    return date.year == now.year;
  }

  //判断是不是7天内
  bool isWithin7Days(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime now = DateTime.now();
    Duration difference = now.difference(date);
    return difference.inDays < 7;
  }

  //判断是不是昨天
  bool isYesterday(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime now = DateTime.now();
    DateTime yesterday = DateTime(now.year, now.month, now.day - 1);
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  //判断是不是今天
  bool isToday(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }

  //判断是不是明天
  bool isTomorrow(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day + 1);
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }

  //几天后的时间
  String backAfterMoreDays(int days) {
    DateTime now = DateTime.now();
    DateTime date = DateTime(now.year, now.month, now.day + days);
    var formatter = DateFormat('MM月dd日');
    return formatter.format(date);
  }

  //判断是不是后天
  bool isTheDayAfterTomorrow(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day + 2);
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }

  String backWeekDay(int timestamp) {
    DateTime date =
        DateTime.fromMillisecondsSinceEpoch(timestamp); // 转换为 DateTime 对象
    int weekday = date.weekday;
    if (weekday == 1) {
      return '星期一';
    }
    if (weekday == 2) {
      return '星期二';
    }
    if (weekday == 3) {
      return '星期三';
    }
    if (weekday == 4) {
      return '星期四';
    }
    if (weekday == 5) {
      return '星期五';
    }
    if (weekday == 6) {
      return '星期六';
    }
    if (weekday == 7) {
      return '星期日';
    }
    return '';
  }

  String formatTimestamp(int timestamp, String formatStr) {
    var date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    var formatter = DateFormat(formatStr);
    return formatter.format(date);
  }

  //时间间隔
  String compareTime(int createTime, int endTime) {
    int doneTime = 0;
    if (createTime > endTime) {
      doneTime = createTime - endTime;
    } else {
      doneTime = endTime - createTime;
    }

    DateTime time = DateTime.fromMillisecondsSinceEpoch(doneTime);
    DateTime zeroTime = DateTime.fromMillisecondsSinceEpoch(0);
    var differenceDay = time.difference(zeroTime);

    DateTime hourTime = DateTime.fromMillisecondsSinceEpoch(
        doneTime - differenceDay.inDays * 24 * 60 * 60 * 1000);
    var differenceHour = hourTime.difference(zeroTime);

    DateTime minuteTime = DateTime.fromMillisecondsSinceEpoch(doneTime -
        differenceDay.inDays * 24 * 60 * 60 * 1000 -
        differenceHour.inHours * 60 * 60 * 1000);
    var differenceMinute = minuteTime.difference(zeroTime);

    int minutes = differenceMinute.inMinutes;
    int hour = differenceHour.inHours;
    int day = differenceDay.inDays;
    if (minutes == 60) {
      minutes = 0;
      hour += 1;
    }
    if (hour == 24) {
      hour = 0;
      day += 1;
    }

    return '${day == 0 ? '' : '$day天'}${hour == 0 ? '' : '$hour小时'}${minutes == 0 ? '' : '$minutes分钟'}';
  }


  /// 发送短信
  Future<void> launchSmsURL(String phone, String message) async {
    if (Platform.isAndroid) {
      SystemUtil.sendSms(phone, message);
    } else {
      String uriStr = Uri.encodeComponent(message);
      final Uri uri = Uri.parse('sms: $phone&body=$uriStr');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        toast('打开失败');
      }
    }
  }

  // 打电话
  Future<void> launchTelURL(String phone) async {
    final Uri uri = Uri.parse('tel: $phone');

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      toast('打开失败');
    }
  }

  //获取个人资料--仅登录

  getUserInfo() async {
    int loginTime = DateTime.now().millisecondsSinceEpoch;
    Map tokenDic = await UserDefault.getData(Define.TOKENKEY);
    tokenDic['loginTime'] = loginTime;
    await UserDefault.setData(Define.TOKENKEY, tokenDic);
    DioUtil()
        .get(LoginApi.GETPERSALINFO, null, true, () {}, isShowLoading: false)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        tokenDic.addAll(data['data']);
        await UserDefault.setData(Define.TOKENKEY, tokenDic);
        Get.offAllNamed('/home');
        Channel().invoke(Channel_LoginInfoFeedback, tokenDic);
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //获取人员信息with imId
  static Future getUserInfoWithImIds(List<String> sessionIds) async{
    try{
      var datasource = WorkbenchDataSource(retrofitDio);
      var resp = await datasource.getInfoWithSessionIds(sessionIds);
      logger('====resp===${resp.toString()}');
      if (resp.success()){
        return resp.data;
      }
      return null;
    }catch(e){
      return null;
    }
  }

  String generateMD5(String data) {
    Uint8List content = Utf8Encoder().convert(data);
    Digest digest = md5.convert(content);
    return digest.toString();
  }

  //获取appVersion
  getAppVersion() async {
    Map<String, dynamic> headerMap = await UserDefault.getHttpHeader();
    String version = headerMap['appVersion'];
    List versionList = version.split('.');
    String appVersion = '';

    for (var i = 0; i < versionList.length; i++) {
      String versionText = versionList[i];
      if (versionText.length == 1 && i != 0) {
        versionText = '0$versionText';
      }
      appVersion = '$appVersion$versionText';
    }
    return appVersion;
  }

  //获取随机4位数
  getRandomStr() {
    String randomStr = Random().nextInt(10).toString();
    for (var i = 0; i < 3; i++) {
      var str = Random().nextInt(10);
      randomStr = '$randomStr$str';
    }
    return randomStr;
  }

  //获取会议合适的时间15分钟间隔
  getMeetingTime() {
    var formatter = DateFormat('HH:mm');
    String timeStr = formatter.format(DateTime.now());
    List timeList = timeStr.split(':');
    String minute = timeList.last;

    int minuteInt = int.parse(minute);
    int timeSpace = 0;
    if (minuteInt < 15) {
      timeSpace = 15 - minuteInt;
    } else if (minuteInt < 30) {
      timeSpace = 30 - minuteInt;
    } else if (minuteInt < 45) {
      timeSpace = 45 - minuteInt;
    } else if (minuteInt <= 59) {
      timeSpace = 60 - minuteInt;
    }
    return DateTime.now().millisecondsSinceEpoch + timeSpace * 60 * 1000;
  }

  String getCurrentNetStr() {
    return NetUtil.netStr;
  }
}

//16进制色值转换无透明度
class ColorUtil {
  static backColorString(String color) {
    if (color.startsWith('0x')) {
      if (color.length == 8) {
        color = color.replaceRange(0, 2, '0xff');
      }
    }
    if (color.startsWith('#')) {
      if (color.length == 7) {
        color = color.replaceRange(0, 1, '0xff');
      }
    }
    return color;
  }

  static backJingColor(String colorString) {
    if (colorString.length > 6) {
      colorString = colorString.replaceRange(0, colorString.length - 6, '#');
    }
    return colorString;
  }
}

class ChinaTextEditController extends TextEditingController {
  ///拼音输入完成后的文字
  var completeText = '';
  GroupAvatarUpgradeController? groupAvatarController;
  @override
  TextSpan buildTextSpan(
      {required BuildContext context,
      TextStyle? style,
      required bool withComposing}) {
    // TODO: implement buildTextSpan
    if (!value.composing.isValid || !withComposing) {
      if (completeText != value.text) {
        completeText = value.text;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
      if (groupAvatarController != null) {
        if (groupAvatarController!.calculateLength(text) > 8) {
          text = completeText;
        }
      }
      return TextSpan(style: style, text: text);
    }

    ///返回输入样式，可自定义样式
    final TextStyle composingStyle = style!.merge(
      const TextStyle(decoration: TextDecoration.underline),
    );
    return TextSpan(style: style, children: <TextSpan>[
      TextSpan(text: value.composing.textBefore(value.text)),
      TextSpan(
        style: composingStyle,
        text: value.composing.isValid && !value.composing.isCollapsed
            ? value.composing.textInside(value.text)
            : "",
      ),
      TextSpan(text: value.composing.textAfter(value.text)),
    ]);
  }
}

class MyCliper extends CustomClipper<Path> {
  double? padding;
  double? offsetY;
  double? radius;
  MyCliper(this.offsetY, this.radius, this.padding);

  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);
    path.addOval(
        Rect.fromCircle(center: Offset(padding!, offsetY!), radius: radius!));
    path.addOval(Rect.fromCircle(
        center: Offset(size.width - padding!, offsetY!), radius: radius!));
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return false;
  }
}
