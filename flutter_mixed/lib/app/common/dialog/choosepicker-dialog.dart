import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../logger/logger.dart';

class ChoosePickerDialog {
  List dataList = [];
  List indexList = [];
  ChoosePickerDialog(this.dataList,this.indexList);
  show(Function onPressed) {
    
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(() => Scaffold(
              backgroundColor: Colors.transparent,
              body: Stack(
                children: [
                  Positioned(
                      left: 0,
                      right: 0,
                      bottom: 330 + DeviceUtils().bottom.value,
                      height: DeviceUtils().height.value -
                          330 -
                          DeviceUtils().bottom.value,
                      child: InkWell(
                        onTap: () {
                          hide();
                        },
                        child: Container(
                          color: Colors.transparent,
                        ),
                      )),
                  Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      height: 330 + DeviceUtils().bottom.value,
                      child: Container(
                        color: ColorConfig.whiteColor,
                        width: DeviceUtils().width.value,
                        height: 330 + DeviceUtils().bottom.value,
                        padding:
                            EdgeInsets.only(bottom: DeviceUtils().bottom.value),
                        child: Column(
                          children: [
                            Container(
                              height: 50,
                              padding: EdgeInsets.only(left: 15, right: 15),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      hide();
                                    },
                                    child: Container(
                                      alignment: Alignment.centerLeft,
                                      width: 50,
                                      child: Text(
                                        '取消',
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: ColorConfig.desTextColor),
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
    
                                      onPressed(indexList);
                                      Get.back();
                                    },
                                    child: Container(
                                      alignment: Alignment.centerRight,
                                      width: 50,
                                      child: Text(
                                        '确定',
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: ColorConfig.themeCorlor),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: DeviceUtils().width.value,
                              height: 280,
                              child: Stack(
                                children: [
                                  Row(
                                    children: backPickerListWidget(),
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ))
                ],
              ),
            )));
  }

  backPickerListWidget() {
    List<Widget> lists = [];
    logger('indexList========$indexList');
    for (var i = 0; i < dataList.length; i++) {
      List contentList = dataList[i];
      lists.add(Expanded(
          child: CupertinoPicker.builder(
              childCount: contentList.length,
              scrollController: FixedExtentScrollController(initialItem: indexList[i]),
              itemExtent: 30,
              onSelectedItemChanged: (index) {
                indexList[i] = index;
              },
              itemBuilder: (context, index) {
                return Container(
                  child: Text(
                    contentList[index],
                    style: TextStyle(
                        fontSize: 14, color: ColorConfig.mainTextColor),
                  ),
                );
              })));
    }
    return lists;
  }

  hide() {
    Get.back();
  }
}
