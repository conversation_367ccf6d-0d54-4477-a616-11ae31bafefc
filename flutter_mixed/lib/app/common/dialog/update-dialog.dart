import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/home/<USER>/home_controller.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:get/get.dart';

class UpdateDialog {
  show(Map updateDic, String currentVersion) {
    double padding = 30;
    int status = updateDic['status'];

    ///** 0是代表是当前版本 1是强制更新 2是非强制更新 **/
    RxString desc = ''.obs;
    String versionName = updateDic['versionName']; //最新版本号

    desc.value = updateDic['desc'];
    RxBool isSave = false.obs;

    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(() => Scaffold(
            backgroundColor: Colors.transparent,
            body: Container(
                padding: EdgeInsets.only(left: 40, right: 40),
                alignment: Alignment.center,
                child: Container(
                  decoration: BoxDecoration(
                      color: ColorConfig.whiteColor,
                      borderRadius: BorderRadius.circular(8)),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: EdgeInsets.fromLTRB(padding, 20, padding, 20),
                        child: Text(
                          '检测到最新版本',
                          style: TextStyle(
                              fontSize: 20,
                              color: ColorConfig.mainTextColor,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                      Container(
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.only(
                            left: padding, right: padding, bottom: 20),
                        child: Text(
                          'v$versionName版本更新内容',
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                        ),
                      ),
                      Container(
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.only(left: padding, right: padding),
                        child: Text(
                          desc.value,
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          maxLines: null,
                        ),
                      ),
                      Offstage(
                        offstage: status == 1,
                        child: Container(
                          alignment: Alignment.centerLeft,
                          padding:
                              EdgeInsets.fromLTRB(padding, 10, padding, 10),
                          child: Text(
                            '是否现在更新?',
                            style: TextStyle(
                                fontSize: 14, color: ColorConfig.themeCorlor),
                          ),
                        ),
                      ),
                      Offstage(
                        offstage: status == 1,
                        child: InkWell(
                          onTap: () async {
                            if (isSave.value) {
                              isSave.value = false;
                              await UserDefault.removeData(
                                  Define.UPDATE_VERSION);
                            } else {
                              isSave.value = true;

                              await UserDefault.setData(
                                  Define.UPDATE_VERSION, currentVersion);
                            }
                          },
                          child: Container(
                            padding:
                                EdgeInsets.fromLTRB(padding, 5, padding, 5),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: Image.asset(isSave.value
                                      ? 'assets/images/3.0x/login_selected.png'
                                      : 'assets/images/3.0x/login_unselect.png'),
                                ),
                                10.gap,
                                Container(
                                    child: Text(
                                  '当前版本不再提示',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                  maxLines: null,
                                ))
                              ],
                            ),
                          ),
                        ),
                      ),
                      10.gap,
                      Divider(
                        height: 1,
                        color: ColorConfig.lineColor,
                      ),
                      status == 1
                          ? InkWell(
                              onTap: () {
                                Channel()
                                    .invoke(Channel_Native_Update, updateDic);
                              },
                              child: Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.only(top: 15, bottom: 15),
                                child: Text(
                                  '立即更新',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: ColorConfig.themeCorlor),
                                ),
                              ),
                            )
                          : Container(
                              padding: EdgeInsets.only(top: 15, bottom: 15),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: InkWell(
                                      onTap: () {
                                        HomeController homeController =
                                            Get.find();
                                        homeController.getIsHaveOutMeeting();
                                        hide();
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: Text(
                                          '以后再说',
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: ColorConfig.desTextColor),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: InkWell(
                                      onTap: () {
                                        hide();
                                        HomeController homeController =
                                            Get.find();
                                        homeController.getIsHaveOutMeeting();
                                        Channel().invoke(
                                            Channel_Native_Update, updateDic);
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: Text(
                                          '立即更新',
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: ColorConfig.themeCorlor),
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            )
                    ],
                  ),
                )))));
  }

  hide() {
    Get.back();
  }
}
