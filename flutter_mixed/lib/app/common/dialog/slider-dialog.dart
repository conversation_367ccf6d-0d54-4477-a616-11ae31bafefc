import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/modules/contact/aboutOrg/orgTransferCreater/controllers/org_transfer_creater_controller.dart';
import 'package:flutter_mixed/app/modules/login/resetPwd/controllers/reset_pwd_controller.dart';
import 'package:flutter_mixed/app/modules/login/verifyCode/controllers/verify_code_controller.dart';
import 'package:get/get.dart';

import '../../utils/http.dart';
import '../../../logger/logger.dart';
import '../api/LoginApi.dart';
import '../config/config.dart';
import '../widgets/widgets.dart';

class SliderDialog {
  String phone = '';
  int type = 0;
  String backStr = '';
  String topStr = '';
  bool isJump = true;

  SliderDialog(this.phone, this.type, this.backStr, this.topStr, this.isJump);

  RxString msg = ''.obs;
  RxDouble sliderValue = 0.00.obs;
  RxDouble offsetX = 0.00.obs;

  double startValue = 0.00;
  double moveValue = 0.00;
  double scale = 0.00;

  int errorInt = 0;
  show() {
    double screenW = DeviceUtils().width.value;
    double screenH = DeviceUtils().height.value;
    double width = screenW > 350 ? 340 : screenW-25;

    double imageW = width - 30;
    double imageH = imageW * 360 / 620;
    double height = 160 + imageH;

    double sliderW = imageW*40/310;//抠图区域宽度占背景图片宽度百分比
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(() => Scaffold(
              backgroundColor: Colors.transparent,
              body: Stack(
                children: [
                  Container(
                      alignment: Alignment.center, color: ColorConfig.maskColor),
                  Positioned(
                      left: (screenW - width) * 0.5,
                      top: (screenH - height) * 0.5,
                      width: width,
                      height: height,
                      child: Container(
                        padding: const EdgeInsets.only(left: 15, right: 15),
                        width: width,
                        height: height,
                        color: Colors.white,
                        child: Column(
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              width: double.infinity,
                              height: 55,
                              child: const Text(
                                '拖动下方滑块完成拼图',
                                style: TextStyle(
                                    color: ColorConfig.mainTextColor,
                                    fontSize: 15),
                              ),
                            ),
                            Container(
                              width: imageW,
                              height: imageH,
                              child: Stack(
                                children: [
                                  Container(child: Image.network(backStr)),
                                  Positioned(
                                      left: sliderValue.value * (imageW - sliderW),
                                      top: 0,
                                      child: Container(
                                        width: sliderW, 
                                        height: imageH,
                                        child: Image.network(topStr),
                                      )),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 11,
                            ),
                            Container(
                                alignment: Alignment.centerLeft,
                                width: imageW,
                                height: 35,
                                padding: const EdgeInsets.all(0),
                                child: Stack(
                                  children: [
                                    Positioned(
                                        top: 12.5,
                                        left: 0,
                                        child: Container(
                                          width: imageW,
                                          height: 10,
                                          decoration: BoxDecoration(
                                              color: ColorConfig.lineColor,
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                        )),
                                    Positioned(
                                        top: 0,
                                        left: offsetX.value,
                                        width: 61.2,
                                        height: 35,
                                        child: GestureDetector(
                                          onHorizontalDragStart: (details) {
                                            startValue =
                                                details.globalPosition.dx;
                                          },
                                          onHorizontalDragUpdate: (details) {
                                            moveValue =
                                                details.globalPosition.dx;
                                            double offset =
                                                moveValue - startValue;
                                            if (offset > imageW - 61.2) {
                                              offset = imageW - 61.2;
                                            }
                                            if (offset < 0) {
                                              offset = 0;
                                            }
                                            offsetX.value = offset;

                                            sliderValue.value =
                                                offset / (imageW - 61.2);
                                          },
                                          onHorizontalDragEnd: (details) {
                                            scale = sliderValue.value *
                                                (imageW - sliderW) /
                                                imageW;
                                            checkSlider(scale);
                                          },
                                          child: Container(
                                            alignment: Alignment.centerLeft,
                                            width: 61.2,
                                            height: 35,
                                            child: Image.asset(
                                                'assets/images/3.0x/login_sliderBtn.png'),
                                          ),
                                        ))
                                  ],
                                )),
                            Expanded(
                                child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(msg.value,
                                    style: const TextStyle(
                                        color: Colors.red, fontSize: 13)),
                                InkWell(
                                  onTap: () {
                                    getImageCode();
                                  },
                                  child: Container(
                                    alignment: Alignment.centerRight,
                                    width: 30,
                                    height: 30,
                                    child: Image.asset(
                                        'assets/images/3.0x/login_code_refresh.png'),
                                  ),
                                )
                              ],
                            ))
                          ],
                        ),
                      ))
                ],
              ),
            )));
  }

  hide() {
    Get.back();
  }

  checkSlider(scale) async {
    Map dict = {
      'newId': backStr,
      'oriId': topStr,
      'phone': phone,
      'scale': scale,
      'type': type == 101 ? 1 : type
    };

    DioUtil().post(LoginApi.CHECK_IMAGE_SEND_CODE, dict, false, () {
      resetValue();
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1 || data['code'] == 1110) {
        msg.value = '';
        if (isJump) {
          hide();
          if (type == 7 && Host.TELLIST.contains(phone)) {
            Get.toNamed('/pwd-login', arguments: {'phone': phone});
          } else {
            Get.toNamed('/verify-code',
                arguments: {'phone': phone, 'type': type});
          }
        } else {
          Get.back();
          if (type == 4) {
            ResetPwdController controller = Get.find();
            controller.timerStart();
          } else if (type == 12) {
            OrgTransferCreaterController controller = Get.find();
            controller.timerStart();
          } else {
            VerifyCodeController controller = Get.find();
            controller.timerStart();
          }
        }
        if (data['code'] == 1110) {
          toast('${data['msg']}');
        }
      } else {
        resetValue();
        if (data['code'] == 1500) {
          msg.value = '验证失败，请控制拼图对齐缺口';
        } else {
          msg.value = '';
        }

        toast('${data['msg']}');
        errorInt++;
        if (errorInt == 3) {
          errorInt = 0;
          getImageCode();
        }
      }
    });
  }

  resetValue() {
    sliderValue.value = 0.00;
    offsetX.value = 0.0;

    startValue = 0.00;
    moveValue = 0.00;
    scale = 0.00;
    msg.value = '';
  }

  getImageCode() async {
    DioUtil().get(LoginApi.GET_IMAGE_CODE + phone, null, false, () {
    }).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        backStr = data['data']['backUrl'];
        topStr = data['data']['topUrl'];
        resetValue();
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
