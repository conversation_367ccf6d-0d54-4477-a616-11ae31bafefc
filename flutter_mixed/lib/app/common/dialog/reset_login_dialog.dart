


import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../routes/app_pages.dart';
import '../channel/channel.dart';

showReLoginDialog(String? msg) {
  SmartDialog.show(
    builder: (context) {
      return Container(
        margin: EdgeInsets.only(left: 60, right: 60),
        height: 130,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            10.gap,
            Text(msg ?? '登录失效了',
                style: TextStyle(fontSize: 16, color: Colors.black)),
            10.gap,
            Container(
              padding: EdgeInsets.only(left: 15, right: 15),
              child: MaterialButton(
                  color: Colors.blue,
                  child: Text(
                    '请重新登录',
                    style: TextStyle(fontSize: 16, color: Colors.white),
                  ),
                  onPressed: () {
                    SmartDialog.dismiss();
                    if (Platform.isIOS) {
                      Channel().invoke(Channel_didClickLoginOut, {});
                    }
                    Get.offAllNamed(Routes.LOGIN);
                  }),
            )
          ],
        ),
      );
    },
    keepSingle: true,
    debounce: false,
    backDismiss: false,
    useAnimation: false,
    clickMaskDismiss: false,
  );
}