import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class DDDatePickerDialog {
  DateTime initialDate = DateTime.now();
  RxBool isTime = false.obs;

  DateTime? currentDateData;
  DateTime? currentTimeData;

  RxString currentDateStr = ''.obs;
  RxString currentTimeStr = ''.obs;
  DDDatePickerDialog(this.initialDate);
  show(Function onPressed, int dateChoose,{bool isMeeting = false,DateTime? minDate,DateTime ?maxDate}) {
    var formatter0 = DateFormat('yyyy-MM-dd');
    var formatter1 = DateFormat('HH:mm');
    currentDateStr.value = formatter0.format(initialDate);
    currentTimeStr.value = formatter1.format(initialDate);
    if(dateChoose == 2){
      isTime.value = true;
    }
    Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        Obx(
          () => Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              children: [
                Positioned(
                    left: 0,
                    right: 0,
                    bottom: 330 + DeviceUtils().bottom.value,
                    height: DeviceUtils().height.value -
                        330 -
                        DeviceUtils().bottom.value,
                    child: InkWell(
                      onTap: () {
                        hide();
                      },
                      child: Container(
                        color: Colors.transparent,
                      ),
                    )),
                Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    height: 330 + DeviceUtils().bottom.value,
                    child: Container(
                      color: ColorConfig.whiteColor,
                      width: DeviceUtils().width.value,
                      height: 330 + DeviceUtils().bottom.value,
                      padding:
                          EdgeInsets.only(bottom: DeviceUtils().bottom.value),
                      child: Column(
                        children: [
                          Container(
                            height: 50,
                            padding: EdgeInsets.only(left: 15, right: 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                InkWell(
                                  onTap: () {
                                    hide();
                                  },
                                  child: Container(
                                    alignment: Alignment.centerLeft,
                                    width: 50,
                                    child: Text(
                                      '取消',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.desTextColor),
                                    ),
                                  ),
                                ),
                                Offstage(
                                  offstage: dateChoose == 2,
                                  child: InkWell(
                                    onTap: () {
                                      isTime.value = false;
                                      currentDateStr.refresh();
                                    },
                                    child: Container(
                                      child: Text(
                                        currentDateStr.value,
                                      ),
                                    ),
                                  ),
                                ),
                                Offstage(
                                  offstage: dateChoose == 1,
                                  child: InkWell(
                                    onTap: () {
                                      isTime.value = true;
                                      currentDateStr.refresh();
                                    },
                                    child: Container(
                                      child: Text(
                                        currentTimeStr.value,
                                      ),
                                    ),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    String currentStr =
                                        '${currentDateStr.value} ${currentTimeStr.value}';
       
                                    DateTime backTime =
                                        DateTime.parse(currentStr);
                                    onPressed(
                                        '${backTime.millisecondsSinceEpoch}');
                                    Get.back();
                                  },
                                  child: Container(
                                    alignment: Alignment.centerRight,
                                    width: 50,
                                    child: Text(
                                      '确定',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: ColorConfig.themeCorlor),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: DeviceUtils().width.value,
                            height: 280,
                            child: Stack(
                              children: [
                                Offstage(
                                  offstage: isTime.value,
                                  child: CupertinoDatePicker(
                                      initialDateTime:
                                          currentDateData ?? initialDate,
                                      mode: CupertinoDatePickerMode.date,
                                      minimumDate: minDate,
                                      maximumDate: maxDate,
                                      onDateTimeChanged: (date) {
                                        String formaStr = 'yyyy-MM-dd';
                                        if (isTime.value) {
                                          formaStr = 'HH:mm';
                                        }
                                        var formatter = DateFormat(formaStr);
                                        if (isTime.value) {
                                          currentTimeStr.value =
                                              formatter.format(date);
                                        } else {
                                          currentDateStr.value =
                                              formatter.format(date);
                                        }

                                        currentDateData = date;
                                      }),
                                ),
                                Offstage(
                                  offstage: !isTime.value,
                                  child: CupertinoDatePicker(
                                      use24hFormat: true,
                                      initialDateTime:
                                           currentDateData ?? initialDate,
                                      mode: CupertinoDatePickerMode.time,
                                      minuteInterval: isMeeting? 15:1,
                                      minimumDate: minDate,
                                      maximumDate: maxDate,
                                      onDateTimeChanged: (date) {
                                        String formaStr = 'yyyy-MM-dd';
                                        if (isTime.value) {
                                          formaStr = 'HH:mm';
                                        }
                                        var formatter = DateFormat(formaStr);
                                        if (isTime.value) {
                                          currentTimeStr.value =
                                              formatter.format(date);
                                        } else {
                                          currentDateStr.value =
                                              formatter.format(date);
                                        }
                                        currentDateData = date;
                                      }),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ))
              ],
            ),
          ),
        ));
  }

  hide() {
    Get.back();
  }
}
