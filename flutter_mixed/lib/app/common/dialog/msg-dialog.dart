import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class MsgDiaLog {
  String title = '';
  String content = '';
  String leftStr = '';
  String rightStr = '';
  late Function onPressedLeft;
  late Function onPressedRight;
  Color? leftColor;
  Color? rightColor;
  MsgDiaLog(this.title, this.content, this.leftStr, this.rightStr,
      this.onPressedLeft, this.onPressedRight,
      {this.leftColor, this.rightColor});
  show() {
    leftColor ??= ColorConfig.mainTextColor;

    rightColor ??= ColorConfig.themeCorlor;
    showDialog(
        context: Get.context!,
        builder: (context) {
          return CupertinoAlertDialog(
            title: Text(title),
            content: Text(content),
            actions: rightStr.isEmpty
                ? [
                    CupertinoButton(
                        child: Text(
                          leftStr,
                          style: TextStyle(color: leftColor),
                        ),
                        onPressed: () {
                          onPressedLeft();
                        }),
                  ]
                : [
                    CupertinoButton(
                        child: Text(
                          leftStr,
                          style: TextStyle(color: leftColor),
                        ),
                        onPressed: () {
                          onPressedLeft();
                        }),
                    CupertinoButton(
                        child: Text(
                          rightStr,
                          style: TextStyle(color: rightColor),
                        ),
                        onPressed: () {
                          onPressedRight();
                        })
                  ],
          );
        });
  }
}
