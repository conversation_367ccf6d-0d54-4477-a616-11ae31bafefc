import 'package:flutter_mixed/app/im/db/entity/message.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';

import '../../modules/contact/model/org/external_model.dart';

class DeptRefresh{
  OrgModel model;
  DeptRefresh(this.model);
}

class ExternalRefresh{
  ExternalModel model;
  ExternalRefresh(this.model);
}

class EventWithDraw {
  Message message;
  EventWithDraw(this.message);
}

class EventTianditu {
  Map param;
  EventTianditu(this.param);
}

class EventCloseWebView {
  Map param;
  EventCloseWebView(this.param);
}