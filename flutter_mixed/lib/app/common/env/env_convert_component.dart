import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/env/env_local.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/login_util.dart';
import 'package:get/get.dart';


// 切换环境组件： 仅在Flutter端触发 ，回传给native，依赖本地native缓存，重启app生效
// dev模式直接开启；  product 模式隐藏式开启，连击5次显示
class ConvertEnvComponent extends StatefulWidget {

  final VoidCallback? updateParent;

  const ConvertEnvComponent({super.key, this.updateParent});

  @override
  State<StatefulWidget> createState() => _ConvertEnvState();
}

class _ConvertEnvState extends State<ConvertEnvComponent> {

  int clickCount = 0;

  bool _show = false;

  @override
  Widget build(BuildContext context) {
    return Visibility(
         visible: _show,
        maintainState:true,
        maintainSize: true,
        maintainAnimation:true,
        maintainInteractivity: true,
        child: InkWell(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          onTap: ()=> _triggerEnv(),
          child: Container(
            color: Colors.transparent,
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text('当前环境: ${EnvConfig.mEnv.name}'),
                const Spacer(),
                TextButton(onPressed: () => _showSwitchDialog(Get.context! , (currentEnv) async {
                      logger('11');
                      try{
                        Future.delayed(const Duration(seconds: 3)).then((value) {
                          Channel().invoke(ChannelReStartApp);
                        });

                        await LoginUtil.reLogin(invokeNative: true);

                        await Channel().invoke(ChannelSwitchEnv , {
                          "env_index" : currentEnv.index
                        });
                      }catch(e){}

                }), child: const Text('切换环境')),
              ],
            ),
          ),
        ));
  }

  _triggerEnv() {
    if(clickCount == 5){
      clickCount = 0;
      _show = true;
    }else {
      clickCount ++ ;
      _show = false;
    }
    setState(() {});
  }

  _showSwitchDialog(BuildContext context , Function(Env) getReStart) {
    if(!_show) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
          context: context,
          builder: (_) {
            return SimpleDialog(
              title: const Text('点击切换环境'),
              children: _buildEnvs(context , (){
                setState(() {});

                widget.updateParent?.call();
              } , getReStart ));
          });
    });
  }

  _buildEnvs(BuildContext context ,VoidCallback callback , Function(Env) v) {
    return Env.values.map((currentEnv) => Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        TextButton(onPressed: () async {
          await resetLocalEnv(currentEnv);
          Navigator.of(context).pop();
          toast('切换成功...重启中');
          v(currentEnv);
          callback();

        }, child: Text(
          currentEnv.name,
          style: TextStyle(color: currentEnv == EnvConfig.mEnv ? Colors.blue : Colors.black),
        )),
      ],
    )).toList();
  }


}