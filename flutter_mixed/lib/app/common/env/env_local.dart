import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:shared_preferences/shared_preferences.dart';

var _ENV_SHARE_KEY = 'ENV_SHARE_KEY';


Future<Env?> getCurrentEnv() async{
  SharedPreferences preferences = await SharedPreferences.getInstance();
  var envIndex = preferences.getInt(_ENV_SHARE_KEY);
  if(envIndex != null){
    EnvConfig.mEnv = Env.values[envIndex];
    return EnvConfig.mEnv;
  }
  return null;
}

Future resetLocalEnv(Env? env) async {
  if(env == null) return;
  SharedPreferences preferences = await SharedPreferences.getInstance();
  var index = Env.values.indexOf(env);
  if(index < 0 || index > Env.values.length) return false;
  preferences.setInt(_ENV_SHARE_KEY, index);
  EnvConfig.mEnv = env;
  return true;
}