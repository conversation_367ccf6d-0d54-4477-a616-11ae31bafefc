

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../logger/logger.dart';

void catchErrorPage() {

  // 设置自定义错误小部件
  ErrorWidget.builder = (FlutterErrorDetails details) {
    logger('构建自定义错误小部件: ${details.exception}');
    return CustomErrorHandle(
      errorMessage: details.exception.toString(),
      stackTrace: details.stack,
    );
  };

  // 使用 runZonedGuarded 包装整个应用，捕获所有未处理的异步错误
  runZonedGuarded(() {
    // 注意：这里不需要再次调用 runApp，因为您的 main 方法已经调用了
    // 这里只是设置错误处理的 Zone
  }, (Object error, StackTrace stack) {
    logger('未捕获的异步错误: $error');
    logger('堆栈跟踪: $stack');
  });
}

class CustomErrorHandle extends StatelessWidget {
  final String errorMessage;
  final StackTrace? stackTrace;

  const CustomErrorHandle({
    Key? key,
    required this.errorMessage,
    this.stackTrace,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 确保即使在 context 无效的情况下也能显示错误
    if (context == null) {
      return _buildErrorWidget();
    }

    // 格式化堆栈跟踪，提取文件位置信息
    String formattedStack = _formatStackTrace();

    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        color: Colors.grey.withOpacity(0.5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.6, // 增加高度以显示更多信息
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10), color: Colors.white),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: const [
                    Icon(Icons.warning_amber_sharp, color: Colors.red),
                    SizedBox(width: 8),
                    Text(
                      "错误提示",
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    )
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  "错误信息:",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  errorMessage,
                  style: TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 16),
                Text(
                  "错误位置:",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Expanded(
                  child: SingleChildScrollView(
                    child: Text(
                      formattedStack,
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: _exitApp,
                      child: const Text('关闭应用'),
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 格式化堆栈跟踪，提取文件位置信息
  String _formatStackTrace() {
    if (stackTrace == null) {
      return "无堆栈信息";
    }

    // 将堆栈跟踪转换为字符串
    String stackString = stackTrace.toString();

    // 分割成行
    List<String> lines = stackString.split('\n');

    // 提取有用的信息（文件路径、行号、列号）
    List<String> formattedLines = [];
    for (String line in lines) {
      // 跳过不包含文件信息的行
      if (!line.contains('dart:') && line.contains('flutter_mixed')) {
        // 尝试提取文件路径和行号
        RegExp regExp = RegExp(r'(#\d+\s+)?(.*) \((.+?):(\d+):(\d+)\)');
        Match? match = regExp.firstMatch(line);

        if (match != null) {
          String function = match.group(2) ?? "";
          String file = match.group(3) ?? "";
          String lineNumber = match.group(4) ?? "";
          String column = match.group(5) ?? "";

          // 简化文件路径，只保留最后几个部分
          List<String> pathParts = file.split('/');
          String simplifiedPath = pathParts.length > 2
              ? '.../${pathParts.sublist(pathParts.length - 2).join('/')}'
              : file;

          formattedLines.add("在 $simplifiedPath 第 $lineNumber 行: $function");
        } else {
          formattedLines.add(line);
        }
      }
    }

    // 如果没有提取到有用的信息，返回原始堆栈
    if (formattedLines.isEmpty) {
      // 限制行数，避免过长
      if (lines.length > 10) {
        return lines.sublist(0, 10).join('\n') + '\n...（更多行被省略）';
      }
      return stackString;
    }

    return formattedLines.join('\n');
  }

  // 当 context 无效时使用的备用错误小部件
  Widget _buildErrorWidget() {
    String formattedStack = _formatStackTrace();

    return Container(
      color: Colors.red.withOpacity(0.3),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '应用发生错误:',
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                errorMessage,
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 16),
              Text(
                '错误位置:',
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Flexible(
                child: SingleChildScrollView(
                  child: Text(
                    formattedStack,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _exitApp,
                child: const Text('关闭应用'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _exitApp() {
    if (Platform.isAndroid) {
      // Android平台使用SystemNavigator退出
      SystemNavigator.pop();
    } else if (Platform.isIOS) {
      // iOS平台使用exit退出
      exit(0);
    } else {
      // 其他平台直接使用exit
      exit(0);
    }
  }
}





