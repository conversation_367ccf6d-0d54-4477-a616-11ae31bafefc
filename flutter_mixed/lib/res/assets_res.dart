// Generated file. Do not edit.
// This file is generated by the iFlutter

// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars
class AssetsRes {
  AssetsRes._();

  static const String PROJECT_NAME = 'flutter_mixed';
  static const String PROJECT_VERSION = '1.0.0+1';
  static const String CITY = 'assets/city.json';
  static const String NAVBAR = 'assets/images/NavBar.png';
  static const String ABOUT_ICON = 'assets/images/about_icon.png';
  static const String ABOUT_ICON_CLEAR = 'assets/images/about_icon_clear.png';
  static const String AI_CLOSE_ICON = 'assets/images/ai_close_icon.png';
  static const String AI_ENTER_ICON = 'assets/images/ai_enter_icon.png';
  static const String AI_ORDER_MEAL = 'assets/images/ai_order_meal.png';
  static const String AI_QUESTION_ICON = 'assets/images/ai_question_icon.png';
  static const String APPROVAL_SESSION_LOGO = 'assets/images/approval_session_logo.png';
  static const String APPROVE_ADDAPPRO = 'assets/images/approve_addAppro.png';
  static const String APPROVE_COMMENT = 'assets/images/approve_comment.png';
  static const String APPROVE_DELETE = 'assets/images/approve_delete.png';
  static const String APPROVE_DELETEUSER = 'assets/images/approve_deleteUser.png';
  static const String APPROVE_MORE = 'assets/images/approve_more.png';
  static const String APPROVE_REMIND = 'assets/images/approve_remind.png';
  static const String APPROVE_SELECTED = 'assets/images/approve_selected.png';
  static const String APPROVE_SIGNREAD = 'assets/images/approve_signRead.png';
  static const String APPROVE_UNSELECTED = 'assets/images/approve_unselected.png';
  static const String APPROVE_UPLOAD = 'assets/images/approve_upLoad.png';
  static const String APPROVE_WITHDRAW = 'assets/images/approve_withdraw.png';
  static const String ARRAW_RIGHT = 'assets/images/arraw_right.png';
  static const String ARROW_FORWARD = 'assets/images/arrow_forward.png';
  static const String BG_IDCARD_BACK = 'assets/images/bg_idcard_back.png';
  static const String BG_IDCARD_FORWARD = 'assets/images/bg_idcard_forward.png';
  static const String BG_TAKE_PHOTO = 'assets/images/bg_take_photo.png';
  static const String BLUE_ADD = 'assets/images/blue_add.png';
  static const String CHAT_ME_VIDEOCALL = 'assets/images/chat_me_videocall.png';
  static const String CHAT_ME_VOICECALL = 'assets/images/chat_me_voicecall.png';
  static const String CHAT_OTHER_VIDEO_CALL = 'assets/images/chat_other_video_call.png';
  static const String CHAT_OTHER_VOICE_CALL = 'assets/images/chat_other_voice_call.png';
  static const String CONTACT_ADD = 'assets/images/contact_add.png';
  static const String CONTACT_ADD_QRCODE = 'assets/images/contact_add_qrcode.png';
  static const String CONTACT_ADD_SEARCH = 'assets/images/contact_add_search.png';
  static const String CONTACT_ADD_TEL = 'assets/images/contact_add_tel.png';
  static const String CONTACT_CREATE_ORG = 'assets/images/contact_create_org.png';
  static const String CONTACT_DEPT_CLOSE = 'assets/images/contact_dept_close.png';
  static const String CONTACT_DEPT_LOGO = 'assets/images/contact_dept_logo.png';
  static const String CONTACT_DEPT_SELECTED = 'assets/images/contact_dept_selected.png';
  static const String CONTACT_HOME_ARCHITECTURE = 'assets/images/contact_home_architecture.png';
  static const String CONTACT_HOME_GROUP = 'assets/images/contact_home_group.png';
  static const String CONTACT_HOME_MY_FRIEND = 'assets/images/contact_home_my_friend.png';
  static const String CONTACT_HOME_NEW_FRIEND = 'assets/images/contact_home_new_friend.png';
  static const String CONTACT_INVITE_IM = 'assets/images/contact_invite_im.png';
  static const String CONTACT_INVITE_SMS = 'assets/images/contact_invite_sms.png';
  static const String CONTACT_INVITE_WECHAT = 'assets/images/contact_invite_wechat.png';
  static const String CONTACT_JOIN_ORG = 'assets/images/contact_join_org.png';
  static const String CONTACT_MANAGER_ORG = 'assets/images/contact_manager_org.png';
  static const String CONTACT_MOREN_HEAD = 'assets/images/contact_moren_head.png';
  static const String CONTACT_MYQRCODE = 'assets/images/contact_myQrCode.png';
  static const String CONTACT_ORG_CAMERA = 'assets/images/contact_org_camera.png';
  static const String CONTACT_ORG_FRAMEWORK = 'assets/images/contact_org_framework.png';
  static const String CONTACT_STAFFINFO_SEXNON = 'assets/images/contact_staffInfo_sexNon.png';
  static const String EXTERNAL_ADD = 'assets/images/external_add.png';
  static const String EXTERNAL_CONTACT = 'assets/images/external_contact.png';
  static const String EXTERNAL_EDIT = 'assets/images/external_edit.png';
  static const String EXTERNAL_SCREEN = 'assets/images/external_screen.png';
  static const String FEED_BACK_CLOSE = 'assets/images/feed_back_close.png';
  static const String FEED_BACK_NO_IMAGE = 'assets/images/feed_back_no_image.png';
  static const String FEED_BACK_RECORD = 'assets/images/feed_back_record.png';
  static const String FEED_BACK_STAR = 'assets/images/feed_back_star.png';
  static const String GROUP_AVATAR_COLOR_CHECK = 'assets/images/group_avatar_color_check.png';
  static const String GROUP_CHANGE_AVATAR = 'assets/images/group_change_avatar.png';
  static const String GROUP_POPULATION = 'assets/images/group_population.png';
  static const String HOME_APPROVE = 'assets/images/home_approve.png';
  static const String HOME_APPROVE_SELECTED = 'assets/images/home_approve_selected.png';
  static const String HOME_CONTACT = 'assets/images/home_contact.png';
  static const String HOME_CONTACT_SELECTED = 'assets/images/home_contact_selected.png';
  static const String HOME_MINE = 'assets/images/home_mine.png';
  static const String HOME_MINE_SELECTED = 'assets/images/home_mine_selected.png';
  static const String HOME_NEWS = 'assets/images/home_news.png';
  static const String HOME_NEWS_SELECTED = 'assets/images/home_news_selected.png';
  static const String HOME_WORKFLOW = 'assets/images/home_workflow.png';
  static const String HOME_WORKFLOW_SELECTED = 'assets/images/home_workflow_selected.png';
  static const String IC_EMPTY_COMPANY = 'assets/images/ic_empty_company.png';
  static const String IC_EMPTY_FRIEND = 'assets/images/ic_empty_friend.png';
  static const String IC_EMPTY_MSG = 'assets/images/ic_empty_msg.png';
  static const String IC_FILE_FOLDER = 'assets/images/ic_file_folder.png';
  static const String IC_FILE_TYPE_AI = 'assets/images/ic_file_type_ai.png';
  static const String IC_FILE_TYPE_DOC = 'assets/images/ic_file_type_doc.png';
  static const String IC_FILE_TYPE_DWF = 'assets/images/ic_file_type_dwf.png';
  static const String IC_FILE_TYPE_DWG = 'assets/images/ic_file_type_dwg.png';
  static const String IC_FILE_TYPE_EXCEL = 'assets/images/ic_file_type_excel.png';
  static const String IC_FILE_TYPE_GIF = 'assets/images/ic_file_type_gif.png';
  static const String IC_FILE_TYPE_HTML = 'assets/images/ic_file_type_html.png';
  static const String IC_FILE_TYPE_JPG = 'assets/images/ic_file_type_jpg.png';
  static const String IC_FILE_TYPE_JS = 'assets/images/ic_file_type_js.png';
  static const String IC_FILE_TYPE_MORE = 'assets/images/ic_file_type_more.png';
  static const String IC_FILE_TYPE_MP3 = 'assets/images/ic_file_type_mp3.png';
  static const String IC_FILE_TYPE_MP4 = 'assets/images/ic_file_type_mp4.png';
  static const String IC_FILE_TYPE_PDF = 'assets/images/ic_file_type_pdf.png';
  static const String IC_FILE_TYPE_PPT = 'assets/images/ic_file_type_ppt.png';
  static const String IC_FILE_TYPE_PSD = 'assets/images/ic_file_type_psd.png';
  static const String IC_FILE_TYPE_RP = 'assets/images/ic_file_type_rp.png';
  static const String IC_FILE_TYPE_RVT = 'assets/images/ic_file_type_rvt.png';
  static const String IC_FILE_TYPE_SKP = 'assets/images/ic_file_type_skp.png';
  static const String IC_FILE_TYPE_STL = 'assets/images/ic_file_type_stl.png';
  static const String IC_FILE_TYPE_SWF = 'assets/images/ic_file_type_swf.png';
  static const String IC_FILE_TYPE_TXT = 'assets/images/ic_file_type_txt.png';
  static const String IC_FILE_TYPE_VSD = 'assets/images/ic_file_type_vsd.png';
  static const String IC_FILE_TYPE_WJJ = 'assets/images/ic_file_type_wjj.png';
  static const String IC_FILE_TYPE_WU = 'assets/images/ic_file_type_wu.png';
  static const String IC_FILE_TYPE_XMIND = 'assets/images/ic_file_type_xmind.png';
  static const String IC_FILE_TYPE_ZIP = 'assets/images/ic_file_type_zip.png';
  static const String IC_FUNC_LOCATION = 'assets/images/ic_func_location.png';
  static const String IC_FUNC_PIC = 'assets/images/ic_func_pic.png';
  static const String IC_FUNC_SHOT = 'assets/images/ic_func_shot.png';
  static const String IC_IDC = 'assets/images/ic_idc.png';
  static const String IC_IM_EMOJI = 'assets/images/ic_im_emoji.png';
  static const String IC_IM_MORE = 'assets/images/ic_im_more.png';
  static const String IC_IM_VOICE = 'assets/images/ic_im_voice.png';
  static const String IC_KINGDEE = 'assets/images/ic_kingdee.png';
  static const String IC_MANAGERMENT = 'assets/images/ic_managerment.png';
  static const String IC_MATTER = 'assets/images/ic_matter.png';
  static const String IC_MSG_ATTEND_BG = 'assets/images/ic_msg_attend_bg.png';
  static const String IC_MSG_ATTEND_FRONT = 'assets/images/ic_msg_attend_front.png';
  static const String IC_RANGE_PARK = 'assets/images/ic_range_park.jpg';
  static const String IC_TANHAO = 'assets/images/ic_tanhao.png';
  static const String IC_TICKET = 'assets/images/ic_ticket.png';
  static const String IC_TRAIN = 'assets/images/ic_train.png';
  static const String IC_VOICE_BLUE = 'assets/images/ic_voice_blue.png';
  static const String IC_VOICE_IMAGE = 'assets/images/ic_voice_image.png';
  static const String IC_VOICE_RECORD_CLOSE = 'assets/images/ic_voice_record_close.png';
  static const String ICON_APPROVAL_PERMISSION = 'assets/images/icon_approval_permission.png';
  static const String ICON_CLOUD_MANAGE = 'assets/images/icon_cloud_manage.png';
  static const String ICON_DOWN_ARROW = 'assets/images/icon_down_arrow.png';
  static const String ICON_GROUP_MEMBER_ADD = 'assets/images/icon_group_member_add.png';
  static const String ICON_GROUP_MEMBER_REMOVE = 'assets/images/icon_group_member_remove.png';
  static const String ICON_HEALTH_STATISTICS = 'assets/images/icon_health_statistics.png';
  static const String ICON_IM_VC_HINT = 'assets/images/icon_im_vc_hint.png';
  static const String ICON_IM_VIDEO_CHAT = 'assets/images/icon_im_video_chat.png';
  static const String ICON_IM_VOICE_CHAT = 'assets/images/icon_im_voice_chat.png';
  static const String ICON_LOCATION_CENTER = 'assets/images/icon_location_center.png';
  static const String ICON_MSG_COOPERATION_COMPANY = 'assets/images/icon_msg_cooperation_company.png';
  static const String ICON_NONSELECTED = 'assets/images/icon_nonSelected.png';
  static const String ICON_NOTICE_UNREMIND = 'assets/images/icon_notice_unremind.png';
  static const String ICON_NUMBER_REPORT_PERMISSION = 'assets/images/icon_number_report_permission.png';
  static const String ICON_REAL_NAME = 'assets/images/icon_real_name.png';
  static const String ICON_REALNAME_AUTH_FAIL = 'assets/images/icon_realname_auth_fail.png';
  static const String ICON_REALNAME_AUTH_SUCCESS = 'assets/images/icon_realname_auth_success.png';
  static const String ICON_REALNAME_AUTHING = 'assets/images/icon_realname_authing.png';
  static const String ICON_REALNAME_NON = 'assets/images/icon_realname_non.png';
  static const String ICON_REALNAME_VERIFY = 'assets/images/icon_realname_verify.png';
  static const String ICON_REFRESH_CIRCLE = 'assets/images/icon_refresh_circle.png';
  static const String ICON_REGIMES_PERMISSION = 'assets/images/icon_regimes_permission.png';
  static const String ICON_REPORT_PERMISSION = 'assets/images/icon_report_permission.png';
  static const String ICON_RETAKE = 'assets/images/icon_retake.png';
  static const String ICON_SAFE = 'assets/images/icon_safe.png';
  static const String ICON_SELECTED = 'assets/images/icon_selected.png';
  static const String ICON_SEND_GROUP_MSG = 'assets/images/icon_send_group_msg.png';
  static const String ICON_SET_BLUE = 'assets/images/icon_set_blue.png';
  static const String ICON_SUPER_PERMISSION_TAG = 'assets/images/icon_super_permission_tag.png';
  static const String ICON_TAKE_PHOTO = 'assets/images/icon_take_photo.png';
  static const String ICON_TASK_MANAGE_SMALL = 'assets/images/icon_task_manage_small.png';
  static const String ICON_TOTAL_APPROVAL = 'assets/images/icon_total_approval.png';
  static const String ICON_UP_ARROW = 'assets/images/icon_up_arrow.png';
  static const String ICON_VISITOR_PER = 'assets/images/icon_visitor_per.png';
  static const String IM_BYITEM_TRANSLATE = 'assets/images/im_byitem_translate.png';
  static const String IM_CHAT_MORE = 'assets/images/im_chat_more.png';
  static const String IM_HOME_NOSOUND = 'assets/images/im_home_nosound.png';
  static const String IM_IMAGE_ERROR = 'assets/images/im_image_error.png';
  static const String IM_MENU_COPY = 'assets/images/im_menu_copy.png';
  static const String IM_MENU_DELETE = 'assets/images/im_menu_delete.png';
  static const String IM_MENU_FORWARD = 'assets/images/im_menu_forward.png';
  static const String IM_MENU_MULTISELECT = 'assets/images/im_menu_multiselect.png';
  static const String IM_MENU_QUOTE = 'assets/images/im_menu_quote.png';
  static const String IM_MENU_RECALL = 'assets/images/im_menu_recall.png';
  static const String IM_MENU_SAVE = 'assets/images/im_menu_save.png';
  static const String IM_MERGE_TRANSLATE = 'assets/images/im_merge_translate.png';
  static const String IM_NEWSHOME_SEARCH = 'assets/images/im_newsHome_search.png';
  static const String IM_NOTICE_SCREEN = 'assets/images/im_notice_screen.png';
  static const String IM_NOTICE_SYSTEM = 'assets/images/im_notice_system.png';
  static const String IM_NOTICE_UNREMIND = 'assets/images/im_notice_unremind.png';
  static const String IM_SESSION_TOP_EXPAND = 'assets/images/im_session_top_expand.png';
  static const String IM_TO_UNREAD_MSG = 'assets/images/im_to_unread_msg.png';
  static const String IM_TRANS_DEL = 'assets/images/im_trans_del.png';
  static const String IM_VIDEO_PLAY = 'assets/images/im_video_play.png';
  static const String IM_WARNING_ICON = 'assets/images/im_warning_icon.png';
  static const String INDEX_LETTER_BUBLE = 'assets/images/index_letter_buble.png';
  static const String LOADING_VIEW = 'assets/images/loading_view.gif';
  static const String LOGIN_BACKGROUND = 'assets/images/login_backGround.png';
  static const String LOGIN_CODE_REFRESH = 'assets/images/login_code_refresh.png';
  static const String LOGIN_PWD_CLOSE = 'assets/images/login_pwd_close.png';
  static const String LOGIN_PWD_OPEN = 'assets/images/login_pwd_open.png';
  static const String LOGIN_SELECTED = 'assets/images/login_selected.png';
  static const String LOGIN_SLIDERBTN = 'assets/images/login_sliderBtn.png';
  static const String LOGIN_SUPPLEMENTARY_MSG = 'assets/images/login_supplementary_msg.png';
  static const String LOGIN_UNSELECT = 'assets/images/login_unselect.png';
  static const String LOGIN_WECHAT = 'assets/images/login_weChat.png';
  static const String MEETING_COPYLINK24 = 'assets/images/meeting_copyLink24.png';
  static const String MEETING_DETAIL_COPY = 'assets/images/meeting_detail_copy.png';
  static const String MEETING_DETAIL_TOP = 'assets/images/meeting_detail_top.png';
  static const String MEETING_JOIN_NOSPEAKER = 'assets/images/meeting_join_nospeaker.png';
  static const String MEETING_JOIN_NOVIDEO = 'assets/images/meeting_join_novideo.png';
  static const String MEETING_JOIN_NOVOICE = 'assets/images/meeting_join_novoice.png';
  static const String MEETING_JOIN_SPEAKER = 'assets/images/meeting_join_speaker.png';
  static const String MEETING_JOIN_VIDEO = 'assets/images/meeting_join_video.png';
  static const String MEETING_JOIN_VOICE = 'assets/images/meeting_join_voice.png';
  static const String MEETING_LIST_JOIN = 'assets/images/meeting_list_join.png';
  static const String MEETING_LIST_QUICK = 'assets/images/meeting_list_quick.png';
  static const String MEETING_LIST_RESERVE = 'assets/images/meeting_list_reserve.png';
  static const String MEETING_MORE = 'assets/images/meeting_more.png';
  static const String MEETING_NORECORD = 'assets/images/meeting_noRecord.png';
  static const String MEETING_REVERVE_TOPIC = 'assets/images/meeting_reverve_topic.png';
  static const String MEETING_SHARE = 'assets/images/meeting_share.png';
  static const String MEETING_SHARE_COPYLINK = 'assets/images/meeting_share_copyLink.png';
  static const String MEETING_SHARE_QRCODE = 'assets/images/meeting_share_qrCode.png';
  static const String MEETING_SHARE_WECHAT = 'assets/images/meeting_share_wechat.png';
  static const String MICRO_CLOSE = 'assets/images/micro_close.png';
  static const String MINEHOME_ABOUT = 'assets/images/mineHome_about.png';
  static const String MINEHOME_QRCODE = 'assets/images/mineHome_qrCode.png';
  static const String MINEHOME_SETTING = 'assets/images/mineHome_setting.png';
  static const String MINEHOME_USERINFO = 'assets/images/mineHome_userInfo.png';
  static const String MINE_BENEFIT = 'assets/images/mine_benefit.png';
  static const String MINE_HOME_FEED_BACK = 'assets/images/mine_home_feed_back.png';
  static const String MINE_RIGHT = 'assets/images/mine_right.png';
  static const String MINE_SEX_M = 'assets/images/mine_sex_m.png';
  static const String MINE_SEX_W = 'assets/images/mine_sex_w.png';
  static const String NET_DISCONNECT = 'assets/images/net_disconnect.png';
  static const String NEWS_ADD_FRIEND = 'assets/images/news_add_friend.png';
  static const String NEWS_AI = 'assets/images/news_ai.png';
  static const String NEWS_CREATE_GROUP = 'assets/images/news_create_group.png';
  static const String NEWS_QR_CODE = 'assets/images/news_qr_code.png';
  static const String ORG_DETAIL_BACK = 'assets/images/org_detail_back.png';
  static const String ORG_DETAIL_INDUSTRY = 'assets/images/org_detail_industry.png';
  static const String ORG_DETAIL_SCALE = 'assets/images/org_detail_scale.png';
  static const String ORG_NO_PERMISSIONS = 'assets/images/org_no_permissions.png';
  static const String ORG_SEARCH = 'assets/images/org_search.png';
  static const String ORG_WHITE_BACK = 'assets/images/org_white_back.png';
  static const String PAN_FILETYPEAI = 'assets/images/pan_fileTypeai.png';
  static const String PAN_FILETYPEDOC = 'assets/images/pan_fileTypedoc.png';
  static const String PAN_FILETYPEDWF = 'assets/images/pan_fileTypedwf.png';
  static const String PAN_FILETYPEDWG = 'assets/images/pan_fileTypedwg.png';
  static const String PAN_FILETYPEEXCEL = 'assets/images/pan_fileTypeexcel.png';
  static const String PAN_FILETYPEGIF = 'assets/images/pan_fileTypegif.png';
  static const String PAN_FILETYPEHTML = 'assets/images/pan_fileTypehtml.png';
  static const String PAN_FILETYPEJPG = 'assets/images/pan_fileTypejpg.png';
  static const String PAN_FILETYPEJS = 'assets/images/pan_fileTypejs.png';
  static const String PAN_FILETYPEMORE = 'assets/images/pan_fileTypemore.png';
  static const String PAN_FILETYPEMP3 = 'assets/images/pan_fileTypemp3.png';
  static const String PAN_FILETYPEMP4 = 'assets/images/pan_fileTypemp4.png';
  static const String PAN_FILETYPEPDF = 'assets/images/pan_fileTypepdf.png';
  static const String PAN_FILETYPEPPT = 'assets/images/pan_fileTypeppt.png';
  static const String PAN_FILETYPEPSD = 'assets/images/pan_fileTypepsd.png';
  static const String PAN_FILETYPERP = 'assets/images/pan_fileTyperp.png';
  static const String PAN_FILETYPERVT = 'assets/images/pan_fileTypervt.png';
  static const String PAN_FILETYPESKP = 'assets/images/pan_fileTypeskp.png';
  static const String PAN_FILETYPESTL = 'assets/images/pan_fileTypestl.png';
  static const String PAN_FILETYPESWF = 'assets/images/pan_fileTypeswf.png';
  static const String PAN_FILETYPETXT = 'assets/images/pan_fileTypetxt.png';
  static const String PAN_FILETYPEVSD = 'assets/images/pan_fileTypevsd.png';
  static const String PAN_FILETYPEWJJ = 'assets/images/pan_fileTypewjj.png';
  static const String PAN_FILETYPEWU = 'assets/images/pan_fileTypewu.png';
  static const String PAN_FILETYPEXMIND = 'assets/images/pan_fileTypexmind.png';
  static const String PAN_FILETYPEZIP = 'assets/images/pan_fileTypezip.png';
  static const String PENDING_EXTERNAL_INVITE = 'assets/images/pending_external_invite.png';
  static const String PENDING_FRIEND_APPLY = 'assets/images/pending_friend_apply.png';
  static const String PER_ATTENDACE = 'assets/images/per_attendace.png';
  static const String PER_ORG = 'assets/images/per_org.png';
  static const String PIC_RETURN = 'assets/images/pic_return.png';
  static const String QRCODE_LOGIN = 'assets/images/qrCode_login.png';
  static const String REGIMES_BREAD_RIGHT = 'assets/images/regimes_bread_right.png';
  static const String REGIMES_CLOSE = 'assets/images/regimes_close.png';
  static const String REGIMES_ICON_CATEGORY = 'assets/images/regimes_icon_category.png';
  static const String REGIMES_ICON_FILE = 'assets/images/regimes_icon_file.png';
  static const String REGIMES_ORG_CHECK = 'assets/images/regimes_org_check.png';
  static const String REGIMES_ORG_MENU = 'assets/images/regimes_org_menu.png';
  static const String REGIMES_RIGHT_ARROW = 'assets/images/regimes_right_arrow.png';
  static const String REGIMES_SEARCH_CLOSE = 'assets/images/regimes_search_close.png';
  static const String REGIMES_TITLE_BAR = 'assets/images/regimes_title_bar.png';
  static const String RETRY_CONNECT = 'assets/images/retry_connect.png';
  static const String SESSION_TOP_TARGET = 'assets/images/session_top_target.png';
  static const String SYSTEM_NOTIFICATION_ICON = 'assets/images/system_notification_icon.png';
  static const String THREEPOINT = 'assets/images/threePoint.png';
  static const String USERLOGO = 'assets/images/userLogo.png';
  static const String USERLOGOB = 'assets/images/userLogoB.png';
  static const String USER_INFO_ADD_BLUE = 'assets/images/user_info_add_blue.png';
  static const String USER_INFO_ADD_WHITE = 'assets/images/user_info_add_white.png';
  static const String USER_INFO_AUDIO_CALL = 'assets/images/user_info_audio_call.png';
  static const String USER_INFO_BACK = 'assets/images/user_info_back.png';
  static const String USER_INFO_CALL = 'assets/images/user_info_call.png';
  static const String USER_INFO_MSG = 'assets/images/user_info_msg.png';
  static const String USER_INFO_QR = 'assets/images/user_info_qr.png';
  static const String USER_INFO_VIDEO_CALL = 'assets/images/user_info_video_call.png';
  static const String WORKFLOW_ADD_BLUE = 'assets/images/workflow_add_blue.png';
  static const String WORKFLOW_ADD_FUNCTION = 'assets/images/workflow_add_function.png';
  static const String WORKFLOW_CELL_DELETE = 'assets/images/workflow_cell_delete.png';
  static const String WORKFLOW_CELL_EDIT = 'assets/images/workflow_cell_edit.png';
  static const String WORKFLOW_FUNCTION_0 = 'assets/images/workflow_function_0.png';
  static const String WORKFLOW_FUNCTION_1 = 'assets/images/workflow_function_1.png';
  static const String WORKFLOW_FUNCTION_2 = 'assets/images/workflow_function_2.png';
  static const String WORKFLOW_FUNCTION_3 = 'assets/images/workflow_function_3.png';
  static const String WORKFLOW_FUNCTION_4 = 'assets/images/workflow_function_4.png';
  static const String WORKFLOW_FUNCTION_ADD = 'assets/images/workflow_function_add.png';
  static const String WORKFLOW_FUNCTION_ADD0 = 'assets/images/workflow_function_add0.png';
  static const String WORKFLOW_FUNCTION_LIST = 'assets/images/workflow_function_list.png';
  static const String WORKFLOW_FUNCTION_LIST0 = 'assets/images/workflow_function_list0.png';
  static const String WORKFLOW_HOME_ANNOUNCE = 'assets/images/workflow_home_announce.png';
  static const String WORKFLOW_HOME_APPROVE = 'assets/images/workflow_home_approve.png';
  static const String WORKFLOW_HOME_ATTENDANCE = 'assets/images/workflow_home_attendance.png';
  static const String WORKFLOW_HOME_DOWN = 'assets/images/workflow_home_down.png';
  static const String WORKFLOW_HOME_MEETING = 'assets/images/workflow_home_meeting.png';
  static const String WORKFLOW_HOME_ORGMANAGER = 'assets/images/workflow_home_orgManager.png';
  static const String WORKFLOW_HOME_SETTINGAPPROVE = 'assets/images/workflow_home_settingApprove.png';
  static const String WORKFLOW_HOME_SETTINGATTENDANCE = 'assets/images/workflow_home_settingAttendance.png';
  static const String WORKFLOW_HOME_UP = 'assets/images/workflow_home_up.png';
}
