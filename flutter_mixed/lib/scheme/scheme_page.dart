

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/scheme/scheme_controller.dart';
import 'package:get/get.dart';

class SchemePage extends StatelessWidget {

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SchemeController>(builder: (_){
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    });
  }
}