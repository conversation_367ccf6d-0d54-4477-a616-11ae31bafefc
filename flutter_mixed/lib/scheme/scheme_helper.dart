

import 'dart:convert';

import 'package:get/get.dart';

import '../app/im/route/route_helper.dart';
import '../app/utils/string-util.dart';

class SchemeHelper {

  static schemeNavigate(dynamic arguments) {
    try {
      if(arguments is String){
        var jsonString = arguments as String;
        var routeMapParam = json.decode(jsonString);
        final String route = routeMapParam['route'];
        var argument = routeMapParam['arguments'];
        RouteHelper.routePath(route , arguments: argument ,preventDuplicates: false);
      }else {
        if(StringUtil.isEmpty(arguments['route'])) return;
        Map<String, dynamic>? argument;
        if(arguments['arguments'] != null){
          argument = arguments['arguments'].cast<String, dynamic>();
        }
        RouteHelper.routePath(arguments['route'],arguments: argument,preventDuplicates: false);
      }
    } catch (e) {
      print(e);
      Get.snackbar('跳转失败', e.toString());
    }
  }

}