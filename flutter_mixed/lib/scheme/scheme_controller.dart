

import 'dart:convert';

import 'package:get/get.dart';

import '../app/im/route/route_helper.dart';
import '../app/utils/string-util.dart';

class SchemeController extends GetxController {

  @override
  void onInit() {
    super.onInit();
    _handleRoute();
  }

  void _handleRoute() {
    try {
      if(Get.arguments is String){
        var jsonString = Get.arguments as String;
        var routeMapParam = json.decode(jsonString);
        final String route = routeMapParam['route'];
        var argument = routeMapParam['arguments'];
        RouteHelper.offRoutePath(route , arguments: argument ,preventDuplicates: false);
      }else {
        if(StringUtil.isEmpty(Get.arguments['route'])) return;
        Map<String, dynamic>? argument;
        if(Get.arguments['arguments'] != null){
          argument = Get.arguments['arguments'].cast<String, dynamic>();
        }
        RouteHelper.offRoutePath(Get.arguments['route'],arguments: argument,preventDuplicates: false);
      }
    } catch (e) {
      print(e);
      Get.snackbar('跳转失败', e.toString());
      Get.back();
    }
  }


}