import 'dart:async';
import 'dart:io';

import 'package:event_bus/event_bus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/config/app_config.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/statis/apm_manager.dart';
import 'package:flutter_mixed/app/utils/http.dart';
import 'package:flutter_mixed/logger/logger_helper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:umeng_apm_sdk/umeng_apm_sdk.dart';

import 'app/common/config/string_const.dart';
import 'app/modules/privacy_policy/privacy_policy/logic.dart';
import 'app/retrofit/app_dio.dart';
import 'app/routes/app_pages.dart';
import 'app/statis/fps_monitor.dart';
import 'app/statis/statics_log.dart';
import 'app/utils/dialog_hud.dart';
import 'app/utils/storage.dart';
import 'error/dd_error_handler.dart';


@pragma('vm:entry-point')
void notificationTapBackground(NotificationResponse notificationResponse) {
  // ignore: avoid_print
  print('notification(${notificationResponse.id}) action tapped: '
      '${notificationResponse.actionId} with'
      ' payload: ${notificationResponse.payload}');
  if (notificationResponse.input?.isNotEmpty ?? false) {
    // ignore: avoid_print
    print(
        'notification action tapped with input: ${notificationResponse.input}');
  }
}
void main() async {
  var isDebug = kDebugMode;

  runZonedGuarded((){
    ApmManager.initApm((observer) async {
      if(isDebug){
        WidgetsFlutterBinding.ensureInitialized();
      }
      Channel().listener();
      await EnvConfig.switchEnv(Env.Dev);

      await checkPrivacyInitConfig();

      FpsMonitor().init(kDebugMode);

      Logger().init();

      catchErrorPage();

      runApp(ScreenUtilInit(
          designSize: const Size(375, 812),
          builder: () => GetMaterialApp(
              theme: ThemeData(
                appBarTheme:
                const AppBarTheme(surfaceTintColor: Colors.transparent),
                scaffoldBackgroundColor: ColorConfig.backgroundColor,
              ),
              localizationsDelegates: const [
                GlobalCupertinoLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate
              ],
              navigatorObservers: [
                FlutterSmartDialog.observer,
                observer ?? ApmNavigatorObserver.singleInstance
              ],
              supportedLocales: const [
                Locale('zh', 'CH'),
                Locale('en', 'US')
              ],
              routingCallback: (routing){
                PageTimeTracker.trackAllPages(routing);
              },
              home: Logger().homeInit(),
              builder: DialogHud.init(),
              defaultTransition: Transition.rightToLeft,
              title: appName,
              initialRoute: AppPages.SPLASH,
              getPages: AppPages.routes,
              transitionDuration: Duration(milliseconds: 220)
          )));

    } , debug: isDebug);
  }, (error , stack){

  });


}

checkPrivacyInitConfig() async {
  var showPrivacy = await isToShowPrivacyPage();
  if(!showPrivacy){
    appInit();
  }
}


final retrofitDio = AppDio.getInstance();
EventBus eventBus = EventBus();
