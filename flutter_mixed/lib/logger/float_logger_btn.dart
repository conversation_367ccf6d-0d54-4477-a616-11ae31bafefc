

import 'package:flutter/material.dart';

import 'logger_helper.dart';

class GlobalDraggableButton {
  OverlayEntry? _overlayEntry;
  late Offset _position;
  final double _buttonSize = 40;

  void init(BuildContext context) {
    _position = Offset(
      MediaQuery.of(context).size.width - _buttonSize - 20,
      MediaQuery.of(context).size.height / 2,
    );

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: _position.dx,
        top: _position.dy,
        child: _buildDraggableButton(context),
      ),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  Widget _buildDraggableButton(BuildContext context) {
    bool _isDragging = false;

    return GestureDetector(
      onPanStart: (details) => _isDragging = true,
      onPanUpdate: (details) {
        final screenSize = MediaQuery.of(context).size;
        final newDx = (_position.dx + details.delta.dx)
            .clamp(0.0, screenSize.width - _buttonSize);
        final newDy = (_position.dy + details.delta.dy)
            .clamp(0.0, screenSize.height - _buttonSize);

        _position = Offset(newDx, newDy);
        _overlayEntry?.markNeedsBuild();
      },
      // 在 _buildDraggableButton 中添加：
      onPanEnd: (details) {
        if (_position.dx > MediaQuery.of(context).size.width / 2) {
          // 自动吸附到右侧
          _position = Offset(
              MediaQuery.of(context).size.width - _buttonSize - 20,
              _position.dy
          );
        } else {
          // 自动吸附到左侧
          _position = Offset(20, _position.dy);
        }
        _overlayEntry?.markNeedsBuild();
      },
      onTap: () => openConsole(context),
      child: Container(
        width: _buttonSize,
        height: _buttonSize,
        decoration: const BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              spreadRadius: 2,
            )
          ],
        ),
        child: Icon(Icons.ac_unit_sharp, color: Colors.white, size: 25),
      ),
    );
  }
}