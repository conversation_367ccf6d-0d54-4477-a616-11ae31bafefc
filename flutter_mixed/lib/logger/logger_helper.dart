import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:logging_flutter/logging_flutter.dart';

import 'float_logger_btn.dart';


var enableLog = kDebugMode || EnvConfig.mEnv != Env.Product;
var enableLogView = false;
bool hasCreateLogView = false;

class Logger {

  Widget? homeInit() {
    if(enableLogView) {
      return Builder(builder: (ctx){
        Logger().createLogFloatView(ctx);
        return Container();
      });
    };
    return null;
  }

  createLogFloatView(BuildContext? context) {
    if(context == null) return;
    if(enableLog){
      WidgetsBinding.instance
          .addPostFrameCallback((_) {
        if(!hasCreateLogView){
          GlobalDraggableButton().init(context);
          hasCreateLogView = true;
        }
      });
    }
  }

  init() {
     if(enableLogView){
       Flogger.init(
         config: const FloggerConfig(
           printClassName: true,
           printMethodName: true,
           showDateTime: true,
           showDebugLogs: true,
         ),
       );

       Flogger.registerListener(
             (record) => log(record.printable(), stackTrace: record.stackTrace),
       );

       Flogger.registerListener(
             (record) => LogConsole.add(
           OutputEvent(record.level, [record.printable()]),
           bufferSize: 1000, // Remember the last X logs
         ),
       );
     }
  }
}


openConsole(BuildContext context) {
  LogConsole.open(context);
}