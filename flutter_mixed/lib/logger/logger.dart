

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_mixed/logger/logger_helper.dart';
import 'package:logging_flutter/logging_flutter.dart';

import '../app/common/api/LoginApi.dart';


logger(Object? msg) {
  if(msg == null) return;
  if(Platform.isIOS){
    _print(msg);
    return;
  }
  if(!enableLog) return;
  _print(msg);
}

_print(Object? obj) {
  print(obj);
  // Flogger.d("${obj}");
}
