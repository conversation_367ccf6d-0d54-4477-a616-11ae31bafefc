
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 页面通用导航返回键
const navigationBackIcon = Icon(Icons.arrow_back_ios , size: 19, color: Color(0xff565D66));

/// 通用 item  右方向按钮
const forwardIcon = Icon(Icons.arrow_forward_ios_outlined , color: Color(0xffd9d9d9), size: 16,);

/// dialog 中间隔线
var divider = Container(height: 0.5, color: Color(0xffE4E4E4));

dividerLine({double? padding}) => Container(height: 0.5, color: Color(0xffE4E4E4), margin: padding == null ? null :  EdgeInsets.only(left: padding, right: padding),);

/// 主要颜色
const majorTextColor = Colors.black;
/// 次要颜色
const minorTextColor = Color(0xFF666666);
/// 淡色 弱提示文案
const hintTextColor = Color(0xff999999);
/// 不可用
const disableTextColor = Color(0xFFCCCCCC);

// 按钮不可用
const disableButtonColor = Color(0xffa9d9fa);
/// 按钮可用
const enableButtonColor = majorBlue;

const majorBlue = Color(0xFF29A0F2);

// 透明沉浸
const translateOverlayStyle = SystemUiOverlayStyle(
  statusBarColor: Colors.transparent,
  statusBarIconBrightness: Brightness.dark,
  // Only honored in iOS.
  statusBarBrightness: Brightness.light,
);