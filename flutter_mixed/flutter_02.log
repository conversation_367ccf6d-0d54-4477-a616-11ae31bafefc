Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter --no-color pub get

## exception

FileSystemException: FileSystemException: Deletion failed, path = '/Users/<USER>/Documents/joinu/bak/flutter_mixed/flutter_mixed/.android' (OS Error: Directory not empty, errno = 66)

```
#0      _Directory._deleteSync (dart:io/directory_impl.dart:188:7)
#1      FileSystemEntity.deleteSync (dart:io/file_system_entity.dart:407:7)
#2      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#3      ErrorHandlingDirectory.deleteSync.<anonymous closure> (package:flutter_tools/src/base/error_handling_io.dart:492:22)
#4      _runSync (package:flutter_tools/src/base/error_handling_io.dart:600:14)
#5      ErrorHandlingDirectory.deleteSync (package:flutter_tools/src/base/error_handling_io.dart:491:12)
#6      ErrorHandlingFileSystem.deleteIfExists (package:flutter_tools/src/base/error_handling_io.dart:84:14)
#7      AndroidProject._regenerateLibrary (package:flutter_tools/src/project.dart:764:29)
#8      AndroidProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:739:13)
#9      FlutterProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:375:21)
<asynchronous suspension>
#10     PackagesGetCommand.runCommand (package:flutter_tools/src/commands/packages.dart:386:7)
<asynchronous suspension>
#11     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1450:27)
<asynchronous suspension>
#12     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#13     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#14     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:421:9)
<asynchronous suspension>
#15     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#16     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:364:5)
<asynchronous suspension>
#17     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:131:9)
<asynchronous suspension>
#18     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#19     main (package:flutter_tools/executable.dart:94:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.27.0, on macOS 14.4.1 23E224 darwin-arm64, locale zh-Hans-US)
    • Flutter version 3.27.0 on channel stable at /Users/<USER>/Documents/sdk/flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision 8495dee1fd (3 months ago), 2024-12-10 14:23:39 -0800
    • Engine revision 83bacfc525
    • Dart version 3.6.0
    • DevTools version 2.40.2
    • Pub download mirror https://pub.flutter-io.cn
    • Flutter download mirror https://storage.flutter-io.cn

[✓] Android toolchain - develop for Android devices (Android SDK version 34.0.0)
    • Android SDK at /Users/<USER>/Library/Android/sdk
    • Platform android-35, build-tools 34.0.0
    • ANDROID_HOME = /Users/<USER>/Library/Android/sdk
    • Java binary at: /Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java
    • Java version OpenJDK Runtime Environment (build 17.0.10+0-17.0.10b1087.21-11609105)
    • All Android licenses accepted.

[!] Xcode - develop for iOS and macOS (Xcode 15.3)
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Build 15E204a
    ! CocoaPods 1.11.3 out of date (1.13.0 is recommended).
        CocoaPods is a package manager for iOS or macOS platform code.
        Without CocoaPods, plugins will not work on iOS or macOS.
        For more info, see https://flutter.dev/to/platform-plugins
      To update CocoaPods, see https://guides.cocoapods.org/using/getting-started.html#updating-cocoapods

[✓] Chrome - develop for the web
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[✓] Android Studio (version 2024.1)
    • Android Studio at /Applications/Android Studio.app/Contents
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 17.0.10+0-17.0.10b1087.21-11609105)

[✓] VS Code (version 1.95.3)
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.100.0

[✓] Connected device (4 available)
    • 23113RKC6C (mobile)             • 2b28a68e              • android-arm64  • Android 15 (API 35)
    • macOS (desktop)                 • macos                 • darwin-arm64   • macOS 14.4.1 23E224 darwin-arm64
    • Mac Designed for iPad (desktop) • mac-designed-for-ipad • darwin         • macOS 14.4.1 23E224 darwin-arm64
    • Chrome (web)                    • chrome                • web-javascript • Google Chrome 134.0.6998.89

[✓] Network resources
    • All expected network resources are available.

! Doctor found issues in 1 category.
```
