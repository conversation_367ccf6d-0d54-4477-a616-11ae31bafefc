// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/im/ext/im_time_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_binding.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_page.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_controller.dart';
import 'package:flutter_mixed/app/im/ui/session_list/session_list_controller.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(GetMaterialApp(
        theme: ThemeData(
          appBarTheme:
          const AppBarTheme(surfaceTintColor: Colors.transparent),
        ),

        supportedLocales: const [
          Locale('zh', 'CH'),
          Locale('en', 'US')
        ],
        defaultTransition: Transition.rightToLeft,
        initialRoute: AppPages.SPLASH,
        getPages: AppPages.routes,
        transitionDuration: Duration(milliseconds: 220)
    ));

    await Future.delayed(Duration(seconds: 2));

    testContinuedSenMsg();


  });

  // _testContinuedSenMsg();
}

testContinuedSenMsg() async {
    // 查询会话，取第一个
   try {
      SessionListController sessionListController = Get.find<SessionListController>();
      if(sessionListController.list.isEmpty) return;
      var chatSessions = sessionListController.list.where((s) => s.isGroupChat() || s.isSingleChat()).toList();
      if(chatSessions.isEmpty) return;
      var session = chatSessions.first;
      RouteHelper.routeTotag(ChatPage(tag: session.sessionId),
          Routes.IM_CHAGE_PAGE,arguments: session,binding: ChatBinding(tag: session.sessionId));

      await Future.delayed(Duration(seconds: 2));
      ChatController chatController = Get.find<ChatController>(tag: session.sessionId);

      var list = [];
      for(int i =0 ;i< 3000 ;i++){
         list.add(i);
      }
      Future.forEach(list, (e) async {
         var time = DateTime.now().millisecondsSinceEpoch.convertDate();
         chatController.sendText('text : ${time}');
         await Future.delayed(Duration(seconds: 1));
      });
   }catch(e){
      print(e);
   }



}

