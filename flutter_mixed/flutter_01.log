Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter pub get --no-example

## exception

PathNotFoundException: PathNotFoundException: Cannot open file, path = '/Users/<USER>/Desktop/担当办公4.0/flutter_mixed/flutter_mixed/.android/local.properties' (OS Error: No such file or directory, errno = 2)

```
#0      _File.throwIfError (dart:io/file_impl.dart:675:7)
#1      _File.openSync (dart:io/file_impl.dart:490:5)
#2      _File.writeAsBytesSync (dart:io/file_impl.dart:644:31)
#3      _File.writeAsStringSync (dart:io/file_impl.dart:668:5)
#4      ForwardingFile.writeAsStringSync (package:file/src/forwarding/forwarding_file.dart:150:16)
#5      ErrorHandlingFile.writeAsStringSync.<anonymous closure> (package:flutter_tools/src/base/error_handling_io.dart:267:22)
#6      _runSync (package:flutter_tools/src/base/error_handling_io.dart:590:14)
#7      ErrorHandlingFile.writeAsStringSync (package:flutter_tools/src/base/error_handling_io.dart:266:5)
#8      SettingsFile.writeContents (package:flutter_tools/src/base/utils.dart:163:10)
#9      updateLocalProperties (package:flutter_tools/src/android/gradle_utils.dart:679:14)
#10     AndroidProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:669:12)
#11     FlutterProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:359:21)
<asynchronous suspension>
#12     PackagesGetCommand.runCommand (package:flutter_tools/src/commands/packages.dart:337:7)
<asynchronous suspension>
#13     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1297:27)
<asynchronous suspension>
#14     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
<asynchronous suspension>
#15     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#16     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:339:9)
<asynchronous suspension>
#17     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
<asynchronous suspension>
#18     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:285:5)
<asynchronous suspension>
#19     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:115:9)
<asynchronous suspension>
#20     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
<asynchronous suspension>
#21     main (package:flutter_tools/executable.dart:90:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.13.9, on macOS 13.6.4 22G513 darwin-x64, locale zh-Hans-CN)
    • Flutter version 3.13.9 on channel stable at /Users/<USER>/Desktop/flutter/flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision d211f42860 (1 year, 2 months ago), 2023-10-25 13:42:25 -0700
    • Engine revision 0545f8705d
    • Dart version 3.1.5
    • DevTools version 2.25.0
    • Pub download mirror https://pub.flutter-io.cn
    • Flutter download mirror https://storage.flutter-io.cn

[✓] Android toolchain - develop for Android devices (Android SDK version 30.0.3)
    • Android SDK at /usr/local/share/android-sdk
    • Platform android-33, build-tools 30.0.3
    • ANDROID_SDK_ROOT = /usr/local/share/android-sdk
    • Java binary at: /usr/bin/java
    • Java version Java(TM) SE Runtime Environment 18.9 (build 11.0.2+9-LTS)
    • All Android licenses accepted.

[✓] Xcode - develop for iOS and macOS (Xcode 15.2)
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Build 15C500b
    • CocoaPods version 1.11.3

[✓] Chrome - develop for the web
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[!] Android Studio (not installed)
    • Android Studio not found; download from https://developer.android.com/studio/index.html
      (or visit https://flutter.dev/docs/get-started/install/macos#android-setup for detailed instructions).

[✓] VS Code (version 1.91.0)
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.102.0

[✓] Connected device (5 available)
    • Us (mobile)         • 00008130-001C5C90016A001C • ios            • iOS 18.0.1 22A3370
    • 林艳飞的iPhone (mobile) • 00008101-001231AC2161001E • ios            • iOS 17.2.1 21C66
    • iPhone11 (mobile)   • 00008030-000244843684802E • ios            • iOS 17.6.1 21G101
    • macOS (desktop)     • macos                     • darwin-x64     • macOS 13.6.4 22G513 darwin-x64
    • Chrome (web)        • chrome                    • web-javascript • Google Chrome 131.0.6778.205
    ! Error: Browsing on the local area network for 薛英荟的iPhone. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for zhenglee 的15 pro max. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for Kylie的iPhone. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for 半岛铁何. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for 明成祖御用白釉镀银彩光板砖. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for keiPhone12. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for 瑾凉 jl. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for 王頔的iPhone📱. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)

[!] Network resources
    ✗ An HTTP error occurred while checking "https://github.com/": Connection closed before full header was received

! Doctor found issues in 2 categories.
```
