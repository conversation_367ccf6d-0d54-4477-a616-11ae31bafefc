time:2025-05-20
cmd:flutter pub deps
input:
Dart SDK 3.6.0
Flutter SDK 3.27.0
flutter_mixed 1.0.0+1
├── async 2.11.0
│   ├── collection...
│   └── meta...
├── audio_waveforms 1.3.0
│   └── flutter...
├── badges 3.1.2
│   └── flutter...
├── build_runner 2.4.15
│   ├── analyzer 6.11.0
│   │   ├── _fe_analyzer_shared 76.0.0
│   │   │   └── meta...
│   │   ├── macros 0.1.3-main.0
│   │   │   └── _macros 0.3.3
│   │   ├── collection...
│   │   ├── convert...
│   │   ├── crypto...
│   │   ├── glob...
│   │   ├── meta...
│   │   ├── package_config...
│   │   ├── path...
│   │   ├── pub_semver...
│   │   ├── source_span...
│   │   ├── watcher...
│   │   └── yaml...
│   ├── build 2.4.2
│   │   ├── analyzer...
│   │   ├── async...
│   │   ├── convert...
│   │   ├── crypto...
│   │   ├── glob...
│   │   ├── logging...
│   │   ├── meta...
│   │   ├── package_config...
│   │   └── path...
│   ├── build_config 1.1.2
│   │   ├── checked_yaml 2.0.3
│   │   │   ├── json_annotation...
│   │   │   ├── source_span...
│   │   │   └── yaml...
│   │   ├── json_annotation...
│   │   ├── path...
│   │   ├── pubspec_parse...
│   │   └── yaml...
│   ├── build_daemon 4.0.4
│   │   ├── built_value 8.9.5
│   │   │   ├── built_collection...
│   │   │   ├── collection...
│   │   │   ├── fixnum...
│   │   │   └── meta...
│   │   ├── built_collection...
│   │   ├── crypto...
│   │   ├── http_multi_server...
│   │   ├── logging...
│   │   ├── path...
│   │   ├── pool...
│   │   ├── shelf...
│   │   ├── shelf_web_socket...
│   │   ├── stream_transform...
│   │   ├── watcher...
│   │   └── web_socket_channel...
│   ├── build_resolvers 2.4.4
│   │   ├── analyzer...
│   │   ├── async...
│   │   ├── build...
│   │   ├── collection...
│   │   ├── convert...
│   │   ├── crypto...
│   │   ├── graphs...
│   │   ├── logging...
│   │   ├── package_config...
│   │   ├── path...
│   │   ├── pool...
│   │   ├── pub_semver...
│   │   ├── stream_transform...
│   │   └── yaml...
│   ├── build_runner_core 8.0.0
│   │   ├── async...
│   │   ├── build...
│   │   ├── build_config...
│   │   ├── build_resolvers...
│   │   ├── collection...
│   │   ├── convert...
│   │   ├── crypto...
│   │   ├── glob...
│   │   ├── graphs...
│   │   ├── json_annotation...
│   │   ├── logging...
│   │   ├── meta...
│   │   ├── package_config...
│   │   ├── path...
│   │   ├── pool...
│   │   ├── timing...
│   │   ├── watcher...
│   │   └── yaml...
│   ├── code_builder 4.10.1
│   │   ├── built_collection...
│   │   ├── built_value...
│   │   ├── collection...
│   │   ├── matcher...
│   │   └── meta...
│   ├── dart_style 2.3.8
│   │   ├── analyzer...
│   │   ├── args...
│   │   ├── collection...
│   │   ├── package_config...
│   │   ├── path...
│   │   ├── pub_semver...
│   │   └── source_span...
│   ├── frontend_server_client 4.0.0
│   │   ├── async...
│   │   └── path...
│   ├── glob 2.1.3
│   │   ├── async...
│   │   ├── collection...
│   │   ├── file...
│   │   ├── path...
│   │   └── string_scanner...
│   ├── graphs 2.3.2
│   │   └── collection...
│   ├── http_multi_server 3.2.2
│   │   └── async...
│   ├── io 1.0.5
│   │   ├── meta...
│   │   ├── path...
│   │   └── string_scanner...
│   ├── js 0.6.7
│   │   └── meta...
│   ├── package_config 2.2.0
│   │   └── path...
│   ├── pool 1.5.1
│   │   ├── async...
│   │   └── stack_trace...
│   ├── pub_semver 2.2.0
│   │   └── collection...
│   ├── pubspec_parse 1.5.0
│   │   ├── checked_yaml...
│   │   ├── collection...
│   │   ├── json_annotation...
│   │   ├── pub_semver...
│   │   └── yaml...
│   ├── shelf 1.4.2
│   │   ├── async...
│   │   ├── collection...
│   │   ├── http_parser...
│   │   ├── path...
│   │   ├── stack_trace...
│   │   └── stream_channel...
│   ├── shelf_web_socket 3.0.0
│   │   ├── shelf...
│   │   ├── stream_channel...
│   │   └── web_socket_channel...
│   ├── stream_transform 2.1.1
│   ├── timing 1.0.2
│   │   └── json_annotation...
│   ├── watcher 1.1.1
│   │   ├── async...
│   │   └── path...
│   ├── web_socket_channel 3.0.2
│   │   ├── async...
│   │   ├── crypto...
│   │   ├── stream_channel...
│   │   ├── web...
│   │   └── web_socket...
│   ├── yaml 3.1.3
│   │   ├── collection...
│   │   ├── source_span...
│   │   └── string_scanner...
│   ├── args...
│   ├── async...
│   ├── collection...
│   ├── crypto...
│   ├── http...
│   ├── logging...
│   ├── meta...
│   ├── mime...
│   ├── path...
│   ├── stack_trace...
│   └── web...
├── buttons_tabbar 1.3.15
│   └── flutter...
├── cached_network_image 3.4.1
│   ├── cached_network_image_platform_interface 4.1.1
│   │   ├── flutter...
│   │   └── flutter_cache_manager...
│   ├── cached_network_image_web 1.3.1
│   │   ├── cached_network_image_platform_interface...
│   │   ├── flutter...
│   │   ├── flutter_cache_manager...
│   │   └── web...
│   ├── flutter_cache_manager 3.4.1
│   │   ├── rxdart 0.28.0
│   │   ├── clock...
│   │   ├── collection...
│   │   ├── file...
│   │   ├── flutter...
│   │   ├── http...
│   │   ├── path...
│   │   ├── path_provider...
│   │   ├── sqflite...
│   │   └── uuid...
│   ├── octo_image 2.1.0
│   │   └── flutter...
│   └── flutter...
├── card_swiper 3.0.1
│   └── flutter...
├── chewie 1.11.0
│   ├── provider 6.1.4
│   │   ├── nested 1.0.0
│   │   │   └── flutter...
│   │   ├── collection...
│   │   └── flutter...
│   ├── wakelock_plus 1.2.11
│   │   ├── wakelock_plus_platform_interface 1.2.2
│   │   │   ├── flutter...
│   │   │   ├── meta...
│   │   │   └── plugin_platform_interface...
│   │   ├── dbus...
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── meta...
│   │   ├── package_info_plus...
│   │   ├── web...
│   │   └── win32...
│   ├── cupertino_icons...
│   ├── flutter...
│   └── video_player...
├── clipboard 0.1.3
│   └── flutter...
├── connectivity_plus 6.1.3
│   ├── collection 1.19.0
│   ├── connectivity_plus_platform_interface 2.0.1
│   │   ├── flutter...
│   │   ├── meta...
│   │   └── plugin_platform_interface...
│   ├── nm 0.5.0
│   │   └── dbus 0.7.11
│   │       ├── args...
│   │       ├── ffi...
│   │       ├── meta...
│   │       └── xml...
│   ├── flutter...
│   ├── flutter_web_plugins...
│   ├── meta...
│   └── web...
├── crypto 3.0.6
│   └── typed_data...
├── cupertino_icons 1.0.8
├── device_info_plus 11.3.0
│   ├── device_info_plus_platform_interface 7.0.2
│   │   ├── flutter...
│   │   ├── meta...
│   │   └── plugin_platform_interface...
│   ├── ffi 2.1.3
│   ├── file 7.0.1
│   │   ├── meta...
│   │   └── path...
│   ├── win32 5.10.1
│   │   └── ffi...
│   ├── win32_registry 1.1.5
│   │   ├── ffi...
│   │   └── win32...
│   ├── flutter...
│   ├── flutter_web_plugins...
│   ├── meta...
│   └── web...
├── dio 5.4.0
│   ├── http_parser 4.1.2
│   │   ├── collection...
│   │   ├── source_span...
│   │   ├── string_scanner...
│   │   └── typed_data...
│   ├── meta 1.15.0
│   ├── path 1.9.0
│   └── async...
├── dio_retry_plus 2.0.5
│   ├── dio...
│   └── flutter...
├── easy_refresh 3.4.0
│   ├── path_drawing 1.0.1
│   │   ├── path_parsing 1.1.0
│   │   │   ├── meta...
│   │   │   └── vector_math...
│   │   ├── flutter...
│   │   ├── meta...
│   │   └── vector_math...
│   └── flutter...
├── event_bus 2.0.1
├── exif 3.3.0
│   ├── args 2.7.0
│   ├── convert 3.1.2
│   │   └── typed_data...
│   ├── sprintf 7.0.0
│   ├── collection...
│   └── json_annotation...
├── extended_image 9.1.0
│   ├── vector_math 2.1.4
│   ├── extended_image_library...
│   ├── flutter...
│   └── meta...
├── extended_image_library 3.6.0
│   ├── crypto...
│   ├── flutter...
│   ├── http_client_helper...
│   ├── js...
│   ├── path...
│   └── path_provider...
├── factory_push 0.0.1
│   ├── package_info_plus 8.3.0
│   │   ├── package_info_plus_platform_interface 3.2.0
│   │   │   ├── flutter...
│   │   │   ├── meta...
│   │   │   └── plugin_platform_interface...
│   │   ├── clock...
│   │   ├── ffi...
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── http...
│   │   ├── meta...
│   │   ├── path...
│   │   ├── web...
│   │   └── win32...
│   ├── flutter...
│   └── plugin_platform_interface...
├── fast_contacts 4.0.0
│   └── flutter...
├── file_manager 1.0.2
│   ├── flutter...
│   └── path_provider...
├── file_picker 8.3.7
│   ├── cross_file 0.3.4+2
│   │   ├── meta...
│   │   └── web...
│   ├── flutter_plugin_android_lifecycle 2.0.27
│   │   └── flutter...
│   ├── ffi...
│   ├── flutter...
│   ├── flutter_web_plugins...
│   ├── path...
│   ├── plugin_platform_interface...
│   ├── web...
│   └── win32...
├── floor 1.5.0
│   ├── floor_annotation 1.5.0
│   │   └── meta...
│   ├── floor_common 1.5.0
│   │   ├── collection...
│   │   ├── floor_annotation...
│   │   ├── meta...
│   │   ├── path...
│   │   ├── sqflite_common...
│   │   └── sqlparser...
│   ├── sqflite_common_ffi 2.3.4+4
│   │   ├── sqlite3 2.7.5
│   │   │   ├── collection...
│   │   │   ├── ffi...
│   │   │   ├── meta...
│   │   │   ├── path...
│   │   │   ├── typed_data...
│   │   │   └── web...
│   │   ├── meta...
│   │   ├── path...
│   │   ├── sqflite_common...
│   │   └── synchronized...
│   ├── sqflite_common_ffi_web 0.4.5+4
│   │   ├── dev_build 1.1.1+8
│   │   │   ├── args...
│   │   │   ├── collection...
│   │   │   ├── meta...
│   │   │   ├── path...
│   │   │   ├── pool...
│   │   │   ├── process_run...
│   │   │   ├── pub_semver...
│   │   │   ├── synchronized...
│   │   │   └── yaml...
│   │   ├── process_run 1.2.2+1
│   │   │   ├── args...
│   │   │   ├── collection...
│   │   │   ├── meta...
│   │   │   ├── path...
│   │   │   ├── pub_semver...
│   │   │   ├── string_scanner...
│   │   │   ├── synchronized...
│   │   │   └── yaml...
│   │   ├── args...
│   │   ├── http...
│   │   ├── meta...
│   │   ├── path...
│   │   ├── pub_semver...
│   │   ├── sqflite_common...
│   │   ├── sqflite_common_ffi...
│   │   ├── sqlite3...
│   │   ├── synchronized...
│   │   └── web...
│   ├── sqlparser 0.34.1
│   │   ├── charcode 1.4.0
│   │   ├── collection...
│   │   ├── meta...
│   │   └── source_span...
│   ├── collection...
│   ├── flutter...
│   ├── meta...
│   ├── path...
│   └── sqflite...
├── floor_generator 1.5.0
│   ├── strings 3.2.0
│   │   ├── unicode 1.1.8
│   │   │   ├── simple_sparse_list 0.1.4
│   │   │   └── archive...
│   │   └── characters...
│   ├── analyzer...
│   ├── build...
│   ├── build_config...
│   ├── code_builder...
│   ├── collection...
│   ├── floor_annotation...
│   ├── meta...
│   └── source_gen...
├── flutter 0.0.0
│   ├── characters 1.3.0
│   ├── material_color_utilities 0.11.1
│   │   └── collection...
│   ├── sky_engine 0.0.0
│   ├── collection...
│   ├── meta...
│   └── vector_math...
├── flutter_cache_manager_dio_proxy 1.0.1
│   ├── dio...
│   ├── flutter...
│   └── flutter_cache_manager...
├── flutter_html 3.0.0
│   ├── csslib 1.0.2
│   │   └── source_span...
│   ├── list_counter 1.0.2
│   ├── collection...
│   ├── flutter...
│   └── html...
├── flutter_image_compress 1.1.3
│   └── flutter...
├── flutter_lints 2.0.3
│   └── lints 2.1.1
├── flutter_local_notifications 18.0.1
│   ├── flutter_local_notifications_linux 5.0.0
│   │   ├── dbus...
│   │   ├── ffi...
│   │   ├── flutter...
│   │   ├── flutter_local_notifications_platform_interface...
│   │   ├── path...
│   │   └── xdg_directories...
│   ├── flutter_local_notifications_platform_interface 8.0.0
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── timezone 0.10.0
│   │   ├── http...
│   │   └── path...
│   ├── clock...
│   └── flutter...
├── flutter_localizations 0.0.0
│   ├── characters...
│   ├── clock...
│   ├── collection...
│   ├── flutter...
│   ├── intl...
│   ├── material_color_utilities...
│   ├── meta...
│   ├── path...
│   └── vector_math...
├── flutter_native_image 0.0.6+1
│   └── flutter...
├── flutter_photo_editor 0.0.5
│   ├── flutter...
│   └── plugin_platform_interface...
├── flutter_scankit 2.0.6
│   └── flutter...
├── flutter_screenutil 5.0.2
│   └── flutter...
├── flutter_slidable 3.1.2
│   └── flutter...
├── flutter_smart_dialog 4.9.8+7
│   └── flutter...
├── flutter_sound 9.28.0
│   ├── flutter_sound_platform_interface 9.28.0
│   │   ├── flutter...
│   │   ├── logger...
│   │   └── plugin_platform_interface...
│   ├── flutter_sound_web 9.28.0
│   │   ├── flutter...
│   │   ├── flutter_sound_platform_interface...
│   │   ├── flutter_web_plugins...
│   │   ├── logger...
│   │   └── web...
│   ├── logger 2.5.0
│   ├── synchronized 3.3.0+3
│   ├── flutter...
│   ├── path...
│   └── path_provider...
├── flutter_swipe_action_cell 3.1.5
│   └── flutter...
├── flutter_test 0.0.0
│   ├── boolean_selector 2.1.1
│   │   ├── source_span...
│   │   └── string_scanner...
│   ├── fake_async 1.3.1
│   │   ├── clock...
│   │   └── collection...
│   ├── leak_tracker 10.0.7
│   │   ├── clock...
│   │   ├── collection...
│   │   ├── meta...
│   │   ├── path...
│   │   └── vm_service...
│   ├── leak_tracker_flutter_testing 3.0.8
│   │   ├── flutter...
│   │   ├── leak_tracker...
│   │   ├── leak_tracker_testing...
│   │   ├── matcher...
│   │   └── meta...
│   ├── leak_tracker_testing 3.0.1
│   │   ├── leak_tracker...
│   │   ├── matcher...
│   │   └── meta...
│   ├── matcher 0.12.16+1
│   │   ├── async...
│   │   ├── meta...
│   │   ├── stack_trace...
│   │   ├── term_glyph...
│   │   └── test_api...
│   ├── source_span 1.10.0
│   │   ├── collection...
│   │   ├── path...
│   │   └── term_glyph...
│   ├── string_scanner 1.3.0
│   │   └── source_span...
│   ├── term_glyph 1.2.1
│   ├── test_api 0.7.3
│   │   ├── async...
│   │   ├── boolean_selector...
│   │   ├── collection...
│   │   ├── meta...
│   │   ├── source_span...
│   │   ├── stack_trace...
│   │   ├── stream_channel...
│   │   ├── string_scanner...
│   │   └── term_glyph...
│   ├── vm_service 14.3.0
│   ├── async...
│   ├── characters...
│   ├── clock...
│   ├── collection...
│   ├── flutter...
│   ├── material_color_utilities...
│   ├── meta...
│   ├── path...
│   ├── stack_trace...
│   ├── stream_channel...
│   └── vector_math...
├── flutter_video_info 1.3.2
│   ├── flutter...
│   └── path...
├── flutter_widget_from_html 0.15.3
│   ├── flutter_widget_from_html_core 0.15.2
│   │   ├── csslib...
│   │   ├── flutter...
│   │   ├── html...
│   │   └── logging...
│   ├── fwfh_cached_network_image 0.14.3
│   │   ├── cached_network_image...
│   │   ├── flutter...
│   │   ├── flutter_cache_manager...
│   │   └── flutter_widget_from_html_core...
│   ├── fwfh_chewie 0.14.8
│   │   ├── chewie...
│   │   ├── flutter...
│   │   ├── flutter_widget_from_html_core...
│   │   └── video_player...
│   ├── fwfh_just_audio 0.15.2
│   │   ├── just_audio 0.9.46
│   │   │   ├── audio_session 0.1.25
│   │   │   │   ├── flutter...
│   │   │   │   ├── flutter_web_plugins...
│   │   │   │   ├── meta...
│   │   │   │   └── rxdart...
│   │   │   ├── just_audio_platform_interface 4.4.0
│   │   │   │   ├── flutter...
│   │   │   │   └── plugin_platform_interface...
│   │   │   ├── just_audio_web 0.4.14
│   │   │   │   ├── flutter...
│   │   │   │   ├── flutter_web_plugins...
│   │   │   │   ├── just_audio_platform_interface...
│   │   │   │   └── web...
│   │   │   ├── async...
│   │   │   ├── crypto...
│   │   │   ├── flutter...
│   │   │   ├── meta...
│   │   │   ├── path...
│   │   │   ├── path_provider...
│   │   │   ├── rxdart...
│   │   │   └── uuid...
│   │   ├── flutter...
│   │   └── flutter_widget_from_html_core...
│   ├── fwfh_svg 0.8.3
│   │   ├── flutter_svg 2.0.17
│   │   │   ├── vector_graphics 1.1.18
│   │   │   │   ├── flutter...
│   │   │   │   ├── http...
│   │   │   │   └── vector_graphics_codec...
│   │   │   ├── vector_graphics_codec 1.1.13
│   │   │   ├── vector_graphics_compiler 1.1.16
│   │   │   │   ├── args...
│   │   │   │   ├── meta...
│   │   │   │   ├── path...
│   │   │   │   ├── path_parsing...
│   │   │   │   ├── vector_graphics_codec...
│   │   │   │   └── xml...
│   │   │   ├── flutter...
│   │   │   └── http...
│   │   ├── flutter...
│   │   └── flutter_widget_from_html_core...
│   ├── fwfh_url_launcher 0.9.1
│   │   ├── flutter...
│   │   ├── flutter_widget_from_html_core...
│   │   └── url_launcher...
│   ├── fwfh_webview 0.15.4
│   │   ├── flutter...
│   │   ├── flutter_widget_from_html_core...
│   │   ├── logging...
│   │   ├── web...
│   │   ├── webview_flutter...
│   │   ├── webview_flutter_android...
│   │   └── webview_flutter_wkwebview...
│   ├── flutter...
│   └── html...
├── fluttertoast 8.2.12
│   ├── flutter_web_plugins 0.0.0
│   │   ├── characters...
│   │   ├── collection...
│   │   ├── flutter...
│   │   ├── material_color_utilities...
│   │   ├── meta...
│   │   └── vector_math...
│   ├── web 1.1.1
│   └── flutter...
├── fluwx 5.4.2
│   ├── plugin_platform_interface 2.1.8
│   │   └── meta...
│   ├── flutter...
│   └── flutter_web_plugins...
├── gap 3.0.1
│   └── flutter...
├── get 4.6.6
│   └── flutter...
├── http 1.3.0
│   ├── async...
│   ├── http_parser...
│   ├── meta...
│   └── web...
├── http_client_helper 3.0.0
│   └── http...
├── image 4.5.3
│   ├── archive 4.0.5
│   │   ├── posix 6.0.1
│   │   │   ├── ffi...
│   │   │   ├── meta...
│   │   │   └── path...
│   │   ├── crypto...
│   │   └── path...
│   ├── xml 6.5.0
│   │   ├── petitparser 6.0.2
│   │   │   └── meta...
│   │   ├── collection...
│   │   └── meta...
│   └── meta...
├── image_picker 0.8.9
│   ├── image_picker_android 0.8.12+22
│   │   ├── flutter...
│   │   ├── flutter_plugin_android_lifecycle...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_for_web 2.2.0
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── image_picker_platform_interface...
│   │   └── mime...
│   ├── image_picker_ios 0.8.12+2
│   │   ├── flutter...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_linux 0.2.1+2
│   │   ├── file_selector_linux 0.9.3+2
│   │   │   ├── cross_file...
│   │   │   ├── file_selector_platform_interface...
│   │   │   └── flutter...
│   │   ├── file_selector_platform_interface 2.6.2
│   │   │   ├── cross_file...
│   │   │   ├── flutter...
│   │   │   ├── http...
│   │   │   └── plugin_platform_interface...
│   │   ├── flutter...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_macos 0.2.1+2
│   │   ├── file_selector_macos 0.9.4+2
│   │   │   ├── cross_file...
│   │   │   ├── file_selector_platform_interface...
│   │   │   └── flutter...
│   │   ├── file_selector_platform_interface...
│   │   ├── flutter...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_platform_interface 2.10.1
│   │   ├── cross_file...
│   │   ├── flutter...
│   │   ├── http...
│   │   └── plugin_platform_interface...
│   ├── image_picker_windows 0.2.1+1
│   │   ├── file_selector_windows 0.9.3+4
│   │   │   ├── cross_file...
│   │   │   ├── file_selector_platform_interface...
│   │   │   └── flutter...
│   │   ├── file_selector_platform_interface...
│   │   ├── flutter...
│   │   └── image_picker_platform_interface...
│   └── flutter...
├── intl 0.19.0
│   ├── clock 1.1.1
│   ├── meta...
│   └── path...
├── isolate 2.1.1
├── json_annotation 4.9.0
│   └── meta...
├── json_serializable 6.9.0
│   ├── source_gen 1.5.0
│   │   ├── analyzer...
│   │   ├── async...
│   │   ├── build...
│   │   ├── dart_style...
│   │   ├── glob...
│   │   ├── path...
│   │   ├── source_span...
│   │   └── yaml...
│   ├── source_helper 1.3.5
│   │   ├── analyzer...
│   │   ├── collection...
│   │   └── source_gen...
│   ├── analyzer...
│   ├── async...
│   ├── build...
│   ├── build_config...
│   ├── collection...
│   ├── json_annotation...
│   ├── meta...
│   ├── path...
│   ├── pub_semver...
│   └── pubspec_parse...
├── kumi_popup_window 2.0.1
│   └── flutter...
├── logging_flutter 3.0.0
│   ├── logging 1.3.0
│   ├── stack_trace 1.12.0
│   │   └── path...
│   └── flutter...
├── lpinyin 2.0.3
├── mime 1.0.6
├── mmkv 2.1.1
│   ├── mmkv_android 2.1.0
│   │   ├── ffi...
│   │   ├── flutter...
│   │   └── mmkv_platform_interface...
│   ├── mmkv_ios 2.1.0
│   │   ├── ffi...
│   │   ├── flutter...
│   │   └── mmkv_platform_interface...
│   ├── mmkv_ohos 2.1.0
│   │   ├── ffi...
│   │   ├── flutter...
│   │   └── mmkv_platform_interface...
│   ├── mmkv_platform_interface 2.1.0
│   │   ├── ffi...
│   │   ├── flutter...
│   │   └── path_provider...
│   ├── ffi...
│   └── flutter...
├── native_dio_adapter 1.4.0
│   ├── cronet_http 1.3.3
│   │   ├── jni 0.12.2
│   │   │   ├── args...
│   │   │   ├── ffi...
│   │   │   ├── meta...
│   │   │   ├── package_config...
│   │   │   ├── path...
│   │   │   └── plugin_platform_interface...
│   │   ├── flutter...
│   │   ├── http...
│   │   └── http_profile...
│   ├── cupertino_http 2.1.0
│   │   ├── http_profile 0.1.0
│   │   ├── objective_c 7.0.0
│   │   │   ├── ffi...
│   │   │   ├── flutter...
│   │   │   └── pub_semver...
│   │   ├── web_socket 0.1.6
│   │   │   └── web...
│   │   ├── async...
│   │   ├── ffi...
│   │   ├── flutter...
│   │   └── http...
│   ├── dio...
│   ├── flutter...
│   └── http...
├── open_filex 4.7.0
│   ├── ffi...
│   └── flutter...
├── path_provider 2.1.5
│   ├── path_provider_android 2.2.16
│   │   ├── flutter...
│   │   └── path_provider_platform_interface...
│   ├── path_provider_foundation 2.4.1
│   │   ├── flutter...
│   │   └── path_provider_platform_interface...
│   ├── path_provider_linux 2.2.1
│   │   ├── xdg_directories 1.1.0
│   │   │   ├── meta...
│   │   │   └── path...
│   │   ├── ffi...
│   │   ├── flutter...
│   │   ├── path...
│   │   └── path_provider_platform_interface...
│   ├── path_provider_platform_interface 2.1.2
│   │   ├── platform 3.1.6
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── path_provider_windows 2.3.0
│   │   ├── ffi...
│   │   ├── flutter...
│   │   ├── path...
│   │   └── path_provider_platform_interface...
│   └── flutter...
├── percent_indicator 4.2.4
│   └── flutter...
├── permission_handler 12.0.0+1
│   ├── permission_handler_android 13.0.1
│   │   ├── flutter...
│   │   └── permission_handler_platform_interface...
│   ├── permission_handler_apple 9.4.7
│   │   ├── flutter...
│   │   └── permission_handler_platform_interface...
│   ├── permission_handler_html 0.1.3+5
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── permission_handler_platform_interface...
│   │   └── web...
│   ├── permission_handler_platform_interface 4.3.0
│   │   ├── flutter...
│   │   ├── meta...
│   │   └── plugin_platform_interface...
│   ├── permission_handler_windows 0.2.1
│   │   ├── flutter...
│   │   └── permission_handler_platform_interface...
│   ├── flutter...
│   └── meta...
├── photo_manager 3.6.4
│   └── flutter...
├── photo_view 0.14.0
│   └── flutter...
├── protobuf 3.1.0
│   ├── fixnum 1.1.1
│   ├── collection...
│   └── meta...
├── pull_to_refresh 2.0.0
│   └── flutter...
├── qr_flutter 4.1.0
│   ├── qr 3.0.2
│   │   └── meta...
│   └── flutter...
├── retrofit 4.4.2
│   ├── dio...
│   └── meta...
├── retrofit_generator 7.0.8
│   ├── built_collection 5.1.1
│   ├── tuple 2.0.2
│   ├── analyzer...
│   ├── build...
│   ├── code_builder...
│   ├── dart_style...
│   ├── dio...
│   ├── retrofit...
│   └── source_gen...
├── scroll_to_index 3.0.1
│   └── flutter...
├── shared_preferences 2.5.3
│   ├── shared_preferences_android 2.4.8
│   │   ├── flutter...
│   │   └── shared_preferences_platform_interface...
│   ├── shared_preferences_foundation 2.5.4
│   │   ├── flutter...
│   │   └── shared_preferences_platform_interface...
│   ├── shared_preferences_linux 2.4.1
│   │   ├── file...
│   │   ├── flutter...
│   │   ├── path...
│   │   ├── path_provider_linux...
│   │   ├── path_provider_platform_interface...
│   │   └── shared_preferences_platform_interface...
│   ├── shared_preferences_platform_interface 2.4.1
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── shared_preferences_web 2.4.3
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── shared_preferences_platform_interface...
│   │   └── web...
│   ├── shared_preferences_windows 2.4.1
│   │   ├── file...
│   │   ├── flutter...
│   │   ├── path...
│   │   ├── path_provider_platform_interface...
│   │   ├── path_provider_windows...
│   │   └── shared_preferences_platform_interface...
│   └── flutter...
├── sqflite 2.4.1
│   ├── sqflite_android 2.4.0
│   │   ├── flutter...
│   │   ├── path...
│   │   ├── sqflite_common...
│   │   └── sqflite_platform_interface...
│   ├── sqflite_common 2.5.4+6
│   │   ├── meta...
│   │   ├── path...
│   │   └── synchronized...
│   ├── sqflite_darwin 2.4.1+1
│   │   ├── flutter...
│   │   ├── meta...
│   │   ├── path...
│   │   ├── sqflite_common...
│   │   └── sqflite_platform_interface...
│   ├── sqflite_platform_interface 2.4.0
│   │   ├── flutter...
│   │   ├── meta...
│   │   ├── platform...
│   │   ├── plugin_platform_interface...
│   │   └── sqflite_common...
│   ├── flutter...
│   └── path...
├── sticky_headers 0.3.0+2
│   └── flutter...
├── stream_channel 2.1.2
│   └── async...
├── syncfusion_flutter_core 29.1.38
│   ├── flutter...
│   └── vector_math...
├── syncfusion_flutter_pdfviewer 29.1.38
│   ├── syncfusion_flutter_pdf 29.1.38
│   │   ├── convert...
│   │   ├── crypto...
│   │   ├── flutter...
│   │   ├── http...
│   │   ├── intl...
│   │   ├── syncfusion_flutter_core...
│   │   └── xml...
│   ├── syncfusion_flutter_signaturepad 29.1.38
│   │   ├── flutter...
│   │   └── syncfusion_flutter_core...
│   ├── syncfusion_pdfviewer_macos 29.1.38
│   │   ├── flutter...
│   │   └── syncfusion_pdfviewer_platform_interface...
│   ├── syncfusion_pdfviewer_platform_interface 29.1.38
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── syncfusion_pdfviewer_web 29.1.38
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── syncfusion_pdfviewer_platform_interface...
│   │   └── web...
│   ├── syncfusion_pdfviewer_windows 29.1.38
│   │   └── flutter...
│   ├── uuid 4.5.1
│   │   ├── crypto...
│   │   ├── fixnum...
│   │   ├── meta...
│   │   └── sprintf...
│   ├── async...
│   ├── device_info_plus...
│   ├── flutter...
│   ├── http...
│   ├── intl...
│   ├── syncfusion_flutter_core...
│   ├── url_launcher...
│   ├── vector_math...
│   └── web...
├── tencentcloud_cos_sdk_plugin 1.2.0
│   ├── flutter...
│   └── plugin_platform_interface...
├── throttling 2.0.1
├── typed_data 1.4.0
│   └── collection...
├── umeng_apm_sdk 2.3.2
│   ├── flutter...
│   └── http...
├── umeng_common_sdk 1.2.8
│   └── flutter...
├── url_launcher 6.3.1
│   ├── url_launcher_android 6.3.15
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_ios 6.3.2
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_linux 3.2.1
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_macos 3.2.2
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_platform_interface 2.3.2
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── url_launcher_web 2.4.0
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── url_launcher_platform_interface...
│   │   └── web...
│   ├── url_launcher_windows 3.1.4
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   └── flutter...
├── video_compress 3.1.4
│   └── flutter...
├── video_player 2.9.3
│   ├── html 0.15.5
│   │   ├── csslib...
│   │   └── source_span...
│   ├── video_player_android 2.8.2
│   │   ├── flutter...
│   │   └── video_player_platform_interface...
│   ├── video_player_avfoundation 2.7.0
│   │   ├── flutter...
│   │   └── video_player_platform_interface...
│   ├── video_player_platform_interface 6.3.0
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── video_player_web 2.3.4
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── video_player_platform_interface...
│   │   └── web...
│   └── flutter...
├── video_thumbnail 0.5.3
│   └── flutter...
├── webview_flutter 4.10.0
│   ├── webview_flutter_android 4.3.4
│   │   ├── flutter...
│   │   └── webview_flutter_platform_interface...
│   ├── webview_flutter_platform_interface 2.10.0
│   │   ├── flutter...
│   │   ├── meta...
│   │   └── plugin_platform_interface...
│   ├── webview_flutter_wkwebview 3.18.4
│   │   ├── flutter...
│   │   ├── path...
│   │   └── webview_flutter_platform_interface...
│   └── flutter...
├── wechat_assets_picker 9.5.0
│   ├── photo_manager_image_provider 2.2.0
│   │   ├── flutter...
│   │   └── photo_manager...
│   ├── visibility_detector 0.4.0+2
│   │   └── flutter...
│   ├── wechat_picker_library 1.0.5
│   │   ├── flutter...
│   │   └── photo_manager...
│   ├── extended_image...
│   ├── flutter...
│   ├── photo_manager...
│   ├── provider...
│   └── video_player...
└── wechat_camera_picker 4.3.7
├── camera 0.10.6
│   ├── camera_avfoundation 0.9.18+12
│   │   ├── camera_platform_interface...
│   │   ├── flutter...
│   │   └── stream_transform...
│   ├── camera_web 0.3.5
│   │   ├── camera_platform_interface...
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── stream_transform...
│   │   └── web...
│   ├── camera_android...
│   ├── camera_platform_interface...
│   ├── flutter...
│   └── flutter_plugin_android_lifecycle...
├── camera_android 0.10.10+1
│   ├── camera_platform_interface...
│   ├── flutter...
│   ├── flutter_plugin_android_lifecycle...
│   └── stream_transform...
├── camera_platform_interface 2.10.0
│   ├── cross_file...
│   ├── flutter...
│   ├── plugin_platform_interface...
│   └── stream_transform...
├── sensors_plus 6.1.1
│   ├── sensors_plus_platform_interface 2.0.1
│   │   ├── flutter...
│   │   ├── logging...
│   │   ├── meta...
│   │   └── plugin_platform_interface...
│   ├── flutter...
│   └── flutter_web_plugins...
├── collection...
├── flutter...
├── path...
├── photo_manager...
├── photo_manager_image_provider...
├── video_player...
└── wechat_picker_library...

